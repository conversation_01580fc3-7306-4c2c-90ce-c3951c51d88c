from typing import Any

from pydantic import BaseModel

from services.studio._text_to_workflow.common.api_workflow.schema import (
    BaseWorkflowActivity,
    If,
    Sequence,
    TryCatch,
)
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import get_do_block
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import APTEDNode, APTEDTree, EvalMode, FilterParamsMode, _workflow_edit_score

IGNORED_ACTIVITY_PARAMS = [
    "thought",
    "id",
    "activity",
]


class ApiWfAPTEDNode(APTEDNode):
    """
    A node in the APTED tree representing an API Workflow activity.
    """

    last_id = 0

    def __init__(self, value: str, params, children: list["APTEDNode"] | None = None):
        ApiWfAPTEDNode.last_id += 1
        super().__init__(value, params, id=ApiWfAPTEDNode.last_id, children=children)

    @staticmethod
    def gather_params_from_node(node: BaseWorkflowActivity) -> dict:
        params = {}

        def extract_values(obj: Any):
            if isinstance(obj, BaseModel):
                for field_name in obj.model_fields.keys():
                    if field_name in IGNORED_ACTIVITY_PARAMS:
                        continue

                    value = getattr(obj, field_name)
                    if (
                        value is None
                        or isinstance(value, BaseWorkflowActivity)
                        or (isinstance(value, list) and any(isinstance(x, BaseWorkflowActivity) for x in value))
                    ):
                        continue

                    if isinstance(value, dict) or isinstance(value, BaseModel):
                        extract_values(value)
                        continue
                    # If it's a primitive type, add it to the params, we will add it to the flattened params dictionary
                    # we don't expect the keys to be duplicated
                    params.setdefault(field_name, value)
                return

            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, dict) or isinstance(value, BaseModel):
                        extract_values(value)
                        continue

                    # If it's a primitive type, add it to the params, we will add it to the flattened params dictionary
                    if key in params:
                        # we don't expect the keys to be duplicated
                        raise ValueError(f"Duplicate key: {key} in params: {params}")
                    params[key] = value

                return

            raise ValueError(f"Unexpected object type: {type(obj)}")

        # build a flattened dictionary of the node's params
        extract_values(node)
        return params

    @staticmethod
    def get_node_from_activity(api_activity: BaseWorkflowActivity) -> APTEDNode:
        # Get the activity type
        node = ApiWfAPTEDNode(getattr(api_activity, "activity", ""), ApiWfAPTEDNode.gather_params_from_node(api_activity))

        # Handle if activity
        if isinstance(api_activity, If):
            if api_activity.then:
                then_node: ApiWfAPTEDNode = ApiWfAPTEDNode(
                    "then",
                    params={},
                    children=[ApiWfAPTEDNode.get_node_from_activity(a) for a in api_activity.then],
                )
                node.add_child(then_node)

            if api_activity.else_:
                else_node = ApiWfAPTEDNode(
                    "else",
                    params={},
                    children=[ApiWfAPTEDNode.get_node_from_activity(a) for a in api_activity.else_],
                )
                node.add_child(else_node)

        # Handle try-catch activity
        if isinstance(api_activity, TryCatch):
            if api_activity.try_:
                try_node = ApiWfAPTEDNode(
                    "try",
                    params={},
                    children=[ApiWfAPTEDNode.get_node_from_activity(a) for a in api_activity.try_],
                )
                node.add_child(try_node)

            if api_activity.catch and api_activity.catch.do:
                catch_node = ApiWfAPTEDNode(
                    "catch",
                    params={},
                    children=[ApiWfAPTEDNode.get_node_from_activity(a) for a in api_activity.catch.do],
                )
                node.add_child(catch_node)

        # Handle do blocks (SequenceActivity and ForEachActivity)
        do_block = get_do_block(api_activity)
        for activity in do_block:
            node.add_child(ApiWfAPTEDNode.get_node_from_activity(activity))
        return node


class ApiWfAPTEDTree(APTEDTree):
    """
    This class is used to compute the tree edit distance for API Workflow generation.
    """

    def __init__(self, workflow_root: Sequence):
        # Extract root sequence
        root_node = ApiWfAPTEDNode.get_node_from_activity(workflow_root)
        root = ApiWfAPTEDNode("[ROOT]", params={}, children=root_node.children)
        super().__init__(root)


def api_workflow_edit_score(
    gt_workflow: Sequence,
    result_workflow: Sequence,
    param_match: EvalMode = "noparams",
    filter_params: FilterParamsMode | None = None,
    compute_mapping: bool = True,
) -> tuple[float, int, float, list[tuple[str, str, float, float]]]:
    """
    Compute the tree edit distance score between two API workflows.
    """
    tree_gt = ApiWfAPTEDTree(gt_workflow)
    tree_res = ApiWfAPTEDTree(result_workflow)
    return _workflow_edit_score(tree_gt, tree_res, param_match, filter_params, compute_mapping)
