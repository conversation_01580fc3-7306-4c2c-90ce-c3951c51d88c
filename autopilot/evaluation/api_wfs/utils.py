import typing as t

from services.studio._text_to_workflow.common.api_workflow.schema import (
    ApiWorkflowDataPoint,
)
from services.studio._text_to_workflow.utils.json_utils import json_load_from_path
from services.studio._text_to_workflow.utils.paths import get_api_workflow_edit_dataset_path, get_api_workflow_retriever_dataset_path

ApiWorkflowDatasetType = t.Literal["generate", "edit"]


def load_api_workflow_datapoints(dataset_type: ApiWorkflowDatasetType = "generate") -> dict[str, ApiWorkflowDataPoint]:
    dataset_dir = get_api_workflow_edit_dataset_path() if dataset_type == "edit" else get_api_workflow_retriever_dataset_path()

    # Find all JSON files in the dataset directory
    api_wf_files = list(dataset_dir.rglob("*.json"))

    datapoints = {}
    for file_path in api_wf_files:
        # Load the JSON file
        data = json_load_from_path(file_path)

        # Convert to ApiWorkflowDataPoint
        datapoint = ApiWorkflowDataPoint.model_validate(data)
        datapoints[file_path.as_posix()] = datapoint

    return datapoints
