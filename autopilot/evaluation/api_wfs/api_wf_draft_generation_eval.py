import asyncio
import html
import os
import pathlib
import time
import typing as t

import <PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel

from evaluation.api_wfs._api_wf_tree_edit_distance import api_workflow_edit_score
from evaluation.api_wfs.utils import load_api_workflow_datapoints
from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import APIActivityRetrievalService
from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import ApiWorkflowDraftResult
from services.studio._text_to_workflow.common.api_workflow.schema import SEQUENCE_ACTIVITY_TYPE, ApiWorkflow, ApiWorkflowDataPoint, Sequence
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import (
    find_activity_by_id,
    try_find_activity_by_id,
)
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, Connection
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.json_utils import json_dump
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.workflow_utils import cleanup_api_workflow_for_compare, get_tld_api
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import (
    EvalMode,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.draft_eval_base import BaseDraftEvalService as DraftEvalBase
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.dataset import get_cached_datapoint, get_corresponding_pkl_path
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.draft_generation import DraftGenerationEval
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import ActivityTreeAccuracyCalculator


class ApiWfDraftEvalService(DraftEvalBase):
    def __init__(
        self,
        api_wf_activities_retriever_service: APIActivityRetrievalService,
        api_wf_draft_service: ApiWfDraftService,
        output_file_prefix: str,
        batch_size: int = 4,
        retry_count: int = 3,
        force_recompute: bool = False,
        force_generation_mode: ActivitiesGenerationMode | None = None,
    ):
        super().__init__(output_file_prefix, batch_size, retry_count, force_recompute)

        config_path = pathlib.Path(os.getcwd()) / "services/studio/_text_to_workflow/api_workflow/config/api_draft_creation_prompt.yaml"
        self.draft_creation_config = yaml_load(config_path)

        self.api_wf_draft_service = api_wf_draft_service
        self.api_wf_activities_retriever_service = api_wf_activities_retriever_service

        self.embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
        self.force_generation_mode: ActivitiesGenerationMode | None = force_generation_mode

    async def _evaluate_sample(self, path: pathlib.Path, sample: ApiWorkflowDataPoint, connections: list[Connection]) -> DraftGenerationEval:
        print(f"Evaluating sample {path}")

        activity_retrieval_result: APIActivityRetrievalResult
        activity_retrieval_results_path = get_corresponding_pkl_path(path)
        if not self.force_recompute and activity_retrieval_results_path.exists():
            activity_retrieval_result = get_cached_datapoint(activity_retrieval_results_path)
        else:
            activity_retrieval_result = await self.api_wf_activities_retriever_service.generate_relevant_activities(
                sample.query, sample.existing_workflow, connections, True
            )
            self._serialize_result(activity_retrieval_results_path, activity_retrieval_result)

        start = time.time()

        # override the generation mode if specified
        generation_mode = self.force_generation_mode or sample.mode

        # remove the demonstration that is identical to the current eval sample
        activity_retrieval_result.demonstrations = [d for d in activity_retrieval_result.demonstrations if d.query != sample.solution_workflow_query]

        draft_creation_result: ApiWorkflowDraftResult = await self.api_wf_draft_service.generate_workflow_draft(
            sample.query, sample.existing_workflow, connections, sample.language, activity_retrieval_result, mode=generation_mode
        )
        elapsed = time.time() - start

        generated_wf = draft_creation_result.api_workflow_details.generated_workflow

        lev_score = Levenshtein.ratio(
            generated_wf.model_dump_json(exclude_none=True, by_alias=True) if generated_wf else "",
            sample.solution_workflow.model_dump_json(exclude_none=True, by_alias=True) if sample.solution_workflow else "",
        )

        # remove all the extra fields that are not relevant for the comparison
        clean_gen_wf = cleanup_api_workflow_for_compare(generated_wf.root.model_dump(exclude_none=True, by_alias=True)) if generated_wf else {}
        clean_gt_workflow = cleanup_api_workflow_for_compare(sample.solution_workflow.root.model_dump(exclude_none=True, by_alias=True))

        lev_score_no_extras = Levenshtein.ratio(json_dump(clean_gen_wf), json_dump(clean_gt_workflow))

        good, total = ActivityTreeAccuracyCalculator("activity").tree_accuracy(clean_gt_workflow, clean_gen_wf)
        activity_tree_accuracy = float(good) / total

        generation_root, gt_root = self._get_relevant_generation_roots(sample, generated_wf)

        # compute TED scores
        ted_score, _, ted_nodes_mapping = self._compute_score(generation_root, gt_root)
        ted_levenstein_params_score, levenstein_params_score, _ = self._compute_score(generation_root, gt_root, "levenshtein")
        ted_exact_params_score, exact_params_score, _ = self._compute_score(generation_root, gt_root, "exact")

        post_generation_errors = draft_creation_result.api_workflow_details.post_generation_errors + draft_creation_result.edit_patch_structure_errors

        tld_score = get_tld_api(sample.existing_workflow, sample.solution_workflow, generated_wf) if sample.existing_workflow else None

        return DraftGenerationEval(
            query=sample.query,
            ground_truth_activities=sample.used_activities,
            solution_workflow=sample.solution_workflow,
            existing_workflow_sequence=sample.existing_workflow,
            elapsed_time=elapsed,
            raw_workflow=generated_wf or draft_creation_result.api_workflow_details.raw_workflow,
            post_generation_errors=post_generation_errors,
            proposed_activities=draft_creation_result.activity_defs,
            token_usage=draft_creation_result.token_usage,
            demonstrations=activity_retrieval_result.demonstrations,
            prompt=draft_creation_result.prompt_value,
            processed_draft_workflow=draft_creation_result.api_workflow_details.processed_workflow,
            raw_model_prediction=draft_creation_result.raw_model_prediction,
            ted_nodes_mapping=ted_nodes_mapping,
            lev_score=lev_score,
            lev_score_no_extras=lev_score_no_extras,
            activity_tree_accuracy=activity_tree_accuracy,
            ted_score=ted_score,
            ted_levenstein_params_score=ted_levenstein_params_score,
            levenstein_params_score=levenstein_params_score,
            ted_exact_params_score=ted_exact_params_score,
            exact_params_score=exact_params_score,
            tld_score=tld_score,
        )

    def serialize_workflow(self, workflow: str | BaseModel | None) -> str:
        if workflow is None:
            return ""
        return html.escape(workflow if isinstance(workflow, str) else yaml_dump(workflow.model_dump(exclude_none=True, by_alias=True)))

    def serialize_demonstration(self, demonstration: ApiWorkflowDataPoint):
        return self.draft_generation_templates["sample_row_demonstrations_template"].format(
            query=demonstration.query,
            type="Workflow",  # TODO: once activity config demos are implemented, we should implement a type for that
            plan=demonstration.plan.replace("\n", "<br>"),
            solution_workflow=self.serialize_workflow(demonstration.solution_workflow),
        )

    @staticmethod
    def _compute_score(
        result_workflow_dict: Sequence,
        gt_workflow_dict: Sequence,
        eval_mode: EvalMode = "noparams",
    ) -> t.Tuple[float, float, list[tuple[str, str, float, float]]]:
        try:
            score, _, _, mapping = api_workflow_edit_score(gt_workflow_dict, result_workflow_dict, param_match=eval_mode)
            mapping_scores = [m[3] for m in mapping if m[3] >= 0]
            mapping_score = sum(mapping_scores) / len(mapping_scores) if mapping_scores else -1.0

            return score, mapping_score, mapping
        except Exception as e:
            print(f"ERROR computing {result_workflow_dict.thought} score. Exception: {e}")
            return -1, -1, []

    def _get_model_settings(self):
        return ModelManager().get_llm_config(WorkflowGenerationDraftService.draft_generation_model_name)

    def _get_system_prompt_config(self):
        return self.draft_creation_config["system_msg"]

    def _get_relevant_generation_roots(self, sample: ApiWorkflowDataPoint, generated_wf: ApiWorkflow | None) -> tuple[Sequence, Sequence]:
        """
        Extract the relevant activities that should be compared when evaluating the generated workflow.
        Sometimes the generation is focused on a single activity/scope, and we should only compare that.
        """
        # check if the generation should be focused on a single activity (relevant for edit evals mostly)
        if sample.focused_activity_id:
            focused_gt_activity = find_activity_by_id(sample.solution_workflow, sample.focused_activity_id)

            if sample.contains_no_replacements and focused_gt_activity and generated_wf:
                # if the replacement focuses on a single activity and no activities should be replaced between ground truth and the solution
                # we should only compute the score for that activity
                focused_generated_activity = try_find_activity_by_id(generated_wf, sample.focused_activity_id)
                generation_root = Sequence(
                    thought="", id="", do=[focused_generated_activity] if focused_generated_activity else [], activity=SEQUENCE_ACTIVITY_TYPE
                )
                gt_root = Sequence(thought="", id="", do=[focused_gt_activity], activity=SEQUENCE_ACTIVITY_TYPE)
                return generation_root, gt_root

        generation_root = generated_wf.root if generated_wf else Sequence(thought="", id="", do=[], activity=SEQUENCE_ACTIVITY_TYPE)
        gt_root = sample.solution_workflow.root

        return generation_root, gt_root


if __name__ == "__main__":
    activitiesFetcher = APIActivitiesRetriever()
    post_gen_processing_service = ApiWfPostGenerationProcessingService(activitiesFetcher)
    draftCreationService = ApiWfDraftService(activitiesFetcher, post_gen_processing_service)

    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activitiesFetcher, embedding_model)
    api_activities_retrieval_service = APIActivityRetrievalService(activitiesFetcher, connection_embeddings_retriever)

    draftEvalService = ApiWfDraftEvalService(api_activities_retrieval_service, draftCreationService, "api_wf_draft_generation_eval", 4)
    tenant_id, default_connections = get_connections_data()

    request_context = get_testing_request_context("en", tenant_id, "Workflow Dataset Generation")
    request_utils.set_request_context(request_context)

    # Edit Evaluation
    datapoints = load_api_workflow_datapoints(dataset_type="edit")

    draftEvalService.output_file_prefix = "api_wf_draft_generation_edit_eval"
    result = asyncio.run(draftEvalService.run(datapoints, default_connections, tenant_id))

    # Edit with Full Rewrite Evaluation
    draftEvalService.force_generation_mode = "workflow"
    draftEvalService.output_file_prefix = "api_wf_draft_generation_edit_full_rewrite_eval"
    result = asyncio.run(draftEvalService.run(datapoints, default_connections, tenant_id))

    # Generate Evaluation
    draftEvalService.output_file_prefix = "api_wf_draft_generation_eval"
    datapoints = load_api_workflow_datapoints()
    result = asyncio.run(draftEvalService.run(datapoints, default_connections, tenant_id))
