{
    "folders": [
        {
            "name": "autopilot",
            "path": ".",
        },
    ],
    "settings": {
        // Files
        "files.exclude": {
            "**/.pytest_cache": true,
            "**/.ruff_cache": true,
            "**/__pycache__": true,
        },
        "files.watcherExclude": {
            "**/.pytest_cache": true,
            "**/.ruff_cache": true,
            "**/__pycache__": true,
            ".data": true,
            ".devcontainer": true,
            ".venv": true,
            ".vscode": true,
        },
        "search.exclude": {
            "**/.pytest_cache": true,
            "**/.ruff_cache": true,
            "**/__pycache__": true,
            ".data": true,
            ".devcontainer": true,
            ".venv": true,
            ".vscode": true,
        },
        // Git
        "git.openRepositoryInParentFolders": "always",
        // Testing
        "python.testing.pytestEnabled": true,
        "python.testing.unittestEnabled": false,
        // Analysis
        "basedpyright.analysis.autoImportCompletions": true,
        "basedpyright.analysis.autoSearchPaths": true,
        // Formatting
        "[python]": {
            "editor.formatOnSave": true,
            "editor.defaultFormatter": "charliermarsh.ruff",
            "editor.codeActionsOnSave": {
                "source.fixAll": "explicit",
                "source.organizeImports": "explicit"
            },
            "notebook.codeActionsOnSave": {
                "source.fixAll": "explicit",
                "source.organizeImports": "explicit"
            }
        },
        "python.testing.cwd": "${workspaceFolder}"
    },
    "launch": {
        "configurations": [
            {
                "name": "Run Autopilot Service (LLM Gateway)",
                "type": "debugpy",
                "request": "launch",
                "module": "uvicorn",
                "args": [
                    "services.studio.service:app",
                    "--host",
                    "0.0.0.0",
                    "--port",
                    "5002",
                    "--log-level",
                    "info",
                    "--reload"
                ],
                "jinja": true,
                "justMyCode": false,
                "env": {
                    "USE_LLM_GATEWAY": "true"
                },
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Run Autopilot Service (without LLM Gateway)",
                "type": "debugpy",
                "request": "launch",
                "module": "uvicorn",
                "args": [
                    "services.studio.service:app",
                    "--host",
                    "0.0.0.0",
                    "--port",
                    "5002",
                    "--log-level",
                    "info",
                    "--reload"
                ],
                "jinja": true,
                "justMyCode": false,
                "env": {
                    "USE_LLM_GATEWAY": "false"
                },
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Launch current file",
                "type": "debugpy",
                "request": "launch",
                "program": "${file}",
                "cwd": "${workspaceFolder}",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}"
                },
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Evaluate Activity Retrieval",
                "type": "debugpy",
                "request": "launch",
                "module": "evaluation.rpa_wfs.activity_retrieval_eval",
                "args": [],
                "jinja": true,
                "justMyCode": false,
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Evaluate API Activity Retrieval",
                "type": "debugpy",
                "request": "launch",
                "module": "evaluation.api_wfs.api_activity_retrieval_eval",
                "args": [],
                "jinja": true,
                "justMyCode": false,
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Evaluate Core WF Draft Generation",
                "type": "debugpy",
                "request": "launch",
                "module": "services.studio._text_to_workflow.workflow_generation.evaluation.draft_generation_eval",
                "args": [],
                "jinja": true,
                "justMyCode": false,
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Evaluate API WF Draft Generation",
                "type": "debugpy",
                "request": "launch",
                "module": "evaluation.api_wfs.api_wf_draft_generation_eval",
                "args": [],
                "jinja": true,
                "justMyCode": false,
                "envFile": "${workspaceFolder}/.env"
            },
            {
                "name": "Evaluate Workflow Generation",
                "type": "debugpy",
                "request": "launch",
                "module": "services.studio._text_to_workflow.workflow_generation.main",
                "env": {
                    "USE_LLM_GATEWAY": "True",
                    "ENV_NAME": "staging",
                    "CLOUD_URL_BASE": "https://staging.uipath.com",
                },
                "args": [
                    "test",
                    "--subset",
                    "test",
                    "--target-framework",
                    "Portable"
                ],
                "jinja": true,
                "justMyCode": false
            },
            {
                "name": "Python: Remote Attach",
                "type": "debugpy",
                "request": "attach",
                "connect": {
                    "host": "localhost",
                    "port": 5678
                },
                "pathMappings": [
                    {
                        "localRoot": "${workspaceFolder}",
                        "remoteRoot": "."
                    }
                ],
                "justMyCode": true
            },
            {
                "name": "Generate API WFS Dataset",
                "type": "debugpy",
                "request": "launch",
                "module": "experimental.autopilot_dataset.generators.generate_api_wfs_dataset",
                "envFile": "${workspaceFolder}/.env"
            }
        ]
    },
    "extensions": {
        "recommendations": [
            "ms-python.python",
            "ms-python.debugpy",
            "charliermarsh.ruff",
            "detachhead.basedpyright",
        ]
    }
}
