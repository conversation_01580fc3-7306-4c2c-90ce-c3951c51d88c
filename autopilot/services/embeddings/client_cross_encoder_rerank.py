import argparse
import os

import requests


def cross_encoder_rerank(endpoint, model, query, texts):
    # Headers
    headers = {
        "Authorization": os.environ.get("UIPATH_TOKEN"),
        "Content-Type": "application/json",
        # "X-Uipath-Tenantid": os.environ.get('X_UIPATH_TENANTID'),
    }

    # Request payload
    payload = {
        "query": query,
        "texts": texts,
        "model": model,
    }
    # Make the POST request
    response = requests.post(endpoint, headers=headers, json=payload)
    if response.status_code != 200:
        results = response.json()
        if "error" in results:
            return response.status_code, results["error"]
        else:
            return response.status_code, results

    results = response.json()
    return response.status_code, results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Rerank a list of texts based on a query")
    parser.add_argument("--endpoint", default="http://localhost:5005/rerank", type=str, help="endpoint to the server")
    parser.add_argument("--model", default="ms-marco-MiniLM-L-6-v2", type=str, help="model to use for embeddings")
    parser.add_argument("--query", type=str, help="Query to rerank the texts")
    parser.add_argument("texts", nargs="+", help="One or more texts to rerank")

    args = parser.parse_args()

    status_code, results = cross_encoder_rerank(args.endpoint, args.model, args.query, args.texts)
    print(f"{status_code=}")
    if status_code != 200:
        print(results)
    else:
        indices = results["indices"]
        scores = results["scores"]
        print(indices)
        print(scores)
