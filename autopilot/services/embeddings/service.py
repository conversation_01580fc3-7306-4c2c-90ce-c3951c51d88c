import time
import zlib

import numpy as np
import typing_extensions as t
import uvicorn
import yaml
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from sentence_transformers import CrossEncoder, SentenceTransformer

DEBUG = False


class EmbeddingRequest(t.TypedDict):
    input: str | list[str]
    model: str
    encoding_format: t.Optional[str]


class EmbeddingObject(t.TypedDict):
    object: str
    embedding: list[float]
    index: int


class EmbeddingResponse(t.TypedDict):
    object: str
    data: list[EmbeddingObject]
    model: str


class CrossEncoderRerankRequest(t.TypedDict):
    query: str
    texts: list[str]
    model: str


class CrossEncoderRerankResponse(t.TypedDict):
    indices: list[int]
    scores: list[float]


app = FastAPI(title="embeddings", debug=False)
EMBEDDING_MODELS = {}
CROSS_ENCODER_MODELS = {}


def init():
    global EMBEDDING_MODELS
    with open("service.yaml", "r") as f:
        yaml_config = yaml.load(f, Loader=yaml.FullLoader)
    embedding_root_path = yaml_config["embedding_root_path"]
    model_names = [
        "all-mpnet-base-v2",
        "all-roberta-large-v1",
        "msmarco-distilbert-base-tas-b",
        "stella_en_400M_v5",
        "stella-trained",
    ]
    for model_name in model_names:
        model = SentenceTransformer(embedding_root_path + model_name, trust_remote_code=True)
        EMBEDDING_MODELS[model_name] = model.cuda()

    cross_encoder_root_path = yaml_config["cross_encoder_root_path"]
    model_names = [
        "ms-marco-MiniLM-L-6-v2",
    ]
    for model_name in model_names:
        model = CrossEncoder(cross_encoder_root_path + model_name, trust_remote_code=True)
        CROSS_ENCODER_MODELS[model_name] = model.cuda()


@app.post("/embed", response_model=EmbeddingResponse)
async def embed(request: EmbeddingRequest) -> EmbeddingResponse | JSONResponse | Response:
    inputs = request["input"]
    modelname = request.get("model", "all-mpnet-base-v2")
    encoding_format = request.get("encoding_format", "float")
    if isinstance(inputs, str):
        inputs = [inputs]
    if encoding_format not in ("binary", "float"):
        response = {"input": request["input"], "model": request["model"], "error": "Invalid encoding format"}
        return JSONResponse(content=jsonable_encoder(response), status_code=400)
    if modelname not in EMBEDDING_MODELS:
        response = {"input": request["input"], "model": request["model"], "error": "Model not found"}
        return JSONResponse(content=jsonable_encoder(response), status_code=404)
    model = EMBEDDING_MODELS[modelname]

    start_time = time.time()
    embeddings = model.encode(inputs, normalize_embeddings=True)
    if DEBUG:
        end_time = time.time()
        print(modelname, "duration", end_time - start_time, len(inputs))

    if encoding_format == "binary":
        embeddings = embeddings.astype(np.float32)
        compressed_data = zlib.compress(embeddings.tobytes())
        ret = Response(
            content=compressed_data,
            media_type="application/octet-stream",
            headers={
                "X-Embedding-Shape": f"{embeddings.shape[0]},{embeddings.shape[1]}",
                "X-Embedding-Dtype": str(embeddings.dtype),
                "Content-Encoding": "deflate",
            },
        )
    elif encoding_format == "float":
        embeddings_list = embeddings.tolist()

        response = {
            "object": "list",
            "data": [{"object": "embedding", "embedding": embedding, "index": i} for i, embedding in enumerate(embeddings_list)],
            "model": request["model"],
        }

        ret = JSONResponse(content=jsonable_encoder(response), status_code=200)
    else:
        response = {"input": request["input"], "model": request["model"]}
        ret = JSONResponse(content=jsonable_encoder(response), status_code=200)
    return ret


@app.post("/rerank", response_model=CrossEncoderRerankResponse)
async def cross_encoder_rerank(request: CrossEncoderRerankRequest) -> CrossEncoderRerankResponse | JSONResponse:
    query = request["query"]
    texts = request["texts"]
    modelname = request["model"]
    if modelname not in CROSS_ENCODER_MODELS:
        response = {"query": request["query"], "texts": request["texts"], "model": request["model"], "error": "Model not found"}
        return JSONResponse(content=jsonable_encoder(response), status_code=404)

    model = CROSS_ENCODER_MODELS[modelname]
    x = [[query, text] for text in texts]

    start_time = time.time()
    scores = model.predict(x)
    if DEBUG:
        end_time = time.time()
        print(request["model"], "duration", end_time - start_time, len(texts))

    indices = np.argsort(-scores)
    response = {"indices": indices.tolist(), "scores": scores.tolist()}
    ret = JSONResponse(content=jsonable_encoder(response), status_code=200)
    return ret


@app.exception_handler(Exception)
async def exception_handler(_: Request, ex: Exception) -> JSONResponse:
    """
    Global exception handler for the FastAPI application.

    Args:
        _ (Request): The request object (unused in this handler).
        ex (Exception): The exception that was raised.

    Returns:
        JSONResponse: A JSON response containing error details and an appropriate HTTP status code.
    """
    return JSONResponse({"error": str(ex)}, status_code=500)


if __name__ == "__main__":
    init()
    uvicorn.run(app, host="0.0.0.0", port=5005)
