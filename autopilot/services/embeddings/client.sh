python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=all-mpnet-base-v2 --encoding-format=binary "Ana are mere" "Gina are pere"
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=all-roberta-large-v1 --encoding-format=binary "Ana are mere" "Gina are pere"
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=msmarco-distilbert-base-tas-b --encoding-format=binary "Ana are mere" "Gina are pere"
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella_en_400M_v5 --encoding-format=binary "Ana are mere" "Gina are pere"
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary "Ana are mere" "Gina are pere"

python3 client_cross_encoder_rerank.py --endpoint http://localhost:5005/rerank --model ms-marco-MiniLM-L-6-v2 --query="Ana are mere" "I am on the seaside" "Gina are pere"

# Test with 16 texts
echo "Testing encoding with 16 texts..."
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary \
  "Text 1" "Text 2" "Text 3" "Text 4" "Text 5" "Text 6" "Text 7" "Text 8" \
  "Text 9" "Text 10" "Text 11" "Text 12" "Text 13" "Text 14" "Text 15" "Text 16"

# Test with 32 texts
echo "Testing encoding with 32 texts..."
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary \
  "Text 1" "Text 2" "Text 3" "Text 4" "Text 5" "Text 6" "Text 7" "Text 8" \
  "Text 9" "Text 10" "Text 11" "Text 12" "Text 13" "Text 14" "Text 15" "Text 16" \
  "Text 17" "Text 18" "Text 19" "Text 20" "Text 21" "Text 22" "Text 23" "Text 24" \
  "Text 25" "Text 26" "Text 27" "Text 28" "Text 29" "Text 30" "Text 31" "Text 32"

# Test with 64 texts
echo "Testing encoding with 64 texts..."
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary \
  "Text 1" "Text 2" "Text 3" "Text 4" "Text 5" "Text 6" "Text 7" "Text 8" \
  "Text 9" "Text 10" "Text 11" "Text 12" "Text 13" "Text 14" "Text 15" "Text 16" \
  "Text 17" "Text 18" "Text 19" "Text 20" "Text 21" "Text 22" "Text 23" "Text 24" \
  "Text 25" "Text 26" "Text 27" "Text 28" "Text 29" "Text 30" "Text 31" "Text 32" \
  "Text 33" "Text 34" "Text 35" "Text 36" "Text 37" "Text 38" "Text 39" "Text 40" \
  "Text 41" "Text 42" "Text 43" "Text 44" "Text 45" "Text 46" "Text 47" "Text 48" \
  "Text 49" "Text 50" "Text 51" "Text 52" "Text 53" "Text 54" "Text 55" "Text 56" \
  "Text 57" "Text 58" "Text 59" "Text 60" "Text 61" "Text 62" "Text 63" "Text 64"

# Test with 128 texts
echo "Testing encoding with 128 texts..."
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary \
  "Text 1" "Text 2" "Text 3" "Text 4" "Text 5" "Text 6" "Text 7" "Text 8" \
  "Text 9" "Text 10" "Text 11" "Text 12" "Text 13" "Text 14" "Text 15" "Text 16" \
  "Text 17" "Text 18" "Text 19" "Text 20" "Text 21" "Text 22" "Text 23" "Text 24" \
  "Text 25" "Text 26" "Text 27" "Text 28" "Text 29" "Text 30" "Text 31" "Text 32" \
  "Text 33" "Text 34" "Text 35" "Text 36" "Text 37" "Text 38" "Text 39" "Text 40" \
  "Text 41" "Text 42" "Text 43" "Text 44" "Text 45" "Text 46" "Text 47" "Text 48" \
  "Text 49" "Text 50" "Text 51" "Text 52" "Text 53" "Text 54" "Text 55" "Text 56" \
  "Text 57" "Text 58" "Text 59" "Text 60" "Text 61" "Text 62" "Text 63" "Text 64" \
  "Text 65" "Text 66" "Text 67" "Text 68" "Text 69" "Text 70" "Text 71" "Text 72" \
  "Text 73" "Text 74" "Text 75" "Text 76" "Text 77" "Text 78" "Text 79" "Text 80" \
  "Text 81" "Text 82" "Text 83" "Text 84" "Text 85" "Text 86" "Text 87" "Text 88" \
  "Text 89" "Text 90" "Text 91" "Text 92" "Text 93" "Text 94" "Text 95" "Text 96" \
  "Text 97" "Text 98" "Text 99" "Text 100" "Text 101" "Text 102" "Text 103" "Text 104" \
  "Text 105" "Text 106" "Text 107" "Text 108" "Text 109" "Text 110" "Text 111" "Text 112" \
  "Text 113" "Text 114" "Text 115" "Text 116" "Text 117" "Text 118" "Text 119" "Text 120" \
  "Text 121" "Text 122" "Text 123" "Text 124" "Text 125" "Text 126" "Text 127" "Text 128"

# Test with 256 texts
echo "Testing encoding with 256 texts..."
python3 client_embeddings.py --endpoint=http://localhost:5005/embed --model=stella-trained --encoding-format=binary \
  "Text 1" "Text 2" "Text 3" "Text 4" "Text 5" "Text 6" "Text 7" "Text 8" \
  "Text 9" "Text 10" "Text 11" "Text 12" "Text 13" "Text 14" "Text 15" "Text 16" \
  "Text 17" "Text 18" "Text 19" "Text 20" "Text 21" "Text 22" "Text 23" "Text 24" \
  "Text 25" "Text 26" "Text 27" "Text 28" "Text 29" "Text 30" "Text 31" "Text 32" \
  "Text 33" "Text 34" "Text 35" "Text 36" "Text 37" "Text 38" "Text 39" "Text 40" \
  "Text 41" "Text 42" "Text 43" "Text 44" "Text 45" "Text 46" "Text 47" "Text 48" \
  "Text 49" "Text 50" "Text 51" "Text 52" "Text 53" "Text 54" "Text 55" "Text 56" \
  "Text 57" "Text 58" "Text 59" "Text 60" "Text 61" "Text 62" "Text 63" "Text 64" \
  "Text 65" "Text 66" "Text 67" "Text 68" "Text 69" "Text 70" "Text 71" "Text 72" \
  "Text 73" "Text 74" "Text 75" "Text 76" "Text 77" "Text 78" "Text 79" "Text 80" \
  "Text 81" "Text 82" "Text 83" "Text 84" "Text 85" "Text 86" "Text 87" "Text 88" \
  "Text 89" "Text 90" "Text 91" "Text 92" "Text 93" "Text 94" "Text 95" "Text 96" \
  "Text 97" "Text 98" "Text 99" "Text 100" "Text 101" "Text 102" "Text 103" "Text 104" \
  "Text 105" "Text 106" "Text 107" "Text 108" "Text 109" "Text 110" "Text 111" "Text 112" \
  "Text 113" "Text 114" "Text 115" "Text 116" "Text 117" "Text 118" "Text 119" "Text 120" \
  "Text 121" "Text 122" "Text 123" "Text 124" "Text 125" "Text 126" "Text 127" "Text 128" \
  "Text 129" "Text 130" "Text 131" "Text 132" "Text 133" "Text 134" "Text 135" "Text 136" \
  "Text 137" "Text 138" "Text 139" "Text 140" "Text 141" "Text 142" "Text 143" "Text 144" \
  "Text 145" "Text 146" "Text 147" "Text 148" "Text 149" "Text 150" "Text 151" "Text 152" \
  "Text 153" "Text 154" "Text 155" "Text 156" "Text 157" "Text 158" "Text 159" "Text 160" \
  "Text 161" "Text 162" "Text 163" "Text 164" "Text 165" "Text 166" "Text 167" "Text 168" \
  "Text 169" "Text 170" "Text 171" "Text 172" "Text 173" "Text 174" "Text 175" "Text 176" \
  "Text 177" "Text 178" "Text 179" "Text 180" "Text 181" "Text 182" "Text 183" "Text 184" \
  "Text 185" "Text 186" "Text 187" "Text 188" "Text 189" "Text 190" "Text 191" "Text 192" \
  "Text 193" "Text 194" "Text 195" "Text 196" "Text 197" "Text 198" "Text 199" "Text 200" \
  "Text 201" "Text 202" "Text 203" "Text 204" "Text 205" "Text 206" "Text 207" "Text 208" \
  "Text 209" "Text 210" "Text 211" "Text 212" "Text 213" "Text 214" "Text 215" "Text 216" \
  "Text 217" "Text 218" "Text 219" "Text 220" "Text 221" "Text 222" "Text 223" "Text 224" \
  "Text 225" "Text 226" "Text 227" "Text 228" "Text 229" "Text 230" "Text 231" "Text 232" \
  "Text 233" "Text 234" "Text 235" "Text 236" "Text 237" "Text 238" "Text 239" "Text 240" \
  "Text 241" "Text 242" "Text 243" "Text 244" "Text 245" "Text 246" "Text 247" "Text 248" \
  "Text 249" "Text 250" "Text 251" "Text 252" "Text 253" "Text 254" "Text 255" "Text 256"