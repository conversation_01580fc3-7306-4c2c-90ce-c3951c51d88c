FROM nvidia/cuda:12.8.1-cudnn-runtime-ubuntu22.04
RUN apt update
RUN apt install -y wget
RUN apt install -y gcc g++
RUN apt install -y libstdc++6 libc6-dev libopenblas-dev
RUN apt install -y vim
RUN apt install -y python3-pip
RUN pip3 install --upgrade pip
RUN apt install -y git
RUN apt install -y python3-packaging
RUN apt install -y gunicorn
COPY requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

WORKDIR /workspace/src/autopilot/services/embeddings
ENTRYPOINT ["python3", "service.py"]