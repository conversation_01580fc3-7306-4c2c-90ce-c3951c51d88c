import argparse
import os
import time

import numpy as np
import requests


def embed(endpoint, texts, model="msmarco-distilbert-base-tas-b", encoding_format="binary"):
    # Headers
    headers = {
        "Authorization": os.environ.get("UIPATH_TOKEN"),
        "Content-Type": "application/json",
        # "X-Uipath-Tenantid": os.environ.get('X_UIPATH_TENANTID'),
    }

    # Request payload
    payload = {
        "input": texts,
        "model": model,
        "encoding_format": encoding_format,
    }

    # Start timing
    start_time = time.time()

    # Make the POST request
    response = requests.post(endpoint, headers=headers, json=payload)

    # Calculate duration
    duration = time.time() - start_time
    duration_per_text = duration / len(texts)

    if response.status_code != 200:
        results = response.json()
        if "error" in results:
            return response.status_code, results["error"], duration, duration_per_text
        else:
            return response.status_code, results, duration, duration_per_text

    if payload["encoding_format"] == "binary":
        shape_str = response.headers.get("X-Embedding-Shape")
        dtype_str = response.headers.get("X-Embedding-Dtype")

        if shape_str and dtype_str:
            # Convert shape string to tuple of integers
            shape = tuple(map(int, shape_str.split(",")))

            # Convert directly to numpy array without decompression
            embeddings = np.frombuffer(response.content, dtype=dtype_str).reshape(shape)
            results = embeddings
        else:
            raise ValueError("Invalid response from embeddings service")
    elif payload["encoding_format"] == "float":
        results = response.json()
    else:
        raise ValueError("Invalid encoding format")
    return response.status_code, results, duration, duration_per_text


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Predict embeddings for a text or a list of texts")
    parser.add_argument("--endpoint", default="http://localhost:5005/embed", type=str, help="endpoint to the server")
    parser.add_argument("--model", default="all-roberta-large-v1", type=str, help="model to use for embeddings")
    parser.add_argument("--encoding-format", default="binary", help="Float or binary")
    parser.add_argument("texts", nargs="+", help="One or more texts to embed")

    args = parser.parse_args()

    status_code, results, duration, duration_per_text = embed(args.endpoint, args.texts, args.model, args.encoding_format)
    print(f"{status_code=}")
    print(f"Total request duration: {duration:.3f} seconds")
    print(f"Duration per text: {duration_per_text:.3f} seconds")
    if status_code != 200:
        print(results)
    else:
        if args.encoding_format == "float":
            print((len(results["data"]), len(results["data"][0]["embedding"])))
        if args.encoding_format == "binary":
            print(results.shape)
