# Autopilot Services

This holds all the core services logic. This package should never depend on other packages from the autopilot package. Each subpackage represents an individual service, with it's own cicd pipeline.

## Services

* **LIVE** `studio` - Service consumed by studio family of products, e.g. expression generation, activity configuration, workflow generation.
* **TODO** `ui_automation` - Service relating to ui automation, e.g. ui agent, semantic selectors.
* **TODO** `agentic_orchestration` - Service relating to agentic orchestration, e.g. bpmn generation.
* **TODO** `embeddings` - Service for creating semantic embeddings for text.

## Common

The `common` package contains code shared across all services. This should be used as a library in other services. It should not be imported directly from sources.

## Coding standards

Standards are strict in this package to ensure we don't break production and deployment go smoothly. These include:

* Runtime type checking with Pydantic and FastAPI
* Unit testing and code coverage with Pytest and minimum code coverage of 80%
* ...
