import asyncio
import json
import traceback
import warnings
from contextlib import asynccontextmanager
from typing import Any, As<PERSON><PERSON><PERSON>ator, Callable, Coroutine

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.middleware.base import Base<PERSON><PERSON>Middleware, RequestResponseEndpoint
from typing_extensions import override

from services.studio._text_to_workflow.activity_config import activity_config_endpoint
from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint
from services.studio._text_to_workflow.api.base.api import api_router as base_router
from services.studio._text_to_workflow.api.deps import get_context_from_request
from services.studio._text_to_workflow.api.v1.api import api_router as v1_router
from services.studio._text_to_workflow.api.v2.api import api_router as v2_router
from services.studio._text_to_workflow.api_workflow import api_workflow_endpoint
from services.studio._text_to_workflow.background_tasks import (
    BG_TASK_NAME,
    recurring_refresh_embeddings_task,
    shutdown_executor,
)
from services.studio._text_to_workflow.code_generation import code_generation_endpoint
from services.studio._text_to_workflow.common.state_store import clear_retrievers_cache
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.predict_expression import predict_expression_endpoint
from services.studio._text_to_workflow.testdata_generation import (
    testdata_generation_endpoint,
)
from services.studio._text_to_workflow.ui_automation import ui_automation_endpoint
from services.studio._text_to_workflow.utils.errors import (
    build_error_body,
    extract_exception_summary,
)
from services.studio._text_to_workflow.utils.request_utils import set_request_context
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    add_azure_request_tracking_middleware,
)
from services.studio._text_to_workflow.workflow_fix import workflow_fix_endpoint
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint

# Filter out FutureWarning messages
warnings.filterwarnings("ignore", category=FutureWarning)

# Types
CallNext = Callable[[Request], Coroutine[Any, Any, Response]]

# Logging
LOGGER = AppInsightsLogger()

# Excluded urls
excludelist_paths = [
    "/alive",
    "/health",
    "/health-details",
    "/swagger",
    "/openapi.json",
    "/",
]


async def _initialize_endpoints(force_rebuild: bool = False) -> None:
    if force_rebuild:
        clear_retrievers_cache()
    api_workflow_endpoint.init()
    workflow_generation_endpoint.init()
    code_generation_endpoint.init()
    activity_summary_endpoint.init()
    ui_automation_endpoint.init()
    testdata_generation_endpoint.init()
    await workflow_fix_endpoint.init()
    await activity_config_endpoint.init()
    predict_expression_endpoint.init()


# Lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Lifespan context manager for the FastAPI application.

    This context manager handles the startup and shutdown logic for the application.
    It initializes necessary components, sets up background tasks if configured,
    and ensures proper cleanup on application shutdown.

    Args:
        app (FastAPI): The FastAPI application instance.
    """
    # Startup logic here
    LOGGER.info(f"Initializing {settings.PROJECT_NAME}...")
    await _initialize_endpoints()

    # Background tasks
    if settings.RUN_EMBEDDINGS_BACKGROUND_REBUILD:
        LOGGER.info("Initializing Background tasks...")
        # Create a task that will run for the lifetime of the application
        bg_task = asyncio.create_task(
            recurring_refresh_embeddings_task(settings.REFRESH_EMBEDDINGS_INTERVAL, 0, app),
            name=BG_TASK_NAME,
        )
        app.state.background_tasks = [bg_task]  # Store task in app state for potential cancellation later

    # yield control to the event loop
    LOGGER.info("Finished initializing.")
    yield

    # Shutdown logic
    LOGGER.info("Shutdown logic")
    if settings.RUN_EMBEDDINGS_BACKGROUND_REBUILD:
        for task in app.state.background_tasks:
            LOGGER.info(f"Attempting to close background task: {task.get_name()}")
            task.cancel()
        # Await everything and discard exceptions
        await asyncio.gather(*app.state.background_tasks, return_exceptions=True)
        LOGGER.info("Attempting to close process pool executor")
        if hasattr(app.state, "executor") and app.state.executor:
            await shutdown_executor(app)


# Server
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url="/openapi.json",
    docs_url="/swagger",
    debug=settings.DEBUG_MODE,
    lifespan=lifespan,
)

if __name__ == "__main__":
    print(f"\033[32mINFO:\033[0m     Autoplot Generation Service v{settings.API_BUILD_VERSION} is running on {settings.DEPLOYMENT_REGION} environment.")


class RootPathMiddleware(BaseHTTPMiddleware):
    """Custom middleware that modifies the behavior before a request is handled."""

    @override
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # Get the x-forwarded-path header and current request path
        forwarded_path = request.headers.get("x-forwarded-path", "").rstrip("/")
        request_path = request.url.path.rstrip("/")

        # Default root path
        app.root_path = ""

        # If we have both paths, extract the root path prefix

        if forwarded_path and request_path and forwarded_path != request_path:
            # Find the root path by removing request_path from forwarded_path
            if forwarded_path.endswith(request_path):
                root_path = forwarded_path[: -len(request_path)]
                if root_path:
                    app.root_path = root_path
                    request.scope["root_path"] = root_path

        return await call_next(request)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    errors = []
    for error in exc.errors():
        if not settings.IS_DEVELOPMENT and "input" in error:
            del error["input"]
        errors.append(error)
    exc_str = json.dumps(errors)
    LOGGER.exception(f"Error parsing request body: {exc_str}")

    return JSONResponse(status_code=400, content=build_error_body(exc_str))


# TODO: This also logs the stck trace in the uvicorn err output. Haven't figured out how to stop it.
# Exception handler
# This is the first middleware to be added to the app, so it will also be the last code to be executed.
@app.exception_handler(Exception)
async def exception_handler(_: Request, ex: Exception) -> JSONResponse:
    """
    Global exception handler for the FastAPI application.

    Args:
        _ (Request): The request object (unused in this handler).
        ex (Exception): The exception that was raised.

    Returns:
        JSONResponse: A JSON response containing error details and an appropriate HTTP status code.
    """
    LOGGER.exception(traceback.format_exc())

    content: Any = None
    status: int | None = None

    content, status = extract_exception_summary(ex)

    return JSONResponse(content, status_code=status)


# Middleware to automatically set the request context
class RequestContextMiddleware(BaseHTTPMiddleware):
    """Custom middleware that modifies the behavior before a request is handled."""

    @override
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # set request context before dispatching
        request_path = request.url.path.rstrip("/")
        if request_path not in excludelist_paths:
            context = get_context_from_request(request)
            set_request_context(context)

        return await call_next(request)


# Middleware for telemetry
add_azure_request_tracking_middleware(app)

if not settings.IS_PROD:
    app.add_middleware(RootPathMiddleware)

# Middleware for setting the request context
app.add_middleware(RequestContextMiddleware)

# Routes
app.include_router(base_router)
app.include_router(v1_router)
app.include_router(v2_router, prefix="/v2")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=settings.PORT)
