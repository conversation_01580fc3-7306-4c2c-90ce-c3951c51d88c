from typing import Optional

from services.studio._text_to_workflow.common.schema import AgentDefinition, FolderDefinition
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.http_utils import http_get_json
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()
MAX_AGENTS_PER_FOLDER = 500


class OrchestratorUrls:
    """URL templates for Orchestrator OData endpoints related to folders and agents/processes."""

    @staticmethod
    def get_folders_page(organization_id: str, tenant_id: str) -> str:
        return f"{settings.CLOUD_URL_BASE}/{organization_id}/{tenant_id}/orchestrator_/api/FoldersNavigation/GetFoldersPageForCurrentUser"

    @staticmethod
    def list_releases(organization_id: str, tenant_id: str) -> str:
        return f"{settings.CLOUD_URL_BASE}/{organization_id}/{tenant_id}/orchestrator_/odata/Releases/UiPath.Server.Configuration.OData.ListReleases"


class AgentFetcher:
    """Class for fetching agent definitions and folder information from the Orchestrator OData API.

    The fetching process:
    1. Fetches all the user's available folders, recursing into each folder that has children.
    2. For each folder, retrieves the agents contained within it.
    3. Associates each agent with its containing folder for future processing.
    """

    @staticmethod
    async def _fetch_folders_page(
        context: RequestContext, parent_ids: Optional[set[int]] = None, existing_folders: Optional[dict[int, FolderDefinition]] = None
    ) -> dict[int, FolderDefinition]:
        """
        Fetches the folders page for the current user. If parent IDs are provided, expands those parent folders to include their children.

        Args:
            context: The request context containing organization and tenant IDs.
            parent_ids: An optional set of parent folder IDs to expand.
            existing_folders: Optional dictionary of already discovered folders.

        Returns:
            A dictionary mapping folder IDs to their corresponding FolderDefinitions, containing only the newly discovered folders.
        """
        if not context.organization_id or not context.tenant_id:
            raise ValueError("Organization ID and Tenant ID are required")

        url = OrchestratorUrls.get_folders_page(context.organization_id, context.tenant_id)
        params: list[tuple[str, str]] = [("includePersonalWorkspace", "true")]
        if parent_ids:
            # Intentionally add each parent ID as a separate expandedParentIds parameter to avoid doing multiple requests.
            for parent_id in parent_ids:
                params.append(("expandedParentIds", str(parent_id)))
        headers = {"Authorization": context.raw_jwt or ""}

        try:
            data = await http_get_json(url, headers, params)
            if not isinstance(data, dict):
                raise ValueError(f"Expected dictionary response from folders page, got {type(data)}")
            new_folders = {}

            for folder in data.get("PageItems", []):
                folder_id = int(folder["Id"])

                # Skip folders that were previously added when we haven't recursed into children yet
                if existing_folders and folder_id in existing_folders:
                    continue

                folder_def = FolderDefinition(
                    fqn=str(folder["FullyQualifiedName"]), has_children=bool(folder["HasChildren"]), id=folder_id, description=folder.get("Description", "N/A")
                )
                new_folders[folder_id] = folder_def

            return new_folders

        except Exception as e:
            LOGGER.exception(f"Failed to fetch the user folders: {e}")
            raise

    @staticmethod
    async def get_folders(context: RequestContext) -> dict[int, FolderDefinition]:
        """
        Retrieves all available folders for the given context, including child folders.
        This will flatten the folder hierarchy.

        Args:
            context: The request context containing organization and tenant IDs.

        Returns:
            A dictionary mapping folder IDs to their corresponding FolderDefinitions.
        """
        all_folders = await AgentFetcher._fetch_folders_page(context)

        folders_with_children = {fid for fid, folder in all_folders.items() if folder.has_children}
        while folders_with_children:
            child_folders = await AgentFetcher._fetch_folders_page(context, folders_with_children, all_folders)
            all_folders.update(child_folders)
            folders_with_children = {fid for fid, folder in child_folders.items() if folder.has_children}

        return all_folders

    @staticmethod
    async def get_agents(context: RequestContext, folder: Optional[FolderDefinition] = None) -> list[AgentDefinition]:
        """
        Retrieves agents, either from a specific folder or all available agents.

        Args:
            context: The request context containing organization and tenant IDs.
            folder: Optional FolderDefinition for the folder to retrieve agents from. If None, retrieves all available agents.

        Returns:
            A list of AgentDefinitions corresponding to the agents.
        """
        if not context.organization_id or not context.tenant_id:
            raise ValueError("Organization ID and Tenant ID are required")

        url = OrchestratorUrls.list_releases(context.organization_id, context.tenant_id)
        params = {
            "$select": "Id,Key,Name,Description,ArgumentsV2",
            "$top": MAX_AGENTS_PER_FOLDER,
            "$filter": "ProcessType eq 'Agent'",
            "$expand": "EntryPointV2",
        }
        headers = {"Authorization": context.raw_jwt or ""}

        if folder is not None:
            headers["X-Uipath-Organizationunitid"] = str(folder.id)

        try:
            data = await http_get_json(url, headers, params)
            if not isinstance(data, dict):
                raise ValueError(f"Expected dictionary response from releases list, got {type(data)}")
            agents: list[AgentDefinition] = []

            for agent in data.get("value", []):
                agent_def = AgentDefinition(
                    id=int(agent["Id"]),
                    key=str(agent["Key"]),
                    name=str(agent["Name"]),
                    description=agent.get("Description", "N/A"),
                    arguments_schema=agent["ArgumentsV2"],
                    folder=folder,
                )
                agents.append(agent_def)

            return agents

        except Exception as e:
            LOGGER.exception(f"Failed to retrieve agents{f' for folder {folder.id}' if folder is not None else ''}: {e}")
            raise

    @staticmethod
    async def populate_folder_details_for_agents(context: RequestContext, agents: set[AgentDefinition]) -> None:
        """
        Updates the folder definition of each agent in the input by searching through all folders.

        Args:
            context: The request context containing organization and tenant IDs.
            agents: Set of AgentDefinitions to find and update with their corresponding folders.
        """
        folders = await AgentFetcher.get_folders(context)
        remaining_agent_ids = {agent.id for agent in agents}
        id_to_agent = {agent.id: agent for agent in agents}

        for folder in folders.values():
            if not remaining_agent_ids:
                break

            try:
                curr_agents = await AgentFetcher.get_agents(context, folder)
                for agent in curr_agents:
                    if agent.id in remaining_agent_ids:
                        id_to_agent[agent.id].folder = folder
                        remaining_agent_ids.remove(agent.id)
            except Exception as e:
                LOGGER.exception(f"Failed to retrieve agents for folder {folder.id}: {e}")
                raise
