import typing as t

from .schema import InputProperty, OutputType


def remove_fields(workflow, fields):
    if isinstance(workflow, dict):
        for field in fields:
            if field in workflow:
                del workflow[field]
        for v in workflow.values():
            remove_fields(v, fields)
    if isinstance(workflow, list):
        for v in workflow:
            remove_fields(v, fields)
    return workflow


def get_output_type(json_schema: dict) -> t.Optional[OutputType]:
    """Parse the output type details from the JSON schema of a DAP activity"""

    if "properties" not in json_schema:
        return None

    object_output_properties = [
        item for item in json_schema["properties"].values() if item["direction"] == "output" and (item.get("$ref") or item.get("items", {}).get("$ref"))
    ]
    if len(object_output_properties) > 1:
        raise ValueError("Multiple output properties with $ref are not supported")

    if not object_output_properties:
        return None

    is_array = _is_array_output_property(object_output_properties[0])

    type = object_output_properties[0]["items"]["$ref"] if is_array else object_output_properties[0]["$ref"]

    type_name = _get_type_name_from_ref(type)

    # prune the definitions to remove the descriptions and examples, as well as any properties referencing objects deeper than 3 levels (root included)
    try:
        definitions = _prune_definition_schema(type_name, json_schema["definitions"], 2)
    except Exception as e:
        print(f"Error pruning definitions: {e}")
        return None

    # if we don't have any definitions, we can't return an output type
    if definitions is None or len(definitions) == 0:
        return None

    return OutputType(
        type=type_name,
        definitions=definitions,
        isArray=is_array,
    )


def _prune_definition_schema(object_name: str, original_definitions: dict, max_depth: int = 3, current_depth: int = 0) -> dict:
    """Remove the descriptions from all the properties in the schema and eliminate all properties referencing objects deeper than the given depth"""

    # If we've reached the max depth, return an empty schema
    if current_depth > max_depth:
        return {}

    return_defs = {}
    current_schema = original_definitions[object_name]
    new_properties = {}

    for property_name, property_schema in current_schema["properties"].items():
        if "description" in property_schema:
            del property_schema["description"]
        if "examples" in property_schema:
            del property_schema["examples"]

        # we're referencing another object, so we need to prune it
        if "$ref" in property_schema:
            type_name = _get_type_name_from_ref(property_schema["$ref"])
            item_defs = _prune_definition_schema(type_name, original_definitions, max_depth, current_depth + 1)
            # if we don't have any objects extracted, we can skip the property
            if len(item_defs) == 0:
                continue
            return_defs.update(item_defs)

        # if we're referencing an array, we need to prune the items
        if "items" in property_schema and "$ref" in property_schema["items"]:
            type_name = _get_type_name_from_ref(property_schema["items"]["$ref"])
            item_defs = _prune_definition_schema(type_name, original_definitions, max_depth, current_depth)  # we will ignore the current depth for arrays
            # if we don't have any objects extracted, we can skip the property
            if len(item_defs) == 0:
                continue
            return_defs.update(item_defs)

        # property is valid, let's save it
        new_properties[property_name] = property_schema

    if len(new_properties) == 0:
        return {}

    # replace the properties with the new ones
    current_schema["properties"] = new_properties
    result = {object_name: current_schema}
    result.update(return_defs)
    return result


def _get_type_name_from_ref(ref: str) -> str:
    return ref.split("/")[-1]


def _is_array_output_property(output_property: dict) -> bool:
    return output_property.get("items") is not None


def get_input_properties(json_schema: dict) -> t.Optional[list[InputProperty]]:
    """Parse the input properties from the JSON schema of a DAP activity"""

    if "properties" not in json_schema:
        return None

    properties: list[InputProperty] = []
    for prop_name, prop_spec in json_schema["properties"].items():
        if prop_spec["direction"] != "input":
            continue

        # Trim leading underscores from property name - this causes invalid JSON schema properties
        # we should make sure to reverse this when converting to an actual workflow
        schema_name = prop_name.lstrip("_")

        is_required = prop_name in json_schema.get("required", [])

        properties.append(
            InputProperty(
                name=prop_name,
                description=prop_spec.get("description", None),
                location=prop_spec.get("location", None),
                schema_name=schema_name,
                type=prop_spec.get("type", "string"),
                required=is_required,
                visibleByDefault=prop_spec.get("visibleByDefault", None),
            )
        )

    return properties
