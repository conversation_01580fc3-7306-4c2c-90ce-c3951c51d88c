import copy
import re

import typing_extensions as t

from services.studio._text_to_workflow.common import helpers
from services.studio._text_to_workflow.common.constants import DELEGATE_VARIABLES_KEY
from services.studio._text_to_workflow.common.schema import ParamType<PERSON>ategor<PERSON>, ParamValueCategory, TypeDef, Variable

_trivial_string_format_regex = re.compile(r"^\[\[string\.Format\(\"\{0\}\",\s*([^)]+)\)\]\]$")
_variable_reference_regex = re.compile(r"^\[\[[a-zA-Z]+[0-9a-zA-Z_]*\]\]$")


def is_variable_reference(param_value: str) -> bool:
    return bool(_variable_reference_regex.match(param_value))


def is_expression(param_value: str) -> bool:
    return param_value.startswith("[[") and param_value.endswith("]]")


def get_param_value_category(param_value: t.Any) -> ParamValueCategory:
    if param_value is None:
        return None
    elif isinstance(param_value, (bool, int, float)):
        return "primitive_property"
    elif isinstance(param_value, str):
        if is_variable_reference(param_value):
            return "variable"
        elif is_expression(param_value):
            return "expression"
        else:
            return "primitive_property"
    elif isinstance(param_value, dict):
        if "Handler" in param_value:
            return "activity_action"
        elif "Body" in param_value:
            return "activity_sequence"
        elif "activity" in param_value:
            return "activity_sequence"  # in this scenario, our dictionary is an activity sequence with a single activity
        else:
            return "dict_property"
    elif isinstance(param_value, list):
        if param_value and isinstance(param_value[0], dict) and param_value[0].get("activity", None) is not None:
            return "activity_sequence"
        else:
            return "list_property"
    else:
        raise ValueError(f"Unknown param value type: {type(param_value)}")


def exclude_nonexistent_params(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
) -> str | None:
    if param_name in activity_typedef["params"]:
        return param_value
    return None


def exclude_param_type_categories(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
) -> str | None:
    if param_value is None:
        return param_value
    param_type_category = activity_typedef["params"].get(param_name, {}).get("category")
    if param_type_category in allowed_types:
        return param_value
    return None


def exclude_param_value_categories(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
) -> str | None:
    if param_value is None:
        return param_value
    param_value_category = get_param_value_category(param_value)
    if param_value_category in allowed_values:
        return param_value
    return None


def exclude_nonexistent_variables(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
) -> str | None:
    param_type_category = activity_typedef["params"].get(param_name, {}).get("category")
    variable_name = try_extract_input_variable_reference(param_value, param_type_category)

    if variable_name in variables:
        return param_value
    return None


def try_extract_input_variable_reference(param_value: t.Any, param_type_category: ParamTypeCategory | None) -> str | None:
    """
    Tries to extract the input variable reference from the param value.
    Returns None if the param value is not a string, is not a variable reference, or is an output variable reference.
    """
    if param_value is None:
        return None
    if not isinstance(param_value, str):
        return None
    if not is_variable_reference(param_value):
        return None
    if param_type_category == "OutArgument":
        return None

    return param_value.removeprefix("[[").removesuffix("]]")


def normalize_trivial_string_format(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
) -> str | None:
    # TODO: This cleanup should be fixed in StudioWeb or Converter. This should be only temporary.
    if param_value is None:
        return param_value
    if not isinstance(param_value, str):
        return param_value
    match = _trivial_string_format_regex.fullmatch(param_value)
    if not match:
        return param_value
    return f"[[{match.group(1)}]]"


def clean_activity_action(
    param_value: t.Any,
    param_name: str,
    activity_typedef: TypeDef,
    variables: set[str],
    allowed_types: set[ParamTypeCategory],
    allowed_values: set[ParamValueCategory],
):
    if param_value is None:
        return param_value
    param_type_category = activity_typedef["params"].get(param_name, {}).get("category")
    if param_type_category != "ActivityAction":
        return param_value
    if not isinstance(param_value, dict):
        return param_value
    return {DELEGATE_VARIABLES_KEY: param_value.get(DELEGATE_VARIABLES_KEY, {})}


def sanitize(params: dict, activity_typedef: TypeDef) -> dict:
    clean_target_params = copy.deepcopy(params)
    for key in clean_target_params.keys():
        clean_target_params[key] = normalize_trivial_string_format(clean_target_params[key], key, activity_typedef, set(), set(), set())
        clean_target_params[key] = exclude_nonexistent_params(clean_target_params[key], key, activity_typedef, set(), set(), set())
    for key in tuple(clean_target_params.keys()):
        if clean_target_params[key] is None:
            del clean_target_params[key]
    return clean_target_params


def get_concrete_activity_fqn(activity_fqn: str, params: dict, activity_typedef: TypeDef) -> str:
    activity_fqn, generic = helpers.extract_generic_pattern(activity_fqn)
    if not generic:
        return activity_fqn
    for param_name in params:
        param_value = params[param_name]
        param_type_category = activity_typedef["params"].get(param_name, {}).get("category")
        if param_type_category != "ActivityAction":
            continue
        if not isinstance(param_value, dict):
            continue
        variables: list[Variable] = param_value.get(DELEGATE_VARIABLES_KEY, [])
        if not variables:
            continue
        type = variables[0]["type"]
        return f"{activity_fqn}<{type}>"
    return activity_fqn
