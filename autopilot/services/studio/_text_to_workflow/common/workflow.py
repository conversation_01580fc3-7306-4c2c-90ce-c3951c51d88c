import copy
import uuid

import typing_extensions as t

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.constants import (
    ACTIVITY_ACTION_KEY,
    DELEGATE_HANDLER_KEY,
    DELEGATE_VARIABLES_KEY,
    SEQUENCE_GENERATION_INSERTION_PHRASE,
    TYPE_HINT_KEY,
)
from services.studio._text_to_workflow.common.embeddingsdb import create_dynamic_activity_full_id
from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.common.schema import ActivityDelegateDict, ActivityDict, Argument, Package, Variable, WorkflowDict
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump

LOGGER = AppInsightsLogger()
RETRIEVER = ActivitiesRetriever()

LEGACY_DAP_TYPE_ID_KEY = "UiPathActivityTypeId"
DAP_TYPE_ID_KEY = "uiPathActivityTypeId"
DAP_DYNAMIC_ACTIVITY_DETAILS_KEY = "dynamicActivityDetails"
DAP_IS_CONFIGURED_KEY = "isConfigured"
DAP_CONFIG_KEY = "configuration"
DAP_CONNECTOR_KEY = "connectorKey"


class Activity:
    _primitive_types = (str, int, float, bool, type(None))
    _complex_types = (tuple, list, set, frozenset, dict)

    id: str
    activity_id: str
    fqn: str
    display_name: str
    is_current_activity: bool
    params: dict[str, t.Any]
    primitive_properties: dict[str, str]
    simple_list_properties: dict[str, list[str]]
    complex_list_properties: dict[str, t.Any]
    simple_dict_properties: dict[str, dict[str, str]]
    complex_dict_properties: dict[str, t.Any]
    activities: dict[str, list["Activity"]]
    activity_delegates: dict[str, list["Activity"]]
    workflow_arguments: dict[str, Argument]
    workflow_variables: dict[str, Variable]
    local_variables: dict[str, dict[str, Variable]]
    is_dynamic: bool
    dap_type_id: str
    dap_is_configured: bool
    dynamic_activity_details: str
    dap_config: str
    dap_connector_key: str
    typename: str

    def __init__(
        self,
        lmyaml: ActivityDict,
        workflow_arguments: dict[str, Argument],
        workflow_variables: dict[str, Variable],
        parent: t.Union["Workflow", "Activity", None],
    ) -> None:
        self.fqn = lmyaml["activity"]
        self.activity_id, generic = extract_generic_pattern(self.fqn)
        if generic and generic.startswith("<") and generic.endswith(">"):
            self.typename = generic[1:-1]
        self.short_activity_id = self.activity_id.rsplit(".", 1)[-1]
        # TODO: what if the activity_id is the fake activity name? Should we lookup and set the is_dynamic and uiPathActivityTypeId?
        self.is_dynamic = False
        if (_activity_type_id := lmyaml.get(DAP_TYPE_ID_KEY)) is not None:
            self._handle_dap_properties(lmyaml, _activity_type_id)
        elif (_activity_type_id := (lmyaml.get("params", {}) or {}).get(LEGACY_DAP_TYPE_ID_KEY)) is not None:  # ["params"] can be None
            self._handle_dap_properties(lmyaml, _activity_type_id)

        # we could enforce uniqueness on default (~1e-7 prob of collision)
        # if we want to change the default for self.id, please leave it starting with `{self.activity_id}`
        self.id = lmyaml.get("id", f"{self.short_activity_id}_{uuid.uuid4().hex[:6].upper()}")
        # self.id = lmyaml.get("id", f"{self.short_activity_id}_{index_of_activity}")  # if you want to control the randomness of generating ids (use global indexing)

        self.display_name = lmyaml.get("thought", self.fqn)
        self.is_current_activity = lmyaml.get("currentActivity", False)

        self.workflow_arguments = copy.deepcopy(workflow_arguments)
        self.workflow_variables = copy.deepcopy(workflow_variables)

        self.parent = parent
        self.params = lmyaml.get("params", {})  # careful handling this (shallow copy) - not a deepcopy due to performance reasons
        self.handle_params()

    def set_random_id(self):
        self.id = f"{self.short_activity_id}_{uuid.uuid4().hex[:6].upper()}"

    def get_model_activity_name(self) -> str:
        if not self.is_dynamic:
            return self.fqn
        activity_def = RETRIEVER.get(self.activity_id, target_framework="Windows")
        if activity_def is None:
            LOGGER.warning(f"Dynamic activity {self.activity_id} not found, using full activity name {self.fqn} instead.")
            return self.fqn
        return activity_def["fullClassName"]

    def __repr__(self) -> str:
        return f"Activity - {self.id} - {self.activity_id} - {self.display_name}"

    def clone(self, parent: "Activity | None" = None) -> "Activity":
        return copy.deepcopy(self)

    def to_dict(
        self,
        keep_original_params_order: bool = True,
        include_complex_properties: bool = True,
        include_ids: bool = False,
        include_dap_properties: bool = False,
        inhibit_child_activity_expansion: bool = False,
        include_thoughts: bool = True,
        use_dap_activity_name: bool = False,
        include_params_when_empty: bool = False,
    ) -> ActivityDict:
        """Provide a dict serialization of the parsed workflow content"""
        _kwargs_for_recursive_calls = {
            "keep_original_params_order": keep_original_params_order,
            "include_complex_properties": include_complex_properties,
            "include_ids": include_ids,
            "include_dap_properties": include_dap_properties,
            "include_thoughts": include_thoughts,
            "use_dap_activity_name": use_dap_activity_name,
        }

        def _recursive_call(activity: "Activity"):
            if inhibit_child_activity_expansion:
                return activity.activity_id
            return activity.to_dict(**_kwargs_for_recursive_calls)

        # order for the dictionaries' keys matters
        if use_dap_activity_name and self.is_dynamic:
            activity_def = RETRIEVER.get(self.activity_id, target_framework="Portable", activity_type="activity")
            if activity_def is None:
                LOGGER.warning(f"Dynamic activity {self.activity_id} not found, using full activity name {self.fqn} instead.")
                activity_name = self.fqn
            else:
                activity_name = activity_def["fullClassName"]
        else:
            activity_name = self.fqn

        serialization = {}
        if include_thoughts:
            serialization["thought"] = self.display_name
        serialization["activity"] = activity_name

        if self.is_current_activity:
            serialization["currentActivity"] = True
        if include_ids:
            serialization["id"] = self.id
        if self.is_dynamic:
            if include_dap_properties:
                serialization[DAP_CONFIG_KEY] = self.dap_config
                serialization[DAP_CONNECTOR_KEY] = self.dap_connector_key
                serialization[DAP_DYNAMIC_ACTIVITY_DETAILS_KEY] = self.dynamic_activity_details
                serialization[DAP_IS_CONFIGURED_KEY] = self.dap_is_configured
                serialization["isDynamic"] = self.is_dynamic
            serialization[DAP_TYPE_ID_KEY] = self.dap_type_id

        params = {}

        # fill in properties, in order: primitive, list, dict
        params.update(self.primitive_properties)
        for k, v in self.simple_list_properties.items():
            if k in params:
                LOGGER.warning(f"Key conflict {k} in {self.fqn}")
                params[k].append(v)
                continue
            params[k] = v
        if include_complex_properties:
            for k, v in self.complex_list_properties.items():
                if k in params:
                    LOGGER.warning(f"Key conflict {k} in {self.fqn}")
                    params[k].append(v)
                    continue
                params[k] = v

        def _gather_from_dict(source: dict[str, t.Any]):
            for k, v in source.items():
                if k in params:
                    LOGGER.warning(f"Key conflict {k} in {self.fqn}")
                    assert set(v.keys()).isdisjoint(set(params[k].keys())), "No disjoint sets allowed"
                    params[k].update(v)
                    continue
                params[k] = v

        _gather_from_dict(self.simple_dict_properties)
        if include_complex_properties:
            _gather_from_dict(self.complex_dict_properties)

        ignore_keys = set()
        if self.switch_cases:
            cases = {}
            for case_key in self.switch_cases:
                case_internal_key = f"Case {case_key}"
                ignore_keys.add(case_internal_key)
                if case_internal_key not in self.activities:  # skip case
                    cases[case_key] = None
                    continue
                cases[case_key] = [_recursive_call(act) for act in self.activities[case_internal_key]]
            params["Cases"] = cases

        # order of activities is assumed to be kept as in the original (e.g. If: Then Else)
        # activities will always be included in a list container, even when they are the only activity
        for k, v in self.activities.items():
            if k in ignore_keys:
                continue
            assert k not in params, f"Key activity conflict {k} in {self.fqn}"
            params[k] = [_recursive_call(act) for act in v]

        # in terms of order, variables section is enforced before the Handler
        assert all(k in self.activity_delegates for k in self.local_variables), "Not all vars are from delegates"
        for k, v in self.activity_delegates.items():
            assert k not in params, f"Key activity delegate conflict {k} in {self.fqn}"
            variables = self.local_variables[k]
            handler_serialization: dict[str, t.Any] = {DELEGATE_VARIABLES_KEY: list(variables.values())} if variables else {}
            handler_serialization.update({DELEGATE_HANDLER_KEY: [_recursive_call(act) for act in v]})

            if k.startswith("Catches"):  # Catches__type (it was only Catches at load time)
                k, _type = k.split("_", 1)
                handler_serialization = {TYPE_HINT_KEY: _type, ACTIVITY_ACTION_KEY: handler_serialization}
                params[k] = params.get(k, []) + [handler_serialization]  # not that efficient (might not matter), but shorter than an if else
                continue

            params[k] = handler_serialization

        if keep_original_params_order:
            original_keys_set = set(self.params_original_order)
            new_params = {k: v for k, v in params.items() if k not in original_keys_set}
            new_params.update({k: params[k] for k in self.params_original_order if k in params})
            params = new_params

        if params or include_params_when_empty:
            serialization["params"] = params
        return ActivityDict(**serialization)

    def lmyaml(
        self,
        keep_original_params_order: bool = True,
        include_complex_properties: bool = True,
        include_ids: bool = False,
        include_dap_properties: bool = False,
        inhibit_child_activity_expansion: bool = False,
    ):
        _dict = self.to_dict(
            keep_original_params_order=keep_original_params_order,
            include_complex_properties=include_complex_properties,
            include_ids=include_ids,
            include_dap_properties=include_dap_properties,
            inhibit_child_activity_expansion=inhibit_child_activity_expansion,
        )
        return yaml_dump(_dict)

    def handle_params(self) -> None:
        """Partition params into primitive, list, dict properties and activities"""
        self.params_original_order = tuple(self.params.keys())
        self.primitive_properties = {}
        self.simple_list_properties = {}
        self.complex_list_properties = {}
        self.simple_dict_properties = {}
        self.complex_dict_properties = {}
        self.switch_cases = []  # special object used for reconstructing switch cases
        self.activities = {}
        self.activity_delegates = {}
        self.local_variables = {}
        for param_key, param_value in self.params.items():
            if self._is_ignored_param(param_key, param_value):
                continue
            elif self._is_switch_cases_branch(param_key, param_value):
                self._handle_switch_cases_branch(param_key, param_value)
            elif self._is_catches_branch(param_key, param_value):
                self._handle_catches_branch(param_key, param_value)
            elif self._is_activity_delegate_property(param_key, param_value):
                self._handle_activity_delegate_property(param_key, param_value)
            elif self._is_activity_property(param_key, param_value):
                self._handle_activity_property(param_key, param_value)
            elif self._is_sequence_property(param_key, param_value):
                self._handle_sequence_property(param_key, param_value)
            elif self._is_simple_list_property(param_key, param_value):
                self._handle_simple_list_property(param_key, param_value)
            elif self._is_complex_list_property(param_key, param_value):
                self._handle_complex_list_property(param_key, param_value)
            elif self._is_simple_dict_property(param_key, param_value):
                self._handle_simple_dict_property(param_key, param_value)
            elif self._is_complex_dict_property(param_key, param_value):
                self._handle_complex_dict_property(param_key, param_value)
            elif self._is_primitive_property(param_key, param_value):
                self._handle_primitive_property(param_key, param_value)
            else:
                LOGGER.error(f"Unknown property type for {param_key}: {param_value}")
                raise errors.UnhandledPropertyError(self.activity_id, param_key)

    def _is_primitive_property(self, param_key: str, param_value: t.Any) -> bool:
        return isinstance(param_value, self._primitive_types)

    def _handle_primitive_property(self, param_key: str, param_value: str):
        self.primitive_properties[param_key] = param_value

    def _is_simple_list_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, list):
            return False
        return all(isinstance(value, self._primitive_types) for value in param_value)

    def _handle_simple_list_property(self, param_key: str, param_value: list[str]):
        self.simple_list_properties[param_key] = param_value

    def _is_complex_list_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, list):
            return False
        if self._is_simple_list_property(param_key, param_value):
            return False
        if self._is_sequence_property(param_key, param_value):
            return False
        return all(isinstance(value, self._complex_types) for value in param_value)

    def _handle_complex_list_property(self, param_key: str, param_value: list[str]):
        self.complex_list_properties[param_key] = param_value

    def _is_simple_dict_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, dict):
            return False
        return all(isinstance(value, self._primitive_types) for value in param_value.values())

    def _handle_simple_dict_property(self, param_key: str, param_value: dict[str, str]):
        self.simple_dict_properties[param_key] = param_value

    def _is_complex_dict_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, dict):
            return False
        if self._is_simple_dict_property(param_key, param_value):
            return False
        if self._is_activity_property(param_key, param_value):
            return False
        if self._is_activity_delegate_property(param_key, param_value):
            return False
        return isinstance(param_value, self._complex_types)

    def _handle_complex_dict_property(self, param_key: str, param_value: dict[str, str]):
        self.complex_dict_properties[param_key] = param_value

    def _is_activity_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, dict):
            return False
        return "thought" in param_value and "activity" in param_value

    def _handle_activity_property(self, param_key: str, param_value: ActivityDict) -> None:
        self.activities[param_key] = [Activity(param_value, self.workflow_arguments, self.workflow_variables, parent=self)]

    def _is_sequence_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, list):
            return False
        if len(param_value) == 0:
            return False
        if not all(isinstance(value, dict) for value in param_value):
            return False
        return all(("activity" in value and "thought" in value) for value in param_value)

    def _handle_sequence_property(self, param_key: str, param_value: list[ActivityDict]) -> None:
        self.activities[param_key] = [Activity(activity, self.workflow_arguments, self.workflow_variables, parent=self) for activity in param_value]

    def _is_activity_delegate_property(self, param_key: str, param_value: t.Any) -> bool:
        if not isinstance(param_value, dict):
            return False
        if DELEGATE_HANDLER_KEY not in param_value:
            return False
        if self._is_sequence_property(DELEGATE_HANDLER_KEY, param_value[DELEGATE_HANDLER_KEY]):
            return True
        if self._is_activity_property(DELEGATE_HANDLER_KEY, param_value[DELEGATE_HANDLER_KEY]):
            return True
        return False

    def _handle_activity_delegate_property(self, param_key: str, param_value: ActivityDelegateDict) -> None:
        self.local_variables[param_key] = {variable["name"]: variable for variable in param_value.get(DELEGATE_VARIABLES_KEY, [])}
        activities_container: list[ActivityDict] = param_value[DELEGATE_HANDLER_KEY]
        if isinstance(activities_container, dict):
            activities_container = [activities_container]  # type: ignore (activities container can be malformed and contain a dict directly)
        self.activity_delegates[param_key] = [
            Activity(activity, self.workflow_arguments, self.workflow_variables, parent=self) for activity in activities_container
        ]

    # TODO: Handle Try-Catch better
    def _is_catches_branch(self, param_key: str, param_value: t.Any) -> bool:
        if param_key != "Catches":
            return False
        if not isinstance(param_value, list):
            return False
        if len(param_value) == 0:
            return False
        representative = param_value[0]
        if not isinstance(representative, dict):
            return False
        return ACTIVITY_ACTION_KEY in representative and TYPE_HINT_KEY in representative

    def _handle_catches_branch(self, param_key: str, param_value: list[dict]) -> None:
        for activity_delegate in param_value:
            if DELEGATE_VARIABLES_KEY not in activity_delegate[ACTIVITY_ACTION_KEY]:
                LOGGER.warning(f"Catches delegate variable dictionary missing: {activity_delegate}")
                continue
            if DELEGATE_HANDLER_KEY not in activity_delegate[ACTIVITY_ACTION_KEY]:
                LOGGER.warning(f"Catches delegate handler missing: {activity_delegate}")
                continue

            catch_key = f"{param_key}_{activity_delegate[TYPE_HINT_KEY]}"  # there can be multiple Catch blocks in a Try activity (each with its own __type)
            self.local_variables[catch_key] = {variable["name"]: variable for variable in activity_delegate[ACTIVITY_ACTION_KEY][DELEGATE_VARIABLES_KEY]}

            activities_container: list[ActivityDict] = activity_delegate[ACTIVITY_ACTION_KEY][DELEGATE_HANDLER_KEY]
            if isinstance(activities_container, dict):
                activities_container = [activities_container]  # type: ignore (Handler can also have only one activity, which is not included in a list)
            self.activity_delegates[catch_key] = [
                Activity(activity, self.workflow_arguments, self.workflow_variables, parent=self) for activity in activities_container
            ]

    def _is_switch_cases_branch(self, param_key: str, param_value: t.Any) -> bool:
        if not param_key == "Cases":
            return False
        if not isinstance(param_value, dict):
            return False
        for case_key, case_sequence in param_value.items():
            if not self._is_switch_case(case_key, case_sequence):
                return False
        return True

    def _is_switch_case(self, case_key: str, case_value: t.Any) -> bool:
        if case_value is None:  # these can happen when the case is forwarded
            return True
        if self._is_sequence_property(case_key, case_value):
            return True
        if self._is_activity_property(case_key, case_value):
            return True
        return False

    def _handle_switch_cases_branch(self, param_key: str, param_value: dict[str, list[ActivityDict] | ActivityDict | None]) -> None:
        for case_key, case_sequence in param_value.items():
            self.switch_cases.append(case_key)
            if case_sequence is None:  # these can happen when the case is forwarded
                continue
            if isinstance(case_sequence, dict):
                case_sequence = [case_sequence]  # type: ignore (case_sequence can be a dict)
            self.activities[f"Case {case_key}"] = [
                Activity(activity, self.workflow_arguments, self.workflow_variables, parent=self) for activity in case_sequence
            ]

    def _is_ignored_param(self, param_key: str, param_value: t.Any) -> bool:
        return param_key in {LEGACY_DAP_TYPE_ID_KEY}

    def _handle_dap_properties(self, lmyaml: ActivityDict, type_id: str) -> None:
        self.activity_id = create_dynamic_activity_full_id(self.activity_id, type_id)
        self.is_dynamic = True
        self.dap_type_id = type_id
        self.dap_is_configured = lmyaml.get(DAP_IS_CONFIGURED_KEY, False)
        self.dap_config = lmyaml.get(DAP_CONFIG_KEY, "")
        self.dynamic_activity_details = lmyaml.get(DAP_DYNAMIC_ACTIVITY_DETAILS_KEY, "")
        self.dap_connector_key = lmyaml.get(DAP_CONNECTOR_KEY, "")

    def _get_summary(self):
        # Note: make sure to not escape '{' by mistake i.e. the space before it is mandatory
        # to have the dictionary comprehension interpreted correctly.
        return f"""\
Activity: {self.fqn}
    Display name: {self.display_name}
    Is current activity {self.is_current_activity}
    Primitive Properties: {self.primitive_properties}
    List Properties: {self.simple_list_properties}
    Dict Properties: {self.simple_dict_properties}
    Local Variables: {self.local_variables}
    Activities: { {k: [act.fqn for act in act_list] for k, act_list in self.activities.items()} }
    Activity Delegates: { {k: [act.fqn for act in act_list] for k, act_list in self.activity_delegates.items()} }
"""


class Workflow:
    name: str
    description: str
    plan: str
    trigger: Activity | None
    activities: list[Activity]
    packages: list[Package]
    arguments: dict[str, Argument]
    variables: dict[str, Variable]
    namespace_imports: list[str]

    def __init__(self, description: str, plan: str, lmyaml: WorkflowDict) -> None:
        self.parent = None  # to make it easier to identify it in an activity tree as the root
        self.description = description
        self.plan = plan
        self.name = lmyaml.get("processName", "Blank Process")
        self.packages = []
        for package in lmyaml.get("packages", []):
            package_name, package_version = package.split(", ")
            self.packages.append(Package(name=package_name, version=package_version))
        self.arguments = {argument["name"]: argument for argument in lmyaml.get("arguments", [])}
        self.variables = {variable["name"]: variable for variable in lmyaml.get("variables", [])}
        self.namespace_imports = lmyaml.get("namespaceImports", [])
        self.trigger = Activity(lmyaml["trigger"], self.arguments, self.variables, parent=self) if "trigger" in lmyaml else None
        self.activities = [Activity(activity, self.arguments, self.variables, parent=self) for activity in lmyaml.get("workflow", [])]

    def __repr__(self) -> str:
        return f"Workflow - {self.name} - {self.description}"

    def to_dict(
        self,
        include_packages: bool = True,
        include_variables: bool = True,
        include_arguments: bool = True,
        keep_original_params_order: bool = True,
        include_complex_properties: bool = True,
        include_ids: bool = False,
        include_name: bool = True,
        include_namespaces: bool = False,
        include_dap_properties: bool = False,
        include_thoughts: bool = True,
        use_dap_activity_name: bool = False,
    ) -> WorkflowDict:
        _activity_kwargs = {
            "keep_original_params_order": keep_original_params_order,
            "include_complex_properties": include_complex_properties,
            "include_ids": include_ids,
            "include_dap_properties": include_dap_properties,
            "include_thoughts": include_thoughts,
            "use_dap_activity_name": use_dap_activity_name,
        }
        _dict: WorkflowDict = {}  # type: ignore
        if include_name and self.name is not None:
            _dict["processName"] = self.name
        if include_packages:
            _dict["packages"] = [f"{package['name']}, {package['version']}" for package in self.packages]
        if self.trigger:
            _dict["trigger"] = self.trigger.to_dict(**_activity_kwargs)
        _dict["workflow"] = [activity.to_dict(**_activity_kwargs) for activity in self.activities]
        if include_variables and self.variables:
            _dict["variables"] = list(self.variables.values())
        if include_arguments and self.arguments:
            _dict["arguments"] = list(self.arguments.values())
        if include_namespaces and self.namespace_imports:
            _dict["namespaceImports"] = list(self.namespace_imports)
        return _dict

    def lmyaml(
        self,
        include_packages: bool = True,
        include_variables: bool = True,
        include_arguments: bool = True,
        keep_original_params_order: bool = True,
        include_complex_properties: bool = True,
        include_ids: bool = False,
        include_name: bool = False,
        include_namespaces: bool = False,
        include_dap_properties: bool = False,
        use_dap_activity_name: bool = False,
    ) -> str:
        _dict = self.to_dict(
            include_packages=include_packages,
            include_variables=include_variables,
            include_arguments=include_arguments,
            keep_original_params_order=keep_original_params_order,
            include_complex_properties=include_complex_properties,
            include_ids=include_ids,
            include_name=include_name,
            include_namespaces=include_namespaces,
            include_dap_properties=include_dap_properties,
            use_dap_activity_name=use_dap_activity_name,
        )
        return yaml_dump(_dict).strip()

    def clone(self) -> "Workflow":
        return copy.deepcopy(self)

    def __eq__(self, value: object) -> bool:
        if isinstance(value, dict):
            return self.to_dict() == value
        elif isinstance(value, Workflow):
            return self.to_dict() == value.to_dict()
        return False

    def append_sequence_insertion_locus(self):
        self.activities.append(
            Activity(
                {
                    "thought": SEQUENCE_GENERATION_INSERTION_PHRASE,
                    "activity": "System.Activities.Statements.Sequence",
                    "currentActivity": True,
                    "params": {},
                },
                self.arguments,
                self.variables,
                None,
            )
        )
