from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils import errors


def validate_workflow_generation_prompt(user_prompt: str):
    # min length
    length_requirement = len(user_prompt) >= 5

    # contains at least one letter
    letters_requirement = bool(any(letter.isalpha() for letter in user_prompt))

    if not length_requirement or not letters_requirement:
        raise errors.UnprocessableEntityError("User request is too short or does not contain any letters.")


def append_debug_runid_to_prompt(prompt: str, runId: str) -> str:
    if settings.DEBUG_MODE:
        return prompt + f"\n\n\nIgnore this please: #RUNID#{runId}"
    return prompt
