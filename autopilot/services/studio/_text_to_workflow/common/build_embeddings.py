import numpy as np

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.embeddingsdb import load_activity_definitions, load_embeddings_db
from services.studio._text_to_workflow.common.schema import (
    ActivityRetrieverState,
    ActivityType,
    EmbeddingsActivityFilterOptions,
    TargetFramework,
)
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel


def load_embeddings(framework: TargetFramework | None, type: ActivityType) -> list[dict]:
    db_path = paths.get_embeddings_path() / "embeddings.db"
    return load_embeddings_db(db_path, framework, type)


def is_unsupported_integration_service_activity(item: dict) -> bool:
    is_integration_service_activity = item["PackageName"].endswith(constants.INTEGRATION_SERVICE_PACKAGE_SUFFIX)
    if is_integration_service_activity:
        is_supported = item["ActivityFullName"] in constants.DAP_ACTIVITY_NAMES or item["PackageName"] in constants.LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST
        return not is_supported
    return False


def build(
    type: ActivityType,
    embedding_model: EmbeddingModel | None = None,
    framework: TargetFramework | None = None,
    show_progress_bar: bool = False,
    activity_filter_options: EmbeddingsActivityFilterOptions | None = None,
) -> ActivityRetrieverState:
    db_path = paths.get_embeddings_path() / "embeddings.db"
    activity_definitions = load_activity_definitions(db_path, framework, type, activity_filter_options)
    activities = list(activity_definitions.values())

    display_names, descriptions, categories = [], [], []
    fullactivityid2index, index2fullactivityid, fullclassname2index, index2fullclassname, classname2fullclassnames = {}, {}, {}, {}, {}
    for activity_definition in activities:
        display_names.append(activity_definition["displayName"])
        descriptions.append(activity_definition["description"])
        categories.append(activity_definition["category"])
        # create mappings
        full_activity_id = activity_definition["fullActivityId"]
        full_classname = activity_definition["fullClassName"]
        classname = activity_definition["className"]
        fullactivityid2index[full_activity_id] = activity_definition["index"]
        index2fullactivityid[activity_definition["index"]] = full_activity_id
        fullclassname2index[full_classname] = activity_definition["index"]
        index2fullclassname[activity_definition["index"]] = full_classname
        if classname not in classname2fullclassnames:
            classname2fullclassnames[classname] = []
        classname2fullclassnames[classname].append(full_classname)
    display_name_embeddings = np.empty(0)
    description_embeddings = np.empty(0)
    category_embeddings = np.empty(0)
    if embedding_model is not None:
        display_name_embeddings = embedding_model.encode_batch(display_names, 256, show_progress_bar=show_progress_bar)
        description_embeddings = embedding_model.encode_batch(descriptions, 256, show_progress_bar=show_progress_bar)
        category_embeddings = embedding_model.encode_batch(categories, 256, show_progress_bar=show_progress_bar)

    return {
        "activities": activities,
        "display_name_embeddings": display_name_embeddings,
        "description_embeddings": description_embeddings,
        "category_embeddings": category_embeddings,
        "fullactivityid2index": fullactivityid2index,
        "index2fullactivityid": index2fullactivityid,
        "fullclassname2index": fullclassname2index,
        "index2fullclassname": index2fullclassname,
        "classname2fullclassnames": classname2fullclassnames,
    }
