from functools import cache

from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@cache
def get_connections_data() -> tuple[str, list[Connection]]:
    setup_path = paths.get_dataset_connections_path()
    with open(setup_path, "r") as f:
        setup = yaml_load(f)
        tenant_id = setup["tenant_id"]
        connections = setup["connections"]

    return tenant_id, connections
