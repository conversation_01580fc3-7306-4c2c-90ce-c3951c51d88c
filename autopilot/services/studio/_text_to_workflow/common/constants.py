from services.studio._text_to_workflow.common.schema import ActivityType, ParamTypeCategory

SEQUENCE_GENERATION_INSERTION_PHRASE = "<sequence generation insert locus>"  # Beware when changing: has to be modified in the prompt.yaml prompts as well
BASIC_FULL_ACTIVITY_IDS: dict[ActivityType, tuple[str, ...]] = {
    "trigger": (
        "UiPath.Core.Activities.ManualTrigger",
        "UiPath.Core.Activities.TimeTrigger",
    ),
    "activity": (
        "UiPath.Core.Activities.Assign",
        "UiPath.Core.Activities.LogMessage",
        "System.Activities.Statements.If",
        "UiPath.Core.Activities.ForEach",
        "UiPath.Core.Activities.InterruptibleWhile",
        "UiPath.UIAutomationNext.Activities.NApplicationCard",
        "UiPath.Core.Activities.AddDataRow",
        "UiPath.Core.Activities.AddDataColumn",
        "UiPath.Core.Activities.ReadTextFile",
        "UiPath.Core.Activities.PathExists",
        "UiPath.Core.Activities.ForEachRow",
        "System.Activities.Statements.TryCatch",
        "UiPath.Activities.System.Text.ExtractText",
    ),
}
TESTING_ACTIVITIES_VERIFY_EXPRESSION = "UiPath.Testing.Activities.VerifyExpression"
DAP_NAMESPACE = "UiPath.IntegrationService.Activities.Runtime.Activities"
DAP_ACTIVITY_NAME = DAP_NAMESPACE + ".ConnectorActivity"
DAP_TRIGGER_NAME = DAP_NAMESPACE + ".ConnectorTriggerActivity"
DAP_HTTP_ACTIVITY_NAME = DAP_NAMESPACE + ".ConnectorHttpActivity"
DAP_ACTIVITY_NAMES: set[str] = {DAP_ACTIVITY_NAME, DAP_TRIGGER_NAME}
INTEGRATION_SERVICE_PACKAGE_SUFFIX = "IntegrationService.Activities"
LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST: set[str] = {"UiPath.Jira.IntegrationService.Activities"}
GENERIC_ACTIVITY_NAME_SUFFIX = "`1"
API_ACTIVITY_NAMES: set[str] = {DAP_HTTP_ACTIVITY_NAME, DAP_ACTIVITY_NAME}
ACTIVITIES_EXCLUDED: set[str] = {
    "OpenAIGenerate_Text_Completion",
    "Microsoft_Azure_OpenAIGenerate_Text_Completion",
    # Google Chat Completion not released yet
    # "Google_VertexGenerate_Text_Completion_using_Gemini",
    # "Google_VertexGenerate_Text_Completion",
}

DAP_HTTP_METHOD_PARAM_NAME = "method"

ACTIVITY_TYPEDEF_NAME_ALIASES = {"UiPath.Core.Activities.IfElseIfV2.ElseIfClause": "System.Activities.Statements.If"}
ACTIVITY_META_PARAMS = ("UiPathActivityTypeId", "__type")

UIA_PACKAGE_NAME = "UiPath.UIAutomation.Activities"
UIA_APPLICATION_SCOPE_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NApplicationCard"
UIA_APPLICATION_SCOPE_MONKEY_PATCH = {
    "typeDefinition": "class NApplicationCard: Activity \r\n{\r\nGenerationOptions AutoGenerationOptions;\r\n}",
    "additionalTypeDefinitions": "class GenerationOptions \r\n{\r\nstring Url;\r\nstring Prompt;\r\nList<string> ExpectedInputs;\r\nList<string> ExpectedOutputs;\r\n}",  # noqa
}

ACTIVITY_THOUGHT_PLACEHOLDER = "<reasoning>"  # Beware when changing: has to be modified in the prompt.yaml prompts as well
ACTIVITY_CONFIGURABLE_PARAMETER_TYPES: set[ParamTypeCategory] = {
    "InArgument",
    "OutArgument",
    "InOutArgument",
    "ListInArgument",
    "DictionaryInArgument",
    "ListOutArgument",
    "DictionaryOutArgument",
}

# Ignore "GSuite Workspace - Classic" and "Office 365 - Classic" activities because they are deprecated
PRODUCTIVITY_IGNORED_ACTIVITIES: set[str] = {
    "UiPath.MicrosoftOffice365.Activities.Calendar.AddAttachment",
    "UiPath.MicrosoftOffice365.Activities.Calendar.AddAttendee",
    "UiPath.MicrosoftOffice365.Activities.Calendar.AddLocation",
    "UiPath.MicrosoftOffice365.Activities.Calendar.CreateEvent",
    "UiPath.MicrosoftOffice365.Activities.Calendar.DeleteEvent",
    "UiPath.MicrosoftOffice365.Activities.Calendar.FindMeetingTimes",
    "UiPath.MicrosoftOffice365.Activities.Calendar.GetCalendars",
    "UiPath.MicrosoftOffice365.Activities.Calendar.ModifyEvent",
    "UiPath.MicrosoftOffice365.Activities.Calendar.Rsvp",
    "UiPath.MicrosoftOffice365.Activities.Calendar.SearchEvents",
    "UiPath.MicrosoftOffice365.Activities.Excel.AddSheet",
    "UiPath.MicrosoftOffice365.Activities.Excel.AppendRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.ClearRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.CopyRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.CopySheet",
    "UiPath.MicrosoftOffice365.Activities.Excel.CreateWorkbook",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteSheet",
    "UiPath.MicrosoftOffice365.Activities.Excel.GetCellColor",
    "UiPath.MicrosoftOffice365.Activities.Excel.GetSheets",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadCell",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadColumn",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadRow",
    "UiPath.MicrosoftOffice365.Activities.Excel.RenameSheet",
    "UiPath.MicrosoftOffice365.Activities.Excel.SetRangeColor",
    "UiPath.MicrosoftOffice365.Activities.Excel.VLookupRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteCell",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.CreateTable",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteColumn",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteRows",
    "UiPath.MicrosoftOffice365.Activities.Excel.GetTableRange",
    "UiPath.MicrosoftOffice365.Activities.Excel.InsertColumn",
    "UiPath.MicrosoftOffice365.Activities.Excel.InsertRows",
    "UiPath.MicrosoftOffice365.Activities.Files.CopyItem",
    "UiPath.MicrosoftOffice365.Activities.Files.CreateFolder",
    "UiPath.MicrosoftOffice365.Activities.Files.DeleteItem",
    "UiPath.MicrosoftOffice365.Activities.Files.DownloadFile",
    "UiPath.MicrosoftOffice365.Activities.Files.ExportAsPdf",
    "UiPath.MicrosoftOffice365.Activities.Files.ForEachFileFolder",
    "UiPath.MicrosoftOffice365.Activities.Files.GetItem",
    "UiPath.MicrosoftOffice365.Activities.Files.MoveItem",
    "UiPath.MicrosoftOffice365.Activities.Files.ShareItem",
    "UiPath.MicrosoftOffice365.Activities.Files.UploadFile",
    "UiPath.MicrosoftOffice365.Activities.Groups.CreateGroup",
    "UiPath.MicrosoftOffice365.Activities.Groups.DeleteGroup",
    "UiPath.MicrosoftOffice365.Activities.Groups.GetGroup",
    "UiPath.MicrosoftOffice365.Activities.ListGroups",
    "UiPath.MicrosoftOffice365.Activities.Mail.DeleteMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.ForwardMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.GetMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.MoveMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.ReplyToMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.SendMail",
    "UiPath.MicrosoftOffice365.Activities.Mail.SetMailCategories",
    "UiPath.MicrosoftOffice365.Activities.Planner.Buckets.CreateBucket",
    "UiPath.MicrosoftOffice365.Activities.Planner.Buckets.DeleteBucket",
    "UiPath.MicrosoftOffice365.Activities.Planner.Buckets.ListBucketTasks",
    "UiPath.MicrosoftOffice365.Activities.Planner.Buckets.ListBuckets",
    "UiPath.MicrosoftOffice365.Activities.Planner.Plans.CreatePlan",
    "UiPath.MicrosoftOffice365.Activities.Planner.Plans.GetPlan",
    "UiPath.MicrosoftOffice365.Activities.Planner.Plans.ListPlans",
    "UiPath.MicrosoftOffice365.Activities.Planner.Tasks.CreateTask",
    "UiPath.MicrosoftOffice365.Activities.Planner.Tasks.DeleteTask",
    "UiPath.MicrosoftOffice365.Activities.Planner.Tasks.GetTask",
    "UiPath.MicrosoftOffice365.Activities.Planner.Tasks.ListTasks",
    "UiPath.MicrosoftOffice365.Activities.Planner.Tasks.UpdateTask",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.AddListItems",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.DeleteListItem",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.ForEachList",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.ForEachListItem",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.GetListInfo",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.GetListItems",
    "UiPath.MicrosoftOffice365.Activities.SharePoint.UpdateListItem",
    "UiPath.GSuite.Activities.CreateScriptDeployment",
    "UiPath.GSuite.Activities.CreateScriptProject",
    "UiPath.GSuite.Activities.GetProjectContent",
    "UiPath.GSuite.Activities.RunScript",
    "UiPath.GSuite.Activities.UploadScriptFile",
    "UiPath.GSuite.Activities.AddAttendee",
    "UiPath.GSuite.Activities.CreateEvent",
    "UiPath.GSuite.Activities.DeleteEvent",
    "UiPath.GSuite.Activities.ModifyEvent",
    "UiPath.GSuite.Activities.SearchEvents",
    "UiPath.GSuite.Activities.BatchUpdateDocumentScope",
    "UiPath.GSuite.Activities.GetDocument",
    "UiPath.GSuite.Activities.GetTextIndex",
    "UiPath.GSuite.Activities.InsertText",
    "UiPath.GSuite.Activities.ReadAllText",
    "UiPath.GSuite.Activities.ReplaceText",
    "UiPath.GSuite.Activities.CopyDriveFile",
    "UiPath.GSuite.Activities.CreateDocument",
    "UiPath.GSuite.Activities.CreateFolder",
    "UiPath.GSuite.Activities.CreateNewSpreadsheet",
    "UiPath.GSuite.Activities.DeleteFile",
    "UiPath.GSuite.Activities.DeleteFilePermission",
    "UiPath.GSuite.Activities.DownloadFile",
    "UiPath.GSuite.Activities.FindFiles",
    "UiPath.GSuite.Activities.GetFileInfo",
    "UiPath.GSuite.Activities.GetFilePermissions",
    "UiPath.GSuite.Activities.MoveDriveFile",
    "UiPath.GSuite.Activities.UpdateFilePermission",
    "UiPath.GSuite.Activities.UploadFile",
    "UiPath.GSuite.Activities.CreateFilePermission",
    "UiPath.GSuite.Activities.ChangeLabels",
    "UiPath.GSuite.Activities.GetMailMessages",
    "UiPath.GSuite.Activities.SendEmail",
    "UiPath.GSuite.Activities.AddDeleteColumns",
    "UiPath.GSuite.Activities.AddDeleteRows",
    "UiPath.GSuite.Activities.AddNewSheet",
    "UiPath.GSuite.Activities.AppendRow",
    "UiPath.GSuite.Activities.AutoFillRange",
    "UiPath.GSuite.Activities.BatchUpdateValuesScope",
    "UiPath.GSuite.Activities.ClearRange",
    "UiPath.GSuite.Activities.CopyPasteRange",
    "UiPath.GSuite.Activities.CopySheet",
    "UiPath.GSuite.Activities.DeleteRange",
    "UiPath.GSuite.Activities.DeleteSheet",
    "UiPath.GSuite.Activities.DownloadSpreadsheet",
    "UiPath.GSuite.Activities.GetCellColor",
    "UiPath.GSuite.Activities.GetSheets",
    "UiPath.GSuite.Activities.ReadCell",
    "UiPath.GSuite.Activities.ReadColumn",
    "UiPath.GSuite.Activities.ReadRange",
    "UiPath.GSuite.Activities.ReadRow",
    "UiPath.GSuite.Activities.RenameSheet",
    "UiPath.GSuite.Activities.WriteCell",
    "UiPath.GSuite.Activities.WriteRange",
}


PRODUCTIVITY_ACTIVITY2CONNECTION: dict[str, str] = {
    # Google Docs
    "UiPath.GSuite.Activities.Docs.GoogleDocsApplicationScope": "uipath-google-docs",
    # Google Drive
    "UiPath.GSuite.Activities.ApplyFileLabelsConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.CopyFileConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.CreateFolderConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.CreateSpreadsheetConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.DeleteFileOrFolderConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.DownloadFileConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.Drive.GoogleDriveApplicationScope": "uipath-google-drive",
    "UiPath.GSuite.Activities.Drive.Triggers.FileUpdated": "uipath-google-drive",
    "UiPath.GSuite.Activities.Drive.Triggers.NewFileCreated": "uipath-google-drive",
    "UiPath.GSuite.Activities.Drive.Triggers.NewFolderCreated": "uipath-google-drive",
    "UiPath.GSuite.Activities.ForEachFileFolderConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.GetFileFolderConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.GetFileListConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.MoveFileConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.RemoveFileLabelsConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.ShareFileFolderConnections": "uipath-google-drive",
    "UiPath.GSuite.Activities.UploadFilesConnections": "uipath-google-drive",
    # Google Gmail
    "UiPath.GSuite.Activities.ArchiveEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Calendar.Triggers.EventReplied": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Calendar.Triggers.EventUpdated": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Calendar.Triggers.NewEventCreated": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Calendar.Triggers.NewEventInvitationReceived": "uipath-google-gmail",
    "UiPath.GSuite.Activities.CreateEventConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.DeleteEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.DeleteEventConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.DownloadAttachmentsConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.DownloadEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ForEachEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ForEachEventConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ForwardEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ForwardEventConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.GetEmailByIdConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.GetEmailListConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.GetEventListConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.GetNewestEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Gmail.Triggers.EmailSent": "uipath-google-gmail",
    "UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived": "uipath-google-gmail",
    "UiPath.GSuite.Activities.MarkAsReadUnreadConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ModifyEventConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.MoveEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.ReplyToEmailConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.RsvpConnections": "uipath-google-gmail",
    "UiPath.GSuite.Activities.SendEmailConnections": "uipath-google-gmail",
    # Google Sheets
    "UiPath.GSuite.Activities.AddSheetConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.DeleteRangeConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.DeleteSheetConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.ForEachRowConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.ForEachSheetConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.ReadCellConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.ReadRangeConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.RenameSheetConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.Sheets.GoogleSheetsApplicationScope": "uipath-google-sheets",
    "UiPath.GSuite.Activities.Sheets.Triggers.SheetCellUpdated": "uipath-google-sheets",
    "UiPath.GSuite.Activities.Sheets.Triggers.SheetCreated": "uipath-google-sheets",
    "UiPath.GSuite.Activities.WriteCellConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.WriteColumnConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.WriteRangeConnections": "uipath-google-sheets",
    "UiPath.GSuite.Activities.WriteRowConnections": "uipath-google-sheets",
    # Microsoft OneDrive
    "UiPath.MicrosoftOffice365.Activities.Excel.AddSheetConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.CreateWorkbookConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteRangeConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.DeleteSheetConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.ForEachRowConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.ForEachSheetConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadCellConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.RenameSheetConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.Triggers.RowAddedToTableBottom": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.Triggers.WorksheetCellUpdated": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.Triggers.WorksheetCreated": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteCellConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteColumnConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteRangeConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Excel.WriteRowConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.CopyItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.CreateFolderConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.DeleteItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.DownloadFileConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.ForEachFileFolderConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.GetFileFolderConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.MoveItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.ShareItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.Triggers.FileUpdated": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.Triggers.NewFileCreated": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.UploadFilesConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Files.UseDriveCard": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.AddListItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.DeleteSharepointListItemsConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.GetListItemsConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.GetSingleSharepointListItemConnections": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.Triggers.SharepointListItemAdded": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.Triggers.SharepointListItemUpdated": "uipath-microsoft-onedrive",
    "UiPath.MicrosoftOffice365.Activities.Sharepoint.UpdateListItemConnections": "uipath-microsoft-onedrive",
    # Microsoft Outlook365
    "UiPath.MicrosoftOffice365.Activities.Calendar.CreateEventConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.DeleteEventConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.ForEachEventConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.ForwardEventConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.GetEventListConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.ModifyEventConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.RsvpConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.Triggers.EventReplied": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.Triggers.EventUpdated": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.Triggers.NewEventCreated": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Calendar.Triggers.NewEventInvitationReceived": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.ArchiveEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.DeleteEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailAttachments": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.ForwardEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.GetEmailByIdConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.GetEmailListConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.GetNewestEmail": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.MarkAsReadUnreadConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.MoveEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.SetEmailCategoriesConnections": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.Triggers.EmailSent": "uipath-microsoft-outlook365",
    "UiPath.MicrosoftOffice365.Activities.Mail.Triggers.NewEmailReceived": "uipath-microsoft-outlook365",
}

DELEGATE_HANDLER_KEY = "Handler"
DELEGATE_VARIABLES_KEY = "variables"
ACTIVITY_ACTION_KEY = "Action"
TYPE_HINT_KEY = "__type"
SEQUENCE_ACTIVITY_NAME = "System.Activities.Statements.Sequence"

# Note: temporary workaround until we find a better way to obtain the correct package name and version
FORCED_IS_PACKAGE_NAME = "UiPath.IntegrationService.Activities"
FORCED_IS_PACKAGE_VERSION = "1.15.0-alpha.20250409.3"
