import re

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.activity_utils import remove_fields
from services.studio._text_to_workflow.common.embeddingsdb import create_dynamic_activity_full_id
from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_load

else_pattern_re = re.compile(r"^\s*(\d+\.)+\s+Else\s*$")
case_pattern_re = re.compile(r"^\s*(\d+\.)+\s+Case\s*")
default_pattern_re = re.compile(r"^\s*(\d+\.)+\s+Default\s*$")


def is_else_step(step: str) -> bool:
    return else_pattern_re.match(step) is not None


def is_case_step(step: str) -> bool:
    return case_pattern_re.match(step) is not None


def is_default_step(step: str) -> bool:
    return default_pattern_re.match(step) is not None


def is_step_without_activity(step: str) -> bool:
    return is_else_step(step) or is_case_step(step) or is_default_step(step)


def find_activities(workflow):
    subs = []
    if isinstance(workflow, dict):
        subs = [find_activities(v) for k, v in workflow.items() if k != "trigger"]
        subs = [item for row in subs for item in row]
        if "activity" in workflow:
            activity, _ = extract_generic_pattern(workflow["activity"])
            if activity in constants.DAP_ACTIVITY_NAMES:
                activity_type_id = None
                workflow["params"] = workflow.get("params", {})
                if "UiPathActivityTypeId" in workflow["params"]:
                    activity_type_id = workflow["params"]["UiPathActivityTypeId"]
                elif "uiPathActivityTypeId" in workflow["params"]:
                    activity_type_id = workflow["params"]["uiPathActivityTypeId"]
                elif "uiPathActivityTypeId" in workflow:
                    activity_type_id = workflow["uiPathActivityTypeId"]
                else:
                    raise ValueError(f"UiPathActivityTypeId not found in {workflow}")
                activity = create_dynamic_activity_full_id(activity, activity_type_id)
            subs = [activity] + subs
    if isinstance(workflow, list):
        subs = [find_activities(v) for v in workflow]
        subs = [item for row in subs for item in row]
    return subs


def find_trigger(workflow):
    if isinstance(workflow, dict):
        if "trigger" in workflow:
            return find_activities(workflow["trigger"])[0]
    if isinstance(workflow, list):
        for item in workflow:
            result = find_trigger(item)
            if result != "":
                return result
    return ""


def update_counters(counters: list[int], level: int) -> list[int]:
    if len(counters) == level:
        counters.append(1)
        return counters

    if len(counters) > level:
        while len(counters) > level + 1:
            counters.pop()
        counters[-1] += 1
    return counters


def get_counter_str(counters: list[int]) -> str:
    return ".".join([repr(c) for c in counters])


def get_indentation(level: int) -> str:
    return "".join(["  "] * level)


def find_thoughts(workflow, accumulator, level=0, counters=None):
    if counters is None:
        counters = [0]
    if isinstance(workflow, dict):
        if "Cases" in workflow:
            for case in workflow["Cases"]:
                counters = update_counters(counters, level)
                accumulator.append(f"{get_indentation(level)}{get_counter_str(counters)}. Case {case}")
                find_thoughts(workflow["Cases"][case], accumulator, level + 1, counters)
            if "Default" in workflow:
                counters = update_counters(counters, level)
                accumulator.append(f"{get_indentation(level)}{get_counter_str(counters)}. Default")
                find_thoughts(workflow["Default"], accumulator, level + 1, counters)
        elif "Then" in workflow and "Else" in workflow:
            find_thoughts(workflow["Then"], accumulator, level, counters)
            if "ElseIfs" in workflow:
                find_thoughts(workflow["ElseIfs"], accumulator, level - 1, counters)

            counters = update_counters(counters, level - 1)
            accumulator.append(f"{get_indentation(level - 1)}{get_counter_str(counters)}. Else")
            find_thoughts(workflow["Else"], accumulator, level, counters)
        else:
            if "thought" in workflow:
                thought = workflow["thought"]
                counters = update_counters(counters, level)
                accumulator.append(f"{get_indentation(level)}{get_counter_str(counters)}. {thought.strip()}")
            for v in workflow.values():
                new_level = level + 1 if "activity" in workflow else level
                find_thoughts(v, accumulator, new_level, counters)
    if isinstance(workflow, list):
        for v in workflow:
            find_thoughts(v, accumulator, level, counters)
    return accumulator


def remove_field_with_value(workflow, field, value):
    if isinstance(workflow, dict):
        if field in workflow and workflow[field] == value:
            del workflow[field]
        for v in workflow.values():
            remove_field_with_value(v, field, value)
    if isinstance(workflow, list):
        for v in workflow:
            remove_field_with_value(v, field, value)
    return workflow


def check_field_exists(workflow, field):
    if isinstance(workflow, dict):
        if field in workflow:
            return True
        for v in workflow.values():
            if check_field_exists(v, field):
                return True
    if isinstance(workflow, list):
        for v in workflow:
            if check_field_exists(v, field):
                return True
    return False


def get_field_parents(workflow, field, parent_name="activity", accumulator=[], parents=[]):  # noqa: B006
    if isinstance(workflow, dict):
        if parent_name in workflow:
            parents.append(workflow[parent_name])
        if field in workflow:
            if len(parents) == 0:
                accumulator.append("")
            else:
                accumulator.append(parents[-1])
        for v in workflow.values():
            get_field_parents(v, field, parent_name, accumulator, parents)
    if isinstance(workflow, list):
        for v in workflow:
            get_field_parents(v, field, parent_name, accumulator, parents)


def has_old_business_entity(workflow):
    field = "BusinessEntity"
    if not check_field_exists(workflow, field):
        return False
    activities = []
    get_field_parents(workflow, field, "activity", accumulator=activities)
    return not all([a.startswith("UiPath.Jira.IntegrationService.Activities") for a in activities])


def add_manual_trigger(workflow):
    if "trigger" in workflow:
        return workflow
    result = {}
    before_trigger = ["processName", "packages"]
    result.update({k: workflow[k] for k in before_trigger if k in workflow})
    result.update({"trigger": {"thought": "Manual Trigger", "activity": "UiPath.Core.Activities.ManualTrigger"}})
    result.update(workflow)
    return result


def build_plan(workflow: dict) -> str:
    thoughts = find_thoughts(workflow, [])
    plan = "\n".join(thoughts).replace("'", "''").replace('"', '""')
    return plan


def build_plan_categories(plan: str, all_activities: list[str], activity_definitions: dict) -> str:
    steps = plan.split("\n")
    plan_category_steps = []
    activity_idx = 0
    for step in steps:
        if is_step_without_activity(step):
            plan_category_steps.append(step)
            continue
        activity = all_activities[activity_idx]
        definition = activity_definitions.get(activity, {})
        category = definition.get("mapped_category", None)
        if (
            category is not None
            and category not in ["", "None", "UiPathOrchestrator", "Workflow"]
            and not category.startswith("System")
            and not category.startswith("Any application")
            and not category.startswith("None")
        ):
            plan_category_steps.append(f"{step} - using {category}")
        else:
            plan_category_steps.append(step)
        activity_idx += 1
    assert activity_idx == len(all_activities)
    return "\n".join(plan_category_steps)


def build_yaml_object(workflow_result: dict, project: dict) -> dict:
    if "result" not in workflow_result:
        if "Model" in workflow_result:
            workflow_result["result"] = workflow_result["Model"]
    workflow_yaml_str = workflow_result["result"]
    workflow = yaml_load(workflow_yaml_str)

    name = project["name"]
    description = project.get("description", "").strip()

    trigger = find_trigger(workflow)
    if not trigger:
        workflow = add_manual_trigger(workflow)
        trigger = "UiPath.Core.Activities.ManualTrigger"
    activities = find_activities(workflow)
    thoughts = find_thoughts(workflow, [])
    plan = "\n".join(thoughts).replace("'", "''").replace('"', '""')
    workflow["processName"] = name.rsplit("]", maxsplit=1)[-1].strip()

    remove_fields(
        workflow,
        [
            "ApiKey",
            "AllColumns",  # this is removed since this field is huge and not useful
            "AllFields",
            "AllLabels",
            "BrowserCalendarGroupId",
            "BrowserCalendarId",
            "BrowserDriveId",
            "BrowserFolderId",
            "BrowserId",
            "BrowserItemId",
            "BrowserLocation",
            "BrowserSiteUrl",
            "BrowserSpecificUrl",
            "Configuration",
            "configuration",
            "ChartHeight",
            "ChartWidth",
            "Guid",
            "Id",
            "id",
            "isDynamic",
            "isConfigured",
            "displayName",
            "dynamicActivityDetails",
            "ReferenceId",
            "spaceKey",
            "TableInfo",  # this is removed since this field is huge and we can't predict it
        ],
    )
    remove_field_with_value(workflow, "thought", "Then")
    remove_field_with_value(workflow, "thought", "Body")
    remove_field_with_value(workflow, "defaultValue", "[[]]")
    remove_field_with_value(workflow, "Account", "Please select an account")
    retrieved_triggers = [trigger]
    retrieved_activities = []
    for activity in activities:
        if activity not in retrieved_activities:
            retrieved_activities.append(activity)

    return {
        "target_framework": project["targetFramework"],
        "name": name,
        "description": description,
        "conversion_success": workflow_result["Success"],
        "conversion_errors": multiline_str("\n".join(workflow_result["Errors"])),
        "plan": multiline_str(plan),
        "plan_categories": None,  # adding this to maintain order, it will get properly populated later
        "retrieved_triggers": retrieved_triggers,
        "retrieved_activities": retrieved_activities,
        "activities": activities,
        "process": workflow,
    }
