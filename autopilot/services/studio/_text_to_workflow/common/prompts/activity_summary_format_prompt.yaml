format_prompt: | 
    Your task is to output a valid YAML file containing the following properties:
    ```yaml
    activityDescriptions:
      - id: "<activity-id>"
        description: "<activity-description>"
    activitySummaries:
      - id: "<activity-id>"
        summary: "<activity-summary>"
    workflowSummary: "<workflow-summary>"
    workflowFileName: "<workflow-file-name>"
    workflowShortDescription: "<workflow-short-description>"
    ```
    where:
    <activity-id>: The id of the activity.
    <activity-description>: A brief description of the action performed by the activity of the specific id. Cannot contain new lines. {activity-description}
    <activity-summary>: A brief summary of the action(s) performed by the activity tree of the activity, including itself. Cannot contain new lines. {activity-summary}
    <workflow-summary>: A brief summary of the action(s) performed by the entire workflow. Cannot contain new lines. Try to use at most 12 words.
    <workflow-file-name>: A short PascalCase (but with words also separated by _, e.g. My_Workflow instead of MyWorkflow) name for the workflow file. Cannot contain new lines. MUST not have generic names like "Untitled" or "Untitled Workflow". Must be a representative name of the workflow.
    <workflow-short-description>: A brief short description of the workflow. Cannot contain new lines. Try to use at most 12 words.
    All fields must be generated and properly escaped.
    For Sequence activities, create a full summary of the whole activity tree of that sequence.
    You MUST follow the full structure of the YAML file, including the "id", "description" and "summary" fields.

    For example, the following generated YAML is **INCORRECT**:
    ```
    activityDescriptions:
      - id: "Sequence_1"
        description: "Get and update time off request status."
    activitySummaries:
      - id: "Sequence_1"
        description: "Get and update time off request status."
    workflowSummary: "Get and update time off request status."
    workflowFileName: "TimeOffRequestStatus"
    workflowShortDescription: "Get and update time off request status."
    ```
    Because the activitySummaries key does not contain 'id' and 'summary' as subfields.
    
    Requirements:
    - Make sure to output a description and a summary for each `activity` with an `id` in the given workflow.
    - Descriptions cannot contain new lines '\n'
    - Avoid generic and vague formulation endings like "... and perform specific actions".
    - The descriptions and summaries should be concise, specific and imperative statements. For control flow activities, use "If" or "For each" to start.
    - For triggers, start with a formulation like "When ..." or include the trigger like "Manual Trigger"
    - Refrain from using expressions if you are not aware of the type definitions. e.g. instead of "emailMessage.From.ToString()" use "email sender"
    - Important: Do NOT use the `thought` as a basis for the <activity-description> as it may not be meaningful, try to understand the Activity tree and come up with a better thought.
    - Important: Generate a valid JSON and properly format all fields.