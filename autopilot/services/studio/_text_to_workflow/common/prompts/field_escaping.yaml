field_escaping_prompt: |
  In case the field of a variable in an expression contains invalid characters, you MUST use the '[' and ']' operators to access the field, otherwise the expression will not be properly parsed.
  Invalid characters include:
  - ' ' (space)
  - '.' (dot)
  - '-' (dash)
  - '[' (left bracket)
  - ']' (right bracket)
  - '(' (left parenthesis)
  - ')' (right parenthesis)
  - '!' (exclamation mark)
  - '?' (question mark)
  - ':' (colon)
  - ';' (semicolon)
  - '=' (equals)
  - '|' (pipe)
  - '$' (dollar sign)
  - '`' (backtick)
  - '@' (at sign)
  - '&' (ampersand)
  - '*' (asterisk)
  - '/' (slash)
  - '\' (backslash)
  - '"' (double quote)
  - "'" (single quote)
  - ' ' (space)
  - '\t' (tab)
  - '\n' (newline)
  - digits (0-9) AT THE BEGINNING OF A FIELD NAME

  For example:
  **INCORRECT:**
  ```
  return user.first-name
  ```
  **CORRECT:**
  ```
  return user["first-name"]
  ```
  **INCORRECT:**
  ```
  return user.a*b
  ```
  **CORRECT:**
  ```
  return user["a*b"]
  ```