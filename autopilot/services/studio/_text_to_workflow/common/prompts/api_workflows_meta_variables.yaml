add_meta_variables: |
  In addition to that list, a series of fixed context variables will be used.
  [
    {{"name": "$workflow", "type": "{{input : System.object}}"}},
    {{"name": "$context", "type": "{{context : System.object}}"}},
    {{"name": "$input", "type": "{{input : System.object}}"}}
  ]
  The input data received by the workflow is present in the "$workflow.input" variable.
  The output of the previous activity is present in the "$input" variable.
  The intermediary results of previous activities are present as keys in the "$context.outputs" variable.
  In case the $context object is not empty, all of its member fields will be present as individual variables.
  The provided information about the structure of the $workflow, $context and $input variables is:
  {meta_variables_schema}
  You MUST use full path for variables. For examples:
  **INCORRECT:**
  output.a
  **CORRECT:**
  $context.output.a