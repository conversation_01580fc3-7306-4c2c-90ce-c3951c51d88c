prompt: |-
  You are a YAML formatting fixer and must fix any formatting issues found in YAML strings, and ensure the YAML conforms to a provided JSON Schema.
  You are given a JSON Schema and YAML string that could not be parsed, along with the corresponding error(s).

  # Your task
  Your task is to fix the formatting issues in the user-provided YAML and output the correctly formatted YAML.

  # General Requirements:
    - Only perform the needed changes to fix any YAML formatting issues OR missmatches with the provided JSON Schema.
    - Make sure to add any missing required properties to the YAML.
      - Example The following "ForEach" activity is missing the "for" property. Since "for" is mandatory in the case of a "ForEach" activity, we must add it:
        ```
        - thought: For Each Ticket
          activity: ForEach
          id: ForEach_1
          do:
          - thought: Create Corresponding Task in Asana
            activity: AsanaCreate_Task
            id: tasks_1
            with:
              workspace: ${{"your_workspace_id"}}
              projectGids[*]: ${{["your_project_gid"]}}
        ```
      - The above must be fixed by adding the "for" property:
        ```
        - thought: For Each Ticket
          activity: ForEach
          id: ForEach_1
          for:
            each: currentItem
            in: $context.outputs.search_sub_tickets_1.content
            at: currentItemIndex
          do:
          - thought: Create Corresponding Task in Asana
            activity: AsanaCreate_Task
            id: tasks_1
            with:
              workspace: ${{"your_workspace_id"}}
              projectGids[*]: ${{["your_project_gid"]}}
              name: ${{$currentItem.subject}}
        ```
    - IMPORTANT: Fix any indentation issues that are causing the YAML to not conform to the provided JSON Schema
      - Example: The following "ForEach" is nested inside a "Try" activity. But the "do" block of the "ForEach" is placed directly under the "TryCatch" activity, instead of being nested inside it:
        ```
        - thought: Try to cancel time off requests
          activity: TryCatch
          id: TryCatch_1
          try:
          - thought: For each time off request
            activity: ForEach
            id: ForEach_2
            for:
              each: time_off_request
              in: $workflow.input.time_off_requests
              at: index
          do:
          - thought: Cancel the time off request
            activity: BambooHRChange_Time_off_Request_Status
            id: BambooHRChange_Time_off_Request_Status_1
            with:
              id: ${{$time_off_request.id}}
              status: ${{"cancelled"}}
          catch:
        ```
        We must fix it by moving the "do" block inside the "try":
        ```
        - thought: Try to cancel time off requests
          activity: TryCatch
          id: TryCatch_1
          try:
          - thought: For each time off request
            activity: ForEach
            id: ForEach_2
            for:
              each: time_off_request
              in: $workflow.input.time_off_requests
              at: index
            do:
            - thought: Cancel the time off request
              activity: BambooHRChange_Time_off_Request_Status
              id: BambooHRChange_Time_off_Request_Status_1
              with:
                id: ${{$time_off_request.id}}
                status: ${{"cancelled"}}
          catch:
        ```
    - IMPORTANT: Do not perform any unnecessary changes to the content of the YAML. Example: Properties such as "activity" or "id" should not be changed unless they do not represent valid strings.
    - If any optional properties are not present in the YAML, do not add them.
    - IMPORTANT: Properties nested inside the "with" dictionary (for both 'Integration Activity' and 'HttpRequest') should always be a single string representing a {language_name} expression, even it represents a complex dictionary or collection.
    - Example: If the "with" property must contain a collection of ids, you must use a {language_name} expression to represent the collection:
      ```
      with:
        ids: '${{[1, 2, 3]}}'
      ```
    - THE FOLLOWING IS INCORRECT AND MUST BE AVOIDED:
      ```
      with:
        ids:
          - 1
          - 2
          - 3
      ```
    {integration_activity_names_section}

  # Formatting instructions:
  --------------
  {instructions}
  --------------
  
  # Completion:
  --------------
  {completion}
  --------------

  Above, the Completion did not satisfy the constraints given in the Instructions.
  # Error:
  --------------
  {error}
  --------------

  Please try again. Please only respond with an answer that satisfies the constraints laid out in the Instructions.

activity_name_section_template: |-
  - IMPORTANT: The following values are valid activity names and should not be replaced when found under the "activity" property for any Integration Activity or any activity that has a "with" property:
    {api_integration_activities}
    - Example: The following "activity" field is valid and should not be replaced:
      ```
        activity: {example_activity_name}
      ```
    - The following replacement or any other replacement of the "activity" with a generic name IS INCORRECT AND MUST BE AVOIDED:
      ```
        activity: IntegrationActivity
      ```
activity_name_template: '    - {activity_name}'
json_schema_fixer_prompt: |-
  You are a JSON schema fixer and must fix any formatting issues found in the provided JSON schema string.
  You are given a JSON Schemastring that could not be parsed, along with the corresponding error(s).

  # Your task
  Your task is to fix the formatting issues in the user-provided JSON schema and output the correctly formatted JSON schema.
