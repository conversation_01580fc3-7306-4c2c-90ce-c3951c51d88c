from dataclasses import dataclass
from typing import Any, Union

from langchain_core.prompt_values import PromptValue
from pydantic import BaseModel, ConfigDict, Field

from services.studio._text_to_workflow.common.api_workflow.schema import (
    ApiWorkflow,
    BaseConnectorIntegration,
    BaseForEach,
    BaseIf,
    BaseSequence,
    BaseTryCatch,
    HttpRequest,
    JsInvoke,
    Response,
)
from services.studio._text_to_workflow.common.schema import ActivityDefinition
from services.studio._text_to_workflow.common.schema_utils import reorder_schema_properties
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import EditPatchStructureError, WorkflowProcessingError

# ======================================================================
# POST-GENERATION SCHEMA
# ======================================================================
# These activities are not generated directly by the LLM, but created in the post-processing step to aid SW in displaying the activity.
# In order to avoid poluting the JSON schema sent to the LLM, we define these activities in a separate hierarchy.

POST_GENERATION_ACTIVITIES = Union[
    Response, HttpRequest, JsInvoke, "PostGenSequence", "PostGenIf", "PostGenForEach", "ConnectorIntegrationWithMetadata", "PostGenTryCatch"
]


class ConnectorRequestParameters(BaseModel):
    """Parameters for a connector activity with a specific connection"""

    connectionId: str | None = None
    pathParameters: dict[str, str]
    queryParameters: dict[str, str]
    bodyParameters: dict[str, str]


class ConnectorIntegrationMetadata(BaseModel):
    """Metadata needed to display a connector activity in the SW canvas"""

    configuration: str | None = None
    method: str | None = None
    uiPathActivityTypeId: str | None = None


# Connector activity with metadata (DAP)
class ConnectorIntegrationWithMetadata(BaseConnectorIntegration):
    """Connector activity with specific DAP metadata.
    This is not generated directly by the LLM, but created in the post-processing step to aid SW in displaying the activity."""

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        json_schema_extra=reorder_schema_properties,
    )

    with_: ConnectorRequestParameters = Field(alias="with")
    metadata: ConnectorIntegrationMetadata


class PostGenSequence(BaseSequence):
    do: list[POST_GENERATION_ACTIVITIES]


class PostGenIf(BaseIf):
    then: list[POST_GENERATION_ACTIVITIES]
    else_: list[POST_GENERATION_ACTIVITIES] = Field(alias="else")


class PostGenForEach(BaseForEach):
    do: list[POST_GENERATION_ACTIVITIES]


class PostGenCatch(BaseModel):
    as_: str = Field(alias="as")
    do: list[POST_GENERATION_ACTIVITIES]


class PostGenTryCatch(BaseTryCatch):
    try_: list[POST_GENERATION_ACTIVITIES] = Field(alias="try")
    catch: PostGenCatch


class PostGenApiWorkflow(BaseModel):
    input: dict[str, Any] | None = None
    root: PostGenSequence


class ApiWorkflowPostProcessingDetails:
    """Results of the post-processing of a generated API workflow. Includes validation errors"""

    def __init__(
        self,
        raw_workflow: str,
        generated_workflow: ApiWorkflow | None,
        processed_workflow: PostGenApiWorkflow | None,  # the processed workflow with the metadata needed to display the activity in the SW canvas
        post_generation_errors: list[WorkflowProcessingError],  # any errors encountered while doing a post processing of the generated workflow
    ):
        self.raw_workflow = raw_workflow
        self.generated_workflow = generated_workflow
        self.post_generation_errors = post_generation_errors
        self.processed_workflow = processed_workflow


@dataclass
class ApiWorkflowDraftResult:
    """Results of the draft generation of an API workflow. Includes the raw workflow, the generated workflow and the post-processing errors"""

    api_workflow_details: ApiWorkflowPostProcessingDetails
    prompt_value: PromptValue
    token_usage: TokenUsage
    activity_defs: list[ActivityDefinition]
    raw_model_prediction: str
    edit_patch_structure_errors: list[EditPatchStructureError]
