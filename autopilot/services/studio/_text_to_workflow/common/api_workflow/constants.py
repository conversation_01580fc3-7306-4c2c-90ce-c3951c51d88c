import re

# Regex to find context output path references in expressions
ANY_OUTPUT_PATH_REGEX = re.compile(r"\$context(?:\??\.|\?\?)(?:outputs?)(?:\??\.|\?\?)([a-zA-Z0-9_]+)")  # matches $context.outputs.activity_id

# Properties that don't contain expressions and should be skipped during validation
STATIC_PROPERTIES = {
    "activity",
    "id",
    "thought",
    "type",  # Input argument type
    "do",  # List of activities
    "then",  # List of activities
    "else",  # List of activities
}
