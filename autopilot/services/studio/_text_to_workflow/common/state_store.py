import pickle
import shutil
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Optional

import yaml

from services.studio._text_to_workflow.utils import paths


def clear_retrievers_cache():
    retrievers_path = paths.get_retrievers_path()
    if retrievers_path.exists():
        shutil.rmtree(retrievers_path)


class StateBuilder(ABC):
    @abstractmethod
    def build(self) -> tuple[Any, dict]:
        """Build the state object (pickle) and the state info (yaml)."""
        pass


class StateStore:
    _instances = {}

    def __new__(cls, state_path: str | Path, builder: StateBuilder, lazy_load: bool = False):
        # Use the builder instance itself in the key for uniqueness
        instance_key = Path(state_path).resolve()
        if instance_key not in cls._instances:
            cls._instances[instance_key] = super().__new__(cls)
            cls._instances[instance_key]._initialized = False
        return cls._instances[instance_key]

    def __init__(self, state_path: str | Path, builder: StateBuilder, lazy_load: bool = False):
        if not self._initialized:
            normalized_state_path = Path(state_path).resolve()
            self.state_path = normalized_state_path
            self._state: Optional[Any] = None
            self._state_info: Optional[dict] = None
            self._builder: StateBuilder = builder
            self._load_failed: bool = False  # Flag to track load/build failures
            self._initialized = True

            if not lazy_load:
                self.load()

    def load(self) -> None:
        """Load state from pickle file."""
        if self._load_failed:
            # If a previous load/build attempt indicated failure, do not try again.
            # An exception will be raised if the user tries to access self.state
            return

        if self.state_path.exists():
            try:
                with open(self.state_path, "rb") as f:
                    self._state = pickle.load(f)
            except (pickle.UnpicklingError, EOFError) as e:
                print(f"Failed to load index from {self.state_path}: {e}")
                self._state = None
        else:
            self._state, self._state_info = self._builder.build()  # Call the builder's build method
            self.save()  # Save will also build if state is None, and handle _load_failed

        self._load_failed = self._state is None

    def save(self) -> None:
        """Save state to pickle file."""
        if self._state is None and not self._load_failed:
            # Attempt to build the state if it's None
            self._state, self._state_info = self._builder.build()  # Call the builder's build method
            self._load_failed = self._state is None

        self.state_path.parent.mkdir(parents=True, exist_ok=True)
        with open(self.state_path, "wb") as f:
            pickle.dump(self._state, f, protocol=pickle.HIGHEST_PROTOCOL)

        if self._state_info:
            with open(self.state_path.with_suffix(".yaml"), "w") as fout:
                yaml.dump(self._state_info, fout, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)

    def force_rebuild(self) -> None:
        """Force rebuild the state using the builder."""
        self._state, self._state_info = self._builder.build()  # Call the builder's build method
        self.save()  # Save will handle _load_failed based on build outcome
        self._load_failed = self._state is None

    def exists(self) -> bool:
        return self.state_path.exists()

    def is_loaded(self) -> bool:
        return self._state is not None

    @property
    def state(self) -> Any:
        """Get the state object, loading it if necessary (thread-safe)."""
        if self._state is None and not self._load_failed:
            self.load()
        return self._state
