import re
from typing import get_args

from services.studio._text_to_workflow.common import helpers
from services.studio._text_to_workflow.common.schema import ParamType<PERSON><PERSON><PERSON>y, ParamTypeDef, TypeDef, WorkflowConversionTypeDef

# NOTE: This is a workaround to avoid dependency of telemetry_utils in this module. in some cases we might not want to have this dependency.
try:
    from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

    _logger = AppInsightsLogger()
except ImportError:
    import logging

    _logger = logging.getLogger(__file__)

# Matches the type and name from the first line. e.g. `class MyType {` -> `class`, `MyType`
# The order matters: record struct should be before record and struct, to avoid eager matching.
_definition_pattern = re.compile(r"(class|interface|enum|record struct|struct|record) (\w+)")
# Matches the type parameters from the first line. e.g. `class MyType<T1, T2> {` -> `T1, T2`
_type_param_pattern = re.compile(r"<([^>]*)>")
# Matches the contraint on the type parameters. e.g. `where T : struct` -> `T`, `struct`
_type_param_constraint_pattern = re.compile(r"where\s+([^:]+)\s*:\s*([^,]+)")
# Matches accessor overload pattern in a type definition. e.g. `public string this[int index] { get; set; }` -> `public`, `string`, `[int index]`
_accessor_overload_pattern = re.compile(
    r"^(?:(static|readonly|const|volatile)\s+)?(?:(public|protected|internal|private)\s+)?([a-zA-Z_][\w\d<>?,.\s\[\]]*)\s+this\s*(\[.*\])$"
)
# Matches the parameter pattern in a type definition. e.g. `readonly public string MyProperty` -> `readonly`, `public`, `string`, `MyProperty`
_parameter_pattern = re.compile(
    r"^(?:(static|readonly|const|volatile)\s+)?(?:(public|protected|internal|private)\s+)?([a-zA-Z_][\w\d<>?,.\s\[\]]*)\s+([a-zA-Z_][\w\d\(\)]*)$"
)


def parse_typedef(typedef: str) -> TypeDef:
    """Parse a single C# type definition into a `TypeDef`."""
    definition, description = None, []
    for line in typedef.splitlines():
        if line.startswith("///"):
            description.append(line[3:].strip())
        elif line.startswith("//"):
            description.append(line[2:].strip())
        elif definition := _definition_pattern.search(typedef):
            break
    else:
        raise ValueError(f"Could not find class definition:\n{typedef}")
    description = "\n".join(description)
    class_type, class_name = (group.strip() for group in definition.groups())
    if class_type in ("class", "interface", "record", "struct", "record struct"):
        params, type_params = parse_classlike_typedef(typedef)
    elif class_type == "enum":
        params, type_params = parse_enum_typedef(typedef), {}
    else:
        raise ValueError(f"Unknown class type:{class_type}")
    return {
        "name": class_name,
        "type": class_type,
        "description": description,
        "type_params": type_params,
        "params": params,
        "text": typedef,
    }


def parse_typedefs(typedefs_str: str) -> dict[str, TypeDef]:
    """Parse multiple C# type definitions into a dictionary of `TypeDef`. The type definitions are expected to be separated by \r\n."""
    if not typedefs_str:
        return {}
    typedef_strs = typedefs_str.strip().split("}")
    typedefs, duplicates = {}, {}
    for typedef_str in typedef_strs:
        typedef_str = typedef_str.strip()
        if not typedef_str:
            continue
        typedef_str += "}"
        typedef = parse_typedef(typedef_str)

        if (
            typedef["name"] in typedefs and typedef["text"] != typedefs[typedef["name"]]["text"]
            # and typedef["type_params"] == typedefs[typedef["name"]]["type_params"]  # should we skip the duplicate if the interface has a type specialization?
        ):
            duplicates[typedef["name"]] = typedef["text"]
            # NOTE: A stupid heuristic to handle duplicate typedefs
            new_typedef_size = len(typedef["text"])
            old_typedef_size = len(typedefs[typedef["name"]]["text"])
            if new_typedef_size > old_typedef_size:
                typedefs[typedef["name"]] = typedef
        typedefs[typedef["name"]] = typedef
    if set(duplicates.keys()) - {
        "SelectorStrategy",  # this is an enum that has 2 definitions in embeddings.db for NExtractDataGeneric activity on windows only
        "IDocumentData",  # this is an interface that has an additional definition for a type specialization
    }:
        _logger.info(f"Duplicate typedefs found: {[k for k in duplicates.keys()]}")
    return typedefs


def parse_param_api_workflows(param_name: str, param_type: str, current_namespace_path: str = "") -> ParamTypeDef:
    if param_name.startswith("const "):
        param_name = param_name.strip("const ")
        param_type = "const " + param_type

    if current_namespace_path:
        param_name = f"{current_namespace_path}.{param_name}"

    return {
        "name": param_name,
        "type": param_type,
        "modifier": "public",
        "description": "",
        "required": "?" not in param_name,
        "category": "Property",
        "components": [],
    }


def parse_function_typedef(typedef_str: str, description: str) -> TypeDef:
    "Parse a single d.ts type definition of a function."
    working_str = typedef_str
    function_name = working_str.split("(")[0].strip()
    parameter_dict = {}

    if function_name.startswith("declare function"):
        function_name = function_name[len("declare function") :].strip()
    elif function_name.startswith("function"):
        function_name = function_name[len("function") :].strip()

    if "<" in function_name:
        template_params = function_name.split("<")[1].split(">")[0].strip()
        template_params = template_params.split(",")
        for template_param in template_params:
            template_param = template_param.strip()
            if template_param:
                template_param_name = template_param.split("=")[0].strip()
                template_param_type = template_param.split("=")[1].strip()
                parameter_dict[template_param_name] = parse_param_api_workflows(template_param_name, template_param_type)
        function_name = function_name.split("<")[0].strip()

    parameter_string = working_str.split("(")[1].split(")")[0].strip()
    parameters = parameter_string.split(",")

    for parameter in parameters:
        parameter = parameter.strip()
        if parameter:
            parameter_name = parameter.split(":")[0].strip()
            parameter_type = parameter.split(":")[1].strip()
            parameter_dict[parameter_name] = parse_param_api_workflows(parameter_name, parameter_type)

    return {
        "name": function_name,
        "type": "function",
        "description": description,
        "type_params": {"returnType": str(working_str.split(")")[1].strip().lstrip(":").strip())},
        "params": parameter_dict,
        "text": typedef_str,  # Use original typedef_str
    }


def parse_type_typedef(typedef_str: str, description: str) -> TypeDef:
    "Parse a single d.ts type definition of a type."
    typedef_str_body = typedef_str.strip("declare type").strip("type")
    type_name = typedef_str_body.split("=")[0].strip()
    type_choices = typedef_str_body.split("=")[1].strip()
    component_types = type_choices.split("|")
    params = {}
    for component_type in component_types:
        component_type = component_type.strip()
        if component_type:
            component_type_name = component_type.strip(";")
            params[component_type_name] = component_type_name

    return {"name": type_name, "type": "type", "description": description, "type_params": {}, "params": params, "text": typedef_str}


def parse_namespace_typedef_api_workflows(typedef_str: str, description: str) -> TypeDef:
    "Parse a single d.ts type definition of a namespace."
    namespace_name = typedef_str.split("namespace")[1].split("{")[0].strip().strip("$")
    namespace_content = typedef_str.split("{", 1)[1].rsplit("}", 1)[0]
    current_namespace_path = ""
    params = dict()
    for current_line in namespace_content.splitlines():
        current_line = current_line.strip()
        if "namespace" in current_line:
            if current_namespace_path != "":
                current_namespace_path += "."
            current_namespace_path += current_line.split("namespace")[1].strip().split()[0]
        elif "class" in current_line:
            if current_namespace_path != "":
                current_namespace_path += "."
            current_namespace_path += current_line.split("class")[1].strip().split()[0]
        elif "interface" in current_line:
            if current_namespace_path != "":
                current_namespace_path += "."
            current_namespace_path += current_line.split("interface")[1].strip().split()[0]
        elif current_line.strip() == "}":
            if "." in current_namespace_path:
                current_namespace_path = current_namespace_path.rsplit(".", 1)[0]
            else:
                current_namespace_path = ""

        if ":" in current_line:
            if "{" in current_line or "}" in current_line:
                if current_namespace_path != "":
                    current_namespace_path = current_namespace_path + "." + current_line.split(":")[0].strip().split()[-1]
                continue
            param_name = f"{current_line.split(':')[0].strip('')}"
            param_type = current_line.split(":")[1].rstrip(";")
            params[param_name] = parse_param_api_workflows(param_name.strip(), param_type.strip(), current_namespace_path)

    return {"name": namespace_name, "type": "namespace", "description": description, "type_params": {}, "params": params, "text": typedef_str}


def parse_typedef_api_workflows(typedef_str: str, messages: str) -> TypeDef:
    if not typedef_str:
        return {}
    "Parse a single d.ts type definition."
    # Add a newline after every '{' and before every '}'
    typedef_str = re.sub(r"({)", r"\1\n", typedef_str)
    typedef_str = re.sub(r"([^\n])(})", r"\1\n\2", typedef_str)
    if re.match(r"(?:declare\s+)?(?:function\s+)?\w+(?:<[^>]*>)?\s*\([^)]*\)\s*:\s*[^;]+;", typedef_str):
        return parse_function_typedef(typedef_str)
    elif re.match(r"(?:declare\s+)?enum\s+\w+\s*{[^}]*}", typedef_str):
        if typedef_str.startswith("declare enum"):
            typedef_str = typedef_str.strip("declare enum")
        return parse_enum_typedef_api_workflows(typedef_str)
    elif re.match(r"(?:declare\s+)?type\s+\w+\s*=\s*[^;]+;", typedef_str):
        return parse_type_typedef(typedef_str, messages)
    elif "namespace" in typedef_str:
        return parse_namespace_typedef_api_workflows(typedef_str, messages)
    else:
        raise ValueError(f"Invalid typedef format: {typedef_str}")


def parse_namespaces_api_workflows(typedefs_str: str) -> dict[str, TypeDef]:
    """Parse multiple TypeScript type definitions separated by namespaces."""

    nesting_level = 0
    command_stack = []
    message_stack = []
    extracted_typedefs_strs = []
    in_multiline_comment = False
    in_jsdoc_comment = False
    current_message = []

    for current_line in typedefs_str.splitlines():
        stripped_line = current_line.strip()

        if stripped_line.startswith("/**"):
            in_jsdoc_comment = True
            current_message = [stripped_line[3:].strip()]
            continue
        elif stripped_line.startswith("/*"):
            in_multiline_comment = True
            current_message = [stripped_line[2:].strip()]
            continue
        elif in_multiline_comment or in_jsdoc_comment:
            if stripped_line.endswith("*/"):
                in_multiline_comment = False
                in_jsdoc_comment = False
                if stripped_line != "*/":
                    current_message.append(stripped_line[:-2].strip())
                message_stack.append("\n".join(filter(None, current_message)))
                current_message = []
            else:
                line_content = stripped_line
                if line_content.startswith("*"):
                    line_content = line_content[1:].strip()
                if line_content:
                    current_message.append(line_content)
            continue

        if stripped_line.startswith("//"):
            message_stack.append(stripped_line)

        if stripped_line.startswith("@ts-"):
            message_stack.append(stripped_line)
            continue

        command_stack.append(stripped_line)
        nesting_level += stripped_line.count("{")
        nesting_level -= stripped_line.count("}")

        if nesting_level == 0 and command_stack:  # A namespace was closed
            new_typedef = "\n".join(command_stack)
            if len(new_typedef):
                extracted_typedefs_strs.append({"code": new_typedef, "messages": "\n".join(message_stack)})
            command_stack = []
            message_stack = []

    extracted_typedefs = {"context": dict()}
    for typedef_data in extracted_typedefs_strs:
        typedef = parse_typedef_api_workflows(typedef_data["code"], typedef_data["messages"])
        extracted_typedefs["context"][typedef["name"]] = typedef

    return extracted_typedefs


def parse_namespaces(input_str) -> dict[str, dict[str, TypeDef]]:
    """Parse multiple C# type definitions separated by namespaces."""
    extracted_namespaces = input_str.split("namespace")
    parsed_namespaces = {}

    if len(extracted_namespaces) > 1:
        for i in range(1, len(extracted_namespaces)):
            namespace = extracted_namespaces[i]
            start_indx = namespace.find("{")
            end_indx = namespace.rfind("}")
            content = namespace[start_indx + 1 : end_indx].strip()
            namespace_name = namespace[:start_indx].strip()
            parsed_namespaces[namespace_name] = parse_typedefs(content)
    else:
        parsed_namespaces["global"] = parse_typedefs(input_str)

    return parsed_namespaces


def parse_workflow_conversion_typedefs(workflow_typedefs: dict[str, WorkflowConversionTypeDef]) -> tuple[dict[str, TypeDef], dict[str, TypeDef]]:
    """Parse the type definitions of a workflow conversion into a dictionary of `TypeDef`."""
    workflow_activity_typedefs, workflow_additional_typedefs = {}, {}
    for activity_id, workflow_typedef in workflow_typedefs.items():
        activity_id, _ = helpers.extract_generic_pattern(activity_id)
        activity_typedef = parse_typedef(workflow_typedef["TypeDefinition"])
        workflow_activity_typedefs[activity_id] = activity_typedef
        additional_typedefs = parse_typedefs(workflow_typedef["AdditionalTypeDefinitions"])
        workflow_additional_typedefs.update(additional_typedefs)
    return workflow_activity_typedefs, workflow_additional_typedefs


def parse_classlike_typedef(typedef: str) -> tuple[dict[str, ParamTypeDef], dict[str, str]]:
    head, body = split_typedef(typedef)
    type_params = parse_type_params(head)
    params = parse_params(body)
    return params, type_params


def parse_enum_typedef_api_workflows(typedef: str) -> dict[str, ParamTypeDef]:
    _, body = split_typedef(typedef)
    enum = parse_enum_values(body)
    return {"enumerator": enum, "text": typedef}


def parse_enum_typedef(typedef: str) -> dict[str, ParamTypeDef]:
    _, body = split_typedef(typedef)
    enum = parse_enum_values(body)
    return {"enumerator": enum}


def split_typedef(typedef: str) -> tuple[str, str]:
    head, body = typedef.split("{", 1)
    return head.strip(), body.strip().rstrip("}").rstrip()


def parse_type_params(head: str) -> dict[str, str]:
    type_params = _type_param_pattern.findall(head)
    type_params_with_constraints = {}
    if type_params:
        type_params = [type_param.strip() for type_param in type_params.pop().split(",")]
        constraints = {type_param.strip(): constraint.strip() for type_param, constraint in _type_param_constraint_pattern.findall(head)}
        for type_param in type_params:
            constraint = "object"
            if type_param in constraints:
                constraint = constraints[type_param].split(",")[-1].strip()  # Take the last constraint. Seems to be the most relevant.
                if constraint in ("struct", "class", "new()"):
                    constraint = "object"
            type_params_with_constraints[type_param] = constraint
    return type_params_with_constraints


def parse_params(body: str) -> dict[str, ParamTypeDef]:
    params = [param.strip().strip("{}").strip() for param in body.split(";")]
    params = [param for param in params if param]
    parsed_params, duplicate_params = {}, []
    for param in params:
        if not param:
            continue
        parsed_param = parse_param(param)
        if not parsed_param:
            continue
        if parsed_param["name"] in parsed_params:
            duplicate_params.append(parsed_param)
            # NOTE: A stupid heuristic to handle duplicate params
            new_param_type_size = len(parsed_param["type"])
            old_param_type_size = len(parsed_params[parsed_param["name"]]["type"])
            if new_param_type_size > old_param_type_size:
                parsed_params[parsed_param["name"]] = parsed_param
        else:
            parsed_params[parsed_param["name"]] = parsed_param
    if len(parsed_params) + len(duplicate_params) != len(params):
        _logger.error(f"Failed to parse params fully:\n{body}")
    return parsed_params


def parse_param(param: str) -> ParamTypeDef | None:
    lines = param.replace("global::", "").strip().splitlines()
    property_line = lines.pop().strip()
    match = _accessor_overload_pattern.findall(property_line) or _parameter_pattern.findall(property_line)
    if not match:
        return None
    member_modifier, access_modifier, param_type, param_name = match.pop()
    access_modifier = access_modifier or "public"
    modifier = f"{member_modifier.strip()} {access_modifier.strip()}".strip()
    description, required = "", False
    for line in lines:
        line = line.strip()
        if line.startswith("[DependsOnProperty"):
            continue  # TODO: consider adding if useful, usage seems mostly bogus
        if line.startswith("[Localized"):
            continue
        if line.startswith("//"):  # parse comment line
            description = line[2:].strip()
        elif line == "[RequiredArgument]":  # parse required attribute
            required = True
        else:
            return None
    category = get_type_category(param_type, param_name)
    ctype = get_ctype(param_type)
    components = get_type_components(param_type)
    return {
        "name": param_name,
        "type": param_type,
        "ctype": ctype,
        "modifier": modifier,
        "description": description,
        "required": required,
        "category": category,
        "components": components,
    }


def parse_enum_values(body: str) -> ParamTypeDef:
    return {
        "name": "values",
        "type": "string",
        "modifier": "private",
        "category": "Property",
        "components": ["string"],
        "description": "",
        "required": True,
        "values": [part.strip() for part in body.split(",") if part.strip()],
    }


def get_type_category(type: str, name: str) -> ParamTypeCategory:
    if type.startswith("InArgument"):
        return "InArgument"
    elif type.startswith(("IEnumerable<InArgument", "ICollection<InArgument", "List<InArgument", "InArgument[]")):
        return "ListInArgument"
    elif type.startswith("Dictionary<string, InArgument"):
        return "DictionaryInArgument"
    elif type.startswith("OutArgument"):
        return "OutArgument"
    elif type.startswith(("IEnumerable<OutArgument", "ICollection<OutArgument", "List<OutArgument", "OutArgument[]")):
        return "ListOutArgument"
    elif type.startswith("Dictionary<string, OutArgument"):
        return "DictionaryOutArgument"
    elif type.startswith("InOutArgument"):
        return "InOutArgument"
    elif type.startswith("ActivityAction"):
        return "ActivityAction"
    elif type.startswith("Activity") or type.startswith("Sequence"):
        return "Activity"
    elif name.startswith("[") and name.endswith("]"):
        return "Accessor"
    else:
        return "Property"


def get_ctype(type: str) -> str:
    """
    Extract the core C# type from a type string, handling generics and special categories.

    Args:
        type (str): The type string to parse (e.g. "InArgument<string>", "OutArgument<T>")

    Returns:
        str: The core C# type without wrappers. For example:
            - "InArgument<string>" -> "string"
            - "OutArgument<T>" -> "T"
            - "List<int>" -> "List<int>"
            - "string" -> "string"
            - "OutArgument" -> ""
    """
    categories = set(get_args(ParamTypeCategory))
    if type.strip() in categories:
        return ""
    split = type.split("<", 1)
    if len(split) == 1:
        return type.strip()
    # else:
    if split[0].strip() in categories:
        suffix = split[1].strip()
        if suffix.endswith(">"):
            return suffix[:-1]
        return suffix
    return type.strip()


def get_type_components(type: str) -> list[str]:
    components_clean = []
    components = type.split("<")
    for component in components:
        component = component.rstrip(">")
        for component_clean in component.split(","):
            component_clean = component_clean.strip()
            is_nullable = False
            if component_clean.endswith("?"):
                component_clean = component_clean[:-1]
                is_nullable = True
            is_array = False
            if component_clean.endswith("[]"):
                component_clean = component_clean[:-2]
                is_array = True
            if component_clean:
                components_clean.append(component_clean)
            if is_array:
                components_clean.append("[]")
            if is_nullable:
                components_clean.append("?")
    return components_clean
