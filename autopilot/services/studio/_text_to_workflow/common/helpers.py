import json
import re
import sys
import typing as t
from pathlib import Path

pascal_case_re = re.compile(r"((?<=[a-z])(?=[A-Z]))|([\W_]+)")


def pascalize_string(s: str) -> str:
    return "".join(w.title() for w in pascal_case_re.split(s)[::3])


def camel_case_split(identifier):
    matches = re.finditer(".+?(?:(?<=[a-z])(?=[A-Z])|(?<=[A-Z])(?=[A-Z][a-z])|$)", identifier)
    return " ".join([m.group(0).replace(".", " ") for m in matches])


def extract_generic_pattern(activity_name: str) -> tuple[str, str]:
    """Split activity name in into full class name and generic pattern."""
    split_idx = activity_name.find("`1")
    if split_idx == -1:
        split_idx = activity_name.find("<")
    if split_idx == -1:
        full_class_name, generic_pattern = activity_name, ""
    else:
        full_class_name, generic_pattern = activity_name[:split_idx], activity_name[split_idx:]
    return full_class_name, generic_pattern


def get_sample_split(workflow_name: str, testsets: dict[str, set[str]]) -> str:
    for testset_name, testset in testsets.items():
        if workflow_name in testset:
            return testset_name
    if workflow_name.startswith("_Prod_filtered"):
        return "prod-filtered"
    return "train"


def target_framework_to_target_platform(target_framework: str) -> str:
    if target_framework == "Portable":
        return "windows"
    return target_framework.lower()


def get_workflow_name(converted_json_path: Path, relative_to: Path) -> str:
    converted_json_relative_path = converted_json_path.relative_to(relative_to)
    json_folder = converted_json_relative_path.parent
    workflow_name = json_folder.as_posix().replace("/", "_")
    if converted_json_relative_path.stem != "Main":
        workflow_name = f"{workflow_name}_{converted_json_relative_path.stem}"
    return workflow_name


def filter_projects(studio_desktop_path: Path, converted_path: Path, contains: str | None = None, only_new: bool = False, only_errors: bool = False):
    projects = []
    for project_json_path in studio_desktop_path.rglob("project.json"):
        if contains is not None and contains not in str(project_json_path):
            continue
        relative_project_path = project_json_path.relative_to(studio_desktop_path.parent)
        workflow_name = "_".join(relative_project_path.parts[:-1])
        if workflow_name in {"StudioDesktop_IFWhileElse_IFELSE", "StudioDesktop_IFWhileElse_RETRYSCOPE"}:
            continue  # Skip these workflows, we changed them manually in the converted dataset

        with open(project_json_path, "r", encoding="utf-8-sig") as f:
            project_json = json.load(f)
            target_framework = project_json["targetFramework"]  # Portable, Windows, Legacy

        # can only convert portable/windows projects for now
        if target_framework == "Legacy":
            # print(f"Skipping {project_json_path} because it is {target_framework}")
            continue

        conversion_folder = relative_project_path.parent.as_posix().replace("/", "_")
        output_folder = converted_path / target_framework / conversion_folder
        is_new = not output_folder.exists()
        has_errors = len(list(output_folder.glob("**/*.error.json"))) > 0

        if only_new and not is_new:
            continue
        if only_errors and not has_errors:
            continue
        projects.append((project_json_path, output_folder, project_json))
    return projects


class Tee(object):
    """Tee stdout to a file and the original stdout. Use as context manager."""

    path: Path | None
    fd: t.IO[str]
    stdout: t.IO[str]

    def __init__(self, path: Path | None, disable: bool = False):
        self.disable = disable
        self.path = path

    def __enter__(self):
        if self.disable:
            return self
        if self.path is not None:
            self.fd = open(self.path, "w")
        self.stdout = sys.stdout
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.disable:
            return
        sys.stdout = self.stdout
        if self.path is not None:
            self.fd.close()

    def write(self, data):
        if self.path is not None:
            self.fd.write(data)
        self.stdout.write(data)

    def flush(self):
        if self.path is not None:
            self.fd.flush()
        self.stdout.flush()
