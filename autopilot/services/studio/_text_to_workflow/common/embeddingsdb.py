import json
import sqlite3
from pathlib import Path

from services.studio._text_to_workflow.common import constants, helpers, typedefs_parser
from services.studio._text_to_workflow.common.activity_utils import get_input_properties, get_output_type
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ActivityType, EmbeddingsActivityFilterOptions, TargetFramework
from services.studio._text_to_workflow.utils.dynamic_activity_utils import is_generic_dynamic_activity


def load_embeddings_db(db_path: Path, framework: TargetFramework | None, activity_type: ActivityType | None) -> list[dict]:
    with sqlite3.connect(db_path) as conn:
        if activity_type is not None:
            is_trigger = int(activity_type == "trigger")
            if framework is not None:
                cursor = conn.execute("SELECT * FROM ActivityEmbeddings WHERE TargetFramework = ? AND IsTrigger = ?", (framework, is_trigger))
            else:
                cursor = conn.execute("SELECT * FROM ActivityEmbeddings WHERE IsTrigger = ?", (is_trigger,))
        else:
            if framework is not None:
                cursor = conn.execute("SELECT * FROM ActivityEmbeddings WHERE TargetFramework = ?", (framework,))
            else:
                cursor = conn.execute("SELECT * FROM ActivityEmbeddings")

        mapping = {}
        for i, col in enumerate(cursor.description):
            mapping[i] = col[0]

        items = []
        for row in cursor:
            item = {}
            for k, v in mapping.items():
                item[v] = row[k]
            items.append(item)
    return items


def is_supported_integration_service_activity(item: dict, include_dynamic_http_activities: bool = False) -> bool:
    is_integration_service_activity = item["PackageName"].endswith(constants.INTEGRATION_SERVICE_PACKAGE_SUFFIX)
    if is_integration_service_activity:
        is_supported = item["ActivityFullName"] in constants.DAP_ACTIVITY_NAMES or item["PackageName"] in constants.LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST
        if include_dynamic_http_activities:
            is_supported = is_supported or item["ActivityFullName"] == constants.DAP_HTTP_ACTIVITY_NAME
        return is_supported
    return True


def create_dynamic_activity_full_id(activity_name: str, type_id: str) -> str:
    """Create a full activity id with type id."""
    return f"{activity_name}@{type_id}"


def load_activity_definitions(
    db_path: Path,
    framework: TargetFramework | None = None,
    activity_type: ActivityType | None = None,
    activity_filter_options: EmbeddingsActivityFilterOptions | None = None,
) -> dict[str, ActivityDefinition]:
    documents = load_embeddings_db(db_path, framework, activity_type)
    missing_name, missing_desc = [], []

    activity_definitions = {}
    for document in documents:
        # here we should check if the activity should be included based on the filter options
        typedef = typedefs_parser.parse_typedef(document["TypeDefinition"])
        classname, params, type_params = typedef["name"], typedef["params"], typedef["type_params"]

        if activity_filter_options and activity_filter_options.exclude_generic_dynamic_activities:
            if is_generic_dynamic_activity(classname, document["Configuration"]):
                continue

        if activity_filter_options:
            if activity_filter_options.allowed_activity_names and document["ActivityFullName"] not in activity_filter_options.allowed_activity_names:
                continue
            if activity_filter_options.activities_to_exclude and classname in activity_filter_options.activities_to_exclude:
                continue

        include_dynamic_http_activities = activity_filter_options.include_dynamic_http_activities if activity_filter_options else False
        if not is_supported_integration_service_activity(document, include_dynamic_http_activities):
            continue
        document["ActivityFullName"] = document["ActivityFullName"].removesuffix(constants.GENERIC_ACTIVITY_NAME_SUFFIX)
        productivity_connection_key = constants.PRODUCTIVITY_ACTIVITY2CONNECTION.get(document["ActivityFullName"], None)
        if productivity_connection_key is not None:
            document["ConnectorKey"] = productivity_connection_key
        if document["ActivityText"] is None:
            missing_name.append(document["ActivityFullName"])
            document["ActivityText"] = helpers.camel_case_split(document["ActivityFullName"].split(".")[-1])
        if document["ActivityDescription"] is None:
            missing_desc.append(document["ActivityFullName"])
            document["ActivityDescription"] = helpers.camel_case_split(document["ActivityFullName"])
        fully_qualified_class_name = f"{document['Namespace']}.{classname}"
        full_activity_id = document["ActivityFullName"]
        if document["UiPathActivityTypeId"] is not None:
            full_activity_id = create_dynamic_activity_full_id(full_activity_id, document["UiPathActivityTypeId"])
        category = (document["Category"] or document["PackageDisplayName"] or "").replace(",", " ")
        package_display_name = document["PackageDisplayName"]

        # TO DO: revisit and validate this once jsonSchemas are added
        input_properties_json_schema, output_type_json_schema, http_method = None, None, None
        if document.get("JsonSchema") is not None:
            json_schema = json.loads(document["JsonSchema"])
            input_properties_json_schema = get_input_properties(json_schema)
            output_type_json_schema = get_output_type(json_schema)
            http_method = json_schema.get("method", None)

        display_name = document["ActivityText"]
        mapped_category, mapped_name = remap_and_get_activity_category_and_name(category, classname, display_name, full_activity_id)
        activity_definitions[full_activity_id] = {
            "id": document["Id"],
            "index": len(activity_definitions),
            "fullActivityId": full_activity_id,
            "fullActivityName": document["ActivityFullName"],
            "fullClassName": fully_qualified_class_name,
            "className": classname,
            "namespace": document["Namespace"],
            "packageName": document["PackageName"],
            "packageVersion": document["PackageVersion"],
            "displayName": display_name,
            "description": document["ActivityDescription"],
            "category": category,
            "typeDefinition": document["TypeDefinition"],
            "additionalTypeDefinitions": document["AdditionalTypeDefinitions"],
            "additionalNamespaces": document["AdditionalTypeNamespaces"],
            "activityConfiguration": document["Configuration"],
            "activityIsConfigured": False if document["Configuration"] is not None else None,
            "connectorKey": document["ConnectorKey"],
            "activityTypeId": document["UiPathActivityTypeId"],
            "connectionId": None,
            "dynamicActivityDetails": None,
            "isTrigger": document["IsTrigger"] == 1,
            "params": params,
            "genericParamDefinition": type_params,
            "similarity": None,
            "step": None,
            "mapped_category": mapped_category,
            "mapped_name": mapped_name,
            "jsonSchema": document.get("JsonSchema"),
            "inputPropertiesJsonSchema": input_properties_json_schema,
            "outputTypeJsonSchema": output_type_json_schema,
            "httpMethod": http_method,
            "packageDisplayName": package_display_name,
        }
    if missing_name:
        print("Documents missing display name:")
        for name in missing_name:
            print(name)
    if missing_desc:
        print("Documents missing description:")
        for name in missing_desc:
            print(name)
    return activity_definitions


# fmt: off
CORE_CONTROL_FLOW_DISPLAY_NAMES = {
    "ForEach", "While", "DoWhile",
    "RepeatNumberOfTimes", "RetryScope",
    "If", "CheckTrue", "Switch", "TryCatch",
    "Break", "Throw", "Rethrow",
    "InvokeCode", "InvokeWorkflowFile",
}
CORE_TEXT_DISPLAY_NAMES = {
    "FindMatchingPatterns", "ReplaceMatchingPatterns", "IsTextMatching",
}
CORE_DATA_TABLE_DISPLAY_NAMES = {
    "BuildDataTable", "JoinDataTables", "MergeDataTable",
    "LookupDataTable", "FilterDataTable", "SortDataTable",
    "ClearDataTable", "GenerateDataTableFromText",
    "CollectionToDataTable", "OutputDataTableAsText",
    "ForEachRowInDataTable",
    "AddDataColumn", "RemoveDataColumn", "AddDataRow", "RemoveDataRow",
    "GetRowItem", "UpdateRowItem", "RemoveDuplicateRows",
}
CORE_FILE_DISPLAY_NAMES = {
    "CreateFile", "MoveFile", "CopyFile",
    "ReadTextFile", "WriteTextFile", "AppendLine",
    "CreateFolder", "CopyFolder",
    "DeleteFileOrFolder",
    "PathExists", "ForEachFileInFolder",
}
# REMAINING_CORE_DISPLAY_NAMES = {"TimeTrigger", "MessageBox", "SetVariableValue", "Comment", "DisabledActivities"}

UIPATH_ACTIVITIES_PARTS_MAPPING = {
    "IntelligentOCR": "DocumentUnderstanding",
    "UIAutomationNext": "UIAutomation",
}
# fmt: on


def remap_and_get_activity_category_and_name(category, class_name, display_name, activity_id) -> tuple[str, str]:
    """
    Remaps the category and name of the activity to a more concise and human-readable form.

    Leverages the existing category, classname, displayname and fullactivityid fields to create the new representation.
    This is an heuristic rule based approach. In order to better understand the mapping, refer to the full mapping table.
    This is an excerpt containing `category.name` pairs for various activities:
    ```
    DocumentUnderstanding.ExtractDocumentData | UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData
    Collections.AddToCollection               | System.Activities.Statements.AddToCollection
    Core.MessageBox                           | UiPath.Core.Activities.MessageBox
    ControlFlow.Break                         | UiPath.Core.Activities.Break
    DataTable.AddDataRow                      | UiPath.Core.Activities.AddDataRow
    Database.BulkUpdate                       | UiPath.Database.Activities.BulkUpdate
    Text.FindMatchingPatterns                 | UiPath.Core.Activities.Matches
    Excel.ReadRange                           | UiPath.Excel.Activities.ReadRange
    File.CopyFile                             | UiPath.Core.Activities.CopyFile
    Forms.CloseFormActivity                   | UiPath.Forms.Activities.CloseFormActivity
    GSuite.ArchiveEmail                       | UiPath.GSuite.Activities.ArchiveEmailConnections
    GitHub.CreateIssue                        | UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@<github_create_issue_id>
    MicrosoftOffice365.CalendarEventCreated   | UiPath.MicrosoftOffice365.Activities.Calendar.Triggers.NewEventCreated
    ```
    """
    category = helpers.pascalize_string(category)  # PascalCase
    display_name = helpers.pascalize_string(display_name)  # PascalCase

    name = display_name
    if class_name.startswith("UiPath_GenAI_Activities"):  # these are IntegrationService activities, keep them first
        category, name = "UiPathGenAI", name.replace("UiPath_GenAI_Activities", "")
    elif activity_id.startswith("UiPath.IntegrationService."):
        if "Box" == category:
            category = "BoxContentManagement"
        # else keep this as is
    # elif any(activity_id.startswith(x) for x in ["UiPath.Core.Activities.", "UiPath.Activities.System.", "System.Activities.Statements.",]):
    #     category = ""  # these are all core, no category for them
    elif activity_id.startswith("UiPath.Activities.System."):
        _, _, _, category, *_remaining = activity_id.split(".")
        if not _remaining:
            category = "System"
        if category == "FileOperations":
            category = "File"
            if name == "DownloadFileFromUrl":
                category = "Web"
    elif activity_id.startswith("UiPath.Core.Activities.Storage"):
        category = "UiPathOrchestratorStorage"
    elif activity_id.startswith("UiPath.Core.Activities.") and category.lower() == "uipathorchestrator":
        category = "UiPathOrchestrator"
    elif activity_id.startswith("UiPath.Core.Activities.") or activity_id.startswith("System.Activities.Statements."):
        category = "Core"  # default mapping
        if name == "AddToCollection":
            category = "Collections"
        elif name in CORE_CONTROL_FLOW_DISPLAY_NAMES:
            category = "ControlFlow"
        elif name in CORE_TEXT_DISPLAY_NAMES:
            category = "Text"
        elif name in CORE_DATA_TABLE_DISPLAY_NAMES:
            category = "DataTable"
        elif name in CORE_FILE_DISPLAY_NAMES:
            category = "File"
    elif activity_id.startswith("UiPath."):
        namespace_parts = [x for x in activity_id.split(".")[1:-1] if x not in {"Activities"}]
        namespace_parts = [UIPATH_ACTIVITIES_PARTS_MAPPING.get(x, x) for x in namespace_parts]
        category = namespace_parts[0] if namespace_parts else ""
        # if category in {"CSV", "Excel", "Forms", "Mail", "Presentations", "Word"}:
        #     name = class_name
    elif activity_id.startswith("RandomizingTools."):
        category = "RandomizingTools"
    return category, name


def get_activity_embedding_representation(activity_details: ActivityDefinition) -> str:
    """Returns a concise string representation of the activity to be used for embeddings."""
    category = activity_details["category"]
    class_name = activity_details["className"]
    display_name = activity_details["displayName"]
    activity_id = activity_details["fullActivityId"]
    category, name = remap_and_get_activity_category_and_name(category, class_name, display_name, activity_id)
    return f"{category}.{name}"
