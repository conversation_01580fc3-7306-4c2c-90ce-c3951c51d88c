import asyncio


async def generate_tasks_and_execute(task_generator, max_concurrent_tasks: int = 10):
    it = task_generator.__aiter__()
    cancelled = False

    async def worker():
        async for task in it:
            try:
                await task
            except asyncio.CancelledError:
                # If a generated task is canceled, let its worker
                # proceed with other tasks - except if it's the
                # outer coroutine that is cancelling us.
                if cancelled:
                    raise
            # other exceptions are propagated to the caller

    worker_tasks = [asyncio.create_task(worker()) for i in range(max_concurrent_tasks)]
    try:
        await asyncio.gather(*worker_tasks)
    except:
        # In case of exception in one worker, or in case we're
        # being cancelled, cancel all workers and propagate the
        # exception.
        cancelled = True
        for t in worker_tasks:
            t.cancel()
        raise
