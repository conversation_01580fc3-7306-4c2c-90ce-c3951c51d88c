import copy
import pathlib
from abc import ABC, abstractmethod

import numpy as np
import tqdm

from services.studio._text_to_workflow.common import (
    build_embeddings,
    constants,
    helpers,
)
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    ActivityRetrieverState,
    ActivitySearchOptions,
    ActivityType,
    Connection,
    PlanStep,
    TargetFramework,
)
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import (
    embeddings_db,
    errors,
    paths,
    telemetry_utils,
)
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.workflow_generation.config import constants as workflow_generation_constants

LOGGER = telemetry_utils.AppInsightsLogger()

_DEPRECATED_ACTIVITIES_FOUND_IN_DATASET = {
    # No activity definition for these; to remove once dataset is fixed
    "UiPath.Core.Activities.IfElseIfV2": "IfElseIfV2",
    "UiPath.Core.Activities.IfElseIfV2.ElseIfClause": "ElseIf",  # legit
    "System.Activities.Statements.InvokeMethod": "InvokeMethod",
    # from Windows
    "System.Activities.Statements.Flowchart": "Flowchart",
    "UiPath.Core.Activities.TakeScreenshot": "TakeScreenshot",
    "UiPath.Database.Activities.DatabaseTransaction": "DatabaseTransaction",
    "UiPath.Excel.Activities.Business.ChartModifications.ChangeDataRangeModification": "ChangeDataRangeModification",
    "UiPath.Excel.Activities.Business.ExecuteMacroArgumentX": "ExecuteMacroArgumentX",
    "UiPath.Excel.Activities.Business.InvokeVBAArgumentX": "InvokeVBAArgumentX",
    "UiPath.Excel.Activities.Business.PivotTableFieldX": "PivotTableFieldX",
    "UiPath.Excel.Activities.Business.SortColumnX": "SortColumnX",
    "UiPath.GSuite.Activities.AddNewSheet": "AddNewSheet",
    "UiPath.GSuite.Activities.DownloadSpreadsheet": "DownloadSpreadsheet",
    "UiPath.GSuite.Activities.ReadRange": "ReadRange",
    "UiPath.GSuite.Activities.Sheets.GoogleSheetsApplicationScope": "GoogleSheetsApplicationScope",
    "UiPath.GSuite.Activities.WriteRange": "WriteRange",
    "UiPath.Presentations.Activities.ChangeShapeNameSlideContentModification": "ChangeShapeNameSlideContentModification",
    "UiPath.Presentations.Activities.RunMacroArgument": "RunMacroArgument",
    "UiPath.Testing.Activities.ArgumentsBridge": "ArgumentsBridge",
    "UiPath.Testing.Activities.VerifyControlAttribute": "VerifyControlAttribute",
    "UiPath.UIAutomationNext.Activities.NExtractData": "NExtractData",
}

# When fixing an activity name, we will usually search for the correct activity by class name. Sometimes we get multiple candidates, each belonging to a different package.
# We need to ensure we don't accidentally search in the wrong package. So we will do the following:
# If the original activity namespace matches any of these segments, we will try to find a candidate with the same namespace segment.
ACTIVITY_NAME_FIX_PREFERENTIAL_NAMESPACE_MAP = {
    "GSuite": "UiPath.GSuite.Activities",
    "Office365": "UiPath.MicrosoftOffice365",
    "O365": "UiPath.MicrosoftOffice365",
}


class ActivitiesRetrieverBase(ABC):
    index: dict[tuple[TargetFramework, ActivityType] | ActivityType, "ActivitiesIndexBase"]

    def _initialize(self) -> None:
        """This method will initialize the object and is explicitly called in __new__ to do the initialization once only"""
        self.db_hash = None

    def build(self) -> "ActivitiesRetrieverBase":
        self.db_hash = embeddings_db.get_embeddings_hash()
        return self

    def _search_in_index(
        self,
        index: "ActivitiesIndexBase",
        step: PlanStep,
        connections: list[Connection],
        ignored_namespaces: set[str],
        ignored_activities: set[str],
        k: int,
    ) -> list[ActivityDefinition]:
        relevant_documents = []
        candidate_documents, candidate_scores = index.search(step, connections, ignored_namespaces, ignored_activities, k=k)
        for document, score in zip(candidate_documents, candidate_scores, strict=False):
            document["similarity"] = score
            relevant_documents.append(document)
        return relevant_documents

    @abstractmethod
    def search(
        self,
        step: PlanStep,
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        activity_type: ActivityType,
        k: int,
    ) -> list[ActivityDefinition]:
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_all_connectors(self) -> set[str]:
        raise NotImplementedError("Subclasses must implement this method")

    def activity_exists_in_index(self, activity_name: str, index: "ActivitiesIndexBase") -> tuple[bool, bool]:
        activity_name, _ = helpers.extract_generic_pattern(activity_name)
        return (
            activity_name in index.store.state["fullactivityid2index"],
            activity_name in index.store.state["fullclassname2index"],
        )

    @abstractmethod
    def get_relevant(
        self,
        steps: list[PlanStep],
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        k: int = 5,
    ) -> tuple[list[PlanStep], list[ActivityDefinition], list[ActivityDefinition], list[str]]:
        raise NotImplementedError("Subclasses must implement this method")


class ActivitiesRetriever(ActivitiesRetrieverBase):
    _singleton_instance: "ActivitiesRetriever | None" = None

    def __new__(cls) -> "ActivitiesRetriever":
        if cls._singleton_instance is None:
            cls._singleton_instance = super(ActivitiesRetriever, cls).__new__(cls)
            cls._singleton_instance._initialize()
        return cls._singleton_instance

    def _initialize(self) -> None:
        super()._initialize()
        embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

        if not hasattr(self, "index"):
            self.index = {
                ("Portable", "trigger"): ActivitiesIndex(embedding_model, "Portable", "trigger"),
                ("Portable", "activity"): ActivitiesIndex(embedding_model, "Portable", "activity"),
                ("Windows", "trigger"): ActivitiesIndex(embedding_model, "Windows", "trigger"),
                ("Windows", "activity"): ActivitiesIndex(embedding_model, "Windows", "activity"),
            }

    def get_all_connectors(self) -> set[str]:
        connectors = set()
        for activity in self.index["Windows", "activity"].store.state["activities"]:
            if activity["connectorKey"] is not None:
                connectors.add(activity["connectorKey"])

        return connectors

    def activity_exists(
        self,
        activity_name: str,
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ) -> tuple[bool, bool]:
        """Base method to check if an activity exists in a given index

        Args:
            activity_name: The name of the activity to check
            index_key: The key to look up the index. Could be a tuple (target_framework, activity_type)
            or just activity_type depending on the subclass implementation

        Returns:
            A tuple of (exists_by_activity_id, exists_by_class_name)
        """
        if (target_framework, activity_type) not in self.index:
            return (False, False)

        index = self.get_index(target_framework, activity_type)
        return self.activity_exists_in_index(activity_name, index)

    def get_activity_info(
        self,
        activity_id: str,
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ) -> ActivityDefinition | None:
        return self.get(activity_id, target_framework, activity_type)

    def update_activity(self, activity_definition: ActivityDefinition, target_framework: TargetFramework) -> None:
        activity_type = "trigger" if activity_definition["isTrigger"] else "activity"
        self.index[(target_framework, activity_type)].update_activity(activity_definition)

    def search(
        self,
        step: PlanStep,
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        activity_type: ActivityType,
        k: int,
    ) -> list[ActivityDefinition]:
        index = self.index[(activity_search_options["target_framework"], activity_type)]  # type: ignore
        activities = self._search_in_index(
            index,
            step,
            connections,
            activity_search_options["ignored_namespaces"],
            activity_search_options["ignored_activities"],
            k,
        )
        return activities

    def get_index(self, target_framework: TargetFramework, activity_type: ActivityType) -> "ActivitiesIndexBase":
        return self.index[(target_framework, activity_type)]

    def placeholder_trigger(self) -> str:
        return "UiPath.Core.Activities.ManualTrigger"

    def placeholder_activity(self) -> str:
        return constants.SEQUENCE_ACTIVITY_NAME

    def get_all_by_package_name(self, package_name: str, target_framework: TargetFramework) -> list[ActivityDefinition]:
        triggers = self.index[(target_framework, "trigger")].get_all_by_package_name(package_name)
        activities = self.index[(target_framework, "activity")].get_all_by_package_name(package_name)
        return triggers + activities

    def get_basic_activities(
        self, target_framework: TargetFramework, mode: ActivitiesGenerationMode
    ) -> tuple[
        list[ActivityDefinition],
        list[ActivityDefinition | None] | list[ActivityDefinition],
    ]:
        basic_triggers = self.index[(target_framework, "trigger")].get_basic_activities()
        basic_activities = self.index[(target_framework, "activity")].get_basic_activities()

        if mode == "testcase":
            # Ensure verification definition is added into a new basic activities array
            basic_activities = basic_activities + [
                self.get(
                    constants.TESTING_ACTIVITIES_VERIFY_EXPRESSION,
                    target_framework,
                    "activity",
                )
            ]

        return basic_triggers, basic_activities

    def get_relevant(
        self,
        steps: list[PlanStep],
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        k: int = 5,
    ) -> tuple[list[PlanStep], list[ActivityDefinition], list[ActivityDefinition], list[str]]:
        steps, packages = copy.deepcopy(steps), set()
        basic_triggers = self.index[(activity_search_options["target_framework"], "trigger")].get_basic_activities()
        basic_activities = self.index[(activity_search_options["target_framework"], "activity")].get_basic_activities()

        if activity_search_options["mode"] == "testcase":
            verify_activity = self.get(constants.TESTING_ACTIVITIES_VERIFY_EXPRESSION, activity_search_options["target_framework"], "activity")
            if verify_activity:
                basic_activities = basic_activities + [verify_activity]

        if activity_search_options["mode"] == "workflow":
            # get triggers for first step
            relevant_triggers = self.search(steps[0], connections, activity_search_options, "trigger", k)
            steps[0]["triggers"] = relevant_triggers
            for trigger in relevant_triggers:
                trigger["step"] = 1
                packages.add(trigger["packageName"])
            for i, step in enumerate(steps[1:]):
                relevant_activities = self.search(step, connections, activity_search_options, "activity", k)
                step["activities"] = relevant_activities
                for activity in relevant_activities:
                    activity["step"] = i + 2
                    packages.add(activity["packageName"])
        else:
            for i, step in enumerate(steps):
                relevant_activities = self.search(step, connections, activity_search_options, "activity", k)
                step["activities"] = relevant_activities
                for activity in relevant_activities:
                    activity["step"] = i + 1
                    packages.add(activity["packageName"])
        return steps, basic_triggers, basic_activities, list(packages)

    def get(
        self,
        item_full_activity_id_or_class_name: str,
        target_framework: TargetFramework,
        activity_type: ActivityType | None = None,
    ) -> ActivityDefinition | None:
        if activity_type is not None:
            return self._get(item_full_activity_id_or_class_name, target_framework, activity_type)
        for activity_type in ("trigger", "activity"):
            if item := self._get(item_full_activity_id_or_class_name, target_framework, activity_type):
                return item
        return None

    def get_by_class_name(
        self,
        item_class_name: str,
        target_framework: TargetFramework,
        activity_type: ActivityType | None = None,
    ) -> ActivityDefinition | None:
        if activity_type is not None:
            index = self.index[(target_framework, activity_type)]
            return index.get_by_full_class_name(item_class_name)

        for activity_type in ("trigger", "activity"):
            index = self.index[(target_framework, activity_type)]
            if item := index.get_by_full_class_name(item_class_name):
                return item

        return None

    def _get(
        self,
        item_full_activity_id_or_class_name: str,
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ) -> ActivityDefinition | None:
        index = self.index[(target_framework, activity_type)]
        if item := index.get_by_full_activity_id(item_full_activity_id_or_class_name):
            return item
        if item := index.get_by_full_class_name(item_full_activity_id_or_class_name):
            return item
        return None

    def get_versioned_package(
        self,
        activity_name: str,
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ) -> str | None:
        full_activity_id, _ = helpers.extract_generic_pattern(activity_name)
        activity = self.get(full_activity_id, target_framework, activity_type)
        if activity is None:
            return None
        return f"{activity['packageName']}, {activity['packageVersion']}"

    def try_fix_activity_name(
        self,
        name: str,
        thought: str,
        connections: list[Connection],
        target_framework: TargetFramework,
        activity_type: ActivityType,
        ignored_activities: set[str],
    ) -> str | None:
        index = self.index[(target_framework, activity_type)]
        name, generic = helpers.extract_generic_pattern(name)
        activity = index.get_by_full_class_name(name)
        if activity is None:
            class_name = name.split(".")[-1]
            namespace = name[: name.rindex(".")]
            activity = index._search_by_class_name(class_name, thought, connections, ignored_activities, namespace)
        if activity is None:
            return None
        # Dynamic activities cannot be generic
        full_class_name = activity["fullClassName"]
        if generic and activity["activityTypeId"] is None:
            full_class_name += generic
        return full_class_name


class ActivitiesIndexBase(StateBuilder):
    store: StateStore
    activity_type: ActivityType
    target_framework: TargetFramework
    embedding_model: EmbeddingModel

    def __init__(
        self,
        embedding_model: EmbeddingModel,
        target_framework: TargetFramework,
        activity_type: ActivityType,
        root_path: pathlib.Path,
    ) -> None:
        self.embedding_model = embedding_model
        self.target_framework = target_framework
        self.activity_type = activity_type
        self.store = StateStore((root_path / f"index.{target_framework}.{activity_type}.pkl").as_posix(), self, lazy_load=settings.DEBUG_MODE)
        self._basic_activities = []

    @abstractmethod
    def build(self) -> tuple[ActivityRetrieverState, dict]:
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_basic_activities(self) -> list[ActivityDefinition]:
        raise NotImplementedError("Subclasses must implement this method")

    def get_by_index(self, index: int) -> ActivityDefinition:
        return copy.deepcopy(self.store.state["activities"][index])

    def _get_by_index_read_only(self, index: int) -> ActivityDefinition:
        return self.store.state["activities"][index]

    def get_by_full_activity_id(self, full_activity_id: str) -> ActivityDefinition | None:
        if index := self.store.state["fullactivityid2index"].get(full_activity_id, None):
            return copy.deepcopy(self.store.state["activities"][index])
        return None

    def get_by_full_class_name(self, full_class_name: str) -> ActivityDefinition | None:
        if full_class_name in self.store.state["classname2fullclassnames"]:
            full_class_name = self.store.state["classname2fullclassnames"][full_class_name][0]
        if index := self.store.state["fullclassname2index"].get(full_class_name, None):
            return copy.deepcopy(self.store.state["activities"][index])
        return None

    def get_all_by_package_name(self, package_name: str) -> list[ActivityDefinition]:
        return [activity for activity in self.store.state["activities"] if activity["packageName"] == package_name]

    def get(self, name: str) -> ActivityDefinition | None:
        if activity := self.get_by_full_activity_id(name):
            return activity
        if activity := self.get_by_full_class_name(name):
            return activity
        return None

    def _search_by_class_name(
        self,
        class_name: str,
        thought: str,
        connections: list[Connection],
        ignored_activities: set[str],
        namespace: str | None = None,
    ) -> ActivityDefinition | None:
        classname_candidates = self.store.state["classname2fullclassnames"].get(class_name, None)
        if classname_candidates is None:
            return None
        elif len(classname_candidates) == 1:
            return self.get_by_full_class_name(classname_candidates[0])

        embedding = self.embedding_model.encode(thought, instruction_set="tool", instruction_type="query").tolist()
        fake_plan_step: PlanStep = {
            "embedding": embedding,
            "text": thought,
            "activities": [],
            "triggers": [],
        }
        thought_candidates, _ = self.search(
            fake_plan_step,
            connections if connections is not None else [],
            set(),
            ignored_activities,
            8,
        )

        # sometimes, multiple activities have the same class name, but are from different namespaces
        # we need to ensure that we return the correct activity and don't accidentally return an activity from the wrong package
        candidate_activities = [c for c in thought_candidates if c["fullClassName"] in classname_candidates]

        if len(candidate_activities) == 0:
            return None

        namespace_identifier = next(
            (ns for ns in ACTIVITY_NAME_FIX_PREFERENTIAL_NAMESPACE_MAP.keys() if namespace and ns in namespace),
            None,
        )
        if namespace_identifier is None:
            return candidate_activities[0]
        else:
            # found a package that matches the current namespace
            namespace_segment = ACTIVITY_NAME_FIX_PREFERENTIAL_NAMESPACE_MAP[namespace_identifier]
            for candidate in candidate_activities:
                if namespace_segment in candidate["namespace"]:
                    # return the first activity that matches the namespace
                    return candidate

        return candidate_activities[0]

    def search(
        self,
        step: PlanStep,
        connections: list[Connection],
        ignored_namespaces: set[str],
        ignored_activities: set[str],
        k: int = 5,
        accepted_dap_activities_names: set[str] = constants.DAP_ACTIVITY_NAMES,
        dinamyc_activities_procent_scores: dict[str, float] = {"description": 0.5, "category": 0.5, "display_name": 0},
    ) -> tuple[list[ActivityDefinition], list[float]]:
        candidate_documents = self.store.state["activities"]
        display_name_embeddings = self.store.state["display_name_embeddings"]
        description_embeddings = self.store.state["description_embeddings"]
        category_embeddings = self.store.state["category_embeddings"]

        # activities with an existing connection or activities that do not require connections are configurable.
        available_connection_key = set()
        for connection in connections:
            available_connection_key.add(connection["connector"])

        # do not require IS connection
        static_document_ids = []
        # requires IS connection but is statically generated
        static_is_document_ids = []
        # requires IS connection and is dynamically generated based on connection
        dynamic_is_document_ids = []
        # static or IS with available connection
        configurable_document_ids = []
        for i, candidate_document in enumerate(candidate_documents):
            if candidate_document["connectorKey"] is None:
                static_document_ids.append(i)
                configurable_document_ids.append(i)
            elif candidate_document["fullActivityName"] in accepted_dap_activities_names:
                dynamic_is_document_ids.append(i)
                if candidate_document["connectorKey"] in available_connection_key:
                    configurable_document_ids.append(i)
            else:
                static_is_document_ids.append(i)
                if candidate_document["connectorKey"] in available_connection_key:
                    configurable_document_ids.append(i)

        query_embedding_arr = np.array(step["embedding"]).transpose()
        display_name_scores = (display_name_embeddings @ query_embedding_arr).flatten()
        description_scores = (description_embeddings @ query_embedding_arr).flatten()
        category_scores = (category_embeddings @ query_embedding_arr).flatten()

        static_display_name_scores = display_name_scores[static_document_ids]
        static_description_scores = description_scores[static_document_ids]

        static_is_display_name_scores = display_name_scores[static_is_document_ids]
        static_is_description_scores = description_scores[static_is_document_ids]
        static_is_category_scores = category_scores[static_is_document_ids]

        dynamic_is_description_scores = description_scores[dynamic_is_document_ids]
        dynamic_is_category_scores = category_scores[dynamic_is_document_ids]
        dynamic_is_display_name_scores = display_name_scores[dynamic_is_document_ids]

        scores = np.zeros_like(display_name_scores, dtype=np.float32)
        # static documents don't need category information
        scores[static_document_ids] = static_display_name_scores * 5 / 10 + static_description_scores * 5 / 10
        # static IS documents (e.g. outlook) rely mostly on display name and description
        scores[static_is_document_ids] = static_is_display_name_scores * 5 / 10 + static_is_description_scores * 4 / 10 + static_is_category_scores * 1 / 10
        # dynamic activities rely heaviling on description and category. display name is usually too generic
        scores[dynamic_is_document_ids] = (
            dynamic_is_description_scores * dinamyc_activities_procent_scores["description"]
            + dynamic_is_category_scores * dinamyc_activities_procent_scores["category"]
            + dynamic_is_display_name_scores * dinamyc_activities_procent_scores["display_name"]
        )
        # bump scores for activities that are configurable
        scores[configurable_document_ids] *= 1.1

        sorted_indices = np.argsort(-scores).tolist()
        sorted_scores = scores[sorted_indices].tolist()

        top_k_documents, top_k_scores = [], []
        for i, score in zip(sorted_indices, sorted_scores, strict=False):
            if len(top_k_documents) >= k:
                break
            candidate_document = self._get_by_index_read_only(i)
            if self._has_ignored_namespace(candidate_document, ignored_namespaces) or candidate_document["fullActivityName"] in ignored_activities:
                continue

            # Only append a copy of the document if it's not a DAP activity, as only those are mutable.
            # DAP activity type definitions are dynamically generated based on the connection
            if candidate_document["connectorKey"] is None:
                top_k_documents.append(candidate_document)
            else:
                top_k_documents.append(copy.deepcopy(candidate_document))
            top_k_scores.append(score)
        return top_k_documents, top_k_scores

    def update_activity(self, activity_definition: ActivityDefinition) -> None:
        self.store.state["activities"][activity_definition["index"]] = activity_definition
        # Invalidate the basic activities cache
        self._basic_activities = []

    def _has_ignored_namespace(self, activity: ActivityDefinition, ignored_namespaces: set[str]) -> bool:
        return activity["namespace"] in ignored_namespaces


class ActivitiesIndex(ActivitiesIndexBase):
    def __init__(
        self,
        embedding_model: EmbeddingModel,
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ) -> None:
        super().__init__(
            embedding_model,
            target_framework,
            activity_type,
            paths.get_activity_retriever_path(),
        )

    def build(self) -> tuple[ActivityRetrieverState, dict]:
        state = build_embeddings.build(
            self.activity_type,
            self.embedding_model,
            self.target_framework,
            show_progress_bar=settings.DEBUG_MODE,
        )
        if not state["activities"]:
            raise errors.EmptyActivitiesIndexError(self.target_framework, self.activity_type)
        state_info = {
            k: state[k]
            for k in (
                "fullactivityid2index",
                "fullclassname2index",
                "classname2fullclassnames",
                "activities",
            )
        }
        tqdm.tqdm.write(f"Built {self.target_framework}.{self.activity_type} index with size {len(state['activities'])}.")

        # MONKEY PATCH:
        # Update the UiPath.UIAutomationNext.Activities.NApplicationCard activity to the index
        activity_index = state["fullactivityid2index"].get(workflow_generation_constants.UI_APPLICATION_CARD_ACTIVITY_NAME, None)
        if activity_index is None:
            activity_index = state["fullclassname2index"].get(workflow_generation_constants.UI_APPLICATION_CARD_ACTIVITY_NAME, None)
        if activity_index is not None:
            for key, value in constants.UIA_APPLICATION_SCOPE_MONKEY_PATCH.items():
                state["activities"][activity_index][key] = value
        return state, state_info

    def get_basic_activities(self) -> list[ActivityDefinition]:
        if self._basic_activities:
            return self._basic_activities
        for name in constants.BASIC_FULL_ACTIVITY_IDS[self.activity_type]:
            basic_activity = self.get_by_full_activity_id(name)
            if basic_activity is None:
                LOGGER.warning(f"Basic activity not found in index: {name}")
                continue
            self._basic_activities.append(basic_activity)
        return self._basic_activities
