from typing import Any, Dict, List

import Levenshtein

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import ExpressionDatasetEntry, ExpressionEvaluationResult
from services.studio._text_to_workflow.expression_generation.expression_generation_endpoint import fix_expression, generate
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    ExpressionGenerationFixRequest,
    ExpressionGenerationRequest,
    SourceType,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()


class BaseExpressionEvaluator(BaseEvaluator[ExpressionDatasetEntry, ExpressionEvaluationResult]):
    evaluation_type = "BaseExpression"
    result_class = ExpressionEvaluationResult

    def get_expected_result(self, dataset: ExpressionDatasetEntry) -> str:
        return dataset.benchmarkExpression

    def get_dataset_metadata(self, dataset: ExpressionDatasetEntry) -> Dict[str, Any]:
        return {
            "id": dataset.id,
            "dataset_source": dataset.source,
            "user_prompt": dataset.userRequest,
            "prompt_verbosity": dataset.userRequestVerbosity,
        }

    def load_datasets(self) -> List[ExpressionDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    async def generate_output(self, dataset: ExpressionDatasetEntry) -> Dict[str, Any]:
        request_data = dataset.model_dump()

        # Convert dataset to ExpressionGenerationRequest
        source_str = (request_data.get("source") or "").lower()
        request_data["source"] = SourceType.BPMN if source_str == "bpmn" else SourceType.Workflow

        try:
            # Check if this is a fix expression request
            is_fix_request = "currentError" in request_data and request_data.get("currentError")

            if is_fix_request:
                request = ExpressionGenerationFixRequest(**request_data)
                response = await fix_expression(request)
            else:
                request = ExpressionGenerationRequest(**request_data)
                response = await generate(request)

            content = response["result"]
            result_dict = yaml_load(content)
            generated_expr = result_dict.get("expression", "")

            return {"generated_output": generated_expr}

        except Exception as e:
            LOGGER.error(f"Error in expression generation: {str(e)}")
            return {}

    def calculate_score(self, dataset: ExpressionDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        generated_expr = output_data.get("generated_output", "")
        benchmark_expr = dataset.benchmarkExpression

        if not generated_expr:
            return {"eval_score": 0}

        # Calculate Levenshtein ratio
        similarity_ratio = Levenshtein.ratio(generated_expr, benchmark_expr)

        # Convert to percentage
        eval_score = similarity_ratio * 100

        return {"eval_score": eval_score}

    def get_threshold(self, prompt_verbosity: str) -> float:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import ScoreThresholds

        return ScoreThresholds.EXPRESSION * 100  # Convert to percentage

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("expression_generation_model", ConsumingFeatureType.EXPRESSION_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class ExpressionGeneration(BaseExpressionEvaluator):
    evaluation_type = EvalTypes.EXPRESSION_GENERATION

    def load_datasets(self) -> List[ExpressionDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import ExpressionDataset

        return ExpressionDataset(self.evaluation_type).load_datasets()
