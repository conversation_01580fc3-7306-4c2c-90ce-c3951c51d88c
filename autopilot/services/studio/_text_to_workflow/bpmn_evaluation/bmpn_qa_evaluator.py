import pathlib
from typing import Any, Dict, List

from langchain_community.callbacks import get_openai_callback
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.prompts.chat import SystemMessagePromptTemplate

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes, ScoreThresholds
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import QaDatasetEntry, QaEvaluationResult
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import ModelType
from services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task import Tool
from services.studio._text_to_workflow.bpmn_generation.bpmn_router_task import BpmnRouterTask
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()
TASK = BpmnRouterTask()


class BaseQaEvaluator(BaseEvaluator[QaDatasetEntry, QaEvaluationResult]):
    evaluation_type = "BASE_QA"
    result_class = QaEvaluationResult

    def __init__(self) -> None:
        config_path = pathlib.Path(__file__).parent / "config"
        yml_config_path = (config_path / "prompt.yaml").absolute()
        self.config = yaml_load(yml_config_path)

    def get_expected_result(self, dataset: QaDatasetEntry) -> str:
        return dataset.expectedExplanation

    def get_dataset_metadata(self, dataset: QaDatasetEntry) -> Dict[str, Any]:
        return {
            "id": dataset.id,
            "dataset_source": dataset.source,
            "user_prompt": dataset.userRequest,
            "prompt_verbosity": dataset.userRequestVerbosity,
        }

    def load_datasets(self) -> List[QaDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    async def generate_output(self, dataset: QaDatasetEntry) -> Dict[str, Any]:
        user_prompt = dataset.userRequest
        current_bpmn = dataset.currentBPMNXml or ""

        try:
            result = await TASK.process(
                {
                    "userRequest": user_prompt,
                    "currentBpmn": current_bpmn,
                    "modelTypeOverride": {"tool": Tool.QA, "model_type": ModelType.Anthropic},
                }
            )
            output = self._get_tool_result(result, Tool.QA)
            if "error" in output:
                error_msg = output["error"]
                LOGGER.error(error_msg)
                return {"error": error_msg}

            generated_explanation = output.get("explanation", "")
            return {"generated_output": generated_explanation}

        except Exception as e:
            LOGGER.error(f"Error in Q&A generation: {str(e)}")
            return {}

    def get_score_from_llm(self, system_message: SystemMessagePromptTemplate, inputs: Dict[str, Any]) -> tuple[int, TokenUsage]:
        gateway_model = ModelManager().get_llm_model("bpmn_qa_eval_model", ConsumingFeatureType.BPMN_EVALUATION)
        chat_template = ChatPromptTemplate.from_messages([system_message])
        chat_template.format(**inputs)
        chat_chain = chat_template | gateway_model

        with get_openai_callback() as cb:
            response = chat_chain.invoke(inputs)
            raw_output = response.content if isinstance(response.content, str) else str(response.content)
            usage = TokenUsage(
                model=getattr(gateway_model, "deployment_name", ""),
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        score = raw_output.strip()
        return int(score), usage

    def calculate_score(self, dataset: QaDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        generated_explanation = output_data.get("generated_output", "")
        expected_explanation = dataset.expectedExplanation

        if not generated_explanation:
            return {"eval_score": 0, "error": "No generated explanation provided"}

        # Check if generated output contains error information
        if "error" in output_data:
            return {"eval_score": 0, "error": output_data["error"]}

        try:
            # Create prompt for getting score of explanation comparison
            prompt_template = self.config["prompt"]["qa_eval_template"]
            system_message = SystemMessagePromptTemplate.from_template(prompt_template)
            template_inputs = {"generated_explanation": generated_explanation, "expected_explanation": expected_explanation}
        except Exception as e:
            LOGGER.error(f"Error during prompt creation: {e}")
            return {"eval_score": 0, "error": f"Error during prompt creation: {str(e)}"}

        try:
            eval_score, _ = self.get_score_from_llm(system_message, template_inputs)
            if eval_score == 0:
                # If score is 0, use the generated output as the error message
                truncated_output = generated_explanation[:300] + "..." if len(generated_explanation) > 300 else generated_explanation
                error_msg = f"Q&A failed with score 0. Generated output: {truncated_output}"
                LOGGER.error(error_msg)
                # Return both error and generated_output to ensure it appears in the report
                return {
                    "eval_score": 0,
                    "error": error_msg,
                    "generated_result": generated_explanation,  # Include full generated text
                }
            return {"eval_score": eval_score}
        except Exception as e:
            error_msg = f"Error during score calculation: {str(e)}"
            LOGGER.error(error_msg)
            return {"eval_score": 0, "error": error_msg, "generated_result": generated_explanation if "generated_explanation" in locals() else ""}

    def get_threshold(self, prompt_verbosity: str) -> float:
        return ScoreThresholds.QA * 100  # Convert to percentage

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("bpmn_generation_chat_model_anthropic", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class QaEvaluator(BaseQaEvaluator):
    evaluation_type = EvalTypes.BPMN_QA

    def load_datasets(self) -> list:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import QaDataset

        return QaDataset(self.evaluation_type).load_datasets()
