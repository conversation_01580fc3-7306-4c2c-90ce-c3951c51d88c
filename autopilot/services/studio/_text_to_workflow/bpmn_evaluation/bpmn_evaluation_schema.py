from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class NodeStats(BaseModel):
    expected_count: int = 0
    generated_count: int = 0
    common_count: int = 0
    missing_count: int = 0
    extra_count: int = 0
    missing_nodes: List[str] = []
    extra_nodes: List[str] = []


class BPMNDatasetEntry(BaseModel):
    id: str
    dataset_source: Optional[str] = None
    user_prompt: str
    prompt_verbosity: str
    expectedBPMNFile: Optional[str] = None
    currentBPMNFile: Optional[str] = None
    expectedBPMN_XML: Optional[str] = None
    currentBPMN_XML: Optional[str] = None
    expectedJSONFile: Optional[str] = None


class BPMNJsonPatchDatasetEntry(BPMNDatasetEntry):
    expectedJSONPatch: Optional[Dict[str, Any]] = None


class ExpressionDatasetEntry(BaseModel):
    id: str
    userRequest: str
    userRequestVerbosity: str
    currentExpression: str
    benchmarkExpression: str
    availableVariables: Optional[List[Any]] = None
    expressionLanguage: str
    expressionTypeDefinition: Optional[str] = None
    additionalTypeDefinitions: Optional[str] = None
    source: str


class RouterDatasetEntry(BaseModel):
    id: str
    userRequest: str
    expectedTools: List[Any]
    imageExists: bool = False


class QaDatasetEntry(BaseModel):
    id: str
    userRequest: str
    userRequestVerbosity: str
    currentExplanation: str
    expectedExplanation: str
    currentBPMNFile: Optional[str] = None
    currentBPMNXml: Optional[str] = None
    source: str


class ExtensionDatasetEntry(BaseModel):
    id: str
    userRequest: str
    currentBpmn: str
    currentBPMNFile: Optional[str] = None
    expectedExtensions: Any
    expectedExtensions_file: Optional[str] = None
    mockExtensionData: Optional[Dict[str, Any]] = None
    mockExtensionData_file: Optional[str] = None


class DatasetList(BaseModel):
    datasets: List[BPMNDatasetEntry]


class BaseEvaluationResult(BaseModel):
    run_id: str
    dataset_id: str
    dataset_source: Optional[str] = None
    prompt_verbosity: Optional[str] = ""
    user_prompt: str
    eval_score: float
    threshold: float
    execution_time: float
    error: Optional[str] = None
    expected_result: Optional[str] = None
    generated_result: Optional[str] = None
    evaluation_type: str
    llm_model_details: Optional[Dict[str, Any]] = None

    def extra_columns(self) -> dict:
        return {}


class BPMNEvaluationResult(BaseEvaluationResult):
    added_elements: Optional[List[str]] = None
    removed_elements: Optional[List[str]] = None
    json_patch_data: Optional[str] = None

    def extra_columns(self) -> dict:
        # Format element lists to be more compact
        added = ", ".join(self.added_elements or []) if self.added_elements else "None"
        removed = ", ".join(self.removed_elements or []) if self.removed_elements else "None"

        return {
            "Added Elements": added,
            "Removed Elements": removed,
            "Generated BPMN XML": f'<textarea readonly style="width: 100%; height: 50px;">{self.generated_result or "None"}</textarea>',
            "Generated JSON Patch Data": f'<textarea readonly style="width: 100%; height: 50px;">{self.json_patch_data or "None"}</textarea>',
        }


class ExpressionEvaluationResult(BaseEvaluationResult):
    def extra_columns(self) -> dict:
        return {
            "Generated Expression": f'<textarea readonly style="width: 100%;">{self.generated_result or "None"}</textarea>',
            "Benchmark Expression": f'<textarea readonly style="width: 100%;">{self.expected_result or "None"}</textarea>',
        }


class QaEvaluationResult(BaseEvaluationResult):
    def extra_columns(self) -> dict:
        return {
            "Generated Explanation": f'<textarea readonly style="width: 100%;">{self.generated_result or "None"}</textarea>',
            "Benchmark Explanation": f'<textarea readonly style="width: 100%;">{self.expected_result or "None"}</textarea>',
        }


class ExtensionEvaluationResult(BaseEvaluationResult):
    extension_accuracy: Optional[float] = None
    overall_explanation: Optional[str] = None

    def extra_columns(self) -> dict:
        result = {
            "Extension Accuracy": f"{self.extension_accuracy or 0:.2f}%",
            "Overall Explanation": self.overall_explanation or "None",
            "Generated Extensions": f'<textarea readonly style="width: 100%; height: 100px;">{self.generated_result or "None"}</textarea>',
            "Expected Extensions": f'<textarea readonly style="width: 100%; height: 100px;">{self.expected_result or "None"}</textarea>',
        }

        return result


class BPMNJsonPatchEvaluationResult(BPMNEvaluationResult):
    added_elements: Optional[List[str]] = None
    updated_elements: Optional[List[str]] = None
    removed_elements: Optional[List[str]] = None
    generated_bpmn_xml: Optional[str] = None
    node_stats: Optional[NodeStats] = None

    def extra_columns(self) -> dict:
        # Format element lists to be more compact
        added = ", ".join(self.added_elements or []) if self.added_elements else "None"
        updated = ", ".join(self.updated_elements or []) if self.updated_elements else "None"
        removed = ", ".join(self.removed_elements or []) if self.removed_elements else "None"

        return {
            "Added Elements": added,
            "Updated Elements": updated,
            "Removed Elements": removed,
            "Generated JSON Patch": f'<textarea readonly style="width: 100%; height: 50px;">{self.generated_result or "None"}</textarea>',
            "Generated BPMN XML": f'<textarea readonly style="width: 100%; height: 50px;">{self.generated_bpmn_xml or "None"}</textarea>',
            "Expected BPMN XML": f'<textarea readonly style="width: 100%; height: 50px;">{self.expected_result or "None"}</textarea>',
        }


class RouterEvaluationResult(BaseEvaluationResult):
    expected_tools: List[Any] = []
    detected_tools: List[Any] = []
    router_response: Dict[str, Any] = {}

    def extra_columns(self) -> dict:
        try:
            import json

            expected_tools_str = ", ".join(str(tool) for tool in self.expected_tools) if self.expected_tools else "None"
            detected_tools_str = ", ".join(str(tool) for tool in self.detected_tools) if self.detected_tools else "None"

            # Format router response as JSON for better readability in reports
            router_response_json = json.dumps(self.router_response)

            # Create HTML textarea to better display the JSON in reports
            html_formatted_json = f'<textarea readonly style="width: 100%; height: 100px;">{router_response_json}</textarea>'

            return {
                "Expected Tools": expected_tools_str,
                "Detected Tools": detected_tools_str,
                "Router Response": html_formatted_json,
            }
        except Exception as e:
            return {"Expected Tools": "Error", "Detected Tools": "Error", "Router Response": f"Error: {str(e)}"}


class LogEntry(BaseModel):
    run_id: str
    dataset_id: str
    dataset_source: Optional[str] = None
    prompt_verbosity: str
    user_prompt: str
    eval_score: float
    threshold: float
    execution_time: float
    added_elements: Optional[List[str]] = None
    updated_elements: Optional[List[str]] = None
    removed_elements: Optional[List[str]] = None
    expected_result: Optional[str] = None
    generated_result: Optional[str] = None
    generated_bpmn_xml: Optional[str] = None
    json_patch_data: Optional[str] = None
    error: Optional[str] = None
    node_stats: Optional[NodeStats] = None


class HTMLReportSummary(BaseModel):
    reports: List[BaseEvaluationResult]
