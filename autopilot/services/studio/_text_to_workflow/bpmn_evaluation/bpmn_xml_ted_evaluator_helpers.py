import re
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Literal, Optional, Set, Tuple

from apted import APTED, Config

from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class BPMNNode:
    def __init__(self, label: str, attrib: Optional[Dict[str, str]] = None, children: Optional[List["BPMNNode"]] = None) -> None:
        self.label: str = label
        self.attrib: Dict[str, str] = attrib if attrib is not None else {}
        self.children: List[BPMNNode] = children if children is not None else []
        self.element_id: str = self.attrib.get("id", "")
        self.element_type: str = self._determine_element_type()
        self.name: str = self.attrib.get("name", "")

    def add_child(self, child: "BPMNNode") -> None:
        self.children.append(child)

    def _determine_element_type(self) -> str:
        base_label = self.label.lower()

        if any(task in base_label for task in ["task", "usertask", "servicetask", "manualtask", "scripttask", "sendtask", "receivetask"]):
            return "task"
        elif any(event in base_label for event in ["startevent", "endevent", "intermediatethrowevent", "intermediatecatchevent"]):
            return "event"
        elif any(gateway in base_label for gateway in ["exclusivegateway", "parallelgateway", "inclusivegateway", "eventbasedgateway"]):
            return "gateway"
        elif "sequenceflow" in base_label:
            return "flow"
        elif any(process in base_label for process in ["process", "subprocess", "adhocsubprocess"]):
            return "process"
        elif any(org in base_label for org in ["lane", "laneset"]):
            return "organization"
        else:
            return base_label

    def get_signature(self) -> str:
        return f"{self.element_type}:{self.name or self.element_id}"

    def __repr__(self) -> str:
        return f"BPMNNode({self.label}, children={len(self.children)})"

    def __len__(self) -> int:
        return 1 + sum(len(child) for child in self.children)

    def __getitem__(self, index: Any) -> Any:
        return self.children[index]

    def __iter__(self):
        return iter(self.children)


class BPMNConfig(Config):
    def delete(self, node: Any) -> Literal[1]:
        return 1

    def insert(self, node: Any) -> Literal[1]:
        return 1

    def rename(self, node1: BPMNNode, node2: BPMNNode) -> int:
        # If labels are identical, no cost
        if node1.label == node2.label:
            return 0

        # Check if element types differ (higher cost for type mismatch)
        if hasattr(node1, "element_type") and hasattr(node2, "element_type"):
            if node1.element_type != node2.element_type:
                return 1

        return 1 if node1.label != node2.label else 0


class BPMNXMLParser:
    @staticmethod
    def strip_namespace(tag: str) -> str:
        return tag.split("}")[-1].split(":")[-1]

    @staticmethod
    def xml_to_tree(xml_element: ET.Element) -> BPMNNode:
        tag = BPMNXMLParser.strip_namespace(xml_element.tag)
        node = BPMNNode(tag, dict(xml_element.attrib))
        for child in xml_element:
            node.add_child(BPMNXMLParser.xml_to_tree(child))
        return node

    @staticmethod
    def remove_bpmndi(bpmn_xml: str) -> str:
        try:
            # Remove all bpmndi:BPMNDiagram sections
            cleaned_xml = re.sub(r"<bpmndi:BPMNDiagram[\s\S]*?</bpmndi:BPMNDiagram>", "", bpmn_xml, flags=re.MULTILINE)
            # Remove all bpmndi:BPMNShape elements
            cleaned_xml = re.sub(r"<bpmndi:BPMNShape[\s\S]*?</bpmndi:BPMNShape>", "", cleaned_xml, flags=re.MULTILINE)
            # Remove all bpmndi:BPMNEdge elements
            cleaned_xml = re.sub(r"<bpmndi:BPMNEdge[\s\S]*?</bpmndi:BPMNEdge>", "", cleaned_xml, flags=re.MULTILINE)
            # Remove di:waypoint elements
            cleaned_xml = re.sub(r"<di:waypoint[\s\S]*?/>", "", cleaned_xml, flags=re.MULTILINE)
            # Remove any remaining self-closing bpmndi tags
            cleaned_xml = re.sub(r"<bpmndi:[^>]*?/>", "", cleaned_xml, flags=re.MULTILINE)
            # Remove dc:Bounds elements
            cleaned_xml = re.sub(r"<dc:Bounds[^>]*?>", "", cleaned_xml, flags=re.MULTILINE)
            # Remove xmlns:bpmndi attribute from definitions
            cleaned_xml = re.sub(r'xmlns:bpmndi="[^"]*"', "", cleaned_xml)
            # Remove xmlns:dc attribute from definitions
            cleaned_xml = re.sub(r'xmlns:dc="[^"]*"', "", cleaned_xml)
            # Remove xmlns:di attribute from definitions
            cleaned_xml = re.sub(r'xmlns:di="[^"]*"', "", cleaned_xml)
            # Clean up multiple spaces
            cleaned_xml = re.sub(r"\s+", " ", cleaned_xml)
            # Clean up empty tags
            cleaned_xml = re.sub(r"<([^>]*)>\s*</\1>", r"<\1/>", cleaned_xml)

            return cleaned_xml.strip()
        except Exception as e:
            LOGGER.error(f"Failed to remove bpmndi section: {e}")
            return bpmn_xml

    @staticmethod
    def parse_bpmn_xml(xml_string: str, file_name: str) -> Optional[BPMNNode]:
        try:
            root = ET.fromstring(xml_string)
            return BPMNXMLParser.xml_to_tree(root)
        except ET.ParseError as e:
            LOGGER.error(f"'{file_name}': BPMN XML Parsing Failed: {e}")
            return None

    @staticmethod
    def is_valid_bpmn_xml(xml_string: str) -> Tuple[bool, Optional[str]]:
        try:
            cleaned_xml = BPMNXMLParser.remove_bpmndi(xml_string)
            ET.fromstring(cleaned_xml)
            return True, None
        except ET.ParseError as e:
            return False, f"XML Parsing Error (Process Only): {str(e)}"


class TEDScoreCalculator:
    @staticmethod
    def compute(expected_bpmn: str, generated_bpmn: str) -> Tuple[float, List[str], List[str], Optional[str]]:
        try:
            expected_bpmn_cleaned = BPMNXMLParser.remove_bpmndi(expected_bpmn)
            generated_bpmn_cleaned = BPMNXMLParser.remove_bpmndi(generated_bpmn)

            expected_tree = TEDScoreCalculator.parse_bpmn(expected_bpmn_cleaned, "expected_bpmn")
            generated_tree = TEDScoreCalculator.parse_bpmn(generated_bpmn_cleaned, "generated_bpmn")
        except Exception as e:
            LOGGER.error(f"BPMN Parsing Failed: {e}")
            return 0.0, [], [], "XML Parsing Failed"

        if expected_tree is None or generated_tree is None:
            return 0.0, [], [], "Invalid BPMN Structure (Missing <process>)"

        total_expected = TEDScoreCalculator.count_nodes(expected_tree)
        total_generated = TEDScoreCalculator.count_nodes(generated_tree)
        max_nodes = max(total_expected, total_generated)

        apted = APTED(expected_tree, generated_tree, BPMNConfig())
        ted_distance = apted.compute_edit_distance()

        # Calculate percentage score (0-100)
        ted_score = 100.0 if max_nodes == 0 else round((1 - (ted_distance / max_nodes)) * 100, 2)
        ted_score = max(0, min(ted_score, 100))

        # Extract element differences
        added, removed = TEDScoreCalculator.extract_changes(expected_bpmn_cleaned, generated_bpmn_cleaned)

        xml_error = None

        return ted_score, added, removed, xml_error

    @staticmethod
    def parse_bpmn(bpmn_xml: str, label: str) -> Optional[BPMNNode]:
        cleaned_xml = BPMNXMLParser.remove_bpmndi(bpmn_xml)
        tree = BPMNXMLParser.parse_bpmn_xml(cleaned_xml, label)
        if tree is None:
            LOGGER.error(f"'{label}': Could not extract valid <process> structure from BPMN XML.")
            return None
        return TEDScoreCalculator.normalize_tree(tree)

    @staticmethod
    def normalize_tree(tree: BPMNNode) -> Optional[BPMNNode]:
        if tree is None:
            return None

        ignored_labels = {"documentation", "extensionElements", "bpmndi", "BPMNDiagram", "BPMNPlane", "dc", "di"}

        def traverse(node: BPMNNode) -> Optional[BPMNNode]:
            if node is None or node.label.lower() in ignored_labels:
                return None

            normalized_label = node.label.lower().replace(" ", "_")
            new_node = BPMNNode(label=normalized_label, attrib=node.attrib, children=[])
            children = filter(None, (traverse(child) for child in node.children))
            sorted_children = sorted(children, key=lambda n: n.label)
            for child in sorted_children:
                new_node.add_child(child)
            return new_node

        return traverse(tree)

    @staticmethod
    def create_clean_node(node: BPMNNode, label: Optional[str] = None) -> BPMNNode:
        new_label = label or node.label.lower().replace(" ", "_")
        return BPMNNode(label=new_label, attrib=node.attrib if hasattr(node, "attrib") else {})

    @staticmethod
    def extract_changes(expected_bpmn: str, generated_bpmn: str) -> Tuple[List[str], List[str]]:
        expected_ids = TEDScoreCalculator.get_all_node_ids(expected_bpmn)
        generated_ids = TEDScoreCalculator.get_all_node_ids(generated_bpmn)

        if not expected_ids and not generated_ids:
            LOGGER.error("No relevant BPMN elements found. Check XML parsing.")
            return [], []

        added = list(generated_ids - expected_ids)
        removed = list(expected_ids - generated_ids)

        if removed:
            LOGGER.info(f"Missing elements detected: {removed}")

        return added, removed

    @staticmethod
    def get_all_node_ids(bpmn_xml: str) -> Set[str]:
        node_ids: Set[str] = set()
        try:
            cleaned_xml = BPMNXMLParser.remove_bpmndi(bpmn_xml)
            root = ET.fromstring(cleaned_xml)

            namespaces = {"bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL"}

            valid_elements = {
                "userTask",
                "serviceTask",
                "scriptTask",
                "businessRuleTask",
                "sendTask",
                "receiveTask",
                "manualTask",
                "task",
                "startEvent",
                "endEvent",
                "intermediateThrowEvent",
                "intermediateCatchEvent",
                "boundaryEvent",
                "exclusiveGateway",
                "inclusiveGateway",
                "parallelGateway",
                "eventBasedGateway",
                "sequenceFlow",
                "messageFlow",
                "process",
                "participant",
                "lane",
                "collaboration",
                "textAnnotation",
                "group",
                "association",
                "dataObjectReference",
                "dataStoreReference",
                "conditionExpression",
            }

            flow_map = {}
            flow_counter = 1

            for elem in root.findall(".//*", namespaces):
                tag = elem.tag.split("}")[-1]

                # Skip any visualization-related elements
                if "bpmndi:" in tag or "di:" in tag or "dc:" in tag:
                    continue

                if tag in valid_elements:
                    elem_id = elem.get("id")
                    if tag == "sequenceFlow" and elem_id:
                        if elem_id not in flow_map:
                            flow_map[elem_id] = f"flow-{flow_counter}"
                            flow_counter += 1
                        elem_id = flow_map[elem_id]
                    if elem_id:
                        node_ids.add(elem_id)
        except ET.ParseError as e:
            LOGGER.error(f"XML Parsing Failed: {e}")

        LOGGER.info(f"Extracted BPMN Element IDs: {len(node_ids)} elements")
        return node_ids

    @staticmethod
    def count_nodes(tree: BPMNNode) -> int:
        return 1 + sum(TEDScoreCalculator.count_nodes(child) for child in tree.children) if tree else 0
