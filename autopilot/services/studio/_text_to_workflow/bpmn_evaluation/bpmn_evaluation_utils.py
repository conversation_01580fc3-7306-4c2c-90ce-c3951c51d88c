import json
from enum import Enum

from pydantic import BaseModel


def enum_serializer(obj):
    if isinstance(obj, Enum):
        return obj.value
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")


class ModelEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, BaseModel):
            return o.model_dump()
        if isinstance(o, Enum):
            return o.value
        try:
            return super().default(o)
        except TypeError:
            return str(o)
