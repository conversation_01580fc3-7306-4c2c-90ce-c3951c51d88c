import asyncio
import os
import time
from typing import Optional

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_report import EvaluationReporter
from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class EvaluationTask:
    def __init__(self, run_id: str, max_concurrent_tasks: Optional[int] = None):
        self.run_id = run_id
        self.evaluators = []

        self.max_concurrent_tasks = 5
        LOGGER.info("Initializing evaluation task with fixed max_concurrent_tasks=5")

    def register_evaluator(self, evaluator) -> None:
        self.evaluators.append(evaluator)

    @telemetry_utils.log_execution_time("bpmn_evaluation_task.run")
    async def run(self) -> None:
        start_time = time.perf_counter()
        tasks = []

        semaphore = asyncio.Semaphore(value=5)

        # Set environment variable to ensure connection pooling
        os.environ["USE_CACHED_CONNECTIONS"] = "true"

        async def evaluate_with_semaphore(evaluator, dataset):
            async with semaphore:
                try:
                    LOGGER.info(f"[START] ({evaluator.evaluation_type}) Evaluating dataset: {dataset.id}")
                    result = await evaluator.evaluate(dataset, self.run_id)

                    # Log more details for failed evaluations
                    if result.eval_score == 0:
                        LOGGER.error(f"[FAILED] Dataset: {dataset.id} failed with score 0")
                        if result.error:
                            LOGGER.error(f"[ERROR] {result.error}")

                        # Additional debugging for BPMN XML issues
                        if hasattr(result, "generated_bpmn_xml") and hasattr(result, "expected_bpmn_xml"):
                            generated_xml = result.generated_bpmn_xml or ""
                            expected_xml = result.expected_bpmn_xml or ""

                            if not generated_xml:
                                LOGGER.error("[XML] Generated XML is empty")
                            elif "<bpmn:process" not in generated_xml:
                                LOGGER.error("[XML] Generated XML missing process element")

                            LOGGER.info(f"[XML] Expected size: {len(expected_xml)}, Generated size: {len(generated_xml)}")

                    return result
                except Exception as e:
                    LOGGER.error(f"[ERROR] Exception in evaluator {evaluator.evaluation_type}/{dataset.id}: {str(e)}")
                    # Create a result object with error information
                    metadata = evaluator.get_dataset_metadata(dataset)
                    return evaluator.result_class(
                        run_id=self.run_id,
                        dataset_id=metadata["id"],
                        dataset_source=metadata.get("dataset_source", "Unknown"),
                        prompt_verbosity=metadata.get("prompt_verbosity", "Unknown"),
                        user_prompt=metadata.get("user_prompt", ""),
                        eval_score=0,
                        threshold=evaluator.get_threshold(metadata.get("prompt_verbosity", "")),
                        execution_time=0,
                        error=f"Exception: {str(e)}",
                        expected_result=evaluator.get_expected_result(dataset),
                        generated_result=None,
                        evaluation_type=evaluator.evaluation_type,
                    )

        # Iterate through all registered evaluators and their datasets
        for evaluator in self.evaluators:
            datasets = evaluator.load_datasets()
            for dataset in datasets:
                tasks.append(evaluate_with_semaphore(evaluator, dataset))

        all_results = await asyncio.gather(*tasks)
        total_elapsed_time_ms = int((time.perf_counter() - start_time) * 1000)
        reporter = EvaluationReporter(all_results, total_elapsed_time_ms, self.run_id)
        reporter.generate_report()
        LOGGER.info("Evaluation complete. Report saved.")
