{"folders": [{"name": "autopilot", "path": "../../../.."}], "launch": {"configurations": [{"name": "BPMN Evaluation - Run All (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - Router (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNRouter"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - JSON Edits (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNJSONEdits"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - JSON Generation (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNJSONGeneration"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - Expression (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "ExpressionGeneration"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - QA (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNQA"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - Extensions (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNExtensions"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - XML Generation (LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-llm-gateway", "--evaluator", "BPMNGeneration"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "true"}}, {"name": "BPMN Evaluation - Run All (without LLM Gateway)", "type": "debugpy", "request": "launch", "module": "services.studio._text_to_workflow.bpmn_evaluation.main", "args": ["--run_id", "local-run-no-llm-gateway"], "justMyCode": true, "env": {"USE_LLM_GATEWAY": "false"}, "envFile": "${workspaceFolder}/.env"}]}}