# bpmn_evaluation_base.py
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Sequence, Type, TypeVar

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_logger import save_log
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import BaseEvaluationResult
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import BpmnAssistantResponse, BpmnGenerationToolResult, Tool, ToolResult
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

T = TypeVar("T")
R = TypeVar("R", bound=BaseEvaluationResult)

LOGGER = telemetry_utils.AppInsightsLogger()


class BaseEvaluator(Generic[T, R], ABC):
    evaluation_type: str = "Base"
    result_class: Type[R]

    @abstractmethod
    def load_datasets(self) -> Sequence[T]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    @abstractmethod
    async def generate_output(self, dataset: T) -> Dict[str, Any]:
        raise NotImplementedError("Subclasses must implement generate_output()")

    @abstractmethod
    def calculate_score(self, dataset: T, output_data: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError("Subclasses must implement calculate_score()")

    def get_expected_result(self, dataset: T) -> str:
        return ""

    def get_dataset_metadata(self, dataset: T) -> Dict[str, Any]:
        # Try various attribute naming patterns based on the dataset type
        user_prompt = getattr(dataset, "user_prompt", getattr(dataset, "userRequest", ""))

        # Basic metadata that should always be included
        metadata = {
            "id": getattr(dataset, "id", "unknown"),
            "user_prompt": user_prompt,
        }

        dataset_source = getattr(dataset, "dataset_source", None)
        if dataset_source is None:
            dataset_source = getattr(dataset, "source", None)

        if dataset_source is not None:
            metadata["dataset_source"] = dataset_source

        # Only add prompt_verbosity if it exists in the dataset
        verbosity = getattr(dataset, "prompt_verbosity", None)
        if verbosity is None:
            verbosity = getattr(dataset, "userRequestVerbosity", None)

        if verbosity is not None:
            metadata["prompt_verbosity"] = verbosity

        return metadata

    async def evaluate(self, dataset: T, run_id: str) -> R:
        start_time = time.perf_counter()
        metadata = self.get_dataset_metadata(dataset)
        dataset_id = metadata["id"]
        dataset_source = metadata.get("dataset_source", "Unknown")
        user_prompt = metadata.get("user_prompt", "")
        prompt_verbosity = metadata.get("prompt_verbosity", "")

        threshold = self.get_threshold(prompt_verbosity)
        error_message = None
        output_data = {}
        score_data = {}

        try:
            # Generate output
            output_data = await self.generate_output(dataset)

            if output_data:
                # Check if output_data already contains an error - if so, use it
                if "error" in output_data and output_data["error"]:
                    error_message = output_data["error"]
                    score_data = {"eval_score": 0}
                else:
                    score_data = self.calculate_score(dataset, output_data)
                    # Check if score_data contains an error
                    if "error" in score_data and score_data["error"]:
                        error_message = score_data["error"]
            else:
                error_message = "Failed to generate output"
                score_data = {"eval_score": 0}

        except Exception as e:
            error_message = f"Error during evaluation: {str(e)}"
            LOGGER.error(f"Evaluation error for {dataset_id}: {error_message}")
            score_data = {"eval_score": 0}

        # Calculate execution time
        execution_time_ms = int((time.perf_counter() - start_time) * 1000)

        # If eval_score is 0 and no error message is set, add a default error message
        if score_data.get("eval_score", 0) == 0 and not error_message:
            error_message = "Evaluation failed with score 0. No specific error was identified."
            LOGGER.error(f"Adding default error message for {dataset_id} with score 0: {error_message}")

        # Build common result data
        result_data = {
            "run_id": run_id,
            "dataset_id": dataset_id,
            "user_prompt": user_prompt,
            "eval_score": score_data.get("eval_score", 0),
            "threshold": threshold,
            "execution_time": execution_time_ms,
            "error": error_message,
            "evaluation_type": self.evaluation_type,
            "expected_result": self.get_expected_result(dataset),
            "llm_model_details": self.get_llm_model_details(),
        }

        # Only add dataset_source if it exists
        if dataset_source and dataset_source != "Unknown":
            result_data["dataset_source"] = dataset_source

        # Add prompt_verbosity if it exists
        if prompt_verbosity:
            result_data["prompt_verbosity"] = prompt_verbosity

        # Add generated result if available
        if "generated_output" in output_data:
            result_data["generated_result"] = output_data["generated_output"]

        # Add any additional data from score_data and output_data
        for key, value in {**output_data, **score_data}.items():
            if key not in result_data and key not in ["eval_score", "generated_output"]:
                result_data[key] = value

        result = self.result_class(**result_data)

        LOGGER.info(f"{self.evaluation_type} - {dataset_id}: {score_data.get('eval_score', 0)}% (Threshold: {threshold}%)")
        if error_message:
            LOGGER.info(f"Error: {error_message}")
        LOGGER.info(f"Finished {dataset_id} in {execution_time_ms}ms")

        save_log(result.model_dump())
        return result

    def get_threshold(self, prompt_verbosity: str) -> float:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import ScoreThresholds

        if hasattr(ScoreThresholds, self.evaluation_type):
            threshold = getattr(ScoreThresholds, self.evaluation_type)

            if isinstance(threshold, dict) and prompt_verbosity in threshold:
                return threshold[prompt_verbosity]
            elif not isinstance(threshold, dict):
                return threshold * 100

        return ScoreThresholds.DEFAULT

    def _get_tool_result(self, result: BpmnAssistantResponse, tool_name: Tool) -> ToolResult | Dict[str, Any]:
        if not result.get("valid", False):
            error_msg = result.get("explanation") or "No results returned from the tool."
            LOGGER.error(error_msg)
            return {"error": error_msg, "tool": tool_name}

        tool_results: List[ToolResult | BpmnGenerationToolResult] = result.get("results") or []
        for tool_result in tool_results:
            if tool_result["tool"] == tool_name:
                return tool_result

        error_msg = f"{str(tool_results)}"
        LOGGER.error(error_msg)
        return {"error": error_msg, "tool": tool_name}

    def get_result_object(self, dataset: T, output_data: Dict[str, Any], score_data: Dict[str, Any], execution_time: float, run_id: str) -> R:
        dataset_metadata = self.get_dataset_metadata(dataset)
        dataset_id = dataset_metadata.get("id", "unknown")
        dataset_source = dataset_metadata.get("dataset_source", None)
        user_prompt = dataset_metadata.get("user_prompt", "")

        # Get prompt_verbosity if it exists, otherwise use empty string
        prompt_verbosity = dataset_metadata.get("prompt_verbosity", "")

        error_message = score_data.get("error")

        threshold = self.get_threshold(prompt_verbosity)

        # Calculate execution time
        execution_time_ms = int(execution_time * 1000)

        # If eval_score is 0 and no error message is set, add a default error message
        if score_data.get("eval_score", 0) == 0 and not error_message:
            error_message = "Evaluation failed with score 0. No specific error was identified."
            LOGGER.error(f"Adding default error message for {dataset_id} with score 0: {error_message}")

        # Build common result data
        result_data = {
            "run_id": run_id,
            "dataset_id": dataset_id,
            "user_prompt": user_prompt,
            "eval_score": score_data.get("eval_score", 0),
            "threshold": threshold,
            "execution_time": execution_time_ms,
            "error": error_message,
            "evaluation_type": self.evaluation_type,
            "expected_result": self.get_expected_result(dataset),
            "llm_model_details": self.get_llm_model_details(),
        }

        if dataset_source:
            result_data["dataset_source"] = dataset_source

        if prompt_verbosity:
            result_data["prompt_verbosity"] = prompt_verbosity

        if "generated_output" in output_data:
            result_data["generated_result"] = output_data["generated_output"]

        for key, value in {**output_data, **score_data}.items():
            if key not in result_data and key not in ["eval_score", "generated_output"]:
                result_data[key] = value

        result = self.result_class(**result_data)

        LOGGER.info(f"{self.evaluation_type} - {dataset_id}: {score_data.get('eval_score', 0)}% (Threshold: {threshold}%)")
        if error_message:
            LOGGER.info(f"Error: {error_message}")
        LOGGER.info(f"Finished {dataset_id} in {execution_time_ms}ms")

        save_log(result.model_dump())
        return result

    def get_llm_model_details(self) -> Dict[str, Any]:
        """Return the model details for the evaluator. Should be overridden by subclasses that use different models."""
        model = ModelManager().get_llm_model("bpmn_generation_chat_model_openai", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }
