name: "v$(Date:yyyy.MM.dd)_$(Rev:rr)"

# Enable manual triggers
schedules:
  - cron: "0 0 * * *"
    displayName: Daily midnight run
    branches:
      include:
        - master
    always: true

# Pipeline parameters
parameters:
  - name: datasetBranch
    type: string
    default: 'main'
    
  - name: runId
    type: string
    default: 'pipeline-run-$(Build.BuildNumber)'

# Automatic triggers for specific paths
trigger:
  branches:
    include:
      - master

  paths:
    include:
      - "autopilot/services/studio/_text_to_workflow/bpmn_evaluation/**/*"
      - "autopilot/services/studio/_text_to_workflow/bpmn_generation/**/*"

# PR configuration - paths are used for auto-triggers but manual triggers are allowed
pr:
  autoCancel: true
  branches:
    include:
      - master
      - refs/pull/*/merge  # Allow PR triggers

  paths:
    include:
      - "autopilot/services/studio/_text_to_workflow/bpmn_evaluation/**/*"
      - "autopilot/services/studio/_text_to_workflow/bpmn_generation/**/*"

resources:
  repositories:
    - repository: "Autopilot.Samples"
      type: github
      endpoint: UiPath
      name: UiPath/Autopilot.Samples
      ref: refs/heads/${{ parameters.datasetBranch }}

variables:
  - template: ./variables/common.yaml
  - name: runId
    value: ${{ parameters.runId }}
  - name: cudaVisibleDevices
    value: ""  # Empty string disables CUDA
  - name: useCPU
    value: "true"  # Force CPU usage

stages:
  - stage: BPMN_Evaluation
    displayName: "Run BPMN Evaluation"
    jobs:
      - template: ./templates/bpmn-evaluation-ci-job.yaml
