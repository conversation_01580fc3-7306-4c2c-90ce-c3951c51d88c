variables:
  poolName: "cm-mls-1t4-gpu-8a-cd"
  workdir: "/workspace/data"
  cudaVisibleDevices: "all"
  keyVaultName: "ml-dev-gptproxy-kv"
  openapiType: $(gpt-api-type)
  openapiEndpoint: $(AZURE-OPENAI-API-ENDPOINT)
  openapiVersion: $(AZURE-OPENAI-API-VERSION)
  openapiKey: $(AZURE-OPENAI-API-KEY)
  usellmGateway: "true"
  appInsightsInstrumentationKey: "$(APP-INSIGHTS-KEY)"
  appInsightsConnectionString: "$(APP-INSIGHTS-CONNECTION-STRING)"
  uipathClientId: "$(uipath-client-id)"
  uipathClientSecret: "$(uipath-client-secret)"
  uipathTenantId: "ff481ed9-5261-4d0b-9f4a-8c60f58d379e"
