import json
import os
from datetime import datetime, timezone
from typing import Any, Dict, List

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import LOGS_DIR
from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


def reset_logs(logs_dir: str = LOGS_DIR) -> None:
    log_file = os.path.join(logs_dir, "bpmn_evaluation_logs.json")
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            LOGGER.info(f"Reset logs: {log_file}")
        except Exception as e:
            LOGGER.error(f"Failed to reset logs: {str(e)}")


def save_log(log_data: Dict[str, Any], logs_dir: str = LOGS_DIR) -> None:
    log_file = os.path.join(logs_dir, "bpmn_evaluation_logs.json")
    os.makedirs(logs_dir, exist_ok=True)
    logs = _load_existing_logs(log_file)

    if "llm_model_details" in log_data:
        log_data["llm_model_used"] = str(log_data["llm_model_details"])
    else:
        log_data["llm_model_used"] = "{}"

    log_data["execution_time"] = f"{log_data.get('execution_time')} ms"
    log_data["timestamp"] = datetime.now(timezone.utc).isoformat()

    logs.append(log_data)
    _write_logs(log_file, logs)
    LOGGER.info(f"Log saved: {log_file}")


def _load_existing_logs(log_file: str) -> List[Any]:
    if os.path.exists(log_file):
        try:
            with open(log_file, "r", encoding="utf-8") as f:
                logs = json.load(f)
            return logs if isinstance(logs, list) else []
        except json.JSONDecodeError:
            return []
    return []


def _write_logs(log_file: str, logs: List[Any]) -> None:
    with open(log_file, "w", encoding="utf-8") as f:
        json.dump(logs, f, indent=4)
