import io
import uuid
import xml.etree.ElementTree as ET
from typing import Dict, List, Set

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import BPMN_NAMESPACES
from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class BPMNPatchApplier:
    def __init__(self):
        self.namespaces = BPMN_NAMESPACES

        for prefix, uri in self.namespaces.items():
            ET.register_namespace(prefix, uri)

        self.added_elements: Set[str] = set()
        self.updated_elements: Set[str] = set()
        self.deleted_elements: Set[str] = set()

    def apply_patch(self, patch: Dict, current_xml: str = "") -> str:
        try:
            self.added_elements = set()
            self.updated_elements = set()
            self.deleted_elements = set()

            if not current_xml:
                return self._create_minimal_xml()

            root = ET.fromstring(current_xml)

            namespaces = self._extract_namespaces(current_xml)
            for prefix, uri in namespaces.items():
                ET.register_namespace(prefix, uri)
                if prefix not in self.namespaces:
                    self.namespaces[prefix] = uri

            process = root.find(f".//{{{self.namespaces['bpmn']}}}process")
            if process is None:
                LOGGER.error("No process element found in BPMN XML")
                return self._create_minimal_xml()

            elements_by_id = self._index_elements_by_id(root)

            self._apply_deletions(patch.get("delete", []), elements_by_id, process)
            self._apply_updates(patch.get("update", []), elements_by_id)
            self._apply_additions(patch.get("add", []), process, elements_by_id)

            self._update_sequence_flow_references(process, elements_by_id)

            self._format_xml(root)

            xml_string = ET.tostring(root, encoding="utf-8", xml_declaration=True).decode("utf-8")

            if self.added_elements:
                LOGGER.info(f"Added {len(self.added_elements)} BPMN elements")
            if self.updated_elements:
                LOGGER.info(f"Updated {len(self.updated_elements)} BPMN elements")
            if self.deleted_elements:
                LOGGER.info(f"Deleted {len(self.deleted_elements)} BPMN elements")

            return xml_string

        except Exception as e:
            import traceback

            LOGGER.error(f"Error applying BPMN patch: {e}")
            LOGGER.error(traceback.format_exc())

            if current_xml:
                return current_xml
            else:
                return self._create_minimal_xml()

    def _extract_namespaces(self, xml_string: str) -> Dict[str, str]:
        namespaces = {}
        try:
            for _, elem in ET.iterparse(io.StringIO(xml_string), events=["start-ns"]):
                prefix, uri = elem
                namespaces[prefix] = uri
        except Exception as e:
            LOGGER.error(f"Error extracting namespaces: {e}")

        for prefix, uri in self.namespaces.items():
            if prefix not in namespaces:
                namespaces[prefix] = uri

        return namespaces

    def _index_elements_by_id(self, root: ET.Element) -> Dict[str, ET.Element]:
        elements = {}
        for elem in root.findall(".//*[@id]"):
            elem_id = elem.get("id")
            if elem_id:
                elements[elem_id] = elem
        return elements

    def _apply_additions(self, additions: List[Dict], process: ET.Element, elements_by_id: Dict[str, ET.Element]) -> None:
        for item in additions:
            self._add_element(process, item, elements_by_id)

    def _apply_updates(self, updates: List[Dict], elements_by_id: Dict[str, ET.Element]) -> None:
        for item in updates:
            self._update_element(item, elements_by_id)

    def _apply_deletions(self, deletions: List[Dict], elements_by_id: Dict[str, ET.Element], process: ET.Element) -> None:
        for item in deletions:
            self._delete_element(item, elements_by_id, process)

    def _add_element(self, process: ET.Element, item: Dict, elements_by_id: Dict[str, ET.Element]) -> None:
        item_id = item.get("id")
        item_type = item.get("type", "")

        if not item_id or not item_type:
            LOGGER.error(f"Missing required fields for add operation: {item}")
            return

        if item_id in elements_by_id:
            LOGGER.warning(f"Element with ID {item_id} already exists, skipping add")
            return

        parent_id = item.get("parentId")
        parent = process

        if parent_id and parent_id in elements_by_id:
            parent = elements_by_id[parent_id]

        if ":" not in item_type:
            item_type = f"bpmn:{item_type}"

        prefix, tag = item_type.split(":")
        namespace = self.namespaces.get(prefix, self.namespaces["bpmn"])

        new_element = ET.SubElement(parent, f"{{{namespace}}}{tag}")
        new_element.set("id", item_id)

        if "name" in item:
            new_element.set("name", item["name"])

        if "data" in item and isinstance(item["data"], dict):
            self._add_element_data(new_element, item["data"], namespace)

        if "source" in item and "target" in item:
            self._add_flow_attributes(new_element, item, namespace)

        elements_by_id[item_id] = new_element
        self.added_elements.add(item_id)

    def _add_element_data(self, element: ET.Element, data: Dict, namespace: str) -> None:
        doc_text = data.get("documentation") or data.get("label")
        if doc_text:
            doc_elem = ET.SubElement(element, f"{{{namespace}}}documentation")
            doc_elem.text = doc_text

        if "eventDefinition" in data:
            self._add_event_definition(element, data["eventDefinition"])

        if "attachedToRef" in data:
            element.set("attachedToRef", data["attachedToRef"])

        if "cancelActivity" in data:
            element.set("cancelActivity", str(data["cancelActivity"]).lower())

        if "gatewayDirection" in data:
            element.set("gatewayDirection", data["gatewayDirection"])

        for task_attr in ["implementation", "instantiate"]:
            if task_attr in data:
                element.set(task_attr, str(data[task_attr]))

    def _add_flow_attributes(self, flow_element: ET.Element, item: Dict, namespace: str) -> None:
        flow_element.set("sourceRef", item["source"])
        flow_element.set("targetRef", item["target"])

        if "data" in item and "conditionExpression" in item["data"]:
            expr = item["data"]["conditionExpression"]
            expr_elem = ET.SubElement(flow_element, f"{{{namespace}}}conditionExpression")
            expr_elem.text = expr

    def _add_event_definition(self, event_element: ET.Element, event_def: Dict) -> None:
        event_type = event_def.get("type", "")
        if not event_type:
            LOGGER.warning("Event definition missing type")
            return

        if ":" not in event_type:
            event_type = f"bpmn:{event_type}"

        prefix, tag = event_type.split(":")
        namespace = self.namespaces.get(prefix, self.namespaces["bpmn"])

        def_element = ET.SubElement(event_element, f"{{{namespace}}}{tag}")

        if "id" in event_def:
            def_element.set("id", event_def["id"])

        for timer_type in ["timeDate", "timeDuration", "timeCycle"]:
            if timer_type in event_def:
                timer_elem = ET.SubElement(def_element, f"{{{namespace}}}{timer_type}")
                timer_elem.text = event_def[timer_type]

        for ref_type in ["messageRef", "errorRef", "signalRef"]:
            if ref_type in event_def:
                def_element.set(ref_type, event_def[ref_type])

    def _update_element(self, item: Dict, elements_by_id: Dict[str, ET.Element]) -> None:
        item_id = item.get("id")

        if not item_id:
            LOGGER.error(f"Missing ID for update operation: {item}")
            return

        if item_id not in elements_by_id:
            LOGGER.warning(f"Element with ID {item_id} not found for update")
            return

        element = elements_by_id[item_id]

        if "name" in item:
            element.set("name", item["name"])

        if "data" in item and isinstance(item["data"], dict):
            self._update_element_data(element, item["data"])

        self._update_flow_attributes(element, item)

        self.updated_elements.add(item_id)

    def _update_element_data(self, element: ET.Element, data: Dict) -> None:
        doc_text = data.get("documentation") or data.get("label")
        if doc_text:
            doc_elem = element.find(f".//{{{self.namespaces['bpmn']}}}documentation")
            if doc_elem is None:
                doc_elem = ET.SubElement(element, f"{{{self.namespaces['bpmn']}}}documentation")
            doc_elem.text = doc_text

        if "eventDefinition" in data:
            for def_elem in element.findall(".//*[contains(local-name(), 'EventDefinition')]"):
                element.remove(def_elem)

            self._add_event_definition(element, data["eventDefinition"])

        if "attachedToRef" in data:
            element.set("attachedToRef", data["attachedToRef"])

        if "cancelActivity" in data:
            element.set("cancelActivity", str(data["cancelActivity"]).lower())

        if "gatewayDirection" in data:
            element.set("gatewayDirection", data["gatewayDirection"])

        for task_attr in ["implementation", "instantiate"]:
            if task_attr in data:
                element.set(task_attr, str(data[task_attr]))

    def _update_flow_attributes(self, element: ET.Element, item: Dict) -> None:
        if "source" in item:
            element.set("sourceRef", item["source"])

        if "target" in item:
            element.set("targetRef", item["target"])

        if "data" in item and "conditionExpression" in item["data"]:
            expr = item["data"]["conditionExpression"]
            expr_elem = element.find(f".//{{{self.namespaces['bpmn']}}}conditionExpression")

            if expr_elem is None:
                expr_elem = ET.SubElement(element, f"{{{self.namespaces['bpmn']}}}conditionExpression")

            expr_elem.text = expr

    def _delete_element(self, item: Dict, elements_by_id: Dict[str, ET.Element], process: ET.Element) -> None:
        item_id = item.get("id")

        if not item_id:
            LOGGER.error(f"Missing ID for delete operation: {item}")
            return

        if item_id not in elements_by_id:
            LOGGER.warning(f"Element with ID {item_id} not found for deletion")
            return

        element = elements_by_id[item_id]

        parent_map = {c: p for p in process.iter() for c in p}

        if element in parent_map:
            parent = parent_map[element]
            parent.remove(element)

            del elements_by_id[item_id]
            self.deleted_elements.add(item_id)

            if element.tag.endswith("}laneSet"):
                self._handle_lane_set_deletion(element, elements_by_id, parent_map)

    def _handle_lane_set_deletion(self, lane_set_element: ET.Element, elements_by_id: Dict[str, ET.Element], parent_map: Dict[ET.Element, ET.Element]) -> None:
        for lane in list(elements_by_id.values()):
            if lane.tag.endswith("}lane"):
                lane_id = lane.get("id")
                if lane_id and lane in parent_map:
                    lane_parent = parent_map[lane]
                    if lane_parent == lane_set_element:
                        if lane_id in elements_by_id:
                            del elements_by_id[lane_id]
                            self.deleted_elements.add(lane_id)

    def _update_sequence_flow_references(self, process: ET.Element, elements_by_id: Dict[str, ET.Element]) -> None:
        flows = {}
        for flow in process.findall(f".//{{{self.namespaces['bpmn']}}}sequenceFlow"):
            flow_id = flow.get("id")
            source = flow.get("sourceRef")
            target = flow.get("targetRef")

            if flow_id and source and target:
                flows[flow_id] = (source, target)

        for _, elem in elements_by_id.items():
            if elem.tag.endswith("}sequenceFlow"):
                continue

            for ref in list(elem.findall(f"{{{self.namespaces['bpmn']}}}outgoing")):
                try:
                    elem.remove(ref)
                except ValueError as e:
                    LOGGER.warning(f"Could not remove outgoing reference: {e}")

            for ref in list(elem.findall(f"{{{self.namespaces['bpmn']}}}incoming")):
                try:
                    elem.remove(ref)
                except ValueError as e:
                    LOGGER.warning(f"Could not remove incoming reference: {e}")

        for flow_id, (source_id, target_id) in flows.items():
            if source_id in elements_by_id:
                source_elem = elements_by_id[source_id]
                outgoing = ET.SubElement(source_elem, f"{{{self.namespaces['bpmn']}}}outgoing")
                outgoing.text = flow_id

            if target_id in elements_by_id:
                target_elem = elements_by_id[target_id]
                incoming = ET.SubElement(target_elem, f"{{{self.namespaces['bpmn']}}}incoming")
                incoming.text = flow_id

    def _format_xml(self, root: ET.Element) -> None:
        try:
            ET.indent(ET.ElementTree(root), space="  ")
        except (AttributeError, TypeError):
            pass

    def _create_minimal_xml(self) -> str:
        root = ET.Element(f"{{{self.namespaces['bpmn']}}}definitions")
        root.set("id", f"Definitions_{uuid.uuid4().hex[:8]}")
        root.set("targetNamespace", self.namespaces["bpmn"])
        root.set("exporter", "BPMN Patch Applier")
        root.set("exporterVersion", "1.0")

        for prefix, uri in self.namespaces.items():
            if prefix != "bpmn":
                root.set(f"xmlns:{prefix}", uri)

        process = ET.SubElement(root, f"{{{self.namespaces['bpmn']}}}process")
        process.set("id", f"Process_{uuid.uuid4().hex[:8]}")
        process.set("isExecutable", "true")

        self._format_xml(root)

        return ET.tostring(root, encoding="utf-8", xml_declaration=True).decode("utf-8")


def apply_bpmn_patch(patch: Dict, current_xml: str = "") -> str:
    applier = BPMNPatchApplier()
    return applier.apply_patch(patch, current_xml)
