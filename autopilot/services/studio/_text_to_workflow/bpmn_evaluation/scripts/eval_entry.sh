#!/bin/bash

# Set RUN_ID if not provided
if [ -z "$RUN_ID" ]; then
  export RUN_ID=$(uuidgen)
fi

echo "Using RUN_ID: $RUN_ID"

# Set environment variables for high concurrency
export USE_CACHED_CONNECTIONS=true

# Configure Python to handle more open files/connections
export PYTHONASYNCIODEBUG=0
ulimit -n 4096 2>/dev/null || echo "Warning: Could not increase file descriptor limit"

# Change directory to the repository subfolder
cd "${BUILD_SOURCESDIRECTORY}/ml/autopilot" || { echo "Failed to cd to ${BUILD_SOURCESDIRECTORY}/ml/autopilot"; exit 1; }

export PYTHONPATH=$(pwd)

any_failed=0

# Use Azure DevOps' default artifact directory
RESULTS_DIR="${BUILD_ARTIFACTSTAGINGDIRECTORY}/bpmn_eval_results"
mkdir -p "$RESULTS_DIR"

# Ensure evaluation directories exist
echo "Creating required directories..."
mkdir -p "./services/studio/_text_to_workflow/bpmn_evaluation/logs"
mkdir -p "./services/studio/_text_to_workflow/bpmn_evaluation/results"
mkdir -p "./services/studio/_text_to_workflow/bpmn_evaluation/config"

# Ensure the schema file exists - copy if needed
if [ ! -f "./services/studio/_text_to_workflow/bpmn_evaluation/config/bpmn_schema.json" ]; then
  echo "Copying bpmn_schema.json from generation to evaluation..."
  if [ -f "./services/studio/_text_to_workflow/bpmn_generation/config/bpmn_schema.json" ]; then
    cp "./services/studio/_text_to_workflow/bpmn_generation/config/bpmn_schema.json" \
       "./services/studio/_text_to_workflow/bpmn_evaluation/config/"
    echo "Schema file copied successfully."
  else
    echo "Warning: bpmn_schema.json not found in generation config"
  fi
fi

# Log the Run ID and configuration
echo "RUN_ID: ${RUN_ID}" > "$RESULTS_DIR/run_id.txt"

commands=(
  "python3 ./services/studio/_text_to_workflow/bpmn_evaluation/main.py --run_id=${RUN_ID} > $RESULTS_DIR/log_${RUN_ID}.txt"
  "find ./services/studio/_text_to_workflow/bpmn_evaluation/results/ -name '*.html' -exec cp {} $RESULTS_DIR/ \\; 2>/dev/null || echo 'No HTML results found'"
  "find ./services/studio/_text_to_workflow/bpmn_evaluation/logs/ -name 'bpmn_evaluation_logs.json' -exec cp {} $RESULTS_DIR/ \\; 2>/dev/null || echo 'No logs found'"
)

run_command() {
  eval "$1" || any_failed=1
}

for cmd in "${commands[@]}"; do
  run_command "$cmd"
done

report_file=$(ls "$RESULTS_DIR"/bpmn_evaluation_summary_*.html 2>/dev/null | tail -n 1)
log_file=$(ls "$RESULTS_DIR"/bpmn_evaluation_logs.json 2>/dev/null | tail -n 1)

if [ -z "$report_file" ]; then
  echo "No HTML report found!"
fi

if [ -z "$log_file" ]; then
  echo "No log file found!"
fi

exit $any_failed
