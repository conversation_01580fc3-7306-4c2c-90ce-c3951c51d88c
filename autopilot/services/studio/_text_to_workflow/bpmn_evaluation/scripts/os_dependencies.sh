#!/bin/bash

set -e  # Exit immediately if any command fails
set -x  # Enable debug mode for logging

echo "Updating package lists..."
sudo mkdir -p /etc/apt/keyrings
sudo apt-get update -qq

echo "Checking and installing missing OS-level dependencies..."
PKGS="curl ffmpeg gcc libb2-1 libfontconfig1 libgl1 libglib2.0-0 libicu-dev libmagic1 libsm6 libxext6 libxrender1 openssh-client python3-dev python3-pip python3-venv screen wget"
sudo apt-get install -y --no-install-recommends $(echo $PKGS | tr ' ' '\n' | grep -vF "$(dpkg --get-selections | awk '{print $1}')")

echo "Setting up Python virtual environment..."
python3 -m venv ~/.venv
source ~/.venv/bin/activate

echo "Upgrading pip..."
pip install --upgrade pip

echo "Dependency installation complete!"
