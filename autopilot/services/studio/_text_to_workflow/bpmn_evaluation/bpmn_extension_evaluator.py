import json
import pathlib
from typing import Any, Dict, List, Tuple, Union

import langchain.prompts

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes, ScoreThresholds
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import ExtensionDatasetEntry, ExtensionEvaluationResult
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_utils import ModelEncoder
from services.studio._text_to_workflow.bpmn_generation.bpmn_extension_task import BpmnExtensionTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import BpmnRequestContext, ExtensionDataOverride, ModelType
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()
EXTENSION_TASK = BpmnExtensionTask()


class BaseExtensionEvaluator(BaseEvaluator[ExtensionDatasetEntry, ExtensionEvaluationResult]):
    evaluation_type = "BASE_EXTENSION"
    result_class = ExtensionEvaluationResult

    def __init__(self):
        super().__init__()
        config_path = pathlib.Path(__file__).parent / "config"
        yml_config_path = (config_path / "prompt.yaml").absolute()
        self.config = yaml_load(yml_config_path)

    def get_expected_result(self, dataset: ExtensionDatasetEntry) -> str:
        return json.dumps(dataset.expectedExtensions, indent=2)

    def get_dataset_metadata(self, dataset: ExtensionDatasetEntry) -> Dict[str, Any]:
        return {
            "id": dataset.id,
            "user_prompt": dataset.userRequest,
        }

    def load_datasets(self) -> List[ExtensionDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    async def generate_output(self, dataset: ExtensionDatasetEntry) -> Dict[str, Any]:
        if not dataset.mockExtensionData:
            return self._error_response("No mock extension data provided. Extensions cannot be generated without mock data.")

        try:
            LOGGER.info(f"Processing dataset: {dataset.id}, user request: {dataset.userRequest}")

            # Set up context with mock data
            request_context = BpmnRequestContext(
                user_request=dataset.userRequest,
                current_bpmn=dataset.currentBpmn,
                request_context=RequestContext(),
                extension_data_override=ExtensionDataOverride.model_validate(dataset.mockExtensionData),
                override_model_type=ModelType.OpenAI,
            )

            # Generate extension result
            extension_result = await EXTENSION_TASK.generate(request_context)

            if extension_result is None:
                return self._error_response("Extension task returned None result")

            generated_output = ""
            try:
                generated_output = json.dumps(extension_result, indent=2, cls=ModelEncoder)
            except Exception as json_err:
                LOGGER.error(f"Failed to serialize extension result: {json_err}")

            return {
                "generated_extensions": extension_result,
                "generated_output": generated_output,
            }

        except ValueError as val_err:
            return self._error_response(f"Invalid extension data: {str(val_err)}")
        except Exception as e:
            return self._error_response(f"Error generating extensions: {str(e)}")

    def _error_response(self, error_msg: str) -> Dict[str, Any]:
        response = {"error": error_msg, "generated_extensions": [], "generated_output": "[]"}
        return response

    def get_evaluation_from_llm(
        self,
        user_request: str,
        bpmn_workflow: str,
        expected_extensions: Union[List[Dict[str, Any]], Dict[str, Any]],
        generated_extensions: Union[List[Dict[str, Any]], Dict[str, Any]],
    ) -> Tuple[Dict[str, Any], str]:
        """Evaluate generated extensions against expected ones using LLM."""
        if not generated_extensions:
            return {"overall_score": 0, "overall_explanation": "No extensions to evaluate"}, "No extensions to evaluate"

        try:
            # Safeguard against None values
            expected_exts = [] if expected_extensions is None else expected_extensions
            generated_exts = [] if generated_extensions is None else generated_extensions

            # Convert to lists if they're not already
            expected_exts = expected_exts if isinstance(expected_exts, list) else [expected_exts]
            generated_exts = generated_exts if isinstance(generated_exts, list) else [generated_exts]

            gateway_model = ModelManager().get_llm_model("bpmn_extension_eval_model", ConsumingFeatureType.BPMN_GENERATION)

            inputs = {
                "user_request": user_request,
                "bpmn_workflow": bpmn_workflow,
                "expected_extensions": json.dumps(expected_exts, indent=2),
                "generated_extensions": json.dumps(generated_exts, indent=2, cls=ModelEncoder),
            }

            template = self.config["prompt"]["extension_eval_template"]
            system_message = langchain.prompts.SystemMessagePromptTemplate.from_template(template)
            human_message = langchain.prompts.HumanMessagePromptTemplate.from_template("Please evaluate the extensions based on the instructions.")
            chat_template = langchain.prompts.ChatPromptTemplate.from_messages([system_message, human_message])

            prompt_value = chat_template.invoke(inputs)
            response = gateway_model.invoke(prompt_value)
            raw_output = str(response.content) if hasattr(response, "content") else str(response)

            json_result = self._parse_llm_output(raw_output)
            return json_result, raw_output

        except Exception as e:
            error_detail = f"Failed to process LLM evaluation: {str(e)}"
            LOGGER.error(error_detail)
            return {"overall_score": 0, "overall_explanation": error_detail}, error_detail

    def _parse_llm_output(self, raw_output: str) -> Dict[str, Any]:
        if not raw_output or not raw_output.strip():
            LOGGER.error("Empty LLM output received")
            return {"overall_score": 0, "overall_explanation": "Empty LLM output received"}

        try:
            json_str = raw_output.strip()

            for marker in ["```json", "```"]:
                if marker in json_str:
                    start = json_str.find(marker) + len(marker)
                    end = json_str.find("```", start)
                    if end > start:
                        json_str = json_str[start:end].strip()
                        break

            return self._safe_parse_json(json_str)

        except Exception as e:
            LOGGER.error(f"Failed to parse output: {e}")
            return {"overall_score": 0, "overall_explanation": f"Error parsing response: {str(e)}"}

    def _safe_parse_json(self, json_str: str) -> Dict[str, Any]:
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            LOGGER.warning("JSON parsing error. Using default values.")
            return {"overall_score": 0, "overall_explanation": "Error parsing LLM response."}

    def calculate_score(self, dataset: ExtensionDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate the evaluation score based on LLM assessment."""
        if "error" in output_data:
            return self._evaluation_error_response(dataset, output_data.get("generated_output", "[]"), f"Failed: {output_data['error']}", output_data["error"])

        # Get generated and expected extensions
        generated_extensions = output_data.get("generated_extensions", [])
        expected_extensions = dataset.expectedExtensions or []
        expected_json = json.dumps(expected_extensions, indent=2)

        if not generated_extensions or generated_extensions is None:
            return self._evaluation_error_response(
                dataset, "[]", "No valid extensions were generated", "Empty or None extension result - model did not generate any extension suggestions"
            )

        try:
            # Ensure extensions are in list format
            if not isinstance(generated_extensions, list):
                generated_extensions = [generated_extensions]

            # Get evaluation from LLM
            evaluation_result, raw_output = self.get_evaluation_from_llm(
                user_request=dataset.userRequest,
                bpmn_workflow=dataset.currentBpmn,
                expected_extensions=expected_extensions,
                generated_extensions=generated_extensions,
            )

            # Get the overall score from the evaluation
            eval_score = int(evaluation_result.get("overall_score", 0) * 100)

            result = {
                "eval_score": eval_score,
                "extension_accuracy": eval_score,
                "overall_explanation": evaluation_result.get("overall_explanation", ""),
                "generated_result": output_data.get("generated_output", "[]"),
                "expected_result": expected_json,
            }

            if "error" in evaluation_result:
                result["error"] = evaluation_result["error"]

            return result

        except Exception as e:
            error_msg = f"Error in evaluation: {str(e)}"
            LOGGER.error(error_msg)
            return self._evaluation_error_response(dataset, output_data.get("generated_output", "[]"), f"Evaluation failed: {error_msg}", f"{error_msg}")

    def _evaluation_error_response(self, dataset: ExtensionDatasetEntry, generated_output: str, explanation: str, error: str) -> Dict[str, Any]:
        result = {
            "eval_score": 0,
            "extension_accuracy": 0,
            "overall_explanation": explanation,
            "error": error,
            "generated_result": generated_output,
            "expected_result": json.dumps(dataset.expectedExtensions or [], indent=2),
        }
        return result

    def get_threshold(self, prompt_verbosity: str) -> float:
        return ScoreThresholds.EXTENSION * 100

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("bpmn_generation_chat_model_anthropic", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class ExtensionEvaluator(BaseExtensionEvaluator):
    evaluation_type = EvalTypes.BPMN_EXTENSION

    def load_datasets(self) -> List[ExtensionDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import ExtensionDataset

        return ExtensionDataset(self.evaluation_type).load_datasets()
