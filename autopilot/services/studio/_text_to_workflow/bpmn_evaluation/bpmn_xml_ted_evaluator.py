import json
import os
import pathlib
from typing import Any, Dict, List, Sequence

import langchain.prompts
import langchain_community.callbacks

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import BPMNDatasetEntry, BPMNEvaluationResult
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_utils import enum_serializer
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_xml_ted_evaluator_helpers import TEDScoreCalculator
from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import ModelType, Tool
from services.studio._text_to_workflow.bpmn_generation.bpmn_router_task import BpmnRouterTask
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()
TASK: BpmnRouterTask = BpmnRouterTask()


class BaseTEDEvaluator(BaseEvaluator[BPMNDatasetEntry, BPMNEvaluationResult]):
    evaluation_type = "BaseTED"
    result_class = BPMNEvaluationResult

    def __init__(self) -> None:
        config_path = pathlib.Path(__file__).parent / "config"
        yml_config_path = (config_path / "prompt.yaml").absolute()
        self.config = yaml_load(yml_config_path)

        # Try to load schema from evaluation config
        schema_path = pathlib.Path(os.path.join(config_path, "bpmn_schema.json"))

        # If not found, fallback to generation config
        if not schema_path.exists():
            LOGGER.info("bpmn_schema.json not found in evaluation config directory, looking in generation config...")
            generation_config_path = pathlib.Path(__file__).parent.parent / "bpmn_generation" / "config"
            schema_path = pathlib.Path(os.path.join(generation_config_path, "bpmn_schema.json"))

            if not schema_path.exists():
                LOGGER.error("bpmn_schema.json not found in either evaluation or generation config directories")
                raise FileNotFoundError("Could not find bpmn_schema.json in any config directory")

        # Load the schema
        self.bpmn_schema = BpmnBaseTask.read_content(schema_path)

    def get_expected_result(self, dataset: BPMNDatasetEntry) -> str:
        return dataset.expectedBPMN_XML or ""

    def get_dataset_metadata(self, dataset: BPMNDatasetEntry) -> Dict[str, Any]:
        return {
            "id": dataset.id,
            "dataset_source": dataset.dataset_source,
            "user_prompt": dataset.user_prompt,
            "prompt_verbosity": dataset.prompt_verbosity,
        }

    def load_datasets(self) -> Sequence[BPMNDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    def get_system_prompt_template(self) -> str:
        return self.config["prompt"]["system_template"]

    async def generate_bpmn_from_llm(self, system_message: langchain.prompts.SystemMessagePromptTemplate, inputs: Dict[str, Any]) -> tuple[str, TokenUsage]:
        gateway_model = ModelManager().get_llm_model("bpmn_generation_eval_model_anthropic", ConsumingFeatureType.BPMN_EVALUATION)
        user_message = langchain.prompts.HumanMessagePromptTemplate.from_template("Generate BPMN XML for: {user_prompt}")
        chat_template = langchain.prompts.ChatPromptTemplate.from_messages([system_message, user_message])
        chat_chain = chat_template | gateway_model

        with langchain_community.callbacks.get_openai_callback() as cb:
            response = await chat_chain.ainvoke(inputs)
            raw_output = response.content if isinstance(response.content, str) else str(response.content)
            usage = TokenUsage(
                model=getattr(gateway_model, "deployment_name", ""),
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        final_bpmn = raw_output.removeprefix("```xml\n").removesuffix("\n```")
        return final_bpmn, usage

    async def generate_output(self, dataset: BPMNDatasetEntry) -> Dict[str, Any]:
        user_prompt = dataset.user_prompt
        current_bpmn = dataset.currentBPMN_XML or ""

        try:
            # Generate JSON patch data
            result = await TASK.process(
                {
                    "userRequest": user_prompt,
                    "currentBpmn": current_bpmn,
                    "modelTypeOverride": {"tool": Tool.EDIT_BPMN, "model_type": ModelType.Anthropic},
                }
            )
            output = self._get_tool_result(result, tool_name=Tool.EDIT_BPMN)
            if "error" in output:
                error_msg = output["error"]
                LOGGER.error(error_msg)
                return {
                    "error": error_msg,
                    "eval_score": 0,
                    "generated_result": "",
                    "generated_bpmn_xml": "",
                    "expected_bpmn_xml": "",
                    "node_stats": {
                        "expected_count": 0,
                        "generated_count": 0,
                        "common_count": 0,
                        "missing_count": 0,
                        "extra_count": 0,
                        "missing_nodes": [],
                        "extra_nodes": [],
                    },
                }

            json_patch_data_str = json.dumps(output, indent=2, default=enum_serializer)
        except Exception as e:
            LOGGER.error(f"Error during JSON Schema generation from BPMN Chat: {e}")
            return {}

        try:
            # Create prompt for BPMN generation
            prompt_template = self.get_system_prompt_template()
            system_message = langchain.prompts.SystemMessagePromptTemplate.from_template(prompt_template)
            template_inputs = {
                "bpmn_schema": self.bpmn_schema,
                "json_patch_data": json_patch_data_str,
                "user_prompt": user_prompt,
                "current_bpmn": current_bpmn,
            }
        except Exception as e:
            LOGGER.error(f"Error during BPMN prompt creation: {e}")
            return {}

        try:
            # Generate final BPMN XML
            final_bpmn, _ = await self.generate_bpmn_from_llm(system_message, template_inputs)
            return {"generated_output": final_bpmn, "json_patch_data": json_patch_data_str}
        except Exception as e:
            LOGGER.error(f"Error during BPMN generation from LLM: {e}")
            return {}

    def calculate_score(self, dataset: BPMNDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        expected_bpmn = dataset.expectedBPMN_XML or ""
        generated_bpmn = output_data.get("generated_output", "")

        if not generated_bpmn:
            return {"eval_score": 0, "added_elements": [], "removed_elements": []}

        # Calculate TED score
        ted_score, added_nodes, removed_nodes, ted_error = TEDScoreCalculator.compute(expected_bpmn, generated_bpmn)

        # Create lists with safely extracted counts
        added_count = len(added_nodes) if isinstance(added_nodes, list) else 0
        removed_count = len(removed_nodes) if isinstance(removed_nodes, list) else 0

        # Convert counts to lists for compatibility with the result schema
        added_elements = [f"added_{i}" for i in range(added_count)]
        removed_elements = [f"removed_{i}" for i in range(removed_count)]

        return {
            "eval_score": ted_score,
            "added_elements": added_elements,
            "removed_elements": removed_elements,
            "ted_error": ted_error,
            "json_patch_data": output_data.get("json_patch_data", ""),
        }

    def get_threshold(self, prompt_verbosity: str) -> float:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import ScoreThresholds

        # TED is a float value (0.80), multiply by 100 to get percentage
        return ScoreThresholds.TED * 100

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("bpmn_generation_chat_model_anthropic", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class TEDEvaluatorGeneration(BaseTEDEvaluator):
    evaluation_type = EvalTypes.BPMN_GENERATION

    def load_datasets(self) -> List[BPMNDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import BPMNDataset

        return BPMNDataset(self.evaluation_type).load_datasets()


class TEDEvaluatorEdits(BaseTEDEvaluator):
    evaluation_type = EvalTypes.BPMN_EDITS

    def load_datasets(self) -> List[BPMNDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import BPMNDataset

        return BPMNDataset(self.evaluation_type).load_datasets()
