import os

BASE_DIR = os.path.abspath(os.path.dirname(__file__))
RESULTS_DIR = os.path.join(BASE_DIR, "results")
LOGS_DIR = os.path.join(BASE_DIR, "logs")
CONFIG_DIR = os.path.join(BASE_DIR, "config")


def create_directories(*dirs: str) -> None:
    for d in dirs:
        os.makedirs(d, exist_ok=True)


create_directories(RESULTS_DIR, LOGS_DIR, CONFIG_DIR)

DEFAULT_SCORE_THRESHOLD = 90
DEFAULT_ENCODING = "utf-8"
DEFAULT_EXECUTION_TIMEOUT = 300


class ScoreThresholds:
    DEFAULT = 0.80
    TED = 0.80
    JSON_PATCH = 0.80
    EXPRESSION = 0.80
    QA = 0.80
    ROUTER = 1.00
    EXTENSION = 0.80


class PromptTypes:
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ALL = [LOW, MEDIUM, HIGH]


class EvalTypes:
    BPMN_GENERATION = "BPMNGeneration"
    BPMN_EDITS = "BPMNEdits"
    EXPRESSION_GENERATION = "ExpressionGeneration"
    BPMN_JSON_GENERATION = "BPMNJSONGeneration"
    BPMN_JSON_EDITS = "BPMNJSONEdits"
    BPMN_QA = "BPMNQA"
    BPMN_ROUTER = "BPMNRouter"
    BPMN_EXTENSION = "BPMNExtensions"
    ALL = "all"

    @classmethod
    def get_all_types(cls):
        return [
            cls.BPMN_ROUTER,
            cls.BPMN_JSON_EDITS,
            cls.BPMN_JSON_GENERATION,
            cls.EXPRESSION_GENERATION,
            cls.BPMN_QA,
            cls.BPMN_EXTENSION,
            cls.ALL,
        ]


# Namespaces used in BPMN XML
BPMN_NAMESPACES = {
    "bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL",
    "bpmndi": "http://www.omg.org/spec/BPMN/20100524/DI",
    "dc": "http://www.omg.org/spec/DD/20100524/DC",
    "di": "http://www.omg.org/spec/DD/20100524/DI",
}

# Namespaces used in BPMN XML
BPMN_NAMESPACES = {
    "bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL",
    "bpmndi": "http://www.omg.org/spec/BPMN/20100524/DI",
    "dc": "http://www.omg.org/spec/DD/20100524/DC",
    "di": "http://www.omg.org/spec/DD/20100524/DI",
    "xsi": "http://www.w3.org/2001/XMLSchema-instance",
}

IGNORED_BPMN_ELEMENT_TYPES = {
    "BPMNDiagram",
    "BPMNShape",
    "BPMNEdge",
    "BPMNLabel",
    "documentation",
    "extensionElements",
}
