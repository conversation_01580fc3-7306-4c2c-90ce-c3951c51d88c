import asyncio
import os
from contextlib import asynccontextmanager

from experimental.autopilot_dataset import studio_token_helper
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.uipath_cloud_platform import is_token_valid

# Global connection pooling variables
_connection_lock = asyncio.Lock()
_connection_pool = {}
_max_connections = int(os.getenv("MAX_GATEWAY_CONNECTIONS", "20"))


@asynccontextmanager
async def connection_pool_manager():
    """Manage connection pool to prevent too many concurrent connections."""
    pool_key = os.getenv("TENANT_ID", "default")

    async with _connection_lock:
        if pool_key not in _connection_pool:
            _connection_pool[pool_key] = asyncio.Semaphore(value=_max_connections)

    semaphore = _connection_pool[pool_key]
    async with semaphore:
        yield


async def get_studio_token():
    """Get a valid studio token from LLM gateway or return None if credentials are missing."""
    token = None

    if os.getenv("UIPATH_CLIENT_ID") and os.getenv("UIPATH_CLIENT_SECRET"):
        # Use LLM gateway if credentials are available
        async with connection_pool_manager():
            for _ in range(3):  # Try up to 3 times
                token = await studio_token_helper.get_studio_token()
                if token and is_token_valid(token):
                    break

            if not token or not is_token_valid(token):
                print("Warning: Could not obtain valid token from LLM gateway")

    return token


async def setup_llm_gateway_authentication(tenant_id=None):
    """Setup authentication using LLM gateway if credentials are available, otherwise use direct endpoints."""

    # Get tenant ID from connections_loader if not provided
    if tenant_id is None:
        try:
            tenant_id, _ = get_connections_data()
            print(f"Using tenant ID from connections_loader: {tenant_id}")
        except Exception:
            # Fall back to environment variable if connections_loader fails
            tenant_id = os.getenv("TENANT_ID")
            print(f"Could not get tenant ID from connections_loader, using fallback: {tenant_id}")

    token = await get_studio_token()

    if token:
        # Configure to use LLM gateway
        os.environ["USE_LLM_GATEWAY"] = "true"
        os.environ["UIPATH_TOKEN"] = token

        # Setup request context with token
        request_context = get_testing_request_context("en", tenant_id, "BPMN Evaluation", token=token)
        request_utils.set_request_context(request_context)
        print(f"Using LLM gateway for tenant: {tenant_id}")
    else:
        # Fall back to direct OpenAI endpoint
        os.environ["USE_LLM_GATEWAY"] = "false"

        # Setup request context without token
        request_context = get_testing_request_context("en", tenant_id, "BPMN Evaluation")
        request_utils.set_request_context(request_context)
        print("Using direct OpenAI endpoint (no credentials for LLM gateway)")

    return token
