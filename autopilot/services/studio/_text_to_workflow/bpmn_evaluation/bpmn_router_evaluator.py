from typing import Any, Dict, List, cast

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes, ScoreThresholds
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import RouterDatasetEntry, RouterEvaluationResult
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import ModelType, Tool
from services.studio._text_to_workflow.bpmn_generation.bpmn_router_task import BpmnRouterTask
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

LOGGER = telemetry_utils.AppInsightsLogger()
ROUTER_TASK = BpmnRouterTask()

EvalTypes.BPMN_ROUTER = "BPMNRouter"


class BaseRouterEvaluator(BaseEvaluator[RouterDatasetEntry, RouterEvaluationResult]):
    evaluation_type = "BASE_ROUTER"
    result_class = RouterEvaluationResult

    def get_expected_result(self, dataset: RouterDatasetEntry) -> str:
        return ", ".join([tool.name for tool in dataset.expectedTools])

    def load_datasets(self) -> List[RouterDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    def _format_output(self, detected_tools: List[Any], router_result, is_error: bool = False, error_msg: str | None = None) -> Dict[str, Any]:
        def serialize_for_json(obj):
            if isinstance(obj, Tool):
                return obj.value
            elif isinstance(obj, dict):
                return {k: serialize_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize_for_json(item) for item in obj]
            else:
                return obj

        serialized_tools = serialize_for_json(detected_tools)

        result = {
            "generated_output": ", ".join([tool.name if isinstance(tool, Tool) else tool for tool in detected_tools])
            if detected_tools
            else "No tools detected",
            "detected_tools": serialized_tools,
            "router_response": {"valid": not is_error, "explanation": error_msg if error_msg else (router_result.explanation if router_result else "")},
        }

        return result

    async def generate_output(self, dataset: RouterDatasetEntry) -> Dict[str, Any]:
        user_prompt = dataset.userRequest

        LOGGER.info(f"Processing dataset {dataset.id}: {user_prompt[:100]}...")
        LOGGER.info(f"Expected tools: {[tool.name for tool in dataset.expectedTools]}")

        try:
            request = {"userRequest": user_prompt, "chatHistory": [], "routerModel": ModelType.Google}
            # Add image flag if needed
            if dataset.imageExists:
                # Just pass a placeholder to indicate image exists
                request["image"] = b"image_exists_flag"
                LOGGER.info(f"Adding image flag for dataset {dataset.id}")
            elif any(tool.name == "CONVERT_IMAGE" for tool in dataset.expectedTools):
                LOGGER.warning(f"Dataset {dataset.id} expects CONVERT_IMAGE but no image flag set")

            # Get router result
            try:
                router_result, _ = await ROUTER_TASK._find_tools(request)
                LOGGER.info(f"Router response for {dataset.id}: valid={router_result.valid}, tools={len(router_result.tools or [])}")

                if not router_result.valid:
                    return self._format_output([], router_result, is_error=True, error_msg=router_result.explanation)

                # Process detected tools
                detected_tools = []
                if router_result.tools:
                    for tool_result in router_result.tools:
                        detected_tools.append(tool_result.tool)
                        LOGGER.info(f"Found tool in router_result: {tool_result.tool}")

                # Return formatted output
                return self._format_output(detected_tools, router_result)
            except KeyError as key_error:
                error_msg = f"KeyError in router response: {str(key_error)}"
                LOGGER.error(f"Error processing router response: {error_msg}")
                return self._format_output([], None, is_error=True, error_msg=error_msg)

        except Exception as e:
            error_msg = str(e)
            LOGGER.error(f"Error in router evaluation: {error_msg}", properties={"dataset_id": dataset.id})
            return self._format_output([], None, is_error=True, error_msg=f"Router error: {error_msg}")

    def _format_router_response_as_error(self, router_response: Dict[str, Any], detected_tools_names: List[str] | None = None) -> str:
        # 1. If there's an explanation in router_response, use it
        if isinstance(router_response, dict) and router_response.get("explanation"):
            return str(router_response.get("explanation"))

        # 2. If there are detected tools, show them as invalid
        if detected_tools_names and len(detected_tools_names) > 0:
            return f"Invalid tool detected: {', '.join(detected_tools_names)}"

        # 3. Default message
        return "No tools detected"

    def _format_score_result(
        self, eval_score: int, expected_tools_names: List[str], detected_tools_names: List[str], router_response: Dict[str, Any], error: str | None = None
    ) -> Dict[str, Any]:
        """Helper method to standardize score calculation result formatting"""

        result = {
            "eval_score": eval_score,
            "expected_tools": list(expected_tools_names),
            "detected_tools": list(detected_tools_names),
            "router_response": router_response,
        }

        if error:
            result["error"] = error

        return result

    def calculate_score(self, dataset: RouterDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        expected_tools_names = [tool.name for tool in dataset.expectedTools]
        expected_tools_set = set(expected_tools_names)

        detected_tools = output_data.get("detected_tools", [])
        detected_tools_names = []
        for tool in detected_tools:
            if isinstance(tool, Tool):
                detected_tools_names.append(tool.name)
            elif isinstance(tool, dict) and "tool" in tool:
                tool_value = tool["tool"]
                if isinstance(tool_value, Tool):
                    detected_tools_names.append(tool_value.name)
                elif isinstance(tool_value, str) and hasattr(Tool, tool_value.upper().replace("-", "_")):
                    # Handle string values like 'generate-bpmn' by converting to enum name format
                    detected_tools_names.append(tool_value.upper().replace("-", "_"))
                else:
                    detected_tools_names.append(str(tool_value))
            elif isinstance(tool, str):
                # Convert hyphenated tool names to enum format
                if hasattr(Tool, tool.upper().replace("-", "_")):
                    detected_tools_names.append(tool.upper().replace("-", "_"))
                else:
                    detected_tools_names.append(tool)
            else:
                detected_tools_names.append(str(tool))

        detected_tools_set = set(detected_tools_names)
        router_response = output_data.get("router_response", {})

        # If response is invalid, always return 0 score and explanation as error
        if not router_response.get("valid", True):
            explanation = router_response.get("explanation", "")
            if not explanation:
                explanation = self._format_router_response_as_error(router_response, detected_tools_names)
            return self._format_score_result(0, expected_tools_names, detected_tools_names, router_response, explanation)

        # Check if there was an error response
        is_error_response = False
        if router_response.get("explanation", ""):
            is_error_response = True

            # Special case for multi-tool requests that are explicitly rejected
            multi_tool_error = "not supported yet" in router_response.get("explanation", "") and len(expected_tools_set) > 1
            if multi_tool_error:
                # This is actually correct behavior - the router detected multiple tools are needed
                # but the current implementation doesn't support it
                return self._format_score_result(100, expected_tools_names, detected_tools_names, router_response)

        # Perfect match case (exact tool selection)
        if expected_tools_set == detected_tools_set:
            return self._format_score_result(100, expected_tools_names, detected_tools_names, router_response)

        if not detected_tools_set:
            if is_error_response:
                # The router determined it couldn't handle the request
                if "CONVERT_IMAGE" in expected_tools_names and "Error" in router_response.get("explanation", ""):
                    error_msg = router_response.get("explanation", "Unknown error")
                    if not error_msg or error_msg == "Unknown error":
                        error_msg = self._format_router_response_as_error(router_response, detected_tools_names)
                    return self._format_score_result(0, expected_tools_names, detected_tools_names, router_response, error_msg)

            # No tools detected and no error explanation - add the router response as error
            error_msg = self._format_router_response_as_error(router_response, detected_tools_names)
            return self._format_score_result(0, expected_tools_names, detected_tools_names, router_response, error_msg)

        if not detected_tools_set and is_error_response:
            explanation = router_response.get("explanation", "")
            if explanation:
                return self._format_score_result(0, expected_tools_names, detected_tools_names, router_response, explanation)
            else:
                error_msg = self._format_router_response_as_error(router_response, detected_tools_names)
                return self._format_score_result(0, expected_tools_names, detected_tools_names, router_response, error_msg)

        # Calculate score for partial matches using F1 score method
        # F1 score balances how many correct tools we found against how many incorrect ones we suggested
        # It gives us a fair way to measure overall accuracy
        true_positives = len(expected_tools_set.intersection(detected_tools_set))

        # Precision: How many of our detected tools were actually correct
        # High precision means we didn't suggest many wrong tools
        precision = true_positives / len(detected_tools_set) if detected_tools_set else 0

        # Recall: How many of the expected tools did we actually find
        # High recall means we didn't miss many tools we should have found
        recall = true_positives / len(expected_tools_set) if expected_tools_set else 0

        # F1 score combines precision and recall into one number
        # It's low if either precision or recall is low
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        # Convert to percentage for reporting
        eval_score = int(f1_score * 100)

        LOGGER.info(f"Score for {dataset.id}: expected_tools={list(expected_tools_names)}, detected_tools={list(detected_tools_names)}, score={eval_score}")

        # Add error message for zero scores
        if eval_score == 0:
            error_msg = self._format_router_response_as_error(router_response, detected_tools_names)
            return self._format_score_result(eval_score, expected_tools_names, detected_tools_names, router_response, error_msg)

        return self._format_score_result(eval_score, expected_tools_names, detected_tools_names, router_response)

    def get_threshold(self, prompt_verbosity: str) -> float:
        router_threshold = getattr(ScoreThresholds, "ROUTER", ScoreThresholds.DEFAULT)
        return router_threshold * 100

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("bpmn_router_model_google", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class RouterEvaluator(BaseRouterEvaluator):
    evaluation_type = EvalTypes.BPMN_ROUTER

    def load_datasets(self) -> List[RouterDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import RouterDataset

        datasets = RouterDataset(self.evaluation_type).load_datasets()
        return cast(List[RouterDatasetEntry], datasets)
