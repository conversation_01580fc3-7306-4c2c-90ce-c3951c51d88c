# **BPMN Evaluation - Setup Guide**

## **Prerequisites**

Before running BPMN evaluation, you must first set up the Autopilot local development environment:

1. Follow the setup instructions in the main Autopilot README: https://github.com/UiPath/ML/blob/master/README.md
2. Complete all the setup steps, including:
   - Python environment setup
   - Dependencies installation
   - Data repository cloning
   - Required models download

Once the base Autopilot environment is properly configured, you can proceed with BPMN evaluation setup.

---

## **Quick Start with VS Code Workspace**

For the fastest setup experience, we've included workspace files for VS Code:

### **Option 1: Use the Root Workspace File (Recommended)**
1. Open VS Code
2. File > Open Workspace from File...
3. Navigate to the root of the autopilot directory and open `autopilot.code-workspace`
4. Go to the Run and Debug panel (Ctrl+Shift+D or Cmd+Shift+D on macOS)
5. Choose one of the BPMN evaluation configurations from the dropdown

### **Option 2: Use the BPMN-specific Workspace File**
1. Open VS Code
2. File > Open Workspace from File...
3. Navigate to `services/studio/_text_to_workflow/bpmn_evaluation/bpmn_evaluation.code-workspace`
4. Choose one of the BPMN evaluation configurations from the dropdown

---

## **Available Launch Configurations**

The workspace files include the following launch configurations for BPMN evaluation:

- **BPMN Evaluation - Run All (LLM Gateway)**: Runs all evaluators with LLM Gateway enabled
- **BPMN Evaluation - Router (LLM Gateway)**: Runs only the Router evaluator
- **BPMN Evaluation - JSON Edits (LLM Gateway)**: Runs only the JSON Edits evaluator
- **BPMN Evaluation - JSON Generation (LLM Gateway)**: Runs only the JSON Generation evaluator
- **BPMN Evaluation - Expression (LLM Gateway)**: Runs only the Expression evaluator
- **BPMN Evaluation - QA (LLM Gateway)**: Runs only the QA evaluator
- **BPMN Evaluation - XML Generation (LLM Gateway)**: Runs only the XML Generation evaluator
- **BPMN Evaluation - Extension (LLM Gateway)**: Runs only the Extension evaluator
- **BPMN Evaluation - Run All (without LLM Gateway)**: Runs all evaluators without using LLM Gateway

Each configuration has a predefined run ID for easy identification in logs and reports.

## **Evaluation Types**

The BPMN evaluation system supports multiple evaluator types, which can be specified using the `--evaluator` command line argument:

```bash
python -m services.studio._text_to_workflow.bpmn_evaluation.main --evaluator=BPMNRouter
```

Available evaluator types (from the `EvalTypes` constants):

| Evaluator Type | Description |
|----------------|-------------|
| `BPMNRouter` | Evaluates the BPMN router functionality |
| `BPMNJSONEdits` | Evaluates JSON patch edits for BPMN workflows |
| `BPMNJSONGeneration` | Evaluates JSON patch generation for BPMN workflows |
| `ExpressionGeneration` | Evaluates expression generation capabilities |
| `BPMNQA` | Evaluates QA functionality for BPMN workflows |
| `BPMNGeneration` | Evaluates BPMN XML generation capabilities |
| `BPMNExtension` | Evaluates extension suggestions for BPMN workflow tasks |
| `all` | Runs all available evaluators (default) |

---

## **Environment Setup**

Create a `.env` file in the root of the autopilot directory with the necessary environment variables:

```ini
# LLM Gateway configuration (toggle between true/false)
USE_LLM_GATEWAY=true

# Method 1: Client credentials for automatic token acquisition (recommended)
UIPATH_CLIENT_ID=your_client_id_here
UIPATH_CLIENT_SECRET=your_client_secret_here

# Method 2: Static token (alternative, requires manual refresh)
# UIPATH_TOKEN=your_jwt_token_here

# Application settings
SHOULD_FORCE_REBUILD_DATASET=false
RUN_EMBEDDINGS_BACKGROUND_REBUILD=false
REFRESH_EMBEDDINGS_INTERVAL=5
USE_CACHED_CONNECTIONS=true

# Optional telemetry settings
ENABLE_TELEMETRY=false
TRACELOOP_TRACE_CONTENT=false
```

## **Authentication Setup for LLM Gateway**

The BPMN evaluation supports two authentication methods for the UiPath LLM Gateway:

### **Method 1: Client Credentials (Recommended)**

This method automatically handles token acquisition and renewal:

```ini
# Enable LLM Gateway
USE_LLM_GATEWAY=true

# Client credentials
UIPATH_CLIENT_ID=your_client_id_here
UIPATH_CLIENT_SECRET=your_client_secret_here
```

### **Method 2: Static Token**

If you prefer to provide a token directly:

```ini
# Enable LLM Gateway
USE_LLM_GATEWAY=true

# Static token
UIPATH_TOKEN="your_jwt_token_here"
```

Note that tokens typically expire after one hour and will need to be refreshed manually.

---

## **Running BPMN Evaluation**

### **Local Development: Using VS Code Launch Configurations**

For local development and testing, use the VS Code launch configurations included in the workspace files:

1. Open the workspace
2. Go to the Run and Debug panel
3. Select a configuration from the dropdown
4. Click the green "Play" button

### **Command Line Usage**

You can also run evaluations from the command line:

```bash
# Run all evaluators
python -m services.studio._text_to_workflow.bpmn_evaluation.main --run_id=my-custom-run-id

# Run a specific evaluator
python -m services.studio._text_to_workflow.bpmn_evaluation.main --run_id=my-custom-run-id --evaluator=BPMNRouter
```

### **For PRs: Using Azure Pipelines**

When you open a PR, the evaluation should run automatically. If it doesn't start automatically, you need to manually trigger it using Azure Pipelines. Add a comment to your PR:

```
/azp run Autopilot-BPMN-Evaluation
```

This will trigger the [BPMN Evaluation Pipeline](https://dev.azure.com/uipath/Autopilot/_build?definitionId=15648&_a=summary).

The pipeline is configured to use specific development datasets for evaluating BPMN generation capabilities. The results will be available in the pipeline artifacts after completion.

---

## **Viewing Results**

### **Log File Location**

Logs are stored at:
```
/autopilot/services/studio/_text_to_workflow/bpmn_evaluation/logs/bpmn_evaluation_log.json
```

### **Evaluation Report**

The HTML evaluation report is saved at:
```
/autopilot/services/studio/_text_to_workflow/bpmn_evaluation/results/evaluation_summary_<RUN_ID>.html
```

Open this file in a web browser to view detailed evaluation results.

---