import argparse
import async<PERSON>
from argparse import Namespace
from datetime import datetime

from services.studio._text_to_workflow.bpmn_evaluation.bmpn_qa_evaluator import QaEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_llm_gateway_auth import setup_llm_gateway_authentication
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_logger import reset_logs
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_task import EvaluationTask
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_extension_evaluator import ExtensionEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_json_patch_ted_evaluator import JSONPatchTEDEvaluatorEdits, JSONPatchTEDEvaluatorGeneration
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_router_evaluator import RouterEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.expression_evaluator import ExpressionGeneration

# Default evaluator
DEFAULT_EVALUATOR = EvalTypes.ALL


def get_evaluator_map():
    return {
        EvalTypes.BPMN_ROUTER: RouterEvaluator(),
        EvalTypes.BPMN_JSON_EDITS: JSONPatchTEDEvaluatorEdits(),
        EvalTypes.BPMN_JSON_GENERATION: JSONPatchTEDEvaluatorGeneration(),
        EvalTypes.EXPRESSION_GENERATION: ExpressionGeneration(),
        EvalTypes.BPMN_QA: QaEvaluator(),
        EvalTypes.BPMN_EXTENSION: ExtensionEvaluator(),
    }


async def main():
    parser = argparse.ArgumentParser(description="Run BPMN Evaluation")
    parser.add_argument("--run_id", type=str, default=datetime.now().strftime("%Y%m%d_%H%M%S"), help="Unique identifier for this evaluation run")
    parser.add_argument(
        "--evaluator",
        type=str,
        choices=EvalTypes.get_all_types(),
        default=DEFAULT_EVALUATOR,
        help="Specific evaluator to run",
    )
    args: Namespace = parser.parse_args()
    reset_logs()

    # Setup authentication to use LLM gateway
    await setup_llm_gateway_authentication()

    eval_task = EvaluationTask(run_id=args.run_id)

    # Get evaluator mapping
    evaluators = get_evaluator_map()

    # Register the selected evaluator(s)
    if args.evaluator == EvalTypes.ALL:
        for evaluator in evaluators.values():
            eval_task.register_evaluator(evaluator)
        print("Running all evaluators")
    else:
        eval_task.register_evaluator(evaluators[args.evaluator])
        print(f"Running only the {args.evaluator} evaluator")

    # Run evaluation
    print(f"Starting evaluation run with ID: {args.run_id}")
    await eval_task.run()
    print("Evaluation completed")


if __name__ == "__main__":
    asyncio.run(main())
