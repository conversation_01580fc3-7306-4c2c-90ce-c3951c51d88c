import datetime
import json
import os
from typing import Any, Callable, Dict, List, Tuple

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import RESULTS_DIR
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import BaseEvaluationResult
from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class EvaluationReporter:
    def __init__(self, results: List[BaseEvaluationResult], total_elapsed_time_ms: int, run_id: str) -> None:
        self.results = results
        self.total_elapsed_time_ms = total_elapsed_time_ms
        self.run_id = run_id
        self.timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.report_filename = f"evaluation_summary_{self.run_id}.html"
        self.report_path = os.path.join(RESULTS_DIR, self.report_filename)

        # Common evaluation columns (with highlight for eval score).
        self.common_eval_columns: List[Tuple[str, Callable[[BaseEvaluationResult], str]]] = [
            ("Eval Score", self.highlight_score),
            ("Threshold", lambda r: f"{r.threshold}%"),
            ("Execution Time", lambda r: self.format_time(r.execution_time)),
        ]

    def highlight_score(self, r: BaseEvaluationResult) -> str:
        """Return a span with a green or red class depending on pass/fail."""
        if r.eval_score >= r.threshold:
            return f'<span class="pass">{r.eval_score:.2f}%</span>'
        else:
            return f'<span class="fail">{r.eval_score:.2f}%</span>'

    def format_time(self, ms: float) -> str:
        seconds = ms / 1000
        return f"{seconds / 60:.2f} min" if seconds >= 60 else f"{seconds:.2f} sec"

    def log_to_app_insights(self) -> None:
        for report in self.results:
            telemetry_data = {
                "run_id": report.run_id,
                "dataset_id": report.dataset_id,
                "dataset_source": getattr(report, "dataset_source", None),
                "prompt_verbosity": getattr(report, "prompt_verbosity", "").upper() if hasattr(report, "prompt_verbosity") and report.prompt_verbosity else "",
                "llm_model_used": json.dumps(report.llm_model_details) if report.llm_model_details else "{}",
                "eval_score": report.eval_score,
                "threshold": report.threshold,
                "execution_time_ms": report.execution_time,
                "error": report.error or "None",
                "expected_result": report.expected_result or "None",
                "generated_result": report.generated_result or "None",
                "evaluation_type": getattr(report, "evaluation_type", "Unknown"),
            }
            if hasattr(report, "extra_columns") and callable(report.extra_columns):
                telemetry_data.update(report.extra_columns())
            LOGGER.log_custom_event("evaluation_result", telemetry_data)  # Temp. until we have more data from BPMNEvaluation
            LOGGER.log_custom_event("BPMNEvaluation", telemetry_data)

    def log_evaluation_summary(self) -> None:
        total_exceptions = sum(1 for report in self.results if report.error)
        summary_data = {
            "run_id": self.run_id,
            "total_datasets": len(self.results),
            "total_exceptions": total_exceptions,
            "total_elapsed_time_ms": self.total_elapsed_time_ms,
        }
        LOGGER.log_custom_event("evaluation_summary", summary_data)

    def _generate_table_header(
        self, extra_headers: List[str], reports: List[BaseEvaluationResult], has_dataset_source: bool = False, has_prompt_verbosity: bool = False
    ) -> str:
        # Include columns conditionally
        base_headers = ["Dataset ID", "Ground Truth Filename"]
        if has_dataset_source:
            base_headers.append("Dataset Source")
        if has_prompt_verbosity:
            base_headers.append("Prompt Verbosity")
        base_headers.append("User Prompt")

        common_headers = [col[0] for col in self.common_eval_columns]
        headers = base_headers + common_headers + extra_headers + ["Error"]
        return "<tr>" + "".join(f"<th>{h}</th>" for h in headers) + "</tr>"

    def _generate_table_row(self, report: BaseEvaluationResult, extra_fields: Dict[str, str], has_dataset_source: bool, has_prompt_verbosity: bool) -> str:
        ground_truth_file = f"/Autopilot.Samples/Dataset/{report.evaluation_type}/Expected/{report.dataset_id}"

        # Start with required cells
        base_cells = [report.dataset_id, ground_truth_file]

        # Add optional cells conditionally
        if has_dataset_source:
            base_cells.append(report.dataset_source or "")
        if has_prompt_verbosity:
            base_cells.append(report.prompt_verbosity.upper() if report.prompt_verbosity else "")

        # Add user prompt
        base_cells.append(report.user_prompt)

        # Evaluate the common columns
        common_cells = [func(report) for _, func in self.common_eval_columns]
        # Evaluate the extra columns or fallback to "N/A"
        extra_cells = [extra_fields.get(header, "N/A") for header in extra_fields.keys()]

        error_cell = f"<pre>{report.error if report.error else 'No Errors Found'}</pre>"
        row_cells = base_cells + common_cells + extra_cells + [error_cell]
        row_style = ' style="background-color: #ffcccc;"' if report.error else ""
        return f"<tr{row_style}>" + "".join(f"<td>{cell}</td>" for cell in row_cells) + "</tr>"

    def _format_model_details(self, model_details: Dict[str, Any]) -> str:
        if not model_details:
            return "No model details available"

        model_name = model_details.get("model_name", "N/A")
        deployment_name = model_details.get("deployment_name", "N/A")

        return f"{model_name} ({deployment_name})" if model_name != "N/A" else deployment_name

    def generate_html_report(self) -> None:
        # Group results by evaluation type
        groups: Dict[str, List[BaseEvaluationResult]] = {}
        for r in self.results:
            mode = getattr(r, "evaluation_type", "Unknown")
            groups.setdefault(mode, []).append(r)

        group_sections = ""
        for mode, reports in groups.items():
            # Use the first result's extra_columns to figure out extra headers
            extra_headers = {}
            for report in reports:
                if hasattr(report, "extra_columns") and callable(report.extra_columns):
                    extra_headers = report.extra_columns()
                    break
            extra_headers_list = list(extra_headers.keys())

            # Check if any reports in this group have dataset_source or prompt_verbosity
            has_dataset_source = any(hasattr(report, "dataset_source") and report.dataset_source for report in reports)
            has_prompt_verbosity = any(hasattr(report, "prompt_verbosity") and report.prompt_verbosity for report in reports)

            # Get model details from the first report or use N/A
            model_details = reports[0].llm_model_details if reports and reports[0].llm_model_details else None
            model_info = self._format_model_details(model_details) if model_details else "Unknown Model"

            header_html = self._generate_table_header(extra_headers_list, reports, has_dataset_source, has_prompt_verbosity)
            rows_html = ""
            for report in reports:
                extra_fields = {}
                if hasattr(report, "extra_columns") and callable(report.extra_columns):
                    extra_fields = report.extra_columns()
                rows_html += self._generate_table_row(report, extra_fields, has_dataset_source, has_prompt_verbosity)

            group_section = f"""
            <h2>{mode} Evaluations (Total: {len(reports)})</h2>
            <div class="model-info">
                <p><strong>Model:</strong> {model_info}</p>
            </div>
            <table>
                {header_html}
                {rows_html}
            </table>
            """
            group_sections += group_section

        total_exceptions = sum(1 for report in self.results if report.error)
        total_datasets = len(self.results)
        above_threshold = sum(1 for r in self.results if r.eval_score >= r.threshold)

        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evaluation Summary - {self.timestamp}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
            background: #f4f4f4;
        }}
        h1, h2 {{
            color: #333;
        }}
        .summary-section {{
            background: #eee;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }}
        .model-info {{
            background: #e0f7fa;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #00acc1;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background: #333;
            color: white;
        }}
        td {{
            overflow: hidden;
            text-overflow: ellipsis;
        }}
        pre, textarea {{
            white-space: pre-wrap;
            word-wrap: break-word;
            max-width: 100%;
        }}
        .fail {{
            color: red;
            font-weight: bold;
        }}
        .pass {{
            color: green;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Evaluation Summary Report</h1>
        <p><b>Generated on:</b> {self.timestamp.replace("_", " ")}</p>
        <div class="summary-section">
            <p><b>Run Id:</b> {self.run_id}</p>
            <p><b>Total Datasets Evaluated:</b> {total_datasets}</p>
            <p><b>Datasets Above Threshold:</b> {above_threshold}/{total_datasets}</p>
            <p><b>Exceptions:</b> {total_exceptions}/{total_datasets}</p>
            <p><b>Total Elapsed Time:</b> {self.format_time(self.total_elapsed_time_ms)}</p>
        </div>
        {group_sections}
    </div>
</body>
</html>"""

        os.makedirs(RESULTS_DIR, exist_ok=True)
        with open(self.report_path, "w", encoding="utf-8") as f:
            f.write(html_content)
        LOGGER.info(f"Report saved at {self.report_path}")

    def generate_report(self) -> None:
        self.log_evaluation_summary()
        self.log_to_app_insights()
        self.generate_html_report()
