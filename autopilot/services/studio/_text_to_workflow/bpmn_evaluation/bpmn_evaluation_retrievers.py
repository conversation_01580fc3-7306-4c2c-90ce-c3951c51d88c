import json
from typing import Any, Dict, List, Optional

from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import (
    BPMNDatasetEntry,
    BPMNJsonPatchDatasetEntry,
    ExpressionDatasetEntry,
    ExtensionDatasetEntry,
    QaDatasetEntry,
)
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_router_evaluator import RouterDatasetEntry
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import Tool
from services.studio._text_to_workflow.utils import paths, telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class BaseDatasetRetriever:
    def __init__(self, eval_type: str) -> None:
        self.eval_type = eval_type
        self.dataset_dir = paths.get_bpmn_evaluation_dataset_path() / eval_type

    def _read_file(self, file_path: str) -> str:
        """Read file content."""
        if not file_path:
            return ""

        try:
            full_path = self.dataset_dir / file_path
            with open(full_path, "r") as f:
                return f.read()
        except Exception as e:
            LOGGER.warning(f"Failed to read file {file_path}: {e}")
            return ""

    def load_raw_datasets(self) -> List[Dict[str, Any]]:
        """Load raw datasets."""
        dataset_file = self.dataset_dir / "datasets.json"
        with open(dataset_file, "r") as f:
            return json.load(f)


class BPMNDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def load_datasets(self) -> List[BPMNDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            expected_bpmn_file = entry.get("expectedBPMNFile", "")
            expected_bpmn_xml = self._read_file(expected_bpmn_file) if expected_bpmn_file else ""

            current_bpmn_file = entry.get("currentBPMNFile", "")
            current_bpmn_xml = self._read_file(current_bpmn_file) if current_bpmn_file else ""

            datasets.append(
                BPMNDatasetEntry(
                    id=entry["id"],
                    dataset_source=entry.get("dataset_source"),
                    user_prompt=entry["user_prompt"],
                    prompt_verbosity=entry["prompt_verbosity"],
                    expectedBPMNFile=expected_bpmn_file,
                    expectedBPMN_XML=expected_bpmn_xml,
                    currentBPMNFile=current_bpmn_file,
                    currentBPMN_XML=current_bpmn_xml,
                )
            )
        return datasets


class ExpressionDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def load_datasets(self) -> List[ExpressionDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            datasets.append(
                ExpressionDatasetEntry(
                    id=entry["id"],
                    userRequest=entry["userRequest"],
                    userRequestVerbosity=entry["userRequestVerbosity"],
                    currentExpression=entry["currentExpression"],
                    benchmarkExpression=entry["benchmarkExpression"],
                    availableVariables=entry["availableVariables"],
                    expressionLanguage=entry["expressionLanguage"],
                    expressionTypeDefinition=entry["expressionTypeDefinition"],
                    additionalTypeDefinitions=entry["additionalTypeDefinitions"],
                    source=entry["source"],
                )
            )
        return datasets


class BPMNJSONDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def _safe_parse_json(self, json_str: Optional[str]) -> Dict[str, Any]:
        if not json_str or not json_str.strip():
            return {}
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            LOGGER.error(f"Failed to parse JSON: {e} | Data: {json_str}")
            return {}

    def load_datasets(self) -> List[BPMNDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            expected_json_file = entry.get("expectedJSONPatchFile", "")
            expected_json_patch_str = self._read_file(expected_json_file) if expected_json_file else ""
            expected_json_patch = self._safe_parse_json(expected_json_patch_str)

            current_bpmn_file = entry.get("currentBPMNFile", "")
            current_bpmn_xml = self._read_file(current_bpmn_file) if current_bpmn_file else ""

            # Expected BPMN XML (ground truth)
            expected_bpmn_file = entry.get("expectedBPMNFile", "")
            expected_bpmn_xml = self._read_file(expected_bpmn_file) if expected_bpmn_file else ""

            datasets.append(
                BPMNJsonPatchDatasetEntry(
                    id=entry["id"],
                    dataset_source=entry.get("dataset_source"),
                    user_prompt=entry["user_prompt"],
                    prompt_verbosity=entry["prompt_verbosity"],
                    expectedJSONFile=expected_json_file,
                    expectedJSONPatch=expected_json_patch,
                    currentBPMNFile=current_bpmn_file,
                    currentBPMN_XML=current_bpmn_xml,
                    expectedBPMNFile=expected_bpmn_file,
                    expectedBPMN_XML=expected_bpmn_xml,
                )
            )
        return datasets


class QaDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def _parse_json(self, json_str: Optional[str]) -> Dict[str, Any]:
        if not json_str or not json_str.strip():
            return {}
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            LOGGER.error(f"Failed to parse JSON: {e} | Data: {json_str}")
            return {}

    def load_datasets(self) -> List[QaDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            current_bpmn_file = entry.get("currentBPMNFile", "")
            current_bpmn_xml = self._read_file(current_bpmn_file) if current_bpmn_file else ""

            datasets.append(
                QaDatasetEntry(
                    id=entry["id"],
                    userRequest=entry["userRequest"],
                    userRequestVerbosity=entry["userRequestVerbosity"],
                    currentExplanation=entry["currentExplanation"],
                    expectedExplanation=entry["expectedExplanation"],
                    currentBPMNFile=current_bpmn_file,
                    currentBPMNXml=current_bpmn_xml,
                    source=entry["source"],
                )
            )
        return datasets


class RouterDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def _safe_parse_json(self, json_str: Optional[str]) -> Dict[str, Any]:
        if not json_str or not json_str.strip():
            return {}
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            LOGGER.error(f"Failed to parse JSON: {e} | Data: {json_str}")
            return {}

    def load_datasets(self) -> List[RouterDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            expected_tools = []
            for tool_name in entry.get("expectedTools", []):
                try:
                    # Convert hyphenated names like "generate-bpmn" to uppercase with underscores "GENERATE_BPMN"
                    enum_tool_name = tool_name.upper().replace("-", "_")
                    tool = getattr(Tool, enum_tool_name)
                    expected_tools.append(tool)
                except (AttributeError, TypeError):
                    LOGGER.warning(f"Invalid tool name: {tool_name} for dataset {entry.get('id', 'unknown')}")
                    continue

            if not expected_tools:
                LOGGER.warning(f"No valid tools found for dataset {entry.get('id', 'unknown')}")
                continue

            # Check if image file is referenced in the dataset
            imageExists = False
            if entry.get("imageFile"):
                imageExists = True
                LOGGER.info(f"Dataset {entry['id']} has an image reference")

            dataset_entry = RouterDatasetEntry(id=entry["id"], userRequest=entry["userRequest"], expectedTools=expected_tools, imageExists=imageExists)
            datasets.append(dataset_entry)

        return datasets


class ExtensionDataset(BaseDatasetRetriever):
    def __init__(self, eval_type: str) -> None:
        super().__init__(eval_type)

    def _safe_parse_json(self, file_path: str) -> Dict[str, Any]:
        content = self._read_file(file_path)
        if not content or not content.strip():
            LOGGER.warning(f"Empty or missing file: {file_path}")
            return {}
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            LOGGER.error(f"Failed to parse JSON file {file_path}: {e}")
            return {}

    def load_datasets(self) -> List[ExtensionDatasetEntry]:
        raw_entries = self.load_raw_datasets()
        datasets = []
        for entry in raw_entries:
            try:
                # Process BPMN file
                current_bpmn_file = entry.get("currentBPMNFile", "")
                current_bpmn = self._read_file(current_bpmn_file) or "<BPMN XML goes here>"

                if not current_bpmn_file or not current_bpmn.strip():
                    LOGGER.warning(f"Missing or empty BPMN file for dataset {entry.get('id')}: {current_bpmn_file}")

                # Process expected extensions
                expected_extensions_file = entry.get("expectedExtensions", "")
                expected_extensions = self._safe_parse_json(expected_extensions_file) if expected_extensions_file else []

                if expected_extensions_file and not expected_extensions:
                    LOGGER.warning(f"Failed to parse expected extensions for dataset {entry.get('id')}: {expected_extensions_file}")

                # Process mock extension data
                mock_data_file = entry.get("mockExtensionData", "")
                mock_extension_data = self._safe_parse_json(mock_data_file) if mock_data_file else {}

                if mock_data_file and not mock_extension_data:
                    LOGGER.warning(f"Failed to parse mock extension data for dataset {entry.get('id')}: {mock_data_file}")

                # Set default structure if mock data is empty
                if not mock_extension_data:
                    mock_extension_data = {"processes": [], "agents": [], "actionApps": [], "connections": []}

                datasets.append(
                    ExtensionDatasetEntry(
                        id=entry["id"],
                        userRequest=entry["userRequest"],
                        currentBpmn=current_bpmn,
                        currentBPMNFile=current_bpmn_file,
                        expectedExtensions=expected_extensions,
                        expectedExtensions_file=expected_extensions_file if isinstance(expected_extensions_file, str) else None,
                        mockExtensionData=mock_extension_data,
                        mockExtensionData_file=mock_data_file if isinstance(mock_data_file, str) else None,
                    )
                )
            except Exception as e:
                LOGGER.error(f"Error processing dataset {entry.get('id', 'unknown')}: {str(e)}")
                import traceback

                LOGGER.error(f"Traceback: {traceback.format_exc()}")

        return datasets
