from pathlib import Path

import numpy as np
import typing_extensions as t

from services.studio._text_to_workflow.common.schema import CodeMethodDefinition, TargetFramework, UIObject
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage, TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BasePydanticRequest, BaseRequest, BaseResponse


# We require Pydantic models because UIObject is a self-referencing TypedDict.
# Pydantic models resolve the forward references internally.
# This does not happen in the case of FastAPI conversion of TypedDict.
class CodeGenerationRequestPydantic(BasePydanticRequest):
    typeDefinitions: str | None
    variables: str | None
    userRequest: str
    methodName: str | None
    className: str | None
    objects: list[UIObject] | str | None
    targetFramework: TargetFramework


class CodeGenerationRequest(BaseRequest):
    typeDefinitions: str | None
    variables: str | None
    userRequest: str
    methodName: str | None
    className: str | None
    objects: list[UIObject] | str | None
    targetFramework: TargetFramework


class CodeGenerationTaskResult(t.TypedDict):
    useful_methods: list[str]
    code: str
    imports: list[str]
    relevant: list[CodeMethodDefinition]
    planning_prompt: str
    planning_usage: TokenUsage
    code_prompt: str
    code_usage: TokenUsage


class CodeGenerationResponse(BaseResponse):
    code: str
    imports: list[str]
    usefulMethods: list[str]
    planningUsage: t.NotRequired[TokenUsageJson]
    codeUsage: t.NotRequired[TokenUsageJson]


class CodePlanStep(t.TypedDict):
    methods: list[str]
    text: str
    embedding: list[float]


class Error(t.TypedDict):
    lineNumber: str
    errorMessage: str
    errorCode: str


class FixCodeRequestPydantic(BasePydanticRequest):
    currentCode: str
    errors: list[Error]
    typeDefinitions: str
    objects: list[UIObject] | str
    targetFramework: TargetFramework


class FixCodeRequest(BaseRequest):
    currentCode: str
    errors: list[Error]
    typeDefinitions: str
    objects: list[UIObject] | str
    targetFramework: TargetFramework


class FixCodeTaskResult(t.TypedDict):
    reasoning: str
    fixedCode: str
    usage: TokenUsage


class FixCodeResponse(BaseResponse):
    reasoning: str
    fixedCode: str
    usage: t.NotRequired[TokenUsageJson]


class MethodIndexState(t.TypedDict):
    methods: list[CodeMethodDefinition]
    embeddings: np.ndarray
    type_method_name_to_id: dict
    type_full_name_to_id: dict


class CodeGenerationDemoRetrieverState(t.TypedDict):
    plan_demonstrations_index2filepath: dict[int, Path]
    embedding_plan_demonstration: list[np.ndarray]

    code_demonstrations_index2filepath: dict[int, Path]
    embedding_code_demonstrations: list[np.ndarray]

    fix_code_demonstrations_index2filepath: dict[int, Path]
    embedding_fix_code_demonstrations: list[np.ndarray]
