from services.studio._text_to_workflow.code_generation.code_generation_schema import (
    CodeGenerationRequest,
    CodeGenerationRequestPydantic,
    CodeGenerationResponse,
    FixCodeRequest,
    FixCodeRequestPydantic,
    FixCodeResponse,
)
from services.studio._text_to_workflow.code_generation.code_generation_task import CodeGenerationTask
from services.studio._text_to_workflow.utils import request_utils, telemetry_utils

TASK = CodeGenerationTask("prompt.yaml")
LOGGER = telemetry_utils.AppInsightsLogger()


@telemetry_utils.log_execution_time("code_generation_endpoint.init")
def init():
    pass


@telemetry_utils.log_execution_time("code_generation_endpoint.generate")
async def generate(request_pydantic: CodeGenerationRequestPydantic) -> CodeGenerationResponse:
    request: CodeGenerationRequest = request_pydantic.model_dump()  # type: ignore
    if request["variables"] is None or request["variables"] != "":
        # TODO: Refactor this prompt in code
        request["variables"] = "I have the following variables available:\n" + request["variables"] + "\n"  # type: ignore

    seed = request_utils.get_seed(request)

    result = await TASK.run(
        request["userRequest"],
        request.get("typeDefinitions", ""),  # type: ignore
        request["variables"],
        request.get("objects", []),  # type: ignore
        request["methodName"],
        request["className"],
        request["targetFramework"],
        seed,
    )
    code_generation_result: CodeGenerationResponse = {
        "code": result["code"],
        "imports": result["imports"],
        "usefulMethods": result["useful_methods"],
        "codeUsage": result["code_usage"].to_json(),
        "planningUsage": result["planning_usage"].to_json(),
    }

    return code_generation_result


@telemetry_utils.log_execution_time("fix_code_endpoint.generate")
async def generate_fix_code(request_pydantic: FixCodeRequestPydantic) -> FixCodeResponse:
    request: FixCodeRequest = request_pydantic.model_dump()  # type: ignore
    seed = request_utils.get_seed(request)
    result = await TASK.run_fix_code(
        request["currentCode"],
        request["errors"],
        request["typeDefinitions"],
        request["objects"],
        request["targetFramework"],
        seed,
    )

    return {"reasoning": result["reasoning"], "fixedCode": result["fixedCode"], "usage": result["usage"].to_json()}
