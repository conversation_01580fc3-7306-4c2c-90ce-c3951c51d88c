import pathlib

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from services.studio._text_to_workflow.code_generation import build_embeddings
from services.studio._text_to_workflow.code_generation.code_generation_schema import CodePlanStep, MethodIndexState
from services.studio._text_to_workflow.common.schema import CodeMethodDefinition, TargetFramework
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils import embeddings_db, errors, paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


class CodeGenerationRetriever:
    config_path: pathlib.Path
    methods_index: dict[TargetFramework, "MethodsIndex"]

    def __init__(self, config_path: pathlib.Path) -> None:
        self.config_path = config_path
        self.config = yaml_load(self.config_path)
        self.methods_index = {
            "Portable": MethodsIndex(self.config, "Portable"),
            "Windows": MethodsIndex(self.config, "Windows"),
        }
        self.db_hash = embeddings_db.get_embeddings_hash()

    def get_by_type_name(self, type_name: str, target_framework: TargetFramework) -> list[CodeMethodDefinition]:
        return self.methods_index[target_framework].get_by_type_name(type_name)

    def get_relevant(self, queries: list[CodePlanStep], target_framework: TargetFramework, k: int) -> list[CodeMethodDefinition]:
        relevant = list()
        documents, scores = self.methods_index[target_framework].search([query["embedding"] for query in queries], k=k)
        for document, score in zip(documents, scores, strict=False):
            document["similarity"] = score
            relevant.append(document)
        return relevant

    def get_relevant_demos_indices(self, query_embedding: list[float], demos_embeddings: list[list[float]], k: int = 5) -> list[int]:
        # Convert data to numpy arrays
        demos_embeddings_arr = np.array(demos_embeddings)
        query_embedding_arr = np.array(query_embedding)

        # Compute the cosine similarity scores between the query and the demos
        scores = cosine_similarity(demos_embeddings_arr, query_embedding_arr.reshape(1, -1)).flatten()

        # Get the indices of the top k scores
        top_k_indices = np.argpartition(scores, -k)[-k:]

        # we sort these indices starting with the highest rated among this subset
        top_k_indices_sorted = top_k_indices[np.argsort(scores[top_k_indices])[::-1]]

        return top_k_indices_sorted.tolist()

    def get_document(self, target_framework: TargetFramework, method_name) -> CodeMethodDefinition:
        return self.methods_index[target_framework].get_document(method_name)


class MethodsIndex(StateBuilder):
    config: dict
    target_framework: TargetFramework
    state_path: pathlib.Path

    def __init__(self, config: dict, target_framework: TargetFramework) -> None:
        self.config = config
        self.target_framework = target_framework
        self.state_path = paths.get_code_generation_retriever_path(target_framework) / "state.pkl"
        self.store = StateStore(self.state_path.as_posix(), self, lazy_load=settings.DEBUG_MODE)

    def build(self) -> tuple[MethodIndexState, dict]:
        state = build_embeddings.run(self.config, self.target_framework, show_progress_bar=settings.DEBUG_MODE)
        if not state["methods"]:
            raise errors.EmptyMethodsIndexError(self.target_framework)
        state_info = {"mehtods": state["methods"]}
        print(f"Built methods index for {self.target_framework} with size {len(state['methods'])}.")
        return state, state_info

    def get_by_id(self, id: int) -> CodeMethodDefinition:
        return self.store.state["methods"][id]

    def get_by_type_name(self, type_name: str) -> list[CodeMethodDefinition]:
        return [self.get_by_id(id) for id in self.get_id_by_full_name(type_name)]

    def search(self, queries_embedding: list[list[float]], k: int = 5) -> tuple[list[CodeMethodDefinition], list[float]]:
        queries_embedding_arr = np.array(queries_embedding).transpose()
        scores = self.store.state["embeddings"] @ queries_embedding_arr
        # sort scores and get top k for each query
        sorted_indices = np.argsort(scores, axis=0)
        sorted_scores = np.take_along_axis(scores, sorted_indices, axis=0)
        top_k_indices = sorted_indices[-k:]
        top_k_scores = sorted_scores[-k:]
        # flatten
        top_k_indices_flat = top_k_indices.flatten()
        top_k_scores_flat = top_k_scores.flatten()
        # sort again
        sorted_top_k_indices = np.argsort(top_k_scores_flat)
        top_k_indices_flat = top_k_indices_flat[sorted_top_k_indices]
        top_k_scores_flat = top_k_scores_flat[sorted_top_k_indices]
        # remove duplicates
        seen = set()
        documents, scores = [], []
        for index, score in zip(top_k_indices_flat, top_k_scores_flat, strict=False):
            if index not in seen:
                documents.append(self.store.state["methods"][index])
                scores.append(score)
                seen.add(index)
        return documents, scores

    def get_id_by_method_name(self, method_name) -> int:
        return self.store.state["type_method_name_to_id"].get(method_name, None)

    def get_id_by_full_name(self, full_name) -> list[int]:
        return self.store.state["type_full_name_to_id"].get(full_name, None)

    def get_document(self, method_name) -> CodeMethodDefinition:
        id = self.get_id_by_method_name(method_name)
        if id:
            return self.store.state["methods"][self.get_id_by_method_name(method_name)]
        else:
            return None
