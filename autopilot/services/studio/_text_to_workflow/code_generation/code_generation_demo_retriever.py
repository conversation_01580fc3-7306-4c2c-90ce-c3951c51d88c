import pathlib as pt

import numpy as np
import tqdm
from sklearn.metrics.pairwise import cosine_similarity

from services.studio._text_to_workflow.code_generation.code_generation_schema import CodeGenerationDemoRetrieverState
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


class CodeGenerationDemoRetriever(StateBuilder):
    _retriever_state_path: pt.Path = paths.get_code_generation_retriever_path(None) / "state.pkl"

    def __init__(self) -> None:
        # plan demonstrations
        self.static_plan_demonstrations = self._load_static_demonstrations("plan", "static_examples")
        # code demonstrations
        self.static_code_demonstrations = self._load_static_demonstrations("code", "static_examples")

        self.store = StateStore(self._retriever_state_path.as_posix(), self, lazy_load=settings.DEBUG_MODE)

    def _load_static_demonstrations(self, type_of_generation: str, type_of_demo: str) -> list[dict]:
        return [
            yaml_load(file_path) for file_path in paths.get_code_generation_dataset_path(type_of_generation, type_of_demo).glob("*.yaml") if file_path.is_file()
        ]

    def _load_demonstrations(self, type_of_generation: str, type_of_demo: str, show_progress_bar: bool = False) -> list[dict]:
        desc = f"Code generation {type_of_generation}/{type_of_demo} loaded:" + "{0}"
        demonstrations, index2filepath, included = [], {}, 0
        demonstration_paths = sorted(paths.get_code_generation_dataset_path(type_of_generation, type_of_demo).glob("*.yaml"))
        pbar = tqdm.tqdm(total=len(demonstration_paths), desc=desc, dynamic_ncols=True, disable=not show_progress_bar)

        for file_path in demonstration_paths:
            if file_path.is_file():
                demonstrations.append(yaml_load(file_path))
                index2filepath[len(demonstrations) - 1] = file_path
                included += 1
                pbar.update(1)
                pbar.set_description(desc.format(included))
        return demonstrations, index2filepath

    def _load_fix_code_demonstrations(self, show_progress_bar: bool = False) -> list[dict]:
        desc = "Fix code demonstrations loaded: {0}"
        demonstration_paths = sorted(paths.get_fix_code_dataset_path().glob("*.yaml"))
        pbar = tqdm.tqdm(total=len(demonstration_paths), desc=desc, dynamic_ncols=True, disable=not show_progress_bar)

        demonstrations, index2filepath, included = [], {}, 0
        for file_path in demonstration_paths:
            if file_path.is_file():
                demonstrations.append(yaml_load(file_path))
                index2filepath[len(demonstrations) - 1] = file_path
                included += 1
                pbar.update(1)
                pbar.set_description(desc.format(included))
        return demonstrations, index2filepath

    def build(self):
        embed_model = ModelManager().get_embeddings_model()
        # plan demonstrations
        plan_demonstrations, plan_demonstrations_index2filepath = self._load_demonstrations("plan", "dynamic_examples", show_progress_bar=settings.DEBUG_MODE)
        embedding_plan_demonstration = [embed_model.encode(demo["input"]) for demo in plan_demonstrations]

        # code demonstrations
        code_demonstrations, code_demonstrations_index2filepath = self._load_demonstrations("code", "dynamic_examples", show_progress_bar=settings.DEBUG_MODE)
        embedding_code_demonstrations = [embed_model.encode(demo["query"]) for demo in code_demonstrations]

        # fix code demonstrations
        fix_code_demonstrations, fix_code_demonstrations_index2filepath = self._load_fix_code_demonstrations(show_progress_bar=settings.DEBUG_MODE)
        embedding_fix_code_demonstrations = [embed_model.encode(demo["error"]["message"]) for demo in fix_code_demonstrations]

        state = CodeGenerationDemoRetrieverState(
            plan_demonstrations_index2filepath=plan_demonstrations_index2filepath,
            plan_demonstrations_embedding=embedding_plan_demonstration,
            code_demonstrations_index2filepath=code_demonstrations_index2filepath,
            code_demonstrations_embedding=embedding_code_demonstrations,
            fix_code_demonstrations_index2filepath=fix_code_demonstrations_index2filepath,
            fix_code_demonstrations_embedding=embedding_fix_code_demonstrations,
        )
        return state, {}

    def get_demo_by_id(self, type_of_generation, demo_id: int) -> dict:
        assert self.store.state is not None
        key = f"{type_of_generation}_index2filepath"
        demonstration_path = self.store.state[key][demo_id]
        return yaml_load(demonstration_path)

    def get_relevant_demos_indices(self, query_embedding: list[float], demos_embeddings: list[list[float]], k: int = 5) -> list[int]:
        # Convert data to numpy arrays
        demos_embeddings_arr = np.array(demos_embeddings)
        query_embedding_arr = np.array(query_embedding)

        # Compute the cosine similarity scores between the query and the demos
        scores = cosine_similarity(demos_embeddings_arr, query_embedding_arr.reshape(1, -1)).flatten()

        # Get the indices of the top k scores
        top_k_indices = np.argpartition(scores, -k)[-k:]

        # we sort these indices starting with the highest rated among this subset
        top_k_indices_sorted = top_k_indices[np.argsort(scores[top_k_indices])[::-1]]

        return top_k_indices_sorted.tolist()

    def get_demos(self, type_of_generation: str, query: str, k: int) -> list[int]:
        assert self.store.state is not None
        if type_of_generation == "plan_demonstrations":
            static_demonstrations = self.static_plan_demonstrations
        elif type_of_generation == "code_demonstrations":
            static_demonstrations = self.static_code_demonstrations
        else:
            static_demonstrations = []

        embed_model = ModelManager().get_embeddings_model()
        key = f"{type_of_generation}_embedding"
        demonstrations_embedding = self.store.state[key]
        query_embedding = embed_model.encode(query)
        indices_relevant = self.get_relevant_demos_indices(query_embedding=query_embedding, demos_embeddings=demonstrations_embedding, k=k)
        return {"demonstrations": [self.get_demo_by_id(type_of_generation, i) for i in indices_relevant] + static_demonstrations}
