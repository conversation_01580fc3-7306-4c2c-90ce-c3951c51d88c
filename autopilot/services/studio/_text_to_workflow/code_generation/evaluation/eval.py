import asyncio
import re
import warnings

from eval_cases import examples
from torchmetrics.text import CHR<PERSON>core

from services.studio._text_to_workflow.code_generation import code_generation_endpoint
from services.studio._text_to_workflow.code_generation.evaluation.code_generation_eval_traces_schema import (
    CodeGenerationInputSchema,
    CodeGenerationOutputSchema,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

warnings.filterwarnings("ignore")

chrf = CHRFScore(beta=2, n_char_order=6, n_word_order=0)


async def eval_examples(examples, eval_set_run_name):
    sum_distances = 0
    evals = []
    for i in range(len(examples)):
        try:
            input = examples[i]

            response = await code_generation_endpoint.generate(input)

            result_code = get_code(response["code"])
            benchmark_code = input["benchmarkCode"]
            distance = chrf(result_code, benchmark_code)
            sum_distances += distance
            if distance < 0.85:
                print("Wrong answer for example: {}".format(input["userRequest"]))
                print("Expected: {}\n Actual: {}".format(benchmark_code, result_code))
                print(distance)
            id = input["id"]
            del input["id"]
            eval = {
                "id": id,
                "name": input["userRequest"],
                "input": input,
                "output_key": "code",
                "result": {"output": {"code": result_code, "benchmark_expression": str(benchmark_code)}, "score": {"type": "number", "value": distance.item()}},
                "trace": {
                    "attributes": {"model_name": ModelManager().get_llm_model("code_generation_model", ConsumingFeatureType.CODE_GENERATION).name},
                    "name": f"{eval_set_run_name} - trace {input['userRequest']}",
                },
            }
            del eval["input"]["benchmarkCode"]
            evals.append(eval)
        except Exception as e:
            print("Exception for example: {}".format(input["userRequest"]))
            print(e)
    eval_set_run = {
        "name": eval_set_run_name,
        "input_schema": CodeGenerationInputSchema,
        "output_schema": CodeGenerationOutputSchema,
        "evals": evals,
        "id": "9a7321aa-56d8-4d6a-a0b2-bf5873d44272",
    }
    print(f"The average score {sum_distances / len(examples)}")
    return eval_set_run


def get_code(result):
    # get code between ```csharp code  ```
    match = re.search(r"```csharp(.*?)```", result, re.DOTALL)
    if match:
        extracted_code = match.group(1).strip()
    else:
        return result

    split_lines = [i for i in re.split("\n|(?<=;)", extracted_code) if i]
    # remove all comments
    return "\n".join(line.split("//", 1)[0].strip() for line in split_lines if not line.strip().startswith("//"))


async def main():
    code_generation_endpoint.init(True)

    await eval_examples(examples, "Eval code generation")


if __name__ == "__main__":
    asyncio.run(main)
