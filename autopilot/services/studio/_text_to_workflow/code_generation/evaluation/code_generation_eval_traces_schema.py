CodeGenerationInputSchema = [
    {"key": "typeDefinitions", "type": "string"},
    {"key": "variables", "type": "string"},
    {"key": "userRequest", "type": "string"},
    {"key": "methodName", "type": "string"},
    {"key": "className", "type": "string"},
    {"key": "objects", "type": "string"},
    {"key": "targetFramework", "type": "string"},
]

CodeGenerationOutputSchema = [{"key": "code", "type": "string"}, {"key": "benchmark_expression", "type": "string"}]
