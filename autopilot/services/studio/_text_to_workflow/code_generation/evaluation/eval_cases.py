# flake8: noqa

examples = [
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": "",
        "userRequest": "UnCheck option 'One' on Form",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n    # Screens\n    - Form\n    # Elements\n    - One",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'var uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Check("One", NCheckType.Uncheck);',
            'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Check("One", NCheckType.Uncheck);',
            'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Check("One", checkType: NCheckType.Uncheck);',
            'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Click("One", new ClickOptions() { ClickType = NClickType.Uncheck, MouseButton = NMouseButton.Left });',
            'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Click("One", new ClickOptions { ClickType = NClickType.Toggle, MouseButton = NMouseButton.Left });',
        ],
        "id": "77d7ec27-1154-4099-9e2f-61be23362167",
    },
    {
        "typeDefinitions": "",
        "variables": "",
        "userRequest": "Check option 'Two'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n    # Screens\n    - Form\n    # Elements\n    - One",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'UiTargetApp formScreen = uiAutomation.Open("Form", "MyApp");\nformScreen.Check("Two");',
            'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp");\nuiApp.Check("Two");',
            'var screen = uiAutomation.Open("Form", "MyApp");\nscreen.Check("Two", NCheckType.Check);',
        ],
        "id": "fcb54ff7-3509-496b-b61d-21f2913d08db",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    void Execute();\n}",
        "variables": "UiTargetApp uiApp",
        "userRequest": "Check if the button 'Submit Loan Application' is enabled",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ['bool isSubmitButtonEnabled = uiApp.IsEnabled("Submit Loan Application");'],
        "id": "d3787a43-df47-467e-9ef1-52512e57f261",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    void Execute();\n}",
        "variables": "UiTargetApp uiApp",
        "userRequest": "Get the InnerText of the 'Loan Rate'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'string loanRate = uiApp.GetText("Loan Rate");',
            'string loanRate = uiApp.GetText(new TargetAnchorableModel() { Reference = "Loan Rate", TextMethod = GetTextMethod.AttributeHtmlInnerText });',
        ],
        "id": "b6e4ddae-54e4-45ba-b8d5-c899ddc62f46",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    void Execute();\n}",
        "variables": "UiTargetApp uiApp",
        "userRequest": "'Highlight' the 'Submit Loan Application' button",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'uiApp.Highlight("Submit Loan Application");',
            'uiApp.Highlight("Submit Loan Application", KnownColor.Yellow, 3.0);',
            'uiApp.Highlight("Submit Loan Application", new HighlightOptions() { Color = KnownColor.Yellow, Duration = 3.0 });',
        ],
        "id": "cefe4f19-91a0-4790-91ce-4c4eddfc3a9e",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": "",
        "userRequest": "Use 'Home' screen attaching to it in 'MyApp'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ['var homeScreen = uiAutomation.Attach("Home", "MyApp");'],
        "id": "d71727c5-2274-49d4-bd54-49533b62fad3",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": "",
        "userRequest": "Open 'Home' screen in MyApp and save it into a var",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "  # Apps\n  MyApp:\n    # Screen\n  - Home",
        "targetFramework": "Windows",
        "benchmarkCode": ['var homeScreen = uiAutomation.Open("Home", "MyApp");'],
        "id": "693d1062-fd9d-4591-ba15-102c89888bfc",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": "",
        "userRequest": "Fill out the form: email: '<EMAIL>' loan amount requested: '10000' loan term: '5' Yearly income: '20000' age: '42'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "  # Apps\n  Uibank_Web:\n    # Screen\n  - Form:\n      # Elements\n    - Email\n    - LoanAmountRequested\n    - LoanTerm\n    - YearlyIncome\n    - Age",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'var app = uiAutomation.Open("Form", "Uibank_Web");\napp.TypeInto("Email", "<EMAIL>");\napp.TypeInto("LoanAmountRequested", "10000");\napp.TypeInto("LoanTerm", "5");\napp.TypeInto("YearlyIncome", "20000");\nuapp.TypeInto("Age", "42");'
        ],
        "id": "209c2e1d-c8b5-4ef5-87fc-050ea3b3d2ec",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": "",
        "userRequest": "Take screenshot of 'Quote' screen and save it to C:\myscreen.jpg",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "  # Apps\n  Uibank_Web:\n    # Screen\n  - Quote",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'var app = uiAutomation.Open("Quote", "Uibank_Web");\napp.TakeScreenshot("Quote", "C:\\\myscreen.jpg");',
            'var quoteScreen = uiAutomation.Open("Quote", "Uibank_Web");\nuiAutomation.TakeScreenshot(new TakeScreenshotOptions() {FileName = "C:\\\myscreen.jpg"});',
            'UiTargetApp quoteScreen = uiAutomation.Open("Quote", "Uibank_Web");\nquoteScreen.TakeScreenshot("Quote", "C:\\\myscreen.jpg");',
        ],
        "id": "93f49bbd-3b00-4199-8dad-10ced40f03a8",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp")',
        "userRequest": "'Hover' over 'Submit Loan Application'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  # Screens\n  - Form\n    # Elements\n    - Submit Loan Application",
        "targetFramework": "Windows",
        "benchmarkCode": ['uiApp.Hover("Submit Loan Application", new HoverOptions() { HoverTime = 1000, CursorMotionType = CursorMotionType.Instant });'],
        "id": "f688e4ca-9329-4245-b9a8-d75ea602e363",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Home", "MyApp")',
        "userRequest": "Drag button 'Submit Loan Application' onto the textfield 'Email'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  # Screen\n- Home:\n  # Elements\n  - Submit Loan Application\n  - Email",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'uiApp.DragAndDrop("Submit Loan Application", new TargetAnchorableModel() { TargetType = TargetType.SelectorBased, Reference = "Email" });'
        ],
        "id": "4d9041f4-ef26-461d-9cca-0bd4f81b60f7",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp")',
        "userRequest": "Take screenshot of 'loan rate' and save it to C:\loan_rate_screenshot.png.",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "  # Apps\n  MyApp:\n    #Screens\n    - Form\n      # Elements\n      - loan rate",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'uiApp.TakeScreenshot("loan rate", new TakeScreenshotOptions() { FileName = "C:\\\loan_rate_screenshot.png" });',
            'uiApp.TakeScreenshot("loan rate", "C:\\\loan_rate_screenshot.png");',
            'TakeScreenshotOptions options = new TakeScreenshotOptions() { FileName = "C:\\\loan_rate_screenshot.png" };\nuiApp.TakeScreenshot("loan rate", options);',
        ],
        "id": "84a30c3f-7960-448f-8859-7d4159686ab1",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp")',
        "userRequest": "Right 'Click' on 'Submit Loan Application'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  # Screens\n  - Form\n    # Elements\n    - Submit Loan Application",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'uiApp.Click("Submit Loan Application", new ClickOptions { MouseButton = NMouseButton.Right });',
            'uiApp.Click("Submit Loan Application", new ClickOptions() { MouseButton = NMouseButton.Right });',
        ],
        "id": "cf31db53-bf9f-49c5-8645-2053d2cd4c16",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp")',
        "userRequest": " Select 'Loan Term' 5",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  # Screens\n- Form:\n    # Elements\n  - Loan Term",
        "targetFramework": "Windows",
        "benchmarkCode": ['uiApp.SelectItem("Loan Term", "5");'],
        "id": "eea0a1c1-9a7f-4764-bce8-aff0fcb91722",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Form", "MyApp")',
        "userRequest": "Type '<EMAIL>' into 'Email' ",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  #Screens\n  - Form\n    # Elements\n    - Email",
        "targetFramework": "Windows",
        "benchmarkCode": ['uiApp.TypeInto("Email", "<EMAIL>");'],
        "id": "ab57262a-6c6e-4bb0-90eb-21d68a513dd6",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    IUiAutomationAppService uiAutomation;\n    void Execute();\n}",
        "variables": 'UiTargetApp uiApp = uiAutomation.Open("Home", "MyApp");',
        "userRequest": "Click on 'Products'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "# Apps\nMyApp:\n  # Screen\n- Home:\n    # Elements\n  - Products",
        "targetFramework": "Windows",
        "benchmarkCode": ['uiApp.Click("Products");'],
        "id": "14c1a5d8-9ed0-45e0-83f7-daa6d26e3a75",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    void Execute();\n}",
        "variables": "",
        "userRequest": "Get currently running jobs",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ["IEnumerable<OrchestratorJob> runningJobs = system.GetJobs();", "var runningJobs = system.GetJobs();"],
        "id": "16682a99-06b6-4413-8bff-0ae3a9c0f763",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    IUiAutomationAppService uiAutomation;\n}",
        "variables": "",
        "userRequest": "In Uibank, Click on the 'Products' button in the header menu, Click on the 'Loans' button in the dropdown menu, Click on the 'Apply For a Loan' button, Fill in the mandatory fields with valid inputs and loan amount less than 100000, Click on the 'Submit Loan Application' button, Verify that the 'loan rate' is '5%' ",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "  # Apps\n  Uibank:\n    # Screen\n  - Header:\n      # Elements\n    - ProductsButton\n  - ProductsDropdown:\n      # Elements\n    - LoansButton\n  - Loans:\n      # Elements\n    - ApplyForALoanButton\n    - SubmitLoanApplicationButton",
        "targetFramework": "Windows",
        "benchmarkCode": [
            'var app = uiAutomation.Open("Header", "Uibank");\napp.Click("ProductsButton");\napp = uiAutomation.Open("ProductsDropdown", "Uibank");\napp.Click("LoansButton");\napp = uiAutomation.Open("Loans", "Uibank");\napp.Click("ApplyForALoanButton");\napp.TypeInto("FirstName", "John");\napp.TypeInto("LastName", "Doe");\napp.TypeInto("LoanAmount", "50000");\napp.Click("SubmitLoanApplicationButton");\nstring loanRate = app.GetAttribute("LoanRate", new GetAttributeOptions() { Timeout = 5000 });\ntesting.VerifyExpression(loanRate == "5%");'
        ],
        "id": "ea4ea1d0-c166-422d-89d1-23ac6a26dacc",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, decimal randomNumber",
        "userRequest": "Generate a random number between 0 and 1000",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ["randomNumber = testing.RandomNumber(0, 1000);"],
        "id": "********-958d-4f31-bdb3-89fff73b5886",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, string loanRate",
        "userRequest": "Verify if 'loanRate' equals '5%' and take a screenshot if failed",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ['bool is5Percent = loanRate.Equals("5%");\ntesting.VerifyExpression(is5Percent, "", false, "", true, false);'],
        "id": "230df00d-5cd9-418b-82fe-1d0f8491d3ca",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, string loanRate",
        "userRequest": "Verify that the variable 'loanRate' contains the string 'foo'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ['testing.VerifyExpressionWithOperator(loanRate, Comparison.Contains, "foo");'],
        "id": "f0345ebd-ecc4-4d00-8150-ed2eef323aa4",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, string lastName",
        "userRequest": "Create a random 'Last Name' ",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ["lastName = testing.LastName();", "lastName = testing.RandomString(Case.UpperCase, 8);"],
        "id": "0adba637-e5e1-40bb-8562-2b4fb9610d40",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, string givenName",
        "userRequest": "Create a random 'Given Name'",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": ["givenName = testing.GivenName();", "givenName = testing.RandomString(Case.UpperCase, 8);"],
        "id": "97149e6e-dfd7-4f3f-b5cf-0494c9270331",
    },
    {
        "typeDefinitions": "class Workflow\n{\n    ITestingService testing;\n    void Execute();\n}",
        "variables": "ITestingService testing, DateTime randomDate",
        "userRequest": "Create a random date between 01.01.2020 and today",
        "methodName": "Execute",
        "className": "Workflow",
        "objects": "",
        "targetFramework": "Windows",
        "benchmarkCode": [
            "DateTime minDate = new DateTime(2020, 1, 1);\nDateTime maxDate = DateTime.Today;\n randomDate = testing.RandomDate(minDate, maxDate);",
            "DateTime startDate = new DateTime(2020, 1, 1);\nDateTime today = DateTime.Today;\nrandomDate = testing.RandomDate(startDate, today);",
            "randomDate = testing.RandomDate(new DateTime(2020, 1, 1), DateTime.Today);",
        ],
        "id": "ba044422-596f-4d26-b5e4-8d202dcfb8a5",
    },
]
