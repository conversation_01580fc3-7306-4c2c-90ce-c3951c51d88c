import argparse

from services.studio._text_to_workflow.code_generation import code_generation_task
from services.studio._text_to_workflow.common.state_store import clear_retrievers_cache
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time


@log_execution_time("code_generation_build")
def run_code_generation_build():
    clear_retrievers_cache()
    return code_generation_task.CodeGenerationTask("prompt.yaml")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("job", default="build", choices=["build"])
    args = ap.parse_args()
    if args.job == "build":
        run_code_generation_build()
    else:
        print("No action specified.")
