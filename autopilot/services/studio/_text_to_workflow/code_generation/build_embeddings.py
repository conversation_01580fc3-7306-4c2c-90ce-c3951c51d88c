import sqlite3

import tqdm

from services.studio._text_to_workflow.code_generation.code_generation_schema import CodeMethodDefinition, MethodIndexState
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths


def load_embeddings(target_framework: TargetFramework):
    db_path = paths.get_embeddings_path() / "embeddings.db"
    with sqlite3.connect(db_path) as conn:
        cursor = conn.execute("SELECT * FROM CodedEmbeddings WHERE TargetFramework = ?", (target_framework,))
        mapping = {}
        for i, col in enumerate(cursor.description):
            mapping[i] = col[0]

        items = []
        for row in cursor:
            item = {}
            for k, v in mapping.items():
                item[v] = row[k]
            items.append(item)
    return items


def run(config: dict, target_framework: TargetFramework, show_progress_bar: bool) -> MethodIndexState:
    items = load_embeddings(target_framework)
    model = ModelManager().get_embeddings_model()
    documents: list[CodeMethodDefinition] = []
    method_names: list[str] = []
    type_full_name_to_id = {}
    type_method_name_to_id = {}
    if show_progress_bar:
        items = tqdm.tqdm(items, dynamic_ncols=True)
    for item in items:
        id = len(documents)
        document: CodeMethodDefinition = {
            "id": id,
            "name": item["MethodName"].strip(),
            "packageName": item["PackageName"].strip(),
            "typeFullName": item["TypeFullName"],
            "typeMethodName": item["TypeMethodName"],
            "description": item["MethodComment"],
            "definitions": item["MethodDefinitions"],
            "additionalTypeDefinitions": item["AdditionalTypeDefinitions"],
            "namespaceImports": item["AdditionalTypeNamespaces"],
            "similarity": None,
        }
        if item["TypeFullName"] in type_full_name_to_id:
            type_full_name_to_id[item["TypeFullName"]].append(id)
        else:
            type_full_name_to_id[item["TypeFullName"]] = [id]
        type_method_name_to_id[item["TypeMethodName"]] = len(documents)
        method_names.append(document["typeMethodName"])
        documents.append(document)
    embeddings = model.encode_batch(method_names, 256)
    return {"methods": documents, "embeddings": embeddings, "type_full_name_to_id": type_full_name_to_id, "type_method_name_to_id": type_method_name_to_id}
