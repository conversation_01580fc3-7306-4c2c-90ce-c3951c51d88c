import json


class ObjectRepositoryElement:
    def __init__(self, reference: str, category: str, type: str, description: str, name: str):
        self.reference = reference
        self.type = type
        self.category = category
        self.description = description
        self.name = name
        self.id = 0
        self.children = []

    def _get_dom_element_type(self):
        element_type = self.type

        if self.type == "Input":
            element_type = "InputBox"
        else:
            element_type = self.type

        if element_type is None:
            element_type = self.category

        if element_type == "Element":
            if len(self.children) > 0:
                element_type = "Container"

        return element_type

    def to_dict(self):
        out = {}
        out["ElementType"] = self._get_dom_element_type()
        out["Label"] = self.name

        if self.id > 0:
            out["Id"] = self.id

        out["AbsoluteRegion"] = "0,0,0,0"
        out["ClientRegion"] = "0,0,0,0"
        out["reference"] = self.reference

        if self.children is not None and len(self.children) > 0:
            out["Children"] = []
            for item in self.children:
                item_dict = item.to_dict()
                out["Children"].append(item_dict)

        if self.description is not None and self.description != "":
            out["Text"] = self.description

        return out


class ObjectRepositoryParser:
    def __init__(self, options=None):
        self.options = options

    def parse_json_file(self, filepath):
        data = None
        with open(filepath, encoding="utf-8") as file:
            data = json.load(file)

        if data is not None:
            return self.parse(data)

    def _build_elem(self, item_dict):
        category = item_dict.get("type", None)
        type = item_dict.get("taxonomyType", item_dict.get("taxonomy_type", None))
        description = item_dict.get("description", None)
        name = item_dict.get("name", None)
        reference = item_dict.get("reference", None)
        elem = ObjectRepositoryElement(reference, category, type, description, name)

        children = item_dict.get("children", [])
        if children is None or len(children) == 0:
            return elem

        for item in children:
            child_elem = self._build_elem(item)
            elem.children.append(child_elem)

        return elem

    def parse(self, object_repository_json_item):
        element: ObjectRepositoryElement = self._build_elem(object_repository_json_item)

        def assign_id_bfs(node: ObjectRepositoryElement):
            current_id = 0
            nodes_to_traverse = [node]
            while len(nodes_to_traverse):
                current_node = nodes_to_traverse.pop(0)
                nodes_to_traverse.extend(current_node.children)
                current_node.id = current_id
                current_id = current_id + 1

        assign_id_bfs(element)

        dom = element.to_dict()
        return dom


if __name__ == "__main__":
    jsonFile = r"C:\work\repos\ml\studio\text_to_workflow\ui_automation\tests\data\object_repository\netsuite.json"
    or_parser = ObjectRepositoryParser()
    out = or_parser.parse_json_file(jsonFile)
