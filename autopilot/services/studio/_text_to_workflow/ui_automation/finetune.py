import copy
import os
import random
import re

import datasets
import torch
import transformers
from datasets import load_dataset
from peft import LoraConfig, PeftModel, get_peft_model, prepare_model_for_int8_training
from transformers import GenerationConfig, LlamaForCausalLM, LlamaTokenizer

DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

BASE_MODEL = "/workdir/llama/llama/llama-2-7b-hf"

CUTOFF_LEN = 4096
LORA_R = 8
LORA_ALPHA = 16
LORA_DROPOUT = 0.05
LORA_TARGET_MODULES = [
    "q_proj",
    "v_proj",
]

BATCH_SIZE = 1
MICRO_BATCH_SIZE = 1
GRADIENT_ACCUMULATION_STEPS = BATCH_SIZE // MICRO_BATCH_SIZE
LEARNING_RATE = 3e-4
TRAIN_STEPS = 350

FINETUNE_DATASET = "/workspaces/ML/studio/text_to_workflow/ui_automation/playground/finetune_dataset.json"
OUTPUT_DIR = "experiment/test_example"
EXPORT_DIR = os.path.join(OUTPUT_DIR, "export")

tokenizer = LlamaTokenizer.from_pretrained(BASE_MODEL)
tokenizer.pad_token_id = 0
tokenizer.padding_side = "left"
rng = random.Random(42)


def bulk_replace(string, pattern, mapping):
    """replace all occurences of mapping's keys to values in string based on pattern"""
    _mapping = {pattern.format(k): pattern.format(v) for k, v in mapping.items()}
    _pattern = re.compile("|".join(map(re.escape, _mapping.keys())))
    return _pattern.sub(lambda x: _mapping[x.group()], string)


def augment_datapoint(datapoint):
    ids = re.findall(r"Id=\"(\d+)\"", datapoint["input"])
    new_ids = rng.sample(range(max(map(int, ids))), len(ids))
    ids_mapping = dict(zip(ids, map(str, new_ids), strict=False))

    datapoint["input"] = bulk_replace(datapoint["input"], 'Id="{}"', ids_mapping)
    datapoint["output"] = bulk_replace(datapoint["output"], "({}", ids_mapping)

    datapoint.update(generate_and_tokenize_prompt(datapoint))
    datapoint.update(generate_and_tokenize_prompt(datapoint))
    return datapoint


def augment_func(datapoints_batch_dict):
    keys = list(datapoints_batch_dict.keys())
    batch_size = len(datapoints_batch_dict[keys[0]])

    datapoints_batch = [{} for __ in range(batch_size)]
    for k, values in datapoints_batch_dict.items():
        for i, v in enumerate(values):
            datapoints_batch[i][k] = v

    processed_datapoints = []
    for datapoint in datapoints_batch:
        processed_datapoints.append(augment_datapoint(copy.deepcopy(datapoint)))

    keys_out = ["input_ids", "labels", "attention_mask"]
    datapoints_batch_dict_out = {k: [] for k in keys_out}
    for datapoint in processed_datapoints:
        for k in keys_out:
            v = datapoint[k]
            datapoints_batch_dict_out[k].append(v)
    return datapoints_batch_dict_out


def keep_only_transform(batch):
    keys_out = ["input_ids", "labels", "attention_mask"]
    for k in list(batch.keys()):
        if k not in keys_out:
            del batch[k]
    return batch


def tokenize(prompt, add_eos_token=True, return_tensors=None):
    result = tokenizer(
        prompt,
        truncation=True,
        max_length=CUTOFF_LEN,
        padding=False,
        return_tensors=return_tensors,
    )
    if result["input_ids"][-1] != tokenizer.eos_token_id and len(result["input_ids"]) < CUTOFF_LEN and add_eos_token:
        result["input_ids"].append(tokenizer.eos_token_id)
        result["attention_mask"].append(1)

    result["labels"] = result["input_ids"].copy()

    return result


def generate_and_tokenize_prompt(data_point):
    # prompt = f"""[INST] <<SYS>>\n{data_point["instruction"]}\n<</SYS>>\n\n{data_point["input"]} [/INST]"""
    prompt = f"""{data_point["instruction"]}{data_point["input"]}"""
    encoded_prompt = tokenize(prompt)
    n_tokens = len(encoded_prompt["input_ids"])

    full_prompt = f"""{prompt}{data_point["output"]}"""
    tokenized_full_prompt = tokenize(full_prompt)
    assert n_tokens < len(tokenized_full_prompt["labels"]), f"{n_tokens}, {len(tokenized_full_prompt['labels'])}"
    tokenized_full_prompt["labels"][: n_tokens - 2] = [-100] * (n_tokens - 2)  # -100 is used for inhibiting the loss function on those tokens (the prompt)

    tokenized_full_prompt["full_prompt"] = full_prompt
    return tokenized_full_prompt


def train(split_type: str):
    """
    split_type: str - one of {"random", "fixed", "all"}
    """
    model = LlamaForCausalLM.from_pretrained(
        BASE_MODEL,
        load_in_8bit=True,
        torch_dtype=torch.float16,
        device_map="auto",
    )

    data = load_dataset("json", data_files=FINETUNE_DATASET)
    if split_type == "random":
        train_val = data["train"].train_test_split(test_size=1, shuffle=True, seed=42)
        train_data = train_val["train"].map(generate_and_tokenize_prompt)
        val_data = train_val["test"].map(generate_and_tokenize_prompt)
    elif split_type == "fixed":
        train_data = datasets.Dataset.from_dict(data["train"][:15]).map(generate_and_tokenize_prompt)
        val_data = datasets.Dataset.from_dict(data["train"][15:]).map(generate_and_tokenize_prompt)
    elif split_type == "all":
        train_data = data["train"].map(generate_and_tokenize_prompt)
        val_data = copy.deepcopy(train_data)
    else:
        raise ValueError(f"Invalid split_type: {split_type}")

    train_data.set_transform(augment_func)
    val_data.set_transform(keep_only_transform)

    model = prepare_model_for_int8_training(model)
    config = LoraConfig(
        r=LORA_R,
        lora_alpha=LORA_ALPHA,
        target_modules=LORA_TARGET_MODULES,
        lora_dropout=LORA_DROPOUT,
        bias="none",
        task_type="CAUSAL_LM",
    )
    model = get_peft_model(model, config)
    model.print_trainable_parameters()

    training_arguments = transformers.TrainingArguments(
        per_device_train_batch_size=MICRO_BATCH_SIZE,
        gradient_accumulation_steps=GRADIENT_ACCUMULATION_STEPS,
        warmup_steps=100,
        max_steps=TRAIN_STEPS,
        learning_rate=LEARNING_RATE,
        fp16=True,
        logging_steps=10,
        optim="adamw_torch",
        evaluation_strategy="steps",
        eval_steps=50,
        save_strategy="steps",
        save_steps=50,
        output_dir=OUTPUT_DIR,
        save_total_limit=3,
        load_best_model_at_end=True,
        report_to="tensorboard",
        #
        # do_eval=False,  # does not seem to work?
        remove_unused_columns=False,
        eval_accumulation_steps=1,
        per_device_eval_batch_size=MICRO_BATCH_SIZE,
    )

    data_collator = transformers.DataCollatorForSeq2Seq(tokenizer, pad_to_multiple_of=8, return_tensors="pt", padding=True)

    trainer = transformers.Trainer(
        model=model,
        train_dataset=train_data,
        eval_dataset=val_data,
        args=training_arguments,
        data_collator=data_collator,
    )
    model.config.use_cache = False

    model = torch.compile(model)
    trainer.train()
    model.save_pretrained(OUTPUT_DIR)


def export(base_model, lora_checkpoint_dir, output_dir):
    model = LlamaForCausalLM.from_pretrained(
        base_model,
        torch_dtype=torch.float16,
        device_map="auto",
    )
    model = PeftModel.from_pretrained(model, lora_checkpoint_dir)
    model.merge_and_unload()
    model.save_pretrained(output_dir)


def predict(prompt="Hello world!"):
    model = LlamaForCausalLM.from_pretrained(
        EXPORT_DIR,
        torch_dtype=torch.float16,
        device_map="auto",
    )

    generation_config = GenerationConfig(
        temperature=0,
        top_p=0.0,
    )

    encoded_prompt = tokenizer.encode(prompt)
    input_ids = encoded_prompt
    input_ids = torch.tensor(input_ids, device=DEVICE).reshape(1, -1)

    generation_output = model.generate(
        input_ids=input_ids,
        generation_config=generation_config,
        return_dict_in_generate=True,
        output_scores=True,
        max_new_tokens=100,
    )
    return tokenizer.decode(generation_output.sequences[0])[len(prompt) :]


if __name__ == "__main__":
    train("all")
    # export(BASE_MODEL, OUTPUT_DIR, EXPORT_DIR)
    # print(predict("Hello world!"))
    pass
