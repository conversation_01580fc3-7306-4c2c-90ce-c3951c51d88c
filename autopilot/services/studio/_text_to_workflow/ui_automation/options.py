opt = {
    "cv": {
        "url": "",
        "license": "",
        "tasks": {
            "compute_ocr": True,
            "compute_cv": True,
            "detect_tables": True,
            "detect_cells": True,
            "detect_relations": True,
            "ocr_use_accents": True,
            "extract_multi_level_relations": True,
            "detect_titles_tabs": True,
            "get_controls_descriptions": True,
        },
    },
    "gpt_config": {
        "type": "azure",
        "base": "https://uipath-openai.openai.azure.com/",
        "api_version": "2025-01-01-preview",
        "key": "",
        "request_timeout": 60,
        "merge_messages": False,
        "max_retries": 0,
    },
    "endpoints": {"default": "http://************:7860/v1"},
    "engine_config": {
        "supported_models": [
            "gpt-4o-2024-05-13",
            "gpt-4o-2024-08-06",
            "gpt-4o-2024-11-20",
            "gpt-4o-mini-2024-07-18",
            "gpt-4.1-nano-2025-04-14",
            "gpt-41-mini-2025-04-14",  # From autopilot-openai
            "gpt-4.1-mini-2025-04-14",  # From LLM Gateway
            "gpt-4.1-2025-04-14",
            "anthropic.claude-3-5-sonnet-20241022-v2:0",
            "anthropic.claude-3-7-sonnet-20250219-v1:0",
            "gemini-2.0-flash-001",
            "gemini-2.5-pro-exp-03-25",
            "OpenGVLab/InternVL2_5-78B-MPO",
            "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
        ],
        "default_engine": "gpt-4o-2024-08-06",
        "default_agent_use_engine": "gemini-2.0-flash-001",
        "temperature": 0.0,
        "max_tokens": 1000,  # default for autopilot, for uiagent set to 2000 in endpoint
        "stop": None,
        "top_p": 0.0,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        "structured_output_method": "json_schema",
    },
    "uipath-computer-use": {
        "planner": {
            "use_last_image": False,
            "use_chat_history": True,
            "number_history_steps_with_images": 2,  # for use_chat_history = True
            "use_review": False,
        },
        "grounder": {
            "model": {
                # "model_name": "OS-Copilot/OS-Atlas-Base-7B",
                # "model_name": "osunlp/UGround-V1-7B",
                # "base_url": "http://************:8009/v1",
                "model_name": "ByteDance-Seed/UI-TARS-1.5-7B",
                "base_url": "http://************:8007/v1",
                # "model_name": "Hcompany/Holo1-7B",
                # "base_url": "http://************:8006/v1",
                "api_key": "123",
            }
        },
    },
    "computer_use_engines": {
        "openai-computer-use": {"model_name": "computer-use-preview-2025-03-11", "resize_to_xga": True, "do_reasoning": True},
        "claude-computer-use": {
            "model_name": "anthropic.claude-3-7-sonnet-20250219-v1:0",
            "max_tokens": 100000,
            "resize_to_xga": True,
            "anthropic_beta_flag": "computer-use-2025-01-24",
            "max_chat_history_messages": 10,
            "max_chat_history_images": 10,
            "thinking": {"type": "enabled", "budget_tokens": 1024},
        },
    },
    "caching": {"enabled": False, "redis": {"connection-string": ""}},
    "prompt_options": {
        "restrict_dom_to_visible_elements": False,
        "is_ui_agent": False,
        "action_format": "xml",
        "max_dom_tokens": 10000,
        "max_url_size": 200,
        "crop_border_percentage": 0.1,
        "review_observation": True,
    },
    "dom": {"html_format": False, "display_containers_id": False, "output_select_tag": False},
}

autopilot_prompt_options = {
    "do_reflection": False,
    "is_ui_agent": False,
    "action_format": "xml",
}

agent_prompt_options = {
    "do_reflection": False,  # only first step
    "is_ui_agent": True,
    "action_format": "json",
    "restrict_dom_to_visible_elements": False,
    "review_observation": True,
    "summary_interval": 5,
}
agent_engine_options = {"structured_output": False, "structured_output_method": "function_calling"}

agent_caching_options = {"enabled": True, "redis": {"connection-string": ""}}

advice_model_options = {
    "use_images": True,
    "image_max_width": 1024,
    "image_max_height": 1024,
}
