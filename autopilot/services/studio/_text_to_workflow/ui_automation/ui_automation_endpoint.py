from copy import deepcopy

from pydantic import ValidationError

from services.studio._text_to_workflow.ui_automation import ui_automation_service
from services.studio._text_to_workflow.ui_automation.options import (
    agent_caching_options,
    agent_engine_options,
    agent_prompt_options,
    autopilot_prompt_options,
    opt,
)
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import UiAgentRequest
from services.studio._text_to_workflow.utils.errors import BadRequestError

autopilot_opt = deepcopy(opt)
autopilot_opt["prompt_options"].update(autopilot_prompt_options)
AUTOPILOT_TASK = ui_automation_service.UiAutomationService(autopilot_opt)

uiagent_opt = deepcopy(opt)
uiagent_opt["prompt_options"].update(agent_prompt_options)
uiagent_opt["engine_config"].update(agent_engine_options)
uiagent_opt["caching"].update(agent_caching_options)
UIAGENT_TASK = ui_automation_service.UIAgentAutomationService(uiagent_opt)


def init():
    global AUTOPILOT_TASK
    global UIAGENT_TASK


async def generate(request):
    response, _predict_info = await AUTOPILOT_TASK.predict(request)
    return response


async def agent_act_on_screen(request):
    try:
        uiagent_request = UiAgentRequest(**request)
    except ValidationError as e:
        raise BadRequestError(message=str(e.errors()))
    response, predict_info = await UIAGENT_TASK.predict(uiagent_request)
    return response, predict_info


async def extract(request):
    try:
        extract_request = ui_automation_service.ExtractInformationRequest(**request)
    except ValidationError as e:
        raise BadRequestError(message=str(e.errors()))
    response, predict_info = await UIAGENT_TASK.extract(extract_request)
    return response, predict_info


async def qa_screen(request, request_headers):
    response = await AUTOPILOT_TASK.qa_element(ui_automation_service.QaElementWithImageRequest(**request), request_headers)
    return response


async def qa_dom(request):
    response = await AUTOPILOT_TASK.qa_element_with_dom(ui_automation_service.QaElementWithDomRequest(**request))
    return response


async def get_element_description(request):
    response = await AUTOPILOT_TASK.get_element_description(ui_automation_service.QaGetElementDescriptionRequest(**request))
    return response


async def close_popup(request):
    response = await AUTOPILOT_TASK.close_popup(ui_automation_service.PopUpMatchRequest(**request))
    return response
