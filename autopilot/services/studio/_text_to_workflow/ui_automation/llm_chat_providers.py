import copy
import os
import time
from typing import List

import httpx
import langchain.chains
import langchain.prompts
import langchain.schema
from openai import RateLimitError

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.ui_automation import anthropic_engine
from services.studio._text_to_workflow.utils import errors, telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_gateway_model import LLMGatewayModel, LLMGeminiModel
from services.studio._text_to_workflow.utils.inference.llm_gateway_multi_model import LLMGatewayMultiModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time

LOGGER = telemetry_utils.AppInsightsLogger()


def get_gpt_config(engine_config, gpt_config):
    merged_config = {
        "deployment_name": engine_config.get("engine", engine_config["default_engine"]),
        "temperature": engine_config["temperature"],
        "max_tokens": engine_config["max_tokens"],
        "request_timeout": gpt_config.get("request_timeout", 60),
        "top_p": engine_config["top_p"],
        "frequency_penalty": engine_config["frequency_penalty"],
        "presence_penalty": engine_config["presence_penalty"],
        "max_retries": gpt_config.get("max_retries", 0),
        "openai_api_version": gpt_config["api_version"],
    }

    if "gpt" not in merged_config["deployment_name"]:
        # if not using gpt model, set to None
        del merged_config["openai_api_version"]

    # only set up openai properties if llm gateway is not used
    # else these will have default values configured in llm_gateway_model.py
    if not settings.USE_LLM_GATEWAY:
        if "AZURE_OPENAI_ENDPOINT" not in os.environ:
            merged_config["azure_endpoint"] = gpt_config.get("base", None)

        if "OPENAI_API_VERSION" not in os.environ:
            merged_config["openai_api_version"] = gpt_config.get("api_version", None)

        if "OPENAI_API_TYPE" not in os.environ:
            merged_config["openai_api_type"] = gpt_config.get("type", None)

        if "AZURE_OPENAI_API_KEY" not in os.environ:
            merged_config["api_key"] = gpt_config.get("key", None)

            # openai_api_key must be a valid string when using langchain
            if not merged_config["api_key"]:
                merged_config["api_key"] = "placeholder"

    return merged_config


def get_anthropic_config(engine_config, config):
    return {}


class LLMChat:
    def __init__(
        self,
        options: dict,
        consuming_feature_type: ConsumingFeatureType = ConsumingFeatureType.UI_AUTOMATION,
        response_format=None,
        structured_output_method: str | None = None,
    ):
        self.options = options
        self.consuming_feature_type = consuming_feature_type
        self.response_format = response_format
        self.structured_output_method = structured_output_method if structured_output_method else options["engine_config"]["structured_output_method"]

    @log_execution_time("UIAutomationPredict")
    async def _send_message(
        self,
        messages: List[langchain.schema.BaseMessage],
        predict_info: dict,
    ) -> langchain.schema.BaseMessage:
        # the LLMGatewayModel has a state based on the user that's making the request
        # so we need to create a new instance for each request
        gpt_config = get_gpt_config(self.options["engine_config"], self.options["gpt_config"])
        start_time = time.time()
        model_name = self.options["engine_config"].get("engine", self.options["engine_config"]["default_engine"])
        token_usage = None
        if "anthropic" in model_name or ("gemini" in model_name and settings.USE_LLM_GATEWAY):
            assert settings.USE_LLM_GATEWAY, f"{model_name} only supported in LLM Gateway"
            assert self.response_format is None, f"Structured output not supported for {model_name}, set structured_output=False in options"
            llm = LLMGatewayMultiModel(self.consuming_feature_type, **gpt_config)
            response = await llm.send_llm_request(messages)
            token_usage = {
                "input_tokens": response["usage"]["prompt_tokens"],
                "output_tokens": response["usage"]["completion_tokens"],
                "total_tokens": response["usage"]["total_tokens"],
            }
            response_message = langchain.schema.AIMessage(content=response["choices"][0]["message"]["content"])
        elif "gemini" in model_name:
            llm = LLMGeminiModel(self.consuming_feature_type, **gpt_config)
            if self.response_format:
                llm = llm.with_structured_output(self.response_format, method="json_mode")
            chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(messages)
            chat_chain = chat_prompt | llm
            response_message: langchain.schema.BaseMessage = await chat_chain.ainvoke({})
            token_usage = response_message.usage_metadata
        else:
            # openai model can be used within gateway or Azure OpenAI API
            # TODO: refactor to use ModelManager().get_llm_model()
            llm = LLMGatewayModel(self.consuming_feature_type, **gpt_config)
            if self.response_format:
                llm = llm.with_structured_output(self.response_format, method=self.structured_output_method)
            chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(messages)
            chat_chain = chat_prompt | llm
            response_message: langchain.schema.BaseMessage = await chat_chain.ainvoke({})
            if self.response_format:
                # Todo: fix this
                token_usage = {}
            else:
                token_usage = response_message.usage_metadata

        request_time = time.time() - start_time

        if self.response_format:
            assistant_message = str(response_message.model_dump())
        else:
            assistant_message = response_message.content

        predict_info["requests"] = predict_info.get("requests", []) + [
            {"model": model_name, "messages": messages, "time": request_time, "token_usage": token_usage, "assistant_message": assistant_message}
        ]
        return response_message

    async def send_message(
        self,
        messages: List[langchain.schema.BaseMessage],
        predict_info: dict,
        max_rate_limit_retries: int = 0,
    ) -> langchain.schema.BaseMessage:
        """Send message with retries for rate limiting error"""
        retry_wait_time = 5  # seconds
        retry_count = 0
        while True:
            try:
                llm_response = await self._send_message(messages, predict_info)
                return llm_response
            except RateLimitError:
                print("RateLimitError!!!")
                predict_info["RateLimitError"] = predict_info.get("RateLimitError", 0) + 1
                if retry_count >= max_rate_limit_retries:
                    # raise error if rate limit error occurs more than 3 times
                    raise errors.LLMGWRateLimit()
                retry_count += 1
                print(f"Retrying after {retry_wait_time} seconds")
                time.sleep(retry_wait_time)


class LLamaChat:
    def __init__(self, options):
        self.options = options
        self.llama_endpoint = self.options["engine_config"].get("llama2_endpoint", "http://10.10.80.8:8100")
        self.url = f"{self.llama_endpoint}/generate"
        # self.url = f"{self.llama_endpoint}/v1/completions"

    async def send_message(
        self,
        messages: List[langchain.schema.BaseMessage],
        predict_info,
    ) -> langchain.schema.BaseMessage:
        messsages_text = "".join([msg.content for msg in messages])  # TODO fix the messages prompt
        data = {
            "prompt": messsages_text,
            "use_beam_search": False,
            "n": 1,
            "temperature": 0,
            "max_tokens": 500,
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(self.url, json=data)
            model_response = response.json()
        response_text = model_response["text"][0]
        completion = response_text[len(messsages_text) :]
        completion = completion.strip()
        # return langchain.schema.BaseMessage to be consistent with other chat providers
        out_message = langchain.schema.AIMessage(role="assistant", content=completion)
        return out_message


class LLMAPIChat:
    """
    Call any open source LLM model which is deployed as an OpenAI compatible API.
    example: vllm serve
    """

    def __init__(self, model, endpoint, api_key=None, options=None):
        self.model = model
        self.endpoint = endpoint
        self.options = options

        from langchain_openai import ChatOpenAI

        if api_key is None:
            api_key = "123"
        self.llm = ChatOpenAI(base_url=self.endpoint, api_key=api_key, model=model)

    async def send_message(self, messages: List[langchain.schema.BaseMessage], predict_info: dict) -> langchain.schema.BaseMessage:
        start_time = time.time()
        response = await self.llm.ainvoke(messages)
        request_time = time.time() - start_time
        predict_info["requests"] = predict_info.get("requests", []) + [
            {
                "messages": messages,
                "model": self.model,
                "time": request_time,
                "token_usage": {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0},
                "assistant_message": response.content,
            }
        ]
        return response


class ClaudeChat:
    def __init__(self, options):
        self.options = options

    async def send_message(
        self,
        messages: List[langchain.schema.BaseMessage],
        predict_info: dict,
    ) -> langchain.schema.BaseMessage:
        engine = anthropic_engine.AnthropicEngine(self.options["engine_config"])
        completion = engine.chat(messages)
        out_message = langchain.schema.AIMessage(role="assistant", content=completion)
        return out_message


def get_model_name(options: dict, req_options: dict | None = None):
    if req_options is None:
        req_options = {}
    model_name = req_options.get("model_name", None)
    model = (
        model_name if (model_name is not None and model_name in options["engine_config"]["supported_models"]) else options["engine_config"]["default_engine"]
    )
    return model


def get_chat_provider(
    options, req_options: dict | None = None, consuming_feature_type: ConsumingFeatureType = ConsumingFeatureType.UI_AUTOMATION, response_format=None
):
    model = get_model_name(options, req_options)
    options = copy.deepcopy(options)
    options["engine_config"]["engine"] = model

    if "gpt" in model or "anthropic" in model or "gemini" in model:
        return LLMChat(options=options, consuming_feature_type=consuming_feature_type, response_format=response_format)
    elif "InternVL" in model or "Qwen" in model or "llama" in model:
        keys = ["temperature", "max_tokens"]
        model_options = {key: options["engine_config"][key] for key in keys}
        endpoint = options["endpoints"].get(model, options["endpoints"]["default"])
        return LLMAPIChat(model=model, endpoint=endpoint, api_key=None, options=model_options)
    else:
        raise ValueError("Not supported LMM model:", model)
