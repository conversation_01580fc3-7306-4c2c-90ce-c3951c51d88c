FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# Add workspace directory
RUN mkdir -p /workspace

# https://explainshell.com/explain?cmd=set+-eux
RUN set -eux; \
    apt-get update; \
    apt upgrade -y; \
    # https://askubuntu.com/questions/258219/how-do-i-make-apt-get-install-less-noisy/668859#668859
    # update the package manager itself and install apt-utils to prevent some warnings for later commands
    apt-get -o=Dpkg::Use-Pty=0 install -y --no-install-recommends apt apt-utils; \
    apt-get -o=Dpkg::Use-Pty=0 install -y --no-install-recommends libmagic1; \
    # opencv requirements - for uiautomation
    apt-get install ffmpeg libsm6 libxext6  -y; \
    # as time goes by we should add more stuff that we don't need to this list of packages to remove,
    # so that we minimize the attack surface of the resulting Docker image
    apt-get -o=Dpkg::Use-Pty=0 remove -y libc-dev-bin; \
    apt-get -o=Dpkg::Use-Pty=0 remove -y linux-libc-dev; \
    # clean up anything left dangling
    apt-get -o=Dpkg::Use-Pty=0 autoremove -y; \
    # update all the stuff!
    apt-get -o=Dpkg::Use-Pty=0 upgrade -y; \
    apt-get -o=Dpkg::Use-Pty=0 dist-upgrade -y; \
    # install python3.10
    apt-get -o=Dpkg::Use-Pty=0 install software-properties-common -y --no-install-recommends; \
    add-apt-repository ppa:deadsnakes/ppa -y; \
    apt-get -o=Dpkg::Use-Pty=0 install python3.10 -y --no-install-recommends; \
    apt-get -o=Dpkg::Use-Pty=0 install python3-pip -y --no-install-recommends; \
    apt-get -o=Dpkg::Use-Pty=0 install python3.10-venv -y --no-install-recommends; \
    # clean up anything left dangling
    apt-get -o=Dpkg::Use-Pty=0 autoremove -y; \
    # https://itsfoss.com/clear-apt-cache/
    apt-get clean; \
    # and done!
    test true
# Default python3.10
RUN ln -fs /usr/bin/python3.10 /usr/bin/python

# Setup and activate virtual environment
ENV PYTHONPYCACHEPREFIX="/workspace/.pycache"
ENV PYTHONPATH="/workspace/src"
ENV PYTHONUNBUFFERED="1"
ENV PYTHONIOENCODING="UTF-8"
ENV VIRTUAL_ENV="/workspace/venv"
ENV PATH="${VIRTUAL_ENV}/bin:$PATH"
RUN python -m venv ${VIRTUAL_ENV}
# Install python dependencies
RUN pip install --upgrade pip
COPY requirements.torch.txt /tmp/pip/requirements.torch.txt
COPY requirements.prod.txt /tmp/pip/requirements.prod.txt
COPY requirements.dev.txt /tmp/pip/requirements.dev.txt
RUN pip install --no-cache-dir -r /tmp/pip/requirements.torch.txt
RUN pip install --no-cache-dir -r /tmp/pip/requirements.prod.txt
RUN pip install --no-cache-dir -r /tmp/pip/requirements.dev.txt

# Install Instrumentation OS dependencies
ENV DEBIAN_FRONTEND=noninteractive
RUN set -eux; \
    apt-get update; \
    apt-get -o=Dpkg::Use-Pty=0 install -y --no-install-recommends fonts-powerline wget curl htop zsh vim tmux git git-lfs; \
    apt-get clean;

ENV DEBIAN_FRONTEND=dialog
ENV PYTHONPATH "/code"
WORKDIR "/code"