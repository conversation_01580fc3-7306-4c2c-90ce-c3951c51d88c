### Run local service
Configuration: `ML/studio/text_to_workflow/ui_automation/options.py`

You can choose between the models:

"anthropic.claude-3-5-sonnet-20241022-v2:0" ==> must use LLM_GATEWAY (which is by default)
"gpt-4o-2024-08-06" ==> can work with or without LLM_GATEWAY
"gpt-4o-2024-05-13"

you need to set the UIPATH_TOKEN in `ML/studio/text_to_workflow/core/config.py` (the token is expire every 1h)

If not using  LLM GATEWAY (only for gpt model, claude needs gateway):
- in `ML/studio/text_to_workflow/core/config.py` set `USE_LLM_GATEWAY=False`
- in `ML/studio/text_to_workflow/ui_automation/options.py`, add AZURE OPENAI DEPLOYMENT info in:

```json
"gpt_config": {
        "type": "azure",
        "base": "...",
        "version": "...",
        "key": ".."

}
```

environment variables for local service
```
// automatically add wait action at the end of each step (except wait one)
AGENT_ACT_WAIT: true/false 
// log the task request/response (except rawDOM since too big) into a single json file
AGENT_ACT_LOG_TASK: true/false
// log the raw doms (in a folder)
AGENT_ACT_LOG_RAWDOM: true/false
// log dir
AGENT_ACT_LOG_DIR: str
```
Use Python 3.12

Run the following powershell commands in the path where you cloned the ML repo:

```powershell
$MLpath = (pwd).Path
$env:PYTHONPATH="$MLpath;$MLpath\autopilot;$MLpath\computer_vision\release\uipath_cv_client"
python -m venv env
env\Scripts\activate.ps1
cd autopilot\services\studio\_text_to_workflow\ui_automation
pip install -r requirements.txt
python service.py
```


### Local development - connecting to a GPT instance

**There are two main ways to connect to a GPT instance: using LLM Gateway or manually connecting to a GPT instance**

By leaving all configurations on default, the service will try to connect to LLMGateway. For debugging, the service must be served either through `studio/text_to_workflow/service.py` or `studio/text_to_workflow/ui_automation/service.py` as a Flask context is required. Also, the default configuration requires a valid token from alpha.uipath.com for accessing LLMGateway.

To manually connect to a GPT instance, firstly edit `studio/text_to_workflow/ui_automation/options.py` and provide the credentials specific to the GPT instance that will be used. The last thing that needs to be done is setting the `USE_LLM_GATEWAY` flag in `studio/text_to_workflow/utils/configs.py` to `False`.

### Service

request_data

```
{
    "userTask": "what_user_want_to_do",
    "variables": {<value>:<type>},  // by default empty
    "input_variables": {<value>:<type>},  // by default empty
    "output_variables": {<value>:<type>},  // by default empty
    "title": "<webpage_title>",
    "url": "<webpage_url>",
    "rawDOM": {...}
    "image': "<image_base_64>"
}
```

resquest_response

```
{
    "step": {
        "Description": "<step_description>",
        "actions": [
            {
                "description": "<action_description>",
                "method": "<action_method>",
                "element_id: "<ui_element_id>",
                "variable": "<input_output_variable>",
                "default_value": "<variable_default_value>"
            }
        ]
    }
}
```
Examples:

```
{
     "method_type": "type_into",
     "element_id": "123",
     "variable": "$firstName",
     "default_value": "Joe"
}

{
     "method_type": "select",
     "variable": "$startDate",
     "element_id": "123",
     "default_value": "10/10/2000"
}

{
     "method_type": "get_text",
     "element_id": "123",
     "variable": "$price",
     "default_value": ""
}

{
     "method_type": "click",
     "element_id": "123",
     "variable": "",
     "default_value":  ""
}

{
     "method_type": "click",
     "element_id": "",
     "variable": "",
     "default_value":  ""
}
```

### Examples

1. With variables as information to perform the task

```
request data:
{
    "userTask": "Fill in the form using the variables",
    "variables": {
        "phone_number": "str",
        "role_in_company": "str",
        "last_name": "str",
        "email": "str",
        "first_name": "str",
        "address": "str"
    },
    "title": "Rpa Challenge",
    "url": https://www.rpachallenge.com/',
    "rawDOM": {...},
    "image": "image_base_64"
}

response:
{
    "step": {
        "Description": "Fill in the form with task variables",
        "actions": [
            {"description": "Type phone number", "method": "type_into(39,'$phone_number')"},
            {"description": "Type role in company", "method": "type_into(41,'$role_in_company')"},
            {"description": "Type last name", "method": "type_into(43,'$last_name')"},
            {"description": "Type email", "method": "type_into(45,'$email')"},
            {"description": "Type first name", "method": "type_into(49,'$first_name')"},
            {"description": "Type address", "method": "type_into(51,'$address')"},
        ]
    }
}
```

2. Without any information to perform the task, the model uses some values for placeholder.

```
request data:
{
    "userTask": "Fill in the form for last name, first name and email",
    "variables": {},
    "title": "Rpa Challenge",
    "url": https://www.rpachallenge.com/',
    "rawDOM": {...},
    "image": "image_base_64"
}

response:
{
    "step": {
        "Description": "Fill in the form for last name, first name and email",
        "actions": [
            {"description": "Type 'Doe' in the Last Name field", "method": "type_into(43, 'Doe')"},
            {"description": "Type 'John' in the First Name field", "method": "type_into(49, 'John')"},
            {"description": "Type '<EMAIL>' in the Email field", "method": "type_into(45, '<EMAIL>')"}
        ]
    }
}
```
