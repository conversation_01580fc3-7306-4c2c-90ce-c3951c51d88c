body,
code,
.form-select {
    font-size: 15px !important;
}

#side-bar-container {
    background-color: rgb(250, 250, 250);
}

#side-bar {
    padding-top: 10px;
    padding-bottom: 10px;
}

@media (min-width: 768px) {
    #side-bar {
        min-height: 100vh;

    }

    #side-bar-container {
        max-width: 380px;
    }
}

#preview-container {
    padding: 0;
}

/* #preview-container img {
    border-style: solid;
    border-color: rgba(22,135,190,0.9);
    border-width: 1px;  
} */

#image-container {
    /* DO NOT CHANGE THIS BODER WIDTH VALUE ==> IMPACT BOXES POSITION */
    position: relative;
    border-width: 0px;
}


#preview-container svg {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 100;
    top: 0;
    left: 0;
    fill: none;
}


svg polygon.action-box {
    background-color: rgba(223, 53, 11, 0.3);
    fill: rgba(223, 53, 11, 0.3);
    stroke-width: 3;
    stroke: rgba(223, 53, 11, 1.0);
}


svg polygon.cv-box[data-text="Button"] {
    background-color: none;
    fill: none;
    opacity: 0.3;
    stroke: rgb(0, 89, 255);
    stroke-width: 3;
    opacity: 1.0;
}


svg polygon.cv-box[data-text="Inputbox"] {
    background-color: none;
    fill: none;
    stroke: rgba(179, 190, 22, 0.9);
    stroke-width: 3;
    opacity: 1.0;
}

svg polygon.cv-box[box-type="text"] {
    background-color: rgba(167, 178, 184, 0.9);
    fill: rgba(167, 178, 184, 0.9);
    opacity: 0.5;
}

svg polygon.cv-box[data-text="Icon"] {
    background-color: rgba(179, 190, 22, 0.9);
    fill: rgba(179, 190, 22, 0.9);
    opacity: 0.3;
}

svg polygon.cv-box[data-text="ArrowButton"] {
    background-color: rgba(0, 7, 107, 0.9);
    fill: rgba(0, 7, 107, 0.9);
    opacity: 0.3;
}

svg polygon.cv-box[data-text="Checkbox"] {
    background-color: rgba(0, 7, 107, 0.9);
    fill: rgba(0, 7, 107, 0.9);
    opacity: 0.3;
}

svg polygon.cv-box[data-text="Radiobutton"] {
    background-color: rgba(0, 7, 107, 0.9);
    fill: rgba(0, 7, 107, 0.9);
    opacity: 0.3;
}

svg polygon.cv-box[data-text="CloseButton"] {
    background-color: rgba(0, 7, 107, 0.9);
    fill: rgba(0, 7, 107, 0.9);
    opacity: 0.3;
}


svg polygon.cv-box[data-text="Title"] {
    background-color: rgba(9, 238, 226, 0.9);
    fill: rgba(9, 238, 226, 0.9);
    opacity: 0.3;
}

svg polygon.cv-box[data-text="SectionTitle"] {
    background-color: rgba(9, 238, 226, 0.9);
    fill: rgba(9, 238, 226, 0.9);
    opacity: 0.3;
}


#annotation_text {
    position: absolute;
    z-index: 4;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 1px 0 rgb(0 0 0 / 90%);
    border-radius: 2px 2px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    padding: 0 10px;
    margin: -15px 0 0 0;
    transform: scale(0, 0);
    transition: transform 144ms ease-out;
    color: rgba(128, 0, 0, 0.9);
    font-family: "Courier New", monospace;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    white-space: pre;
}


.form-group {
    margin-top: 20px;
}

#file-info {
    margin-top: 30px;
    font-family: 'Courier New', Courier, monospace;
    word-wrap: break-word;
}

.mytooltip {
    position: relative;
    display: inline-block;
}

.mytooltip .tooltiptext {
    visibility: hidden;
    background-color: #888;
    width: 100px;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 110%;
    left: 50%;
    margin-left: -50px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
}

.mytooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #888 transparent transparent transparent;
}

.mytooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}


.row-height {
    padding-top: 50px;
    height: 100vh;
}

.left {
    height: 100%;
    overflow-y: scroll;
    padding-top: 10px;
}

.right {
    height: 100%;
    width: 100%;
    overflow: scroll;
    padding-top: 10px;
}

.action-block {
    margin-top: 12px;
}

.action-str {
    margin-bottom: 4px;
}


.parsed-xml {
    color: #000066;
}