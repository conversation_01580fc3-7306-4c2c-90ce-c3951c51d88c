var IMAGE_WIDTH;
var IMAGE_HEIGHT;
var SCALE = 1.0;
var SHOW_CV = false;
var STEP_IDX = 0;
var STEP_DATA = {};
var TASKS = [];
var TASK;
var TASK_DATA = {};
var FOLDER;
var FOLDERS = []


const BORDER_WIDTH = 3;
const UPLOAD_ALLOW_FOLDERS = ["demo", "issues"];

myStorage = window.localStorage;


function formatXml(xml) {
    var formatted = '';
    var reg = /(>)(<)(\/*)/g;
    xml = xml.replace(reg, '$1\r\n$2$3');
    var pad = 0;
    jQuery.each(xml.split('\r\n'), function (index, node) {
        var indent = 0;
        if (node.match(/.+<\/\w[^>]*>$/)) {
            indent = 0;
        } else if (node.match(/^<\/\w/)) {
            if (pad != 0) {
                pad -= 1;
            }
        } else if (node.match(/^<\w[^>]*[^\/]>.*$/)) {
            indent = 1;
        } else {
            indent = 0;
        }

        var padding = '';
        for (var i = 0; i < pad; i++) {
            padding += '  ';
        }

        formatted += padding + node + '\r\n';
        pad += indent;
    });


    formatted = formatted.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/ /g, '&nbsp;').replace(/\n/g, '<br />');

    return formatted;
}

function encode_url(s) {
    return s.replace(/&/g, '%26')
}

function copyFileName() {
    var copyText = $("#file-name").text();
    navigator.clipboard.writeText(copyText);
    $("#myTooltip").html("Copied!");
}

function moveOutTooltip() {
    $("#myTooltip").html("Copy");
}

function show_boxes() {
    $("#image").css({ "width": (IMAGE_WIDTH * SCALE).toFixed(2), "max-width": "" });
    $("#image-container").css({ "width": (IMAGE_WIDTH * SCALE + 2 * BORDER_WIDTH).toFixed(2), "max-width": "", "border-width": BORDER_WIDTH + "px" });

    var boxes_html = ""
    STEP_DATA["actions"].forEach(action => {
        if (action["valid"]) {
            const padding = 4;
            var box = action["box"];
            var x0 = box[0] - padding;
            var y0 = box[1] - padding;
            var x1 = box[2] + padding;
            var y1 = box[3] + padding;
            var vertices = [[x0, y0], [x1, y0], [x1, y1], [x0, y1]];
            var points = vertices.map(v => (v[0] * SCALE).toFixed(2).toString() + "," + (v[1] * SCALE).toFixed(2).toString())
            var points = points.join(" ")
            var box_type = "text";
            boxes_html += '<polygon stroke-dasharray="6,3" class="action-box" points="' + points + '" ' + 'data-text="' + action_str(action) + '" ' + 'box-type="' + box_type + '"/>'

        }
    });

    if (SHOW_CV) {
        STEP_DATA["cv"].forEach(box => {
            var vertices = box["boundingPoly"]["vertices"];
            var description = box["description"];
            var box_type = box["class"];
            var points = vertices.map(v => Math.round(v["x"] * SCALE).toString() + "," + Math.round(v["y"] * SCALE).toString())
            var points = points.join(" ")
            boxes_html += '<polygon class="cv-box" points="' + points + '" ' + 'data-text="' + description + '" ' + 'box-type="' + box_type + '"/>'
        });

    }
    $("#boxes").html(boxes_html)
}

function load_image() {
    $("#boxes").html("");
    var step_data = STEP_DATA;
    var image_base64 = step_data["image_base64"]
    var image_src = 'data:image/png;base64,' + image_base64;
    var image = new Image();

    image.onload = function () {
        IMAGE_WIDTH = image.width
        IMAGE_HEIGHT = image.height
        // const zoomValue = Math.round(scale * 100);
        const zoomValue = 100;
        $("#zoom-value").html(zoomValue.toString() + "%");
        $("#zoom-slider").attr("value", zoomValue);
        console.log("Image loaded");
        show_boxes();
    }

    image.src = image_src;
    $("#image").show()
    $("#image").attr("src", image_src);
}

function get_folders(callback) {
    $.get("folders", function (res) {
        FOLDERS = res;
        console.log("folders:", FOLDERS)
        var options = "";
        FOLDERS.forEach(folder => {
            options += '<option value="' + folder + '">' + folder + '</option>';
        });
        $("#select-folder").html(options);

        FOLDER = localStorage.getItem('folder');
        if (FOLDER == null || FOLDERS.indexOf(FOLDER) == -1) {
            FOLDER = FOLDERS[0];
            FOLDER = localStorage.setItem('folder', FOLDER);
        }
        console.log("folder:", FOLDER);
        $("#select-folder").val(FOLDER);
        callback();
    })
}

function get_tasks() {
    $.get("tasks?folder=" + FOLDER, function (res) {
        TASKS = res;
        console.log("tasks:", TASKS)
        var options = "";
        TASKS.forEach((task, i) => {
            options += '<option value="' + task + '">' + (i+1)+') ' + task + '</option>';
        });
        $("#select-task").html(options);

        TASK = localStorage.getItem('task');
        if (TASK == null || TASKS.indexOf(TASK) == -1) {
            TASK = TASKS[0];
            localStorage.setItem('task', TASK);
        }

        $("#select-task").val(TASK);

        show_task();
    })
}


function show_task(step_idx = -1) {
    $.get("task?folder=" + FOLDER + "&task=" + encode_url(TASK), function (res) {
        TASK_DATA = res;
        console.log("task_data:", TASK_DATA)
        $("#select-step").val(1);

        const number_steps = TASK_DATA["step_files"].length
        var options = "";
        TASK_DATA["step_files"].forEach((step, i) => {
            options += '<option value="' + i + '">Step ' + (i + 1) + '/' + number_steps + '</option>';
        });

        $("#select-step").html(options);
        $("#user-prompt").html(TASK_DATA["user_prompt"]);
        $("#editPrompt textarea").val(TASK_DATA["user_prompt"]);
        if (step_idx == -1) {
            STEP_IDX = 0;
        } else {
            STEP_IDX = step_idx;
        }
        $("#select-step").val(STEP_IDX);
        if (TASK_DATA["version"] == 1) {
            // no edit for legacy data
            $("#edit-actions-btn").hide();
            $("#edit-prompt-btn").hide();
        } else {
            $("#edit-actions-btn").show();
            $("#edit-prompt-btn").show();
        }
        show_step();
    })
}

function action_str(action) {
    var s = "";
    s += action["method"] + "(" + action["id"];
    if (action["variable"].length > 0) {
        s += ", " + action["variable"];
    }
    if (action["text"].length > 0) {
        s += ", " + action["text"];
    }
    s += ")";
    return s;
}


function show_actions() {
    var actions = "";
    STEP_DATA["actions"].forEach((action, i) => {
        actions += "<div class='action-block'>"
        actions += "<div class='action-str'><code>" + (i + 1) + ") " + action_str(action) + "</code></div>";
        actions += "<div><code>" + formatXml(action["target"]) + "</code></div>";
        actions += '<div><code class="parsed-xml">' + formatXml(action["parsed_target"]) + "</code></div>";
        actions += "</div>";

    });
    $("#actions").html(actions);

    const models = ["gpt35-turbo-16k", "gpt-4"]
    var predicted_step = "";
    models.forEach(model => {
        if (model in STEP_DATA["prediction"]) {
            var step_prediction = STEP_DATA["prediction"][model];
            predicted_step += "<div class='action-block'>" + model + ": ";
            predicted_step += "<span class='correct-actions'>" + step_prediction["correct_actions"] + "/" + step_prediction["actions"] + "</span>, " ;
            predicted_step += "<span class='incorrect-actions'>" + step_prediction["incorrect_actions"] + "</span>";
            predicted_step += "</div>";
            predicted_step += "<div><pre>" + formatXml(step_prediction["step"]) + "</pre></div>";
            predicted_step += "</div>";

        }
        
    })
    $("#predicted_step").html(predicted_step);
    
}


function show_step() {
    $.get("step?folder=" + FOLDER + "&task=" + encode_url(TASK) + "&step_idx=" + STEP_IDX, function (res) {
        STEP_DATA = res;
        console.log("step_data:", STEP_DATA);
        if (SHOW_CV && !("cv" in STEP_DATA)) {
            get_cv_data(function () {
                load_image();
            });
        } else {
            load_image();
            get_cv_data(function () { });
        }

        show_actions();

        var raw_dom_xml = formatXml(STEP_DATA["xml"]);
        $("#raw-dom").html(raw_dom_xml)
        var parsed_dom_xml = formatXml(STEP_DATA["parsed_xml"]);
        $("#parsed-dom").html(parsed_dom_xml);
        var actions_str = STEP_DATA["actions"].map((action) => action_str(action)).join("\n");
        $("#editActions textarea").val(actions_str);

        const site_url = STEP_DATA["site_url"];
        $("#site-url").attr("href", site_url);
        var site_url_short = site_url.split('?')[0];
        const max_url_len = 60;
        if (site_url_short.length > max_url_len) {
            site_url_short = site_url_short.slice(0, max_url_len) + "...";
        }
        $("#site-url").html(site_url_short);
    })
}

function clear_step() {
    $("#actions").html(null);
    $("#predicted_step").html(null);
    $("#raw-dom").html(null);
    $("#parsed-dom").html(null);
    $("#image").hide();
    $("#image").attr("src", null);
    $("#site-url").attr("href", null);
    $("#site-url").html(null);
}

function clear_task() {
    $("#user-prompt").html(null);
}


function get_cv_data(callback) {
    $.ajax({
        url: "/cv?folder=" + FOLDER + "&task=" + encode_url(TASK) + "&step_idx=" + STEP_IDX,
        type: 'GET',
        success: function (res) {
            STEP_DATA["cv"] = res;
            callback();
        },
        error: function (res) {
            console.log("Problem loading CV data");
        },
    });
}


$(document).ready(function () {
    // $("#ocr-refresh").hide();
    $("#select-folder").val(null);
    $("#select-task").val(null);
    $("#select-step").val(null);

    // get folders
    get_folders(function () {
        get_tasks();
    });


    $('#select-folder').on('change', function () {
        clear_task();
        clear_step();
        FOLDER = $("#select-folder").val();
        localStorage.setItem('folder', FOLDER);
        get_tasks();
    });


    $('#select-task').on('change', function () {
        clear_task();
        clear_step();
        TASK = $("#select-task").val();
        localStorage.setItem('task', TASK);

        show_task();
    });

    $('#select-step').on('change', function () {
        clear_step();
        STEP_IDX = parseInt($("#select-step").val());
        show_step();
    });

    $("#next-btn").click(function () {
        clear_step();
        if (STEP_IDX < TASK_DATA["step_files"].length - 1) {
            STEP_IDX += 1;
            $("#select-step").val(STEP_IDX);
            show_step();
        }
    });

    $("#prev-btn").click(function () {
        clear_step();
        if (STEP_IDX > 0) {
            STEP_IDX -= 1;
            $("#select-step").val(STEP_IDX);
            show_step();
        }
    });


    $("#updatePromptBtn").click(function () {
        var old_prompt = TASK_DATA["user_prompt"];
        var new_prompt = $("#editPrompt textarea").val();

        if (old_prompt === new_prompt) {
            alert("No change");
        } else {
            $.ajax({
                url: '/user_prompt',
                type: 'POST',
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({
                    "task": TASK,
                    "folder": FOLDER,
                    "user_prompt": old_prompt,
                    "new_prompt": new_prompt
                }),
                success: function (response) {
                    $('#editPrompt').modal('hide');
                    console.log("update user prompt:", response)
                    if (response["status"]) {
                        show_task(STEP_IDX);
                        alert("Update Succeeded!");
                    } else {
                        alert("Update Failed!");
                    }
                },
                error: function (response) {
                    $('#editPrompt').modal('hide');
                    alert("Update Failed!");
                },
            });

        }
    });


    $("#updateActionsBtn").click(function () {
        var actions = STEP_DATA["actions"].map((action) => action_str(action));
        var new_actions = $("#editActions textarea").val().split(/\r?\n/);
        new_actions = new_actions.map((s) => s.trim());

        console.log("Current actions:", actions.join("\n"));
        console.log("New actions:", new_actions.join("\n"));

        if (actions.toString() === new_actions.toString()) {
            alert("No change");
        } else {
            $.ajax({
                url: '/actions',
                type: 'POST',
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({
                    "task": TASK,
                    "step_idx": STEP_IDX,
                    "folder": FOLDER,
                    "actions": actions,
                    "new_actions": new_actions
                }),
                success: function (response) {
                    $('#editActions').modal('hide');
                    console.log("update user prompt:", response)
                    if (response["status"]) {
                        show_step();
                        alert("Update Succeeded!");
                    } else {
                        $('#editPrompt').modal('hide');
                        alert("Update Failed!");
                    }

                },
                error: function (response) {
                    $('#editActions').modal('hide');
                    alert("Update Failed!");
                },
            });

        }
    });


    $("#predictBtn").click(function () {
        $.get("prediction?folder=" + FOLDER + "&task=" + encode_url(TASK), function (res) {
            show_step();
        })
    });



    $("#showCV").click(function () {
        $(this).toggleClass('checked');
        if ($(this).hasClass('checked')) {
            if ("cv" in STEP_DATA) {
                SHOW_CV = true;
                show_boxes();
            } else {
                get_cv_data(function () {
                    SHOW_CV = true;
                    show_boxes();
                });
            }
        } else {
            SHOW_CV = false;
            show_boxes();
        }

    });


    $("#zoom-slider").on("input", function () {
        const zoomValue = this.value.toString() + "%";
        $("#zoom-value").html(zoomValue);
        SCALE = this.value / 100.0;
        show_boxes();
    });

    $("#boxes").on("mousemove", function (evt) {
        var evtarget = evt.target || evt.srcElement;
        var zspan = $("#annotation_text").get(0);
        if (evtarget.tagName.toLowerCase() == "polygon") {
            zspan.innerHTML = evtarget.getAttribute("data-text");
            zspan.classList.add("inview");

            zspan.style.top = evt.offsetY + "px";
            zspan.style.left = evt.offsetX + "px";
            if (evt.pageX < window.innerWidth / 2) {
                zspan.style.transform = 'translateX(15%)';
            } else {
                zspan.style.transform = 'translateX(-115%)';
            }

        } else {
            zspan.classList.remove("inview");
            zspan.style.removeProperty('transform');
        }
    })

});


