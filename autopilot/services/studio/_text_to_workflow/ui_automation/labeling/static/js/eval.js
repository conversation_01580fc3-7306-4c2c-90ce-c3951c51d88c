
function get_eval() {
    $.get("/eval_results", function (res) {
        var s = "";
        var tasks = res["tasks"];
        var models = ["gpt-4"];
        var result = res["result"];
        var models_result = res["models_result"];

        var row = "<tr  class='table-secondary'>";
        row += "<th score='row'>" + "</th>";
        row += "<td>" + "</td>";
        row += "<td>" + models_result[models[0]]["steps"] + "</td>";
        row += "<td>" + models_result[models[0]]["actions_label"] + "</td>";
        models.forEach(model => {
            row += "<td>" + models_result[model]["f1"].toFixed(2).toString() + "</td>";
            row += "<td>" + models_result[model]["actions_correct"] + "</td>";
            row += "<td>" + models_result[model]["actions_incorrect"] + "</td>";

        })
        row += "</tr>";
        s += row;

        tasks.forEach((task, i) => {
            var row = "<tr>";
            row += "<th score='row'>" + (i + 1) + "</th>";
            row += "<td>" + task + "</td>";
            row += "<td>" + result[task]["steps"] + "</td>";
            row += "<td>" + result[task]["actions_label"] + "</td>";
            models.forEach(model => {
                row += "<td>" + result[task][model]["f1"].toFixed(2).toString() + "</td>";
                row += "<td>" + result[task][model]["actions_correct"] + "</td>";
                row += "<td>" + result[task][model]["actions_incorrect"] + "</td>";
            })
            row += "</tr>";
            s += row;
        });

        $("#eval_table").html(s);
    })
};


$(document).ready(function () {
    get_eval();

});

