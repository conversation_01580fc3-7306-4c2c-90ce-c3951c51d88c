import os
import sys

sys.path.append(r"C:\work\repos\ml")

import asyncio
import base64
import glob
import json
import re
import xml.etree.ElementTree as ET
from collections import defaultdict
from copy import deepcopy

import cv2
import dotenv
import numpy as np
from flask import Flask, jsonify, render_template, request
from uipath_cv_client.dom_processing import DomSourceType, get_dom_tree_from_elements, parse_raw_dom

from services.studio._text_to_workflow.ui_automation.eval import eval_dataset
from services.studio._text_to_workflow.ui_automation.models.actions_model import ActionPredictionModel
from services.studio._text_to_workflow.ui_automation.options import opt

# Load a .env from the project directory (may contain credentials)
dotenv.load_dotenv()
if not opt["cv"]["license"]:
    opt["cv"]["license"] = os.environ["CV_KEY"]
if not opt["gpt_config"]["key"]:
    opt["gpt_config"]["key"] = os.getenv("AZURE_OPENAI_API_KEY")


class Encoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return super(Encoder, self).default(obj)


app = Flask(__name__)
app.json_encoder = Encoder

uia_model = ActionPredictionModel(opt)

data_folders = [
    ("dataset", r"C:\work\repos\Autopilot.Samples\UIAutomation\Dataset"),
    ("dataset_raw", r"C:\work\repos\Autopilot.Samples\UIAutomation\DatasetRaw"),
    # ("logdata", "/data/wingmanui/UIA07022024_extracted/"),
    ("logdata", r"C:\work\repos\logdata"),
]

defaultmodel = "gpt-4"
good = "wingman_Dataset_gpt-4o-2024-05-13_2024_12_19 16_02_39"
latest = "wingman_Dataset_gpt-4o-2024-05-13_2025_01_20 14_47_30"
root_eval_path = r"C:\work\repos\ml\studio\text_to_workflow\ui_automation"
eval_file_paths = {
    "dataset": os.path.join(root_eval_path, f"eval_outputs/{latest}.json"),
    "logdata": os.path.join(root_eval_path, f"eval_outputs/{latest}.html"),
    # "dataset_raw": os.path.join(root_eval_path, "eval_outputs/wingman_dataset_raw_gpt-4.json"),
}


def load_eval_results():
    eval_result_files = {
        "gpt-4": eval_file_paths["dataset"],
    }
    eval_results = {}
    for key, path in eval_result_files.items():
        if os.path.exists(path):
            with open(path, encoding="utf-8") as f:
                eval_results[key] = json.load(f)
        else:
            print("Not found result file:", path)
    print("Results loaded")
    return eval_results


def create_task_info(data_folder):
    task_names = os.listdir(data_folder)
    tasks = []
    for task_name in task_names:
        task_folder = os.path.join(data_folder, task_name)
        metadata_files = glob.glob(os.path.join(task_folder, "*metadata*.json"))
        with open(metadata_files[0]) as f:
            metadata = json.load(f)
            user_prompt = metadata["UserPrompt"]

        step_files = glob.glob(os.path.join(task_folder, "*step*.json"))
        step_files = [os.path.basename(f) for f in step_files]
        step_files = [f for f in step_files if "cv_data.json" not in f and "actions.json" not in f and "unused" not in f]
        step_files = [
            (
                int(re.findall(r"_step_\d+", step_file)[-1].split("_step_")[-1]),
                step_file,
            )
            for step_file in step_files
        ]
        step_files = sorted(step_files, key=lambda x: x[0])
        task_data = {
            "task": task_name,
            "user_prompt": user_prompt,
            "step_files": [x[1] for x in step_files],
        }
        tasks.append(task_data)
    return tasks


def create_data_info():
    data_info = {}
    for folder_name, data_folder in data_folders:
        data_info[folder_name] = {
            "data_folder": data_folder,
            "tasks": create_task_info(data_folder),
        }
    return data_info


data_info = create_data_info()
eval_results = {}


@app.route("/folders", methods=["GET"])
def get_folders():
    global data_info
    global eval_results
    data_info = create_data_info()
    eval_results = load_eval_results()
    return [folder for folder, _ in data_folders]


@app.route("/tasks", methods=["GET"])
def get_tasks():
    folder = request.args.get("folder")
    tasks = data_info[folder]["tasks"]
    return jsonify([task["task"] for task in tasks])


@app.route("/task", methods=["GET"])
def get_task_data():
    folder = request.args.get("folder")
    task_name = str(request.args.get("task"))
    found_tasks = list(filter(lambda t: t["task"] == task_name, data_info[folder]["tasks"]))
    if len(found_tasks) == 0:
        print("No task found for task", task_name)
        return jsonify({"message": "no task found for task: " + task_name}), 400
    task_data = found_tasks[0]
    return jsonify(task_data)


@app.route("/prediction", methods=["GET"])
def get_prediction():
    folder = request.args.get("folder")
    task_name = str(request.args.get("task"))
    found_tasks = list(filter(lambda t: t["task"] == task_name, data_info[folder]["tasks"]))
    if len(found_tasks) == 0:
        print("No task found for task", task_name)
        return jsonify({"message": "no task found for task: " + task_name}), 400
    _dataset, _result, log_info = asyncio.run(
        eval_dataset(uia_model, dataset_folder=data_info[folder]["data_folder"], options=opt, tasks=[task_name], do_screen_summary=False, retry_if_fails=False)
    )

    with open(eval_file_paths[folder], "r", encoding="utf-8") as f:
        log_data = json.load(f)
        log_data["tasks"][task_name] = log_info["tasks"][task_name]

    with open(eval_file_paths[folder], "w", encoding="utf-8") as f:
        json.dump(log_data, f)

    eval_results[defaultmodel]["tasks"][task_name] = log_info["tasks"][task_name]
    return {"success": True}


def dom_to_xml(elem, xml_parent):
    if "Children" in elem:
        children = elem["Children"]
        if len(children) > 0:
            for child in children:
                xml_child = ET.SubElement(xml_parent, child["ElementType"])
                for key in [
                    "Text",
                    "Id",
                    "AbsoluteRegion",
                    "ClientRegion",
                    "TextAnchor",
                ]:
                    if key in child and child[key] is not None and str(child[key]) != "":
                        xml_child.attrib[key] = str(child[key])

                if child.get("Attributes") is not None and "tag" in child["Attributes"]:
                    xml_child.attrib["Tag"] = child["Attributes"]["tag"]
                dom_to_xml(child, xml_child)


def find_target_dom(dom, target_id):
    if dom["Id"] == target_id:
        return dom
    if "Children" in dom:
        for child in dom["Children"]:
            found = find_target_dom(child, target_id)
            if found:
                return found
    else:
        return None


def remove_attribute(dom, keys):
    for key in keys:
        if key in dom.attrib:
            dom.attrib.pop(key)
    for child in dom:
        remove_attribute(child, keys)


def find_task(data_info, folder, task_name):
    found_tasks = list(filter(lambda t: t["task"] == task_name, data_info[folder]["tasks"]))
    if len(found_tasks) == 0:
        print("No task found for task name:", task_name)
        return None
    else:
        return found_tasks[0]


@app.route("/step", methods=["GET"])
def get_step_data():
    folder = request.args.get("folder")
    task_name = str(request.args.get("task"))
    step_idx = int(request.args.get("step_idx", 0))
    data_folder = data_info[folder]["data_folder"]
    task_data = find_task(data_info, folder, task_name)
    if task_data is None:
        return jsonify({"message": "no task found for task: " + task_name}), 400
    step_file = task_data["step_files"][step_idx]
    task_folder = os.path.join(data_folder, task_name)

    image = None
    step_file_path = os.path.join(task_folder, step_file)
    with open(step_file_path, encoding="utf-8") as f:
        step_json = json.load(f)

    image_base64 = step_json.get("ImageBase64")
    site_url = step_json["ExtractedDOM"]["Url"]
    site_title = step_json["ExtractedDOM"]["Title"]
    action_file_path = step_file_path[:-5] + "_actions.json"
    with open(action_file_path, encoding="utf-8") as f:
        recorded_actions = json.load(f)

    cv_data_path = os.path.join(step_file_path[:-5] + ".cv_data.json")
    if image_base64 and not os.path.exists(cv_data_path):
        image_blob = base64.b64decode(image_base64)
        image_np = np.fromstring(image_blob, np.uint8)
        image = cv2.imdecode(image_np, cv2.IMREAD_UNCHANGED)
    raw_dom = get_dom_tree_from_elements(
        step_json["ExtractedDOM"]["RawDOM"]["RawElements"],
        application_area=step_json.get("ApplicationArea"),
    )
    cloned_raw_dom = deepcopy(raw_dom)
    parsed_xml = parse_raw_dom(
        cloned_raw_dom,
        dom_type=DomSourceType.Driver,
        options=opt,
        image=image,
        file_path=step_file_path,
        use_cv_cache=True,
        combine_cv=True,
    )
    xml_root = ET.Element("Window")
    dom_to_xml(raw_dom, xml_root)

    actions = []
    for ra in recorded_actions:
        # "ScreenActions":[{"Action":"type_into","TypeIntoText":"UIPATH","ElementId":190}]}
        target_id = ra["ElementId"]
        method = ra["Action"]
        text = ra["TypeIntoText"]
        variable = ra.get("Variable", "")

        action = {"method": method, "text": text, "id": target_id, "variable": variable}
        action["valid"] = False
        action["target_dom"] = find_target_dom(raw_dom, target_id)
        target_elem = xml_root.find(f'.//*[@Id="{target_id}"]')
        if target_elem is not None:
            action["valid"] = True
            target_coords = [int(_) for _ in target_elem.attrib["AbsoluteRegion"].split(",")]
            box = [
                target_coords[0],
                target_coords[1],
                target_coords[0] + target_coords[2],
                target_coords[1] + target_coords[3],
            ]
            target_res = deepcopy(target_elem)
            remove_attribute(target_res, ["AbsoluteRegion", "ClientRegion", "TextAnchor"])

            action["target"] = ET.tostring(target_res, encoding="utf-8").decode("utf-8")
            action["box"] = box
        else:
            action["target"] = "None"

        parsed_xml_target = parsed_xml.find(f'.//*[@Id="{target_id}"]')
        if parsed_xml_target is not None:
            action["parsed_target"] = ET.tostring(parsed_xml_target, encoding="utf-8").decode("utf-8")
        else:
            action["parsed_target"] = "None"

        actions.append(action)

    xml_res = deepcopy(xml_root)
    # remove_attribute(xml_res, "AbsoluteRegion")

    step_prediction = {}

    for key, res in eval_results.items():
        if task_name in res["tasks"]:
            step_res = res["tasks"][task_name]["steps"][step_idx]
            step_prediction[key] = {
                "step": step_res["predicted_step"],
                "actions": step_res["expected_actions"],
                "correct_actions": step_res["correct_actions"],
                "incorrect_actions": step_res["incorrect_actions"],
            }

    step_data = {
        "site_url": site_url,
        "site_title": site_title,
        "image_base64": image_base64,
        "actions": actions,
        "xml": ET.tostring(xml_res, encoding="utf-8").decode("utf-8"),
        "parsed_xml": ET.tostring(parsed_xml, encoding="utf-8").decode("utf-8"),
        "prediction": step_prediction,
    }
    return jsonify(step_data)


def parse_cv_data(cv_data, score_thresh=0.7):
    boxes, scores, classes, labels, _flags, _masks, _flags_names = cv_data["controls"]
    cv_boxes = np.asarray(boxes)
    cv_scores = np.asarray(scores)
    cv_types = np.asarray(classes)
    control_types = labels

    cv_boxes = cv_boxes[cv_scores > score_thresh]
    cv_types = cv_types[cv_scores > score_thresh]
    cv_labels = [control_types[i] for i in cv_types]

    text_boxes = cv_data["text"][0][1:]
    texts = cv_data["text"][1][1:]

    all_boxes = []
    idx = 0
    for box, text in zip(cv_boxes, cv_labels, strict=False):
        x0, y0, w, h = box
        x1, y1 = x0 + w, y0 + h
        x0, y0, x1, y1 = int(x0), int(y0), int(x1), int(y1)
        word = {
            "boundingPoly": {
                "vertices": [
                    {"x": x0, "y": y0},
                    {"x": x1, "y": y0},
                    {"x": x1, "y": y1},
                    {"x": x0, "y": y1},
                ]
            },
            "description": text,
            "id": idx,
            "class": "control",
        }
        all_boxes.append(word)
        idx += 1

    for box, text in zip(text_boxes, texts, strict=False):
        x0, y0, x1, y1 = box
        x0, y0, x1, y1 = int(x0), int(y0), int(x1), int(y1)
        word = {
            "boundingPoly": {
                "vertices": [
                    {"x": x0, "y": y0},
                    {"x": x1, "y": y0},
                    {"x": x1, "y": y1},
                    {"x": x0, "y": y1},
                ]
            },
            "description": text,
            "id": idx,
            "class": "text",
        }
        all_boxes.append(word)
        idx += 1

    return all_boxes


@app.route("/cv", methods=["GET"])
def get_cv():
    folder = request.args.get("folder")
    task_name = str(request.args.get("task"))
    step_idx = int(request.args.get("step_idx", 0))
    data_folder = data_info[folder]["data_folder"]
    task_data = find_task(data_info, folder, task_name)
    if task_data is None:
        return jsonify({"message": "no task found for task: " + task_name}), 400
    step_file = task_data["step_files"][step_idx]
    task_folder = os.path.join(data_folder, task_name)

    step_file_path = os.path.join(task_folder, step_file)
    cv_data_path = os.path.join(step_file_path[:-5] + ".cv_data.json")
    if os.path.exists(cv_data_path):
        with open(cv_data_path, encoding="utf-8") as f:
            cv_data = json.load(f)

        all_boxes = parse_cv_data(cv_data)
        return jsonify(all_boxes)
    else:
        return jsonify({"message": "no cvdata for " + task_name}), 400


@app.route("/user_prompt", methods=["POST"])
def post_user_prompt():
    data = request.get_json()
    task_name = data["task"]
    folder = data["folder"]
    user_prompt = data["user_prompt"]
    new_prompt = data["new_prompt"]

    print("Update user_prompt with data:", data)

    if folder not in data_info:
        print("Update prompt error: folder not found")
        return {"status": False}

    task_data = find_task(data_info, folder, task_name)
    if task_data is None or task_data["user_prompt"] != user_prompt:
        print("Update prompt error:  task name or current user prompt is not valid")
        return {
            "status": False,
            "message": "task name or current user prompt is not valid",
        }

    # update
    task_folder = os.path.join(data_info[folder]["data_folder"], task_name)
    task_metadata_file = glob.glob(os.path.join(task_folder, "*metadata*.json"))[0]
    with open(task_metadata_file) as f:
        metadata = json.load(f)
    metadata["UserPrompt"] = new_prompt
    with open(task_metadata_file, "w", encoding="utf-8") as f:
        json.dump(metadata, f, separators=(",", ":"), ensure_ascii=False)

    print("Update metadata user prompt ok!")

    task_data["user_prompt"] = new_prompt
    return {"status": True}


def parse_action(action):
    # {"Action":"type_into","TypeIntoText":"UIPATH","ElementId":190}
    action = action.strip()
    start_paranthesis = action.index("(")
    end_paranthesis = action.rindex(")")
    method = action[:start_paranthesis].strip()
    parameters = action[start_paranthesis + 1 : end_paranthesis]

    if parameters is not None:
        parameters = parameters.strip().split(",")
        parameters = [x.strip() for x in parameters if x.strip() != ""]
    else:
        parameters = []

    element_id = int(parameters[0]) if len(parameters) > 0 and parameters[0].isdigit() else None
    variable = ""
    text = ""
    if method in ("type_into", "select"):
        if len(parameters) == 3:
            variable = parameters[1]
            text = parameters[2]
        elif len(parameters) == 2:
            if parameters[1].startswith("$"):
                variable = parameters[1]
            else:
                text = parameters[1]

    record_action = {
        "Action": method,
        "TypeIntoText": text,
        "ElementId": element_id,
        "Variable": variable,
    }
    return record_action


def compare_actions(actions1, actions2):
    if len(actions1) != len(actions2):
        return False
    for action1, action2 in zip(actions1, actions2, strict=False):
        if not all([action1.get(key, "") == action2.get(key, "") for key in ["ElementId", "TypeIntoText", "Action", "Variable"]]):
            return False

    return True


@app.route("/actions", methods=["POST"])
def post_actions():
    data = request.get_json()
    task_name = data["task"]
    step_idx = int(data["step_idx"])
    folder = data["folder"]
    actions = data["actions"]
    new_actions = data["new_actions"]

    print("Update actions with data:", data)

    if folder not in data_info:
        print("Update actions error: folder not found")
        return {"status": False}

    task_data = find_task(data_info, folder, task_name)
    if task_data is None:
        print("Update actions error:  task is found")
        return {"status": False, "message": "task not found"}

    # update
    parsed_current_actions = [parse_action(action) for action in actions]
    print(parsed_current_actions)

    parsed_new_actions = [parse_action(action) for action in new_actions]
    print(parsed_new_actions)

    data_folder = data_info[folder]["data_folder"]
    step_file = task_data["step_files"][step_idx]
    task_folder = os.path.join(data_folder, task_name)
    step_file_path = os.path.join(task_folder, step_file)

    action_file_path = step_file_path[:-5] + "_actions.json"
    with open(action_file_path, encoding="utf-8") as f:
        recorded_actions = json.load(f)

    if not compare_actions(recorded_actions, parsed_current_actions):
        print("Update actions error:  current actions do not match")
        print("recorded_actions:", recorded_actions)
        print("parsed_current_actions:", parsed_current_actions)
        return {
            "status": False,
            "message": "internal error, current actions do not match",
        }

    with open(action_file_path, "w", encoding="utf-8") as f:
        json.dump(parsed_new_actions, f, indent=4, ensure_ascii=False)

    print("Update actions prompt ok!")
    return {"status": True}


@app.route("/eval_results", methods=["GET"])
def get_eval_results():
    eval_results = load_eval_results()
    models = list(eval_results.keys())
    tasks = []

    output = defaultdict(lambda: dict())

    model_results = {}

    for key, res in eval_results.items():
        tasks.extend(list(res["tasks"].keys()))
        total_correct = 0
        total_incorrect = 0
        total_actions = 0
        total_steps = 0
        total_correct_steps = 0
        for task_name, task_res in res["tasks"].items():
            actions_correct = 0
            actions_incorrect = 0
            actions_label = 0
            steps_correct = 0
            for step_res in task_res["steps"]:
                actions_correct += step_res["correct_actions"]
                actions_label += step_res["expected_actions"]
                actions_incorrect += step_res["incorrect_actions"]
                if step_res["correct_actions"] == step_res["correct_actions"] and step_res["incorrect_actions"] == 0:
                    steps_correct += 1

            total_correct += actions_correct
            total_incorrect += actions_incorrect
            total_actions += actions_label
            total_steps += len(task_res["steps"])
            total_correct_steps += steps_correct

            recall = actions_correct / (actions_label + 1e-6)
            precision = actions_correct / (actions_correct + actions_incorrect + 1e-6)
            actions_f1 = recall * precision * 2 / (recall + precision + 1e-6)
            output[task_name]["steps"] = len(task_res["steps"])
            output[task_name]["actions_label"] = actions_label
            output[task_name][key] = {
                "f1": actions_f1,
                "steps_correct": steps_correct,
                "actions_correct": actions_correct,
                "actions_incorrect": actions_incorrect,
            }

        recall = total_correct / (total_actions + 1e-6)
        precision = total_correct / (total_correct + total_incorrect + 1e-6)
        actions_f1 = recall * precision * 2 / (recall + precision + 1e-6)
        model_results[key] = {
            "f1": actions_f1,
            "steps_correct": total_correct_steps,
            "steps": total_steps,
            "actions_correct": total_correct,
            "actions_incorrect": total_incorrect,
            "actions_label": total_actions,
        }

    tasks = sorted(list(set(tasks)))
    return {
        "tasks": tasks,
        "models": models,
        "result": output,
        "models_result": model_results,
    }


@app.route("/", methods=["GET"])
def index():
    return render_template("index.html")


@app.route("/eval", methods=["GET"])
def eval():
    return render_template("eval.html")


if __name__ == "__main__":
    app.json_encoder = Encoder
    app.run(host="0.0.0.0", port=6060)
