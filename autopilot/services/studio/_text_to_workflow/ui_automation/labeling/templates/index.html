<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="/static/css/style.css" />
    <!-- Latest compiled and minified JavaScript -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <script src="/static/js/script.js"></script>
  </head>

  <body>
    <div class="container-fluid">
      <!-- CONTROLS -->
      <div class="fixed-top">
        <div class="row align-items-center" style="background: #fafafa; padding: 4px;">
          <div class="col-1">
            <select id="select-folder" class="form-control form-select"></select>
          </div>
          <div class="col-3">
            <select id="select-task" class="form-control form-select"></select>
          </div>
          <div class="col-1">
            <select id="select-step" class="form-select"></select>
          </div>
          <div class="col-auto">
            <button type="button" id="prev-btn" class="btn btn-outline-secondary" style="width: 90px;  height: 34px;"><i class="bi bi-chevron-double-left" heigth="16"></i></button>
            <button type="button" id="next-btn" class="btn btn-outline-secondary" style="width: 90px; height: 34px;"><i class="bi bi-chevron-double-right" heigth="16"></i></button>
          </div>

          <div class="col-auto nav nav-tabs" id="nav-tab" role="tablist">
            <button class="nav-link active" id="nav-home-tab" data-bs-toggle="tab" data-bs-target="#nav-home" type="button" role="tab" aria-controls="nav-home" aria-selected="true">Image</button>
            <button class="nav-link" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="#nav-profile" type="button" role="tab" aria-controls="nav-profile" aria-selected="false">Raw DOM</button>
            <button class="nav-link" id="nav-contact-tab" data-bs-toggle="tab" data-bs-target="#nav-contact" type="button" role="tab" aria-controls="nav-contact" aria-selected="false">DOM</button>
          </div>

          <div class="col-auto">
            <input type="range" min="10" max="200" value="100" class="form-range" id="zoom-slider" style="width: 160px" />
          </div>
          <div class="col-auto">
            <label id="zoom-value" style="line-height: 100%">100%</label>
          </div>

          <div class="col-auto">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" value="" id="showCV" />
              <label class="form-check-label" for="showCV">CV</label>
            </div>
          </div>
        </div>
      </div>

      <!-- CONTENT -->
      <div class="row row-height">
        <div class="col-3 left">
          <div>
            <strong>User prompt:</strong>
            <button class="btn btn-light btn-sm" id="edit-prompt-btn" type="button" data-bs-toggle="modal" data-bs-target="#editPrompt">Edit</button>
          </div>
          <div id="user-prompt" style="margin-bottom: 12px;"></div>

          <div style="margin-bottom: 12px; overflow-wrap: break-word;">
            <strong>Website:</strong> <a id="site-url" target="_blank"></a>
          </div>

          <div style="margin-bottom: 12px;">
            <strong>Actions:</strong>
            <button class="btn btn-light btn-sm" id="edit-actions-btn" type="button" data-bs-toggle="modal" data-bs-target="#editActions">Edit</button>
          </div>
          <div style="margin-bottom: 12px;" id="actions"></div>
          
          <div style="margin-bottom: 12px;">
            <strong>Predictions:</strong>
            <button class="btn btn-light btn-sm" id="predictBtn">Predict</button>
            <div id="predicted_step"></div>
          </div>
          
        </div>

        <div id="preview-container" class="col-9 right">
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
              <div id="image-container">
                <img id="image" />
                <svg id="boxes" xmlns="http://www.w3.org/2000/svg"></svg>
                <span id="annotation_text"></span>
              </div>
            </div>
            <div class="tab-pane fade" id="nav-profile" role="tabpanel" aria-labelledby="nav-profile-tab">
              <div>
                <code id="raw-dom"></code>
              </div>
            </div>
            <div class="tab-pane fade" id="nav-contact" role="tabpanel" aria-labelledby="nav-contact-tab">
              <div>
                <code id="parsed-dom" class="parsed-xml"></code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- EDIT -->
      <!-- Modal -->
      <div class="modal fade" id="editPrompt" tabindex="-1" aria-labelledby="editPromptLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h1 class="modal-title fs-5" id="editModalLabel">Edit</h1>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="userPromptInput" class="form-label">User prompt</label>
                <textarea rows="3" class="form-control" id="userPromptInput"></textarea>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="updatePromptBtn">Save changes</button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="editActions" tabindex="-1" aria-labelledby="editActionsLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h1 class="modal-title fs-5" id="editActionsLabel">Edit</h1>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label for="actionsInput" class="form-label">Actions</label>
                <textarea rows="10" class="form-control" id="actionsInput"></textarea>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="updateActionsBtn">Save changes</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
