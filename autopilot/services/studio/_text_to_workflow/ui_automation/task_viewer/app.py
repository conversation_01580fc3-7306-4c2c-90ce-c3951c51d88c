import asyncio
import base64
import json
import os
from io import BytesIO

os.environ["AGENT_ACT_LOG_DIR"] = r"C:\work\agent_logs_2"
# os.environ["AGENT_ACT_LOG_DIR"] = r"C:\Users\<USER>\work\wingman\UIA\act_on_screen_errors"
import streamlit as st
from PIL import Image

from services.studio._text_to_workflow.ui_automation.ui_automation_endpoint import UIAGENT_TASK
from services.studio._text_to_workflow.ui_automation.utils import messages_utils

st.set_page_config(layout="wide")


def build_messages_from_request(request):
    """
    To create the action model prompt using hte request log from Studio Log
    """
    uia_state, _request_data = asyncio.run(UIAGENT_TASK.get_request_data(request=request))
    predict_info = {}
    messages = UIAGENT_TASK.uia_model.build_messages(uia_state, predict_info=predict_info)
    return messages


@st.cache_data
def load_task_names(folder_path: str) -> list:
    """Load and return a sorted list of task names (without file extension) from JSON files."""
    print(folder_path)
    if not os.path.exists(folder_path):
        return []
    task_names = []
    for task_folder in os.listdir(folder_path):
        # for filename in os.listdir(os.path.join(folder_path, task_folder)):
        #     if filename.endswith("trace.json"):
        #         task_name = filename[:-5]  # remove .json extension
        task_names.append(task_folder)

    return sorted(task_names)


@st.cache_data
def load_task(task_name: str, folder_path: str) -> list:
    """
    Load the content of a task from a JSON file for the given task name.
    If the loaded content is a dict (a single request log), it is wrapped in a list and augmented.
    """
    file_path = os.path.join(folder_path, task_name, f"{task_name}__trace.json")
    if not os.path.exists(file_path):
        return []
    with open(file_path, "r", encoding="utf-8") as f:
        task_data = json.load(f)
    if isinstance(task_data, dict):
        messages = build_messages_from_request(task_data["request"])
        task_data["messages"] = messages
        task_data["input_message"] = messages_utils.get_input_message(messages)
        return [task_data]
    return task_data


async def predict_async(messages):
    print("Send prediction for request messages")
    # Your async logic here
    predict_info = {}
    response = await UIAGENT_TASK.uia_model.chat_provider.send_message(messages, predict_info)
    return response.content


def run_async(func, *args, **kwargs):
    loop = asyncio.new_event_loop()
    try:
        return loop.run_until_complete(func(*args, **kwargs))
    finally:
        loop.close()


@st.cache_data
def decode_base64_to_bytes(base64_string: str) -> bytes:
    """Decode base64 image string into raw bytes, cache the result."""
    return base64.b64decode(base64_string)


def main():
    # Set the folder path containing JSON files.
    folder_path = os.getenv("AGENT_ACT_LOG_DIR", "path_to_your_folder")

    # --- Sidebar ---
    with st.sidebar:
        # Button to reload the task names (clears cache and resets selection).
        if st.button("Reload Tasks"):
            load_task_names.clear()  # Clear cache for task names.
            st.session_state.selected_task = None
            st.session_state.step_index = 1
            st.success("Tasks reloaded from disk!")

    # Load only task names for faster startup.
    task_names = load_task_names(folder_path)

    if not task_names:
        st.error("No tasks found in the folder!")
        return

    # Initialize session state variables if they don't exist.
    if "selected_task" not in st.session_state:
        st.session_state.selected_task = None
    if "step_index" not in st.session_state:
        st.session_state.step_index = 1

    # --- Sidebar: Task Selection ---
    with st.sidebar:
        task_name = st.selectbox("Select a Task", options=task_names)

        # Button to reload the selected task.
        if st.button("Reload Selected Task"):
            load_task.clear()  # Clear cache for task content.
            st.session_state.step_index = 1
            # st.experimental_rerun()

        # Reset step index if a new task is selected.
        if task_name != st.session_state.selected_task:
            st.session_state.selected_task = task_name
            st.session_state.step_index = 1

        # Load the selected task's content only now.
        task_steps = load_task(task_name, folder_path)

        # Navigation buttons.
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Previous"):
                st.session_state.step_index = max(1, st.session_state.step_index - 1)
        with col2:
            if st.button("Next"):
                st.session_state.step_index = min(len(task_steps), st.session_state.step_index + 1)

        st.write(f"**Current Step:** {st.session_state.step_index} / {len(task_steps)}")

        # --- Display the user task (if available) ---
        if task_steps and len(task_steps) > 0:
            # Assuming the 'userTask' is stored in the request of the first step.
            user_task = task_steps[0].get("request", {}).get("userTask")
            if user_task:
                st.info(f"**User Task:** {user_task}")

    # --- Main Container: Display Image & Prompt ---
    if task_name:
        step_index = st.session_state.step_index
        if not task_steps:
            st.error("No data found for the selected task!")
            return

        step_data = task_steps[step_index - 1]  # Adjust for 0-based indexing.

        # Create tabs for different views.
        tab1, tab2, tab4 = st.tabs(["Prediction", "Requests", "Test"])

        with tab1:
            # Decode the base64 image (using cache) and open it with Pillow.
            image_bytes = decode_base64_to_bytes(step_data["image"])
            image = Image.open(BytesIO(image_bytes))
            st.image(image, caption=f"Step {step_index} Image", use_container_width=True)
            response_output = step_data.get("response", "")
            st.json(response_output)

        with tab2:
            if "requests" in step_data:
                request_tabs = st.tabs([f"request_{i + 1}" for i in range(len(step_data["requests"]))])
                for i, request in enumerate(step_data["requests"], 0):
                    with request_tabs[i]:
                        prompt_content = request["prompt"]
                        st.code(prompt_content)

                        llm_response = request["assistant_message"]
                        st.text(llm_response)

        with tab4:
            if st.button("Predict"):
                messages = step_data.get("messages")
                if messages is not None:
                    prediction_response = run_async(predict_async, messages)
                else:
                    prediction_response = "No messages found in the task data."
                st.write("**Prediction Response:**")
                st.text(prediction_response)


if __name__ == "__main__":
    main()
