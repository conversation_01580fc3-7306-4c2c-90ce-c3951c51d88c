import argparse
import os
from typing import Named<PERSON>uple

from uipath_cv_client.helpers import intersection_over_predicted

from services.studio._text_to_workflow.ui_automation.qa_semantic.eval import QAEvalDatapoint


class SemanticTargetingScoreResults(NamedTuple):
    accuracy: list[float]
    accuracy_positives_fraction: list[float]
    accuracy_negatives_fraction: float
    filtered_req_ratio: float
    filtered_req_ratio_positives: float
    filtered_req_ratio_negatives: float
    scores_point: float
    iop_thresholds: list[float]
    per_class_tps: dict[str, list[float]] | None = None
    per_class_correct_points: dict[str, int] | None = None
    per_class_total: dict[str, int] | None = None


def is_center_inside_box(expected_box, predicted_box):
    x1_pred, y1_pred, x2_pred, y2_pred = predicted_box
    x1_exp, y1_exp, x2_exp, y2_exp = expected_box

    x_pred_center, y_pred_center = (x1_pred + x2_pred) / 2, (y1_pred + y2_pred) / 2
    return x1_exp <= x_pred_center and x_pred_center <= x2_exp and y1_exp <= y_pred_center and y_pred_center <= y2_exp


def calculate_score(predictions_paths: list[str], iops: list[float] | None = None) -> SemanticTargetingScoreResults:
    if not iops:
        iops = [0.1, 0.5, 0.9]
    total = 0
    tps = [0] * len(iops)
    total_neg = 0
    total_pos = 0
    tps_neg = 0
    tps_pos = [0] * len(iops)
    content_filtered_pos = 0
    content_filtered_neg = 0
    debug_images = []
    correct_points = 0

    per_class_tps = {}
    per_class_total = {}
    per_class_correct_points = {}

    for predictions_path in predictions_paths:
        for prediction_filepath in os.listdir(predictions_path):
            with open(os.path.join(predictions_path, prediction_filepath), "r") as f:
                prediction = QAEvalDatapoint.model_validate_json(f.read())

            if prediction.error_message:
                print(f"Error in prediction {prediction_filepath}: {prediction.error_message}")

            if prediction.expected_box_type not in per_class_tps:
                per_class_tps[prediction.expected_box_type] = [0] * len(iops)
                per_class_total[prediction.expected_box_type] = 0
                per_class_correct_points[prediction.expected_box_type] = 0

            per_class_total[prediction.expected_box_type] += 1

            total += 1

            if prediction.expected_box is None:
                total_neg += 1
                if prediction.content_filter is True:
                    assert prediction.predicted_box is None, "inconsistency between content_filter True and predicted_box not None"
                    content_filtered_neg += 1
                if prediction.predicted_box is None:
                    tps_neg += 1
                    for i in range(len(iops)):
                        tps[i] += 1
                    correct_points += 1
                continue
            total_pos += 1
            if prediction.content_filter is True:
                content_filtered_pos += 1
            if not prediction.predicted_box:
                continue
            iop = intersection_over_predicted(prediction.expected_box, prediction.predicted_box)
            point_intersect = is_center_inside_box(prediction.expected_box, prediction.predicted_box)
            for i, iop_threshold in enumerate(iops):
                if iop > iop_threshold:
                    tps[i] += 1
                    tps_pos[i] += 1
                    per_class_tps[prediction.expected_box_type][i] += 1

            if point_intersect:
                correct_points += 1
                per_class_correct_points[prediction.expected_box_type] += 1

            if iop < 0.1:
                debug_images.append(prediction_filepath)

    if total == 0:
        scores_total = [-1.0] * len(iops)
        filtered_req_ratio = -1.0
        scores_point = -1
    else:
        scores_total = [tp / total for tp in tps]
        filtered_req_ratio = (content_filtered_pos + content_filtered_neg) / total
        scores_point = correct_points / total
    if total_pos == 0:
        scores_pos = [-1.0] * len(iops)
        filtered_req_ratio_pos = -1.0
    else:
        scores_pos = [tp_pos / total_pos for tp_pos in tps_pos]
        filtered_req_ratio_pos = content_filtered_pos / total_pos

    if total_neg == 0:
        score_neg = -1.0
        filtered_req_ratio_neg = -1.0
    else:
        score_neg = tps_neg / total_neg
        filtered_req_ratio_neg = content_filtered_neg / total_neg

    for box_type in per_class_total:
        if per_class_total[box_type] == 0:
            continue
        for box_idx, _ in enumerate(iops):
            per_class_tps[box_type][box_idx] /= per_class_total[box_type]
        per_class_correct_points[box_type] /= per_class_total[box_type]

    return SemanticTargetingScoreResults(
        scores_total,
        scores_pos,
        score_neg,
        filtered_req_ratio,
        filtered_req_ratio_pos,
        filtered_req_ratio_neg,
        scores_point,
        iops,
        per_class_tps,
        per_class_correct_points,
        per_class_total,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "-p",
        "--predictionpaths",
        nargs="+",
        help="Prediction paths",
        default=["/workdir_st/eval_data_results/st_eval_v2_human_run1"],
    )

    args = parser.parse_args()

    predictions_paths = args.predictionpaths
    iop_thresholds = [0.1, 0.5, 0.9]
    results = calculate_score(predictions_paths, iop_thresholds)
    for threshold, score in zip(results.iop_thresholds, results.accuracy, strict=False):
        print(f"Accuracy (IoP >= {threshold:.2f}): {(100 * score):.2f}")
    print(f"Percent of content filtered requests - {(100 * results.filtered_req_ratio):.2f}")
    for threshold, score in zip(results.iop_thresholds, results.accuracy_positives_fraction, strict=False):
        print(f"Accuracy positives (IoP >= {threshold:.2f}): {(100 * score):.2f}")
    print(f"Percent of content filtered requests among positives - {(100 * results.filtered_req_ratio_positives):.2f}")
    print(f"Accuracy negatives: {(100 * results.accuracy_negatives_fraction):.2f}")
    print(f"Percent of content filtered requests among negatives - {(100 * results.filtered_req_ratio_negatives):.2f}")
    print(f"Total score at centered point is {results.scores_point}")

    print("-" * 20)
    if results.per_class_tps and results.per_class_correct_points and results.per_class_total:
        for box_type in results.per_class_tps:
            print("Total no. samples for", box_type, "is", results.per_class_total[box_type])
            if box_type in results.per_class_correct_points:
                print(f"Total score centered point for {box_type} is {(100 * results.per_class_correct_points[box_type]):.2f}")
            for threshold, score in zip(results.iop_thresholds, results.per_class_tps[box_type], strict=False):
                print(f"Accuracy (IoP >= {threshold:.2f}) for {box_type} is {(100 * score):.2f}")
            print("-" * 20)
            print()
