# flake8: noqa
qa_prompt_system_message = """You are an Ui Assistant expert that helps with identifying UI elements in a screen, based on a human description.
Screens are given as a DOM which contains elements like buttons <Button>, texts <Texts>, InputBox <InputBox> etc. An element is uniquely identified by his ID.
Your input will be a screen and a human description. Your task is to return the element Id of the element which matches the best human description. In case there is no appropiate match, you should return {{"Id":-1}}. While evaluating the answers please consider the following guideline:
    {container_id_guidline}
 - In case you find an element, but its context is not the one present in the screen, don't consider it as a correct answer. Look at a few examples:
    Example 1: 
    - To select a search bar inside a web page, don't select the browser search bar, because is not inside a web page, is from the browser itself so you should return -1
    Example 2:
    - If you have to select a login button, but in the page there are only buttons with no login text on them, don't select any button at all and return -1.
 - You should take care of the situation when the query is describing more details of an object. In case not all the details are present, you should return -1. Look at a few examples:
    Example 1:
    - If the query ask you for a login close button, but you can see only the browser close button, you should return -1, not the browser close button.
 - You should return a valid response if you find a good match between the question and the screen.
    Example 1:
    - If you have to select the password inputbox, and there exists an inputbox and close to it says Password, you should return that inputbox.
"""

container_id_guidline = """- A description can also describe a container. In this case, you should return only the Id of the container like {{"Id": {{containerID}}}}. Do not return the Ids of the elements inside it."""

qa_user_message = """
Current screen:
{dom_string}

Human element description: {human_description} 
"""
qa_element_query_example_list = [
    (
        '<Window><Title Id="2"><Text Text="Price Information Variant for Equipment (1)" Id="13" /></Title><MinimizeButton Id="3"><Text Text="_" Id="14" /></MinimizeButton><CloseButton Id="4"><Text Text="X" Id="15" /></CloseButton><SelectedTab Id="5"><Container Text="Restrictions" Id="16"><Container Text="Variant:" Id="19"><Inputbox Id="23"><Icon Id="27"><Text Text="ZZZ" Id="29" /></Icon></Inputbox></Container><Container Text="Description:" Id="20"><Inputbox Id="24" /></Container><Container Text="Int./ext. variant:" Id="21"><Inputbox Id="25" /></Container><Container Text="Maximum Nn of Hits;" Id="22"><Inputbox Id="26"><Text Text="500" Id="28" /></Inputbox></Container></Container></SelectedTab><Button Text="=" Id="6" /><Text Text="Start Search" Id="8" /><Text Text="Multiple Selection" Id="10" /><Icon Id="11"><Text Text="X" Id="18" /></Icon><Text Text="Close" Id="12" /></Window>',
        "Equipment details entry",
        '{"Id":24}',
    ),
    (
        '<Window><Title Id="2"><Text Text="Price Information Variant for Equipment (1)" Id="13" /></Title><MinimizeButton Id="3"><Text Text="_" Id="14" /></MinimizeButton><CloseButton Id="4"><Text Text="X" Id="15" /></CloseButton><SelectedTab Id="5"><Container Text="Restrictions" Id="16"><Container Text="Variant:" Id="19"><Inputbox Id="23"><Icon Id="27"><Text Text="ZZZ" Id="29" /></Icon></Inputbox></Container><Container Text="Description:" Id="20"><Inputbox Id="24" /></Container><Container Text="Int./ext. variant:" Id="21"><Inputbox Id="25" /></Container><Container Text="Maximum Nn of Hits;" Id="22"><Inputbox Id="26"><Text Text="500" Id="28" /></Inputbox></Container></Container></SelectedTab><Button Text="=" Id="6" /><Text Text="Start Search" Id="8" /><Text Text="Multiple Selection" Id="10" /><Icon Id="11"><Text Text="X" Id="18" /></Icon><Text Text="Close" Id="12" /></Window>',
        "UserName field",
        '{"Id":-1}',
    ),
]
label_matching_query_example_list = [
    (
        '<body><ul><li id="0">Căutare Google</li></ul></body>',
        "Google Search 2",
        '{"Id":0}',
    ),
    (
        '<body><ul><li id="0">Close</li><li id="1">Cancel</li><li id="2">Create Request</li><li id="3">Open calendar</li><li id="4">Clear</li><li id="5">Search filter options for Department</li></ul></body>',
        "Submit Request",
        '{"Id":2}',
    ),
    (
        '<body><ul><li id="0">Total Items QTY</li><li id="1">Invoice uploader</li><li id="2">Clear fields</li><li id="3">NovaSys Systems</li><li id="4">Vendor Name</li><li id="5">Transaction information</li><li id="6">Total Amount</li><li id="7">Post invoice</li><li id="8">Invoice Number</li><li id="9">PO #</li><li id="10">Due Date</li></ul></body>',
        "Submit invoice",
        '{"Id":7}',
    ),
    (
        '<body><ul><li id="0">Submit invoice</li><li id="1">Clear fields</li></ul></body>',
        "Post invoice",
        '{"Id":0}',
    ),
]

label_matching_prepend_dom_message = "<title>Consider synonyms and different languages</title>"
