import argparse
import asyncio
import base64
import concurrent.futures
import json
import os
from typing import List, Literal, NamedTuple

import cv2
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential
from tqdm import tqdm
from uipath_cv_client.dom_processing import BoxCoordinates

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.ui_automation.options import opt
from services.studio._text_to_workflow.ui_automation.ui_automation_service import (
    QaElementWithImageRequest,
    QaGetElementDescriptionRequest,
    UiAutomationService,
)
from services.studio._text_to_workflow.utils import errors

opt["cv"]["url"] = os.getenv("CV_SERVICE_URL", "")
opt["cv"]["license"] = os.getenv("CV_SERVICE_API_KEY", "")
opt["gpt_config"]["version"] = os.getenv("LLM_API_VERSION", "2024-02-15-preview")
opt["engine_config"]["default_engine"] = os.getenv("LLM_MODEL", "gpt-4o-2024-05-13")


settings.USE_LLM_GATEWAY = os.getenv("USE_LLM_GATEWAY", "false") == "true"
settings.AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "")
settings.AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "")


class BoundingBoxCoordinates(NamedTuple):
    x1: float
    y1: float
    x2: float
    y2: float


class QAPair(NamedTuple):
    image_filepath: str
    box: BoundingBoxCoordinates | None
    box_type: Literal[
        "Other", "ArrowButton", "Button", "Checkbox", "CloseButton", "Inputbox", "Icon", "MaximizeButton", "MinimizeButton", "Radiobutton", "Text"
    ]
    question: str | None
    json_filename: str
    rawDOM: List[dict] | None = None


class QAEvalDatapoint(BaseModel):
    question: str | None
    generated_question: str | None
    expected_box: BoundingBoxCoordinates | None
    expected_box_type: (
        Literal["Other", "ArrowButton", "Button", "Checkbox", "CloseButton", "Inputbox", "Icon", "MaximizeButton", "MinimizeButton", "Radiobutton", "Text"]
        | None
    ) = None
    predicted_box: BoundingBoxCoordinates | None
    content_filter: bool
    filename: str
    error_message: str | None = None


class QAReturnData(NamedTuple):
    predicted_box: BoundingBoxCoordinates | None
    description: str | None


def get_xyxy_box_for_word(word) -> BoundingBoxCoordinates:
    vertices = word["boundingPoly"]["vertices"]
    return BoundingBoxCoordinates(vertices[0]["x"], vertices[0]["y"], vertices[2]["x"], vertices[2]["y"])


def load_dataset_web_format(dataset_path) -> List[QAPair]:
    """
    This dataset contains the list of rawDOM driver elements from the actual webpage along with a screenshot and a the target box.
    Currently, we don't have annotated questions.
    We use this dataset for the circular evaluation of the model (target_box -> description -> question -> pred_box)
    """

    data_points = []
    for root, _, files in os.walk(dataset_path):
        for file in files:
            if not file.endswith(".json"):
                continue
            with open(os.path.join(root, file), "r") as f:
                data = json.loads(f.read())

            idx = 0
            x, y, width, height = data["target_box"]["x"], data["target_box"]["y"], data["target_box"]["width"], data["target_box"]["height"]
            image_name = os.path.splitext(file)[0] + ".png"

            data_points.append(
                QAPair(
                    image_filepath=os.path.join(root, image_name),
                    box=BoundingBoxCoordinates(x, y, x + width - 1, y + height - 1),
                    box_type=data.get("target_label", "Other"),
                    question=data.get("question", None),
                    json_filename=str(idx) + "_" + file,
                    rawDOM=data["rawDOM"],
                )
            )

    return data_points


def load_dataset(dataset_path) -> List[QAPair]:
    data_points = []
    for filename in os.listdir(dataset_path):
        if not filename.endswith(".json"):
            continue
        with open(os.path.join(dataset_path, filename), "r") as f:
            data = json.loads(f.read())
        words_by_instance_id = {int(word["instance"]): get_xyxy_box_for_word(word) for line in data["words"] for word in line}
        words_by_instance_id = {k: v for k, v in sorted(words_by_instance_id.items(), key=lambda item: item[0])}
        questions_by_instance_id = {int(k): value for k, value in data["instance_questions"].items()}
        idx = 0
        for instance_id, question in questions_by_instance_id.items():
            idx = idx + 1
            data_points.append(
                QAPair(
                    image_filepath=os.path.join(dataset_path, data["fname"]),
                    box=words_by_instance_id[instance_id],
                    box_type=data.get("instance_label", "Other"),
                    question=question,
                    json_filename=str(idx) + "_" + filename,
                )
            )
        for question in data["negative_questions"]:
            idx = idx + 1
            data_points.append(QAPair(image_filepath=data["fname"], box=None, box_type="Other", question=question, json_filename=str(idx) + "_" + filename))  # type: ignore
    return data_points


@retry(stop=stop_after_attempt(6), wait=wait_exponential(multiplier=1, min=4, max=128), reraise=True)
async def get_qa_box(datapoint: QAPair, service: UiAutomationService, dataset_path: str) -> QAReturnData:
    print(f"Processing {datapoint.image_filepath}")
    image = cv2.imread(datapoint.image_filepath)
    _, buffer = cv2.imencode(".png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    image_base_64 = base64.b64encode(buffer).decode("utf-8")

    assert datapoint.question is not None, "Question is None"
    request = QaElementWithImageRequest(image=image_base_64, human_description=datapoint.question, rawDOM=datapoint.rawDOM)

    bbox = None

    try:
        out = await service.qa_element(request, request_headers={})
    except errors.ContentFilterError as ex:
        raise ex
    if out is not None and "element" in out:
        box_xywh = out["element"]["Area"].split(",")
        box_xywh = list(map(int, box_xywh))
        bbox = BoundingBoxCoordinates(
            box_xywh[0],
            box_xywh[1],
            box_xywh[0] + box_xywh[2] - 1,
            box_xywh[1] + box_xywh[3] - 1,
        )
    return QAReturnData(predicted_box=bbox, description=None)


@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=128), reraise=True)
async def get_element_description_box(datapoint: QAPair, service: UiAutomationService, dataset_path: str) -> QAReturnData:
    print(f"Processing {datapoint.image_filepath}")
    image = cv2.imread(datapoint.image_filepath)
    _, buffer = cv2.imencode(".png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    image_base_64 = base64.b64encode(buffer).decode("utf-8")
    response_box = None

    if datapoint.box is None:
        return QAReturnData(predicted_box=response_box, description=None)

    target_box = BoxCoordinates(
        x=int(datapoint.box.x1),
        y=int(datapoint.box.y1),
        width=int(datapoint.box.x2 - datapoint.box.x1 + 1),
        height=int(datapoint.box.y2 - datapoint.box.y1 + 1),
    )
    request = QaGetElementDescriptionRequest(image=image_base_64, target_box=target_box, rawDOM=datapoint.rawDOM)

    try:
        response = await service.get_element_description(request)  # type: ignore
        raw_element = await service.qa_element(
            QaElementWithImageRequest(image=image_base_64, human_description=response["description"] or "", rawDOM=datapoint.rawDOM), request_headers={}
        )

    except errors.ContentFilterError as ex:
        raise ex
    except Exception as ex:
        print("Error", ex)
        raise ex

    if raw_element is not None and "element" in raw_element:
        box_xywh = raw_element["element"]["Area"].split(",")
        box_xywh = list(map(int, box_xywh))
        response_box = BoundingBoxCoordinates(
            box_xywh[0],
            box_xywh[1],
            box_xywh[0] + box_xywh[2] - 1,
            box_xywh[1] + box_xywh[3] - 1,
        )
    return QAReturnData(predicted_box=response_box, description=response["description"])


async def process_worker_job(datapoint, service, dataset_path, output_path, service_call=get_qa_box):
    output_filepath = os.path.join(output_path, datapoint.json_filename)
    if os.path.exists(output_filepath):
        return

    json_filename = "_".join(datapoint.json_filename.split("_")[1:])
    try:
        result = await service_call(datapoint, service, dataset_path)
        box = result.predicted_box
        description = result.description
    except errors.ContentFilterError:
        with open(output_filepath, "w") as f:
            f.write(
                QAEvalDatapoint(
                    question=datapoint.question,
                    generated_question=None,
                    expected_box=datapoint.box,
                    expected_box_type=datapoint.box_type,
                    predicted_box=None,
                    filename=json_filename,
                    content_filter=True,
                ).model_dump_json()
            )
        return
    except Exception as ex:
        print("Error", ex)
        with open(output_filepath, "w") as f:
            f.write(
                QAEvalDatapoint(
                    question=datapoint.question,
                    generated_question=None,
                    expected_box=datapoint.box,
                    expected_box_type=datapoint.box_type,
                    predicted_box=None,
                    filename=json_filename,
                    content_filter=False,
                    error_message=str(ex),
                ).model_dump_json()
            )
        return

    with open(output_filepath, "w") as f:
        f.write(
            QAEvalDatapoint(
                question=datapoint.question,
                generated_question=description,
                expected_box=datapoint.box,
                expected_box_type=datapoint.box_type,
                predicted_box=box,
                filename=json_filename,
                content_filter=False,
            ).model_dump_json()
        )


def run_in_thread(datapoint, service, dataset_path, output_path, service_call):
    new_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(new_loop)
    result = new_loop.run_until_complete(process_worker_job(datapoint, service, dataset_path, output_path, service_call))
    return result


async def eval_qa(dataset_paths, output_paths, service_call=get_qa_box, dataset_format: Literal["cv-qa-rel-format", "st-web-format"] = "cv-qa-rel-format"):
    service = UiAutomationService(options=opt)
    print("Dataset format", dataset_format)
    assert len(output_paths) == len(dataset_paths)
    for dataset_idx, dataset_path in enumerate(dataset_paths):
        output_path = output_paths[dataset_idx]
        os.makedirs(output_path, exist_ok=True)
        datapoints = load_dataset(dataset_path) if dataset_format == "cv-qa-rel-format" else load_dataset_web_format(dataset_path)
        print("Datapoints loaded", len(datapoints))

        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            for datapoint in tqdm(datapoints):
                executor.submit(run_in_thread, datapoint, service, dataset_path, output_path, service_call)


async def main():
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "-d",
        "--datasetpaths",
        nargs="+",
        help="Dataset paths",
        default=["/workdir/data/cv-qa-rel-format/cv-qa-rel-format"],
    )
    parser.add_argument(
        "-o",
        "--outputpaths",
        nargs="+",
        help="Output paths",
        default=["/workdir/cv-qa-rel-format-cache-with-descriptions/"],
    )
    parser.add_argument(
        "-s",
        "--servicecall",
        type=str,
        help="Service call to use (qa-element or get-box-description)",
        default="qa",
    )
    parser.add_argument(
        "-f",
        "--datasetformat",
        type=str,
        help="Dataset format (cv-qa-rel-format or st-web-format)",
        default="cv-qa-rel-format",
    )

    args = parser.parse_args()
    await eval_qa(
        args.datasetpaths, args.outputpaths, get_element_description_box if args.servicecall == "get-box-description" else get_qa_box, args.datasetformat
    )


if __name__ == "__main__":
    asyncio.run(main())
