import json
import os
import re
from collections import defaultdict

import torch
import typing_extensions as t


def _box_from_vertices(vertices):
    return [
        vertices[0]["x"],
        vertices[0]["y"],
        vertices[2]["x"],
        vertices[2]["y"],
    ]


def _get_questions_answer(instance_questions, words_by_instance, box_by_instance):
    # decoder_questions = []
    for instance, questions in instance_questions.items():
        # answer = " ".join(words_by_instance[int(instance)])
        questions = questions.split(",")
        answers_boxes = [box_by_instance[int(instance)]] * len(questions)

    return questions, answers_boxes


def _load_datamanager_qa_datapoint(annotation):
    instance_questions = annotation["instance_questions"]

    # replace control and number placeholders with actual control and number
    control_and_number_regex = "__(?P<control>[A-Z][a-zA-Z]+)__(?P<number>\d+)__"  # noqa
    all_words = [word for line in annotation["words"] for word in line]
    for word in all_words:
        word["description"] = re.sub(
            control_and_number_regex,
            lambda x: f"<|{x.group('control')}|><|{x.group('number')}|>",
            word["description"],
        )

    words_boxes = [_box_from_vertices(word["boundingPoly"]["vertices"]) for word in all_words]
    words_text = [word["description"] for word in all_words]

    words_by_instance = defaultdict(list)
    box_by_instance = defaultdict(list)
    for index, word in enumerate(all_words):
        words_by_instance[word["instance"]].append(word["description"])
        box_by_instance[word["instance"]].append(words_boxes[index])

    questions, answer_boxes = [], []
    if (instance_questions is not None) and (len(instance_questions) > 0):
        questions, answer_boxes = _get_questions_answer(instance_questions, words_by_instance, box_by_instance)

    return words_text, words_boxes, questions, answer_boxes


class DataManagerQuestionAnsweringDataset(torch.utils.data.Dataset):
    def load(self, is_train):
        self.questions = []
        for filepath in self.filepaths:
            annotation_filepath = f"{filepath}_mapped.json"
            sample = json.load(open(annotation_filepath))
            category = sample["subset"]
            if (category == "TRAIN" and not is_train) or (category != "TRAIN" and is_train):
                continue
            words_text, words_boxes, questions, answer_boxes = _load_datamanager_qa_datapoint(sample)
            image_path = os.path.join(os.path.split(filepath)[0], sample["fname"])

            for question, answer_box in zip(questions, answer_boxes, strict=False):
                datapoint = {
                    "image": image_path,
                    "words_text": words_text,
                    "words_boxes": words_boxes,
                    "question": question,
                    "answer_box": answer_box,
                }
                self.questions.append(datapoint)

    def __init__(self, dataset_path: str, split: t.Literal["test", "train"]):
        dirpath, dirnames, filenames = next(os.walk(dataset_path))
        extensions = {".png", ".jpg", ".jpeg"}
        filepaths = [os.path.join(dirpath, filename) for filename in filenames if os.path.splitext(filename)[1] in extensions]
        self.filepaths = filepaths
        self.load(split.upper() == "TRAIN")

    def __len__(self):
        return len(self.questions)

    def __getitem__(self, idx):
        datapoint = self.questions[idx]
        return datapoint


class DataManagerQuestionAnsweringDatasetNew(torch.utils.data.Dataset):
    def load(self, exclude_icons=True):
        self.questions = []
        for filepath in self.filepaths:
            annotation_filepath = f"{os.path.splitext(filepath)[0]}.json"
            sample = json.load(open(annotation_filepath))

            for question, values in sample.items():
                if isinstance(values, dict):
                    bbox = [int(item) for item in values["AbsoluteRegion"].split(",")]
                    datapoint = {
                        "image": filepath,
                        "question": question,
                        "answer_box": bbox,
                    }
                    self.questions.append(datapoint)

    def __init__(self, dataset_path: str):
        dirpath, _, filenames = next(os.walk(dataset_path))
        extensions = {".png", ".jpg", ".jpeg"}
        filepaths = [os.path.join(dirpath, filename) for filename in filenames if os.path.splitext(filename)[1] in extensions]
        self.filepaths = filepaths
        self.load()

    def __len__(self):
        return len(self.questions)

    def __getitem__(self, idx):
        datapoint = self.questions[idx]
        return datapoint


if __name__ == "__main__":
    # dataset = DataManagerQuestionAnsweringDataset(r"C:\Users\<USER>\Documents\cv_rel_data\computer_vision_rel")
    dataset = DataManagerQuestionAnsweringDatasetNew(r"C:\work\cv_qa\cv_qa")
