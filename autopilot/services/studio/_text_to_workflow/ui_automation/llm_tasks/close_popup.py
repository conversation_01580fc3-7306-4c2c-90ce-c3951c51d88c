from typing import List, NamedTuple

from langchain.schema import AIMessage, BaseMessage, HumanMessage, SystemMessage
from pydantic import BaseModel
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LLMChat
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

prompt_system_message = """You are a UI Assistant expert that specializes in matching descriptions to pop-up images. A pop-up is provided both as an image and as a DOM string. The DOM contains elements such as <Button>, <Text>, <InputBox>, etc.

Your input includes:
- A pop-up image.
- A DOM string.
- A set of possible descriptions provided as tuples (index, description).

You have two independent tasks:
1. **Description Matching:** Return the index of the description that best matches the pop-up image. If none of the descriptions fit, return null.
2. **Close-Element Identification:** Find the DOM element most likely responsible for closing the pop-up, and return its ID. If there is no such element, return -1.

Guidelines:
- Process the tasks sequentially: first focus on matching the description, then identify the closing element.
- Briefly describe the pop-up content and explain your reasoning for selecting the matching description.
- Ensure the returned description index does not exceed the number of provided descriptions.
- There may be multiple elements that can close the pop-up, but you only need to identify one. 
- Preferably, choose the pop-up that rejects or cancels the pop-up. Otherwise, choose any element that closes the pop-up or that might redirect you to another page.
- Use the information from the image, DOM, and provided descriptions to guide your decisions.

Keep your output clear and structured for each task."""

user_message_template = """
Current pop-up in DOM format:
{dom_string}

Set of possible descriptions: {descriptions}
"""


class PopupResponse(BaseModel):
    description_matching_reasoning: str
    description_id: int | None
    closing_element_reasoning: str
    closing_element_id: int | None


class PromptExample(NamedTuple):
    dom_string: str
    descriptions: List[str]
    expected_response: dict


examples = [
    PromptExample(
        dom_string='<Window Id="0"><Title Text="Icon" Id="1"><Container Text="This is a blocking" Id="4"><Title Id="8" /></Container><Text Text="modal popup!" Id="5" /></Title><CloseButton Text="X" Id="2" /><Button Text="Cancel" Id="3" /></Window>',
        descriptions=["Real estate", "Validation error", "Virus"],
        expected_response=PopupResponse(
            description_matching_reasoning="'The pop-up image shows a promotional offer asking users to enter their email to receive a 15% off coupon. This is a typical marketing strategy and does not relate to real estate, validation errors, or viruses. None of the provided descriptions match the content of the pop-up, which is a special offer or discount promotion.",
            description_id=None,
            closing_element_reasoning="The pop-up image includes a small 'X' icon in the top right corner, which is commonly used to close windows or pop-ups. In the DOM, there is an <Icon> element with Id=\"2\", which likely corresponds to this 'X' icon.",
            closing_element_id=2,
        ).model_dump(),
    ),
    PromptExample(
        dom_string='<Window Id="0"><Text Text="We use cookie modules to personalize and" Id="1" /><Text Text="improve your experience on our site." Id="2" /><Text Text="Please" Id="3" /><Text Text="visit our Privacy Statement for more" Id="4" /><Text Text="information on our data collection practices." Id="5" /><Text Text="When you click &quot;I accept all&quot;, you agree that" Id="6" /><Text Text="we will use cookie modules for the purposes" Id="7" /><Text Text="mentioned in our cookie module consent tool." Id="8" /><Text Text="Privacy Statement" Id="9" /><Button Text="I accept all" Id="10" /><Button Text="It repels" Id="11" /><Text Text="!h!" Id="12" /><Text Text="Manage your modules cookie" Id="13" /></Window>',
        descriptions=["Real estate", "Cookies", "Virus"],
        expected_response=PopupResponse(
            description_matching_reasoning="The pop-up image and DOM string both describe a message about cookie usage and consent, which is a common feature on websites to comply with privacy regulations. The description 'Cookies' directly matches the content of the pop-up, as it discusses cookie modules and consent. The other descriptions, 'Real estate' and 'Virus', do not relate to the content shown in the image or DOM.",
            description_id=1,
            closing_element_reasoning="The pop-up provides two buttons: 'I accept all' and 'It repels'. Typically, a button that allows users to reject or manage cookies would close the pop-up. The button 'It repels' seems to serve this purpose, as it suggests rejecting or managing cookie settings. In the DOM, this corresponds to the button with Id='11",
            closing_element_id=11,
        ).model_dump(),
    ),
    PromptExample(
        dom_string='<Window Id="0"><Title Text="Icon" Id="1"><Container Text="This is a blocking" Id="4"><Title Id="8" /></Container><Text Text="modal popup!" Id="5" /></Title><CloseButton Text="X" Id="2" /><Button Text="Cancel" Id="3" /></Window>',
        descriptions=[],
        expected_response=PopupResponse(
            description_matching_reasoning="The pop-up image shows a promotional offer where users can enter their email to receive a 15% off coupon. The DOM string matches this exactly, with text elements indicating the offer and an input box for the email address. However, there are no descriptions provided to match against, so the description index is null.",
            description_id=None,
            closing_element_reasoning="The pop-up image includes a small 'X' icon in the top right corner, which is commonly used to close windows or pop-ups. In the DOM, there is an <Icon> element with Id=\"2\", which likely corresponds to this 'X' icon.",
            closing_element_id=2,
        ).model_dump(),
    ),
]


def _build_prompt(dom_string: str, descriptions: List[str], image_base64: str) -> List[BaseMessage]:
    messages = []
    messages.append(SystemMessage(role="system", content=prompt_system_message))
    for example in examples:
        user_message_content = user_message_template.format(
            dom_string=example.dom_string, descriptions=", ".join(f"({i},{d})" for i, d in enumerate(example.descriptions))
        )
        messages.append(HumanMessage(role="user", content=user_message_content))
        messages.append(AIMessage(role="assistant", content=str(example.expected_response)))

    query_descriptions = ", ".join(f"({i},{d})" for i, d in enumerate(descriptions)) if descriptions else "None"
    user_message_content = [
        {
            "type": "text",
            "text": user_message_template.format(dom_string=dom_string, descriptions=query_descriptions),
        }
    ]
    if image_base64:
        user_message_content.append(
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            }
        )
    messages.append(HumanMessage(role="user", content=user_message_content))
    return messages


async def close_popup(dom_str: str, descriptions: List[str], image_base64: str, options) -> PopupResponse:
    dom_str = truncate_dom_str(dom_str, options.get("prompt_options", {}).get("max_dom_tokens", 4000))

    element_messages = _build_prompt(dom_str, descriptions, image_base64)
    gpt_chat = LLMChat(options, ConsumingFeatureType.CLOSE_POPUP, response_format=PopupResponse)

    predict_info = {}
    response = await gpt_chat.send_message(element_messages, predict_info)
    return response
