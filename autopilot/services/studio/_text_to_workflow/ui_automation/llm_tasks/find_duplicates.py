from typing import List, NamedTuple

import langchain
from langchain.schema import AIMessage, BaseMessage, HumanMessage, SystemMessage
from pydantic import BaseModel
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LLMChat
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

prompt_system_message = """
Role:
You are a UI Assistant expert that helps with identifying UI elements on a screen based on a human description.

Context:
Screens are given as a DOM which contains elements like buttons <Button>, texts <Texts>, InputBox <InputBox> etc. An element is uniquely identified by its ID.
The targeting model is trained to identify the element that best matches a human description, given the screen image, its DOM representation and a human description.
The output of that model is the Id of the element that best matches the human description. If the model does not find any match, it returns -1.

Instructions:
You should evaluate the quality of the model output by re-analyzing the screen image and its DOM representation. 
Your main task is to identify if the human description matches other elements on the screen, which are not the one matched by the model. If you find such elements, you should return their IDs.
Otherwise, you should return an empty list.

Guidelines:
    - Do not include internal details in the reasoning, just explain the logic behind your decision.
    - Do not include the Id of the element that was matched by the model in the duplicate Ids list.
    
<IMPORTANT>:

    **Use the description as query to find duplicates.**
        A description should narrow down the search to a specific element, unless it is clearly ambiguous.
        If you consider the description to be ambiguous, find the duplicate elements that match the description as closely as possible.

    **If functionally similar elements are found, closely analyze the context of the element and the description.**
        The description should narrow down the functionality to a single specific element.
        If there are other elements with the same functionality, but in different contexts or regions, do not consider them as duplicates. Return an empty list of ids.
        Use the description to identify the context of the element and find other elements that are functionally similar in the same context.
        Elements from other sections/columns/row/tabs should not be considered as duplicates, even if they are visually or functionally similar.

You should return:
    - a short reasoning on why the duplicates were found or not found
    - a list of duplicate Ids
"""

user_message_template = """
Current screen:
{dom_string}

Matched element Id: {element_id}

human description: {description}
"""


class FindDuplicatesResponse(BaseModel):
    short_reasoning: str
    duplicate_ids: List[int]


class DuplicateExample(NamedTuple):
    dom_string: str
    description: str
    matched_element_id: int
    expected_response: FindDuplicatesResponse


examples = [
    DuplicateExample(
        dom_string="""<Window Id="0"><Icon Id="1" /><Text Text="Configuration Manager - Erdal SEVGİLOĞLU (vb17828), Server ************ v." Id="2" /><Text Text="********** on port 2020" Id="3" /><MinimizeButton Id="4"><Text Text="_" Id="108" /></MinimizeButton><MaximizeButton Id="5" /><CloseButton Id="6"><Text Text="X" Id="109" /></CloseButton><Text Text="File" Id="7" /><Text Text="Edit" Id="8" /><Text Text="View" Id="9" /><Text Text="Tools" Id="10" /><Text Text="Help" Id="11" /><Inputbox Id="12"><Icon Id="110" /><Text Text="IPT-TDM" Id="111" /><ArrowButton Id="112" /></Inputbox><Icon Id="13" /><Icon Text="cut" Id="14" /><Icon Id="15" /><Icon Id="16" /><Icon Text="cut" Id="17" /><Icon Id="18" /><Icon Id="19" /><ArrowButton Id="20" /><Icon Id="21" /><ArrowButton Id="22" /><Icon Text="search" Id="23" /><Text Text="All Folders" Id="24" /><Text Text="Contents of '/Configuration/Environment/Switches/Ankara_Switch/Agent Logins/IPT-TDM'" Id="25" /><Icon Id="26" /><Text Text="Configuration" Id="27" /><Button Text="-" Id="28" /><Icon Id="29" /><Text Text="Environment" Id="30" /><Button Text="+" Id="31" /><Icon Id="32" /><Text Text="Persons" Id="33" /><Button Text="+" Id="34" /><Icon Id="35" /><Text Text="Places" Id="36" /><table Id="37"><row Id="116"><cell Id="133"><Text Text="Code" Id="149" /><ArrowButton Id="150" /></cell><cell Id="134"><Text Text="Switch" Id="151" /></cell></row><row Id="117"><cell Id="135"><Text Text="Metni buraya girin" Id="152" /><Icon Text="filter" Id="153" /></cell><cell Id="136"><Text Text="Metni buraya girin" Id="154" /><Icon Text="filter" Id="155" /></cell></row><row Id="118"><cell Id="137"><Icon Id="156" /><Text Text="19026" Id="157" /></cell><cell Id="138"><Text Text="Ankara_Switch" Id="158" /></cell></row><row Id="119"><cell Id="139"><Icon Id="159" /><Text Text="19027" Id="160" /></cell><cell Id="140"><Text Text="Ankara_Switch" Id="161" /></cell></row><row Id="120"><cell Id="141"><Icon Id="162" /><Text Text="19028" Id="163" /></cell><cell Id="142"><Text Text="Ankara_Switch" Id="164" /></cell></row><row Id="121"><cell Id="143"><Icon Id="165" /><Text Text="19029" Id="166" /></cell><cell Id="144"><Text Text="Ankara_Switch" Id="167" /></cell></row><row Id="122"><cell Id="145"><Icon Text="book" Id="168" /><Text Text="19030" Id="169" /></cell><cell Id="146"><Text Text="Ankara_Switch" Id="170" /></cell></row><row Id="123"><cell Id="147"><Icon Text="percent" Id="171" /><Text Text="19031" Id="172" /></cell><cell Id="148"><Text Text="Ankara_Switch" Id="173" /></cell></row></table><Button Text="+" Id="38" /><Icon Id="39" /><Text Text="Scripts" Id="40" /><Button Id="41" /><Icon Id="42" /><Text Text="Switches" Id="43" /><Button Text="-" Id="44" /><Icon Id="45" /><Text Text="Ankara_Switch" Id="46" /><Button Text="-" Id="47" /><Icon Id="48" /><Text Text="Agent Logins" Id="49" /><Icon Id="50" /><Text Text="CagriMerkezi" Id="51" /><Icon Id="52" /><Text Text="DisFirmaAssistt" Id="53" /><Icon Id="54" /><Text Text="DisFirmaCMC" Id="55" /><Icon Id="56" /><Text Text="DisFirmaGlobal" Id="57" /><Icon Id="58" /><Text Text="DisFirmaTeleperformance" Id="59" /><Button Text="+" Id="60" /><Icon Id="61" /><Text Text="Ebis" Id="62" /><Icon Id="63" /><Text Text="IPT-TDM" Id="64" /><Icon Id="65" /><Text Text="Outbound" Id="66" /><Icon Id="67" /><Text Text="POSDestek" Id="68" /><Icon Id="69" /><Text Text="Silinecekler" Id="70" /><Button Text="+" Id="71" /><Icon Id="72" /><Text Text="DNs" Id="73" /><Button Text="-" Id="74" /><Icon Id="75" /><Text Text="IstanbulDRC_Switch" Id="76" /><Button Id="77" /><Icon Id="78" /><Text Text="Agent Logins" Id="79" /><Icon Id="80" /><Text Text="CagriMerkezi" Id="81" /><Icon Id="82" /><Text Text="DisFirmaAssistt" Id="83" /><Icon Id="84" /><Text Text="DisFirmaCMC" Id="85" /><Icon Id="86" /><Text Text="DisFirmaGlobal" Id="87" /><Icon Id="88" /><Text Text="DisFirmaTeleperformance" Id="89" /><Button Text="+" Id="90" /><Icon Id="91" /><Text Text="Ebis" Id="92" /><Icon Id="93" /><Text Text="IPT-TDM" Id="94" /><Icon Id="95" /><Text Text="Outbound" Id="96" /><Icon Id="97" /><Text Text="POSDestek" Id="98" /><Icon Id="99" /><Text Text="Silinecekler" Id="100" /><Button Text="+" Id="101" /><Icon Id="102" /><Text Text="DNs" Id="103" /><Text Text="For Help, press F1." Id="104" /><Icon Id="105" /><Text Text="ON line" Id="106" /><Icon Id="107" /></Window>""",
        description="Filter button for Switch column",
        matched_element_id=155,
        expected_response=FindDuplicatesResponse(
            short_reasoning="The description 'Filter button for Switch column' matches the filter icon in the Switch column of the table. There is another filter icon in the 'Code' column, but the description is specific to the Switch column. Even though the filter icon in the 'Code' column is visually similar and it has a filtering functionality, it is not in the same context as the one in the Switch column. Therefore, the filter icon in the 'Code' column is not considered a duplicate.",
            duplicate_ids=[],
        ),
    ),
    DuplicateExample(
        dom_string="""<Window Id="0"><Text Text="v" Id="1" /><Button Id="2"><Icon Text="google" Id="64" /><Text Text="mangalore temperature - Goog" Id="65" /><CloseButton Id="66"><Text Text="X" Id="84" /></CloseButton></Button><Icon Id="3" /><MinimizeButton Id="4"><Text Text="_" Id="68" /></MinimizeButton><MaximizeButton Id="5" /><CloseButton Id="6"><Text Text="X" Id="69" /></CloseButton><Icon Id="7" /><Icon Id="8" /><Icon Text="refresh" Id="9" /><Inputbox Id="10"><Icon Id="70" /><Text Text="google.com/search?q=mangalore+temperature&amp;riz=1C1CHBF_enIN1045IN10458oq=Mangalore+Tem&amp;gs_Icrp=EgZjaHJvbWUqDQgAEAAYgwEYsQMYgAQy..." Id="71" /><Icon Text="star" Id="72" /></Inputbox><Icon Id="11" /><Icon Text="uipath logo" Id="12" /><Icon Id="13" /><Button Id="14"><Text Text="Error" Id="74" /><Icon Id="75" /></Button><ArrowButton Id="15" /><Text Text="Google" Id="16" /><Inputbox Id="17"><Text Text="mangalore temperature" Id="76" /><CloseButton Id="77" /><Icon Text="microfone" Id="78" /></Inputbox><Icon Text="settings" Id="18" /><Icon Text="search" Id="19" /><Icon Text="settings" Id="20" /><Icon Text="chrome apps" Id="21" /><Icon Id="22" /><Icon Text="search" Id="23" /><Text Text="All" Id="24" /><Icon Text="uipath logo" Id="25" /><Text Text="News" Id="26" /><Icon Id="27" /><Text Text="Books" Id="28" /><Icon Id="29" /><Text Text="Images" Id="30" /><Icon Id="31" /><Text Text="Shopping" Id="32" /><Text Text="More" Id="33" /><Text Text="Tools" Id="34" /><Text Text="SafeSearch" Id="35" /><ArrowButton Id="36" /><Text Text="About 1,87,00,000 results (0.52 seconds)" Id="37" /><Text Text="Results for Mangaluru, Karnataka - Choose area" Id="38" /><Text Text="Precipitation: °C °F" Id="39" /><Text Text="0%" Id="40" /><Text Text="Weather" Id="41" /><Icon Id="42" /><Text Text="29" Id="43" /><Container Text="Humidity:" Id="44"><Text Text="66%" Id="80" /></Container><Text Text="Tuesday, 3:00 pm" Id="45" /><Container Text="Wind:" Id="46"><Text Text="11 km/h" Id="81" /></Container><Text Text="Cloudy" Id="47" /><Text Text="Temperature" Id="48" /><Text Text="Precipitation" Id="49" /><Text Text="Wind" Id="50" /><Text Text="29" Id="51" /><Text Text="28" Id="52" /><Text Text="27" Id="53" /><Text Text="28" Id="54" /><Text Text="26" Id="55" /><Text Text="26" Id="56" /><Text Text="24" Id="57" /><Text Text="23" Id="58" /><table Id="59"><row Id="82"><cell Id="85"><Text Text="4 pm" Id="101" /></cell><cell Id="86"><Text Text="7 pm" Id="102" /></cell><cell Id="87"><Text Text="10 pm" Id="103" /></cell><cell Id="88"><Text Text="1 am" Id="104" /></cell><cell Id="89"><Text Text="4 lam" Id="105" /></cell><cell Id="90"><Text Text="7 am" Id="106" /></cell><cell Id="91"><Text Text="10 am" Id="107" /></cell><cell Id="92"><Text Text="1 pm" Id="108" /></cell></row><row Id="83"><cell Id="93"><Button Text="Tue 31° 23°" Id="109" /></cell><cell Id="94"><Text Text="Wed" Id="110" /><Icon Id="111" /><Text Text="29° 24°" Id="112" /></cell><cell Id="95"><Text Text="Thu" Id="113" /><Icon Id="114" /><Text Text="28° 24°" Id="115" /></cell><cell Id="96"><Text Text="Fri" Id="116" /><Icon Id="117" /><Text Text="29° 24°" Id="118" /></cell><cell Id="97"><Text Text="Sat" Id="119" /><Icon Id="120" /><Text Text="30° 24°" Id="121" /></cell><cell Id="98"><Text Text="Sun" Id="122" /><Icon Id="123" /><Text Text="31° 24°" Id="124" /></cell><cell Id="99"><Text Text="Mon" Id="125" /><Icon Id="126" /><Text Text="31° 24°" Id="127" /></cell><cell Id="100"><Text Text="Tue" Id="128" /><Icon Id="129" /><Text Text="31° 24°" Id="130" /></cell></row></table><Text Text="Weather data" Id="60" /><Text Text="." Id="61" /><Text Text="Feedback" Id="62" /><ArrowButton Id="63" /></Window>""",
        description="Google Chrome close button",
        matched_element_id=6,
        expected_response=FindDuplicatesResponse(
            short_reasoning="The description 'Google Chrome close button' refers to the close button of the Google Chrome window. There is another close button within the tab (Id 66), but it is not the main window close button. The description specifically targets the main window close button, not the tab close button.",
            duplicate_ids=[],
        ),
    ),
]


def _build_prompt(dom_string: str, description: str, element_id: int, image_base64: str) -> List[BaseMessage]:
    messages = []
    messages.append(SystemMessage(role="system", content=prompt_system_message))
    for example in examples:
        user_message_content = user_message_template.format(
            dom_string=example.dom_string, element_id=example.matched_element_id, description=example.description
        )

        messages.append(HumanMessage(role="user", content=user_message_content))
        messages.append(AIMessage(role="assistant", content=str(example.expected_response)))

    user_message_content: List[dict[str, str | dict[str, str]]] = [
        {"type": "text", "text": user_message_template.format(dom_string=dom_string, element_id=element_id, description=description)}
    ]
    if image_base64:
        user_message_content.append(
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            }
        )

    messages.append(langchain.schema.HumanMessage(role="user", content=user_message_content))
    return messages


async def find_duplicates(dom_str: str, description: str, raw_element: dict, image_base64: str, options) -> FindDuplicatesResponse:
    dom_str = truncate_dom_str(dom_str, options.get("prompt_options", {}).get("max_dom_tokens", 4000))

    element_messages = _build_prompt(dom_str, description, raw_element["Id"], image_base64)
    gpt_chat = LLMChat(options, ConsumingFeatureType.QA_SCREEN, response_format=FindDuplicatesResponse)

    predict_info = {}
    response = await gpt_chat.send_message(element_messages, predict_info)
    return response
