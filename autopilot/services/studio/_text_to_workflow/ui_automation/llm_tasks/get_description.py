import json
from typing import List, NamedTuple

import langchain
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LLMChat
from services.studio._text_to_workflow.utils.inference import llm_schema

prompt_system_message = """You are an Ui Assistant expert that helps with identifiying UI elements in a screen based on a human description.
Screens are given as a DOM which contains elements like buttons <Button>, texts <Texts>, InputBox <InputBox> etc. An element is uniquely identified by its Id.
{input_description}
{container_id_guidline}
Your output shoud be a valid json response.
"""  # noqa

container_id_guidline = "If that element is a container, use the text of the child elements to describe it."

element_input_message = """Your input will be a screen, a target element Id and, optionally, a list of anchor Ids that are used as additional context for that particular element. If the target element does not provide enough information, then use those anchors to locate it. If the element Id is -1, you should return {{element_description : null}}.
    Your task is to return a human description of the target element with the given Id, focusing on its functionality."
"""

no_element_input_message = """
Your input will be a the screen represented as a DOM and the actual crop of the element you need to describe.
Your task is to return a human description of the provided element, focusing on its functionality.
If the crop does not contain any text or UI element, then return {{element_description : null}}.
"""

user_message_template = """
Current screen:
{dom_string}

Element Id: {element_id}

Anchors Ids: {anchors_ids}
"""

no_element_user_message_template = """
Current screen:
{dom_string}

"""


class PromptExample(NamedTuple):
    dom_string: str
    element_id: int
    anchors_ids: List[int] | None
    expected_description: str


examples = [
    PromptExample(
        '<Window><Title Id="2"><Text Text="Price Information Variant for Equipment (1)" Id="13" /></Title><MinimizeButton Id="3"><Text Text="_" Id="14" /></MinimizeButton><CloseButton Id="4"><Text Text="X" Id="15" /></CloseButton><SelectedTab Id="5"><Container Text="Restrictions" Id="16"><Container Text="Variant:" Id="19"><Inputbox Id="23"><Icon Id="27"><Text Text="ZZZ" Id="29" /></Icon></Inputbox></Container><Container Text="Description:" Id="20"><Inputbox Id="24" /></Container><Container Text="Int./ext. variant:" Id="21"><Inputbox Id="25" /></Container><Container Text="Maximum Nn of Hits;" Id="22"><Inputbox Id="26"><Text Text="500" Id="28" /></Inputbox></Container></Container></SelectedTab><Button Text="=" Id="6" /><Text Text="Start Search" Id="8" /><Text Text="Multiple Selection" Id="10" /><Icon Id="11"><Text Text="X" Id="18" /></Icon><Text Text="Close" Id="12" /></Window>',  # noqa
        24,
        None,
        '{"element_description": "Equipment details entry"}',
    )
]


def _build_prompt(
    dom_string: str, element_id: int, anchors_ids: List[int] | None = None, image_base64: str | None = None, use_container_id: bool = False
) -> List[langchain.schema.BaseMessage]:
    messages = []
    system_prompt = prompt_system_message.format(
        input_description=element_input_message if element_id != -1 else no_element_input_message,
        container_id_guidline=container_id_guidline if use_container_id else "",
    )
    system_message = langchain.schema.SystemMessage(role="system", content=system_prompt)
    messages.append(system_message)
    for example in examples:
        user_message_content = user_message_template.format(dom_string=example.dom_string, element_id=example.element_id, anchors_ids=example.anchors_ids)
        user_message = langchain.schema.HumanMessage(role="user", content=user_message_content)
        messages.append(user_message)
        assistant_message = langchain.schema.AIMessage(role="assistant", content=example.expected_description)
        messages.append(assistant_message)

    user_message_content = [
        {
            "type": "text",
            "text": user_message_template.format(dom_string=dom_string, element_id=element_id, anchors_ids=anchors_ids)
            if element_id != -1
            else no_element_user_message_template.format(dom_string=dom_string),
        }  # noqa
    ]
    if image_base64:
        user_message_content.append(
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            }
        )
    if user_message_content:
        user_message = langchain.schema.HumanMessage(role="user", content=user_message_content)
        messages.append(user_message)

    return messages


async def get_description(dom_str: str, element_id: int, image_base64: str | None, anchors_ids: List[int] | None, options) -> str | None:
    dom_str = truncate_dom_str(dom_str, options.get("prompt_options", {}).get("max_dom_tokens", 4000))

    element_messages = _build_prompt(dom_str, element_id, anchors_ids, image_base64, options["dom"].get("display_containers_id", False))
    gpt_chat = LLMChat(options, consuming_feature_type=llm_schema.ConsumingFeatureType.GET_SEMANTIC_DESCRIPTION)

    predict_info = {}
    out = await gpt_chat.send_message(element_messages, predict_info)
    out_dict = json.loads(out.content.strip().removeprefix("```json").removesuffix("```").strip())
    description = out_dict.get("element_description")

    return description
