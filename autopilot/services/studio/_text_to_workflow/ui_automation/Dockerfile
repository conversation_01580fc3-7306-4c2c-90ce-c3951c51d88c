ARG PYTHON_VARIANT="3.10"
### PRODUCTION
FROM python:${PYTHON_VARIANT}-slim-bullseye AS prod

ENV PYTHONPATH="/workspace/src"

# Install python dependencies
RUN pip install --upgrade pip
COPY ./studio/text_to_workflow/ui_automation/requirements.txt /tmp/pip/requirements.prod.txt
RUN pip install --no-cache-dir -r /tmp/pip/requirements.prod.txt
RUN apt-get update && apt-get install ffmpeg libsm6 libxext6  -y

# Copy sources
ADD ./gptproxy /workspace/src/gptproxy
ADD ./computer_vision /workspace/src/computer_vision
ADD ./ocr /workspace/src/ocr
ADD ./studio/text_to_workflow /workspace/src/studio/text_to_workflow
ADD ./shared /workspace/src/shared
ADD ./util /workspace/src/util

# Start
WORKDIR "/workspace/src"
ENTRYPOINT ["python", "studio/text_to_workflow/ui_automation/service.py"]