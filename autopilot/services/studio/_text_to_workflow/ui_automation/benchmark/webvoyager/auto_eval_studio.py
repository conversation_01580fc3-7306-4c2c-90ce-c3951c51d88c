import argparse
import json
import os
import pathlib
import time
from copy import deepcopy
from typing import Literal

from dotenv import load_dotenv
from json_utils import IterationImage, load_and_parse_trace_json
from openai import AzureOpenAI

from services.studio._text_to_workflow.ui_automation.options import agent_engine_options, agent_prompt_options, opt

load_dotenv()

### FROM https://github.com/MinorJerry/WebVoyager/blob/main/evaluation/auto_eval.py
SYSTEM_PROMPT_IMAGES = """As an evaluator, you will be presented with three primary components to assist you in your role:
1. Web Task Instruction: This is a clear and specific directive provided in natural language, detailing the online activity to be carried out. These requirements may include conducting searches, verifying information, comparing prices, checking availability, or any other action relevant to the specified web service (such as Amazon, Apple, ArXiv, BBC News, Booking etc).
2. Result Screenshots: This is a visual representation of the screen showing the result or intermediate state of performing a web task. It serves as visual proof of the actions taken in response to the instruction.
3. Result Response: This is a textual response obtained after the execution of the web task. It serves as textual result in response to the instruction.
-- You DO NOT NEED to interact with web pages or perform actions such as booking flights or conducting searches on websites.
-- You SHOULD NOT make assumptions based on information not presented in the screenshot when comparing it to the instructions.
-- Your primary responsibility is to conduct a thorough assessment of the web task instruction against the outcome depicted in the screenshot and in the response, evaluating whether the actions taken align with the given instructions.
-- NOTE that the instruction may involve more than one task, for example, locating the garage and summarizing the review. Failing to complete either task, such as not providing a summary, should be considered unsuccessful.
-- NOTE that the screenshot is authentic, but the response provided by LLM is generated at the end of web browsing, and there may be discrepancies between the text and the screenshots.
-- Note the difference: 1) Result response may contradict the screenshot, then the content of the screenshot prevails, 2) The content in the Result response is not mentioned on the screenshot, choose to believe the content.
You should elaborate on how you arrived at your final evaluation and then provide a definitive verdict on whether the task has been successfully accomplished, either as 'SUCCESS' or 'NOT SUCCESS'."""


USER_PROMPT_IMAGES = """TASK: <task>
Result Response: <answer>
<num> screenshots at the end: """
###


SYSTEM_PROMPT_DOM = """As an evaluator, you will be presented with four primary components to assist you in your role:
1. Web Task Instruction: This is a clear and specific directive provided in natural language, detailing the online activity to be carried out. These requirements may include conducting searches, verifying information, comparing prices, checking availability, or any other action relevant to the specified web service (such as Amazon, Apple, ArXiv, BBC News, Booking etc).
2. Result Screenshots: This is a visual representation of the screen showing the result or intermediate state of performing a web task. It serves as visual proof of the actions taken in response to the instruction.
3. DOM Structure: This is the Document Object Model (DOM) representation of the web page, showing the hierarchical structure of all elements on the page including their attributes, text content, and relationships. The DOM provides precise technical details about elements that may or may not be visible in the screenshots.
4. Result Response: This is a textual response obtained after the execution of the web task. It serves as textual result in response to the instruction.
-- You DO NOT NEED to interact with web pages or perform actions such as booking flights or conducting searches on websites.
-- You SHOULD NOT make assumptions based on information not presented in the screenshots or DOM when comparing them to the instructions.
-- Your primary responsibility is to conduct a thorough assessment of the web task instruction against:
a) The outcome depicted in the screenshots
b) The elements and content present in the DOM structure
c) The provided result response evaluating whether the actions taken align with the given instructions.
-- NOTE that the instruction may involve more than one task, for example, locating the garage and summarizing the review. Failing to complete either task, such as not providing a summary, should be considered unsuccessful.
-- NOTE that the screenshots and DOM are authentic, but the response provided by LLM is generated at the end of web browsing, and there may be discrepancies between the text, screenshots, and DOM.
-- When evaluating information:
1) If the result response contradicts what's visible in the screenshot or DOM, the content in the screenshot and DOM prevails
2) If content in the result response is not visible in the screenshot but is present in the DOM, it can be considered valid
3) If content is neither in the screenshot nor the DOM, it should be considered unverified
-- Pay special attention to verifying that any extracted values, clicked elements, or interacted components mentioned in the result response are actually present in either the DOM structure or screenshots.
You should elaborate on how you arrived at your final evaluation and then provide a definitive verdict on whether the task has been successfully accomplished, either as 'SUCCESS' or 'NOT SUCCESS'."""


USER_PROMPT_DOM = """TASK: <task>
Result Response: <answer>

<num_dom> DOM trees at the end: 
<dom>

<num> screenshots at the end: """


AZURE_OPENAI_ENDPOINT = "https://openai-pf.openai.azure.com/"


class ErrorMessgage:
    NO_TASK_RESULT = "ERROR: NO RESULT FILE"
    NO_TASK_TRACE = "ERROR: NO TRACE FILE"
    INVALID_TASK_TRACE = "ERROR: LOAD TRACE FILE"
    EVAL_NO_RESPONSE = "ERROR: EVAL NO RESPONSE"


class ReponseCode:
    SUCCESS = 1
    NOT_SUCCESS = 0
    TASK_ERROR = -400
    NO_VERDICT = -500
    NO_RESPONSE = -600


def _get_openai_client() -> AzureOpenAI:
    return AzureOpenAI(
        azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT"],
        api_key=os.environ["AZURE_OPENAI_API_KEY"],
        api_version="2025-01-01-preview",
    )


def auto_eval_by_gpt4v(
    task_prompt: str,
    images: list[IterationImage],
    llm_outputs: list[str],
    trace_data: list[str],
    openai_client: AzureOpenAI,
    api_model: str,
    max_imgs_eval: int,
    max_doms_eval: int,
) -> tuple[str, Literal[0, 1, -500, -600]]:
    max_imgs_eval = min(max_imgs_eval, len(images))
    max_doms_eval = min(max_doms_eval, len(trace_data))

    # IMAGES
    whole_content_img = []
    for i, image in enumerate(images[-max_imgs_eval:]):
        b64_img = image.image_base64
        whole_content_img.append(
            {
                "type": "text",
                "text": f"Image for Step {len(images) - max_imgs_eval + i + 1}:",
            }
        )
        whole_content_img.append(
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/png;base64,{b64_img}"},
            }
        )

    # CONSTRUCT MESSAGES WITH (1) TASK PROMPT, (2) LLM OUTPUT RESPONSE AND (3) IMAGES

    if max_doms_eval > 0:
        # the last max_doms_eval traces are XML DOMs
        dom_based_on_xml = "\n\n---\n\n".join(trace_data[-max_doms_eval:])
        user_prompt_tmp = USER_PROMPT_DOM.replace("<task>", task_prompt)
        user_prompt_tmp = user_prompt_tmp.replace("<answer>", llm_outputs[-1])
        user_prompt_tmp = user_prompt_tmp.replace("<num>", str(max_imgs_eval))
        user_prompt_tmp = user_prompt_tmp.replace("<num_dom>", str(max_doms_eval))
        user_prompt_tmp = user_prompt_tmp.replace("<dom>", dom_based_on_xml)
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT_DOM},
            {
                "role": "user",
                "content": [{"type": "text", "text": user_prompt_tmp}] + whole_content_img + [{"type": "text", "text": "Your verdict:\n"}],
            },
        ]
    else:
        user_prompt_tmp = USER_PROMPT_IMAGES.replace("<task>", task_prompt)
        user_prompt_tmp = user_prompt_tmp.replace("<answer>", llm_outputs[-1])
        user_prompt_tmp = user_prompt_tmp.replace("<num>", str(max_imgs_eval))
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT_IMAGES},
            {
                "role": "user",
                "content": [{"type": "text", "text": user_prompt_tmp}] + whole_content_img + [{"type": "text", "text": "Your verdict:\n"}],
            },
        ]

    retries = 0
    while True:
        try:
            retries += 1
            if retries > 3:
                return ErrorMessgage.EVAL_NO_RESPONSE, ReponseCode.NO_RESPONSE
            print("Calling gpt4v API to get the auto evaluation......")
            openai_response = openai_client.chat.completions.create(
                model=api_model,
                messages=messages,
                max_tokens=1000,
                seed=42,
                temperature=0,
            )
            print(
                "Cost: $",
                openai_response.usage.prompt_tokens / 1000 * 0.01 + openai_response.usage.completion_tokens / 1000 * 0.03,
            )
            break
        except Exception as e:
            print(e)
            if type(e).__name__ == "RateLimitError":
                time.sleep(10)
            elif type(e).__name__ == "APIError":
                time.sleep(15)
            elif type(e).__name__ == "InvalidRequestError":
                exit(0)
            else:
                time.sleep(10)

    gpt_response = openai_response.choices[0].message.content
    messages.append({"role": "assistant", "content": gpt_response})

    print_message = messages[1]
    for idx in range(len(print_message["content"])):
        if print_message["content"][idx]["type"] == "image_url":
            print_message["content"][idx]["image_url"] = {"url": "data:image/png;base64, b64_img"}

    if gpt_response is not None:
        auto_eval_res = 0 if "NOT SUCCESS" in gpt_response else 1
        if "SUCCESS" not in gpt_response:
            auto_eval_res = ReponseCode.NO_VERDICT  # Means gpt did not come up with a verdict
    else:
        gpt_response = ErrorMessgage.EVAL_NO_RESPONSE
        auto_eval_res = ReponseCode.NO_RESPONSE  # means gpt had no generated response
    return gpt_response, auto_eval_res


def _get_response_from_task_folder(task_folder: pathlib.Path, args: argparse.Namespace, client: AzureOpenAI, model_name: str = "uipath") -> tuple[str, int]:  # noqa
    if (task_folder / "ui_task_trace.json").exists():
        try:
            task_prompt, images, llm_outputs, trace_data = load_and_parse_trace_json(task_folder / "ui_task_trace.json")
        except Exception:
            return ErrorMessgage.INVALID_TASK_TRACE, ReponseCode.TASK_ERROR
        task_result_path = task_folder / "ui_task_result.json"
        if os.path.exists(task_result_path):
            if model_name == "uipath":
                try:
                    # for uipath better load with json to handle unicode characters
                    llm_output: str = json.dumps(
                        json.load(open(task_result_path, "r", encoding="utf-8")),
                    )
                except Exception:
                    with open(task_result_path, "r", encoding="utf-8") as f:
                        llm_output = f.read()
            else:
                with open(task_result_path, "r", encoding="utf-8") as f:
                    llm_output = f.read()
        else:
            if model_name == "uipath":
                return ErrorMessgage.NO_TASK_RESULT, ReponseCode.TASK_ERROR
            else:
                llm_output = ""

        # llm_outputs_str = flatten_llm_outputs(llm_outputs)
        gpt_response, response = auto_eval_by_gpt4v(
            task_prompt=task_prompt,
            images=images,
            llm_outputs=[llm_output],
            openai_client=client,
            api_model=args.api_model,
            max_imgs_eval=args.max_attached_imgs,
            max_doms_eval=args.max_attached_doms,
            trace_data=trace_data,
        )
        return gpt_response, response
    else:
        return ErrorMessgage.NO_TASK_TRACE, ReponseCode.TASK_ERROR


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", type=pathlib.Path)
    parser.add_argument("--api_model", default="gpt-4.1-2025-04-14", type=str, help="api model name")
    parser.add_argument("--max_attached_imgs", type=int, default=5)
    parser.add_argument("--max_attached_doms", type=int, default=0)
    parser.add_argument("--model_name", default="uipath", type=str, help="uipath, openai, claude")
    args = parser.parse_args()

    client = _get_openai_client()
    webs = [
        "Allrecipes",
        "Amazon",
        "Apple",
        "ArXiv",
        "BBC News",
        "Booking",
        "Cambridge Dictionary",
        "Coursera",
        "ESPN",
        "GitHub",
        "Google Flights",
        "Google Map",
        "Google Search",
        "Huggingface",
        "Wolfram Alpha",
        "Google",
        "WolframAlpha",
    ]

    responses: dict[str, dict[str, int | None]] = {k: {} for k in webs}
    responses_flat_completed_only: list[int] = []
    selected_webs = webs
    total = 0
    correct = 0
    outdir = args.results_dir / "eval"
    os.makedirs(outdir, exist_ok=True)
    for web in selected_webs:
        task_folders = (args.results_dir / web).glob("*")
        for task_folder in task_folders:
            eval_output_path = os.path.join(outdir, f"{web}_{os.path.basename(task_folder)}_eval.txt")
            print("\nEvaluating for", task_folder)
            run_folders = task_folder.glob("*")
            last_timestamp = max([int(run_folder.name) for run_folder in run_folders])
            lastest_run_folder = task_folder / str(last_timestamp)
            print("  run:", str(last_timestamp), lastest_run_folder)
            gpt_response, response = _get_response_from_task_folder(lastest_run_folder, args, client, model_name=args.model_name)
            responses[web][task_folder.name] = response
            if response is not None:
                responses_flat_completed_only.append(response)
            print(f"   result: {response}")
            total += 1
            correct += response == 1
            print("  accumulated accuracy:", correct / max(total, 1))
            with open(eval_output_path, "w", encoding="utf-8") as f:
                f.write(gpt_response)

    with open(os.path.join(outdir, "responses.json"), "w") as f:
        f.write(json.dumps(responses, indent=4))


if __name__ == "__main__":
    uiagent_opt = deepcopy(opt)
    uiagent_opt["prompt_options"].update(agent_prompt_options)
    uiagent_opt["engine_config"].update(agent_engine_options)
    main()
