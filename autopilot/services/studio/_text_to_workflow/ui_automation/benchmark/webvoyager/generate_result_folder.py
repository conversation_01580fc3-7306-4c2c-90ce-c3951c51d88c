#!/usr/bin/env python3
import argparse
import os
import shutil


def get_latest_run_path(eval_dir):
    """
    Given an evaluation directory, finds the subdirectory with the latest timestamp.
    Assumes subdirectory names are timestamps in the format YYYYMMDDHHMMSS.
    """
    # Filter for directories that look like timestamps (all digits, length 14)
    run_dirs = [d for d in os.listdir(eval_dir) if os.path.isdir(os.path.join(eval_dir, d)) and d.isdigit() and len(d) == 14]
    if not run_dirs:
        return None
    # Lexicographical order works for timestamps in this format
    latest_run = max(run_dirs)
    return os.path.join(eval_dir, latest_run)


def copy_contents(src, dst):
    """
    Copies all files and subdirectories from src to dst.
    """
    if not os.path.exists(dst):
        os.makedirs(dst)
    for item in os.listdir(src):
        s_item = os.path.join(src, item)
        d_item = os.path.join(dst, item)
        if os.path.isdir(s_item):
            shutil.copytree(s_item, d_item)
        else:
            shutil.copy2(s_item, d_item)


def main():
    parser = argparse.ArgumentParser(description="Copy files from the latest run in a specified evaluation folder.")
    parser.add_argument("--input-root", required=True, help="Path to the input root folder (ROOT)")
    parser.add_argument("--output-root", required=True, help="Path to the input root folder (ROOT)")
    args = parser.parse_args()

    input_root = args.input_root
    output_root = args.output_root

    os.makedirs(output_root, exist_ok=True)

    # Loop over website folders (WEB_i) in input_root
    for web in os.listdir(input_root):
        web_path = os.path.join(input_root, web)
        print(web_path)
        if not os.path.isdir(web_path):
            continue

        # Loop over task folders (TASK_j) in each website folder
        for task in os.listdir(web_path):
            task_path = os.path.join(web_path, task)
            if not os.path.isdir(task_path):
                continue

            # Get the latest run folder (timestamp folder) inside eval_dir
            latest_run_path = get_latest_run_path(task_path)
            if latest_run_path is None:
                print(f"Skipping: No run folders found in {task_path}")
                continue

            # Build the destination path: OUTPUT_ROOT/EVAL/WEB_i/TASK_j
            dest_path = os.path.join(output_root, web, task)
            print(f"Copying from {latest_run_path} to {dest_path}...")
            copy_contents(latest_run_path, dest_path)

    print("Copying completed.")


if __name__ == "__main__":
    main()
