""" """

import json
import pathlib

from attr import dataclass
from glom import PathAccessError, glom


@dataclass
class IterationImage:
    loop_iteration: int
    image_base64: str


@dataclass
class LLMOutput:
    loop_iteration: int
    results_summaries: list[str]


def load_and_parse_trace_json(
    json_path: pathlib.Path,
) -> tuple[str, list[IterationImage], list[LLMOutput]]:
    """This function loads a UITask json log and extracts the images and LLM outputs."""
    with open(json_path, "r", encoding="utf-8") as fr:
        data = json.load(fr)

    task: str = glom(data, "Prompt.Prompt")
    images: list[IterationImage] = []
    trace_data: list[str] = []
    llm_outputs: list[LLMOutput] = []
    for _, iteration in enumerate(data["Iterations"]):
        images.append(
            IterationImage(
                loop_iteration=int(iteration["LoopIteration"]),
                image_base64=iteration["ImageBase64"],
            )
        )
        if "TraceData" in iteration:
            trace_data.append(iteration["TraceData"])
        # Actions -> inputAction -> Action -> parameters -> result -> summary
        results_txt = []
        for _, action in enumerate(iteration["Actions"]):
            try:
                results_txt.append(glom(action, "InputAction.Action.parameters.result.summary"))
            except PathAccessError:
                pass

        if len(results_txt) > 0:
            llm_outputs.append(
                LLMOutput(
                    loop_iteration=int(iteration["LoopIteration"]),
                    results_summaries=results_txt,
                )
            )

    return task, images, llm_outputs, trace_data


def flatten_llm_outputs(llm_outputs: list[LLMOutput]) -> list[str]:
    """Flattens the list of LLMOutput objects to a list of strings."""
    return [summary for llm_output in llm_outputs for summary in llm_output.results_summaries]


def test():
    JSON_PATH = pathlib.Path("/home/<USER>/dev/benchmarks/WebVoyager/638739653223252382_1012fd51-d036-4b3b-a5c5-4a2e516d0c89.json")
    task, images, llm_outputs = load_and_parse_trace_json(JSON_PATH)
    flat_llm_outputs = flatten_llm_outputs(llm_outputs)
    print(images)
    print(llm_outputs)
    print(
        f"- Task: {task}\n"
        f"- Last summary: {flat_llm_outputs[-1] if flat_llm_outputs else 'No summaries found'}\n"
        f"- Extracted {len(images)} images and {len(flat_llm_outputs)} LLM outputs.\n"
    )


if __name__ == "__main__":
    test()
