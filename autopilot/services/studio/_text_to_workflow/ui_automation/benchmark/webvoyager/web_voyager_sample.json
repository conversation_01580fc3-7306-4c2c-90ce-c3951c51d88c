[{"web_name": "Allrecipes", "id": "Allrecipes--27", "ques": "Locate a chicken curry recipe on Allrecipes that has been reviewed more than 30 times and has a rating of at least 4 stars. Provide a summary of the recipe including ingredients, preparation time, and cooking instructions.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--29", "ques": "Search for a Mediterranean-style grilled fish recipe on Allrecipes that includes ingredients like olives, has at least a 4-star rating, and more than 25 reviews. Detail the ingredients, cooking method, and total time required for preparation and cooking.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--6", "ques": "Search for a vegetarian lasagna recipe that has at least a four-star rating and over 500 reviews.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--39", "ques": "Locate a recipe for sushi rolls on Allrecipes with a minimum of 20 reviews. Show the Nutrition Facts and the main ingredients. Tell me how to store these rolls.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--8", "ques": "Search for a recipe for <PERSON><PERSON> on Allrecipes that has at least 200 reviews and an average rating of 4.5 stars or higher. List the main ingredients required for the dish.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--15", "ques": "Choose a dessert recipe on Allrecipes with a prep time of less than 30 minutes, has chocolate as an ingredient, and has a user rating of 4 stars or higher. Provide the name of the recipe, ingredients list, and step-by-step instructions.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--5", "ques": "Search for a popular Pasta Sauce with more than 1000 reviews and a rating above 4 stars. Create a shopping list of ingredients for this recipe.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--34", "ques": "Locate a baked salmon recipe on Allrecipes that has at least 50 reviews and a rating of 4.5 stars or higher. Note the primary seasoning or herb used and the estimated cooking time.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--43", "ques": "Find a recipe for a vegan pumpkin pie on Allrecipes with a minimum four-star rating and a total cook time exceeding 1 hour.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--36", "ques": "Locate a recipe for an American apple pie on Allrecipes with a rating of at least 4 stars and more than 50 reviews. Note the maximum temperature mentioned in the Directions.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--22", "ques": "Find a recipe for a healthy avocado salad on Allrecipes that has a preparation time of less than 20 minutes and more than 30 user reviews. Include the nutritional information per serving.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--42", "ques": "Find a recipe for banana bread with more than 200 reviews and a rating of at least 4.0 stars on Allrecipes.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--18", "ques": "Find a recipe for a vegetarian lasagna that has over 300 reviews and an average rating of 4.5 or higher on Allrecipes.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--31", "ques": "Search for a seafood paella recipe on Allrecipes with a minimum of 4.5 stars rating and at least 50 reviews. The recipe should include shrimp and mussels. Provide the ingredients, total time, and an overview of the preparation steps.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--38", "ques": "Find a French ratatouille recipe on Allrecipes with a 4-star rating or higher and at least 15 reviews. Note the variety of vegetables included and the overall cooking time.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--14", "ques": "Search for a recipe that includes \"chicken breast\" and \"quinoa\" with preparation time under 30 minutes on Allrecipes.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--7", "ques": "Find a popular recipe for a chocolate chip cookie and list the ingredients and preparation steps.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--30", "ques": "Find a recipe for a vegan smoothie bowl on Allrecipes that includes bananas and leaves, has more than 20 reviews, and a rating of at least 4 stars. Provide a list of ingredients, preparation time, and a summary of the recipe steps.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--44", "ques": "List at least 6 holiday recipes sections mentioned in the Occasions section of Allrecipes.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--37", "ques": "Search for a Greek salad recipe on Allrecipes that has a prep time of under 25 minutes and more than 15 reviews. Include the primary cheese used and the type of dressing recommended.", "web": "https://www.allrecipes.com/"}, {"web_name": "Amazon", "id": "Amazon--10", "ques": "Find the cost of a 2-year protection for PS4 on Amazon.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--35", "ques": "Find a men's leather wallet on Amazon with RFID blocking, at least 6 card slots, and priced below $50. Check if it's available for FREE delivery.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--3", "ques": "Find climbing gears and sort the results by price high to low. Answer the first 3 results after sorting.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--2", "ques": "Find a gaming desktop with Windows 11 Home, and the disk size should be 1TB.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--29", "ques": "Find a set of solar-powered garden lights on Amazon with a minimum pack of 10 lights. They should be LED and priced under $50.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--15", "ques": "Find a pair of mens running shoes in black, size 7, 4+ stars and under $50 and add them to my cart on Amazon.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--33", "ques": "Search for a portable air conditioner on Amazon suitable for a room size of 300 sq ft, with energy efficiency rating, and compare the prices of the top three search results.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--11", "ques": "Find a stainless steel kitchen sink with double bowls on Amazon. Sort the results and find the cheapest one with FREE delivery.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--18", "ques": "Open Amazon's home page and tell me what the deal is that is going on at the moment, list the names of at least 2 items that are on offer and tell me what percent off they are.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--30", "ques": "Locate the highest-rated fiction book released in 2025 on Amazon, with a minimum of 50 customer reviews.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--32", "ques": "Search for an electric kettle on Amazon with a capacity of at least 1.5 liters, made of stainless steel, and with a customer rating of 4 stars or above.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--21", "ques": "Find a stainless steel, 12-cup programmable coffee maker on Amazon. The price range should be between $100 to $200. Report the one with the 4+ customer rating.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--34", "ques": "Find a beginner's acrylic paint set on Amazon, with at least 24 colors, suitable for canvas painting, and priced under $40.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--22", "ques": "Search for a set of non-stick, oven-safe cookware on Amazon. The set should include at least 10 pieces and be priced under $150.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--37", "ques": "Locate a queen-sized bedspread on Amazon with a floral pattern, and check if it's available in blue color.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--26", "ques": "Find a portable Bluetooth speaker on Amazon with a water-resistant design, under $50. It should have a minimum battery life of 10 hours.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--24", "ques": "Browse for a compact air fryer on Amazon with a capacity of 2 to 3 quarts. It should have a digital display, auto shutoff and be priced under $100.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--17", "ques": "Show me the list of baby products that are on sale and under 10 dollars on Amazon. Provide at least 2 on sale products", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--25", "ques": "Search for a queen-sized, hypoallergenic mattress topper on Amazon. It should have a memory foam material and be priced between $50 to $100.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--1", "ques": "Search for women's golf polos in m size, priced between 50 to 75 dollars, and save the lowest priced among results.", "web": "https://www.amazon.com/"}, {"web_name": "Apple", "id": "Apple--29", "ques": "On Apple's website, check the estimated battery life of the latest MacBook Air during web browsing in Tech Specs.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--4", "ques": "How much does it cost to buy a Macbook pro, 16-inch, Apple M4 Max chip with 16-core CPU, 40-core GPU, 64GB unified memory, 1TB SSD.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--13", "ques": "How many colors does the latest MacBook Air come in?", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--39", "ques": "Find solutions on Apple's website if you forgot your Apple ID password.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--34", "ques": "Identify the size and weight for the Apple TV 4K and list the Siri Remote features introduced.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--31", "ques": "On Apple's website, what is the slogan for the latest Apple Watch Series.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--32", "ques": "Investigate the trade-in value for an iPhone 11 Pro Max on Apple's website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--18", "ques": "Check if there are trade-in offers for the latest model of iPhone.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--12", "ques": "What Apple Repair ways are mentioned on apple website, answer 2 of them.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--26", "ques": "Find the maximum video recording resolution supported by the latest iPad mini on the Apple website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--15", "ques": "On Apple's website, how many different types of keyboards are available when customizing your 14-inch MacBook Pro?", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--3", "ques": "Find the latest model of the iPhone and compare the price and screen size between the pro and pro max.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--21", "ques": "Identify the available storage options for the latest iPad Pro on the Apple website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--28", "ques": "On the Apple website, find out if the Mac Mini can be configured with a GPU larger than 16-core.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--33", "ques": "Look for the color options available for the newest iMac.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--30", "ques": "Check the storage options and prices for the latest iPad Pro models on Apple's website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--36", "ques": "Browse Apple Music on the entertainment section of the Apple's website, and see which singers' names are included in the pictures on this page.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--27", "ques": "On Apple's website, check if the HomePod mini in store is available in multiple colors and list them.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--22", "ques": "Find out the trade-in value for an iPhone 13 Pro Max in good condition on the Apple website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--8", "ques": "Identify and list the specifications of the latest iPad model released by Apple, including its storage options, processor type, and display features.", "web": "https://www.apple.com/"}, {"web_name": "ArXiv", "id": "ArXiv--6", "ques": "How many figures and tables are in the paper \"On the Sentence Embeddings from Pre-trained Language Models\"?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--22", "ques": "Locate the ArXiv Help section and find instructions on how to subscribe to daily listing emails for new submissions in a specific category.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--38", "ques": "Find the ArXiv Blog on the ArXiv website and summarize the content of its latest article.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--29", "ques": "On ArXiv, search for papers with 'Neural Network Optimization' in the title published in 2023, and provide the number of such papers.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--24", "ques": "Identify the most recent paper related to 'graph neural networks' on ArXiv and determine the affiliation of the first author.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--36", "ques": "Search 'CVPR 2023' and 'CVPR2023' through journal ref on ArXiv to see how many results there are respectively.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--27", "ques": "On ArXiv, what categories does Economics include, and what are their abbreviations?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--19", "ques": "Which university maintains and manages ArXiv. Accessing the university's website from ArXiv, how many underegraduate students are currently at the university.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--4", "ques": "Find the most recent research papers in Astrophysics of Galaxies. How many papers have been announced in the last day?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--33", "ques": "Query ArXiv for the latest research article in the category of Systems and Control under Computer Science. Summarize the main objective or hypothesis presented in the paper and provide the names of the authors.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--5", "ques": "Search papers about \"quantum computing\" which has been submitted to the Quantum Physics category on ArXiv. How many results in total. What if search in all archives?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--12", "ques": "Find store in arXiv Help, tell me how many styles of arXiv Logo Shirt are available?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--30", "ques": "Look up the submission guidelines on ArXiv for submitting a paper and tell me the formats for figures.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--42", "ques": "Find an article published between 1 January 2000 and 1 January 2005 that requires Support Vector Machines in the title and its Journey ref is ACL Workshop.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--2", "ques": "Look up the most recent papers related to 'cs.CL', select one and show its abstract.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--16", "ques": "Locate the latest research about gravitational waves that were uploaded to ArXiv this week and provide a brief summary of one article's main findings.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--41", "ques": "Find the button to share arxiv non-profit store and follow the QR code to share the shop. Then add arXiv Forever short sleeve (XL) to your cart.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--0", "ques": "Search for the latest preprints about 'quantum computing'.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--37", "ques": "Find the names of people in ArXiv's Leadership Team.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--31", "ques": "Search ArXiv for papers with 'Graph Neural Networks' in the abstract that were submitted between Jan 1, 2025, and Jan 3, 2025, and determine how many of these papers have more than five authors.", "web": "https://arxiv.org/"}, {"web_name": "BBC News", "id": "BBC News--14", "ques": "Find a news article on BBC News about the impact of the recent tech industry layoffs on the global economy. Summarize the key points and the name of the author, and provide the date of publication.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--13", "ques": "Search for recent news related to <PERSON> and summarize the main points.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--4", "ques": "Find the latest article regarding the economic implications of climate change in Europe as reported by BBC News and summarize the central points.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--22", "ques": "Determine the current top business story on BBC News and give a brief overview of its economic implications.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--36", "ques": "Look up recent articles in the Africa news section in World, summarize what topics most of these news are about", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--9", "ques": "Find out which musician made the headlines in Music News.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--3", "ques": "Check the leaderboard for Golf's DP World Tour in the SPORT section, what was the name of the most recent tournament, and how many teams have a Total of -10 strokes.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--24", "ques": "Search the latest article about space exploration on BBC News and summarize its key points.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--28", "ques": "Find the Market Data section on BBC News and tell me which company the data comes from.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--35", "ques": "In the Asia section, browse and identify the most recent report about technological advancements and summarize its content.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--20", "ques": "Find the latest article in the Green Living section on BBC News and provide a summary of its main points.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--11", "ques": "Find out how many teams are in the Scottish Premiership of the Football Tournament and when did the Hibernian team's most recent match start?", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--15", "ques": "What does the current headline in Natural Wonders tell about.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--31", "ques": "Check the Sports section for the result of the most recent Manchester United football match.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--12", "ques": "Find a picture in the travel section that contains food, tell me what the food is called and what region it comes from.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--18", "ques": "Visit BBC News Audio, What are the best PodCasts for 2024? List 2 of them.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--34", "ques": "Find The SpeciaList section in Travel and browse the page to see which cities are mentioned.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--29", "ques": "Visit BBC News Audio and find out which podcast episode is currently featured as the \"New Releases\".", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--7", "ques": "Find a AI-related story under Technology of Business. What is in the first picture in the story?", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--39", "ques": "Check the Horse Racing results in Sport section, browse all the games that took place yesterday and see which one had the highest number of runners.", "web": "https://www.bbc.com/news/"}, {"web_name": "Booking", "id": "Booking--10", "ques": "Find a well-reviewed hotel in Paris with available bookings suitable for a couple (2 adults) on May 14-21, 2025, that offers free cancellation options.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--39", "ques": "Find a pet-friendly hotel with parking available in downtown Toronto for the stay of May 24-26, 2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--0", "ques": "Find a Mexico hotel with deals for May 25-26.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--27", "ques": "Locate a hotel in Melbourne offering free parking and free WiFi, for a stay from June 28 to July 4, 2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--43", "ques": "Search for properties in Los Angeles, browse the results page to see what filters are available, list some of them.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--6", "ques": "Book one room which provides breakfast, and airport shuttle from May 22 to 25 in Los Angeles.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--31", "ques": "Search for hotels in Rio de Janeiro from May 1-7, 2025, check the Brands filter to see which brand has the most hotels and which brand has the fewest.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--1", "ques": "Find the cheapest available hotel room for a three night stay from 1st May in Jakarta. The room is for 2 adults, just answer the cheapest hotel room and the price.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--20", "ques": "Locate a hotel in Rome with a good rating (7 or above) that offers free cancellation and breakfast included, for a three-night stay from May 28 to June 2, 2025, for two adults.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--32", "ques": "Look for hotels in Sydney from May 24 to May 27, 2025, on Booking. Once the Swimming Pool and Airport Shuttle filters are applied, what is the total number of hotels available?", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--42", "ques": "Search for a hotel in Hokkaido for the period May 1 to May 7, 2025, with a rating of 9+, check out its user reviews, which categories are greater than 9 and which are less than 9?", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--3", "ques": "Find a hotel with 4 star and above rating in Los Angeles for 3 days from May 18th.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--21", "ques": "Find a hotel in Sydney with a rating of 8 or higher, providing free Wi-Fi and parking, available for a four-night stay starting on May 10, 2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--25", "ques": "Search for a hotel in Lisbon with airport shuttle, rated 8.5 or above, available for a six-night stay from May 1 to May 7, 2025, for two adults, breakfast included.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--16", "ques": "Look for a hotel in Paris with a user rating of 9 or higher and available for a 5-night stay starting May 15, 2025. The hotel should also offer free Wi-Fi and breakfast included in the price. Provide the name, location, and price per night.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--2", "ques": "Find a hotel in Ohio From May 20th to May 23th for 3 adults and 2 rooms.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--9", "ques": "Find hotels for 2 adults in London with a price less than 250 dollars for four days starting from April 25. You must browse the page and offer at least 3 options.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--28", "ques": "Find a hotel in Dubai with a swimming pool, for a week-long stay from June 22 to June 29, 2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--23", "ques": "Identify a hotel in Tokyo with a spa and wellness center, rated 9 or above, with availability for a five-night stay starting on May 20, 2025. Check if free cancellation is offered.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--36", "ques": "Search for a budget hotel in Rome under $100 per night for one adult from May 20 to May 23, 2025. Sort the results by price, identify if any of top three results offer breakfast.", "web": "https://www.booking.com/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--3", "ques": "Look up the definition, pronunciation, and examples of the word \"zeitgeist.\"", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--36", "ques": "Search for guidelines on using indirect speech in English, with examples of how to change direct speech to indirect speech, on the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--10", "ques": "Look up the definition and pronunciation of the word \"impeccable\" and also find an example sentence using that word.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--30", "ques": "Find the grammar for present perfect simple uses in English, including examples of affirmative, negative, and interrogative sentences, on the Cambridge Dictionary website.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--40", "ques": "Look up the definition, pronunciation in UK English, and at least one example using the word 'mitigate'.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--42", "ques": "Convert the Cambridge Dictionary homepage from English (UK) to Deutsch.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--14", "ques": "Use the Cambridge Dictionary to find the pronunciation, definition, and one example sentence for the word \"concatenate\".", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--28", "ques": "Search for \"feel giddy\" in Cambridge Dictionary's Thesaurus and list the synonyms the dictionary provides.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--21", "ques": "Search for the word \"ephemeral\" on Cambridge Dictionary and find its translation into Spanish.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--29", "ques": "Go to the Plus section of Cambridge Dictionary, find Image quizzes and do an easy quiz about Animals and tell me your final score.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--34", "ques": "Use the Cambridge Dictionary to understand the rules for forming and using comparative and superlative adjectives in English Grammar, including example sentences.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--11", "ques": "Look up the pronunciation and definition of the word \"ameliorate,\" and provide an example sentence using the word.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--41", "ques": "Find and browse Cambridge Dictionary Shop section, listing 3 items.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--39", "ques": "Try the Word Scramble game in the Plus section, Can you beat the clock by unscrambling the letters to spell the word? (Just try the first example.)", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--19", "ques": "Try a Cambridge Dictionary translation and tell me which company provided the translation.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--25", "ques": "Find two different meanings of the word \"harmony\" in the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--2", "ques": "Look up the pronunciation, definition, and example sentence for the word \"ubiquitous\" in UK and US English.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--9", "ques": "Look up the British pronunciation of the word \"euphoria\" and find an example sentence using that word on the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--16", "ques": "Look up the definition of \"cryptocurrency\" on Cambridge Dictionary, provide the pronunciation, and use it in two example sentences that illustrate different contexts.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--6", "ques": "Search for the word \"sustainability\" on the Cambridge Dictionary, what is the translation of sustainability into Chinese and French in the dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Coursera", "id": "Coursera--16", "ques": "Identify a course on Coursera named 'Introduction to Finance: The Basics', who is the course instructor and what other courses does he/she teach.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--22", "ques": "Identify a Specialization on Coursera that focuses on 'Human Resource', list the courses included in this Specialization, and the institution offering it.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--20", "ques": "Find an Intermediate-level online course on Coursera about 'Blockchain Technology' which lasts between 1 to 4 weeks, and is provided by a well-known institution. Also, note the course's main goals and the instructor's name.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--3", "ques": "Identify a new course or Specialization on Coursera related to Python Data Science, sort the courses by newest, what the first course is and which institution offers it.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--27", "ques": "Search for a Specialization on Coursera about 'Data Visualization' that includes a project. Provide the name of the Specialization, the institution offering it, and the skills that will be developed by completing it.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--31", "ques": "Search for the course 'Exploring Quantum Physics' on Coursera, offered by the University of Maryland, College Park. Identify the percentage (rounded) of 5-star ratings in the reviews.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--32", "ques": "Search for 'Data Analysis' courses on Coursera. Apply filters to find courses that are 'Beginner Level' and have a duration ranging from 1 to 3 months. Determine the total count of courses that match these specifications.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--4", "ques": "Identify a course or Specialization on Coursera that helps business process management with with a rating 4.7.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--23", "ques": "Find a course on Coursera about 'Artificial Intelligence Ethics', which has a duration of less than 5 weeks and has been rated 4.5 stars or higher. Provide the course name and the instructor's name.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--11", "ques": "Search for a Specialization on Coursera about project management that is produced by a university, show a testimonial for this Specialization.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--39", "ques": "Find the Space Safety course offered by TUM on Coursera. How many videos are there in module 2? What is the name of each video?", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--41", "ques": "Browse online degrees section on Coursera and list 3 Bachelor's degree programmes.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--18", "ques": "Identify a Coursera course that teaches JavaScript, which is beginner-friendly and includes a certificate upon completion.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--26", "ques": "Identify a Specialization on Coursera that offers an overview of 'Renewable Energy'. The Specialization should be beginner-level and include a course on Renewable Energy Futures. Note the instructor's name and the number of weeks required to complete the course if I spend 5 hours a week.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--5", "ques": "Identify a Specialization on Coursera that teaches C++ programming for beginners, provide the name and what the learning outcomes are.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--37", "ques": "Browse the Coursera homepage and list at least three free courses.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--12", "ques": "Look for a Coursera course (not Specialization) that teaches Java programming basics.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--0", "ques": "Find a beginner-level online course about '3d printing' which lasts 1-3 months, and is provided by a renowned university.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--14", "ques": "Find a course on Coursera related to Introductory Project Management that includes modules on Agile methodology.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--33", "ques": "Find a beginner level Coursera course related to \"Internet of Things (IoT)\" with a high rating. Provide the course name, instructor's name, and a brief summary of the skills that will be taught.", "web": "https://www.coursera.org/"}, {"web_name": "ESPN", "id": "ESPN--24", "ques": "Find the final score from the most recent NFL game broadcast on ESPN, including the teams' names and the date of the match.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--1", "ques": "Check the latest articles on ESPN for updates on any trades that occurred in the NBA within the past 2 days.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--37", "ques": "Check out <PERSON><PERSON><PERSON> to see how many games he has played in his career so far.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--8", "ques": "Find information on ESPN about the top three scoring leaders in the NBA as of the last day of the regular season, and note which teams they play for.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--36", "ques": "Search for <PERSON>'s last 5 games, which teams has he played for, and what are the results?", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--5", "ques": "Identify the top scorer in the NBA from the latest completed game and note down the points scored, the team they play for, and their position on the team.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--27", "ques": "Search on ESPN for how many teams have 'Golden' in their name and how many of them are in the NHL.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--23", "ques": "Find the result of the latest basketball game between the Miami Heat and the New York Knicks, including the final score and top rebounder from the match.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--12", "ques": "The first three Top Headlines in the current ESPN home page correspond to which sports leagues?", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--42", "ques": "Check out NCAAM standings on ESPN, what are the teams with equal wins and losses in the America East Conference currently?", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--41", "ques": "Find out which four teams the NFC North contains in the NFL on ESPN.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--17", "ques": "Check out the NBA Basketball Power Index 2023-24 to see which teams are in first place and which are in last place.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--0", "ques": "Look up the current standings for the NBA Eastern Conference on ESPN.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--39", "ques": "Check the New York Jets Depth Chart in the NFL section of ESPN and identify the players listed as injured in the 2ND position.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--40", "ques": "Browse the ESPN+ page from ESPN for a brief summary of what ESPN+ Tools is used for.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--31", "ques": "Who has the heaviest weight among infielders in the New York Yankees Roster 2023-24?", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--32", "ques": "Review yesterday's NHL game results on ESPN, focusing on teams' performance.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--10", "ques": "Check ESPN for the score and a brief recap of the latest college football championship game.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--9", "ques": "Search on ESPN for how many teams have Los Angeles in their name and how many of them are NBA.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--4", "ques": "Check ESPN for the final scores of NBA games that were played yesterday.", "web": "https://www.espn.com/"}, {"web_name": "GitHub", "id": "GitHub--2", "ques": "Look for the trending Python repositories on GitHub with most stars.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--30", "ques": "Search for an open-source project related to 'blockchain technology' on GitHub updated in the past 15 days and list the top five contributors.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--40", "ques": "Select Sign up on the GitHub homepage to see if email '<EMAIL>' already exists.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--38", "ques": "Identify and report the most popular (by stars) open-source repo related to cybersecurity on GitHub.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--18", "ques": "Identify and report the most popular (in terms of stars) open-source image processing tool on GitHub.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--29", "ques": "Compare the maximum number of private repositories allowed in the Free and Pro plans in GitHub Pricing.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--35", "ques": "Check the latest release version of React and the date it was published on GitHub.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--24", "ques": "Locate the GitHub repository for the open-source project \"angular\" and identify the last three issues closed.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--3", "ques": "Find out how much more package storage the Enterprise version has over Team in GitHub Pricing.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--16", "ques": "Find the GitHub Skill section and how many courses are under the 'First day on GitHub' heading.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--13", "ques": "Identify the latest top-trending open-source project in the category of 'Machine Learning' on GitHub, and check the number of stars it has received.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--8", "ques": "Look up the latest stable release version of Vuex and find out when it was published.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--5", "ques": "Find a Python repository on GitHub that has been updated in the past 2 days and has at least 500 stars.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--6", "ques": "Search for an open-source project related to 'cryptocurrency wallet' updated in the past 30 days and provide the top three contributors.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--25", "ques": "Search for a 'virtual reality' related repository on GitHub updated in the last 10 days with at least 200 stars and summarize its main objective.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--36", "ques": "Identify a new open-source project on GitHub related to 'AI agriculture' that created in 2022, and note its main programming language and description.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--4", "ques": "Find a popular JavaScript repository created in the last 30 days on GitHub with a Readme file.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--20", "ques": "Open GitHub Copilot's FAQs to find the official answer to when Copilot chat can be used on mobile.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--14", "ques": "Locate the repository for the open-source project \"vscode\" and identify the top three contributors.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--0", "ques": "Search for an open-source project related to 'climate change data visualization' on GitHub and report the project with the most stars.", "web": "https://github.com/"}, {"web_name": "Google Flights", "id": "Google Flights--18", "ques": "Compare flight options from New York to Tokyo for a round trip leaving on May 25, 2025, and returning on June 15, 2025, for one adult. Prioritize the comparisons by the shortest travel time.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--28", "ques": "Find the best-priced round-trip flight from Seattle to Paris, departing on June 27, 2025, and returning on July 1, 2025, with a maximum of one stop.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--10", "ques": "Locate the cheapest round-trip flights from New York to Tokyo leaving on May 25, 2025, and returning on June 15, 2025.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--6", "ques": "Search for a flight on May 19 and return on May 26 from Tel Aviv to Venice and Select First Class.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--11", "ques": "Compare the prices for round-trip flights from New York to Tokyo for a departure on May 10, 2025, and a return on May 24, 2025, and select the option with the least number of stops.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--31", "ques": "Find a one-way economy flight from Auckland to Honolulu on May 25, 2025, browse the full page and display a flight option with the most stops.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--16", "ques": "Find the cheapest one-way flight from New York to Tokyo departing on May 15, 2025, and provide the airline and total flight duration.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--23", "ques": "Search for a one-way flight from Mumbai to Vancouver on May 28, 2025, filtering the results to show only 1-stop flights.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--9", "ques": "Find a one way economy flight from Pune to New York in May. 15th and show me how long it will take for flight transfer.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--13", "ques": "Find the cheapest round-trip flight option from New York City to Tokyo for a departure on May 10, 2025, and a return on May 24, 2025.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--8", "ques": "Search a one-way filght from Dublin To Athens Greece for 1 Adult that leaves on May 30 and analyse the price graph for the next 2 months.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--38", "ques": "Compare the prices and flight durations for economy class flights from Oslo to Dubai, departing on June 8, 2025, and show the options with no more than two layovers.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--25", "ques": "Find a one-way business class flight from Buenos Aires to Amsterdam on May 10, 2025, and provide the details of the flight with the shortest duration.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--0", "ques": "Book a journey with return option on same day from Edinburg to Manchester on May 28th and show me the lowest price option available.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--5", "ques": "Find flights from Chicago to London on 20 May and return on 23 May.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--7", "ques": "Find a round trip from Phoenix to Miami (May. 25th - May. 28th), show the First Class plane tickets for me that do not exceed $1320..", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--39", "ques": "Find a one-way flight from Prague to a city in Japan on June 20, 2025, which city in Japan is cheaper to go to, Tokyo or a certain city in Hokkaido?", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--20", "ques": "Book a round-trip flight from San Francisco to Berlin, departing on May 5, 2025, and returning on May 12, 2025, and find the option with the shortest total travel time.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--21", "ques": "Locate the lowest-priced one-way flight from Tokyo to Sydney for an adult, departing on May 25, 2025, and include the flight duration and number of layovers.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--17", "ques": "Find the cheapest round-trip flight from New York to Paris leaving on August 27, 2025, and returning on September 10, 2025.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Map", "id": "Google Map--14", "ques": "Find motorcycle parking near Radio City Music Hall.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--2", "ques": "Find Apple Stores close to zip code 90028", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--8", "ques": "Find a place to climb within 2 miles of zip code 90028.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--5", "ques": "Search for a parking garage near Thalia Hall in Chicago that isn't open 24 hours.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--1", "ques": "Tell me one bus stop that is nearest to the intersection of main street and Amherst street in Altavista.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--0", "ques": "Find 5 beauty salons with ratings greater than 4.8 in Seattle, WA.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--21", "ques": "Identify the nearest bus stop to the corner of Elm Street and Oak Street in Massachusetts.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--17", "ques": "Search for locksmiths open now but not open 24 hours in Texas City.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--40", "ques": "Find a restaurant in Boston that eats Boston lobster and asks for a rating of 4.6 or higher, and check out what a one-star review says.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--32", "ques": "Search for plumbers available now but not open 24 hours in Orlando, FL.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--11", "ques": "Locate a large store in Washington that has kids' and maternity products, also check if it has a parking lot.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--7", "ques": "Find bus stops in Alanson, MI", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--31", "ques": "First search New York's Central Park Zoo on Google Map, and then find the way to share the map. What is the generated sharing link?", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--22", "ques": "Find a Best Buy store near zip code 33139.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--15", "ques": "Find daytime only parking nearest to Madison Square Garden. Summarize what people are saying about it. ", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--30", "ques": "Locate a parking lot near the Brooklyn Bridge that open 24 hours. Review the user comments about it.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--6", "ques": "Find all Uniqlo locations in Chicago, IL.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--12", "ques": "Find 5 places that serve burgers near 44012 zip code and sort these 5 places by highest rating.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--37", "ques": "Locate a parking area in Salem and find a route from there to Marblehead, including map directions for better understanding.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--10", "ques": "Search for a park in the state of California called Castle Mountains National Monument and find out it's Basic Information.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Search", "id": "Google Search--31", "ques": "Discover which year <PERSON><PERSON><PERSON> scored the most goals in a single season.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--12", "ques": "Find the year that <PERSON> had the most touchdowns in a single seasson.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--27", "ques": "Check the current air quality index in Paris.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--8", "ques": "Find the video on YouTube: 'Oscars 2023: Must-See Moments!'. Tell me who the first comment displayed under that video belongs to, and how many thumbs up and replies it has.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--40", "ques": "Look up the elevation of Mount Kilimanjaro on Google Search.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--7", "ques": "Find the software requirements for iPhones that support AirDrop's ability to continue transmitting over the web when out of range.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--33", "ques": "Find and copy the SHA of the latest commit in the TensorFlow repository on GitHub, then find a textbox to paste and tell me what the SHA is.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--38", "ques": "Search for the next visible solar eclipse in North America and its expected date, and what about the one after that.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--11", "ques": "According to FlightAware, tell me the busiest airport last week and its total arrivals and departures last week.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--35", "ques": "Look up the latest research paper related to black holes published in the journal \"Nature Astronomy\".", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--41", "ques": "Look up the current statistics of air pollution level in Los Angeles using Google Search.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--20", "ques": "Find the release date for the latest \"Fast & Furious\" movie.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--39", "ques": "Identify the top-10 trending travel destination for 2024 through a blog, how many of them are in Asian.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--36", "ques": "Search for the most recent Nobel Prize winner in Physics and their contribution to the field.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--22", "ques": "Browse and list the top three trending topics this month in New York City.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--10", "ques": "Find the no. 1 weekly charts ranked artist based on Billboard and tell me 10 most played song by this artist until now.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--9", "ques": "Show the rating of Prometheus movie on IMDb and Rotten Tomatoes.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--3", "ques": "Show me a list of comedy movies, sorted by user ratings. Show me the Top 5 movies.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--28", "ques": "Check the IMDb and Metacritic scores of the movie \"Inception.\"", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--37", "ques": "Find the current top 3 super-earth planets and give a brief introduction to them.", "web": "https://www.google.com/"}, {"web_name": "Huggingface", "id": "Huggingface--15", "ques": "Identify the most downloaded models on Hugging face that use the PaddlePaddle library.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--2", "ques": "Discover three new and popular open-source NLP models for language translation released in the past month on Huggingface.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--37", "ques": "Browse the daily paper on Hugging Face. What is the title of the first article, how many upvotes has it received, and is there any related model or data release?", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--40", "ques": "Check out Text Embeddings Inference in Hugging face's Doc to summarise the strengths of the toolkit.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--8", "ques": "Retrieve an example of a pre-trained language model in natural language processing and identify the tasks it is specifically designed for, like translation or text summarization.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--41", "ques": "What is the current Text-to-3D model with the highest number of downloads and tell me are there Spaces that use the model.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--27", "ques": "Determine the most downloaded dataset related to Text Retrieval in NLP on Hugging Face.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--3", "ques": "Look up a model with a license of cc-by-sa-4.0 with the most likes on Hugging face.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--16", "ques": "Find information on the latest (as of today's date) pre-trained language model on Huggingface suitable for text classification and briefly describe its intended use case and architecture.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--19", "ques": "Explore and summarize the features of the most recent open-source NLP model released by Hugging Face for English text summarization.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--11", "ques": "Identify the latest updated image to video model available on Huggingface and summarize its main features.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--30", "ques": "Identify the most downloaded English-Chinese (en-zh) machine translation model on Huggingface and report its latest performance metrics and usage guidelines.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--0", "ques": "Find a pre-trained natural language processing model on Hugging Face that can perform sentiment analysis, and make sure the model's last update is within March 2023.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--20", "ques": "Locate a pre-trained natural language processing model on Hugging Face that specializes in named entity recognition (NER), confirm that the model was last updated in 2022 and has 1M+ downloads.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--35", "ques": "Find the latest Diffusion-related blog on Hugging Face, and read its intro or overview section to roughly summarize the content of the blog.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--9", "ques": "Find the most download machine translation model on Huggingface which focuses on English and Japanese (en-ja) and report the evaluation metrics stated for it.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--12", "ques": "Find the most recently updated machine learning model on Huggingface which focuses on Error Correction.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--4", "ques": "Locate an open-source conversational AI model on Hugging Face, trained in English and list its main features and applications.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--31", "ques": "Identify the latest machine learning model on Huggingface that specializes in detecting fake news, including the date of its last update.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--38", "ques": "Investigate the 'transformers' library in the Hugging Face documentation, focusing on how to add new tokens to a tokenizer.", "web": "https://huggingface.co/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--0", "ques": "derivative of x^2 when x=5.6", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--26", "ques": "Convert 15 kilograms of sulfuric acid to moles and display the percentage composition of H, S, and O by weight.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--45", "ques": "A 175cm tall, 85kg, 40yo man climbs 2500 steps at about 18cm per step and 40 steps per minute. summarise the Metabolic properties.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--30", "ques": "Calculate the estimated time to sunburn for different skin types when exposed to the sun at 1:00 pm with SPF 1 in Brazil.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--39", "ques": "Use Wolfram alpha to write the expression of the ellipse x^2 + 3 y^2 = 4 rotated 33 degrees counterclockwise.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--17", "ques": "Show the average price of movie ticket in Providence, Nashville, Boise in 2023.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--40", "ques": "Approximate amount of fat burned by a 28yo, 172cm tall, 70kg woman running for 30min at a pace of 6min/mile.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--24", "ques": "Solve the differential equation y''(t) - 2y'(t) + 10y(t) = 0 and display its general solution.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--22", "ques": "Determine the area of a regular hexagon with a side length of 7 cm.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--42", "ques": "What is the raw memory of a 100.2\" * 123.5\" true colour picture at 72 ppi?", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--5", "ques": "Show the solution of y\"(z) + sin(y(z)) = 0 from wolframalpha.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--25", "ques": "Calculate the final position and velocity of a projectile launched at 45 degrees with an initial speed of 30 m/s after 3 seconds.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--38", "ques": "Compute the length of a curve defined by y = 2x^3 - 3x^2 + 4x - 5 from x = 0 to x = 3.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--32", "ques": "Print all prime numbers between 1000 and 1200 using Wolfram alpha.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--23", "ques": "Calculate the population growth rate of Canada from 2020 to 2023 using Wolfram Alpha.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--31", "ques": "Using Wolfram Alpha, determine the current temperature and wind speed in Chicago, IL.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--7", "ques": "Give the final angle and final length after 6s of a Spring pendulum with spring equilibrium length=0.12m, initial length=0.24m, initial angle=80deg, mass=1kg, spring constant=120 N/m .", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--41", "ques": "What is the approximate Heart Rate Reserve of a 50 year old man who has a heart rate of 60bpm at rest.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--29", "ques": "Create a plot of cat curve using wolfram alpha.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--35", "ques": "Calculate the determinant of a 6x6 Hilbert matrix.", "web": "https://www.wolframalpha.com/"}]