# read eval texts file to get the final score statistics
import argparse
import glob
import json
import os
import pathlib

import pandas as pd
import tqdm

from services.studio._text_to_workflow.ui_automation.benchmark.webvoyager.auto_eval_studio import ErrorMessgage
from services.studio._text_to_workflow.ui_automation.benchmark.webvoyager.utils import webs


def compute_stats(folder):
    all_results = []
    for web in webs:
        task_folders = (folder / web).glob("*")
        for task_folder in task_folders:
            task_token_usage = {
                "requests": 0,
                "total_tokens": 0,
                "input_tokens": 0,
                "output_tokens": 0,
            }
            run_folders = task_folder.glob("*")
            last_timestamp = max([int(run_folder.name) for run_folder in run_folders])
            lastest_run_folder = task_folder / str(last_timestamp)
            trace_file = list(lastest_run_folder.glob("*_trace.json"))
            if len(trace_file) == 0:
                print(f"trace file not found in {lastest_run_folder}")
                continue
            trace_file = trace_file[0]
            with open(trace_file, "r", encoding="utf-8") as f:
                trace_data = json.load(f)
                if "TokenUsage" not in trace_data:
                    print(f"!!!!! TokenUsage not found in {trace_file}")
                    continue
                task_token_usage = trace_data["TokenUsage"]

            all_results.append({"web": web, "task": web + "__" + str(task_folder), **task_token_usage, "steps": len(trace_data["Iterations"])})

    all_results_df = pd.DataFrame(all_results)
    # print avarage
    all_results_mean = all_results_df.agg(
        {
            "steps": "mean",
            "requests": "mean",
            "total_tokens": "mean",
            "input_tokens": "mean",
            "output_tokens": "mean",
        }
    )

    print("Average usage statistics:")
    print(all_results_mean)
    return str(all_results_mean)


def compute_metrics(df):
    total_task = df.shape[0]
    total_task_error = df["task_error"].sum()
    total_eval_error = df["eval_error"].sum()
    total_success = df[df["status"] == "SUCCESS"].shape[0]

    denominator_1 = total_task - total_eval_error
    denominator_2 = total_task - total_task_error - total_eval_error

    accuracy = total_success / denominator_1 if denominator_1 != 0 else 0
    accuracy_ignore_task_error = total_success / denominator_2 if denominator_2 != 0 else 0

    return pd.Series(
        {
            "tasks": int(total_task),
            "errors": int(total_task_error),
            "eval_errors": int(total_eval_error),
            "success": int(total_success),
            "accuracy": accuracy,
            "accuracy_ignore_errors": accuracy_ignore_task_error,
        }
    )


def compute_scores(folder):
    eval_files = glob.glob(os.path.join(folder, "*_eval.txt"))
    results = []
    for eval in tqdm.tqdm(eval_files):
        with open(eval, "r", encoding="utf-8") as f:
            eval_name = os.path.basename(eval).replace("_eval.txt", "")
            web = eval_name.split("_")[0]
            text = f.read()
            task_error = 0
            eval_error = 0
            score = 0
            if ErrorMessgage.EVAL_NO_RESPONSE in text:
                status = ErrorMessgage.EVAL_NO_RESPONSE
                eval_error = 1
            elif ErrorMessgage.NO_TASK_RESULT in text:
                status = ErrorMessgage.NO_TASK_RESULT
                task_error = 1
            elif ErrorMessgage.NO_TASK_TRACE in text:
                status = ErrorMessgage.NO_TASK_TRACE
                task_error = 1
            elif ErrorMessgage.INVALID_TASK_TRACE in text:
                status = ErrorMessgage.INVALID_TASK_TRACE
                task_error = 1
            elif "NOT SUCCESS" in text:
                status = "NOT SUCCESS"
                score = 1
            elif "SUCCESS" in text:
                status = "SUCCESS"
            else:
                status = "UNKNOWN"

            item = {"web": web, "task": eval_name, "status": status, "task_error": task_error, "eval_error": eval_error, "score": score}

        results.append(item)

    result_df = pd.DataFrame(results)
    print(result_df)
    # web_metrics_df = result_df.groupby("web", group_keys=False).apply(compute_metrics).reset_index()
    total_metrics = compute_metrics(result_df)
    total_metrics.name = "All"

    # Group by the 'web' column and compute metrics for each group
    grouped_metrics = result_df.groupby("web").apply(compute_metrics)
    metrics = pd.concat([total_metrics.to_frame().T, grouped_metrics])

    # Define formatters for each column:
    formatters = {
        "tasks": lambda x: f"{int(x)}",
        "errors": lambda x: f"{int(x)}",
        "eval_errors": lambda x: f"{int(x)}",
        "success": lambda x: f"{int(x)}",
        "accuracy": lambda x: f"{x:.3f}",
        "accuracy_ignore_errors": lambda x: f"{x:.3f}",
    }

    # Convert the DataFrame to a formatted string:
    formatted_string = metrics.to_string(formatters=formatters)

    # Print the formatted string to the console:
    print(formatted_string)
    return formatted_string


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", type=pathlib.Path)
    args = parser.parse_args()
    scores_str = compute_scores(args.results_dir / "eval")
    usages_str = compute_stats(args.results_dir)

    # Write the formatted string to a text file:
    with open(args.results_dir / "metrics.txt", "w") as f:
        f.write(str(args.results_dir) + "\n\n")
        f.write(scores_str)
        f.write("\n\n")
        f.write(usages_str)


if __name__ == "__main__":
    main()
