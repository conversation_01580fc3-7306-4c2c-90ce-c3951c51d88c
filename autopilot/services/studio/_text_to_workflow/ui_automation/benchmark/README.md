
# Webvoyager
Workflow: https://github.com/UiPath/uitask_benchmark

After running workflows, the traces and results are uploaded to:
uitaskbenchmark > benchmark > web-voyager

https://portal.azure.com/#view/Microsoft_Azure_Storage/ContainerMenuBlade/~/overview/storageAccountId/%2Fsubscriptions%2F5db48574-8a20-418f-b488-1fafd8d021df%2FresourceGroups%2Fuipath-pf%2Fproviders%2FMicrosoft.Storage%2FstorageAccounts%2Fuitaskbenchmark/path/benchmark/etag/%220x8DD503D0F6BBEC9%22/defaultEncryptionScope/%24account-encryption-key/denyEncryptionScopeOverride~/false/defaultId//publicAccessVal/None

Each folder corresponds to a benchmark workflow run (= benchmark_id arg in the workflow)


To get score:
```bash
# first download the benchmark result folder, example "gemini_20250321"
azcopy copy "https://uitaskbenchmark.blob.core.windows.net/benchmark/web-voyager/gemini_20250321?sv=..." "." --recursive=true 

# use gpt4o for eval
# define .env with azure openai credentials:
# AZURE_OPENAI_ENDPOINT=https://autopilot-openai-eastus.openai.azure.com/
# AZURE_OPENAI_API_KEY=...
# OPENAI_API_VERSION=2025-01-01-preview
python auto_eval_studio.py --results_dir "C:\Users\<USER>\work\wingman\UIA\webvoyager_results\gemini_20250321" --max_attached_imgs=5


# convert uitask_trace.json to html
python .\convert_trace_to_html.py --results_dir="C:\Users\<USER>\work\wingman\UIA\webvoyager_results\gemini_20250321"
```