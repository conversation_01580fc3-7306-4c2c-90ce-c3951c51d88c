from dataclasses import dataclass
from enum import Enum
from typing import List, Literal

from pydantic import BaseModel, ConfigDict, Field


@dataclass
class TokenUsage:
    model: str = ""
    requests: int = 1  # number of requests to LLM
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    total_time: float = 0.0

    def to_json(self):
        return {
            "model": self.model,
            "requests": self.requests,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens,
            "total_tokens": self.total_tokens,
            "total_time": self.total_time,
        }


class SupportedActions(str, Enum):
    Click = "click"
    TypeInto = "type_into"
    GetText = "get_text"
    ExtractInfo = "extract_info_from_screen"
    Select = "select"
    Wait = "wait_load_completed"
    Finish = "finish"
    NavigateBack = "navigate_browser"
    Scroll = "scroll"
    Drag = "drag"
    KeyPress = "keypress"
    MouseMove = "mouse_move"
    UserQuestion = "user_question"

    def __str__(self):
        return self.value


class AgentResponseAction(BaseModel):
    description: str
    method_type: SupportedActions
    parameters: dict
    id: str | None = Field(None, description="Currently used in CU models to identify a tool call action.")
    result: dict | str | None = Field(None, description="Used to pass the result of a final action or to respond to a user question (client side).")
    execution_error_message: str = Field("", description="Error message if the step execution failed. Sent back by the client if the step fails.")
    detected_items: List[str] = Field([], description="Detected items in the action. Sent by the client when a misclick into a dropdown occurs.")

    model_config = ConfigDict(extra="forbid")


class AgentResponseStep(BaseModel):
    description: str | None = None
    actions: list[AgentResponseAction]
    thought: str | None = None
    image: str | None = None
    trace_data: str | None = None
    additional_parameters: dict | None = Field(None, description="Additional parameters for the step. You can pass model-specific parameters here.")

    title: str | None = None
    url: str | None = None
    model_config = ConfigDict(extra="forbid")


class PreviousStepsParameters(BaseModel):
    """
    Currently used fo Claude CU model, limiting the number of messages and images in the chat history.
    The image parameter is used to pass the current screen image to the next step (client side).
    """

    max_chat_history_messages: int
    max_chat_history_images: int
    image: str | None = None

    model_config = ConfigDict(extra="forbid")


class UiAgentResponse(BaseModel):
    step: AgentResponseStep
    token_usage: TokenUsage | None = None
    previous_steps_parameters: PreviousStepsParameters | None = None

    model_config = ConfigDict(extra="forbid")


class UiAgentRequest(BaseModel):
    userTask: str
    model_name: str | None
    env_name: Literal["windows", "mac", "linux", "browser"]
    image_width: int
    image_height: int
    image: str
    rawDOM: List[dict] | None
    title: str | None
    url: str | None
    previousSteps: List[AgentResponseStep] | None
    input_variables: dict = Field(default_factory=dict)
    output_variables: dict = Field(default_factory=dict)
    metadata: dict | None

    model_config = ConfigDict(extra="forbid")
