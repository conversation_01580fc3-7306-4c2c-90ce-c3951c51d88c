{"SiteTitle": "Rpa Challenge", "SiteURL": "https://www.rpachallenge.com/", "Action": "click", "ElementId": 28, "RawDOM": {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "RPA Challenge", "Href": null, "ElementType": "Text", "AbsoluteRegion": "0, 103, 249, 64", "ClientRegion": "0, 0, 249, 64", "Id": 2, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Input Forms", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1206, 103, 119, 64", "ClientRegion": "1206, 0, 119, 64", "Id": 9, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Shortest Path", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1324, 103, 130, 64", "ClientRegion": "1324, 0, 130, 64", "Id": 10, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Movie Search", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1454, 103, 130, 64", "ClientRegion": "1454, 0, 130, 64", "Id": 11, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Invoice Extraction", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1584, 103, 161, 64", "ClientRegion": "1584, 0, 161, 64", "Id": 12, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "RPA Stock Market", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1745, 103, 158, 64", "ClientRegion": "1745, 0, 158, 64", "Id": 13, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1206, 103, 697, 64", "ClientRegion": "1206, 0, 697, 64", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 103, 1903, 64", "ClientRegion": "0, 0, 1903, 64", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Instructions", "Href": null, "ElementType": "Text", "AbsoluteRegion": "11, 166, 340, 56", "ClientRegion": "11, 63, 340, 56", "Id": 23, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "EN", "Href": null, "ElementType": "Text", "AbsoluteRegion": "351, 166, 113, 46", "ClientRegion": "351, 63, 113, 46", "Id": 24, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "11, 166, 453, 56", "ClientRegion": "11, 63, 453, 56", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "1. The goal of this challenge is to create a workflow that will input data from a spreadsheet into the form fields on the screen.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "11, 245, 453, 135", "ClientRegion": "11, 142, 453, 135", "Id": 17, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "2. Beware! The fields will change position on the screen after every submission throughout 10 rounds thus the workflow must correctly identify where each spreadsheet record must be typed every time.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "11, 380, 453, 164", "ClientRegion": "11, 277, 453, 164", "Id": 18, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "3. The actual countdown of the challenge will begin once you click the Start button until then you may submit the form as many times as you wish without receiving penalties.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "11, 544, 453, 164", "ClientRegion": "11, 441, 453, 164", "Id": 19, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Good luck!", "Href": null, "ElementType": "Text", "AbsoluteRegion": "11, 708, 453, 49", "ClientRegion": "11, 605, 453, 49", "Id": 20, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Download Excel", "Href": null, "ElementType": "Text", "AbsoluteRegion": "150, 790, 140, 21", "ClientRegion": "150, 687, 140, 21", "Id": 29, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "cloud_download", "Href": null, "ElementType": "Text", "AbsoluteRegion": "434, 783, 20, 36", "ClientRegion": "434, 680, 20, 36", "Id": 30, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "11, 783, 453, 36", "ClientRegion": "11, 680, 453, 36", "Id": 25, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "START", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "11, 828, 453, 54", "ClientRegion": "11, 725, 453, 54", "Id": 26, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "11, 756, 453, 71", "ClientRegion": "11, 653, 453, 71", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 166, 476, 880", "ClientRegion": "0, 63, 476, 880", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Phone Number", "Href": null, "ElementType": "Text", "AbsoluteRegion": "587, 219, 89, 17", "ClientRegion": "587, 116, 89, 17", "Id": 38, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "587, 239, 398, 46", "ClientRegion": "587, 136, 398, 46", "Id": 39, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "587, 216, 398, 89", "ClientRegion": "587, 113, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Role in Company", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1007, 219, 102, 17", "ClientRegion": "1007, 116, 102, 17", "Id": 40, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "1007, 239, 398, 46", "ClientRegion": "1007, 136, 398, 46", "Id": 41, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1007, 216, 398, 89", "ClientRegion": "1007, 113, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Last Name", "Href": null, "ElementType": "Text", "AbsoluteRegion": "587, 308, 63, 17", "ClientRegion": "587, 205, 63, 17", "Id": 42, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "587, 327, 398, 46", "ClientRegion": "587, 224, 398, 46", "Id": 43, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "587, 305, 398, 89", "ClientRegion": "587, 202, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Email", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1007, 308, 33, 17", "ClientRegion": "1007, 205, 33, 17", "Id": 44, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "1007, 327, 398, 46", "ClientRegion": "1007, 224, 398, 46", "Id": 45, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1007, 305, 398, 89", "ClientRegion": "1007, 202, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Company Name", "Href": null, "ElementType": "Text", "AbsoluteRegion": "587, 396, 100, 17", "ClientRegion": "587, 293, 100, 17", "Id": 46, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "587, 416, 398, 46", "ClientRegion": "587, 313, 398, 46", "Id": 47, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "587, 393, 398, 89", "ClientRegion": "587, 290, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "First Name", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1007, 396, 64, 17", "ClientRegion": "1007, 293, 64, 17", "Id": 48, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "1007, 416, 398, 46", "ClientRegion": "1007, 313, 398, 46", "Id": 49, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1007, 393, 398, 89", "ClientRegion": "1007, 290, 398, 89", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Address", "Href": null, "ElementType": "Text", "AbsoluteRegion": "587, 485, 49, 17", "ClientRegion": "587, 382, 49, 17", "Id": 50, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "587, 504, 398, 46", "ClientRegion": "587, 401, 398, 46", "Id": 51, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "587, 482, 398, 89", "ClientRegion": "587, 379, 398, 89", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "576, 216, 840, 354", "ClientRegion": "576, 113, 840, 354", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "576, 570, 116, 36", "ClientRegion": "576, 467, 116, 36", "Id": 28, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "576, 216, 840, 390", "ClientRegion": "576, 113, 840, 390", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 166, 1903, 880", "ClientRegion": "0, 63, 1903, 880", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": true, "Text": null, "Href": null, "ElementType": "None", "AbsoluteRegion": "0, 0, 0, 0", "ClientRegion": "0, 0, 0, 0", "Id": 0, "ImageBase64": null}, "ParsedDOM": "<Window><Container><Text Id=\"2\" Text=\"RPA Challenge\" /><Container><Button Id=\"9\" Text=\"Input Forms\" /><Button Id=\"10\" Text=\"Shortest Path\" /><Button Id=\"11\" Text=\"Movie Search\" /><Button Id=\"12\" Text=\"Invoice Extraction\" /><Button Id=\"13\" Text=\"RPA Stock Market\" /></Container></Container><Container><Container><Container><Text Id=\"23\" Text=\"Instructions\" /><Text Id=\"24\" Text=\"EN\" /></Container><Text Id=\"17\" Text=\"1. The goal of this challenge is to create a workflow that will input data from a spreadsheet into the form fields on the screen.\" /><Text Id=\"18\" Text=\"2. Beware! The fields will change position on the screen after every submission throughout 10 rounds thus the workflow must correctly identify where each spreadsheet record must be typed every time.\" /><Text Id=\"19\" Text=\"3. The actual countdown of the challenge will begin once you click the Start button until then you may submit the form as many times as you wish without receiving penalties.\" /><Text Id=\"20\" Text=\"Good luck!\" /><Container><Button Id=\"25\"><Text Id=\"29\" Text=\"Download Excel\" /><Text Id=\"30\" Text=\"cloud_download\" /></Button><Button Id=\"26\" Text=\"START\" /></Container></Container><Container><Container><Container><Text Id=\"38\" Text=\"Phone Number\" /><InputBox Id=\"39\" /></Container><Container><Text Id=\"40\" Text=\"Role in Company\" /><InputBox Id=\"41\" /></Container><Container><Text Id=\"42\" Text=\"Last Name\" /><InputBox Id=\"43\" /></Container><Container><Text Id=\"44\" Text=\"Email\" /><InputBox Id=\"45\" /></Container><Container><Text Id=\"46\" Text=\"Company Name\" /><InputBox Id=\"47\" /></Container><Container><Text Id=\"48\" Text=\"First Name\" /><InputBox Id=\"49\" /></Container><Container><Text Id=\"50\" Text=\"Address\" /><InputBox Id=\"51\" /></Container></Container><Button Id=\"28\" /></Container></Container></Window>", "TypeIntoText": null}