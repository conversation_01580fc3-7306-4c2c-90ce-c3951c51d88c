"""Compute actions, tokens per request statistics for UI agent from logs"""

import glob
import json
import os

import numpy as np
import tqdm
from vertexai.preview import tokenization

model_name = "gemini-1.5-flash-001"
tokenizer = tokenization.get_tokenizer_for_model(model_name)


def load_task(file_path) -> list:
    """
    Load the content of a task from a JSON file for the given task name.
    If the loaded content is a dict (a single request log), it is wrapped in a list and augmented.
    """
    with open(file_path, "r", encoding="utf-8") as f:
        task_data = json.load(f)
    return task_data


def compute_statistics(log_folder: str):
    file_paths = glob.glob(os.path.join(log_folder, "*.json"))
    step_counts = []
    input_tokens_counts = []
    output_tokens_counts = []
    for file_path in tqdm.tqdm(file_paths):
        with open(file_path, "r", encoding="utf-8") as f:
            task_data = json.load(f)
            step_counts.append(len(task_data))
            for step in task_data:
                input_message = step["input_message"]
                input_tokens = tokenizer.count_tokens(input_message).total_tokens
                input_tokens_counts.append(input_tokens)
                assistant_message = step["assistant_message"]
                output_tokens = tokenizer.count_tokens(assistant_message).total_tokens
                output_tokens_counts.append(output_tokens)
    print("Number of tasks:", len(file_paths))
    print("Average steps:", np.mean(step_counts))
    print("Max steps:", np.max(step_counts))
    print("Median steps:", np.median(step_counts))
    print("Percentile steps:", np.percentile(step_counts, 90))
    print("Average input tokens:", np.mean(input_tokens_counts))
    print("Max input tokens:", np.max(input_tokens_counts))
    print("Median input tokens:", np.median(input_tokens_counts))
    print("Percentile input tokens:", np.percentile(input_tokens_counts, 90))
    print("Average output tokens:", np.mean(output_tokens_counts))
    print("Max out tokens:", np.max(output_tokens_counts))
    print("Median output tokens:", np.median(output_tokens_counts))
    print("Percentile output tokens:", np.percentile(output_tokens_counts, 90))


def compute_operator_statistics(eval_folder: str):
    file_paths = glob.glob(os.path.join(eval_folder, "**", "**", "ui_task_trace.json"))
    print("total tracing files:", len(file_paths))
    step_counts = []
    for file_path in tqdm.tqdm(file_paths):
        with open(file_path, encoding="utf-8") as f:
            trace_data = json.load(f)
            step_counts.append(len(trace_data["Iterations"]))

    print("Number of tasks:", len(file_paths))
    print("Average steps:", np.mean(step_counts))
    print("Max steps:", np.max(step_counts))
    print("Median steps:", np.median(step_counts))
    print("Percentile steps:", np.percentile(step_counts, 90))


if __name__ == "__main__":
    # log_folder = r"C:\Users\<USER>\OneDrive - UiPath\wingman\uia\gemini_v3\logs"
    # compute_statistics(log_folder)

    eval_folder = r"C:\Users\<USER>\work\wingman\UIA\webvoyager_results\operator_1_eval"
    compute_operator_statistics(eval_folder)
