import argparse
import base64
import io
import json
import os
import pathlib

from PIL import Image, ImageDraw


def process_image_with_boxes(image_base64, actions):
    """
    Given a base64 encoded image and a list of actions, draws bounding boxes
    from the "DOMElement" -> "Area" on the image and returns a new base64 encoded image.
    """
    # Remove data URI prefix if present.
    if image_base64.startswith("data:"):
        image_data = image_base64.split(",", 1)[1]
    else:
        image_data = image_base64

    try:
        img_data = base64.b64decode(image_data)
        img_io = io.BytesIO(img_data)
        img = Image.open(img_io).convert("RGB")
    except Exception as e:
        print(f"Error processing image: {e}")
        return image_base64  # Fallback: return the original image data.

    draw = ImageDraw.Draw(img)
    # Loop through each action to see if it contains a DOMElement with an "Area"
    for action in actions:
        dom_element = action.get("DOMElement")
        if dom_element:
            area_str = dom_element.get("Area")
            if area_str:
                try:
                    # The area is expected to be in the format "X, Y, W, H"
                    parts = [int(p.strip()) for p in area_str.split(",")]
                    if len(parts) == 4:
                        x, y, w, h = parts
                        # Draw a red rectangle with a width of 2 pixels.
                        draw.rectangle([x, y, x + w, y + h], outline="red", width=2)
                except Exception as e:
                    print(f"Error drawing bounding box: {e}")
                    continue
        else:
            parameters = action.get("InputAction", {}).get("Action", {}).get("parameters", {})
            position = parameters.get("position", [])
            if len(position) == 2:
                x, y = position
                x, y = int(x), int(y)
                # Draw a dot at the specified position.
                draw.ellipse([x - 5, y - 5, x + 5, y + 5], fill="red")

    # Save the modified image to a BytesIO buffer.
    out_io = io.BytesIO()
    img.save(out_io, format="PNG")
    new_image_data = base64.b64encode(out_io.getvalue()).decode("utf-8")
    return "data:image/png;base64," + new_image_data, img.size


def convert_uiact_trace_to_html(trace_file):
    with open(trace_file, "r", encoding="utf-8") as file:
        data = json.load(file)

    html_parts = []
    html_parts.append("<!DOCTYPE html>")
    html_parts.append("<html lang='en'>")
    html_parts.append("<head>")
    html_parts.append("  <meta charset='UTF-8'>")
    html_parts.append("  <style>")
    html_parts.append("""
      body {
        font-family: sans-serif;
        margin: 20px;
      }
      .prompt {
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #f9f9f9;
      }
      .iteration {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
      .iteration h2 {
        margin-top: 0;
      }
      .image {
        max-width: 100%;
        height: auto;
        display: block;
        margin-top: 10px;
      }
      .actions {
        margin-top: 15px;
      }
      .actions h3 {
        margin-bottom: 5px;
      }
      .actions ul {
        margin: 0;
        padding-left: 20px;
      }
    """)
    html_parts.append("  </style>")
    html_parts.append("</head>")
    html_parts.append("<body>")
    html_parts.append("  <h1>Trace Viewer</h1>")

    # Add the prompt at the top if it exists in the JSON data.
    prompt = data.get("Prompt")
    if prompt:
        html_parts.append("  <div class='prompt'>")
        html_parts.append("    <h2>Prompt</h2>")
        html_parts.append(f"    <p>{prompt}</p>")
        html_parts.append("  </div>")

    iterations = data.get("Iterations", [])
    for idx, iteration in enumerate(iterations):
        html_parts.append("  <div class='iteration'>")
        html_parts.append(f"    <h2>Iteration {idx + 1}</h2>")
        html_parts.append(f"    <p>Url: <a href='{iteration.get('Url')}'>{iteration.get('Url')}</a></p>")
        html_parts.append(f"    <p>Title: {iteration.get('Title')}</h2>")

        # Add the image if available
        image_base64 = iteration.get("ImageBase64")
        # Add the actions list if available
        actions = iteration.get("Actions", [])
        if image_base64:
            # if image_base64.startswith("data:"):
            #     img_src = image_base64
            # else:
            #     img_src = "data:image/png;base64," + image_base64
            processed_img_src, image_size = process_image_with_boxes(image_base64, actions)
            html_parts.append(f"    <p>Image Size: {image_size[0]} x {image_size[1]}</p>")
            html_parts.append(f"    <img class='image' src='{processed_img_src}' alt='Iteration {idx + 1} image' />")

            # html_parts.append(f"    <img class='image' src='{img_src}' alt='Iteration {idx + 1} image' />")
        step_parameters = iteration.get("ServerParameters", {})
        if step_parameters:
            html_parts.append("    <div class='step-parameters'>")
            html_parts.append("      <h3>Step Parameters:</h3>")
            html_parts.append("      <ul>")
            for key, value in step_parameters.items():
                if isinstance(value, list):
                    value = ", ".join(map(str, value))
                html_parts.append(f"        <li><strong>{key}:</strong> {value}</li>")
            html_parts.append("      </ul>")
            html_parts.append("    </div>")
        if actions:
            html_parts.append("    <div class='actions'>")
            html_parts.append("      <h3>Actions:</h3>")
            html_parts.append("      <ul>")
            for action in actions:
                # Get the nested action description
                description = action.get("InputAction", {}).get("Action", {})
                dom_element = action.get("DOMElement", {})
                html_parts.append(f"        <li><p>{description}</p><p>{dom_element}</p></li>")
            html_parts.append("      </ul>")
            html_parts.append("    </div>")

        html_parts.append("  </div>")

    html_parts.append("</body>")
    html_parts.append("</html>")

    return "\n".join(html_parts)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", type=pathlib.Path)
    args = parser.parse_args()
    webs = [
        "Allrecipes",
        "Amazon",
        "Apple",
        "ArXiv",
        "BBC News",
        "Booking",
        "Cambridge Dictionary",
        "Coursera",
        "ESPN",
        "GitHub",
        "Google Flights",
        "Google Map",
        "Google Search",
        "Huggingface",
        "Wolfram Alpha",
    ]

    responses: dict[str, dict[str, int | None]] = {k: {} for k in webs}
    responses_flat_completed_only: list[int] = []
    outdir = args.results_dir / "htmls"
    os.makedirs(outdir, exist_ok=True)
    for web in webs:
        task_folders = (args.results_dir / web).glob("*")
        for task_folder in task_folders:
            eval_output_path = os.path.join(outdir, f"{web}_{os.path.basename(task_folder)}_eval.txt")
            print("\nConverting trace for", task_folder)
            run_folders = task_folder.glob("*")
            last_timestamp = max([int(run_folder.name) for run_folder in run_folders])
            lastest_run_folder = task_folder / str(last_timestamp)
            print("  run:", str(last_timestamp))
            trace_file = lastest_run_folder / "ui_task_trace.json"
            if not trace_file.exists():
                print("  No trace file found")
                continue
            html_content = convert_uiact_trace_to_html(trace_file)
            with open(outdir / f"{web}_{os.path.basename(task_folder)}.html", "w", encoding="utf-8") as html_file:
                html_file.write(html_content)
