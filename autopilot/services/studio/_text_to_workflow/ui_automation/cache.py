import json
import os
from dataclasses import asdict, dataclass
from datetime import timedelta

import redis

from services.studio._text_to_workflow.ui_automation.options import opt


@dataclass(slots=True, frozen=True)
class CacheableUIAgentStep(object):
    model_prediction: dict
    uia_state: dict


cache = None


def get_cache(opt):
    global cache
    if cache is None:
        cache_config = opt["caching"]
        connection_string = os.environ.get("REDIS_CONNECTION_STRING", cache_config["redis"]["connection-string"])
        host, password, ssl, _ = connection_string.split(",")
        host, port = host.split(":")
        password = password.split("password=")[-1]
        ssl = ssl.split("ssl=")[-1].lower() == "true"

        cache = redis.StrictRedis(
            host=host,
            socket_timeout=10,
            socket_connect_timeout=10,
            port=int(port),
            password=password,
            db=0,
            ssl=ssl,
        )
    return cache


def add_step_data_to_cache(session_id, step_index, data: CacheableUIAgentStep):
    """
    Adds a new entry under a session_id. Each entry expires in 60 minutes.
    """

    # Generate a unique key for the data under this session_id
    unique_key = f"{session_id}:{step_index}"

    cache = get_cache(opt)

    # Optional: serialize data to JSON string if it's not a string already
    cache.set(name=unique_key, value=json.dumps(asdict(data)), ex=timedelta(minutes=60))


def get_steps_data_from_cache(session_id) -> list[CacheableUIAgentStep]:
    """
    Retrieves all the steps under a session_id.
    """
    cache = get_cache(opt)

    pattern = f"{session_id}:*"
    keys = cache.keys(pattern)

    if not keys:
        return []
    steps_indices = [int(key.decode().split(":")[-1]) for key in keys]

    # mget returns a list of values in the order of keys
    values = cache.mget(keys)

    # Decode and parse JSON (assuming data was stored as JSON string)
    steps = [CacheableUIAgentStep(**json.loads(value)) for value in values if value is not None]
    sorted_steps = [x for _, x in sorted(zip(steps_indices, steps, strict=False), key=lambda x: x[0])]
    return sorted_steps


def add_review_observation_to_cache(session_id, step_index, reviewed_observation_info: dict):
    """
    Adds a new entry under a session_id. Each entry expires in 60 minutes.
    """

    # Generate a unique key for the data under this session_id
    unique_key = f"{session_id}_observation:{step_index}"

    cache = get_cache(opt)

    # Optional: serialize data to JSON string if it's not a string already
    reviewed_observation_str = json.dumps(reviewed_observation_info, ensure_ascii=False)
    cache.set(name=unique_key, value=reviewed_observation_str, ex=timedelta(minutes=60))


def get_review_observation_from_cache(session_id) -> list[tuple[dict, int]]:
    cache = get_cache(opt)

    pattern = f"{session_id}_observation:*"
    keys = cache.keys(pattern)

    steps_indices = [int(key.decode().split(":")[-1]) for key in keys]

    # mget returns a list of values in the order of keys
    values = cache.mget(keys)

    reviewed_observation_infos = [json.loads(value) for value in values]

    reviewed_observation_infos = [
        (observation, index) for index, observation in sorted(zip(steps_indices, reviewed_observation_infos, strict=False), key=lambda x: x[0])
    ]

    return reviewed_observation_infos


def add_image_to_cache(session_id: str, step_index: int, image: str):
    """
    Adds a new entry under a session_id. Each entry expires in 60 minutes.
    """

    # Generate a unique key for the data under this session_id
    unique_key = f"{session_id}_image:{step_index}"

    cache = get_cache(opt)

    # Optional: serialize data to JSON string if it's not a string already
    cache.set(name=unique_key, value=image, ex=timedelta(minutes=60))


def get_images_from_cache(session_id: str) -> list[tuple[str, int]]:
    cache = get_cache(opt)

    pattern = f"{session_id}_image:*"
    keys = cache.keys(pattern)

    steps_indices = [int(key.decode().split(":")[-1]) for key in keys]

    # mget returns a list of values in the order of keys
    values = cache.mget(keys)

    images = [value.decode() for value in values]

    images = [(image, index) for index, image in sorted(zip(steps_indices, images, strict=False), key=lambda x: x[0])]

    return images
