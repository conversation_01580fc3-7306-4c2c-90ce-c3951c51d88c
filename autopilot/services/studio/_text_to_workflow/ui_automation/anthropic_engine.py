from typing import List

import anthropic
import langchain.schema


class AnthropicEngine:
    """Abstraction for using the anthropic inference in the style of existing gpt inference"""

    def __init__(self, engine_config):
        self.engine_config = {
            "model": engine_config["engine"],
            "max_tokens_to_sample": engine_config["max_tokens"],
            "temperature": engine_config["temperature"],
            "top_p": engine_config["top_p"],
        }
        self.model = anthropic.Anthropic()
        # print(self.engine_config)

    def chat(self, messages: List[langchain.schema.BaseMessage], verbose=False):
        """Inference implementation in the style of GPT engine for anthropic claude"""
        original_prompt = "".join([m.content for m in messages])
        prompt = f"{anthropic.HUMAN_PROMPT} {original_prompt} {anthropic.AI_PROMPT}"
        if verbose:
            print("-" * 60)
            print(prompt)
            print("-" * 60)
        completion = self.model.completions.create(
            prompt=prompt,
            **self.engine_config,
        )
        result = completion.completion
        if verbose:
            print("-" * 60)
            print(result)
            print("-" * 60)
        return result
