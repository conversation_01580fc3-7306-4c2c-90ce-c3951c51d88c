{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install transformers==4.33.0\n", "# !pip install transformers --upgrade"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \"\"\"You are an UI automation assistant expert that performs certain actions on computer screens to complete tasks.\n", "Screens are represented as a DOM which contains elements like buttons <Button>, texts <Texts>, InputBox <InputBox> etc. An element is uniquely identified by his ID.\n", "Each action has a description and a method and an expected result. All fields are mandatory.\n", "\n", "The possible methods are:\n", "\n", "click(element_id:int) # Clicks on an element with the corresponding element id.\n", "type_into(element_id:int, value:str) # Enter the value into an input element with the corresponding element id\n", "get_text(element_id:int) # Extract the text from the element with the corresponding element id\n", "select(element_id:int, value:str) # Select the value from a list or picker from the corresponding element id\n", "finish() # Indicates that the user task is completed\n", "\n", "Group your actions into steps which are valid to the current screen only. Look at some steps examples:\n", "\n", "<Step description=\"Create an invoice in SAP\">\n", "  <Action description=\"Click on the SAP link\" action=\"click(14)\" expected_result=\"I will navigate to the SAP home page. There I hope I can find new info on how to create an invoice\"/>\n", "</Step>\n", "\n", "<Step description=\"Remove last recording\">\n", "  <Action description=\"Select the last recording item\" action=\"click(46)\" expected_result=\"Last recording item will be selected\"/>\n", "  <Action description=\"Press the remove button\" action=\"click(18)\" expected_result=\"The item will be removed\"/>\n", "</Step>\n", "\n", "<Step description=\"Book a flight from Bucharest to London from Jul 7 2023 to Jul 14 2023.\">\n", "  <Action description=\"Type 'Bucharest' in the source box\" action=\"type_into(31, 'Bucharest')\" expected_result=\"Source will be set\"/>\n", "  <Action description=\"Type 'London' in the destination\" action=\"type_into(32, 'London')\" expected_result=\"Destination will be set\"/>\n", "  <Action description=\"Set check-in period with Jul 7 2023\" action=\"type(46,'Jul 7 2023')\" expected_result=\"Check-in period will be set\"/>\n", "  <Action description=\"Set check-out period with Jul 14 2023\" action=\"type(47,'Jul 14 2023')\" expected_result=\"Check-out period will be set\"/>\n", "  <Action description=\"Click Submit\" action=\"click(50)\" expected_result=\"Finalize the task\"/>\n", "</Step>\n", "\n", "You will be asked to complete a task in many steps. After each step the screen will be updated.\n", "\n", "Your task: \"Open the Request Absence page and request PTO absence from 24 July to 28 July\"\n", "\n", "\n", "Current screen:\n", "Title: Home - Workday\n", "Url: https://wd5.myworkday.com/uipath/d/home.htmld\n", "DOM: <Window><Container><Container><Container><Icon Id=\"1623\" /><Text Text=\"Skip to main content\" Id=\"1611\" /></Container><Container><Icon Id=\"1624\" /><Text Text=\"Accessibility Overview\" Id=\"1612\" /></Container></Container><Container><Container><Text Text=\"Menu\" Id=\"1587\" /><Button Id=\"1588\"><Icon Id=\"1610\" /></Button></Container><Container><Text Text=\"Apps\" Id=\"1598\" /><Text Text=\"Shortcuts\" Id=\"1599\" /></Container><Container><Text Text=\"Your Saved Order\" Id=\"1639\" /><Button Id=\"1652\"><Icon Id=\"1677\" /></Button></Container><Container><Icon Id=\"1814\" /><Text Text=\"Onboarding\" Id=\"1710\" /><Icon Id=\"1815\" /><Text Text=\"Performance\" Id=\"1711\" /><Icon Id=\"1816\" /><Text Text=\"Personal Information\" Id=\"1712\" /><Icon Id=\"1817\" /><Text Text=\"Absence\" Id=\"1713\" /><Icon Id=\"1818\" /><Text Text=\"Employee Resource Center\" Id=\"1714\" /></Container><Container><Container><Icon Id=\"1678\" /><Text Text=\"Add Apps\" Id=\"1667\" /></Container><Container><Icon Id=\"1679\" /><Text Text=\"Edit\" Id=\"1668\" /></Container></Container></Container><Container><Button Id=\"1582\"><Container><Icon Id=\"1613\" /><Text Text=\"MENU\" Id=\"1602\" /></Container></Button><Button Id=\"1583\"><Icon Text=\"\" Id=\"1603\" /></Button><Container><Icon Id=\"1625\" /><DropDown Id=\"1604\" /></Container><Container><Container><Text Text=\"1\" Id=\"1593\" /><Icon Id=\"1605\" /></Container><Container><Text Text=\"1\" Id=\"1594\" /><Icon Id=\"1606\" /></Container><Button Id=\"1586\"><Icon Text=\"Trong Canh Nguyen\" Id=\"1614\" /></Button></Container></Container><Container><Container><Container><Text Text=\"Lets Focus on You\" Id=\"1693\" /><Container><Container><Text Text=\"Awaiting Your Action\" Id=\"1643\" /><Button Id=\"1659\"><Icon Id=\"1681\" /></Button></Container><Container><Button Id=\"1669\"><Icon Id=\"1719\" /><Container><Text Text=\"Change My Photo\" Id=\"1694\" /><Text Text=\"Inbox - 1 years ago\" Id=\"1695\" /><Text Text=\"DUE 02/25/2022\" Id=\"1696\" /></Container></Button><Container><Icon Id=\"1683\" /><Text Text=\"Go to All Inbox Items 1\" Id=\"1670\" /></Container></Container></Container><Container><Text Text=\"Timely Suggestions\" Id=\"1644\" /><Container><Icon Id=\"1722\" /><Container><Container><Text Text=\"Keep Your Home Contact Information Updated\" Id=\"1804\" /><Container><Text Text=\"We would like you to review your Contact Information and ensure its\" Id=\"1819\" /><Text Text=\"up to date\" Id=\"1820\" /></Container></Container><Text Text=\"Update Contact Info\" Id=\"1723\" /></Container><Icon Id=\"1724\" /><Container><Container><Text Text=\"Keep Your Emergency Contacts Updated\" Id=\"1806\" /><Container><Text Text=\"We would like you to review your Emergency Contact Information and\" Id=\"1821\" /><Text Text=\"ensure its up to date\" Id=\"1822\" /></Container></Container><Text Text=\"Update Contacts\" Id=\"1725\" /></Container></Container></Container><Container><Text Text=\"Recommended for You\" Id=\"1633\" /><Container><Container><Container><Text Text=\"Understand Your People Network\" Id=\"1880\" /><Text Text=\"Based on your role\" Id=\"1877\" /></Container><Text Text=\"View Org Chart\" Id=\"1823\" /></Container><Container><Container><Text Text=\"Showcase Your Best Self with Your Profile\" Id=\"1881\" /><Text Text=\"Based on your most popular actions\" Id=\"1879\" /></Container><Text Text=\"Manage Profile\" Id=\"1824\" /></Container></Container></Container></Container><Container><Text Text=\"Its Tuesday, June 13, 2023\" Id=\"1617\" /><Container><Text Text=\"Your Top Apps\" Id=\"1646\" /><Container><Button Id=\"1647\" Text=\"Onboarding\"><Icon Id=\"1701\" /></Button><Button Id=\"1648\" Text=\"Performance\"><Icon Id=\"1703\" /></Button><Button Id=\"1649\" Text=\"Personal Information\"><Icon Id=\"1705\" /></Button><Button Id=\"1650\" Text=\"Absence\"><Icon Id=\"1707\" /></Button></Container><Container><Icon Id=\"1686\" /><Text Text=\"View All Apps\" Id=\"1672\" /></Container></Container><Container><Text Text=\"Announcements\" Id=\"1673\" /><Container><Button Id=\"1663\"><Icon Id=\"1754\" /><Container><Container><Text Text=\"Report Requests Now in\" Id=\"1755\" /><Text Text=\"Workday\" Id=\"1756\" /></Container><Container><Text Text=\"You can now request a report\" Id=\"1757\" /><Text Text=\"directly in Workday.\" Id=\"1758\" /></Container></Container></Button><Button Id=\"1664\"><Container><Container><Text Text=\"Your Career Profile is Now\" Id=\"1759\" /><Text Text=\"Available\" Id=\"1760\" /></Container><Container><Text Text=\"We encourage you to take the lead\" Id=\"1761\" /><Text Text=\"when it comes to growing your c\" Id=\"1762\" /></Container></Container></Button><Button Id=\"1665\"><Container><Container><Text Text=\"Volunteer Time Off is now\" Id=\"1763\" /><Text Text=\"available\" Id=\"1764\" /></Container><Container><Text Text=\"We know that giving back to your\" Id=\"1765\" /><Text Text=\"communities is important to you.\" Id=\"1766\" /></Container></Container></Button></Container><Container><Icon Id=\"1666\" /><Text Text=\"View More Announcements\" Id=\"1651\" /></Container></Container></Container></Container><Icon Text=\"\" Id=\"1596\" /><Container><Text Text=\"Privacy\" Id=\"1620\" /><Text Text=\" 2023 Workday, Inc. All rights reserved.\" Id=\"1621\" /><Text Text=\"System Status Your system will be unavailable for a maximum of 3 hours during the next Weekly Service Update starting on Friday, June 16, 2023 at 1100 PM PDT GMT-7 until Saturday, June 17, 2023 at 200 AM PDT GMT-7.\" Id=\"1609\" /></Container></Container><Icon Id=\"149\" /><Icon Id=\"300\" /><Icon Id=\"583\" /><Icon Id=\"580\" /><Icon Id=\"577...</Window>\n", "\n", "Previous done steps:\n", "None\n", "\n", "Question: Given the previous done steps and the current screen, what is the best step to complete the task?\n", "Assistant response in xml format:\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import LlamaForCausalLM, LlamaTokenizer\n", "from transformers.generation import GenerationConfig\n", "\n", "BASE_MODEL = \"meta-llama/Llama-2-7b-hf\"\n", "\n", "model = LlamaForCausalLM.from_pretrained(\n", "    BASE_MODEL,\n", "    # load_in_8bit=True,\n", "    torch_dtype=torch.float16,\n", "    device_map=\"auto\",\n", ")\n", "\n", "tokenizer = LlamaTokenizer.from_pretrained(BASE_MODEL)\n", "\n", "\n", "generation_config = GenerationConfig(\n", "    temperature=0,\n", "    top_p=0.0,\n", ")\n", "\n", "encoded_prompt = tokenizer.encode(prompt)\n", "input_ids = encoded_prompt\n", "input_ids = torch.tensor(input_ids, device=\"cuda\").reshape(1, -1)\n", "\n", "generation_output = model.generate(\n", "    input_ids=input_ids,\n", "    generation_config=generation_config,\n", "    return_dict_in_generate=True,\n", "    output_scores=True,\n", "    max_new_tokens=100,\n", ")\n", "print(tokenizer.decode(generation_output.sequences[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.decode(generation_output.sequences[0])[-500:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}