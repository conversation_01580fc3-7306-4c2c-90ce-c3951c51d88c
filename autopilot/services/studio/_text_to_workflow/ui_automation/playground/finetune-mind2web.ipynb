{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dumping the dataset for finetuning\n", "Capturing the prompt and dumping it in order to have a clearly distinguished instruction-input-output relationship"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pp install accelerate --upgrade\n", "# !pip install appdirs --upgrade\n", "# !pip install bitsandbytes --upgrade\n", "# !pip install datasets --upgrade\n", "# !pip install fire --upgrade\n", "# !pip install peft --upgrade\n", "# !pip install transformers --upgrade\n", "# !pip install torch --upgrade\n", "# !pip install sentencepiece --upgrade\n", "# !pip install tensorboardX --upgrade\n", "# !pip install gradio --upgrade\n", "# !pip install seaborn --upgrade"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import os\n", "import random\n", "import re\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"6\"\n", "\n", "import tiktoken\n", "import torch\n", "import transformers\n", "from datasets import load_dataset\n", "from peft import LoraConfig, PeftModel, get_peft_model, prepare_model_for_int8_training\n", "from transformers import GenerationConfig, LlamaForCausalLM, LlamaTokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DEVICE = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "DEVICE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # needed for huggingface model downloading\n", "# !huggingface-cli login --token"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading the base model and the tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# local models\n", "BASE_MODEL = \"/workspace/proddata/llama/llama-2-7b-hf\"\n", "# BASE_MODEL = \"/workdir/llama/llama/llama-2-13b-hf\"\n", "\n", "# huggingface on-the-fly downloading - requires credentials (use hugginface-cli)\n", "# BASE_MODEL = \"meta-llama/Llama-2-7b-hf\"\n", "# BASE_MODEL = \"meta-llama/Llama-2-7b-chat-hf\"\n", "\n", "LOAD_IN_8BIT = True\n", "# LOAD_IN_8BIT = False\n", " \n", "model = LlamaForCausalLM.from_pretrained(\n", "    BASE_MODEL,\n", "    load_in_8bit=LOAD_IN_8BIT,\n", "    torch_dtype=torch.float16,\n", "    device_map=\"auto\",\n", ")\n", "\n", "tokenizer = LlamaTokenizer.from_pretrained(BASE_MODEL)\n", "# padding will be done by the collator, most likely\n", "tokenizer.pad_token_id = 0  # unk. we want this to be different from the eos token\n", "tokenizer.padding_side = \"left\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = load_dataset(\"json\", data_files=\n", "                    { \"train\": \"/workspace/proddata/canh/mind2web_train.json\",\n", "                     \"test\": \"/workspace/proddata/canh/mind2web_test.json\"} )\n", "data[\"train\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_template = \"\"\"You are an assistant who suggest actions to perform on a computer screen for a given task.\n", "The screen is represented as an XML DOM. Each element in the screen has a unique identifer (id).\n", "Possible actions are TYPE, CLICK, SELECT. Each action must correspond to a unique screen element, for example:\n", "- TYPE(300, '<PERSON>')\n", "- CLICK(123)\n", "- SELECT(6060, 'New York')\n", "\n", "where the first argument is the element id of the action's target.\n", "\n", "TASK: {task}\n", "\n", "SCREEN DOM:\n", "{dom}\n", "\n", "PREVIOUS ACTIONS:\n", "{previous_actions}\n", "\n", "Given the above task, screen dom and previous actions, what are next actions to do?\n", "\"\"\"\n", "\n", "openai_encoding = tiktoken.encoding_for_model(\"gpt-3.5-turbo\")\n", "def truncate_dom_str(dom_str, max_dom_tokens=2500):\n", "    \"\"\"Truncate DOM string if needed\"\"\"\n", "    dom_enc = openai_encoding.encode(dom_str)\n", "    num_tokens = len(dom_enc)\n", "    if num_tokens > max_dom_tokens:\n", "        # remove the tail which often corresponding to the footer\n", "        dom_str = openai_encoding.decode(dom_enc[: max_dom_tokens - 10])\n", "        dom_str = dom_str + \"...</html>\"\n", "    return dom_str\n", "\n", "def format_action(action):\n", "    if action[\"value\"] is not None and len(action[\"value\"]) > 0:\n", "        return f\"{action['method']}({action['id']}, '{action['value']}')\"\n", "    else:\n", "        return f\"{action['method']}({action['id']})\"\n", "\n", "\n", "def generate_prompt(step):\n", "    task = step[\"task\"]\n", "    dom = truncate_dom_str(step[\"html\"])\n", "    previous_actions = step[\"previous_actions\"]\n", "    previous_actions_str = \"none\"\n", "    if len(previous_actions) > 0:\n", "        previous_actions_str = \" - \" + \" - \".join([format_action(action) + \"\\n\" for action in sum(previous_actions, [])])\n", "    prompt = prompt_template.format(task=task, dom=dom, previous_actions=previous_actions_str)\n", "    output = \" - \" + \" - \".join([format_action(action) + \"\\n\" for action in step[\"actions\"]])\n", "    return prompt, output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CUTOFF_LEN = 4096\n", "\n", "def tokenize(prompt, add_eos_token=True, return_tensors=None):\n", "    result = tokenizer(\n", "        prompt,\n", "        truncation=True,\n", "        max_length=CUTOFF_LEN,\n", "        padding=False,\n", "        return_tensors=return_tensors,\n", "    )\n", "    if (\n", "        result[\"input_ids\"][-1] != tokenizer.eos_token_id\n", "        and len(result[\"input_ids\"]) < CUTOFF_LEN\n", "        and add_eos_token\n", "    ):\n", "        result[\"input_ids\"].append(tokenizer.eos_token_id)\n", "        result[\"attention_mask\"].append(1)\n", " \n", "    result[\"labels\"] = result[\"input_ids\"].copy()\n", "\n", "    return result\n", "\n", "def generate_and_tokenize_prompt(data_point):\n", "    prompt, output = generate_prompt(data_point)\n", "    encoded_prompt = tokenize(prompt)\n", "    n_tokens = len(encoded_prompt['input_ids'])\n", "\n", "    full_prompt = f\"\"\"{prompt}{output}\"\"\"\n", "    tokenized_full_prompt = tokenize(full_prompt)\n", "    assert n_tokens <= len(tokenized_full_prompt['labels']), f\"{n_tokens}, {len(tokenized_full_prompt['labels'])}\"\n", "    tokenized_full_prompt['labels'][:n_tokens - 2] = [-100] * (n_tokens - 2)  # -100 is used for inhibiting the loss function on those tokens (the prompt)\n", "\n", "    tokenized_full_prompt['full_prompt'] = full_prompt\n", "    return tokenized_full_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = data[\"train\"].map(generate_and_tokenize_prompt)\n", "val_data = data[\"test\"].map(generate_and_tokenize_prompt)\n", "print(len(train_data))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example = train_data[0]\n", "example.keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Augmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "rng = random.<PERSON>(42)\n", "\n", "\n", "def bulk_replace(string, pattern, mapping):\n", "    _mapping = {pattern.format(k): pattern.format(v) for k, v in mapping.items()}\n", "    _pattern = re.compile('|'.join(map(re.escape, _mapping.keys())))\n", "    return _pattern.sub(lambda x: _mapping[x.group()], string)\n", "\n", "\n", "def augment_datapoint(datapoint):\n", "    ids = re.findall(r'Id=\\\"(\\d+)\\\"', datapoint['input'])\n", "    new_ids = rng.sample(range(max(map(int, ids))), len(ids))\n", "    ids_mapping = dict(zip(ids, map(str, new_ids), strict=False))\n", "\n", "    datapoint['input'] = bulk_replace(datapoint['input'], 'Id=\\\"{}\"', ids_mapping)\n", "    datapoint['output'] = bulk_replace(datapoint['output'], '({}', ids_mapping)\n", "\n", "    datapoint.update(generate_and_tokenize_prompt(datapoint))\n", "    datapoint.update(generate_and_tokenize_prompt(datapoint))\n", "    return datapoint\n", "\n", "\n", "def augment_func(datapoints_batch_dict):\n", "    # print(datapoint)\n", "    # return deepcopy(datapoint)\n", "    # return datapoint\n", "\n", "    keys = list(datapoints_batch_dict.keys())\n", "    batch_size = len(datapoints_batch_dict[keys[0]])\n", "\n", "    datapoints_batch = [{} for __ in range(batch_size)]\n", "    for k, values in datapoints_batch_dict.items():\n", "        for i, v in enumerate(values):\n", "            datapoints_batch[i][k] = v\n", "    # datapoints_batch = datapoints_batch_dict['input_ids']\n", "    # keys = list(datapoints_batch[0].keys())\n", "\n", "    # raise\n", "    processed_datapoints = []\n", "    for datapoint in datapoints_batch:\n", "        processed_datapoints.append(augment_datapoint(copy.deepcopy(datapoint)))\n", "\n", "    keys_out = ['input_ids', 'labels', 'attention_mask']\n", "    datapoints_batch_dict_out = {k: [] for k in keys_out}\n", "    for datapoint in processed_datapoints:\n", "        # for k, v in datapoint.items():\n", "        for k in keys_out:\n", "            v = datapoint[k]\n", "            datapoints_batch_dict_out[k].append(v)\n", "    return datapoints_batch_dict_out\n", "\n", "\n", "\n", "def keep_only_transform(batch):\n", "    keys_out = ['input_ids', 'labels', 'attention_mask']\n", "    for k in list(batch.keys()):\n", "        if k not in keys_out:\n", "            del batch[k]\n", "    return batch\n", "\n", "train_data.set_transform(keep_only_transform)\n", "display(train_data[0]['input_ids'] != train_data[0]['input_ids'])\n", "val_data.set_transform(keep_only_transform)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adaptor preparation (LORA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LORA_R = 8\n", "LORA_ALPHA = 16\n", "LORA_DROPOUT= 0.05\n", "LORA_TARGET_MODULES = [\n", "    \"q_proj\",\n", "    \"v_proj\",\n", "]\n", "\n", "BATCH_SIZE = 1\n", "MICRO_BATCH_SIZE = 1\n", "GRADIENT_ACCUMULATION_STEPS = BATCH_SIZE // MICRO_BATCH_SIZE\n", "LEARNING_RATE = 3e-4\n", "TRAIN_STEPS = 5000\n", "OUTPUT_DIR = \"/workspace/proddata/experiment/finetune_7b_4\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = prepare_model_for_int8_training(model)\n", "config = LoraConfig(\n", "    r=LORA_R,\n", "    lora_alpha=LORA_ALPHA,\n", "    target_modules=LORA_TARGET_MODULES,\n", "    lora_dropout=LORA_DROPOUT,\n", "    bias=\"none\",\n", "    task_type=\"CAUSAL_LM\",\n", ")\n", "model = get_peft_model(model, config)\n", "model.print_trainable_parameters()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Finetuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_arguments = transformers.TrainingArguments(\n", "    per_device_train_batch_size=MICRO_BATCH_SIZE,\n", "    gradient_accumulation_steps=GRADIENT_ACCUMULATION_STEPS,\n", "    warmup_steps=100,\n", "    max_steps=TRAIN_STEPS,\n", "    learning_rate=LEARNING_RATE,\n", "    fp16=True,\n", "    logging_steps=10,\n", "    optim=\"adamw_torch\",\n", "    # do_eval=False,  # does not seem to work?\n", "    evaluation_strategy=\"steps\",\n", "    eval_steps=200,\n", "    save_strategy=\"steps\",\n", "    save_steps=200,\n", "    output_dir=OUTPUT_DIR,\n", "    save_total_limit=3,\n", "    load_best_model_at_end=True,\n", "    report_to=\"tensorboard\",\n", "    #\n", "    remove_unused_columns=False,\n", "    eval_accumulation_steps=1,\n", "    per_device_eval_batch_size=MICRO_BATCH_SIZE,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_collator = transformers.DataCollatorForSeq2Seq(\n", "    tokenizer, pad_to_multiple_of=8, return_tensors=\"pt\", padding=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer = transformers.Trainer(\n", "    model=model,\n", "    train_dataset=train_data,\n", "    # train_dataset=proto_train_data,\n", "    # train_dataset=aug_train_data,\n", "    eval_dataset=val_data,\n", "    args=training_arguments,\n", "    data_collator=data_collator\n", ")\n", "model.config.use_cache = False\n", "\n", "# old_state_dict = model.state_dict\n", "# model.state_dict = (\n", "#     lambda self, *_, **__: get_peft_model_state_dict(\n", "#         self, old_state_dict()\n", "#     )\n", "# ).__get__(model, type(model))\n", "\n", "# comment this line if you want to debug the model (while .train() fails) so you do not have to reload it\n", "model = torch.compile(model)\n", "trainer.train()\n", "model.save_pretrained(OUTPUT_DIR)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "# model.save_pretrained(OUTPUT_DIR)\n", "# model.save_pretrained(os.path.join(OUTPUT_DIR, \"final\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading finetune model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "model = LlamaForCausalLM.from_pretrained(\n", "    BASE_MODEL,\n", "    load_in_8bit=False,\n", "    torch_dtype=torch.float16,\n", "    device_map=\"auto\",\n", ")\n", "\n", "\n", "model : PeftModel = PeftModel.from_pretrained(\n", "    model,\n", "    \"/workspace/proddata/experiment/finetune_7b_4\",\n", "    torch_dtype=torch.float16,\n", "    is_trainable=False\n", "    # overwrite=True\n", ")\n", "\n", "# model = torch.compile(model)\n", "model.merge_and_unload()  # only works when base model and adaptor have the same dtype"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.eval()\n", "model = torch.compile(model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# not used anymore\n", "# model.config.pad_token_id = tokenizer.pad_token_id = 0  # unk\n", "# model.config.bos_token_id = 1\n", "# model.config.eos_token_id = 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = data[\"test\"][1]\n", "prompt = generate_promt(x)\n", "encoded_prompt = tokenize(prompt, False)\n", "input_ids = encoded_prompt['input_ids']\n", "\n", "# print(tokenizer.decode(input_ids))\n", "# print(tokenizer.decode(train_data[0]['input_ids'][:10]))\n", "# raise\n", "input_ids = torch.tensor(input_ids, device=DEVICE).reshape(1, -1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers.generation import GenerationConfig\n", "\n", "generation_config = GenerationConfig(\n", "    temperature=0,\n", "    top_p=0.0,\n", "    # top_k=,\n", "    # num_beams=num_beams,\n", "    # **kwargs,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generation_output = model.generate(\n", "    input_ids=input_ids,\n", "    generation_config=generation_config,\n", "    return_dict_in_generate=True,\n", "    output_scores=True,\n", "    max_new_tokens=500,\n", ")\n", "seq = tokenizer.decode(generation_output.sequences[0])\n", "print(seq.split(\"Assistant response in xml format:\")[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> llama2 base predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# base_model_predictions = []\n", "# for i, x in enumerate(train_data):\n", "for i, x in enumerate(val_data, len(train_data) + 1):\n", "    prompt = generate_promp(x)\n", "    generation_output = model.generate(\n", "        input_ids=torch.tensor(tokenize(prompt, False)['input_ids'], device=DEVICE).reshape(1, -1),\n", "        generation_config=generation_config,\n", "        return_dict_in_generate=True,\n", "        output_scores=True,\n", "        max_new_tokens=1000,\n", "    )\n", "    seq = tokenizer.decode(generation_output.sequences[0])\n", "    resp = seq.split(\"Assistant response in xml format:\")[-1]\n", "    base_model_predictions.append(resp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# with open(\"/workspaces/ML/studio/text_to_workflow/ui_automation/playground/results/llama2_7b_predictions.json\", \"w\") as fd:\n", "#     json.dump(base_model_predictions, fd, indent=2)\n", "with open(\"/workspaces/ML/studio/text_to_workflow/ui_automation/playground/results/llama2_7b_predictions.json\", \"r\") as fd:\n", "    base_model_predictions = json.load(fd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive view of base and finetuned predictions, alongside the ground truth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i, x in enumerate(train_data):\n", "for i, x in enumerate(val_data, len(train_data)):\n", "    prompt = x['instruction'] + x['input']\n", "    generation_output = model.generate(\n", "        input_ids=torch.tensor(tokenize(prompt, False)['input_ids'], device=DEVICE).reshape(1, -1),\n", "        generation_config=generation_config,\n", "        return_dict_in_generate=True,\n", "        output_scores=True,\n", "        max_new_tokens=1000,\n", "    )\n", "    print(\"-\" * 30 + f\" {i} \" + \"-\" * 30)\n", "    print(x[\"identifier\"])\n", "    print(\"-\" * 30 + \" Prediction base \" + \"-\" * 30)\n", "    print(base_model_predictions[i].strip())\n", "    seq = tokenizer.decode(generation_output.sequences[0])\n", "    resp = seq.split(\"Assistant response in xml format:\")[-1]\n", "    print(\"-\" * 30 + \" Prediction finetuned \" + \"-\" * 30)\n", "    print(resp.strip())\n", "    print(\"-\" * 30 + \" Ground truth \" + \"-\" * 30)\n", "    print(x['output'])\n", "    print(\"-\" * 70)\n", "    print(\"\\n\" * 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display([x['identifier'] for x in train_data])\n", "display([x['identifier'] for x in val_data])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.decode(generation_output.sequences[0])[-300:])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}