from typing import Literal


class ApplicationType:  # noqa: N801
    Web = "web"
    Desktop = "desktop"


class ActionType:
    Click = "click"
    TypeInto = "type_into"
    GetText = "get_text"
    ExtractInfo = "extract_info_from_screen"
    SendKeys = "send_keys"
    Select = "select"
    Wait = "wait_load_completed"
    Finish = "finish"
    NavigateBack = "browser_navigate_back"
    Scroll = "scroll"


class FinishStatus:
    Success = "success"
    Partial = "partial"
    Failure = "failure"
    UserInput = "user_input"


actions_client_server_mapping = {"navigate_browser": ActionType.NavigateBack}

# AUTOPILOT ACTION DEFINITIONS
autopilot_action_definitions = [
    # "click(element_id:int)"
    {
        "name": ActionType.Click,
        "description": "Click on an element with the corresponding element id.",
        "parameters": [
            ("element_id", int),
        ],
    },
    # "type_into(element_id:int, variable:str, default_value:str)"
    {
        "name": ActionType.TypeInto,
        "description": "Type a variable or a default_value into an input element with the corresponding element id.",
        "parameters": [
            ("element_id", int),
            ("variable", str),
            ("default_value", str),
        ],
    },
    # "get_text(element_id:int, variable:str)"
    {
        "name": ActionType.GetText,
        "description": "Extract the text from the element with the corresponding element id and assign to a variable.",
        "parameters": [
            ("element_id", int),
            ("variable", str),
        ],
    },
    # "select(element_id:int, variable:str, default_value:str)"
    {
        "name": ActionType.Select,
        "description": "Select the value of a variable or a default_value from a list or date picker from the corresponding element id",
        "parameters": [
            ("element_id", int),
            ("variable", str),
            ("default_value", str),
        ],
    },
    # finish()
    {"name": ActionType.Finish, "description": "Indicate that the task is completed successfully.", "parameters": []},
]


# AGENT ACTION DEFINITIONS
agent_action_definitions = [
    {
        "name": ActionType.Click,
        "description": "Click on an element with the corresponding element id.",
        "parameters": [
            ("element_id", int),
        ],
    },
    {
        "name": ActionType.TypeInto,
        "description": "Type a value or an input variable into an input element with the corresponding element id.",
        "parameters": [
            ("element_id", int),
            ("variable", str),
            ("value", str),
        ],
    },
    {
        "name": ActionType.Select,
        "description": "Select a value or an input variable from an element with 'Select' tag. \
If the list of options is present in the DOM and the value must be one of the options, otherwise guess a closest possible value.",
        "parameters": [
            ("element_id", int),
            ("variable", str),
            ("value", str),
        ],
    },
    {
        "name": ActionType.SendKeys,
        "description": "Send special keys like ['Escape'], ['Backspace'], ['Insert'], ['PageUp'], ['PageDown'], ['Delete'], ['Enter'], Shortcuts such as ['Control'], ['Control','Shift'], ['Control','A'] are supported as well.",
        "parameters": [
            ("element_id", int),
            ("keys", list[str]),
        ],
    },
    {
        "name": ActionType.NavigateBack,
        "description": "Use this action to navigate back to the previous url, only in case you are in a browser(there is an url associated with the DOM). This action is useful when the best action to complete the task is to go back and there is no other way to do that using clicks.",
        "parameters": [],
    },
    {
        "name": ActionType.Wait,
        "description": "Wait for the screen ui (screen image) to completely load. If the dom is not in sync with the image you should call this action. Use this action in case the UI is not completely loaded (spinner, loader, blank area in the image etc...).",
        "parameters": [],
    },
    {
        "name": ActionType.ExtractInfo,
        "description": "Extract information from screen using a LLM. \
You need to create a short prompt which will be used to extract the information. The prompt should be precise but generic and not rely on specific information in the current screen. \
Also associate an output_label for the extracted data to be used in the task completion process. \
If the information exists in the screen DOM but not visible yet in the screenshot, do scroll first.",
        "parameters": [
            ("prompt", str),
            ("label", str),
        ],
    },
    {
        "name": ActionType.Scroll,
        "description": "Scroll within a particular element or the whole page. \
Do scroll when an element is not fully visible in the current screenshot and you need to see more of his content in order to fulfil the task. \
For example for a table with multiple rows or a container with multiple elements, you can use this action to scroll to see more rows / elements. \
It is important to specify the scroll direction (up, down, left, right). \
Number of scrolls must be chosen from 3, 6 with default value 6. Use 3 when you see the need to scroll less.",
        "parameters": [("element_id", int), ("direction", Literal["up, down, left, right"]), ("number_of_scrolls", int)],
    },
    {
        "name": ActionType.Finish,
        "description": "Indicate that the task is completed successfully, stopped with failure or require additional user input to procceed.",
        "parameters": [
            ("status", Literal["success", "failure", "user_input"]),
        ],
    },
]
actions_client_server_mapping = {"navigate_browser": "browser_navigate_back", "keypress": "send_keys"}
