import hashlib
import json
import re
import xml.etree.ElementTree as ET
from collections import Counter
from copy import deepcopy

import tqdm
from datasets import load_dataset


def minify_html(html):
    result = html.replace("backend_node_id", "id")
    result = result.replace("\n", "")
    result = result.replace("\t", "")
    result = re.sub(" +", " ", result)
    result = result.replace("> <", "><")
    return result


def hash_html(html):
    dom = ET.fromstring(html)
    get_dom_structure(dom)
    s = ET.tostring(dom, encoding="utf-8").decode("utf-8")
    result = hashlib.md5(s.encode())
    return result.hexdigest()


def get_dom_structure(dom):
    dom.attrib.clear()
    if dom.tag == "text":
        dom.clear()
    for child in dom:
        get_dom_structure(child)


def remove_id(dom):
    if "id" in dom.attrib:
        del dom.attrib["id"]
    for child in dom:
        remove_id(child)


def get_steps(datapoint, idx):
    actions = datapoint["actions"]
    action_reprs = datapoint["action_reprs"]
    steps = []
    for i, action in enumerate(actions):
        step = {}
        op = action["operation"]
        candidates = action["pos_candidates"]
        if len(candidates) > 0:
            if len(candidates) > 1:
                selected_candidates = [c for c in candidates if c["is_top_level_target"] is True]
                if len(selected_candidates) == 0:
                    selected_candidates = [c for c in candidates if c["is_original_target"] is True]
                if len(selected_candidates) == 0:
                    candidate = candidates[0]
                else:
                    candidate = selected_candidates[0]
                if len(selected_candidates) > 1:
                    print(
                        "more than top_level_target for task:",
                        idx,
                        "action",
                        i,
                        candidates,
                        op,
                        action_reprs[i],
                    )
            else:
                candidate = candidates[0]
            del candidate["attributes"]
            candidate["id"] = candidate["backend_node_id"]
            del candidate["backend_node_id"]
            step["actions"] = [
                {
                    "id": candidate["id"],
                    "method": op["op"],
                    "value": op.get("value", None),
                    "tag": candidate["tag"],
                }
            ]

            minified_html = minify_html(action["cleaned_html"])
            step["cleaned_html"] = minified_html
            step["dom_hash"] = hash_html(minified_html)
            steps.append(step)
    return steps


def get_path_to_elem(dom, target_id, current_path):
    if dom.get("id") == target_id:
        return current_path
    for idx, child in enumerate(dom, 0):
        found_path = get_path_to_elem(child, target_id, current_path + [idx])
        if found_path:
            return found_path
    return None


def get_elem_from_path(dom, current_path):
    if len(current_path) > 0:
        idx = current_path.pop(0)
        child = dom[idx]
        return get_elem_from_path(child, current_path)
    else:
        return dom


def merge_2_steps(step1, step2):
    assert step1["dom_hash"] == step2["dom_hash"]
    assert len(step2["actions"]) == 1
    step2_action = step2["actions"][0]
    dom2 = ET.fromstring(step2["cleaned_html"])
    elem2_path = get_path_to_elem(dom2, str(step2_action["id"]), [])

    dom1 = ET.fromstring(step1["cleaned_html"])
    elem1 = get_elem_from_path(dom1, elem2_path)
    copy_action = deepcopy(step2_action)
    copy_action["id"] = elem1.get("id")
    step1["actions"].append(copy_action)


def merge_steps(steps):
    output_steps = []
    n = len(steps)
    current_step_idx = 0
    current_step = steps[current_step_idx]
    output_steps.append(current_step)
    while current_step_idx < n - 1:
        next_step = steps[current_step_idx + 1]
        if next_step["dom_hash"] == current_step["dom_hash"]:
            merge_2_steps(current_step, next_step)
        else:
            output_steps.append(next_step)
            current_step = next_step
        current_step_idx += 1
    return output_steps


def build_dataset(dataset):
    n = len(dataset)
    data = []
    for i in tqdm.tqdm(range(n)):
        datapoint = {"task": dataset[i]["confirmed_task"]}
        steps = merge_steps(get_steps(dataset[i], i))
        datapoint["steps"] = steps
        data.append(datapoint)
    return data


test_indices = [
    660,
    355,
    503,
    829,
    399,
    544,
    611,
    409,
    176,
    418,
    878,
    902,
    147,
    205,
    859,
    488,
    374,
    708,
    630,
    247,
]


def build_steps_dataset(data, is_test=False):
    step_dataset = []
    for i in range(len(data)):
        if (is_test and i in test_indices) or (not is_test and i not in test_indices):
            task_data = data[i]
            previous_actions = []
            for step_index, step in enumerate(task_data["steps"]):
                step_data = {"task": task_data["task"]}
                step_data["step_index"] = step_index
                step_data["html"] = step["cleaned_html"]
                step_data["previous_actions"] = deepcopy(previous_actions)
                step_data["actions"] = step["actions"]
                previous_actions.append(step["actions"])
                step_dataset.append(step_data)
    return step_dataset


if __name__ == "__main__":
    dataset = load_dataset("osunlp/Mind2Web")
    data = build_dataset(dataset["train"])

    with open("mind2web_data.json", "w", encoding="utf-8") as f:
        json.dump(data, f)

    total_steps = 0
    count_actions = Counter()
    for d in data:
        print(d["task"])
        total_steps += len(d["steps"])

        for step in d["steps"]:
            print(step["actions"])
            count_actions[str(len(step["actions"]))] += 1
    print("total steps", total_steps)
    print("count_actions", count_actions)

    # flatten dataset as list of steps
    train_dataset = build_steps_dataset(data)
    test_dataset = build_steps_dataset(data, is_test=True)
    print("train_dataset:", len(train_dataset))
    print("test_dataset:", len(test_dataset))
    with open("mind2web_train.json", "w", encoding="utf-8") as f:
        json.dump(train_dataset, f)

    with open("./mind2web_test.json", "w", encoding="utf-8") as f:
        json.dump(test_dataset, f)
