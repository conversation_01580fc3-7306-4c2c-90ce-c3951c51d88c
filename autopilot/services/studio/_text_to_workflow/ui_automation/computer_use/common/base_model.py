from abc import ABC, abstractmethod
from typing import Dict

from services.studio._text_to_workflow.ui_automation.computer_use.common.model_state import ComputerUseState
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import TokenUsage


class ComputerUseBaseModel(ABC):
    @abstractmethod
    async def predict(self, request_state: ComputerUseState) -> Dict:
        pass

    @abstractmethod
    def _normalize_response_step(self, response) -> ComputerUseStep:
        pass

    @abstractmethod
    def _get_token_usage(self, response) -> TokenUsage:
        pass
