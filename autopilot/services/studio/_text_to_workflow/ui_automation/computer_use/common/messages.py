from dataclasses import dataclass
from typing import List, Literal, TypeAlias, Union


@dataclass
class OAIComputerUserTaskMessage:
    role: Literal["user"]
    task: str
    image: str

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {"type": "input_text", "text": self.task},
                {"type": "input_image", "image_url": {"url": f"data:image/png;base64,{self.image}"}},
            ],
        }


@dataclass
class OAIComputerUseTextMessage:
    role: Literal["user"]
    text: str | dict

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {"type": "input_text", "text": str(self.text)},
            ],
        }


@dataclass
class OAIComputerUseToolResultMessage:
    previous_call_id: str
    type: Literal["computer_call_output"]
    image: str

    def to_dict(self):
        return {
            "call_id": self.previous_call_id,
            "type": self.type,
            "output": {"type": "input_image", "image_url": f"data:image/png;base64,{self.image}"},
        }


@dataclass
class ClaudeComputerUseTaskMessage:
    role: Literal["user"]
    task: str
    image: str

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {"type": "text", "text": self.task},
                {"type": "image/png", "image_url": {"url": f"data:image/png;base64,{self.image}", "detail": "auto"}},
            ],
        }


@dataclass
class ClaudeComputerUseThinkingMessage:
    role: Literal["assistant"]
    text: str
    signature: str

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {
                    "type": "thinking",
                    "thinking": f"{self.text}",
                    "signature": f"{self.signature}",
                },
            ],
        }


@dataclass
class ClaudeComputerUseRedactedThinkingMessage:
    role: Literal["assistant"]
    data: str

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {
                    "type": "redacted_thinking",
                    "redacted_thinking": f"{self.data}",
                },
            ],
        }


@dataclass
class ClaudeComputerUseTextMessage:
    role: Literal["user"]
    text: str | dict

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {"type": "text", "text": str(self.text)},
            ],
        }


@dataclass
class ClaudeComputerUseActionMessage:
    id: str
    input: dict
    name: str

    def to_dict(self):
        return {"id": self.id, "name": "computer_20250124", "arguments": {"action": self.name, **self.input}}


@dataclass
class ClaudeComputerUseToolResultMessage:
    id: str | None
    name: str = "computer"
    content: str = "Tool executed successfully"
    role: Literal["tool"] = "tool"

    def to_dict(self):
        return {"role": self.role, "content": {"call_id": self.id, "name": self.name, "result": {"result": self.content}}}


@dataclass
class ClaudeComputerUseImageMessage:
    role: Literal["user"]
    image: str

    def to_dict(self):
        return {
            "role": self.role,
            "content": [
                {"type": "image/png", "image_url": {"url": f"data:image/png;base64,{self.image}", "detail": "auto"}},
            ],
        }


@dataclass
class ClaudeComputerUserToolCallMessage:
    role: Literal["user", "assistant"]
    tool_calls: List[ClaudeComputerUseActionMessage]

    def to_dict(self):
        return {
            "role": self.role,
            "tool_calls": [msg.to_dict() for msg in self.tool_calls],
        }


ClaudeComputerUseMessage: TypeAlias = Union[
    ClaudeComputerUseTaskMessage,
    ClaudeComputerUseTextMessage,
    ClaudeComputerUseImageMessage,
    ClaudeComputerUserToolCallMessage,
    ClaudeComputerUseToolResultMessage,
]
