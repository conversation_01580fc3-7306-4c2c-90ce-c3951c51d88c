from dataclasses import dataclass
from typing import Literal


@dataclass
class OAIComputerUseTool:
    type: Literal["computer_use_preview"]
    display_width: int
    display_height: int
    environment: str | Literal["mac", "windows", "ubuntu", "browser"]

    def to_dict(self):
        return {
            "type": self.type,
            "display_width": self.display_width,
            "display_height": self.display_height,
            "environment": self.environment,
        }


@dataclass
class ClaudeComputerUseTool20250124:
    display_width_px: int
    display_height_px: int
    display_number: int
    name: str = "computer"
    type: Literal["computer_20250124"] = "computer_20250124"

    def to_dict(self):
        return {
            "type": self.type,
            "display_width_px": self.display_width_px,
            "display_height_px": self.display_height_px,
            "display_number": self.display_number,
            "name": self.name,
        }
