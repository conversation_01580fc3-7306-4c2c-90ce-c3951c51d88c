from typing import Optional

from services.studio._text_to_workflow.ui_automation.action import ValidationException
from services.studio._text_to_workflow.ui_automation.computer_use.utils.keyboard_utils import KeyMapper
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import SupportedActions


class ComputerUseAction(object):
    def __init__(self, name: str, description: str, parameters: dict, action_id: str | None = None, result: Optional[dict | str] = None):
        self.id = action_id
        self.name = name
        self.parameters = parameters
        self.description = description
        self.result = result

    @staticmethod
    def from_dict(action_dict: dict):
        try:
            result = action_dict.get("result")
            # client backward compat: for parsing result from the extraction in case client return the full response
            # client should return the "data" key only in future client release
            if result is not None and isinstance(result, dict) and "token_usage" in result and "data" in result:
                result = result["data"]

            return ComputerUseAction(
                name=action_dict["name"],
                description=action_dict["description"],
                result=result,
                parameters=action_dict.get("parameters", {}),
            )
        except Exception:
            raise ValidationException(f"Invalid action, must contain name, description and parameters keys, {action_dict}")

    @staticmethod
    def _find_scroll_direction(offset_x: int, offset_y: int):
        if offset_x > 0:
            return "right"
        if offset_x < 0:
            return "left"
        if offset_y > 0:
            return "down"
        if offset_y < 0:
            return "up"
        return "unknown"

    @staticmethod
    def from_openai_action(action_dict: dict):
        parameters = {}
        match action_dict["type"]:
            case "click":
                parameters = {"position": [action_dict["x"], action_dict["y"]], "button": action_dict["button"]}
                action_dict["type"] = SupportedActions.Click
            case "double_click":
                parameters = {"position": [action_dict["x"], action_dict["y"]], "clickType": "double"}
                action_dict["type"] = SupportedActions.Click
            case "scroll":
                parameters = {
                    "position": [action_dict["x"], action_dict["y"]],
                    "direction": ComputerUseAction._find_scroll_direction(action_dict["scroll_x"], action_dict["scroll_y"]),
                    "offset": [action_dict["scroll_x"], action_dict["scroll_y"]],
                }
                action_dict["type"] = SupportedActions.Scroll
            case "type":
                parameters = {"value": action_dict["text"]}
                action_dict["type"] = SupportedActions.TypeInto
            case "drag":
                parameters = {"path": action_dict["path"]}
                action_dict["type"] = SupportedActions.Drag
            case "wait":
                parameters = {"duration": 3}
                action_dict["type"] = SupportedActions.Wait
            case "move":
                parameters = {"position": [action_dict["x"], action_dict["y"]]}
                action_dict["type"] = SupportedActions.MouseMove
            case "keypress":
                action_dict["keys"] = [KeyMapper.map_openai_key(key) for key in action_dict["keys"]]
                parameters = {"keys": action_dict["keys"]}
                action_dict["type"] = SupportedActions.KeyPress
            case "finish":
                parameters = {"result": action_dict["result"], "status": action_dict["status"]}
                action_dict["type"] = SupportedActions.Finish
            case "user_question":
                parameters = {"text": action_dict["text"]}
                action_dict["type"] = SupportedActions.UserQuestion
            case "screenshot":
                action_dict["type"] = SupportedActions.Wait
                parameters = {"duration": 3}
            case _:
                raise ValueError(f"Action type {action_dict['type']} is not supported")

        return ComputerUseAction(
            action_id=action_dict.get("id"),
            name=str(action_dict["type"]),
            description="",
            parameters=parameters,
            result=action_dict.get("result"),
        )

    @staticmethod
    def from_claude_action(action_dict: dict):
        parameters = {}
        action_dict["type"] = action_dict["action"]
        del action_dict["action"]

        match action_dict["type"]:
            case "right_click" | "middle_click" | "left_click":
                parameters = {"position": action_dict["coordinate"], "button": action_dict["type"].split("_")[0]}
                action_dict["type"] = SupportedActions.Click
            case "double_click" | "triple_click":
                parameters = {"position": action_dict["coordinate"], "clickType": action_dict["type"].split("_")[0]}
                action_dict["type"] = SupportedActions.Click
            case "scroll":
                parameters = {"position": action_dict["coordinate"], "direction": action_dict["scroll_direction"]}
                action_dict["type"] = SupportedActions.Scroll
            case "type":
                parameters = {"value": action_dict["text"]}
                action_dict["type"] = SupportedActions.TypeInto
            case "left_click_drag":  #
                action_dict["type"] = SupportedActions.Drag
                parameters = {
                    "path": [
                        {"x": action_dict["start_coordinate"][0], "y": action_dict["start_coordinate"][1]},
                        {"x": action_dict["coordinate"][0], "y": action_dict["coordinate"][1]},
                    ]
                }
            case "wait":
                parameters = {"duration": action_dict["duration"]}
                action_dict["type"] = SupportedActions.Wait
            case "mouse_move":
                parameters = {"position": action_dict["coordinate"]}
                action_dict["type"] = SupportedActions.MouseMove
            case "key":
                keypress = [KeyMapper.map_claude_key(key) for key in action_dict["text"].split("+")]
                parameters = {"keys": keypress}
                action_dict["type"] = SupportedActions.KeyPress
            case "finish":
                parameters = {"result": action_dict["result"], "status": action_dict["status"]}
                action_dict["type"] = SupportedActions.Finish
            case "user_question":
                parameters = {"text": action_dict["text"]}
                action_dict["type"] = SupportedActions.UserQuestion
            case "screenshot":
                action_dict["type"] = SupportedActions.Wait
                parameters = {"duration": 3}
            case _:
                raise ValueError(f"Action type {action_dict['type']} is not supported")

        return ComputerUseAction(
            action_id=action_dict.get("id"),
            name=str(action_dict["type"]),
            description="",
            parameters=parameters,
            result=action_dict.get("result"),
        )

    def to_claude_action(self) -> dict:
        action_dict = {
            "id": self.id,
            "name": self.name,
        }
        parameters = {}
        match self.name:
            case SupportedActions.Click:
                parameters["coordinate"] = list(self.parameters["position"])
                if "button" in self.parameters:
                    action_dict["name"] = f"{self.parameters['button']}_click"
                if "clickType" in self.parameters:
                    action_dict["name"] = "double_click"
            case SupportedActions.TypeInto:
                parameters["text"] = self.parameters["value"]
                action_dict["name"] = "type"
            case SupportedActions.Drag:
                parameters["start_coordinate"] = [self.parameters["path"][0]["x"], self.parameters["path"][0]["y"]]
                parameters["coordinate"] = [self.parameters["path"][1]["x"], self.parameters["path"][1]["y"]]
                action_dict["name"] = "left_click_drag"
            case SupportedActions.Wait:
                parameters["duration"] = self.parameters["duration"]
                action_dict["name"] = "wait"
            case SupportedActions.MouseMove:
                parameters["coordinate"] = list(self.parameters["position"])
            case SupportedActions.KeyPress:
                parameters["text"] = "+".join(self.parameters["keys"])
                action_dict["name"] = "key"
            case SupportedActions.Scroll:
                parameters["coordinate"] = list(self.parameters["position"])
                parameters["scroll_direction"] = self.parameters["direction"]
            case SupportedActions.Finish:
                parameters["result"] = self.parameters["result"]
            case SupportedActions.UserQuestion:
                parameters["text"] = self.parameters["text"]
            case "screenshot":
                pass
        action_dict["input"] = parameters
        return action_dict

    @staticmethod
    def from_request_dict(action_dict: dict):
        return ComputerUseAction(
            action_id=action_dict.get("id", None),
            name=action_dict["method_type"],
            description=action_dict["description"],
            parameters=action_dict.get("parameters", {}),
            result=action_dict.get("result"),
        )

    def to_response_dict(self):
        """for UIA service response"""

        action_dict = {
            "description": self.description,
            "method_type": self.name,
            "parameters": self.parameters,
            "id": self.id,
        }

        if self.result is not None:
            action_dict["result"] = self.result

        return action_dict
