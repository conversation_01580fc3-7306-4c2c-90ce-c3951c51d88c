from typing import List

from services.studio._text_to_workflow.ui_automation.action_definition import ActionType, FinishStatus
from services.studio._text_to_workflow.ui_automation.computer_use.common.computer_action import ComputerUseAction


class ComputerUseStep(object):
    def __init__(
        self,
        description: str,
        actions: List[ComputerUseAction],
        thought: str | None = None,
        screen_info: dict | None = None,
        image: str | None = None,
        additional_parameters: dict | None = None,
    ):
        self.description = description
        self.actions: List[ComputerUseAction] = actions
        self.thought = thought
        self.screen_info = screen_info
        self.additional_parameters = additional_parameters
        self.image = image

    @staticmethod
    def from_openai_computer_use(message: dict):
        actions = [ComputerUseAction.from_openai_action(message["action"])]
        return ComputerUseStep(
            description="OpenAI computer use step",
            thought=message.get("description"),
            actions=actions,
            additional_parameters={"previous_response_id": message["previous_response_id"]},
        )

    @staticmethod
    def from_claude_computer_use(actions: List[dict], description: str, thought: str | None = None, **kwargs):
        step_actions = [ComputerUseAction.from_claude_action(action) for action in actions]
        return ComputerUseStep(description=description, thought=thought, actions=step_actions, additional_parameters=kwargs)

    @staticmethod
    def from_dict(step_dict):
        return ComputerUseStep(
            description=step_dict["description"],
            thought=step_dict.get("thought"),
            actions=[ComputerUseAction.from_dict(action_dict) for action_dict in step_dict["actions"]],
        )

    @staticmethod
    def from_request_dict(step_dict):
        """Parse the step dictionary as in the request
        This has different keys for backward compatibility
        """
        step_description = step_dict["description"]
        additional_parameters = step_dict.get("additional_parameters", {})
        image = step_dict.get("image", None)
        thought = step_dict.get("thought", None)

        screen_info = {}
        if "title" in step_dict:
            screen_info["title"] = step_dict["title"]
        if "url" in step_dict:
            screen_info["url"] = step_dict["url"]

        steps_actions = [ComputerUseAction.from_request_dict(action) for action in step_dict.get("actions", [])]
        step = ComputerUseStep(step_description, steps_actions, thought, screen_info, image=image, additional_parameters=additional_parameters)
        return step

    def to_response_dict(self):
        response_step = {"description": self.description, "thought": self.thought, "additional_parameters": self.additional_parameters}
        response_actions = []

        for action in self.actions:
            action_dict = action.to_response_dict()
            response_actions.append(action_dict)
        if self.image is not None:
            response_step["image"] = self.image
        response_step["actions"] = response_actions

        return response_step

    @staticmethod
    def create_finish_failure_step(description: str):
        return ComputerUseStep(
            description=description,
            actions=[ComputerUseAction(name=ActionType.Finish, description=description, parameters={"status": FinishStatus.Failure})],
        )

    @staticmethod
    def create_text_output_step(description: str, text: str, status: str):
        return ComputerUseStep(
            description=description,
            actions=[ComputerUseAction(name=ActionType.Finish, description=description, parameters={"result": text, "status": status}, result=text)],
        )
