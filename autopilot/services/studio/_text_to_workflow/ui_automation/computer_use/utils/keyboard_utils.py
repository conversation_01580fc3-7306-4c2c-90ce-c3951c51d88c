class KeyMapper:
    special_keys = {
        "Backspace": "Back",
        "Backslash": "Backslash",
        "Tab": "Tab",
        "Enter": "Enter",
        "Shift": "Shift",
        "Control": "Ctrl",
        "Ctrl": "Ctrl",
        "Alt": "Alt",
        "Pause": "Break",
        "Capslock": "Caps",
        "Escape": "Esc",
        "Space": "Space",
        "Pageup": "PgUp",
        "Pagedown": "PgDown",
        "End": "End",
        "Home": "Home",
        "Arrowleft": "Left",
        "Arrowup": "Up",
        "Arrowright": "Right",
        "Arrowdown": "Down",
        "Printscreen": "PrintScreen",
        "Insert": "Ins",
        "Delete": "Del",
        "Num0": "Num0",
        "Num1": "Num1",
        "Num2": "Num2",
        "Num3": "Num3",
        "Num4": "Num4",
        "Num5": "Num5",
        "Num6": "Num6",
        "Num7": "Num7",
        "Num8": "Num8",
        "Num9": "Num9",
        "Num*": "Num*",
        "Num+": "Num+",
        "Num-": "Num-",
        "Num/": "Num/",
        "Num.": "Num.",
        "Numenter": "NumEnter",
        "Decimal": "Decimal",
        "Win": "Win",
        "Command": "Win",
    }

    @staticmethod
    def map_openai_key(key: str) -> str:
        if key in KeyMapper.special_keys:
            return KeyMapper.special_keys[key]

        if key.capitalize() in KeyMapper.special_keys:
            return KeyMapper.special_keys[key.capitalize()]

        if key == "CMD":
            return KeyMapper.special_keys["Command"]

        return KeyMapper.special_keys.get(key, key)

    @staticmethod
    def map_claude_key(key: str) -> str:
        if key in KeyMapper.special_keys:
            return KeyMapper.special_keys[key]

        if key.capitalize() in KeyMapper.special_keys:
            return KeyMapper.special_keys[key.capitalize()]

        if "KP_" in key:
            numpad_value = key.split("_")[1]

            if numpad_value in ("0", "1", "2", "3", "4", "5", "6", "7", "8", "9"):
                return KeyMapper.special_keys[f"Num{numpad_value}"]

            match numpad_value:
                case num if num in ("0", "1", "2", "3", "4", "5", "6", "7", "8", "9"):
                    return KeyMapper.special_keys[f"Num{num}"]
                case "Add":
                    return KeyMapper.special_keys["Num_plus"]
                case "Subtract":
                    return KeyMapper.special_keys["Num-"]
                case "Multiply":
                    return KeyMapper.special_keys["Num*"]
                case "Divide":
                    return KeyMapper.special_keys["Num/"]
                case "Enter":
                    return KeyMapper.special_keys["NumEnter"]
                case "Decimal":
                    return KeyMapper.special_keys["Decimal"]
                case _:
                    return key

        # elif is a letter
        elif len(key) == 1 and key.isalpha():
            return key

        elif "Control_" in key or key == "ctrl":
            return KeyMapper.special_keys["Control"]

        elif "Alt_" in key or key == "alt":
            return KeyMapper.special_keys["Alt"]

        elif "Shift_" in key or key == "shift":
            return KeyMapper.special_keys["Shift"]

        elif "Super_" in key:
            return KeyMapper.special_keys["Win"]

        elif key == "Return":
            return KeyMapper.special_keys["Enter"]

        elif key == "Tab":
            return KeyMapper.special_keys["Tab"]

        elif key == "space":
            return KeyMapper.special_keys["Space"]

        elif key == "BackSpace":
            return KeyMapper.special_keys["Backspace"]

        elif key == "Escape":
            return KeyMapper.special_keys["Escape"]

        elif key == "backslash":
            return KeyMapper.special_keys["Backslash"]

        elif key in ["Down", "Up", "Left", "Right"]:
            return KeyMapper.special_keys[f"Arrow{key.lower()}"]

        return KeyMapper.special_keys.get(key, key)
