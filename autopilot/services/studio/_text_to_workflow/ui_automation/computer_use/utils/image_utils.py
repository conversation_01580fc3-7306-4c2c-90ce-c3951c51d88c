from base64 import b64decode, b64encode
from dataclasses import dataclass
from io import Bytes<PERSON>
from typing import Tuple

from PIL import Image


@dataclass(frozen=True)
class ScreenResolution:
    width: int
    height: int


XGA_SIZE: ScreenResolution = ScreenResolution(1024, 768)


def decode_base64_image(b64_img: str) -> Image.Image:
    return Image.open(BytesIO(b64decode(b64_img)))


def encode_image_base64_png(image: Image.Image) -> str:
    image_bytes = BytesIO()
    image.save(image_bytes, format="PNG")
    return b64encode(image_bytes.getvalue()).decode()


def convert_image_to_res(image: Image.Image | str, resolution: ScreenResolution) -> Image.Image:
    """
    Convert an image (or base64 encoded image) to a new resolution, keeping the aspect
    ratio, centering the image and adding a black padding to fill the new resolution.

    Args:
        image: Image to convert. Can be a PIL Image or a base64 encoded image.
        res_width: New width resolution.
        res_height: New height resolution.

    Returns:
        Image in the new resolution.
    """
    if isinstance(image, str):
        new_image = decode_base64_image(image)
    else:
        new_image = image.copy()

    res_width, res_height = resolution.width, resolution.height
    width, height = new_image.size
    scale = min(1, min(res_width / width, res_height / height))
    new_width = int(width * scale)
    new_height = int(height * scale)
    if scale < 1:
        new_image = new_image.resize((new_width, new_height), Image.LANCZOS)

    background = Image.new("RGB", (resolution.width, resolution.height), (0, 0, 0))
    offset_x = (res_width - new_width) // 2
    offset_y = (res_height - new_height) // 2
    background.paste(new_image, (offset_x, offset_y))
    return background


def convert_to_xga(image: Image.Image | str) -> Image.Image:
    return convert_image_to_res(image, XGA_SIZE)


def convert_point_to_resized_image(point: Tuple[int, int], orig_screen_size: ScreenResolution, image_resolution: ScreenResolution) -> Tuple[int, int]:
    """
    Convert a point from the original resolution to the resized image resolution.

    Args:
        point: (x, y) point in original resolution
        orig_screen_size: Original screen size
        image_resolution: Resized image resolution

    Returns:
        Converted point in the resized image
    """
    x, y = point
    orig_width, orig_height = orig_screen_size.width, orig_screen_size.height
    res_width, res_height = image_resolution.width, image_resolution.height

    # Compute scale factor
    scale = min(res_width / orig_width, res_height / orig_height)

    # Compute offsets for centered image
    offset_x = (res_width - orig_width * scale) // 2
    offset_y = (res_height - orig_height * scale) // 2

    # Convert point
    converted_x = int(x * scale + offset_x)
    converted_y = int(y * scale + offset_y)

    return (converted_x, converted_y)


def revert_scroll_amount(scroll_amount: Tuple[int, int], orig_screen_size: ScreenResolution, image_resolution: ScreenResolution) -> Tuple[int, int]:
    """
    Revert a scroll amount from a converted image to the original resolution.

    Args:
        scroll_amount: Scroll amount to revert
        orig_screen_size: Original screen size.
        image_resolution: Image resolution

    Returns:
        Reverted scroll amount.
    """
    orig_width, orig_height = orig_screen_size.width, orig_screen_size.height
    res_width, res_height = image_resolution.width, image_resolution.height
    scale = min(1, min(res_width / orig_width, res_height / orig_height))
    resized_x = min(int(scroll_amount[0] / scale), orig_width - 1)
    resized_y = min(int(scroll_amount[1] / scale), orig_height - 1)

    return (resized_x, resized_y)


def revert_point_from_converted_image(
    point: Tuple[int, int],
    orig_screen_size: ScreenResolution,
    image_resolution: ScreenResolution,
    fallback_point: Tuple[int, int] = (0, 0),
) -> Tuple[int, int]:
    """
    Revert a point from a converted image to the original resolution. If the point is
    out of the original resolution, return (0, 0).

    Args:
        point: Point to revert
        orig_screen_size: Original screen size.
        image_resolution: Image resolution

    Returns:
        Reverted point.
    """
    x, y = point
    orig_width, orig_height = orig_screen_size.width, orig_screen_size.height
    res_width, res_height = image_resolution.width, image_resolution.height
    scale = min(1, min(res_width / orig_width, res_height / orig_height))
    offset_x = (res_width - int(orig_width * scale)) // 2
    offset_y = (res_height - int(orig_height * scale)) // 2
    resized_x = int((x - offset_x) / scale)
    resized_y = int((y - offset_y) / scale)
    if 0 > resized_x or resized_x >= orig_width or 0 > resized_y or resized_y >= orig_height:
        return fallback_point

    return (resized_x, resized_y)
