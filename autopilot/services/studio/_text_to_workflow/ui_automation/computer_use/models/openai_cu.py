import re
import time
from typing import Literal

from services.studio._text_to_workflow.ui_automation.action_definition import FinishStatus
from services.studio._text_to_workflow.ui_automation.computer_use.common.base_model import ComputerUseBaseModel
from services.studio._text_to_workflow.ui_automation.computer_use.common.messages import (
    OAIComputerUserTaskMessage,
    OAIComputerUseTextMessage,
    OAIComputerUseToolResultMessage,
)
from services.studio._text_to_workflow.ui_automation.computer_use.common.model_state import ComputerUseState
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.computer_use.common.tools import OAIComputerUseTool
from services.studio._text_to_workflow.ui_automation.computer_use.utils.image_utils import (
    XGA_SIZE,
    ScreenResolution,
    convert_to_xga,
    encode_image_base64_png,
    revert_point_from_converted_image,
    revert_scroll_amount,
)
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import TokenUsage
from services.studio._text_to_workflow.utils.inference.llm_gateway_multi_model import LLMGatewayMultiModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class OpenAIComputerUse(ComputerUseBaseModel):
    def __init__(self, model_name: str, do_reasoning: bool, resize_to_xga=False):
        self.model_name = model_name
        self.do_reasoning = do_reasoning
        self.resize_to_xga = resize_to_xga
        self.system_message = """ 
            You'll be given a task to complete on a computer screen. Use your available tools to complete the task. If you think that the task has finished, return a single text message as 'Finished : {{result}}'.
            If you have a request, need any confirmation or have any questions, then return a single text message with the format 'Question: {{your_question_here}}'.
            The current date is {current_date}.
            """

    def _find_last_response(self, response, response_type: Literal["computer_call", "message", "reasoning"]):
        # at most one computer call per response
        for idx in range(len(response["choices"]) - 1, -1, -1):
            output_item = response["choices"][idx]["message"]
            if output_item["type"] == response_type:
                return output_item

        return None

    def _get_token_usage(self, response) -> TokenUsage:
        return TokenUsage(
            model=self.model_name,
            input_tokens=response["usage"]["prompt_tokens"],
            output_tokens=response["usage"]["completion_tokens"],
            total_tokens=response["usage"]["total_tokens"],
        )

    def _normalize_response_step(self, response) -> ComputerUseStep:
        last_computer_call = self._find_last_response(response, "computer_call")
        reasoning_block = self._find_last_response(response, "reasoning")
        step_description = "OpenAI computer use step"

        if reasoning_block and len(reasoning_block["summary"]) > 0:
            step_description = reasoning_block["summary"][0]["text"]

        if not last_computer_call:
            try:
                text_response = self._find_last_response(response, "message")

                if not text_response or len(text_response["content"]) == 0:
                    raise ValueError(f"No text response found in response {response}")

                text_response = text_response["content"]

                finished_match = re.match(r"Finished\s*:\s*(.*)", text_response, re.DOTALL)
                question_match = re.match(r"Question\s*:\s*(.*)", text_response, re.DOTALL)

                if finished_match:
                    result = finished_match.group(1)
                    return ComputerUseStep.from_openai_computer_use(
                        {
                            "action": {"type": "finish", "result": result, "status": FinishStatus.Success},
                            "previous_response_id": response["id"],
                            "description": step_description,
                        }
                    )

                elif question_match:
                    question = question_match.group(1)
                    step = ComputerUseStep.from_openai_computer_use(
                        {"action": {"type": "user_question", "text": question}, "previous_response_id": response["id"], "description": step_description}
                    )
                    return step

                return ComputerUseStep.create_text_output_step(
                    description="No computer call found in response", text=str(text_response), status=FinishStatus.Success
                )

            except ValueError as e:
                raise e
            except Exception as e:
                raise Exception(f"Error parsing response: {response}\nError:\n{e}")

        return ComputerUseStep.from_openai_computer_use(
            {
                "previous_response_id": response["id"],
                "description": step_description,
                "action": {"id": last_computer_call["call_id"], **last_computer_call["action"]},
            }
        )

    def _revert_action_coordinates_from_xga(self, action_step: ComputerUseStep, orignal_screen_size: ScreenResolution) -> None:
        fallback_point = (0, 0)

        for action in action_step.actions:
            if "offset" in action.parameters:
                offset = revert_scroll_amount((abs(action.parameters["offset"][0]), abs(action.parameters["offset"][1])), orignal_screen_size, XGA_SIZE)
                action.parameters["offset"] = [
                    offset[0] * (1 if action.parameters["offset"][0] > 0 else -1),
                    offset[1] * (1 if action.parameters["offset"][1] > 0 else -1),
                ]
                fallback_point = (orignal_screen_size.width // 2, orignal_screen_size.height // 2)
            if "position" in action.parameters:
                action.parameters["position"] = revert_point_from_converted_image(action.parameters["position"], orignal_screen_size, XGA_SIZE, fallback_point)
            if "path" in action.parameters:
                source_point = revert_point_from_converted_image(
                    (action.parameters["path"][0]["x"], action.parameters["path"][0]["y"]), orignal_screen_size, XGA_SIZE
                )
                dest_point = revert_point_from_converted_image(
                    (action.parameters["path"][1]["x"], action.parameters["path"][1]["y"]), orignal_screen_size, XGA_SIZE
                )
                action.parameters["path"] = [{"x": source_point[0], "y": source_point[1]}, {"x": dest_point[0], "y": dest_point[1]}]

    async def predict(self, request_state: ComputerUseState) -> dict:
        if self.resize_to_xga:
            image = encode_image_base64_png(convert_to_xga(request_state.image))
            width, height = XGA_SIZE.width, XGA_SIZE.height
        else:
            image = request_state.image
            width, height = request_state.width, request_state.height

        reasoning = {}
        if self.do_reasoning:
            reasoning = {"generate_summary": "concise"}

        client = LLMGatewayMultiModel(
            feature_type=ConsumingFeatureType.SCREEN_AGENT,
            deployment_name=self.model_name,
            standardize_messages=False,
            tools=[OAIComputerUseTool(type="computer_use_preview", display_width=width, display_height=height, environment=request_state.env_name).to_dict()],
            reasoning=reasoning,
            truncation="auto",
        )

        if not request_state.previous_steps or len(request_state.previous_steps) == 0:
            system_prompt = self.system_message.format(current_date=time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()))
            task = f"{system_prompt}\nTask: {request_state.task}"
            response = await client.send_llm_request([OAIComputerUserTaskMessage(role="user", task=task, image=image).to_dict()])

        else:
            last_step = request_state.previous_steps[-1]
            last_step = ComputerUseStep.from_request_dict(last_step)

            if last_step.actions[0].name == "user_question":
                request_input = [OAIComputerUseTextMessage(role="user", text=last_step.actions[0].result).to_dict()]  # type: ignore
            else:
                request_input = [
                    OAIComputerUseToolResultMessage(previous_call_id=last_step.actions[-1].id, type="computer_call_output", image=image).to_dict()  # type: ignore
                ]

            response = await client.send_llm_request(request_input, previous_response_id=last_step.additional_parameters["previous_response_id"])

        step = self._normalize_response_step(response)

        if self.resize_to_xga:
            self._revert_action_coordinates_from_xga(step, ScreenResolution(request_state.width, request_state.height))

        return {"step": step.to_response_dict(), "token_usage": self._get_token_usage(response).to_json()}
