import re
import time
from typing import Dict, <PERSON>, <PERSON><PERSON>

from services.studio._text_to_workflow.ui_automation.action_definition import Finish<PERSON>tatus
from services.studio._text_to_workflow.ui_automation.computer_use.common.base_model import ComputerUseBaseModel
from services.studio._text_to_workflow.ui_automation.computer_use.common.messages import (
    ClaudeComputerUseActionMessage,
    ClaudeComputerUseImageMessage,
    ClaudeComputerUseMessage,
    ClaudeComputerUseRedactedThinkingMessage,
    Claude<PERSON>om<PERSON>r<PERSON>serToolCallMessage,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TaskMessage,
    <PERSON>ComputerUseTextMessage,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ThinkingMessage,
    ClaudeComputerUseToolResultMessage,
)
from services.studio._text_to_workflow.ui_automation.computer_use.common.model_state import ComputerUseState
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.computer_use.common.tools import ClaudeComputer<PERSON><PERSON><PERSON>ool20250124
from services.studio._text_to_workflow.ui_automation.computer_use.utils.image_utils import (
    XGA_SIZE,
    ScreenResolution,
    convert_point_to_resized_image,
    convert_to_xga,
    encode_image_base64_png,
    revert_point_from_converted_image,
)
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import PreviousStepsParameters, TokenUsage
from services.studio._text_to_workflow.utils.inference.llm_gateway_multi_model import LLMGatewayMultiModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class ClaudeComputerUse(ComputerUseBaseModel):
    def __init__(self, model_name, resize_to_xga, max_tokens, anthropic_beta_flag, max_chat_history_messages, max_chat_history_images, thinking):
        self.model_name = model_name
        self.resize_to_xga = resize_to_xga
        self.max_tokens = max_tokens
        self.thinking = thinking if thinking and thinking["type"] == "enabled" else None
        self.anthrpoic_beta_flag = anthropic_beta_flag
        self.max_chat_history_messages = max_chat_history_messages
        self.max_chat_history_images = max_chat_history_images

        if self.thinking:
            self.max_tokens += self.thinking["budget_tokens"]

        self.system_message = """
            <IMPORTANT>
            You'll be given a task to complete on a computer screen. Use your available tools to complete the task. If you think that the task has finished, return a single text message as 'Finished : {{result}}'.
            If you have a request, need any confirmation or have any questions, then return a single text message with the format 'Question: {{your_question_here}}'.
            If the task is finished or you have a question, you should return a single text message in the format above.
            You are always provided with the image of the screen to help you with the task. Do not use the screenshot action altogether.
            </IMPORTANT>

            The current date is {current_date}.
            """

    def _get_token_usage(self, response) -> TokenUsage:
        return TokenUsage(
            model=self.model_name,
            input_tokens=response["usage"]["prompt_tokens"],
            output_tokens=response["usage"]["completion_tokens"],
            total_tokens=response["usage"]["total_tokens"],
        )

    def _parse_additional_actions(self, message, query_action):
        match = re.search(rf"{query_action}\s*:\s*(.*)", message, re.DOTALL)
        if match:
            return match.group(1)
        return None

    def _revert_action_coordinates_from_xga(self, response: dict, orignal_screen_size: ScreenResolution) -> None:
        def check_str_coords(coords) -> Tuple[int, int]:
            # model sometimes outputs a string instead of a [x, y] list
            if isinstance(coords, str):
                coords = coords.strip("[]").split(", ")
                x, y = int(coords[0]), int(coords[1])
                return (x, y)
            if isinstance(coords, list):
                return tuple(coords)
            return (0, 0)

        for choice in response["choices"]:
            if choice["finish_reason"] == "tool_use":
                message = choice["message"]
                for tool_call in message["tool_calls"]:
                    if "coordinate" in tool_call["arguments"]:
                        tool_call["arguments"]["coordinate"] = revert_point_from_converted_image(
                            check_str_coords(tool_call["arguments"]["coordinate"]), orignal_screen_size, XGA_SIZE
                        )
                    if "start_coordinate" in tool_call["arguments"]:
                        tool_call["arguments"]["start_coordinate"] = revert_point_from_converted_image(
                            check_str_coords(tool_call["arguments"]["start_coordinate"]), orignal_screen_size, XGA_SIZE
                        )

    def _convert_step_coordinates_to_xga(self, steps: List[ComputerUseStep], screen_res: ScreenResolution):
        for step in steps:
            for action in step.actions:
                if "position" in action.parameters:
                    action.parameters["position"] = convert_point_to_resized_image(action.parameters["position"], screen_res, XGA_SIZE)
                if "path" in action.parameters:
                    action.parameters["path"][0] = convert_point_to_resized_image(action.parameters["path"][0], screen_res, XGA_SIZE)
                    action.parameters["path"][1] = convert_point_to_resized_image(action.parameters["path"][1], screen_res, XGA_SIZE)

    def _normalize_response_step(self, response: dict) -> ComputerUseStep:
        tool_actions = []
        text_blocks = []
        thinking_block = {}
        redacted_thinking_block = {}

        message = response["choices"][0]["message"]
        finish_reason = response["choices"][0]["finish_reason"]

        if finish_reason == "tool_use":
            for tool_call in message["tool_calls"]:
                tool_actions.append({"id": tool_call["id"], **tool_call["arguments"]})

        elif finish_reason == "end_turn":
            text_blocks.append(message["content"])

        if self.thinking:
            if message.get("thinking", None):
                thinking_block = {"thinking": message["thinking"], "signature": message["signature"]}
            if message.get("redacted_thinking", None):
                redacted_thinking_block = {"data": message["data"]}

        if len(tool_actions) > 0:
            return ComputerUseStep.from_claude_computer_use(
                tool_actions,
                description="Claude computer use step",
                thought=thinking_block.get("thinking"),
                thinking_block=thinking_block,
                redacted_thinking_block=redacted_thinking_block,
            )

        if len(text_blocks) > 0:
            last_text_message = text_blocks[-1]

            query_message = self._parse_additional_actions(last_text_message, "Question")

            if query_message:
                return ComputerUseStep.from_claude_computer_use(
                    [{"action": "user_question", "text": query_message, "id": response["id"]}],
                    description="User question",
                    thought=thinking_block.get("thinking"),
                    thinking_block=thinking_block,
                    redacted_thinking_block=redacted_thinking_block,
                )
            else:
                finish_message = self._parse_additional_actions(last_text_message, "Finished")
                if finish_message:
                    return ComputerUseStep.from_claude_computer_use(
                        [{"action": "finish", "result": finish_message, "status": FinishStatus.Success, "id": response["id"]}],
                        description="Tasked finished",
                        thought=thinking_block.get("thinking"),
                        thinking_block=thinking_block,
                        redacted_thinking_block=redacted_thinking_block,
                    )

        return ComputerUseStep.create_text_output_step(
            description="No computer call found in response",
            text=str(text_blocks[-1]) if len(text_blocks) > 0 else str(message[-1]),
            status=FinishStatus.Success,
        )

    async def _run_llm_gateway_api_call(
        self,
        display_width: int,
        display_height: int,
        image: str | None,
        previous_messages: List[ClaudeComputerUseMessage] | None,
        task: str,
    ) -> dict:
        client = LLMGatewayMultiModel(
            feature_type=ConsumingFeatureType.SCREEN_AGENT,
            anthropic_computer_use_tool=self.anthrpoic_beta_flag,
            deployment_name=self.model_name,
            max_tokens=self.max_tokens,
            standardize_messages=False,
            tools=[ClaudeComputerUseTool20250124(display_width_px=display_width, display_height_px=display_height, display_number=1).to_dict()],
            thinking=self.thinking,
            temperature=1.0 if self.thinking else 0.0,  # temperature must be 1.0 when thinking is enabled
        )

        step_messages = []
        if not previous_messages:
            assert image is not None, "Image is required when starting a new task"
            step_messages.append(ClaudeComputerUseTaskMessage(role="user", task=task, image=image))

        else:
            step_messages.append(ClaudeComputerUseTextMessage(role="user", text=task))
            step_messages.extend(previous_messages)
            if image is not None:
                step_messages.append(ClaudeComputerUseImageMessage(role="user", image=image))

        response = await client.send_llm_request([message.to_dict() for message in step_messages])

        return response

    def _build_message_history(self, previous_steps: List[dict], screen_res: ScreenResolution) -> List[ClaudeComputerUseMessage]:
        previous_messages = []

        steps = [ComputerUseStep.from_request_dict(step) for step in previous_steps[-self.max_chat_history_messages :]]
        if self.resize_to_xga:
            self._convert_step_coordinates_to_xga(steps, screen_res)

        for step in steps:
            if step.image is not None:
                previous_messages.append(ClaudeComputerUseImageMessage(role="user", image=step.image))

            if step.additional_parameters:
                if step.additional_parameters.get("thinking_block", None):
                    thinking_block = step.additional_parameters["thinking_block"]
                    previous_messages.append(
                        ClaudeComputerUseThinkingMessage(role="assistant", text=thinking_block["thinking"], signature=thinking_block["signature"])
                    )

                if step.additional_parameters.get("redacted_thinking_block", None):
                    redacted_thinking_block = step.additional_parameters["redacted_thinking_block"]
                    previous_messages.append(ClaudeComputerUseRedactedThinkingMessage(role="assistant", data=redacted_thinking_block["data"]))

            tool_calls = [ClaudeComputerUseActionMessage(**action.to_claude_action()) for action in step.actions]
            tool_results = [ClaudeComputerUseToolResultMessage(id=action.id, content="Tool executed successfully.") for action in step.actions]

            previous_messages.append(ClaudeComputerUserToolCallMessage(role="assistant", tool_calls=tool_calls))
            previous_messages.extend(tool_results)

        return previous_messages

    async def predict(self, request_state: ComputerUseState) -> Dict:
        if self.resize_to_xga:
            image = encode_image_base64_png(convert_to_xga(request_state.image))
        else:
            image = request_state.image

        system_prompt = self.system_message.format(current_date=time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()))

        task = f"{system_prompt}\nTask: {request_state.task}"

        if not request_state.previous_steps or len(request_state.previous_steps) == 0:
            response = await self._run_llm_gateway_api_call(request_state.width, request_state.height, image, previous_messages=None, task=task)
        else:
            previous_messages = self._build_message_history(request_state.previous_steps, ScreenResolution(request_state.width, request_state.height))

            last_step = ComputerUseStep.from_request_dict(request_state.previous_steps[-1])

            if last_step.actions[0].name == "user_question":
                if last_step.actions[0].result:
                    previous_messages.append(ClaudeComputerUseTextMessage(role="user", text=last_step.actions[0].result))
                response = await self._run_llm_gateway_api_call(
                    request_state.width, request_state.height, image=None, previous_messages=previous_messages, task=task
                )
            else:
                response = await self._run_llm_gateway_api_call(request_state.width, request_state.height, image, previous_messages, task)

        if self.resize_to_xga:
            self._revert_action_coordinates_from_xga(response, ScreenResolution(request_state.width, request_state.height))

        normalized_step = self._normalize_response_step(response)
        previous_steps_parameters = PreviousStepsParameters(
            max_chat_history_messages=self.max_chat_history_messages, max_chat_history_images=self.max_chat_history_images, image=image
        )

        return {
            "step": normalized_step.to_response_dict(),
            "previous_steps_parameters": previous_steps_parameters.model_dump(),
            "token_usage": self._get_token_usage(response).to_json(),
        }
