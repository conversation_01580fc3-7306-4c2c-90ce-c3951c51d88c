import time
from typing import <PERSON><PERSON>

from services.studio._text_to_workflow.ui_automation.computer_use.common.base_model import ComputerUseBaseModel
from services.studio._text_to_workflow.ui_automation.computer_use.common.model_state import ComputerUseState
from services.studio._text_to_workflow.ui_automation.computer_use.models.claude_cu import ClaudeComputerUse
from services.studio._text_to_workflow.ui_automation.computer_use.models.openai_cu import OpenAIComputerUse


class ComputerUseModel:
    def __init__(self, options):
        self.options = options

    def get_llm_model_name(self, request_state: ComputerUseState) -> str:
        return self.options["computer_use_engines"][request_state.engine_name]["model_name"]

    def _get_computer_use_model(self, engine_name: str) -> ComputerUseBaseModel:
        if engine_name == "openai-computer-use":
            return OpenAIComputerUse(**self.options["computer_use_engines"][engine_name])
        if engine_name == "claude-computer-use":
            return ClaudeComputerUse(**self.options["computer_use_engines"][engine_name])

        raise ValueError(f"Computer use engine {engine_name} is not supported")

    async def get_request_state(self, request: dict) -> ComputerUseState:
        computer_use_engine_name = request.get("model_name", None)

        if computer_use_engine_name:
            assert computer_use_engine_name in self.options["computer_use_engines"], f"Computer use model {computer_use_engine_name} not found in engine config"
        else:
            raise ValueError("model_name was not provided in the request")

        return ComputerUseState(
            engine_name=computer_use_engine_name,
            task=request["userTask"],
            image=request["image"],
            width=request["image_width"],
            height=request["image_height"],
            env_name=request["env_name"],
            previous_steps=request.get("previousSteps", []),
        )

    async def predict(self, request_state: ComputerUseState) -> Tuple[dict, dict]:
        starting_time = time.time()
        model = self._get_computer_use_model(request_state.engine_name)
        output = await model.predict(request_state)

        request_time = time.time() - starting_time
        return output, {"request_time": request_time}
