{"cells": [{"cell_type": "code", "execution_count": 1, "id": "94c71e21", "metadata": {}, "outputs": [], "source": ["from util.gptutil.engine import GPTEngine\n", "from util.gptutil.parsers import SimpleParser\n", "from util.gptutil.schema import GPTEngineChatMessage"]}, {"cell_type": "code", "execution_count": 23, "id": "089d40a4", "metadata": {}, "outputs": [], "source": ["config = {\"type\": \"azure\", \"base\": \"https://uipath-ml-openai.openai.azure.com/\", \"version\": \"2023-03-15-preview\", \"key\": \"630611c1ebcf47f5adbdba120c363c67\"}\n", "\n", "engine_config = {\n", "    # \"engine\": \"gpt-35-turbo\",\n", "    \"engine\": \"gpt-4-vision\",\n", "    \"temperature\": 0.0,\n", "    \"max_tokens\": 1000,\n", "    \"stop\": None,\n", "    \"top_p\": 0.0,\n", "    \"frequency_penalty\": 0.0,\n", "    \"presence_penalty\": 0.0,\n", "}\n", "gpt_engine = GPTEngine(engine_config, config)"]}, {"cell_type": "code", "execution_count": null, "id": "22da9f78-a6e2-47e2-ba7e-bdb48745f380", "metadata": {}, "outputs": [], "source": ["??gpt_engine.chat"]}, {"cell_type": "code", "execution_count": null, "id": "e575cb4b-35a7-42ae-9ffb-f7424aefa7f1", "metadata": {}, "outputs": [], "source": ["gpt_engine.config"]}, {"cell_type": "code", "execution_count": 26, "id": "7dcd85ce", "metadata": {}, "outputs": [], "source": ["def send_gpt_message(message):\n", "    request_message = GPTEngineChatMessage(role=\"system\", content=message)\n", "    messages = [request_message]\n", "    response = gpt_engine.chat(messages, \"\", \"\")\n", "    parser = SimpleParser()\n", "    result = []\n", "    for choice in response[\"choices\"]:\n", "        result.append(parser.parse(choice[\"message\"][\"content\"]))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "1f975a62", "metadata": {}, "outputs": [], "source": ["request_message = \"\"\"You are an UI automation assistant expert that performs certain actions on computer screens to complete tasks.\n", "Screens are represented as a DOM which contains elements like buttons <Button>, texts <Texts>, InputBox <InputBox> etc. An element is uniquely identified by his ID.\n", "Each action has a description and a method and an expected result. All fields are mandatory.\n", "\n", "The possible methods are:\n", "\n", "click(element_id:int) # Clicks on an element with the corresponding element id.\n", "type_into(element_id:int, value:str) # Enter the value into an input element with the corresponding element id\n", "get_text(element_id:int) # Extract the text from the element with the corresponding element id\n", "select(element_id:int, value:str) # Select the value from a list or picker from the corresponding element id\n", "finish() # Indicates that the user task is completed\n", "\n", "For type_into, if value is not specified, use the variables in the variable list or create new variables using prefixed $. Do not invent value not speficied in the task.\n", "For example: type_into(234, $city)\n", "\n", "Group your actions into steps which are valid to the current screen only. Look at some steps examples:\n", "\n", "<Step description=\"Create an invoice in SAP\">\n", "  <Action description=\"Click on the SAP link\" action=\"click(14)\" />\n", "</Step>\n", "\n", "<Step description=\"Remove last recording\">\n", "  <Action description=\"Select the last recording item\" action=\"click(46)\"/>\n", "  <Action description=\"Press the remove button\" action=\"click(18)\"/>\n", "</Step>\n", "\n", "<Step description=\"Book a flight from Bucharest to London from Jul 7 2023 to Jul 14 2023.\">\n", "  <Action description=\"Type 'Bucharest' in the source box\" action=\"type_into(31, 'Bucharest')\" />\n", "  <Action description=\"Type 'London' in the destination\" action=\"type_into(32, 'London')\" />\n", "  <Action description=\"Set departure date with Jul 7 2023\" action=\"select(46,'Jul 7 2023')\" />\n", "  <Action description=\"Set return date with Jul 14 2023\" action=\"select(47,'Jul 14 2023')\" />\n", "  <Action description=\"Click Submit\" action=\"click(50)\" />\n", "</Step>\n", "\n", "<Step description=\"Fill in form\">\n", "  <Action description=\"Type $email from variable list in the email input box\" action=\"type_into(23, $email)\" />\n", "  <Action description=\"Type $country from variable list in the country input box\" action=\"type_into(30, $email)\" />\n", "  <Action description=\"Create $age variable and type $age to age input box\" action=\"type_into(100, $age)\" />\n", "  <Action description=\"Click Submit\" action=\"click(20)\" />\n", "</Step>\n", "\n", "You will be asked to complete a task in many steps. After each step the screen will be updated.\n", "\n", "Current screen:\n", "Title: Rpa Challenge\n", "Url: https://www.rpachallenge.com/\n", "DOM: <Window><Container><Text Text=\"RPA Challenge\" Id=\"2\" /><Container><InputBox Text=\"Input Forms\" Id=\"9\" /><Button Text=\"Shortest Path\" Id=\"10\" /><Button Text=\"Movie Search\" Id=\"11\" /><Button Text=\"Invoice Extraction\" Id=\"12\" /><Button Text=\"RPA Stock Market\" Id=\"13\" /></Container></Container><Container><Container><Container><Text Text=\"Instructions\" Id=\"23\" /><Text Text=\"EN\" Id=\"24\" /></Container><Text Text=\"1. The goal of this challenge is to create a workflow that will input data from a spreadsheet into the form fields on the screen.\" Id=\"17\" /><Text Text=\"2. Beware! The fields will change position on the screen after every submission throughout 10 rounds thus the workflow must correctly identify where each spreadsheet record must be typed every time.\" Id=\"18\" /><Text Text=\"3. The actual countdown of the challenge will begin once you click the Start button until then you may submit the form as many times as you wish without receiving penalties.\" Id=\"19\" /><Text Text=\"Good luck!\" Id=\"20\" /><Container><Button Text=\"Download Excel cloud_download\" Id=\"25\" /><Button Text=\"START\" Id=\"26\" /></Container></Container><Container><Container><Container><Text Text=\"Phone Number\" Id=\"38\" /><InputBox Id=\"39\" /></Container><Container><Text Text=\"Role in Company\" Id=\"40\" /><InputBox Id=\"41\" /></Container><Container><Text Text=\"Last Name\" Id=\"42\" /><InputBox Id=\"43\" /></Container><Container><Text Text=\"Email\" Id=\"44\" /><InputBox Id=\"45\" /></Container><Container><Text Text=\"Company Name\" Id=\"46\" /><InputBox Id=\"47\" /></Container><Container><Text Text=\"First Name\" Id=\"48\" /><InputBox Id=\"49\" /></Container><Container><Text Text=\"Address\" Id=\"50\" /><InputBox Id=\"51\" /></Container></Container></Container></Container></Window>\n", "\n", "Previous done steps:\n", "None\n", "\n", "Variable list:\n", "None\n", "\n", "Your task: Fill in the form for first, last name, phone number, and email.\n", "\n", "Create variables for input fields if not speficied in variable list.\n", "\n", "Question: Given the previous done steps and the current screen, what is the best step to complete the task?\n", "Assistant response in xml format:\n", "\"\"\"\n", "\n", "out = send_gpt_message(request_message)\n", "print(out[0][0][\"completion\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}