import html
import json

import tiktoken

from services.studio._text_to_workflow.ui_automation.action import Step


def match_set_info(expected_action, predicted_action, variables, input_variables, task):
    id1, var1, value1 = expected_action.parameters["element_id"], expected_action.parameters["variable"], expected_action.parameters["default_value"]
    id2, var2, value2 = predicted_action.parameters["element_id"], predicted_action.parameters["variable"], predicted_action.parameters["default_value"]
    if id2 != id1:
        return False
    if not var2.startswith("$"):
        return False

    if var1[1:] in variables | input_variables:
        # input variable is defined, should pick it
        # also test if a default value is created
        if var2 != var1 or len(value2) == 0:
            return False
    else:
        # input variable is not defined, we should generate a new variable var1
        # can not compare var1 and var2
        # if the value is specified in the task then default value should be the same
        if (value2.lower() in task.lower()) and (value1.lower() != value2.lower()):
            return False
    return True


def match_get_info(expected_action, predicted_action, variables, output_variables, is_ui_agent=False):
    id1, var1 = expected_action.parameters["element_id"], expected_action.parameters["variable"]
    if is_ui_agent:
        id2 = predicted_action.parameters["element_id"]
        var2 = None
    else:
        id2, var2 = predicted_action.parameters["element_id"], predicted_action.parameters["variable"]
    if id2 != id1:
        return False
    if not is_ui_agent and not var2.startswith("$"):
        return False
    if not is_ui_agent and (var1[1:] in output_variables | variables):
        # output variable is defined, should pick it
        return var2 == var1

    return True


def match_click(expected_action, predicted_action):
    id1 = expected_action.parameters["element_id"]
    id2 = predicted_action.parameters["element_id"]
    return id2 == id1


def match_actions(expected_action, predicted_action, variables, input_variables, output_variables, task, is_ui_agent=False):
    expected_method = expected_action.name
    predicted_method = predicted_action.name
    if expected_method != predicted_method:
        return False
    if expected_method in ("type_into", "select"):
        return match_set_info(expected_action, predicted_action, variables, input_variables, task)
    if expected_method == "get_text":
        return match_get_info(expected_action, predicted_action, variables, output_variables, is_ui_agent=is_ui_agent)
    if expected_method == "click":
        return match_click(expected_action, predicted_action)
    if expected_method == "finish":
        return True
    raise ValueError("Not supported method:", expected_method)


def eval_step_score(expected_step, predicted_step, variables, input_variables, output_variables, task="", is_ui_agent=False):
    tp = 0
    fp = 0

    # sometimes the model might predict duplicate actions
    # we could mitigate this in 2 ways
    # 1. count them as fp, although they are pretty benign
    # 2. deduplicate the actions before matching
    # discussed to go with 2. for now to allow for some tolerance
    taken_actions = set()

    num_actions = len(expected_step.actions)
    if predicted_step is not None:
        for predicted_action in predicted_step.actions:
            action_serialization = predicted_action.serialize()
            if action_serialization in taken_actions:
                continue
            taken_actions.add(action_serialization)

            is_match = False
            for expected_action in expected_step.actions:
                if match_actions(expected_action, predicted_action, variables, input_variables, output_variables, task, is_ui_agent=is_ui_agent):
                    is_match = True
                    break
            if is_match:
                tp += 1
            else:
                fp += 1

    return tp, fp, num_actions


def test_eval1():
    expected_step_1 = Step.parse_xml(
        """
  <Step description="">
  <Action description="" method="type_into(39,$phonenumber,'')"/>
  <Action description="" method="type_into(43,$lastname,'')"/>
  <Action description="" method="type_into(45,$email,'')"/>
  <Action description="" method="type_into(49,$firstname,'')"/>
</Step>
""",
        is_ui_agent=False,
    )

    predicted_step_1 = Step.parse_xml(
        """
<Step description="">
  <Action description="" method="type_into(49,$firstname,'John')"/>
  <Action description="" method="type_into(43,$lastname,'Doe')"/>
  <Action description="" method="type_into(45,$email,'<EMAIL>')"/>
  <Action description="" method="type_into(39,$phone_number,'1234567890')"/>
</Step>
""",
        is_ui_agent=False,
    )
    tp, fp, num_actions = eval_step_score(
        expected_step_1,
        predicted_step_1,
        variables={},
        input_variables={"firstname": "str", "lastname": "str"},
        output_variables={},
    )
    assert tp == 4
    assert fp == 0


openai_encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")

prefix = """
<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
.collapsible {
  background-color: #888;
  color: white;
  cursor: pointer;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 15px;
}

.active, .collapsible:hover {
  background-color: #666;
}

.content {
  padding: 0 18px;
  display: none;
  overflow: hidden;
  background-color: #f1f1f1;
}
</style>
</head>
<body>
"""

suffix = """
<script>
var coll = document.getElementsByClassName("collapsible");
var i;

for (i = 0; i < coll.length; i++) {
  coll[i].addEventListener("click", function() {
    this.classList.toggle("active");
    var content = this.nextElementSibling;
    if (content.style.display === "block") {
      content.style.display = "none";
    } else {
      content.style.display = "block";
    }
  });
}
</script>

</body>
</html>
"""

div_template = """<div>
<button type="button" class="collapsible">{title}</button>
<pre style="display:none">{content}</pre>
</div>"""


def log_data_to_html(log_data, dataset, write_image=False, write_prompt=True):
    s = prefix
    s += f"<h2>Evaluation {log_data['config']['engine_config']['default_engine']}</h2>"
    total_correct = 0
    total_incorrect = 0
    total_actions = 0
    for _task_name, task_log in log_data["tasks"].items():
        task_result = task_log["result"]
        total_correct += task_result["correct actions"]
        total_actions += task_result["expected actions"]
        total_incorrect += task_result["incorrect actions"]

    recall = total_correct / (total_actions + 1e-6)
    precision = total_correct / (total_correct + total_incorrect + 1e-6)
    f1 = recall * precision * 2 / (recall + precision + 1e-6)
    metrics = {
        "f1": f1,
        "precision": precision,
        "recall": recall,
        "total_actions": total_actions,
        "total_steps": log_data["total_steps"],
    }
    s += f"<h4>Precision: {precision:.2f},&emsp; Recall: {recall:.2f},&emsp; F1: {f1:.2f}</h4>"
    s += f"<h4>Actions: {total_actions},&emsp; Steps: {log_data['total_steps']},&emsp; Total time: {log_data['time']:.2f},&emsp; \
LLM time: {log_data['request_time']:.2f},&emsp; \
LLM requests: {log_data['request_count']},&emsp; Avg request time: {log_data['avg_request_time']:.2f}</h4>"
    s += f"<h4>ValidationException: {log_data['ValidationException']},&emsp; RateLimitError: {log_data['RateLimitError']}</h4>"

    s += f"<pre>{json.dumps(log_data['config'], indent=2)}</pre>"

    for task_name, task_log in log_data["tasks"].items():
        task_data = dataset[task_name]["data"]
        task = task_log["task"]
        s += "<hr>"
        s += f"<h3>{task}</h3>"
        s += f"<p>Time: {task_log['time']:.2f},&emsp; LLM time: {task_log['request_time']:.2f},&emsp; LLM requests: {task_log['request_count']},&emsp; \
Avg request time: {task_log['avg_request_time']:.2f}</p>"
        s += f"<p>ValidationException: {task_log['ValidationException']},&emsp; RateLimitError: {task_log['RateLimitError']}</p>"
        s += f"<pre>{html.escape(json.dumps(task_log['result'], indent=2))}</pre>"

        for idx, step_log in enumerate(task_log["steps"], 1):
            step_data = task_data[idx - 1]
            step_str = ""
            dom_enc = openai_encoding.encode(step_log["dom"])
            if write_image:
                step_str += f'<div><img src="data:image/png;base64, {step_data["image_base64"]}" /></div>'
            step_str += f"<p>DOM: {len(dom_enc)} tokens</p>" + html.escape(step_log["dom"])
            step_str += "<br/>"
            step_str += "<p>Input Variables:</p>" + html.escape(step_log["input_variables"])
            step_str += "<p>Output Variables:</p>" + html.escape(step_log["output_variables"])
            step_str += "<br/>"
            step_str += "<p>Previous steps:</p>" + html.escape(step_log["previous_steps"])
            step_str += "<br/>"
            completion_enc = openai_encoding.encode(step_log["completion"])
            step_str += f"<p>Response: {len(completion_enc)} tokens<p>" + html.escape(step_log["completion"])
            step_str += "<br/>"
            step_str += (
                "<p>Predicted step:<p>" + html.escape(step_log["predicted_step"]) + "<br/>" + "<p>Expected step:</p>" + html.escape(step_log["expected_step"])
            )
            s += div_template.format(
                title=f"Step {idx}: {step_log['correct_actions']} / {step_log['expected_actions']} correct, {step_log['incorrect_actions']} incorrect",
                content=step_str,
            )
            if write_prompt:
                prompt_enc = openai_encoding.encode(step_log["prompt"])
                s += div_template.format(
                    title=f"Prompt: {len(prompt_enc)} tokens",
                    content=html.escape(step_log["prompt"]),
                )
    s += suffix
    return s, metrics


if __name__ == "__main__":
    test_eval1()
