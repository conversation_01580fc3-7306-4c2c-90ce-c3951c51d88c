import asyncio
import json
import xml.etree.ElementTree as ET

from uipath_cv_client.dom_processing import (
    BoxCoordinates,
    DomSourceType,
    get_dom_tree_from_elements,
    parse_raw_dom,
)

from services.studio._text_to_workflow.ui_automation.options import opt
from services.studio._text_to_workflow.ui_automation.ui_automation_service import QaElementWithImageRequest, QaGetElementDescriptionRequest, UiAutomationService


def find_raw_target(dom, target_id):
    if dom["Id"] == target_id:
        return dom
    if "Children" in dom:
        for child in dom["Children"]:
            found = find_raw_target(child, target_id)
            if found:
                return found
    else:
        return None


def debug_qa():
    service = UiAutomationService(opt)
    sample_path = "..."
    with open(sample_path) as f:
        json_content = json.load(f)
        elements = json_content["ExtractedDOM"]["RawDOM"]["RawElements"]
        image_base_64 = json_content["ImageBase64"]
    target_box = BoxCoordinates(**{"x": 1694, "y": 6268, "width": 32, "height": 32})
    request = QaGetElementDescriptionRequest(target_box=target_box, image=image_base_64, dom=elements, element_id=85)
    response = asyncio.run(service.get_element_description(request))
    generated_description = response.get("description", "")
    print("generated_description:", generated_description)
    request = QaElementWithImageRequest(image=image_base_64, human_description="share abc", dom=elements)
    dom_info = asyncio.run(service.extract_dom_data_from_request(request_raw_dom=request.dom, request_image=request.image))
    parsed_dom = parse_raw_dom(
        dom_info.raw_dom,
        dom_type=dom_info.dom_type,
        options=opt,
        image=None,
    )

    print(ET.tostring(parsed_dom, encoding="utf-8").decode("utf-8"))


def debug_raw_dom(raw_dom_path):
    with open(raw_dom_path, "r", encoding="utf-8") as f:
        request_raw_dom = json.load(f)
    raw_dom: dict = get_dom_tree_from_elements(request_raw_dom)
    opt["dom"]["display_containers_id"] = True
    opt["dom"]["output_select_tag"] = True
    xml_dom = parse_raw_dom(raw_dom, dom_type=DomSourceType.Driver, options=opt, image=None)
    print(ET.tostring(xml_dom, encoding="utf-8").decode("utf-8"))
    return xml_dom


if __name__ == "__main__":
    raw_dom_path = r"C:\Users\<USER>\work\ML2\computer_vision\release\uipath_cv_client\tests\rawdom\select.json"
    xml_dom = debug_raw_dom(raw_dom_path)
