import json
import re
from typing import List

import langchain.schema
import requests
from json_minify import json_minify
from json_repair import repair_json

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger


class ValidationException(Exception):
    def __init__(self, message: str):
        print("ValidationException:", message)
        self.message = message


def parse_message_json(message: str) -> dict:
    message = message.strip()

    # 1. Try extracting between triple backticks (```json ... ```)
    #    The '([\s\S]+?)' part matches across multiple lines (non‐greedy).
    code_block_pattern = r"```json\s*([\s\S]+?)```"
    code_block_match = re.search(code_block_pattern, message, re.DOTALL)

    if code_block_match:
        # If we find a code block, extract just that part
        json_str = code_block_match.group(1).strip()
    else:
        # 2. Fallback to finding the first {...} block in the text
        bracket_pattern = r"\{.*\}"
        bracket_match = re.search(bracket_pattern, message, re.DOTALL)
        if not bracket_match:
            raise ValidationException("Message does not have correct json format")
        json_str = bracket_match.group(0).strip()

    # 3. Remove comments, etc. before parsing
    try:
        json_str = json_minify(json_str)
        data = json.loads(json_str)
    except json.JSONDecodeError:
        try:
            console_logger.error("Error parsing JSON, trying to repair it %s", message)
            json_str = repair_json(json_str)
            data = json.loads(json_str)
        except json.JSONDecodeError:
            console_logger.error("Repar json failed")
            raise ValidationException("Message does not have correct json format")
    return data


def get_input_message(messages: List[langchain.schema.BaseMessage]):
    """
    Get the input message string from the langchain messages for debugging
    """
    input_message = ""
    for idx, message in enumerate(messages, 0):
        if idx > 0:
            input_message += "\n"
        if isinstance(message.content, str):
            input_message += message.content
        elif isinstance(message.content, list):
            for content in message.content:
                assert isinstance(content, dict)
                if content["type"] == "text":
                    input_message += content["text"]
                elif "image" in content["type"]:
                    input_message += "<image>"
                else:
                    raise ValueError(f"Unknown content type: {content['type']}")
        else:
            raise ValueError(f"Unknown message type: {message.content}")

    return input_message


STREAM_LOG_URL = "http://127.0.0.1:8000"


# for live log streaming
def clear_log():
    requests.post(f"{STREAM_LOG_URL}/reset")


def send_log(payload):
    requests.post(f"{STREAM_LOG_URL}/add_log", json=payload)
