class KeyMapper:
    special_keys = {
        "Backspace": "Back",
        "Backslash": "Backslash",
        "Tab": "Tab",
        "Enter": "Enter",
        "Shift": "Shift",
        "Control": "Ctrl",
        "Alt": "Alt",
        "Pause": "Break",
        "Capslock": "Caps",
        "Escape": "Esc",
        "Space": "Space",
        "Pageup": "PgUp",
        "Pagedown": "PgDown",
        "End": "End",
        "Home": "Home",
        "Arrowleft": "Left",
        "Arrowup": "Up",
        "Arrowright": "Right",
        "Arrowdown": "Down",
        "Printscreen": "PrintScreen",
        "Insert": "Ins",
        "Delete": "Del",
        "Num0": "Num0",
        "Num1": "Num1",
        "Num2": "Num2",
        "Num3": "Num3",
        "Num4": "Num4",
        "Num5": "Num5",
        "Num6": "Num6",
        "Num7": "Num7",
        "Num8": "Num8",
        "Num9": "Num9",
        "Num*": "Num*",
        "Num+": "Num+",
        "Num-": "Num-",
        "Num/": "Num/",
        "Num.": "Num.",
        "Numenter": "NumEnter",
        "Decimal": "Decimal",
        "Win": "Win",
        "Command": "Win",
    }

    @staticmethod
    def map_openai_key(key: str) -> str:
        if key in KeyMapper.special_keys:
            return KeyMapper.special_keys[key]

        if key.capitalize() in KeyMapper.special_keys:
            return KeyMapper.special_keys[key.capitalize()]

        if key == "CMD":
            return KeyMapper.special_keys["Command"]

        return KeyMapper.special_keys.get(key, key)
