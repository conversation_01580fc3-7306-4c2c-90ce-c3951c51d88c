import re


class Error(Exception):
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(self.message)


def clean_text(text: str):
    """Clean text by removing leading and trailing whitespace and newlines."""
    text = remove_whitespace(text)
    return text


def remove_whitespace(text: str) -> str:
    """Remove leading and trailing whitespace."""
    return re.sub(r"^\s+|^\n+$|\s+$|\n+$", "", text)
