import os
import uuid

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.testing import get_testing_request_context


def set_test_request_context():
    if settings.USE_LLM_GATEWAY:
        # get from connections.yaml
        tenant_id = os.environ.get("TENANT_ID", "")
        localization = "en"
        token = settings.UIPATH_TOKEN
        request_context = get_testing_request_context(localization, tenant_id, os.environ.get("ORG_ID", ""), token=token)
        request_context.ui_task_session_id = str(uuid.uuid4())
        request_utils.set_request_context(request_context)
