from typing import List, Literal

from pydantic import BaseModel, Field


class ClickAction(BaseModel):
    name: str
    description: str
    element_id: int


class TypeIntoAction(BaseModel):
    name: str
    description: str
    element_id: int
    variable: str
    value: str


class GetTextAction(BaseModel):
    name: str
    description: str
    element_id: int


class SelectAction(BaseModel):
    name: str
    element_id: int
    description: str
    variable: str
    value: str


class NavigateBackAction(BaseModel):
    name: str
    description: str


class WaitAction(BaseModel):
    name: str
    description: str


class ExtractInfoAction(BaseModel):
    name: str
    description: str
    prompt: str
    label: str


class ScrollAction(BaseModel):
    name: str
    description: str
    element_id: int
    direction: Literal["up, down, left, right"]


class FinishAction(BaseModel):
    name: str
    description: str
    status: bool
    result: str


class StepResponse(BaseModel):
    """Response from the agent after executing an action"""

    observation: str = Field(..., description="Observation of the current screen and analysis of the executed steps (1 line)")
    reasoning: str = Field(..., description="What should be done next and why (1 line)")
    description: str
    actions: List[
        ClickAction | TypeIntoAction | GetTextAction | SelectAction | NavigateBackAction | WaitAction | ExtractInfoAction | ScrollAction | FinishAction
    ]


class CompilerResponse(BaseModel):
    task_result: dict = Field(..., description="Result of the task execution")


class ExtractionResponse(BaseModel):
    """Response from the agent after extracting information"""

    description: str = Field(..., description="Short and concise overall description of the data (couple of words)")
    data: dict = Field(..., description="Data extracted from the screen as key-value pairs")


class ReflectionResponse(BaseModel):
    analysis: str | None
    candidates_solutions: List[str] | None
    final_solution: str | None
