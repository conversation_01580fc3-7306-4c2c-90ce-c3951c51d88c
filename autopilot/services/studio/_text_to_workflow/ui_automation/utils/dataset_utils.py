import base64
import glob
import json
import os
import re

import cv2
import numpy as np
from uipath_cv_client.client import CvClient
from uipath_cv_client.cv_dom_extractor import CVDomExtractor
from uipath_cv_client.cv_post_processing import Io<PERSON>
from uipath_cv_client.dom_processing import DomSourceType, get_dom_tree_from_elements, parse_raw_dom

from services.studio._text_to_workflow.ui_automation.action import Action, Step


def parse_recorded_action(recorded_action):
    action = recorded_action["Action"]
    element_id = recorded_action["ElementId"]
    value = recorded_action.get("TypeIntoText")
    variable = recorded_action.get("Variable", "")

    if value.startswith("$"):
        variable = value
        default_value = ""
    else:
        default_value = value

    return Action.from_request_dict(
        {"name": action, "description": "", "method_type": action, "variable": variable, "element_id": element_id, "default_value": default_value},
        is_ui_agent=False,
    )


def parse_dataset(dir_folder, options, use_cv_cache=True, tasks=None, is_ui_agent=False):
    task_runs = [f for f in os.listdir(dir_folder) if not os.path.isfile(os.path.join(dir_folder, f))]
    out = {}
    for folder in task_runs:
        if isinstance(tasks, list) and len(tasks) > 0 and folder not in tasks:
            continue
        task_folder = os.path.join(dir_folder, folder)
        task_data = parse_data(task_folder, options, use_cv_cache, is_ui_agent=is_ui_agent)

        out[folder] = task_data

    assert len(out) > 0, "No tasks found"
    return out


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def parse_data(task_folder, options, use_cv_cache=True, is_ui_agent=False):
    task_data = {"data": [], "expected_steps": []}

    with open(glob.glob(os.path.join(task_folder, "*metadata*.json"))[0]) as f:
        metadata = json.load(f)
        task = metadata["UserPrompt"]
        task_data["task"] = task
        task_data["output_variables"] = metadata.get("output_variables", {})
        task_data["input_variables"] = metadata.get("input_variables", {})

    step_files = glob.glob(os.path.join(task_folder, "*step*.json"))
    step_files = [f for f in step_files if "cv_data.json" not in f and "actions.json" not in f and "unused" not in f]
    step_files = [(int(re.findall(r"_step_\d+", step_file)[-1].split("_step_")[-1]), step_file) for step_file in step_files]
    step_files = sorted(step_files, key=lambda x: x[0])

    for _, step_file in step_files:
        step = {}
        step["task"] = task
        with open(step_file, encoding="utf-8") as f:
            step_json = json.load(f)

        with open(step_file[:-5] + "_actions.json", encoding="utf-8") as f:
            actions_json = json.load(f)

        image = None
        image_base64 = step_json.get("ImageBase64")
        if image_base64 is not None:
            image_base64 = step_json["ImageBase64"]
            image_blob = base64.b64decode(image_base64)
            image_np = np.fromstring(image_blob, np.uint8)
            image = cv2.imdecode(image_np, cv2.IMREAD_UNCHANGED)

        raw_dom = get_dom_tree_from_elements(
            step_json["ExtractedDOM"]["RawDOM"]["RawElements"],
            step_json.get("ApplicationArea"),
        )

        if options.get("CV_Only", False) and (image is not None):
            use_cv_cache = False

            all_dom_gt_nodes = []
            gt_ids = [item["ElementId"] for item in actions_json]

            def get_raw_dom_nodes(current_node, filter_func=None):
                out = []
                if filter_func is None or filter_func(current_node):
                    out = [current_node]
                children_nodes = current_node.get("Children", [])
                if children_nodes is None:
                    return out
                for node in children_nodes:
                    out.extend(get_raw_dom_nodes(node, filter_func))
                return out

            all_dom_gt_nodes = get_raw_dom_nodes(raw_dom, lambda x: x["Id"] in gt_ids)  # noqa

            if len(all_dom_gt_nodes) > 0:
                dom_extractor = CVDomExtractor(CvClient.from_legacy_opt(options))
                raw_dom = dom_extractor.extract_visual_dom_from_image(image)
                all_nodes = get_raw_dom_nodes(
                    raw_dom,
                    lambda x: x["ElementType"] != "Icon" or (x["Text"] is not None and x["Text"] != ""),
                )
                all_nodes_bboxes = np.asarray([list(map(int, node["AbsoluteRegion"].split(","))) for node in all_nodes])
                all_nodes_bboxes[:, 2:] = all_nodes_bboxes[:, :2] + all_nodes_bboxes[:, 2:] - 1

                all_dom_gt_nodes_bboxes = np.asarray([list(map(int, node["AbsoluteRegion"].split(","))) for node in all_dom_gt_nodes])
                all_dom_gt_nodes_bboxes[:, 2:] = all_dom_gt_nodes_bboxes[:, :2] + all_dom_gt_nodes_bboxes[:, 2:] - 1

                iou_val = IoU(all_dom_gt_nodes_bboxes, all_nodes_bboxes)
                matched_indices = np.argmax(iou_val, axis=1)
                for index in range(len(all_dom_gt_nodes_bboxes)):
                    if iou_val[index, matched_indices[index]] == 0 or ("Id" not in all_nodes[matched_indices[index]]):
                        print("Couldn't find a match")
                        break
                    print("Match found")
                    actions_json[index]["ElementId"] = all_nodes[matched_indices[index]]["Id"]

            dom = parse_raw_dom(
                raw_dom,
                dom_type=DomSourceType.CV,
                options=options,
                image=image,
                file_path=step_file,
                use_cv_cache=use_cv_cache,
            )

        else:
            dom = parse_raw_dom(
                raw_dom,
                dom_type=DomSourceType.Driver,
                options=options,
                image=image,
                file_path=step_file,
                use_cv_cache=use_cv_cache,
            )

        url = step_json["ExtractedDOM"]["Url"]
        title = step_json["ExtractedDOM"]["Title"]
        step_data = {
            "dom": dom,
            "url": url,
            "title": title,
            "image": image,
            "image_base64": image_base64,
            "raw_dom": raw_dom,
        }
        task_data["data"].append(step_data)
        recorded_actions = actions_json

        # "ScreenActions":[{"Action":"type_into","TypeIntoText":"UIPATH","ElementId":190}]}
        actions = []
        for recorded_action in recorded_actions:
            action = parse_recorded_action(recorded_action)
            action.assign_action_target(dom)
            actions.append(action)

        expected_step = Step(description="", actions=actions, screen_info={"url": url, "title": title})
        task_data["expected_steps"].append(expected_step)

    return task_data


if __name__ == "__main__":
    from services.studio._text_to_workflow.ui_automation.options import opt

    dataset_folder = r"/workspace/code/studio/text_to_workflow/ui_automation/dataset"
    parse_dataset(dataset_folder, opt)
