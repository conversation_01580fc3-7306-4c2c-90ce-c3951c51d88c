import copy
import json
import os
import shutil
import traceback
from typing import Optional

import requests

from services.studio._text_to_workflow.ui_automation.utils import messages_utils

# log the request and response data for debugging
ENABLE_LOG = os.getenv("AGENT_ACT_LOG_TASK", "false").lower() in ("true", "1")
LOG_DIR: str = os.getenv("AGENT_ACT_LOG_DIR", "")

if ENABLE_LOG:
    assert LOG_DIR != "", "AGENT_ACT_LOG_DIR env not defined"

# for live log streaming
ENABLE_STREAM_LOG = os.getenv("AGENT_ACT_STREAM_LOG", "false").lower() in ("true", "1")
STREAM_LOG_URL = "http://127.0.0.1:8000"


def reset_stream_log():
    requests.post(f"{STREAM_LOG_URL}/reset")


def send_stream_log(payload):
    requests.post(f"{STREAM_LOG_URL}/add_log", json=payload)


def stream_log(request, response, predict_info, endpoint="act"):
    def extract_thought(text: str) -> str:
        start_str = "1. Screen observation and reasoning:"
        index = text.find("2. Step")
        if index == -1:
            index = text.find("```json")
        # If triple backticks are found, return the substring before them
        if index != -1:
            thought = text[len(start_str) : index]
            thought = thought.strip()
            thought = thought.split("\n")
            thought = "\n" + "\n".join([t for t in thought if t.strip()])
            return thought
        else:
            # If not found, return the entire string
            return ""

    def short_step_str(response):
        s = response["step"]["description"] + ":\n"
        s += "".join(" - " + action["description"] + "\n" for action in response["step"]["actions"])
        if "result" in response["step"]["actions"][-1]:
            s += "\nResult:\n"
            s += str(response["step"]["actions"][-1]["result"])
        return s

    try:
        if endpoint == "act":
            log_payload = {
                "model_thought": extract_thought(predict_info["assistant_message"]),
                "model_actions": short_step_str(response),
                "screenshot": request["image"],
            }
        else:
            log_payload = {"model_thought": "Extracted data:", "model_actions": json.dumps(response, indent=2), "screenshot": request["image"]}
        send_stream_log(log_payload)
    except Exception as e:
        print("Error in streaming log:", e)


def log_task(request, response, predict_info, status_code, task_id: Optional[str], run_id: str, endpoint: str = "act"):
    """
    For task execuation debugging
    This also log the LLM requests with prompt made during each request, using predict_info
    task_id: task_id defined in uiact_config metadata (optional), useful to identify the task being run
       like Apple--1 in WebVoyager benchmark
    run_id: the id created by uiact unique for each execution
    """
    task_name = run_id if task_id is None or len(task_id) == 0 else task_id
    log_folder = os.path.join(LOG_DIR, task_name)
    trace_filepath = os.path.join(log_folder, f"{task_name}__trace.json")

    if endpoint == "act" and len(request["previousSteps"]) == 0:
        # first request
        shutil.rmtree(log_folder, ignore_errors=True)
        os.makedirs(log_folder, exist_ok=True)

    try:
        data = {"image": request["image"], "response": response}
        if status_code == 200:
            # uia agent internal llm requests
            if "requests" in predict_info:
                requests_info = [copy.deepcopy(r) for r in predict_info["requests"]]
                for r in requests_info:
                    if "messages" in r:
                        # get the input message from the messages list
                        # reviewed observations do not have messages saved in predict_info
                        r["prompt"] = messages_utils.get_input_message(r["messages"])
                        r.pop("messages")  # not serializable
                data["requests"] = requests_info

            if endpoint == "act" and len(request["previousSteps"]) == 0:
                steps = [data]
            else:
                with open(trace_filepath, "r", encoding="utf-8") as f:
                    steps = json.load(f)
                    steps.append(data)
            with open(trace_filepath, "w", encoding="utf-8") as f:
                json.dump(steps, f, indent=2)
        else:
            # exception
            if os.path.exists(trace_filepath):
                with open(trace_filepath, "r", encoding="utf-8") as f:
                    steps = json.load(f)
            else:
                steps = []
            steps.append(data)
            with open(trace_filepath, "w", encoding="utf-8") as f:
                json.dump(steps, f, indent=2)

            # append error message
            log_error_path = os.path.join(log_folder, f"{task_name}__errors.txt")
            error_message = response["error"]

            with open(log_error_path, "a", encoding="utf-8") as f:
                f.write(error_message)
                f.write("\n\n")

        request_filepath = os.path.join(log_folder, f"request_{len(steps)}_{str(status_code)}.json")
        with open(request_filepath, "w", encoding="utf-8") as f:
            json.dump({"request": request, "response": response}, f, indent=2)

    except Exception:
        print("TASK LOG ERROR")
        print(traceback.format_exc())
