"""Service for UIA Agent / Wingman local debugging"""

# os.environ["AGENT_ACT_WAIT"] = "True"
# os.environ["AGENT_ACT_LOG_DIR"] = r"C:\Users\<USER>\work\wingman\UIA\requests"
# os.environ["AGENT_ACT_LOG_TASK"] = "True"

import logging
import os
import time
import traceback
from datetime import datetime
from typing import Any, Callable, Coroutine

import dotenv
import uvicorn
import vertexai
from fastapi import FastAPI, Header, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from typing_extensions import override

from services.studio._text_to_workflow.api.deps import get_context_from_request
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.ui_automation import ui_automation_endpoint
from services.studio._text_to_workflow.ui_automation.models.ui_agent.advice_model import AdviceModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.master_slave_agent import UIMasterSlaveAgent
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.options import opt as ui_agent_options
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uitars_agent import UITARSAgent
from services.studio._text_to_workflow.ui_automation.utils import log_utils
from services.studio._text_to_workflow.utils.request_utils import set_request_context

dotenv.load_dotenv()
console_logger.setLevel(logging.DEBUG)


write_error_to_file = False
if write_error_to_file:
    error_handler = logging.FileHandler("errors.log")
    error_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    error_handler.setFormatter(error_formatter)
    error_handler.setLevel(logging.ERROR)
    console_logger.addHandler(error_handler)

CallNext = Callable[[Request], Coroutine[Any, Any, Response]]

# Excluded urls
excludelist_paths = ["/alive", "/health", "/health-details", "/swagger", "/openapi.json", "/", "/learn-from-experience"]

if not settings.USE_LLM_GATEWAY:
    os.environ["GOOGLE_CLOUD_PROJECT"] = "devtest-autopilot"
    # GOOGLE_APPLICATION_CREDENTIALS = path to gcp_credentials.json
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.environ["GOOGLE_APPLICATION_CREDENTIALS"]
    vertexai.init(project="devtest-autopilot")

ui_automation_endpoint.init()
uitars_agent = UITARSAgent(ui_agent_options, add_image_to_response=True)
ui_path_master_slave_agent = UIMasterSlaveAgent(ui_agent_options)
PORT = int(os.getenv("WINGMAN_PORT", 8080))
app = FastAPI()


def get_task_id(request_body):
    task_id = None
    if request_body.get("metadata"):
        task_id = request_body["metadata"].get("taskId")
    return task_id


# Middleware to automatically set the request context
class RequestContextMiddleware(BaseHTTPMiddleware):
    """Custom middleware that modifies the behavior before a request is handled."""

    @override
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # set request context before dispatching
        request_path = request.url.path.rstrip("/")
        if request_path not in excludelist_paths:
            context = get_context_from_request(request)
            # context.tenant_id = os.environ["TENANT_ID"]
            set_request_context(context)
        return await call_next(request)


# Middleware for setting the request context
app.add_middleware(RequestContextMiddleware)


@app.post("/agent-act-on-screen")
async def agent_act_on_screen(request: Request):
    request_body = await request.json()
    if log_utils.ENABLE_STREAM_LOG:
        # reset stream log if new task
        if len(request_body["previousSteps"]) == 0:
            log_utils.reset_stream_log()

    if os.environ.get("INCLUDE_ADDITIONAL_TRACE_INFO"):
        if "metadata" not in request_body:
            request_body["metadata"] = {}
        if type(request_body["metadata"]) is dict:
            request_body["metadata"]["include_additional_trace_info"] = str(os.environ.get("INCLUDE_ADDITIONAL_TRACE_INFO")).lower() in ["true", "1", "yes"]

    try:
        start = time.time()
        model_name = request_body.get("model_name", None)
        if model_name == "uitars":
            response = await uitars_agent.predict_request(request_body)
            predict_info = {}
        elif model_name == "uipath-master-slave":
            response = await ui_path_master_slave_agent.predict_request(request_body)
            predict_info = {}
        else:
            response, predict_info = await ui_automation_endpoint.agent_act_on_screen(request_body)
        response_content = response
        status_code = 200
        step_response = {key: value for key, value in response["step"].items() if key != "image"}
        console_logger.debug("act response: %s", step_response)
        console_logger.debug("elapsed time: %s", time.time() - start)

        if log_utils.ENABLE_STREAM_LOG:
            log_utils.stream_log(request_body, response, predict_info, endpoint="act")
    except Exception:
        console_logger.error("SERVICE ERROR")
        predict_info = None
        trace_message = str(datetime.now()) + "\n" + traceback.format_exc()
        console_logger.error(trace_message)
        response_content = {"error": trace_message}
        status_code = 500

    if log_utils.ENABLE_LOG:
        run_id = request.headers["x-uipath-wingman-prompt-id"]
        task_id = get_task_id(request_body)
        log_utils.log_task(request_body, response_content, predict_info, status_code, task_id=task_id, run_id=run_id, endpoint="act")

    return JSONResponse(content=response_content, status_code=status_code)


@app.post("/agent-screen-extract")
async def agent_extract(
    request: Request,
):
    request_body = await request.json()
    try:
        response, predict_info = await ui_automation_endpoint.extract(request_body)
        console_logger.debug("extract response: %s", response)
        response_content = response
        status_code = 200

        if log_utils.ENABLE_STREAM_LOG:
            log_utils.stream_log(request_body, response, predict_info, endpoint="extract")
    except Exception:
        console_logger.error("SERVICE ERROR")
        trace_message = str(datetime.now()) + "\n" + traceback.format_exc()
        console_logger.error(trace_message)
        response_content = {"error": trace_message}
        status_code = 500
        predict_info = None

    if log_utils.ENABLE_LOG:
        run_id = request.headers["x-uipath-wingman-prompt-id"]
        task_id = get_task_id(request_body)
        log_utils.log_task(request_body, response_content, predict_info, status_code, task_id=task_id, run_id=run_id, endpoint="extract")

    return JSONResponse(content=response_content, status_code=status_code)


@app.post("/close-popup")
async def close_popup(request: Request):
    request_body = await request.json()
    response = await ui_automation_endpoint.close_popup(request_body)
    return JSONResponse(content=response, status_code=200)


@app.post("/qa-screen")
async def qa_screen(
    request: Request,
    x_uipath_semantic_duplicates_matching: bool = Header(default=False, description="Enable semantic duplicates matching for QA screen requests."),
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.qa_screen(request_body, {"x-uipath-semantic-duplicates-matching": x_uipath_semantic_duplicates_matching})
    return JSONResponse(content=response, status_code=200)


@app.post("/qa-dom")
async def qa_dom(request: Request):
    request_body = await request.json()
    response = await ui_automation_endpoint.qa_dom(request_body)
    return JSONResponse(content=response, status_code=200)


@app.post("/get-element-description")
async def get_element_description(request: Request):
    request_body = await request.json()
    response = await ui_automation_endpoint.get_element_description(request_body)
    return JSONResponse(content=response, status_code=200)


@app.post("/learn-from-experience")
async def learn_from_experience(request: Request):
    """Generate advice based on execution trace data."""

    request_body = await request.json()
    try:
        model = AdviceModel()
        advice = await model.predict(trace_data=request_body)
        response_content = {"advice": advice}
        status_code = 200

    except Exception:
        console_logger.error("SERVICE ERROR")
        trace_message = str(datetime.now()) + "\n" + traceback.format_exc()
        console_logger.error(trace_message)
        response_content = {"error": trace_message}
        status_code = 500

    if log_utils.ENABLE_LOG:
        run_id = request.headers["x-uipath-wingman-prompt-id"]
        task_id = get_task_id(request_body)
        log_utils.log_task(request_body, response_content, None, status_code, task_id=task_id, run_id=run_id, endpoint="learn")

    return JSONResponse(content=response_content, status_code=status_code)


if __name__ == "__main__":
    # set test request context in case using LLM Gateway
    uvicorn.run(app, host="0.0.0.0", port=PORT)
