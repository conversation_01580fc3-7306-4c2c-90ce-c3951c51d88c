{"children": [{"name": "NetSuite App", "description": null, "taxonomyType": null, "type": "App", "reference": "aNzOp9-eak-7XcQhq0tkVw/jlr_YPziNke_TMWSR_wOGg", "children": [{"name": "<PERSON>", "description": null, "taxonomyType": null, "type": "Screen", "reference": "aNzOp9-eak-7XcQhq0tkVw/hqgml0oIIk2JzdIiPSmcWw", "children": [{"name": "Vendor Name", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/bPG3PFk6Jk2JURp_yfCKig", "children": []}]}, {"name": "New Vendor", "description": null, "taxonomyType": null, "type": "Screen", "reference": "aNzOp9-eak-7XcQhq0tkVw/UrSGhkSdvEa3CrytC0WKww", "children": [{"name": "Company Name", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/Jzk_PLKcIkuJ3DlhuvylVQ", "children": []}, {"name": "Email", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/_tWOBWvLsUqKUJnov_9Tmg", "children": []}, {"name": "Phone", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/R1dMg1tx6keaL7pfe6J0Gw", "children": []}, {"name": "Save", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/ubI-6FqXyUSxFPNeRy55uQ", "children": []}, {"name": "Web Address", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/ocjGVYOGgESGuhPloW2P9A", "children": []}]}, {"name": "V<PERSON>or Created Confirmation", "description": null, "taxonomyType": null, "type": "Screen", "reference": "aNzOp9-eak-7XcQhq0tkVw/-VN3eVFedU-bYOKHvICYig", "children": [{"name": "Confirmation", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/c81hI3TrZ0WQgMiWY-g_3w", "children": []}]}, {"name": "Vendor Search", "description": null, "taxonomyType": null, "type": "Screen", "reference": "aNzOp9-eak-7XcQhq0tkVw/ARQm5Pt6-0uONw7ss7ES3Q", "children": [{"name": "Company Name", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/PSUKJhFx_0Whwvjh3zMFuQ", "children": []}, {"name": "Submit", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "aNzOp9-eak-7XcQhq0tkVw/LAwSn1QBh0653nDxFZ4vTA", "children": []}]}]}]}