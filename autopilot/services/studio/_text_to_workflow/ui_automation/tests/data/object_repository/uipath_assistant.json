{"children": [{"name": "UiPathAssistant", "description": null, "taxonomyType": null, "type": "App", "reference": "KZd2kw_XeEyluA_OFeuYeg/w-VnwlIet0qyDdqdorE2sw", "children": [{"name": "LibTFMainScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/orpI7APcPkKB9fDohJBTyQ", "children": [{"name": "ConnectionTypeSelectItem", "description": null, "taxonomyType": "Dropdown", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/n549FGK5BkWJrYZ5oAjyTA", "children": []}, {"name": "Con<PERSON>ue<PERSON><PERSON><PERSON>", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/KjD84HIAVUSQKd0HkNYQ1A", "children": []}, {"name": "OrchestratorSettingsTab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/qaxWggRGiUasXQww3zsn5Q", "children": []}, {"name": "Pick<PERSON>enantTitle", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/CWJv2NeExkmSJ_ei3f8SvA", "children": []}, {"name": "PreferencesMenuButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/dTa0y6aQpEiMuESiJ6aI6A", "children": []}, {"name": "PreferencesMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/qRu_PMG42Ue35swnjI7PRQ", "children": []}, {"name": "RobotNameInput", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bw0DcEoizEiUTmHo7DHFrA", "children": []}, {"name": "ServiceUrlInput", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/xQ_9dYDYKEeDZZphIjhAhQ", "children": []}, {"name": "SignInButton", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/-m54fzO130-ScjznndIQLw", "children": []}, {"name": "TenantRadioButton", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/NB1XXwhx7E2O9VRA2K8ykg", "children": []}]}, {"name": "LibTFPreferencesScreen", "description": null, "taxonomyType": "Screen", "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/Ybu_lhUZLUeB72Cta0heGA", "children": [{"name": "ConnectionTypeSelectItem", "description": null, "taxonomyType": "Dropdown", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/fafmqEmQS02_I7gdGiL5TQ", "children": []}, {"name": "OrchestratorSettingsTab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/haXMs-NkQUmmuteyM8QDgg", "children": []}, {"name": "ServiceUrlInput", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ox7oFBbkyUSWAGuL_8RxLg", "children": []}, {"name": "SignInButton", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/xUGP2yCDwESd1q41IyDUPw", "children": []}]}]}, {"name": "UiPathStudio", "description": null, "taxonomyType": null, "type": "App", "reference": "KZd2kw_XeEyluA_OFeuYeg/jMe_RoKnCUKf_g07g2waPg", "children": [{"name": "LibOpenDialogScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/qtP9SWzVaEyNje_brg3QWQ", "children": [{"name": "FileInput", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/y4Z4w0QlhkS1efY30Q4qMQ", "children": []}, {"name": "Open", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/t3pQpRgh-k-uH77dUi9F6g", "children": []}]}, {"name": "LibTFActivitiesTabScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/U3Y_k6eCbkedkd1ua7UXBg", "children": [{"name": "ActivitiesTab", "description": null, "taxonomyType": "Tab", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/IKLMr3-Ne0-pQ_4rEJzy-Q", "children": []}, {"name": "ActivitySearchBox", "description": null, "taxonomyType": "Text", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Vk8uAkS36kS4lqFMpiq5pA", "children": []}, {"name": "FilterMenuLabelByType", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Q0rSk0_kCki_M554ynWsKw", "children": []}, {"name": "GenericActivityTreeViewItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/_QJxdZyvKEyTou5l4DEocg", "children": []}, {"name": "GenericButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/x3EN25b43USZTzKPe2oHdw", "children": []}, {"name": "GenericFilterMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/K34OLfIaYk-PYD_Bm1yLWg", "children": []}]}, {"name": "LibTFBreakpointsAndBookmarksScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/WE7sVOfODEeeEzuxCFOUHg", "children": [{"name": "BookmarkLine", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/KZ1uInLy9UubzEqIkrn9Fw", "children": []}, {"name": "BookmarkLineEnabled", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Ni5hC_3TGUqYlLpawbyFIg", "children": []}, {"name": "BookmarksRootNode", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/F8DESUZgXUejUXFkY2-o6w", "children": []}, {"name": "BookmarksRootNodeCheckBox", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/k1RHQks9O0eH-ulYM24PIA", "children": []}, {"name": "BreakpointLine", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/5_2xz5cX9U2uKNYDLUZbrA", "children": []}, {"name": "BreakpointLineEnabled", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/UqCOAy7XsEK-ZXiAWrqjTg", "children": []}, {"name": "BreakpointsAndBookmarksRootNode", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/YdOU4Z5vl0qJw9hzUBUfQg", "children": []}, {"name": "BreakpointsAndBookmarksTab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/1D_K0QOaVEqOKCwtnIUm_A", "children": []}, {"name": "BreakpointsAndBookmarksToolbar", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/JNTpjDv7O0uFsbc1Th-A_A", "children": []}, {"name": "BreakpointsRootNode", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/k_YjgemjnkeUFX-eZw8q4g", "children": []}, {"name": "BreakpointsRootNodeCheckBox", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/j8BFMG1Ej0mIr0S6V2sXbQ", "children": []}, {"name": "DisableAllBookmarks", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/_Vvdc5W7xUmd3bCBMIJ-5g", "children": []}, {"name": "DisableAllBreakpoints", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/lgFeBqLGjEqSCyKMgxCTTQ", "children": []}, {"name": "DisableLocationCtxMenuBtn", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/PNF7-idqHEqJP9LDXffMDg", "children": []}, {"name": "EnableAllBookmarks", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/7fA-SQs0Fk6WhZUMgyWNXw", "children": []}, {"name": "EnableAllBreakpoints", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/A7KQmHhn-UalKmwndWIHVg", "children": []}, {"name": "EnableLocationCtxMenuBtn", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/h4CCHXZxDEaO7Ja2RjD18Q", "children": []}, {"name": "FocusLocationCtxMenuBtn", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/lw6k--6g70S1h_Btd1xn3Q", "children": []}, {"name": "NavigateNextBookmark", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/TgVpeF-t3kaNJyjYqj5r8Q", "children": []}, {"name": "NavigatePreviousBookmark", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6G3phrZvA0OJ-U1zUrDrtw", "children": []}, {"name": "RemoveAllBookmarks", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ZKrsIrJawEuDw0VUzzOYpw", "children": []}, {"name": "RemoveAllBookmarksConfirmBtn", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/x2uU5-puPkeSb6abzK7UFw", "children": []}, {"name": "RemoveAllBreakpoints", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/J5wVoceASEqdoDyEQ_Trcw", "children": []}, {"name": "RemoveAllBreakpointsConfirmBtn", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/r2NaJT3HLkuniq3yZIYQbQ", "children": []}, {"name": "Settings", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/kEFOFreR2ESug29Zgn7Uag", "children": []}, {"name": "SettingsDialog", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/g1NU-jZmY0WEZkHAgtDgHg", "children": []}, {"name": "SettingsDialogCancel", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Pt36OqcNc0-X3iosMf_0Bg", "children": []}, {"name": "SettingsDialogCondition", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/wqU-uxfwDEy44ih6Nakt2Q", "children": []}, {"name": "SettingsDialogHitCount", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/W2zTqqbST0O3SkRn0sDV0w", "children": []}, {"name": "SettingsDialogLogMessage", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/F3bqbJZLgE-qJizmnAl9KA", "children": []}, {"name": "SettingsDialogOk", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6YmqxJ1qRkiE-Axic53Y7g", "children": []}]}, {"name": "LibTFConfirmationDialogScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/hsxMrUD9MkCIlrGsIBYSAQ", "children": [{"name": "GenericButton", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/p7Ziqbnik0Giv_15U-rEyA", "children": []}, {"name": "No", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/0C5XXWvhik-G83NRmMTkAA", "children": []}, {"name": "Ok", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/l57BziOLY0KD3FR-eOZ9_A", "children": []}]}, {"name": "LibTFDataManagerScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/To5EPZ-vukKz7G9aDj9GTQ", "children": [{"name": "ArgumentsSectionMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/hSlE5W1qzEqAQaiauD1DHw", "children": []}, {"name": "CollapseAllButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/a4U0Wfsks0SMd_NzXR8Luw", "children": []}, {"name": "ConstantSectionMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/mt0DhEfprkGIoWdG2R7Y1Q", "children": []}, {"name": "DataManagerFiltersButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/SYpXUNeIrEe2aINinzUiGQ", "children": []}, {"name": "DataManagerSection", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/31WICisKiEuCilKutG3Xjw", "children": []}, {"name": "DataManagerTab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/QzMVCalUn0aGFJ4gMbpTZA", "children": []}, {"name": "ExpandAllButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/SZYnobJq40u4dfTBZeY3iQ", "children": []}, {"name": "ExpandSectionWithVariableButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/0ysKZB7ToUWRhse9wLM6_A", "children": []}, {"name": "FilterOption", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/zdoh_nu7Bkyj5ICkSd_Z2A", "children": []}, {"name": "GroupItemsByScopeButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/wZ7nVR2eRkKBoyh5HLK_Bg", "children": []}, {"name": "NewArgumentButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/LQg04PHA80mK1So69vQhCA", "children": []}, {"name": "NewConstantButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Pn8IyUO8Y0Wh2VzTh8MWVw", "children": []}, {"name": "NewToolbarButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/szIK-ybyS06IJYcrnSHweg", "children": []}, {"name": "NewToolbarButtonMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/jPc5U3HgX0qjsh7FO9vXJA", "children": []}, {"name": "NewVariableButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/k81-kM55eky8L8r_uunEww", "children": []}, {"name": "RefreshDataManagerButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/qugcYt0TekCi4HY8djeNbw", "children": []}, {"name": "SortMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Bu4Qo0Vpz0asfkSF7o86ag", "children": []}, {"name": "SortSectionsButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/z_DDwbTrXE-4GxcxtY_KjQ", "children": []}, {"name": "SyncWithActiveScopeButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/45zipWVipUKuybyk0poAMQ", "children": []}, {"name": "VariableSectionMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/OMk9qppCmku3bDdsvANalQ", "children": []}]}, {"name": "LibTFDebugRibbon", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/cT65oNtITE6ax-2n3l8pzA", "children": [{"name": "Break", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ciss8alkYUuhm9vmZjXjfg", "children": []}, {"name": "Breakpoints", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/nLQ_6w-zl0eeEoScc13nZw", "children": []}, {"name": "Breakpoints Drop Down", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6UKzHp7ZBU-Ji8fmKLk4sA", "children": [{"name": "ShowBreakpointsPanel", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/2dwD6s99DkKaQoC6NVwpUQ", "children": []}]}, {"name": "Continue", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/-y8jZ4CMnUCq-CGseGyNqQ", "children": []}, {"name": "DebugFile", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/utxH7UEI2UiBAk_6nQyWyQ", "children": []}, {"name": "DebugFile Drop Down", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/qwdXQFapBk2DOnYXcW8Ztg", "children": [{"name": "Debug", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bQDJwFqdckqdFTnOSxU6fA", "children": []}, {"name": "Run", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/9My_0FyanE6InXGLA_1iYg", "children": []}, {"name": "RunFile", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/SzBwsaTno0iaKInrEGBg7w", "children": []}]}, {"name": "Focus", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/fWGTynxWxUefHIPNoo6yvQ", "children": []}, {"name": "<PERSON><PERSON>", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/7viG8KP_1EOu4UVyFkrjkA", "children": []}, {"name": "StepInto", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/aZ1DG6utJ0uEpACFYP2t6A", "children": []}, {"name": "StepOut", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/9Q8jmONCKUm1GhOkkI6CSg", "children": []}, {"name": "StepOver", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/r-IW8JnTc0OaEy5FAuf-rw", "children": []}, {"name": "Stop", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bnErU9WCDky7OHzh3ro8Sg", "children": []}]}, {"name": "LibTFDesigner", "description": null, "taxonomyType": "Screen", "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/2uXpx3lcNUGbEDz6nDxl-A", "children": [{"name": "ActivityInSearch", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/FDG4V6zadkSHboT2vwVuYA", "children": []}, {"name": "ActivityOnWorkflow", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/u6-FICGPJESVOoDe7FVssg", "children": []}, {"name": "ActivityOptions", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/QHg22MNVnUyTjck7C4Uivg", "children": []}, {"name": "ActivityPartListItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/whdxhLAdEEelXlXf8C6K-w", "children": []}, {"name": "ActivityProperty", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/wm66gQfXa0a49T2TJhfumw", "children": []}, {"name": "ActivityPropertyDropDownList", "description": null, "taxonomyType": "Listbox", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/lXP6hrhL_Uydjfce8Jj6MA", "children": []}, {"name": "ActivityPropertyMenu", "description": null, "taxonomyType": "Label", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Qgj7LG_j1kC7-tTP_aUsRA", "children": []}, {"name": "ActivityTitle", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/AIghSroKJUCnTl7XKD_7CQ", "children": []}, {"name": "AddActivityButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/5zxpIMiKjkaZ5G9cKWdtog", "children": []}, {"name": "CloseTabButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/vZySLKVOdEenDMd4opEG7Q", "children": []}, {"name": "ContextMenuItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/BOzv5jEhVEGb6EVxA-_Ctw", "children": []}, {"name": "DebugOutputPane", "description": null, "taxonomyType": "Listbox", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/VOLJwhKvGUi4THPCwpQH0A", "children": []}, {"name": "MainArea", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/YvgNGU-G8UmNke36zLVYyQ", "children": []}, {"name": "SearchForActivityInput", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/DUdh1hBOz02M1_4CbvO9hg", "children": []}, {"name": "Tab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/qQzVjM6ztkWX4eRm37_Xlw", "children": []}]}, {"name": "LibTFDesignRibbon", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/ucl5LpHq0kW7ZBO2z8OYSA", "children": [{"name": "Analyze Drop Down", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6h-4aL7ggUqxo7Mare5DKA", "children": []}, {"name": "Analyze File", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/m8AQtO-weU2CyxkZSfxpbA", "children": []}, {"name": "Analyze Project", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/_zi30KKwf0OlLSvMxmPTOg", "children": []}, {"name": "Debug File", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/OMJ3P7ke6kuz1cmgafnhhw", "children": []}, {"name": "ManagePackages", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/S_6N9VdsqU-cPnqk5oCU_Q", "children": []}, {"name": "New", "description": "The new element from the ribbon", "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/UqtDz-Mf9kiqpgdDbKagIw", "children": [{"name": "ItemSelector", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/zBa85Q-GR0K0Gfky5IshGQ", "children": []}]}, {"name": "NewItemSelector", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bgjYLjTvYEq9Wt5OmysCdg", "children": []}, {"name": "Publish", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/CdD_s28xGU6yHP1WH8pNKA", "children": []}, {"name": "Publish_Drop_Down", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ItfHyRc660eyDe6e8xFhQA", "children": []}, {"name": "PublishTestCases", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Mbfxv3_6eU662AhgnKSWTg", "children": []}, {"name": "Redo", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/fvHp9gAL2UqDnII86OM1fw", "children": []}, {"name": "Save", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/uw1QtIztpEqDyW4mwtemZg", "children": []}, {"name": "Stop", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/hPytC5WX9kiKz7x07SG34Q", "children": []}, {"name": "Undo", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/T47pnefBgU-TbLprtmlocw", "children": []}, {"name": "Validate File", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/lWsb5SCN9kituY0zbmhUuA", "children": []}, {"name": "Validate Project", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ks5NrspmnUKhmmQQxoAfEA", "children": []}]}, {"name": "LibTFErrorsPanel", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/cFsG5YgoZkynT_zBnV7BkA", "children": [{"name": "Clear All", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ec7_r_DO906W8bx_DwPlhw", "children": []}, {"name": "ErrorItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/1d0ULvXG1023NUuizDoM1g", "children": []}, {"name": "ErrorsTab", "description": null, "taxonomyType": "Tab", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ixB55zEku0Gi-GuC9xH5Ww", "children": []}, {"name": "LevelToggle", "description": "use button variable: Error, Warning, Messasge", "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/28lxFl8KrEqL3WAXbGyi8Q", "children": []}, {"name": "Rules List", "description": null, "taxonomyType": "Combobox", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ECXifd-ga0i-maAd8VUKlA", "children": []}, {"name": "Scope List", "description": null, "taxonomyType": "Combobox", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/j0RCru7Pn0i4TCdt_YP37g", "children": []}]}, {"name": "LibTFGenericCreateItemScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/jDaGBJgvl0Gn_t7iplxvIQ", "children": [{"name": "CreateItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/iokpOoSV1Umkb4xxmnTjqg", "children": []}, {"name": "ItemLocation", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bUR3bkncoUujKMbcI2I6kg", "children": []}, {"name": "ItemName", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/35JlOPZhrEqcfKCtEgCg6w", "children": []}]}, {"name": "LibTFGenericDialog", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/nreH-0YDUEaP6V_YPEe6Xw", "children": [{"name": "ProgressDialog", "description": null, "taxonomyType": "None", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6qEqzp1kyEqeJVpi0x25vg", "children": []}]}, {"name": "LibTFGenericDialogScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/TcyY0aeCMkCGyJBejoznug", "children": [{"name": "CloseXButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/uTKLzEfE_Eyx6Fy6HNFxfg", "children": []}, {"name": "CopyToClipboardButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/9xLlyOe2V0uO7wIhwRGcGw", "children": []}, {"name": "DialogTitleLabel", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/HSdJ8AxpnUqoQXr7XXgN9Q", "children": []}, {"name": "MessageBox", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/1ZiW0-sWm0qdK9kFA5cyZw", "children": []}, {"name": "NoButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/fat5MIZ5bEqjQX2UaVqeYQ", "children": []}, {"name": "OkButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/8rRvpQkvS0u-IEvrbqUXtA", "children": []}, {"name": "YesButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/A6D3AfdVYEmdl5ssklsbgw", "children": []}]}, {"name": "LibTFHomeScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/P6pbgJ368U6SMmyc1Bv-5Q", "children": [{"name": "BackButton", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/y_WH0BVIJkuPy_3uwTvZYg", "children": []}, {"name": "CloseProject", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/7CrJ_l7qaUKb1JencHHrzA", "children": []}, {"name": "NewLibrary", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/x1cqr2Rjx0qvB5e8G_g6Yw", "children": []}, {"name": "NewProcess", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/kxfHoR3kxEy3vcm61HiCDA", "children": []}, {"name": "NewTemplate", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/B4V-jGZ-i0aN-B22G12BXg", "children": []}, {"name": "NewTestAutomation", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Ofw969O6i0-IiR26MTAiyA", "children": []}, {"name": "OpenLocalProject", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/eTHcbVOvBUeaziZPo49I4Q", "children": []}, {"name": "OpenRecentProject", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/ixoWEoltN0OBLHgYxYVx9Q", "children": []}, {"name": "SelectableHomeSection", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/Bxble9nRIUOht0BoQ8fhtA", "children": []}, {"name": "SettingsSubsection", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/DFnOJkjp3EKLhsm7NF7PKA", "children": []}, {"name": "ToolsSection", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/WPcwuK6vdEGRhH6cBxFXYg", "children": []}, {"name": "ToolsSubsection", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6whwOsSb80mzK2lZ_CEtUA", "children": []}]}, {"name": "LibTFHomeScreenInProject", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/zX31pM9mF0C2d3butIDZsQ", "children": [{"name": "Close", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/iYp1vs9REkapU9zG8puCBw", "children": []}]}, {"name": "LibTFLoadingOverlay", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/3GxkEvag1U-G1rT0wRdtcA", "children": [{"name": "LoadingOverlay", "description": null, "taxonomyType": "Loader", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/GdaGMqBWRUixl4uPCc4pww", "children": []}]}, {"name": "LibTFMainScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/TnrRJIIyuk6Gsu4VRDx4wQ", "children": [{"name": "Debug", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/J4hLHPkH0k6pSFI-qihc4Q", "children": []}, {"name": "Design", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/G6evdend20i1MaV63PgKNQ", "children": []}, {"name": "Home", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/cFDdCu9MQk25pTVLQfuD0w", "children": []}]}, {"name": "LibTFNewBlankProjectScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/zfFvH6eGMkudWOHWVCFCoA", "children": [{"name": "AdvancedOptionsExpander", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/pf-olm8G00ubtihQZ1Wbsw", "children": []}, {"name": "CreateNewProject", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/c0gTNNUI5EWYFx_HS5WwXA", "children": []}, {"name": "CSharpLanguage", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/2-eVaMTzXkiLSEnb0yRMqw", "children": []}, {"name": "ExpressionLanguage", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/7lRb2MwaUEW6zn0-oS6Y0g", "children": []}, {"name": "ProjectDescription", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/L6qUzoZETU6OVNOF7gXmVg", "children": []}, {"name": "ProjectLocation", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/O9G8h4qViUeKUtsy9bGKLQ", "children": []}, {"name": "ProjectName", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/TR1M8dxiykSE5bGFmqwk1A", "children": []}, {"name": "TargetFramework", "description": null, "taxonomyType": "Dropdown", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/oJ8-lW0MeUC4whMVxWAffQ", "children": []}, {"name": "VBLanguage", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/2mfKu2kV2kKEqD3ocCL6bA", "children": []}]}, {"name": "LibTFOutputScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/GuccJJvU8UOEVomM0WOsBg", "children": [{"name": "Clear", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/hreHjovRUUqQ7ysXcKYICw", "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/TZl_BOsQq0KJKCNoWiWWLA", "children": []}, {"name": "FailedAssertionFilter", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/uNOTWcnc9k208v5llg3BwQ", "children": []}, {"name": "InfoFilter", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/xl0Mh4vFm0e7TP0PJGc4JA", "children": []}, {"name": "Output", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/56BsXTQ6-kCN69qfDNPeVw", "children": []}, {"name": "OutputPanel", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/t7yjXR4gDUqd-kBBGJEtaA", "children": []}, {"name": "PassedAs<PERSON><PERSON><PERSON><PERSON>er", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/XTvOcphmJ02qrawWfMiemw", "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/9SErO9IA30qwlpFRbUrU-Q", "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/lgZjydM2gkKsf7GnsUc6oA", "children": []}]}, {"name": "LibTFProjectExplorerScreen", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/IsPgsQoOfUGqg1eUXMz4_w", "children": [{"name": "DeleteButton", "description": null, "taxonomyType": "RowName", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/3aal8WM3KEuM7pMKIcDQog", "children": []}, {"name": "DeletingStatus", "description": null, "taxonomyType": "ModalWindow", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/mZmXXxY3WkC29Nb84u5D9w", "children": []}, {"name": "ExpandProjectExplorerDebug", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/rAoGRyUyfEWjQuZ0UBamXQ", "children": []}, {"name": "FilterButton", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/SKk1i3zpiE-iBefHdCs6BQ", "children": []}, {"name": "FilterItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/xhEWNZbYkUKIMi6W3mP1rQ", "children": []}, {"name": "GenericButton", "description": null, "taxonomyType": "<PERSON><PERSON>", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/d80kUKmfHUWIoi5LMcpUoA", "children": []}, {"name": "ItemContextMenuButton", "description": null, "taxonomyType": "RowName", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/GFldnfDhH0Ss8g77P9AQtA", "children": []}, {"name": "ProjectItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/B5OVGoKceU21D7OWCDNe3w", "children": []}, {"name": "ProjectName", "description": null, "taxonomyType": "Label", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/N8egNAXCsUOUPGrOJzt_FQ", "children": []}, {"name": "ProjectTabItem", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/bmFoe0SQ7UuGKbU35uksGQ", "children": []}, {"name": "Refresh", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/7t9kXhi-J0Ks_bz3BlomHw", "children": []}, {"name": "Search", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/l8mCIvry2keTReaF2he6-Q", "children": []}]}, {"name": "LibTFProperties", "description": null, "taxonomyType": "Screen", "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/RGVqeOdXrkW26H9N5fkb3Q", "children": [{"name": "OutputProperty", "description": null, "taxonomyType": "Label", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/BHNf5uezS0KbOSbCgK2VWg", "children": []}, {"name": "OutputPropertyInput", "description": null, "taxonomyType": "Label", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/iMIP4ynxfUqRGU-kHI0R2w", "children": []}, {"name": "PropertiesPageTab", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/6lbj2piza0m-r7BMirkpTA", "children": []}, {"name": "SetVarNameInput", "description": null, "taxonomyType": "Input", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/WVPJn6FvT0GyUDa0x2r0IQ", "children": []}, {"name": "ValidationErrorText", "description": null, "taxonomyType": "Label", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/0r1bMch-oUy--3GjVryQaQ", "children": []}]}, {"name": "LibTFSettings", "description": null, "taxonomyType": null, "type": "Screen", "reference": "KZd2kw_XeEyluA_OFeuYeg/mGIdjKrA_UqYTQb6raMz9g", "children": [{"name": "ToggleSwitch", "description": null, "taxonomyType": "Element", "type": "Element", "reference": "KZd2kw_XeEyluA_OFeuYeg/E9I_jrTZ702HWOGSk6YXOQ", "children": []}]}]}]}