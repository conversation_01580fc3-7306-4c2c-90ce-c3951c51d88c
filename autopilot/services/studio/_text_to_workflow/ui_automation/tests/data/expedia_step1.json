{"SiteTitle": "Expedia Travel: Vacation Homes, Hotels, Car Rentals, Flights & More", "SiteURL": "https://www.expedia.com/", "Action": "click", "ElementId": 133, "RawDOM": {"Children": [{"Children": [{"Children": [{"Children": [{"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "200, 1491, 500, 281", "ClientRegion": "200, 1363, 500, 281", "Id": 58, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Go further with the Expedia app", "Href": null, "ElementType": "Text", "AbsoluteRegion": "730, 1521, 698, 45", "ClientRegion": "730, 1393, 698, 45", "Id": 72, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Save even more - get up to 20% on select hotels when you book on the app. Our app deals help you to save on trips so you can travel more and manage it all on the go.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "730, 1566, 698, 55", "ClientRegion": "730, 1438, 698, 55", "Id": 73, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Scan the QR code with your device camera and download our app", "Href": null, "ElementType": "Text", "AbsoluteRegion": "730, 1621, 698, 95", "ClientRegion": "730, 1493, 698, 95", "Id": 74, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "730, 1521, 698, 195", "ClientRegion": "730, 1393, 698, 195", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "QR code", "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1477, 1521, 193, 193", "ClientRegion": "1477, 1393, 193, 193", "Id": 154, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "700, 1491, 1000, 255", "ClientRegion": "700, 1363, 1000, 255", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 1491, 1500, 281", "ClientRegion": "200, 1363, 1500, 281", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "2023 EXPEDIA AIR TRAVEL HACKS", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 2413, 480, 25", "ClientRegion": "200, 2285, 480, 25", "Id": 180, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Flight tips to save you money and get you there faster", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 2438, 480, 50", "ClientRegion": "200, 2310, 480, 50", "Id": 181, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2398, 480, 90", "ClientRegion": "200, 2270, 480, 90", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "200, 2078, 480, 320", "ClientRegion": "200, 1950, 480, 320", "Id": 196, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2078, 480, 410", "ClientRegion": "200, 1950, 480, 410", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 2078, 480, 410", "ClientRegion": "200, 1950, 480, 410", "Id": 156, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2078, 480, 410", "ClientRegion": "200, 1950, 480, 410", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "MADE TO TRAVEL", "Href": null, "ElementType": "Text", "AbsoluteRegion": "710, 2413, 480, 25", "ClientRegion": "710, 2285, 480, 25", "Id": 183, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Inspiration and deals for your next journey", "Href": null, "ElementType": "Text", "AbsoluteRegion": "710, 2438, 480, 25", "ClientRegion": "710, 2310, 480, 25", "Id": 184, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "710, 2398, 480, 65", "ClientRegion": "710, 2270, 480, 65", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "710, 2078, 480, 320", "ClientRegion": "710, 1950, 480, 320", "Id": 197, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "710, 2078, 480, 385", "ClientRegion": "710, 1950, 480, 385", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "710, 2078, 480, 385", "ClientRegion": "710, 1950, 480, 385", "Id": 158, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "710, 2078, 480, 385", "ClientRegion": "710, 1950, 480, 385", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "VACATION PACKAGE DEALS", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1220, 2413, 480, 25", "ClientRegion": "1220, 2285, 480, 25", "Id": 186, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Taking it easy is even easier with flight + hotel packages", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1220, 2438, 480, 50", "ClientRegion": "1220, 2310, 480, 50", "Id": 187, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1220, 2398, 480, 90", "ClientRegion": "1220, 2270, 480, 90", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1220, 2078, 480, 320", "ClientRegion": "1220, 1950, 480, 320", "Id": 198, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1220, 2078, 480, 410", "ClientRegion": "1220, 1950, 480, 410", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1220, 2078, 480, 410", "ClientRegion": "1220, 1950, 480, 410", "Id": 160, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1220, 2078, 480, 410", "ClientRegion": "1220, 1950, 480, 410", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "185, 2078, 1530, 410", "ClientRegion": "185, 1950, 1530, 410", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "VIP ACCESS STAYS", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 2977, 735, 25", "ClientRegion": "200, 2849, 735, 25", "Id": 189, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Experience a stellar stay at a Member Price", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 3002, 735, 25", "ClientRegion": "200, 2874, 735, 25", "Id": 190, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2962, 735, 65", "ClientRegion": "200, 2834, 735, 65", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "200, 2548, 735, 413", "ClientRegion": "200, 2420, 735, 413", "Id": 199, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2548, 735, 478", "ClientRegion": "200, 2420, 735, 478", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 2548, 735, 478", "ClientRegion": "200, 2420, 735, 478", "Id": 162, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 2548, 735, 478", "ClientRegion": "200, 2420, 735, 478", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "BUCKET LIST TRIPS", "Href": null, "ElementType": "Text", "AbsoluteRegion": "965, 2977, 735, 25", "ClientRegion": "965, 2849, 735, 25", "Id": 192, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Today is the day. Plan your dream trip and save.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "965, 3002, 735, 25", "ClientRegion": "965, 2874, 735, 25", "Id": 193, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "965, 2962, 735, 65", "ClientRegion": "965, 2834, 735, 65", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "965, 2548, 735, 413", "ClientRegion": "965, 2420, 735, 413", "Id": 200, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "965, 2548, 735, 478", "ClientRegion": "965, 2420, 735, 478", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "965, 2548, 735, 478", "ClientRegion": "965, 2420, 735, 478", "Id": 164, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "965, 2548, 735, 478", "ClientRegion": "965, 2420, 735, 478", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "185, 2548, 1530, 478", "ClientRegion": "185, 2420, 1530, 478", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Travel Sale activities deals", "Href": null, "ElementType": "Icon", "AbsoluteRegion": "200, 788, 1500, 643", "ClientRegion": "200, 660, 1500, 643", "Id": 70, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Go from no plans to a weekend away and save 10% or more on over 100,000 hotels as a member.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "260, 1099, 460, 50", "ClientRegion": "260, 971, 460, 50", "Id": 152, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Your downtime just got better", "Href": null, "ElementType": "Text", "AbsoluteRegion": "260, 974, 460, 110", "ClientRegion": "260, 846, 460, 110", "Id": 167, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "260, 974, 1380, 175", "ClientRegion": "260, 846, 1380, 175", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "See Member Prices", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "260, 1164, 217, 60", "ClientRegion": "260, 1036, 217, 60", "Id": 153, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 788, 1500, 643", "ClientRegion": "200, 660, 1500, 643", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 788, 1500, 643", "ClientRegion": "200, 660, 1500, 643", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Explore a world of travel with Expedia", "Href": null, "ElementType": "Text", "AbsoluteRegion": "797, 3132, 304, 23", "ClientRegion": "797, 3004, 304, 23", "Id": 168, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Discover new places and experiences", "Href": null, "ElementType": "Text", "AbsoluteRegion": "797, 3154, 304, 23", "ClientRegion": "797, 3026, 304, 23", "Id": 169, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "797, 3132, 304, 45", "ClientRegion": "797, 3004, 304, 45", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "170, 758, 1560, 2464", "ClientRegion": "170, 630, 1560, 2464", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Stays", "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "648, 356, 82, 55", "ClientRegion": "648, 228, 82, 55", "Id": 101, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Flights", "Href": null, "ElementType": "Text", "AbsoluteRegion": "750, 372, 53, 23", "ClientRegion": "750, 244, 53, 23", "Id": 146, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "730, 356, 94, 55", "ClientRegion": "730, 228, 94, 55", "Id": 133, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Cars", "Href": null, "ElementType": "Text", "AbsoluteRegion": "844, 372, 35, 23", "ClientRegion": "844, 244, 35, 23", "Id": 147, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "824, 356, 75, 55", "ClientRegion": "824, 228, 75, 55", "Id": 134, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Packages", "Href": null, "ElementType": "Text", "AbsoluteRegion": "918, 372, 74, 23", "ClientRegion": "918, 244, 74, 23", "Id": 148, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "898, 356, 114, 55", "ClientRegion": "898, 228, 114, 55", "Id": 135, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Things to do", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1033, 372, 100, 23", "ClientRegion": "1033, 244, 100, 23", "Id": 149, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1013, 356, 140, 55", "ClientRegion": "1013, 228, 140, 55", "Id": 136, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Cruises", "Href": null, "ElementType": "InputBox", "AbsoluteRegion": "1153, 356, 98, 55", "ClientRegion": "1153, 228, 98, 55", "Id": 106, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "230, 356, 1440, 55", "ClientRegion": "230, 228, 1440, 55", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Add a flight", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "292, 571, 185, 23", "ClientRegion": "292, 443, 185, 23", "Id": 202, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Add a car", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "525, 571, 185, 23", "ClientRegion": "525, 443, 185, 23", "Id": 203, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Check-in", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1128, 491, 61, 20", "ClientRegion": "1128, 363, 61, 20", "Id": 214, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": "Check-in", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1128, 491, 61, 20", "ClientRegion": "1128, 363, 61, 20", "Id": 208, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Jul 3", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1073, 486, 276, 60", "ClientRegion": "1073, 358, 276, 60", "Id": 209, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1088, 501, 30, 30", "ClientRegion": "1088, 373, 30, 30", "Id": 210, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1073, 486, 276, 60", "ClientRegion": "1073, 358, 276, 60", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Check-out", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1419, 491, 72, 20", "ClientRegion": "1419, 363, 72, 20", "Id": 215, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": "Check-out", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1419, 491, 72, 20", "ClientRegion": "1419, 363, 72, 20", "Id": 211, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Jul 4", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1364, 486, 276, 60", "ClientRegion": "1364, 358, 276, 60", "Id": 212, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1379, 501, 30, 30", "ClientRegion": "1379, 373, 30, 30", "Id": 213, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1364, 486, 276, 60", "ClientRegion": "1364, 358, 276, 60", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1073, 486, 566, 60", "ClientRegion": "1073, 358, 566, 60", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "260, 486, 799, 60", "ClientRegion": "260, 358, 799, 60", "Id": 217, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Going to", "Href": null, "ElementType": "Text", "AbsoluteRegion": "315, 503, 81, 25", "ClientRegion": "315, 375, 81, 25", "Id": 218, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "275, 501, 30, 30", "ClientRegion": "275, 373, 30, 30", "Id": 219, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "260, 486, 799, 60", "ClientRegion": "260, 358, 799, 60", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "260, 486, 1380, 118", "ClientRegion": "260, 358, 1380, 118", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Search", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "841, 618, 218, 60", "ClientRegion": "841, 490, 218, 60", "Id": 178, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "1 room, 2 travelers", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1469, 434, 142, 24", "ClientRegion": "1469, 306, 142, 24", "Id": 204, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1617, 436, 23, 23", "ClientRegion": "1617, 308, 23, 23", "Id": 205, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1469, 426, 170, 45", "ClientRegion": "1469, 298, 170, 45", "Id": 201, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "260, 426, 1380, 283", "ClientRegion": "260, 298, 1380, 283", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "230, 356, 1440, 353", "ClientRegion": "230, 228, 1440, 353", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 281, 1899, 2941", "ClientRegion": "0, 153, 1899, 2941", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Company", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 3314, 314, 33", "ClientRegion": "200, 3186, 314, 33", "Id": 38, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "About", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3367, 314, 20", "ClientRegion": "200, 3239, 314, 20", "Id": 108, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Jobs", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3407, 314, 20", "ClientRegion": "200, 3279, 314, 20", "Id": 109, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "List your property", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3447, 314, 20", "ClientRegion": "200, 3319, 314, 20", "Id": 110, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Partnerships", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3487, 314, 20", "ClientRegion": "200, 3359, 314, 20", "Id": 111, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Investor Relations", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3527, 314, 20", "ClientRegion": "200, 3399, 314, 20", "Id": 112, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Advertising", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3567, 314, 20", "ClientRegion": "200, 3439, 314, 20", "Id": 113, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Affiliate Marketing", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3607, 314, 20", "ClientRegion": "200, 3479, 314, 20", "Id": 114, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 3357, 314, 270", "ClientRegion": "200, 3229, 314, 270", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 3314, 314, 313", "ClientRegion": "200, 3186, 314, 313", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Explore", "Href": null, "ElementType": "Text", "AbsoluteRegion": "524, 3314, 441, 33", "ClientRegion": "524, 3186, 441, 33", "Id": 40, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "United States of America travel guide", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3367, 441, 20", "ClientRegion": "524, 3239, 441, 20", "Id": 115, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Hotels in United States of America", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3407, 441, 20", "ClientRegion": "524, 3279, 441, 20", "Id": 116, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Vacation rentals in United States of America", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3447, 441, 20", "ClientRegion": "524, 3319, 441, 20", "Id": 117, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Vacation packages in United States of America", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3487, 441, 20", "ClientRegion": "524, 3359, 441, 20", "Id": 118, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Domestic flights", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3527, 441, 20", "ClientRegion": "524, 3399, 441, 20", "Id": 119, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Car rentals in United States of America", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3567, 441, 20", "ClientRegion": "524, 3439, 441, 20", "Id": 120, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "All accommodation types", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "524, 3607, 441, 20", "ClientRegion": "524, 3479, 441, 20", "Id": 121, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "524, 3357, 441, 270", "ClientRegion": "524, 3229, 441, 270", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "524, 3314, 441, 313", "ClientRegion": "524, 3186, 441, 313", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Policies", "Href": null, "ElementType": "Text", "AbsoluteRegion": "975, 3314, 306, 33", "ClientRegion": "975, 3186, 306, 33", "Id": 42, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Privacy", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "975, 3367, 306, 20", "ClientRegion": "975, 3239, 306, 20", "Id": 122, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Terms of use", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "975, 3407, 306, 20", "ClientRegion": "975, 3279, 306, 20", "Id": 123, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Vrbo terms and conditions", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "975, 3447, 306, 20", "ClientRegion": "975, 3319, 306, 20", "Id": 124, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Accessibility", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "975, 3487, 306, 20", "ClientRegion": "975, 3359, 306, 20", "Id": 125, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Your Privacy Choices", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "975, 3527, 306, 20", "ClientRegion": "975, 3399, 306, 20", "Id": 126, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "975, 3357, 306, 190", "ClientRegion": "975, 3229, 306, 190", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "975, 3314, 306, 233", "ClientRegion": "975, 3186, 306, 233", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Help", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1291, 3314, 428, 33", "ClientRegion": "1291, 3186, 428, 33", "Id": 44, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Support", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3367, 428, 20", "ClientRegion": "1291, 3239, 428, 20", "Id": 127, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Cancel your hotel or vacation rental booking", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3407, 428, 20", "ClientRegion": "1291, 3279, 428, 20", "Id": 128, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Cancel your flight", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3447, 428, 20", "ClientRegion": "1291, 3319, 428, 20", "Id": 129, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Refund timelines, policies & processes", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3487, 428, 20", "ClientRegion": "1291, 3359, 428, 20", "Id": 130, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Use an Expedia coupon", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3527, 428, 20", "ClientRegion": "1291, 3399, 428, 20", "Id": 131, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "International travel documents", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1291, 3567, 428, 20", "ClientRegion": "1291, 3439, 428, 20", "Id": 132, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1291, 3357, 428, 230", "ClientRegion": "1291, 3229, 428, 230", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1291, 3314, 428, 273", "ClientRegion": "1291, 3186, 428, 273", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 3277, 188, 28", "ClientRegion": "200, 3149, 188, 28", "Id": 60, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 3267, 1520, 370", "ClientRegion": "200, 3139, 1520, 370", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Expedia, Inc. is not responsible for content on external Web sites.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 3698, 441, 20", "ClientRegion": "200, 3570, 441, 20", "Id": 46, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "© 2023 Expedia, Inc., an Expedia Group company. All rights reserved. Expedia and the Airplane Logo are trademarks or registered trademarks of Expedia, Inc. CST# 2029030-50.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "200, 3718, 1200, 20", "ClientRegion": "200, 3590, 1200, 20", "Id": 47, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "200, 3698, 1520, 40", "ClientRegion": "200, 3570, 1520, 40", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "170, 3237, 1560, 561", "ClientRegion": "170, 3109, 1560, 561", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 281, 1899, 3532", "ClientRegion": "0, 153, 1899, 3532", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "200, 208, 173, 55", "ClientRegion": "200, 80, 173, 55", "Id": 18, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "929, 226, 20, 20", "ClientRegion": "929, 98, 20, 20", "Id": 63, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Get the app", "Href": null, "ElementType": "Text", "AbsoluteRegion": "954, 224, 97, 23", "ClientRegion": "954, 96, 97, 23", "Id": 64, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "913, 216, 154, 40", "ClientRegion": "913, 88, 154, 40", "Id": 49, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1087, 224, 23, 23", "ClientRegion": "1087, 96, 23, 23", "Id": 65, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "English", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1120, 224, 58, 23", "ClientRegion": "1120, 96, 58, 23", "Id": 66, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1087, 224, 90, 23", "ClientRegion": "1087, 96, 90, 23", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1067, 213, 130, 45", "ClientRegion": "1067, 85, 130, 45", "Id": 28, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "List your property", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1217, 224, 144, 23", "ClientRegion": "1217, 96, 144, 23", "Id": 51, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1197, 213, 184, 45", "ClientRegion": "1197, 85, 184, 45", "Id": 29, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Support", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1401, 224, 66, 23", "ClientRegion": "1401, 96, 66, 23", "Id": 52, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1381, 213, 106, 45", "ClientRegion": "1381, 85, 106, 45", "Id": 30, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Trips", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1507, 224, 38, 23", "ClientRegion": "1507, 96, 38, 23", "Id": 53, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1487, 213, 78, 45", "ClientRegion": "1487, 85, 78, 45", "Id": 31, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1580, 221, 30, 30", "ClientRegion": "1580, 93, 30, 30", "Id": 100, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1565, 206, 60, 60", "ClientRegion": "1565, 78, 60, 60", "Id": 54, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Sign in", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1645, 224, 55, 23", "ClientRegion": "1645, 96, 55, 23", "Id": 68, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1625, 206, 95, 60", "ClientRegion": "1625, 78, 95, 60", "Id": 55, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "913, 206, 806, 60", "ClientRegion": "913, 78, 806, 60", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "More travel", "Href": null, "ElementType": "Text", "AbsoluteRegion": "397, 224, 93, 23", "ClientRegion": "397, 96, 93, 23", "Id": 61, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "500, 224, 23, 23", "ClientRegion": "500, 96, 23, 23", "Id": 62, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "377, 206, 165, 60", "ClientRegion": "377, 78, 165, 60", "Id": 48, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "170, 206, 1560, 60", "ClientRegion": "170, 78, 1560, 60", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 191, 1899, 3622", "ClientRegion": "0, 63, 1899, 3622", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Welcome to Expedia.com. ", "Href": null, "ElementType": "Text", "AbsoluteRegion": "20, 147, 211, 23", "ClientRegion": "20, 19, 211, 23", "Id": 7, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Continue to the Vietnam site at ", "Href": null, "ElementType": "Text", "AbsoluteRegion": "231, 147, 255, 23", "ClientRegion": "231, 19, 255, 23", "Id": 9, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Expedia.com.vn", "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "486, 148, 125, 23", "ClientRegion": "486, 20, 125, 23", "Id": 10, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "231, 147, 384, 23", "ClientRegion": "231, 19, 384, 23", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "0, 128, 1899, 3685", "ClientRegion": "0, 0, 1899, 3685", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Skip to main content", "Href": null, "ElementType": "Text", "AbsoluteRegion": "44, 219, 62, 70", "ClientRegion": "44, 91, 62, 70", "Id": 221, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "14, 204, 60, 100", "ClientRegion": "14, 76, 60, 100", "Id": 220, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1773, 972, 23, 23", "ClientRegion": "1773, 844, 23, 23", "Id": 225, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Help", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1805, 971, 44, 25", "ClientRegion": "1805, 843, 44, 25", "Id": 226, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "1753, 961, 116, 45", "ClientRegion": "1753, 833, 116, 45", "Id": 224, "ImageBase64": null}], "Attributes": [], "IsRoot": true, "Text": null, "Href": null, "ElementType": "None", "AbsoluteRegion": "0, 0, 0, 0", "ClientRegion": "0, 0, 0, 0", "Id": 0, "ImageBase64": null}, "ParsedDOM": "<Window><Container><Container><Container><Container><Container><Container><Icon Id=\"58\" /><Container><Container><Text Id=\"72\" Text=\"Go further with the Expedia app\" /><Text Id=\"73\" Text=\"Save even more - get up to 20% on select hotels when you book on the app. Our app deals help you to save on trips so you can travel more and manage it all on the go.\" /><Text Id=\"74\" Text=\"Scan the QR code with your device camera and download our app\" /></Container><Icon Id=\"154\" Text=\"QR code\" /></Container></Container><Container><Container><Container><Container><Text Id=\"180\" Text=\"2023 EXPEDIA AIR TRAVEL HACKS\" /><Text Id=\"181\" Text=\"Flight tips to save you money and get you there faster\" /></Container><Icon Id=\"196\" /></Container><Button Id=\"156\" /></Container><Container><Container><Container><Text Id=\"183\" Text=\"MADE TO TRAVEL\" /><Text Id=\"184\" Text=\"Inspiration and deals for your next journey\" /></Container><Icon Id=\"197\" /></Container><Button Id=\"158\" /></Container><Container><Container><Container><Text Id=\"186\" Text=\"VACATION PACKAGE DEALS\" /><Text Id=\"187\" Text=\"Taking it easy is even easier with flight + hotel packages\" /></Container><Icon Id=\"198\" /></Container><Button Id=\"160\" /></Container></Container><Container><Container><Container><Container><Text Id=\"189\" Text=\"VIP ACCESS STAYS\" /><Text Id=\"190\" Text=\"Experience a stellar stay at a Member Price\" /></Container><Icon Id=\"199\" /></Container><Button Id=\"162\" /></Container><Container><Container><Container><Text Id=\"192\" Text=\"BUCKET LIST TRIPS\" /><Text Id=\"193\" Text=\"Today is the day. Plan your dream trip and save.\" /></Container><Icon Id=\"200\" /></Container><Button Id=\"164\" /></Container></Container><Container><Icon Id=\"70\" Text=\"Travel Sale activities deals\" /><Container><Container><Text Id=\"152\" Text=\"Go from no plans to a weekend away and save 10% or more on over 100,000 hotels as a member.\" /><Text Id=\"167\" Text=\"Your downtime just got better\" /></Container><Button Id=\"153\" Text=\"See Member Prices\" /></Container></Container><Container><Text Id=\"168\" Text=\"Explore a world of travel with Expedia\" /><Text Id=\"169\" Text=\"Discover new places and experiences\" /></Container></Container><Container><Container><InputBox Id=\"101\" Text=\"Stays\" /><Button Id=\"133\"><Text Id=\"146\" Text=\"Flights\" /></Button><Button Id=\"134\"><Text Id=\"147\" Text=\"Cars\" /></Button><Button Id=\"135\"><Text Id=\"148\" Text=\"Packages\" /></Button><Button Id=\"136\"><Text Id=\"149\" Text=\"Things to do\" /></Button><InputBox Id=\"106\" Text=\"Cruises\" /></Container><Container><Container><CheckBox Id=\"202\" Text=\"Add a flight\" /><CheckBox Id=\"203\" Text=\"Add a car\" /><Container><Container><Text Id=\"208\" Text=\"Check-in\"><Text Id=\"214\" Text=\"Check-in\" /></Text><Button Id=\"209\" Text=\"Jul 3\" /><Icon Id=\"210\" /></Container><Container><Text Id=\"211\" Text=\"Check-out\"><Text Id=\"215\" Text=\"Check-out\" /></Text><Button Id=\"212\" Text=\"Jul 4\" /><Icon Id=\"213\" /></Container></Container><Container><Button Id=\"217\" /><Text Id=\"218\" Text=\"Going to\" /><Icon Id=\"219\" /></Container></Container><Button Id=\"178\" Text=\"Search\" /><Button Id=\"201\"><Text Id=\"204\" Text=\"1 room, 2 travelers\" /><Icon Id=\"205\" /></Button></Container></Container></Container><Container><Container><Container><Text Id=\"38\" Text=\"Company\" /><Container><Button Id=\"108\" Text=\"About\" /><Button Id=\"109\" Text=\"Jobs\" /><Button Id=\"110\" Text=\"List your property\" /><Button Id=\"111\" Text=\"Partnerships\" /><Button Id=\"112\" Text=\"Investor Relations\" /><Button Id=\"113\" Text=\"Advertising\" /><Button Id=\"114\" Text=\"Affiliate Marketing\" /></Container></Container><Container><Text Id=\"40\" Text=\"Explore\" /><Container><Button Id=\"115\" Text=\"United States of America travel guide\" /><Button Id=\"116\" Text=\"Hotels in United States of America\" /><Button Id=\"117\" Text=\"Vacation rentals in United States of America\" /><Button Id=\"118\" Text=\"Vacation packages in United States of America\" /><Button Id=\"119\" Text=\"Domestic flights\" /><Button Id=\"120\" Text=\"Car rentals in United States of America\" /><Button Id=\"121\" Text=\"All accommodation types\" /></Container></Container><Container><Text Id=\"42\" Text=\"Policies\" /><Container><Button Id=\"122\" Text=\"Privacy\" /><Button Id=\"123\" Text=\"Terms of use\" /><Button Id=\"124\" Text=\"Vrbo terms and conditions\" /><Button Id=\"125\" Text=\"Accessibility\" /><Button Id=\"126\" Text=\"Your Privacy Choices\" /></Container></Container><Container><Text Id=\"44\" Text=\"Help\" /><Container><Button Id=\"127\" Text=\"Support\" /><Button Id=\"128\" Text=\"Cancel your hotel or vacation rental booking\" /><Button Id=\"129\" Text=\"Cancel your flight\" /><Button Id=\"130\" Text=\"Refund timelines, policies &amp; processes\" /><Button Id=\"131\" Text=\"Use an Expedia coupon\" /><Button Id=\"132\" Text=\"International travel documents\" /></Container></Container><Button Id=\"60\" /></Container><Container><Text Id=\"46\" Text=\"Expedia, Inc. is not responsible for content on external Web sites.\" /><Text Id=\"47\" Text=\"© 2023 Expedia, Inc., an Expedia Group company. All rights reserved. Expedia and the Airplane Logo are trademarks or registered trademarks of Expedia, Inc. CST# 2029030-50.\" /></Container></Container></Container><Container><Button Id=\"18\" /><Container><Button Id=\"49\"><Icon Id=\"63\" /><Text Id=\"64\" Text=\"Get the app\" /></Button><Button Id=\"28\"><Container><Icon Id=\"65\" /><Text Id=\"66\" Text=\"English\" /></Container></Button><Button Id=\"29\"><Text Id=\"51\" Text=\"List your property\" /></Button><Button Id=\"30\"><Text Id=\"52\" Text=\"Support\" /></Button><Button Id=\"31\"><Text Id=\"53\" Text=\"Trips\" /></Button><Button Id=\"54\"><Icon Id=\"100\" /></Button><Button Id=\"55\"><Text Id=\"68\" Text=\"Sign in\" /></Button></Container><Button Id=\"48\"><Text Id=\"61\" Text=\"More travel\" /><Icon Id=\"62\" /></Button></Container></Container><Text Id=\"7\" Text=\"Welcome to Expedia.com. \" /><Container><Text Id=\"9\" Text=\"Continue to the Vietnam site at \" /><Button Id=\"10\" Text=\"Expedia.com.vn\" /></Container></Container><Button Id=\"220\"><Text Id=\"221\" Text=\"Skip to main content\" /></Button><Button Id=\"224\"><Icon Id=\"225\" /><Text Id=\"226\" Text=\"Help\" /></Button></Window>", "TypeIntoText": null}