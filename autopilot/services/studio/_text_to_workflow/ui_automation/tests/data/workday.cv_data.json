{"controls": [[[1213, 596, 66, 53], [1218, 668, 54, 63], [1214, 516, 63, 62], [1843, 11, 41, 41], [1792, 5, 43, 40], [1217, 445, 56, 54], [685, 5, 536, 54], [1739, 5, 42, 40], [15, 20, 29, 24], [354, 470, 50, 48], [705, 18, 25, 26], [360, 772, 65, 52], [1902, 918, 17, 20], [343, 566, 22, 20], [1214, 773, 23, 22], [1098, 390, 28, 20], [1900, 0, 20, 19], [129, 10, 40, 39], [1114, 116, 106, 104], [1110, 109, 117, 115], [136, 2, 520, 59], [1093, 385, 39, 28], [1178, 857, 421, 80], [312, 64, 106, 80], [1181, 857, 415, 79], [0, 0, 117, 64], [297, 355, 856, 267], [1216, 657, 370, 85], [1187, 369, 407, 452], [344, 567, 20, 18], [1217, 544, 28, 30], [124, 7, 118, 49], [1112, 115, 109, 108], [329, 728, 796, 140], [1586, 866, 9, 70], [1800, 29, 40, 89], [289, 254, 878, 102], [1567, 859, 31, 77], [364, 566, 763, 29], [690, 449, 296, 27], [949, 728, 160, 89], [1509, 653, 79, 21], [333, 877, 712, 59], [1179, 601, 406, 68], [1447, 639, 143, 28], [1435, 671, 158, 26], [664, 399, 356, 26], [1188, 855, 395, 80], [1521, 599, 66, 52], [1497, 645, 87, 19], [681, 573, 184, 34], [1355, 688, 240, 27], [1500, 635, 85, 22], [1512, 532, 78, 23], [707, 432, 328, 26], [1632, 171, 58, 23], [392, 357, 759, 43], [317, 196, 55, 23], [1584, 862, 8, 74], [966, 797, 168, 30], [0, 181, 3, 75], [1518, 551, 70, 53], [682, 465, 283, 29], [1632, 164, 59, 22], [679, 558, 193, 30], [683, 580, 77, 22], [1526, 627, 67, 23], [1504, 661, 80, 19], [1511, 585, 72, 26], [1239, 228, 65, 25], [1444, 561, 148, 28], [1276, 228, 64, 25], [1502, 525, 87, 20], [1438, 657, 151, 26], [1810, 187, 94, 25], [1381, 906, 393, 32], [1834, 41, 46, 81], [686, 512, 226, 27], [602, 417, 370, 24], [1502, 668, 81, 21], [1634, 156, 56, 21], [1527, 610, 66, 25], [1527, 619, 66, 24], [699, 529, 206, 27], [709, 482, 250, 28], [684, 573, 73, 20], [1741, 99, 52, 23], [1628, 147, 53, 22], [856, 616, 252, 34], [701, 544, 194, 29], [173, 862, 139, 31], [307, 203, 52, 24], [1541, 608, 43, 35], [668, 497, 245, 30], [720, 530, 61, 26], [237, 175, 68, 77], [298, 121, 52, 135], [720, 538, 62, 25], [1526, 602, 67, 25], [1270, 221, 71, 23], [709, 580, 81, 22], [700, 563, 69, 22], [691, 254, 160, 106], [1716, 92, 61, 22], [1645, 147, 56, 22], [761, 255, 208, 105], [589, 619, 320, 31], [1509, 562, 72, 24], [1509, 569, 73, 26], [720, 515, 61, 24], [1534, 617, 58, 51], [720, 547, 62, 23], [681, 590, 78, 20], [1707, 99, 49, 23], [320, 165, 67, 88], [627, 356, 379, 254], [720, 522, 60, 26], [1512, 555, 70, 25], [1756, 142, 148, 31], [1894, 909, 10, 26], [312, 212, 61, 23], [719, 555, 63, 23], [915, 461, 82, 22], [1527, 595, 66, 23], [257, 204, 47, 47], [1894, 118, 11, 97], [1812, 181, 89, 21], [1293, 212, 63, 22], [697, 382, 308, 28], [1569, 617, 24, 32], [710, 597, 250, 24], [1758, 122, 146, 36], [1295, 221, 69, 21], [1436, 545, 153, 29], [922, 453, 77, 22], [712, 572, 71, 21], [1301, 229, 66, 23], [1534, 587, 58, 46], [291, 585, 831, 29], [1654, 156, 54, 21], [1510, 547, 69, 23], [1568, 634, 25, 30], [1450, 624, 140, 30], [1659, 132, 50, 22], [320, 355, 632, 63], [1899, 208, 7, 45], [1652, 164, 57, 20], [262, 201, 48, 28], [1199, 663, 396, 63], [1180, 518, 419, 75], [622, 726, 343, 80], [1548, 616, 41, 35], [732, 667, 224, 34], [672, 598, 98, 22], [1450, 609, 139, 29], [308, 200, 42, 51], [736, 453, 77, 22], [1569, 602, 24, 30], [744, 580, 82, 23], [336, 755, 97, 26], [327, 203, 57, 23], [900, 446, 81, 19], [399, 617, 354, 33], [899, 436, 83, 22], [1573, 612, 19, 23], [0, 179, 39, 72], [1510, 540, 71, 23], [908, 404, 89, 22], [264, 182, 169, 53], [1492, 677, 87, 20], [364, 721, 647, 143], [946, 725, 85, 149], [1240, 221, 68, 21], [297, 196, 52, 23], [915, 420, 79, 23], [1891, 204, 14, 49], [1240, 512, 193, 29], [1441, 576, 147, 28], [1716, 85, 63, 19], [283, 201, 50, 28], [1285, 768, 312, 24], [407, 862, 539, 19], [900, 428, 81, 22], [167, 848, 146, 31], [1689, 99, 52, 23], [902, 413, 83, 21], [898, 468, 77, 23], [935, 778, 22, 88], [1825, 157, 76, 20], [897, 454, 74, 21], [338, 765, 104, 27], [1570, 585, 23, 30], [1574, 597, 19, 57], [1200, 576, 353, 64], [1672, 140, 53, 21], [1509, 688, 162, 30], [601, 433, 336, 26], [1500, 706, 172, 27], [856, 725, 109, 124], [749, 530, 64, 26], [778, 530, 65, 25], [729, 563, 72, 22], [865, 530, 66, 25], [1574, 627, 19, 65], [741, 538, 64, 25], [274, 194, 50, 26], [1692, 208, 101, 14], [784, 538, 68, 25], [1455, 143, 214, 29], [743, 725, 342, 94], [1260, 212, 72, 23], [1816, 174, 86, 19], [1894, 106, 16, 131], [193, 186, 205, 62], [989, 525, 80, 21], [1467, 903, 123, 15], [1534, 569, 58, 48], [751, 418, 344, 25], [1676, 132, 50, 22], [924, 532, 65, 23], [1677, 150, 56, 19], [249, 204, 209, 41], [924, 722, 201, 57], [841, 530, 65, 25], [810, 530, 65, 25], [290, 210, 56, 27], [1573, 595, 19, 25], [761, 573, 179, 34], [664, 565, 72, 20], [949, 750, 175, 41], [344, 880, 448, 41], [1900, 59, 6, 60], [746, 547, 67, 23], [1799, 92, 55, 20], [249, 128, 50, 127], [584, 689, 349, 25], [883, 460, 73, 23], [744, 555, 70, 23], [1820, 165, 79, 21], [738, 572, 78, 21], [1659, 95, 173, 31], [245, 211, 57, 24], [840, 580, 83, 23], [864, 538, 68, 25], [1887, 52, 19, 200], [807, 580, 83, 23], [1827, 142, 74, 67], [776, 580, 83, 23], [640, 667, 252, 31], [1695, 92, 58, 22], [908, 396, 91, 22], [776, 547, 68, 23], [312, 124, 61, 132], [190, 197, 73, 22], [1528, 579, 65, 24], [924, 540, 66, 23], [1198, 579, 390, 27], [1763, 92, 142, 33], [600, 252, 207, 106], [950, 254, 201, 31], [1549, 601, 39, 34], [775, 555, 69, 23], [1574, 580, 18, 24], [348, 359, 805, 82], [1489, 685, 90, 19], [684, 555, 71, 23], [949, 862, 96, 19], [832, 538, 68, 25], [927, 595, 93, 26], [1548, 639, 40, 36], [1683, 215, 101, 14], [1578, 621, 14, 37], [1533, 909, 374, 27], [994, 533, 75, 22], [1664, 160, 83, 16], [191, 254, 99, 23], [1560, 624, 32, 33], [1868, 63, 34, 37], [1841, 121, 60, 58], [883, 522, 57, 26], [685, 548, 69, 22], [1832, 140, 69, 21], [995, 540, 73, 23], [780, 497, 255, 30], [380, 836, 713, 31], [949, 587, 83, 24], [199, 204, 73, 22], [1771, 772, 65, 23], [1870, 74, 32, 45], [190, 186, 72, 66], [568, 544, 314, 25], [223, 139, 45, 114], [884, 515, 58, 24], [864, 547, 68, 23], [1897, 373, 12, 110], [692, 508, 81, 23], [268, 158, 71, 95], [347, 725, 645, 33], [1090, 782, 32, 19], [795, 511, 233, 28], [948, 411, 69, 23], [1005, 518, 87, 20], [941, 725, 191, 31], [1276, 204, 66, 23], [183, 214, 81, 17], [643, 572, 80, 23], [905, 905, 139, 12], [856, 570, 78, 23], [530, 650, 464, 21], [948, 451, 75, 24], [1827, 149, 74, 20], [778, 515, 63, 24], [909, 389, 88, 21], [823, 547, 68, 23], [1780, 100, 72, 21], [814, 480, 280, 29], [958, 760, 143, 107], [1193, 356, 400, 54], [1658, 175, 87, 16], [948, 460, 76, 23], [236, 196, 61, 21], [588, 570, 206, 33], [1550, 584, 38, 34], [993, 548, 76, 22], [1560, 609, 32, 31], [853, 555, 71, 23], [1899, 161, 7, 51], [821, 555, 70, 23], [1006, 509, 85, 21], [625, 149, 77, 20], [763, 563, 75, 22], [716, 686, 259, 32], [289, 203, 47, 48], [814, 529, 225, 27], [681, 453, 95, 20], [684, 540, 70, 23], [1578, 637, 14, 38], [7, 252, 222, 32], [768, 572, 77, 21], [1236, 521, 172, 78], [781, 863, 312, 18], [996, 501, 85, 21], [1574, 572, 18, 24], [1816, 26, 44, 96], [780, 544, 190, 29], [1241, 212, 268, 25], [859, 563, 76, 22], [320, 216, 68, 28], [1900, 414, 5, 74], [274, 205, 48, 46], [1813, 460, 86, 23], [1188, 906, 402, 9], [10, 179, 71, 70], [770, 558, 191, 30], [804, 447, 272, 29], [336, 747, 105, 22], [1429, 758, 123, 18], [808, 572, 78, 23], [1443, 594, 144, 27], [900, 530, 62, 25], [1681, 107, 50, 23], [698, 255, 831, 98], [1900, 439, 7, 47], [1836, 0, 59, 63], [1354, 200, 122, 13], [936, 378, 81, 22], [1573, 558, 20, 56], [1400, 775, 135, 16], [683, 532, 69, 23], [813, 877, 306, 26], [684, 501, 91, 22], [995, 493, 86, 20], [1271, 196, 258, 27], [690, 650, 287, 32], [1686, 124, 47, 23], [1392, 823, 94, 113], [958, 476, 83, 22], [899, 523, 63, 23], [767, 726, 185, 134], [1368, 208, 133, 15], [963, 500, 79, 23], [1898, 255, 6, 80], [1588, 579, 329, 50], [679, 493, 98, 20], [1843, 87, 56, 28], [191, 187, 72, 22], [794, 563, 75, 22], [679, 478, 104, 20], [1658, 182, 56, 19], [1872, 113, 30, 106], [913, 547, 69, 23], [952, 445, 72, 21], [978, 516, 75, 22], [996, 485, 85, 20], [1351, 530, 235, 27], [814, 573, 197, 34], [827, 563, 74, 22], [1428, 750, 126, 18], [1469, 913, 121, 15], [951, 530, 63, 26], [300, 215, 58, 30], [678, 486, 102, 19], [1551, 568, 37, 35], [859, 860, 522, 78], [332, 648, 800, 78], [1437, 767, 116, 17], [679, 468, 102, 22], [1592, 580, 68, 23], [969, 547, 74, 23], [993, 555, 78, 23], [1196, 440, 402, 76], [899, 538, 65, 25], [678, 461, 99, 20], [847, 588, 278, 29], [1642, 215, 91, 18], [555, 527, 329, 25], [1513, 504, 71, 189], [938, 522, 63, 26], [686, 1, 550, 62], [205, 142, 45, 113], [885, 255, 164, 108], [804, 464, 290, 28], [1863, 91, 36, 31], [1488, 720, 182, 24], [1899, 136, 7, 57], [832, 435, 284, 23], [1561, 594, 29, 30], [1592, 587, 69, 23], [1680, 143, 75, 18], [802, 548, 353, 22], [263, 215, 52, 30], [1390, 182, 112, 18], [956, 779, 173, 27], [288, 385, 848, 34], [1427, 783, 125, 18], [1738, 117, 62, 20], [1452, 168, 118, 15], [253, 254, 204, 27], [1359, 218, 92, 16], [638, 580, 83, 23], [565, 559, 264, 26], [1432, 798, 116, 18], [1561, 577, 29, 30], [823, 381, 272, 30], [897, 512, 274, 22], [1020, 533, 77, 22], [973, 508, 78, 23], [313, 703, 20, 23], [674, 515, 78, 23], [1593, 595, 69, 23], [922, 562, 65, 24], [1195, 873, 400, 34], [1022, 461, 70, 20], [1896, 291, 8, 44], [1481, 165, 226, 28], [679, 523, 71, 23], [1450, 176, 109, 14], [1228, 652, 358, 45], [1466, 127, 170, 30], [848, 902, 283, 34], [308, 617, 852, 34], [217, 51, 76, 204], [968, 555, 76, 23], [242, 204, 46, 48], [1506, 855, 84, 81], [1345, 182, 112, 16], [804, 398, 296, 27], [1648, 208, 88, 16], [1019, 540, 78, 23], [1316, 214, 69, 20], [878, 555, 68, 23], [568, 359, 348, 41], [335, 914, 109, 18], [1593, 603, 69, 24], [1803, 85, 50, 19], [948, 491, 83, 25], [1371, 193, 130, 15], [346, 511, 80, 19], [983, 563, 79, 23], [227, 204, 64, 22], [1608, 577, 215, 29], [551, 597, 302, 23], [1575, 525, 18, 63], [963, 522, 68, 26], [1783, 35, 80, 130], [984, 478, 84, 20], [938, 515, 68, 23], [902, 579, 79, 24], [1236, 349, 349, 495], [1605, 608, 225, 30], [1258, 609, 335, 27], [1777, 884, 83, 23], [1428, 790, 123, 18], [947, 507, 72, 24], [575, 841, 374, 22], [146, 186, 205, 43], [708, 362, 289, 37], [1187, 855, 405, 30], [1604, 592, 229, 29], [328, 527, 716, 86], [1848, 380, 58, 24], [1291, 703, 301, 26], [1243, 673, 135, 100], [352, 451, 742, 32], [1428, 808, 126, 19], [930, 203, 60, 24], [959, 189, 385, 45], [1731, 127, 77, 19], [963, 580, 83, 22], [1851, 386, 55, 26], [1019, 548, 76, 22], [1258, 661, 332, 25], [1112, 121, 181, 36], [1899, 257, 9, 103], [586, 815, 351, 26], [944, 544, 65, 27], [1020, 478, 83, 20], [1605, 624, 221, 30], [962, 538, 69, 25], [222, 186, 67, 23], [1236, 427, 351, 35], [1473, 693, 100, 19], [890, 501, 278, 22], [1771, 874, 66, 26], [172, 204, 74, 22], [1548, 563, 41, 145], [644, 315, 507, 46], [1272, 460, 148, 81], [1559, 649, 31, 30], [1893, 80, 12, 75], [586, 356, 169, 121], [925, 211, 75, 22], [1844, 371, 62, 25], [1898, 201, 11, 51], [1240, 355, 257, 85], [1678, 190, 107, 18], [1864, 392, 40, 305], [383, 214, 62, 20], [931, 554, 67, 26], [1864, 248, 40, 251], [1199, 356, 397, 265], [1821, 755, 62, 25], [1578, 851, 11, 57], [1098, 255, 506, 102], [1114, 160, 160, 45], [336, 903, 108, 17], [1560, 544, 30, 34], [1442, 855, 135, 81], [904, 569, 70, 26], [529, 757, 413, 28], [1607, 641, 219, 30], [1848, 80, 53, 26], [1821, 747, 62, 25], [137, 2, 552, 62], [1682, 116, 49, 23], [2, 178, 146, 31], [128, 514, 184, 30], [1822, 739, 61, 24], [1576, 552, 16, 39], [816, 254, 296, 28], [1822, 732, 62, 23], [1824, 715, 68, 25], [842, 598, 99, 20], [1234, 591, 359, 29], [1024, 453, 67, 22], [1759, 175, 145, 36], [1824, 707, 68, 25], [1821, 764, 61, 23], [997, 87, 69, 165], [1237, 638, 355, 26], [214, 179, 67, 23], [1824, 667, 67, 25], [214, 211, 67, 23], [1824, 660, 67, 23], [1824, 692, 67, 23], [1824, 684, 67, 24], [1824, 675, 67, 25], [1824, 644, 67, 24], [1824, 652, 67, 23], [1824, 700, 67, 23], [1522, 692, 72, 22], [1824, 635, 67, 25], [1886, 150, 19, 109], [1189, 858, 204, 57], [622, 598, 99, 22], [396, 197, 86, 20], [879, 461, 250, 25], [1824, 627, 67, 24], [928, 577, 79, 26], [1512, 638, 85, 31], [1822, 722, 62, 26], [1028, 84, 61, 171], [949, 569, 79, 27], [0, 88, 14, 100], [1875, 257, 29, 133], [1671, 168, 118, 16], [1900, 255, 6, 85], [1664, 74, 240, 51], [928, 895, 120, 11], [126, 254, 259, 27], [731, 1, 988, 59], [1892, 255, 13, 106], [986, 469, 82, 21], [1024, 435, 63, 23], [1812, 772, 60, 23], [1568, 591, 30, 165], [1819, 620, 66, 23], [1561, 561, 29, 31], [385, 204, 62, 20], [1901, 117, 5, 67], [578, 599, 114, 19], [1820, 612, 65, 23], [1834, 422, 65, 58], [991, 572, 80, 23], [1026, 443, 59, 23], [136, 312, 1033, 46], [1589, 545, 331, 58], [720, 268, 421, 85], [1461, 186, 114, 15], [885, 844, 72, 22], [0, 254, 5, 63], [1819, 595, 66, 25], [955, 913, 270, 25], [988, 461, 76, 22], [718, 598, 99, 22], [1819, 603, 66, 25], [1888, 281, 18, 223], [212, 174, 194, 42], [428, 167, 91, 20], [1821, 779, 61, 24], [1897, 122, 10, 122], [2, 161, 59, 160], [876, 414, 257, 34], [632, 590, 86, 20], [1000, 443, 69, 23], [1654, 190, 87, 16], [1740, 493, 157, 34], [1819, 587, 66, 24], [1762, 848, 135, 31], [1608, 656, 196, 30], [1207, 208, 214, 33], [336, 914, 28, 21], [886, 562, 72, 23], [1102, 140, 171, 29], [622, 851, 155, 13], [1040, 728, 85, 139], [1800, 1, 38, 67], [1819, 580, 66, 23], [0, 254, 3, 49], [460, 486, 697, 23], [1228, 551, 337, 244], [1135, 132, 190, 44], [1521, 700, 73, 22], [1546, 655, 41, 35], [1170, 255, 294, 33], [886, 787, 222, 74], [595, 887, 535, 49], [0, 85, 4, 55], [0, 174, 15, 78], [501, 359, 392, 172], [685, 725, 264, 33], [324, 550, 320, 35], [1476, 529, 123, 31], [1603, 393, 305, 94], [1491, 178, 234, 31], [1222, 739, 196, 31], [2, 352, 227, 30], [840, 659, 290, 68], [1673, 182, 113, 19], [1592, 609, 314, 49], [1764, 830, 140, 33], [1435, 702, 129, 19], [1847, 403, 59, 25], [1278, 512, 315, 27], [1740, 509, 158, 33], [890, 728, 46, 135], [10, 338, 260, 29], [416, 789, 540, 24], [1899, 8, 5, 55], [191, 156, 69, 71], [1352, 89, 162, 48], [360, 522, 784, 23], [335, 767, 35, 20], [621, 565, 94, 21], [821, 559, 248, 29], [248, 218, 172, 37], [1607, 688, 161, 31], [141, 174, 183, 39], [331, 724, 109, 38], [1355, 175, 126, 13], [820, 252, 108, 113], [957, 845, 90, 21], [983, 453, 75, 22], [307, 222, 66, 31], [231, 222, 60, 30], [1608, 673, 181, 30], [948, 700, 183, 174], [1416, 152, 112, 16], [475, 5, 399, 52], [960, 204, 958, 48], [1282, 587, 187, 95], [1820, 421, 75, 22], [745, 150, 69, 19], [1592, 128, 197, 33], [1822, 789, 65, 23], [888, 530, 249, 25], [723, 142, 78, 19], [956, 273, 199, 37], [1743, 862, 148, 34], [960, 714, 94, 116], [837, 199, 184, 46], [1781, 157, 123, 33], [936, 196, 59, 23], [1493, 152, 67, 17], [801, 823, 119, 18], [1332, 192, 116, 13], [131, 529, 182, 30], [1827, 796, 67, 24], [467, 356, 368, 36], [1571, 505, 27, 170], [0, 304, 170, 27], [865, 743, 60, 123], [1888, 60, 16, 58], [1260, 230, 252, 22], [1193, 768, 395, 48], [1753, 38, 145, 242], [1901, 914, 16, 21], [946, 229, 240, 27], [772, 356, 327, 30], [633, 254, 327, 30], [1491, 772, 202, 25], [1544, 925, 43, 11], [774, 189, 236, 38], [541, 222, 82, 22], [0, 283, 163, 31], [7, 185, 149, 63], [1776, 812, 133, 38], [1855, 841, 56, 29], [1392, 808, 102, 19], [984, 428, 73, 23], [96, 95, 81, 19], [1559, 664, 33, 32], [1365, 856, 183, 80], [387, 779, 53, 8], [1605, 573, 316, 106], [1589, 577, 33, 29], [312, 454, 109, 26], [1869, 392, 36, 31], [1603, 613, 71, 22], [1607, 559, 222, 30], [1752, 784, 148, 32], [1652, 199, 79, 17], [192, 102, 121, 125], [1560, 635, 105, 37], [57, 93, 75, 21], [514, 388, 240, 237], [1603, 620, 70, 23], [853, 359, 234, 37], [267, 411, 878, 32], [1599, 660, 73, 22], [1598, 543, 243, 31], [1826, 84, 48, 22], [1589, 594, 33, 30], [1679, 85, 70, 19], [926, 767, 22, 96], [1178, 924, 403, 12], [982, 413, 70, 21], [758, 838, 204, 26], [594, 140, 152, 32], [336, 791, 58, 22], [1589, 609, 33, 30], [1345, 870, 175, 15], [794, 598, 99, 22], [1717, 527, 184, 32], [48, 176, 190, 36], [404, 190, 99, 19], [1536, 670, 132, 33], [1833, 491, 64, 25], [883, 212, 67, 22], [219, 156, 73, 77], [983, 436, 74, 22], [1601, 645, 71, 22], [1603, 637, 70, 21], [1603, 628, 70, 22], [315, 688, 14, 37], [1187, 508, 403, 166], [792, 605, 356, 16], [985, 421, 72, 22], [541, 211, 84, 24], [350, 92, 213, 30], [1831, 317, 64, 200], [836, 316, 277, 37], [1155, 254, 210, 27], [123, 80, 113, 16], [1206, 732, 371, 51], [1400, 798, 97, 15], [1227, 485, 103, 121], [1156, 228, 217, 27], [1375, 816, 198, 15], [542, 851, 147, 13], [383, 222, 94, 19], [1600, 653, 73, 22], [0, 847, 63, 89], [813, 580, 313, 23], [6, 0, 107, 60], [88, 540, 225, 30], [1599, 527, 239, 32], [801, 848, 125, 16], [941, 772, 20, 98], [1558, 681, 34, 30], [1848, 62, 53, 56], [1593, 486, 321, 48], [0, 100, 3, 83], [1608, 684, 59, 24], [863, 284, 266, 33], [1704, 135, 100, 19], [1826, 485, 71, 24], [658, 233, 287, 23], [1812, 74, 53, 22], [471, 156, 154, 32], [1310, 627, 253, 96], [1848, 411, 59, 23], [416, 175, 100, 19], [342, 128, 59, 131], [1718, 193, 186, 29], [1237, 620, 355, 30], [549, 724, 555, 45], [1544, 653, 118, 32], [404, 381, 626, 29], [291, 62, 135, 74], [801, 649, 316, 30], [70, 160, 195, 34], [899, 189, 149, 41], [416, 182, 91, 19], [1308, 877, 225, 15], [1252, 451, 349, 35], [1272, 671, 320, 26], [1416, 193, 119, 15], [1425, 185, 119, 13], [1503, 168, 58, 18], [0, 175, 7, 78], [1353, 146, 236, 27], [1901, 208, 6, 48], [1701, 656, 204, 30], [1225, 415, 348, 35], [712, 808, 236, 38], [315, 710, 17, 38], [331, 121, 94, 15], [790, 790, 123, 20], [92, 815, 213, 30], [1684, 160, 126, 16], [0, 251, 298, 74], [1593, 642, 323, 56], [0, 92, 2, 62], [657, 809, 388, 58], [1900, 176, 6, 37], [1700, 673, 205, 30], [361, 771, 70, 16], [1521, 796, 159, 35], [1687, 283, 199, 31], [1610, 668, 61, 24], [1589, 626, 33, 30], [108, 431, 203, 31], [1483, 176, 121, 17], [1591, 516, 329, 55], [1152, 355, 181, 309], [1106, 251, 210, 25], [66, 63, 235, 190], [1600, 511, 237, 33], [947, 764, 174, 20], [980, 403, 71, 23], [588, 156, 177, 31], [1882, 888, 30, 33], [540, 511, 445, 23], [1609, 675, 59, 25], [1490, 160, 83, 19], [1608, 706, 163, 30], [1698, 641, 206, 30], [1884, 847, 26, 78], [1885, 55, 22, 86], [803, 37, 281, 121], [1591, 667, 31, 27], [54, 100, 68, 22], [1228, 559, 366, 29], [1763, 60, 139, 39], [1363, 223, 87, 18], [99, 878, 206, 29], [1500, 192, 229, 31], [1610, 691, 55, 24], [1702, 689, 203, 30], [1098, 110, 146, 38], [0, 62, 119, 30], [412, 164, 63, 23], [1273, 440, 213, 84], [1695, 624, 209, 30], [350, 713, 538, 16], [1899, 12, 15, 49], [72, 143, 153, 33], [1857, 826, 60, 26], [887, 226, 210, 30], [1677, 304, 212, 28], [703, 140, 66, 21], [415, 595, 343, 23], [585, 609, 136, 12], [686, 851, 153, 13], [348, 451, 745, 97], [1591, 641, 31, 30], [1352, 161, 224, 29], [1704, 704, 200, 30], [986, 755, 129, 26], [771, 845, 355, 19], [615, 714, 345, 18], [433, 837, 360, 22], [688, 17, 41, 22], [453, 815, 532, 48], [694, 786, 255, 27], [389, 77, 217, 30], [1569, 142, 215, 33], [1096, 650, 49, 97], [1185, 928, 67, 8], [531, 218, 294, 35], [153, 215, 89, 18], [1189, 486, 386, 55], [1684, 592, 222, 30], [1885, 761, 26, 177], [1496, 867, 84, 29], [1688, 609, 217, 30], [1116, 139, 108, 19], [149, 142, 165, 38], [113, 447, 192, 33], [0, 71, 34, 112], [1708, 753, 193, 28], [1491, 145, 72, 16], [239, 2, 276, 28], [1367, 608, 166, 173], [964, 744, 150, 29], [83, 558, 228, 30], [1698, 145, 103, 16], [1900, 12, 7, 51], [1706, 721, 198, 30], [1365, 205, 171, 40], [19, 798, 233, 33], [1431, 210, 106, 14], [962, 852, 166, 11], [1225, 739, 200, 88], [905, 1, 97, 113], [1705, 736, 199, 30], [1, 210, 52, 204], [927, 837, 200, 31], [1380, 790, 111, 18], [1724, 876, 170, 38], [1642, 222, 92, 19], [1809, 920, 104, 13], [414, 418, 368, 32], [1198, 585, 99, 79], [1703, 768, 196, 30], [738, 851, 158, 13], [337, 800, 95, 41], [1363, 167, 110, 16], [345, 140, 760, 109], [1544, 476, 54, 235], [1416, 200, 112, 16], [308, 254, 978, 45], [82, 830, 220, 33], [1582, 359, 314, 42], [1215, 483, 72, 8], [492, 851, 164, 13], [316, 725, 372, 27], [1712, 150, 126, 22], [1510, 783, 190, 27], [1855, 883, 65, 24], [620, 156, 71, 19], [585, 685, 539, 41], [941, 814, 183, 24], [1885, 5, 27, 60], [1850, 891, 70, 24], [1692, 544, 209, 30], [1288, 815, 249, 13], [1716, 800, 181, 31], [315, 418, 558, 58], [562, 899, 570, 19], [1888, 406, 16, 38], [1296, 254, 277, 27], [1863, 494, 41, 333], [1177, 356, 434, 117], [0, 215, 2, 45], [1601, 447, 305, 44], [914, 736, 35, 127], [962, 663, 186, 40], [1895, 403, 7, 44], [354, 827, 71, 25], [1361, 501, 178, 189], [1664, 849, 195, 29], [336, 921, 49, 15], [1901, 255, 6, 69], [212, 221, 75, 21], [389, 563, 331, 22], [1682, 577, 222, 29], [860, 4, 180, 59], [365, 668, 470, 32], [338, 732, 110, 127], [1756, 199, 145, 56], [392, 454, 233, 29], [394, 110, 29, 9], [25, 863, 212, 29], [1656, 866, 208, 27], [1668, 117, 176, 29], [379, 836, 255, 27], [320, 707, 13, 30], [1185, 356, 405, 29], [0, 62, 3, 39], [1429, 218, 96, 27], [15, 370, 250, 29], [1032, 678, 113, 38], [357, 772, 78, 8], [1681, 561, 220, 30], [651, 880, 442, 19], [13, 769, 248, 30], [52, 160, 53, 96], [409, 81, 36, 12], [836, 236, 1085, 20], [1424, 218, 104, 15], [23, 786, 246, 31], [741, 927, 347, 8], [598, 342, 1153, 65], [734, 931, 417, 5], [1559, 154, 216, 36], [354, 909, 501, 27], [1676, 833, 205, 28], [389, 775, 42, 9], [1896, 927, 23, 6], [1249, 1, 507, 56], [1657, 896, 240, 33], [851, 0, 442, 10], [1677, 320, 217, 29], [1338, 179, 214, 27], [354, 764, 90, 64], [841, 302, 296, 30], [979, 251, 208, 25], [1549, 205, 219, 39], [902, 0, 227, 7], [1425, 1, 285, 31], [1200, 410, 390, 81], [504, 563, 252, 35], [614, 281, 357, 36], [767, 247, 1154, 66], [131, 161, 203, 37], [215, 142, 233, 35], [1400, 927, 188, 9], [1274, 717, 310, 24], [334, 932, 90, 4], [1844, 62, 58, 27], [685, 27, 48, 15], [1345, 124, 264, 35], [1744, 128, 129, 19], [1898, 59, 11, 99], [393, 399, 619, 29], [1233, 718, 313, 34], [0, 139, 2, 51], [1280, 736, 304, 22], [1190, 674, 267, 48], [1257, 932, 335, 6], [1363, 418, 177, 203], [1840, 71, 56, 26], [905, 169, 152, 43], [0, 700, 50, 238], [1556, 92, 57, 22], [1011, 214, 520, 41], [1349, 829, 113, 106], [1572, 854, 11, 67], [1258, 779, 273, 142], [575, 313, 389, 37], [410, 798, 550, 47], [1514, 185, 66, 17], [1552, 396, 45, 171], [703, 134, 65, 19], [334, 783, 47, 41], [324, 529, 841, 34], [1380, 935, 214, 3], [1189, 216, 500, 32], [1807, 59, 75, 55], [1648, 371, 259, 26], [16, 158, 177, 30], [328, 583, 467, 39], [1552, 858, 37, 50], [733, 789, 212, 45], [1071, 117, 80, 20], [709, 11, 21, 42], [342, 920, 471, 16], [1588, 363, 9, 106], [12, 754, 253, 30], [619, 164, 64, 20], [1056, 127, 62, 20], [1200, 697, 383, 61], [1271, 493, 101, 105], [752, 66, 365, 56], [1090, 721, 39, 139], [1105, 252, 468, 46], [23, 848, 226, 30], [1672, 819, 208, 26], [1754, 120, 90, 17], [1609, 1, 286, 58], [312, 374, 17, 55], [1296, 494, 131, 110], [912, 935, 219, 3], [1240, 529, 345, 38], [6, 214, 188, 41], [64, 252, 249, 25], [399, 931, 525, 5], [391, 219, 998, 36], [331, 478, 504, 42], [403, 507, 377, 24], [339, 728, 251, 140], [698, 4, 24, 53], [12, 385, 241, 30], [643, 142, 445, 38], [1, 127, 72, 19], [397, 208, 120, 18], [8, 399, 246, 30], [12, 736, 249, 30], [1512, 225, 255, 27], [337, 0, 924, 9], [1899, 58, 11, 65], [1596, 634, 325, 113], [691, 0, 854, 20], [0, 181, 530, 35], [618, 169, 53, 24], [1846, 64, 27, 62], [508, 501, 499, 26], [1738, 923, 175, 10], [1163, 721, 424, 95], [131, 34, 563, 25], [1179, 826, 408, 41], [759, 773, 168, 19], [1689, 352, 206, 30], [313, 103, 144, 37], [10, 909, 298, 27], [1190, 442, 121, 75], [1227, 760, 194, 32], [1901, 418, 13, 66], [350, 579, 302, 25], [143, 251, 498, 31], [0, 386, 299, 54], [1476, 168, 396, 37], [887, 157, 167, 40], [389, 768, 425, 91], [0, 258, 492, 40], [308, 366, 29, 49], [720, 0, 429, 10], [310, 597, 40, 13], [612, 149, 14, 88], [1515, 192, 75, 16], [0, 9, 16, 51], [1249, 592, 140, 87], [277, 331, 876, 29], [8, 0, 309, 52], [1591, 674, 320, 55], [2, 92, 145, 26], [611, 176, 202, 33], [1670, 479, 222, 33], [1347, 107, 207, 40], [3, 804, 307, 38], [337, 453, 483, 42], [756, 808, 157, 19], [710, 934, 414, 4], [1393, 157, 98, 19], [687, 5, 19, 52], [1553, 203, 368, 45], [38, 197, 527, 49], [705, 125, 71, 19], [1192, 648, 281, 114], [1648, 338, 243, 26], [322, 512, 530, 48], [1681, 428, 216, 37], [744, 181, 272, 201], [1737, 1, 54, 63], [301, 239, 98, 16], [1100, 89, 167, 144], [391, 496, 775, 20], [2, 118, 69, 18], [1737, 254, 159, 26], [308, 384, 28, 24], [1400, 388, 123, 19], [958, 63, 481, 48], [10, 830, 292, 54], [1652, 786, 215, 30], [800, 935, 257, 3], [309, 370, 17, 27], [331, 851, 485, 28], [404, 339, 481, 24], [1176, 935, 158, 3], [336, 726, 47, 135], [902, 17, 479, 50], [1073, 125, 96, 22], [1588, 388, 309, 45], [1356, 168, 391, 43], [0, 12, 3, 44], [68, 461, 235, 31], [119, 251, 564, 88], [600, 172, 123, 32], [0, 454, 298, 51], [1353, 96, 389, 55], [1, 111, 148, 30], [344, 497, 170, 81], [1568, 854, 9, 71], [1519, 199, 67, 16], [539, 764, 423, 45], [119, 138, 454, 34], [1680, 400, 214, 30], [319, 583, 89, 16], [328, 706, 804, 42], [1084, 117, 114, 19], [618, 178, 44, 23], [8, 281, 288, 37], [684, 34, 49, 11], [866, 1, 105, 78], [350, 783, 136, 37], [1547, 255, 238, 27], [1750, 935, 168, 3], [0, 509, 297, 54], [0, 13, 9, 41], [1555, 794, 342, 55], [412, 916, 706, 22], [1428, 931, 168, 7], [1279, 204, 642, 31], [22, 320, 253, 30], [0, 483, 297, 51], [1203, 811, 390, 17], [1179, 891, 415, 29], [1615, 866, 282, 52], [1344, 128, 371, 49], [691, 203, 581, 43], [371, 500, 51, 8], [0, 353, 296, 51], [1195, 800, 397, 26], [0, 717, 304, 45], [1172, 930, 418, 6], [450, 193, 362, 59], [311, 562, 85, 23], [1652, 384, 250, 27], [1522, 207, 72, 17], [334, 760, 39, 84], [1, 899, 309, 21], [49, 0, 367, 24], [1358, 223, 180, 32], [1586, 562, 30, 153], [0, 0, 125, 17], [1789, 20, 50, 10], [1900, 367, 13, 114], [1183, 924, 239, 11], [972, 124, 325, 27], [1588, 707, 328, 49], [1659, 415, 238, 31], [0, 543, 299, 50], [1212, 469, 370, 36], [399, 899, 349, 34], [847, 609, 263, 19], [66, 478, 235, 31], [425, 512, 34, 21], [309, 728, 28, 24], [105, 219, 234, 34], [105, 66, 587, 63], [601, 923, 536, 13], [708, 117, 80, 19], [1170, 760, 454, 107], [1177, 331, 413, 37], [421, 509, 138, 22], [1218, 450, 50, 16], [1903, 73, 4, 46], [404, 95, 25, 12], [1283, 2, 236, 26], [441, 56, 347, 38], [0, 413, 285, 56], [1729, 262, 163, 37], [471, 613, 287, 26], [17, 301, 259, 33], [0, 149, 401, 33], [324, 931, 132, 5], [0, 673, 289, 53], [336, 840, 376, 34], [1213, 660, 148, 83], [1589, 613, 20, 73], [999, 4, 422, 49], [94, 122, 731, 122], [455, 732, 137, 131], [346, 811, 74, 24], [1215, 446, 59, 9], [1191, 514, 106, 82], [986, 0, 430, 7], [1449, 327, 156, 514], [1576, 407, 48, 322], [331, 718, 30, 155], [391, 927, 52, 9], [531, 150, 90, 101], [1196, 935, 397, 3], [96, 0, 637, 20], [1899, 0, 18, 14], [453, 928, 162, 4], [1044, 23, 113, 12], [1204, 786, 386, 23], [1197, 406, 293, 175], [1330, 356, 203, 177], [1078, 172, 314, 40], [118, 41, 84, 23], [937, 927, 195, 9], [648, 860, 339, 19], [1235, 722, 39, 11], [618, 149, 17, 81], [308, 349, 853, 33], [673, 129, 181, 32], [1181, 935, 149, 3], [320, 464, 101, 56], [342, 498, 66, 8], [423, 757, 373, 34], [1240, 750, 347, 23], [724, 186, 559, 38], [1215, 602, 77, 8], [1552, 739, 361, 46], [1593, 931, 325, 5], [319, 373, 313, 46], [6, 865, 303, 43], [1611, 927, 296, 9], [127, 921, 175, 14], [1065, 52, 196, 12], [420, 809, 385, 25], [948, 932, 193, 6], [1181, 856, 81, 80], [52, 259, 243, 41], [312, 465, 106, 22], [451, 931, 290, 5], [213, 471, 1025, 51], [111, 0, 22, 63], [216, 66, 601, 45], [562, 935, 542, 3], [1189, 518, 107, 30], [129, 9, 99, 43], [307, 290, 280, 36], [332, 677, 188, 28], [1202, 384, 143, 29], [1203, 883, 157, 30], [446, 761, 346, 23], [424, 456, 135, 22], [329, 384, 205, 30], [1288, 612, 179, 28], [444, 761, 348, 24], [1178, 298, 256, 29], [156, 14, 71, 30], [334, 677, 187, 28], [331, 384, 203, 30], [1289, 688, 80, 27], [1183, 298, 247, 30], [1290, 536, 115, 27], [1208, 385, 133, 28], [441, 915, 313, 23], [1291, 460, 103, 30], [1207, 441, 385, 63], [131, 13, 35, 35], [317, 289, 268, 39], [0, 2, 114, 60], [133, 14, 95, 33], [307, 289, 283, 39], [1210, 517, 381, 67], [48, 22, 42, 17], [331, 383, 201, 32], [1201, 384, 142, 31], [424, 455, 136, 24], [1292, 459, 102, 31], [332, 674, 189, 33], [364, 920, 55, 18], [48, 21, 43, 19], [1177, 297, 255, 32], [440, 915, 314, 23], [1290, 535, 115, 28], [961, 787, 135, 21], [1292, 688, 76, 26], [1103, 277, 452, 86], [1220, 519, 52, 53], [1289, 612, 179, 28], [448, 759, 351, 50], [1236, 773, 101, 21], [1291, 459, 102, 31], [1284, 533, 124, 33], [14, 4, 131, 58], [1236, 598, 332, 54], [311, 292, 69, 30], [1180, 860, 411, 76], [444, 761, 347, 23], [1209, 439, 383, 67], [1207, 665, 372, 62], [1183, 862, 408, 75], [1742, 5, 35, 40], [359, 566, 161, 19], [1217, 446, 61, 51], [857, 733, 252, 125], [1237, 774, 99, 20], [1287, 611, 183, 30], [1205, 886, 154, 24], [428, 512, 114, 21], [1121, 115, 103, 110], [426, 455, 133, 24], [958, 786, 141, 21], [1158, 486, 430, 123], [1214, 600, 60, 44], [1288, 536, 117, 29], [2, 6, 112, 50], [866, 734, 245, 118], [1208, 376, 174, 48], [426, 512, 119, 22], [1285, 685, 156, 35], [1290, 612, 177, 28], [138, 1, 378, 57], [121, 8, 113, 48], [1197, 376, 152, 39], [9, 10, 92, 44], [0, 2, 115, 56], [1292, 459, 101, 31], [691, 9, 512, 49], [1217, 445, 67, 52], [21, 21, 72, 21], [298, 279, 305, 67], [323, 377, 220, 42], [1740, 4, 112, 45], [130, 13, 100, 36], [1216, 516, 371, 70], [1290, 458, 105, 33], [354, 566, 175, 19], [452, 763, 195, 19], [392, 756, 427, 46], [1196, 378, 160, 42], [191, 261, 580, 92], [1208, 378, 169, 44], [1176, 294, 261, 55], [442, 914, 312, 24], [19, 21, 75, 22], [11, 10, 94, 45], [307, 265, 281, 53], [449, 763, 202, 19], [324, 289, 544, 43], [1147, 434, 456, 136], [322, 374, 223, 44], [1257, 451, 147, 42], [422, 763, 519, 62], [316, 651, 225, 61], [961, 786, 136, 21], [128, 3, 183, 57], [346, 567, 43, 20], [446, 915, 301, 22], [1223, 517, 204, 59], [1181, 294, 258, 57], [277, 266, 879, 82], [693, 4, 555, 64], [421, 452, 142, 27], [308, 291, 71, 30], [1192, 868, 362, 62], [1239, 597, 323, 56], [133, 7, 192, 52], [1194, 446, 390, 123], [324, 667, 208, 42], [1199, 369, 280, 62], [1221, 443, 368, 61], [15, 15, 95, 35], [292, 367, 874, 89], [455, 761, 314, 23], [1293, 459, 100, 30], [1218, 667, 51, 63], [1347, 70, 174, 193], [69, 3, 169, 51], [426, 455, 133, 24], [312, 292, 68, 30], [491, 460, 65, 15], [335, 675, 183, 29], [1212, 386, 130, 26], [131, 12, 99, 36], [1178, 295, 251, 30], [428, 512, 114, 21], [1207, 372, 330, 64], [1174, 266, 261, 57], [329, 287, 255, 37], [35, 19, 65, 25], [78, 8, 165, 49], [314, 288, 258, 40], [1181, 579, 405, 173], [1103, 276, 450, 88], [156, 7, 441, 50], [338, 384, 193, 28], [1291, 688, 77, 26], [1272, 608, 216, 35], [351, 729, 770, 131], [1110, 574, 482, 103], [1282, 532, 134, 35], [424, 488, 138, 18], [380, 455, 200, 57], [1202, 884, 162, 29], [1114, 121, 107, 102], [362, 771, 61, 52], [428, 455, 127, 23], [1170, 291, 285, 59], [1177, 293, 253, 32], [312, 375, 248, 43], [436, 487, 122, 20], [351, 918, 101, 20], [76, 2, 644, 65], [11, 10, 94, 44], [1235, 775, 101, 19], [1189, 376, 186, 43], [325, 652, 291, 73], [1206, 380, 138, 37], [314, 651, 296, 72], [1213, 517, 64, 58], [331, 673, 203, 58], [124, 0, 225, 62], [1291, 535, 114, 29], [288, 275, 318, 57], [1183, 300, 130, 25], [953, 777, 156, 35], [319, 349, 260, 68], [1287, 686, 84, 31], [1194, 877, 170, 39], [330, 384, 209, 29], [1845, 11, 37, 39], [1193, 366, 275, 62], [432, 767, 392, 67], [1285, 684, 88, 34], [1241, 519, 167, 54], [1262, 683, 322, 35], [1188, 447, 397, 123], [1292, 612, 174, 27], [1238, 772, 99, 23], [1292, 459, 103, 29], [340, 674, 179, 33], [1197, 379, 157, 31], [400, 455, 182, 56], [158, 11, 71, 37], [298, 271, 767, 72], [1164, 286, 297, 54], [1152, 264, 301, 59], [1248, 445, 298, 56], [422, 913, 331, 25], [701, 8, 519, 53], [1154, 433, 457, 138], [1209, 598, 66, 50], [1262, 677, 116, 46], [440, 914, 298, 23], [128, 11, 113, 35], [418, 454, 147, 23], [1180, 295, 253, 26], [274, 270, 356, 74], [393, 748, 422, 47], [1095, 390, 39, 24], [1161, 484, 430, 128], [1779, 6, 102, 49], [289, 270, 777, 74], [522, 292, 63, 30], [309, 292, 70, 29], [338, 285, 261, 36], [1230, 600, 257, 38], [1211, 372, 319, 64], [274, 660, 922, 65], [116, 9, 117, 46], [1199, 871, 204, 47], [334, 559, 211, 34], [1245, 518, 163, 54], [710, 2, 539, 73], [1179, 297, 251, 27], [325, 360, 328, 70], [358, 767, 66, 64], [407, 914, 333, 22], [384, 900, 382, 37], [1284, 611, 184, 28], [406, 454, 178, 58], [143, 3, 307, 51], [1184, 435, 412, 143], [449, 761, 338, 23], [337, 387, 156, 21], [7, 6, 102, 48], [1819, 6, 66, 47], [1794, 8, 39, 34], [1204, 661, 378, 70], [364, 922, 56, 16], [121, 1, 215, 57], [1241, 775, 93, 20], [333, 675, 187, 28], [774, 116, 635, 126], [1268, 606, 265, 70], [1214, 600, 64, 46], [1195, 871, 361, 58], [652, 763, 130, 19], [452, 760, 339, 25], [1181, 300, 134, 25], [315, 283, 282, 42], [1182, 300, 104, 25], [359, 899, 444, 39], [127, 8, 112, 41], [1188, 871, 370, 57], [48, 21, 45, 20], [376, 901, 389, 33], [445, 761, 353, 23], [1289, 458, 108, 33], [1213, 593, 334, 142], [1252, 442, 299, 61], [1215, 376, 313, 60], [671, 763, 115, 19], [416, 453, 148, 26], [1195, 397, 380, 120], [409, 913, 339, 25], [315, 287, 555, 45], [356, 921, 70, 16], [1270, 526, 269, 54], [553, 179, 950, 74], [330, 368, 788, 81], [1229, 520, 354, 63], [1234, 448, 318, 56], [1282, 536, 133, 29], [311, 292, 71, 30], [157, 15, 74, 26], [1168, 294, 271, 31], [511, 293, 69, 29], [1235, 600, 346, 51], [655, 6, 599, 57], [321, 381, 228, 63], [1290, 688, 76, 25], [276, 268, 361, 78], [9, 9, 96, 45], [1743, 3, 34, 45], [1145, 362, 453, 101], [317, 652, 221, 61], [1180, 313, 251, 28], [35, 18, 59, 25], [1218, 445, 66, 51], [373, 915, 139, 23], [327, 674, 202, 31], [1151, 357, 430, 108], [272, 269, 361, 78], [1271, 605, 260, 72], [457, 763, 122, 19], [1286, 685, 161, 34], [1221, 447, 85, 52], [348, 740, 271, 116], [1254, 447, 289, 53], [302, 657, 250, 67], [333, 671, 206, 63], [61, 1, 199, 57], [1284, 611, 194, 30], [1194, 370, 298, 57], [1088, 390, 53, 23], [1212, 595, 67, 51], [142, 3, 315, 54], [274, 271, 802, 73], [1202, 883, 158, 29], [365, 748, 618, 68], [322, 349, 259, 71], [313, 276, 263, 26], [1187, 444, 401, 122], [440, 760, 363, 25], [312, 359, 333, 69], [192, 259, 562, 91], [1279, 533, 149, 34], [1738, 3, 90, 45], [97, 0, 614, 73], [376, 752, 513, 55], [1200, 864, 371, 70], [116, 0, 211, 58], [1181, 360, 409, 54], [518, 291, 67, 32], [1092, 387, 42, 26], [1220, 453, 190, 80], [361, 566, 159, 19], [1196, 864, 396, 69], [1293, 614, 165, 22], [297, 662, 852, 61], [399, 755, 421, 48], [394, 915, 213, 23], [324, 55, 93, 92], [356, 468, 46, 49], [1208, 512, 363, 101], [1200, 877, 178, 44], [5, 18, 53, 34], [1179, 579, 405, 177], [1136, 357, 463, 104], [1186, 294, 253, 52], [1238, 774, 95, 18], [1199, 862, 159, 34], [464, 146, 160, 112], [309, 277, 294, 70], [320, 286, 555, 47], [167, 294, 1144, 40], [1268, 526, 270, 54], [1209, 601, 341, 139], [1275, 527, 261, 53], [324, 374, 223, 73], [133, 13, 73, 37], [1204, 496, 356, 114], [1232, 445, 328, 53], [1095, 390, 39, 24], [310, 365, 268, 65], [152, 265, 509, 93], [427, 511, 116, 23], [1597, 484, 322, 53], [1198, 393, 377, 120], [1215, 597, 64, 48], [321, 656, 242, 68], [157, 13, 73, 34], [1771, 5, 101, 47], [1212, 386, 131, 25], [429, 513, 112, 19], [1083, 387, 63, 25], [367, 733, 602, 59], [1204, 884, 155, 27], [1050, 250, 481, 87], [649, 763, 134, 19], [344, 671, 447, 38], [305, 297, 283, 37], [1218, 522, 57, 52], [353, 679, 165, 21], [1353, 80, 337, 178], [1233, 598, 258, 40], [447, 782, 438, 32], [1192, 89, 388, 146], [1142, 279, 372, 89], [311, 293, 68, 26], [1217, 517, 58, 59], [899, 742, 204, 111], [1249, 668, 290, 52], [348, 918, 101, 20], [1279, 524, 182, 36], [855, 743, 250, 108], [508, 290, 663, 44], [360, 919, 113, 19], [1611, 480, 303, 128], [1239, 668, 305, 54], [403, 780, 611, 62], [1200, 374, 355, 112], [1153, 283, 341, 79], [1217, 505, 355, 113], [1097, 386, 31, 19]], [0.99995, 0.9999, 0.99979, 0.99978, 0.99969, 0.99968, 0.99963, 0.99945, 0.99913, 0.999, 0.99895, 0.99757, 0.99246, 0.98429, 0.97519, 0.97507, 0.94435, 0.83647, 0.27612, 0.24969, 0.24325, 0.19204, 0.15906, 0.10225, 0.07749, 0.07439, 0.0513, 0.04386, 0.04089, 0.04052, 0.03362, 0.0313, 0.02095, 0.01621, 0.10403, 0.09595, 0.05623, 0.04956, 0.04777, 0.04553, 0.03814, 0.03423, 0.03186, 0.02965, 0.02753, 0.02698, 0.02577, 0.02512, 0.02279, 0.02183, 0.02149, 0.02108, 0.02049, 0.01931, 0.01891, 0.01839, 0.01817, 0.01781, 0.01689, 0.01675, 0.01641, 0.01557, 0.01521, 0.01441, 0.01429, 0.014, 0.01355, 0.01316, 0.01289, 0.01289, 0.01288, 0.01276, 0.01259, 0.0125, 0.01219, 0.01183, 0.01178, 0.01155, 0.01125, 0.0111, 0.01066, 0.01045, 0.0099, 0.00983, 0.00982, 0.00974, 0.00943, 0.00902, 0.00858, 0.00838, 0.00823, 0.00815, 0.00807, 0.00757, 0.00755, 0.00735, 0.00717, 0.00709, 0.00707, 0.00684, 0.00683, 0.00681, 0.00679, 0.00665, 0.00664, 0.00661, 0.00644, 0.00639, 0.00625, 0.00623, 0.00605, 0.00598, 0.00596, 0.00588, 0.00571, 0.00555, 0.0055, 0.00541, 0.00531, 0.00527, 0.00521, 0.00514, 0.00506, 0.00504, 0.00486, 0.00484, 0.00483, 0.00471, 0.0047, 0.0047, 0.00465, 0.00449, 0.00446, 0.00439, 0.00438, 0.00436, 0.00428, 0.00427, 0.00426, 0.00423, 0.00405, 0.00402, 0.00398, 0.00391, 0.00375, 0.00369, 0.00365, 0.00358, 0.00357, 0.00355, 0.00349, 0.00348, 0.00345, 0.00341, 0.00335, 0.00334, 0.00334, 0.00333, 0.00331, 0.00331, 0.0033, 0.00329, 0.00321, 0.00317, 0.00312, 0.00311, 0.00311, 0.0031, 0.00309, 0.00306, 0.00303, 0.00297, 0.00296, 0.00295, 0.00294, 0.00292, 0.00291, 0.00291, 0.00285, 0.00285, 0.00278, 0.00277, 0.00273, 0.00265, 0.00264, 0.00263, 0.00254, 0.00254, 0.00252, 0.00247, 0.00242, 0.0024, 0.00236, 0.00234, 0.00234, 0.00231, 0.00228, 0.00228, 0.00228, 0.00224, 0.00223, 0.00221, 0.00219, 0.00218, 0.00218, 0.00214, 0.00212, 0.0021, 0.00209, 0.00209, 0.00208, 0.00208, 0.00206, 0.00205, 0.00204, 0.00204, 0.00203, 0.002, 0.002, 0.00196, 0.00195, 0.00193, 0.00193, 0.0019, 0.0019, 0.00189, 0.00189, 0.00188, 0.00187, 0.00185, 0.00184, 0.00183, 0.00182, 0.00181, 0.0018, 0.0018, 0.00179, 0.00179, 0.00178, 0.00177, 0.00176, 0.00175, 0.00175, 0.00175, 0.00173, 0.00172, 0.00171, 0.00171, 0.0017, 0.0017, 0.0017, 0.00169, 0.00164, 0.00163, 0.00163, 0.00162, 0.00161, 0.0016, 0.0016, 0.00159, 0.00154, 0.00152, 0.00152, 0.00151, 0.00151, 0.00151, 0.0015, 0.00148, 0.00148, 0.00146, 0.00146, 0.00144, 0.00144, 0.00144, 0.00143, 0.00143, 0.00141, 0.0014, 0.00138, 0.00137, 0.00137, 0.00137, 0.00136, 0.00134, 0.00134, 0.00134, 0.00132, 0.00132, 0.0013, 0.00128, 0.00128, 0.00126, 0.00124, 0.00124, 0.00123, 0.00123, 0.00123, 0.00123, 0.00121, 0.00121, 0.0012, 0.0012, 0.0012, 0.0012, 0.00118, 0.00118, 0.00117, 0.00117, 0.00116, 0.00115, 0.00114, 0.00114, 0.00113, 0.00113, 0.00113, 0.00112, 0.00111, 0.00111, 0.00111, 0.00107, 0.00107, 0.00107, 0.00106, 0.00106, 0.00105, 0.00105, 0.00105, 0.00104, 0.00104, 0.00102, 0.00101, 0.00101, 0.00099, 0.00099, 0.00099, 0.00098, 0.00098, 0.00096, 0.00096, 0.00094, 0.00094, 0.00094, 0.00094, 0.00093, 0.00093, 0.00092, 0.00092, 0.00091, 0.00091, 0.00089, 0.00089, 0.00089, 0.00089, 0.00087, 0.00087, 0.00086, 0.00086, 0.00086, 0.00086, 0.00086, 0.00085, 0.00085, 0.00085, 0.00085, 0.00084, 0.00083, 0.00082, 0.00082, 0.00082, 0.00082, 0.00082, 0.00081, 0.00081, 0.00081, 0.00081, 0.00081, 0.00079, 0.00079, 0.00078, 0.00078, 0.00077, 0.00077, 0.00077, 0.00077, 0.00077, 0.00077, 0.00076, 0.00076, 0.00076, 0.00076, 0.00075, 0.00075, 0.00074, 0.00074, 0.00074, 0.00074, 0.00073, 0.00073, 0.00073, 0.00073, 0.00072, 0.00072, 0.00072, 0.00072, 0.00072, 0.0007, 0.00069, 0.00069, 0.00069, 0.00069, 0.00068, 0.00068, 0.00068, 0.00068, 0.00068, 0.00066, 0.00066, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00064, 0.00064, 0.00063, 0.00063, 0.00063, 0.00063, 0.00063, 0.00062, 0.00062, 0.00062, 0.00062, 0.00061, 0.0006, 0.0006, 0.0006, 0.0006, 0.0006, 0.0006, 0.00059, 0.00059, 0.00059, 0.00059, 0.00059, 0.00058, 0.00058, 0.00058, 0.00058, 0.00057, 0.00057, 0.00057, 0.00056, 0.00056, 0.00056, 0.00056, 0.00055, 0.00055, 0.00055, 0.00055, 0.00055, 0.00055, 0.00054, 0.00054, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00052, 0.00052, 0.00052, 0.00052, 0.00052, 0.00052, 0.00052, 0.00051, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.00049, 0.00049, 0.00049, 0.00049, 0.00049, 0.00049, 0.00049, 0.00049, 0.00048, 0.00048, 0.00048, 0.00047, 0.00047, 0.00047, 0.00047, 0.00046, 0.00046, 0.00046, 0.00046, 0.00046, 0.00045, 0.00044, 0.00044, 0.00044, 0.00043, 0.00043, 0.00043, 0.00043, 0.00043, 0.00043, 0.00043, 0.00043, 0.00043, 0.00042, 0.00042, 0.00042, 0.00042, 0.00041, 0.00041, 0.0004, 0.0004, 0.0004, 0.0004, 0.0004, 0.00039, 0.00039, 0.00039, 0.00039, 0.00039, 0.00039, 0.00037, 0.00037, 0.00037, 0.00037, 0.00037, 0.00037, 0.00036, 0.00036, 0.00036, 0.00036, 0.00035, 0.00035, 0.00034, 0.00034, 0.00034, 0.00034, 0.00034, 0.00033, 0.00033, 0.00033, 0.00033, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.0003, 0.0003, 0.0003, 0.0003, 0.0003, 0.0003, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00026, 0.00026, 0.00026, 0.00026, 0.00026, 0.00026, 0.00026, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.00019, 0.00019, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.95591, 0.92553, 0.91825, 0.86306, 0.79687, 0.74028, 0.71497, 0.70566, 0.56619, 0.49767, 0.48925, 0.464, 0.33713, 0.3322, 0.32336, 0.32272, 0.2983, 0.27394, 0.22074, 0.21124, 0.20387, 0.15072, 0.13495, 0.13146, 0.11297, 0.10113, 0.09842, 0.09304, 0.08813, 0.05003, 0.04578, 0.04016, 0.03872, 0.03661, 0.03521, 0.0321, 0.02836, 0.0245, 0.02351, 0.02254, 0.01766, 0.01521, 0.01461, 0.01313, 0.01069, 0.0096, 0.00914, 0.00913, 0.00906, 0.00811, 0.00727, 0.00674, 0.00651, 0.00577, 0.00522, 0.00501, 0.00497, 0.00483, 0.00453, 0.0045, 0.00416, 0.0039, 0.00388, 0.00382, 0.0037, 0.00368, 0.00355, 0.00349, 0.00332, 0.00302, 0.00301, 0.0027, 0.00245, 0.00233, 0.00214, 0.00212, 0.00206, 0.00194, 0.00176, 0.00171, 0.00171, 0.0017, 0.0017, 0.00167, 0.00162, 0.00157, 0.00157, 0.0015, 0.00146, 0.00145, 0.00139, 0.00136, 0.00136, 0.00135, 0.00128, 0.00124, 0.00124, 0.00123, 0.00119, 0.00119, 0.00117, 0.00116, 0.00114, 0.00112, 0.00111, 0.00107, 0.00107, 0.00104, 0.00104, 0.00097, 0.00093, 0.00093, 0.00092, 0.00091, 0.00089, 0.00084, 0.00078, 0.00073, 0.00073, 0.00072, 0.0007, 0.00068, 0.00067, 0.00067, 0.00067, 0.00067, 0.00067, 0.00066, 0.00066, 0.00065, 0.00061, 0.0006, 0.00059, 0.00057, 0.00056, 0.00056, 0.00054, 0.00054, 0.00053, 0.00053, 0.00052, 0.00051, 0.00051, 0.0005, 0.0005, 0.00049, 0.00048, 0.00048, 0.00046, 0.00045, 0.00044, 0.00044, 0.00044, 0.00042, 0.00041, 0.00041, 0.0004, 0.00039, 0.00038, 0.00038, 0.00037, 0.00036, 0.00036, 0.00035, 0.00035, 0.00034, 0.00034, 0.00033, 0.00033, 0.00033, 0.00032, 0.00032, 0.00032, 0.00032, 0.0003, 0.00029, 0.00029, 0.00029, 0.00028, 0.00028, 0.00028, 0.00027, 0.00027, 0.00027, 0.00026, 0.00026, 0.00026, 0.00025, 0.00025, 0.00024, 0.00024, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00022, 0.00022, 0.00022, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.00019, 0.00019, 0.00019, 0.00019, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00017, 0.00017, 0.00017, 0.00017, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05], [6, 6, 6, 6, 6, 6, 5, 6, 6, 6, 6, 6, 1, 6, 6, 6, 1, 6, 2, 6, 5, 2, 2, 6, 5, 2, 5, 2, 2, 2, 6, 2, 9, 2, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 17, 11, 12, 12, 12, 12, 12, 12, 12, 11, 11, 17, 11, 11, 12, 12, 12, 11, 12, 12, 13, 17, 12, 13, 11, 16, 13, 11, 16, 16, 11, 11, 16, 17, 12, 16, 11, 11, 12, 11, 13, 17, 11, 11, 13, 13, 13, 17, 13, 11, 15, 16, 15, 13, 13, 17, 12, 17, 13, 12, 16, 11, 12, 17, 13, 13, 13, 17, 16, 14, 15, 16, 13, 13, 13, 14, 13, 15, 17, 15, 16, 14, 13, 11, 11, 13, 11, 12, 15, 15, 13, 12, 12, 13, 13, 11, 12, 16, 12, 11, 11, 11, 11, 13, 15, 13, 11, 12, 11, 17, 13, 13, 13, 11, 13, 13, 15, 17, 12, 15, 11, 11, 15, 13, 14, 13, 13, 13, 17, 17, 17, 17, 16, 12, 12, 13, 13, 16, 13, 11, 16, 11, 17, 13, 11, 13, 13, 15, 11, 17, 16, 15, 13, 13, 15, 12, 13, 16, 11, 17, 17, 13, 15, 14, 13, 13, 13, 12, 11, 14, 13, 17, 15, 13, 12, 13, 17, 15, 12, 15, 12, 13, 15, 18, 17, 15, 12, 15, 12, 13, 12, 17, 15, 18, 17, 18, 12, 13, 11, 15, 13, 12, 15, 11, 15, 13, 13, 17, 18, 18, 18, 13, 11, 16, 15, 11, 12, 17, 13, 18, 12, 12, 13, 14, 12, 13, 11, 17, 17, 13, 13, 18, 11, 18, 11, 17, 17, 17, 13, 18, 17, 17, 15, 11, 14, 17, 18, 13, 12, 12, 11, 12, 15, 11, 14, 13, 13, 15, 16, 16, 12, 18, 14, 11, 17, 11, 13, 14, 12, 14, 12, 18, 12, 13, 12, 14, 16, 18, 16, 18, 14, 11, 14, 15, 12, 17, 12, 16, 11, 13, 11, 12, 17, 15, 13, 14, 12, 16, 11, 13, 14, 11, 13, 11, 16, 11, 13, 14, 14, 13, 15, 16, 16, 13, 12, 11, 11, 16, 14, 15, 15, 14, 17, 17, 13, 17, 15, 13, 13, 12, 12, 11, 14, 15, 12, 16, 13, 17, 17, 12, 11, 17, 15, 15, 17, 18, 12, 17, 17, 16, 11, 16, 12, 11, 11, 13, 11, 18, 11, 16, 14, 15, 13, 11, 11, 17, 14, 17, 14, 16, 15, 13, 17, 13, 11, 12, 18, 12, 15, 17, 11, 11, 17, 17, 18, 11, 17, 11, 15, 13, 14, 13, 11, 13, 12, 12, 16, 16, 17, 15], ["Others", "ArrowButton", "<PERSON><PERSON>", "Checkbox", "CloseButton", "Inputbox", "Icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MinimizeButton", "Radiobutton", "Cell", "Title", "SectionTitle", "SelectedTab", "BrowserTab", "SectionTab", "PopupTitle", "Logo", "ExcelSheet"], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], ["checked"]], "text": [[[0, 0, 1920, 937], [1759, 13, 1767, 25], [1815, 13, 1823, 24], [49, 24, 89, 36], [136, 19, 160, 42], [167, 20, 227, 42], [741, 24, 791, 39], [311, 293, 379, 321], [386, 294, 475, 320], [481, 299, 519, 321], [524, 293, 583, 321], [1183, 302, 1209, 320], [1213, 302, 1293, 324], [1297, 303, 1344, 320], [1348, 303, 1376, 323], [1380, 303, 1427, 320], [333, 388, 416, 410], [419, 389, 464, 406], [467, 388, 529, 406], [1102, 385, 1122, 404], [1206, 389, 1251, 406], [1253, 389, 1289, 410], [1292, 389, 1340, 410], [426, 459, 484, 477], [486, 459, 510, 477], [513, 459, 558, 474], [1292, 464, 1388, 485], [427, 489, 464, 503], [465, 492, 471, 502], [474, 490, 481, 503], [484, 489, 531, 506], [534, 493, 559, 506], [430, 516, 459, 529], [461, 516, 540, 530], [1292, 540, 1400, 557], [371, 569, 390, 582], [391, 569, 407, 582], [408, 568, 428, 582], [430, 568, 468, 582], [470, 569, 508, 582], [510, 568, 531, 585], [1293, 616, 1365, 633], [1369, 616, 1465, 633], [333, 680, 399, 702], [402, 680, 519, 702], [1292, 692, 1365, 709], [447, 764, 486, 782], [488, 764, 525, 779], [526, 764, 571, 779], [574, 764, 633, 779], [636, 764, 723, 779], [726, 764, 788, 782], [1242, 776, 1276, 790], [1278, 776, 1296, 790], [1298, 777, 1334, 793], [371, 795, 414, 818], [447, 795, 469, 808], [470, 794, 511, 808], [514, 794, 537, 808], [539, 798, 563, 811], [565, 796, 580, 808], [583, 796, 626, 808], [627, 798, 658, 811], [660, 795, 712, 808], [714, 794, 790, 808], [793, 794, 818, 808], [821, 798, 865, 808], [868, 794, 887, 808], [963, 789, 1011, 806], [1013, 790, 1065, 803], [1068, 789, 1095, 803], [447, 818, 465, 831], [466, 816, 481, 828], [483, 814, 514, 828], [1205, 889, 1358, 906], [448, 918, 486, 936], [488, 918, 524, 933], [526, 918, 610, 936], [612, 918, 679, 933], [682, 918, 745, 936]], ["1 1 MENU Ui Path Search Let's Focus on You It's Tuesday, June 13, 2023 Awaiting Your Action ... Your Top Apps Change My Photo Onboarding Inbox - 1 year(s) ago DUE 02/25/2022 Performance Go to All Inbox Items (1) Personal Information Timely Suggestions Absence Keep Your Home Contact Information Updated View All Apps epe We would like you to review your Contact Information and ensure it's Update Contact Info up to date Announcements Keep Your Emergency Contacts Updated", "1", "1", "MENU", "Ui", "Path", "Search", "Let's", "Focus", "on", "You", "It's", "Tuesday,", "June", "13,", "2023", "Awaiting", "Your", "Action", "...", "Your", "Top", "Apps", "Change", "My", "Photo", "Onboarding", "Inbox", "-", "1", "year(s)", "ago", "DUE", "02/25/2022", "Performance", "Go", "to", "All", "Inbox", "Items", "(1)", "Personal", "Information", "Timely", "Suggestions", "Absence", "Keep", "Your", "Home", "Contact", "Information", "Updated", "View", "All", "Apps", "epe", "We", "would", "like", "you", "to", "review", "your", "Contact", "Information", "and", "ensure", "it's", "Update", "Contact", "Info", "up", "to", "date", "Announcements", "Keep", "Your", "Emergency", "Contacts", "Updated"], [0.97, 0.99, 0.99, 0.99, 0.99, 0.41, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.83, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.97, 0.99, 0.99, 0.82, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.39, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.93, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99]], "relations": [[[3, 2], [1, 0]], [0.99997, 0.33639], [[136, 2, 520, 59], [166, 19, 62, 23], [685, 5, 536, 54], [741, 23, 51, 15]], [0, 0], ["Label", "Context", "Header"]], "content_type": null, "version": "23.4.10.0"}