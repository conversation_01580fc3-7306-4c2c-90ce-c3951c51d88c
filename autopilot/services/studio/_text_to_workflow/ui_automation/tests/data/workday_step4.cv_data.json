{"controls": [[[828, 576, 117, 39], [954, 576, 119, 39], [1792, 5, 42, 40], [1738, 5, 41, 42], [224, 185, 147, 45], [688, 5, 534, 53], [1842, 9, 42, 43], [993, 430, 26, 25], [994, 480, 25, 25], [245, 329, 26, 26], [704, 18, 26, 26], [611, 266, 27, 21], [890, 472, 139, 44], [15, 19, 28, 24], [37, 185, 183, 44], [890, 422, 139, 44], [342, 263, 87, 28], [129, 10, 40, 39], [439, 264, 29, 29], [143, 322, 137, 43], [1086, 301, 37, 37], [35, 881, 183, 41], [874, 483, 17, 17], [480, 264, 24, 29], [412, 86, 28, 19], [875, 533, 16, 17], [875, 433, 16, 16], [1087, 301, 35, 35], [143, 321, 137, 45], [477, 261, 33, 33], [831, 528, 255, 35], [37, 880, 178, 39], [128, 4, 546, 55], [1826, 346, 20, 24], [436, 260, 34, 34], [1, 1, 115, 61], [517, 270, 18, 19], [744, 459, 17, 20], [525, 568, 20, 21], [1610, 457, 19, 22], [1174, 675, 27, 26], [1607, 565, 27, 26], [890, 421, 139, 45], [37, 185, 183, 45], [890, 471, 140, 45], [1086, 299, 37, 38], [412, 87, 28, 18], [437, 262, 33, 33], [476, 260, 34, 35], [129, 11, 39, 39], [1426, 554, 225, 110], [1031, 554, 158, 114], [1424, 772, 222, 92], [1209, 663, 221, 109], [1001, 772, 210, 95], [775, 772, 215, 91], [562, 772, 217, 95], [1426, 663, 223, 110], [1644, 663, 220, 111], [1211, 771, 219, 95], [1208, 551, 222, 113], [1211, 442, 219, 111], [559, 663, 222, 110], [1642, 554, 223, 110], [1426, 442, 221, 111], [995, 661, 218, 113], [1436, 331, 208, 113], [357, 554, 202, 110], [1644, 772, 216, 91], [775, 661, 221, 113], [359, 445, 201, 107], [342, 661, 223, 115], [342, 772, 223, 91], [995, 626, 213, 38], [562, 552, 222, 109], [1645, 450, 220, 101], [1645, 331, 216, 113], [1121, 443, 91, 113], [564, 442, 226, 107], [1204, 301, 232, 34], [1404, 298, 244, 36], [772, 670, 225, 35], [340, 331, 225, 112], [1211, 331, 219, 51], [1120, 339, 92, 36], [1641, 333, 223, 46], [1424, 331, 222, 51], [1209, 331, 220, 113], [815, 558, 387, 87], [995, 721, 216, 27], [1643, 299, 227, 36], [558, 554, 221, 55], [1116, 335, 97, 109], [772, 830, 223, 33], [1428, 490, 217, 37], [789, 630, 205, 32], [337, 664, 227, 52], [992, 838, 219, 23], [1208, 554, 221, 53], [776, 721, 219, 29], [558, 331, 223, 43], [1120, 559, 90, 116], [992, 678, 212, 22], [559, 718, 219, 33], [995, 808, 217, 42], [776, 811, 221, 38], [732, 664, 45, 110], [1426, 714, 218, 37], [1163, 666, 47, 108], [561, 808, 219, 41], [1438, 333, 210, 17], [1424, 605, 221, 37], [992, 703, 220, 59], [1210, 606, 219, 29], [1210, 519, 217, 33], [559, 605, 221, 33], [1426, 371, 219, 25], [1209, 715, 219, 39], [1428, 594, 217, 35], [563, 331, 225, 109], [1438, 348, 210, 20], [1426, 837, 218, 26], [989, 667, 224, 40], [345, 837, 215, 27], [993, 707, 219, 26], [732, 772, 45, 96], [1119, 385, 92, 56], [556, 509, 224, 44], [1208, 836, 220, 27], [1594, 666, 49, 108], [1644, 511, 218, 33], [1301, 772, 118, 112], [1118, 443, 93, 47], [1212, 417, 217, 26], [1428, 417, 218, 27], [810, 559, 171, 69], [784, 735, 203, 19], [1120, 301, 88, 34], [1595, 445, 49, 111], [1429, 400, 216, 33], [774, 707, 222, 26], [342, 707, 220, 49], [1426, 385, 219, 26], [1426, 552, 219, 44], [778, 663, 218, 22], [732, 552, 45, 116], [343, 417, 221, 27], [1426, 512, 218, 39], [1080, 381, 38, 19], [1595, 555, 48, 109], [558, 663, 219, 42], [1208, 486, 219, 37], [1641, 370, 222, 27], [1594, 773, 49, 94], [992, 736, 218, 36], [1210, 384, 219, 28], [1117, 614, 20, 50], [559, 592, 221, 29], [1210, 595, 219, 27], [1424, 254, 229, 223], [343, 371, 220, 25], [572, 735, 197, 19], [558, 443, 223, 40], [758, 775, 18, 96], [1084, 395, 34, 27], [342, 630, 221, 30], [1641, 603, 220, 39], [338, 809, 226, 43], [1164, 773, 47, 95], [1899, 63, 5, 66], [1426, 374, 219, 55], [1004, 735, 198, 19], [340, 743, 222, 29], [1428, 116, 233, 234], [1641, 837, 220, 26], [338, 479, 224, 52], [559, 837, 219, 26], [1390, 666, 39, 111], [1120, 609, 90, 29], [1641, 552, 223, 50], [1388, 554, 40, 111], [340, 512, 223, 36], [1118, 556, 93, 37], [1641, 664, 226, 47], [1642, 443, 220, 41], [776, 737, 216, 35], [507, 331, 52, 113], [38, 182, 215, 45], [1209, 443, 14, 110], [559, 384, 221, 26], [1641, 717, 220, 37], [1426, 624, 219, 37], [340, 587, 223, 52], [1432, 406, 209, 16], [1643, 620, 218, 41], [1642, 804, 221, 46], [556, 490, 225, 37], [558, 482, 223, 30], [559, 704, 220, 36], [1130, 500, 79, 52], [1209, 370, 222, 23], [337, 331, 228, 50], [559, 735, 218, 37], [560, 370, 224, 24], [1641, 592, 222, 33], [559, 624, 220, 37], [1645, 422, 217, 22], [1436, 399, 206, 19], [1389, 446, 39, 111], [1212, 398, 217, 34], [1055, 558, 122, 34], [543, 772, 18, 98], [561, 417, 218, 27], [1617, 772, 25, 96], [1425, 736, 219, 36], [1640, 381, 222, 38], [949, 773, 46, 94], [1212, 624, 216, 16], [1211, 637, 218, 27], [1641, 490, 221, 41], [983, 605, 227, 30], [1833, 772, 26, 95], [1207, 666, 222, 48], [1210, 735, 217, 37], [1424, 299, 52, 33], [1210, 443, 46, 116], [974, 776, 19, 94], [1219, 333, 211, 19], [1187, 772, 23, 95], [1673, 859, 210, 77], [1215, 406, 211, 19], [335, 442, 222, 45], [1008, 630, 130, 16], [1212, 399, 214, 16], [1108, 616, 20, 48], [1039, 413, 58, 24], [1900, 62, 6, 67], [996, 663, 207, 22], [1223, 251, 215, 207], [1208, 443, 221, 41], [780, 845, 206, 19], [512, 301, 47, 34], [1076, 403, 43, 23], [1086, 370, 32, 27], [1210, 631, 221, 15], [1555, 297, 88, 35], [1424, 663, 221, 52], [1641, 703, 221, 34], [775, 692, 222, 23], [1079, 389, 39, 22], [340, 399, 223, 40], [1219, 346, 210, 22], [560, 398, 221, 36], [506, 443, 50, 113], [1427, 359, 226, 17], [1189, 450, 20, 105], [1426, 367, 228, 16], [564, 533, 218, 23], [1067, 359, 14, 38], [1657, 899, 252, 37], [1641, 482, 222, 27], [1221, 117, 220, 248], [1071, 411, 48, 23], [1642, 740, 219, 36], [1823, 664, 38, 116], [1597, 338, 46, 106], [1426, 443, 219, 43], [572, 349, 194, 19], [504, 664, 55, 110], [1041, 630, 157, 26], [1010, 573, 195, 66], [1078, 356, 40, 26], [336, 377, 229, 41], [1080, 584, 129, 43], [1120, 352, 92, 30], [503, 775, 57, 93], [1388, 772, 41, 96], [564, 399, 207, 19], [1641, 681, 224, 52], [1425, 299, 25, 33], [352, 345, 207, 24], [503, 555, 54, 110], [337, 551, 227, 49], [991, 689, 222, 40], [1491, 258, 142, 77], [561, 334, 94, 23], [995, 695, 212, 20], [1124, 490, 86, 33], [1428, 663, 71, 110], [556, 299, 224, 35], [1211, 449, 22, 107], [996, 645, 211, 19], [349, 331, 96, 116], [563, 406, 212, 16], [0, 653, 327, 34], [1654, 735, 194, 20], [348, 298, 207, 243], [1201, 446, 12, 110], [783, 721, 205, 84], [556, 458, 226, 39], [1208, 356, 224, 26], [1208, 663, 15, 105], [1118, 374, 95, 27], [1641, 551, 14, 106], [1, 772, 333, 94], [545, 333, 16, 113], [823, 563, 89, 59], [363, 333, 179, 19], [1210, 320, 224, 15], [1644, 530, 218, 23], [1209, 856, 229, 82], [346, 725, 208, 87], [1644, 501, 135, 22], [830, 489, 291, 57], [1818, 772, 40, 69], [1123, 334, 87, 27], [1073, 341, 137, 427], [998, 702, 206, 20], [1221, 713, 203, 77], [1426, 302, 116, 32], [1423, 860, 229, 78], [1127, 479, 85, 23], [1310, 297, 183, 34], [1641, 663, 14, 105], [1120, 403, 91, 30], [1647, 623, 214, 20], [575, 331, 171, 23], [558, 685, 222, 45], [555, 353, 229, 28], [1639, 570, 229, 47], [1035, 591, 106, 30], [1644, 509, 121, 22], [1093, 631, 37, 31], [1209, 298, 14, 34], [1632, 775, 13, 93], [1351, 298, 77, 34], [758, 663, 18, 113], [1644, 639, 218, 25], [1212, 442, 102, 125], [1633, 331, 12, 121], [1636, 555, 29, 110], [1802, 556, 52, 112], [565, 718, 205, 77], [1428, 554, 70, 107], [1017, 811, 196, 73], [1860, 833, 42, 31], [1010, 650, 194, 76], [560, 644, 216, 20], [1653, 345, 204, 22], [1371, 333, 54, 114], [1425, 566, 222, 55], [520, 255, 40, 84], [1209, 862, 220, 23], [1250, 266, 162, 69], [1803, 773, 57, 94], [835, 523, 246, 40], [9, 830, 319, 33], [227, 182, 146, 47], [0, 775, 328, 35], [558, 302, 37, 32], [1054, 616, 71, 16], [778, 556, 207, 294], [1427, 642, 218, 22], [1429, 563, 216, 283], [1640, 458, 228, 41], [1075, 569, 135, 42], [1642, 443, 14, 112], [1222, 616, 212, 42], [1164, 446, 49, 113], [1632, 331, 12, 41], [1837, 685, 23, 92], [1209, 460, 222, 44], [1202, 331, 11, 110], [565, 624, 189, 19], [1643, 772, 218, 47], [1156, 292, 48, 42], [565, 511, 181, 20], [1212, 302, 98, 33], [1600, 294, 41, 38], [938, 664, 55, 109], [1642, 772, 15, 91], [0, 685, 326, 37], [1189, 554, 21, 110], [1432, 718, 205, 80], [1425, 442, 13, 102], [790, 634, 197, 81], [1202, 286, 391, 42], [1402, 291, 22, 43], [930, 2, 444, 58], [1635, 663, 30, 111], [579, 342, 180, 22], [1428, 614, 130, 21], [985, 627, 227, 19], [1429, 606, 140, 22], [1425, 454, 220, 56], [1428, 443, 71, 108], [1429, 725, 140, 22], [1848, 772, 13, 96], [1417, 331, 12, 98], [564, 614, 182, 21], [798, 612, 203, 19], [370, 411, 196, 334], [1349, 446, 65, 109], [1345, 1, 540, 70], [1201, 554, 12, 111], [1663, 396, 198, 272], [1836, 598, 24, 70], [328, 392, 233, 109], [1860, 779, 42, 84], [0, 743, 326, 34], [1203, 664, 29, 110], [957, 858, 550, 74], [1430, 378, 215, 265], [777, 771, 53, 90], [0, 812, 331, 38], [1658, 750, 179, 23], [1202, 772, 11, 95], [1667, 283, 234, 45], [3, 663, 329, 110], [1435, 809, 204, 80], [1407, 333, 23, 118], [1644, 725, 117, 23], [1846, 551, 17, 78], [1206, 686, 226, 46], [1119, 508, 92, 27], [1084, 301, 38, 23], [214, 837, 104, 24], [1644, 862, 241, 26], [1860, 534, 45, 22], [781, 754, 213, 25], [1347, 559, 68, 105], [724, 562, 56, 34], [1847, 661, 15, 80], [1650, 333, 202, 20], [338, 391, 229, 17], [12, 550, 324, 28], [828, 530, 61, 30], [1125, 389, 86, 25], [1821, 551, 39, 60], [1209, 333, 14, 111], [1349, 565, 80, 31], [343, 406, 218, 19], [1124, 465, 88, 27], [1210, 661, 48, 113], [338, 562, 235, 301], [985, 772, 12, 92], [340, 399, 224, 17], [340, 814, 91, 21], [1, 801, 325, 29], [1143, 59, 654, 71], [1440, 862, 216, 23], [0, 613, 312, 48], [1216, 735, 161, 19], [1430, 334, 144, 23], [1212, 754, 218, 26], [1417, 772, 12, 98], [1804, 446, 50, 113], [777, 772, 217, 44], [1837, 370, 23, 78], [1435, 533, 209, 24], [1463, 844, 109, 94], [1620, 281, 21, 54], [1209, 583, 222, 255], [1245, 815, 185, 89], [1339, 666, 74, 108], [1425, 551, 13, 109], [1635, 443, 29, 113], [1433, 519, 188, 19], [558, 772, 220, 45], [1425, 320, 229, 15], [787, 706, 202, 56], [1624, 297, 13, 35], [1119, 368, 92, 24], [1120, 522, 90, 31], [774, 681, 221, 45], [1250, 863, 120, 75], [540, 446, 27, 110], [1646, 275, 213, 270], [1209, 552, 14, 105], [340, 302, 224, 38], [1209, 551, 40, 111], [735, 552, 37, 66], [1416, 445, 13, 111], [20, 671, 305, 32], [565, 367, 206, 16], [321, 631, 24, 33], [1207, 298, 43, 36], [16, 860, 282, 68], [181, 837, 105, 26], [1640, 518, 222, 222], [1481, 236, 146, 160], [770, 771, 11, 96], [555, 862, 221, 24], [669, 551, 101, 116], [991, 859, 224, 79], [1426, 667, 14, 107], [1643, 551, 49, 113], [1209, 772, 15, 92], [1435, 623, 165, 20], [1186, 660, 23, 116], [1312, 862, 89, 76], [1646, 295, 75, 37], [1020, 471, 10, 45], [1022, 421, 11, 42], [551, 341, 246, 208], [567, 552, 211, 22], [0, 715, 328, 36], [1783, 335, 53, 111], [554, 772, 11, 91], [1426, 299, 13, 35], [1208, 411, 224, 236], [1043, 771, 351, 96], [1436, 735, 164, 20], [343, 527, 221, 26], [1221, 493, 208, 245], [991, 772, 220, 47], [322, 743, 23, 31], [1837, 489, 23, 68], [359, 865, 180, 73], [1168, 554, 44, 111], [823, 573, 45, 40], [559, 450, 14, 106], [1123, 417, 87, 27], [690, 1, 539, 59], [21, 703, 303, 31], [704, 670, 75, 34], [21, 641, 304, 30], [1433, 511, 148, 20], [321, 841, 23, 23], [440, 771, 115, 103], [909, 775, 75, 96], [340, 822, 85, 20], [237, 776, 531, 87], [1450, 884, 138, 26], [543, 667, 18, 109], [560, 858, 221, 80], [1208, 772, 219, 44], [7, 595, 317, 23], [525, 280, 29, 51], [463, 262, 42, 33], [1424, 772, 221, 47], [775, 862, 209, 24], [1822, 568, 40, 30], [1216, 606, 144, 22], [560, 304, 126, 31], [1178, 291, 31, 45], [798, 526, 41, 30], [1448, 894, 128, 26], [888, 772, 268, 95], [204, 186, 74, 29], [1899, 5, 7, 60], [275, 320, 5, 43], [340, 772, 222, 44], [343, 836, 90, 24], [778, 725, 110, 23], [668, 768, 109, 110], [771, 663, 10, 114], [1211, 836, 94, 24], [1840, 63, 62, 69], [1731, 849, 105, 89], [547, 331, 13, 63], [1857, 862, 45, 41], [776, 862, 224, 76], [419, 837, 109, 101], [1427, 443, 18, 98], [1891, 63, 13, 67], [1847, 439, 15, 88], [344, 382, 222, 19], [1852, 870, 52, 68], [1822, 335, 39, 117], [348, 830, 96, 20], [1431, 837, 118, 23], [13, 493, 322, 30], [350, 819, 172, 81], [757, 550, 20, 121], [1632, 556, 13, 111], [463, 566, 98, 27], [1820, 661, 40, 61], [1648, 772, 82, 89], [343, 863, 222, 23], [1120, 551, 88, 23], [1899, 860, 7, 43], [1533, 767, 107, 110], [998, 599, 210, 243], [1387, 333, 40, 49], [1427, 279, 33, 55], [321, 819, 20, 48], [1200, 664, 13, 113], [26, 461, 302, 34], [1228, 255, 499, 77], [14, 608, 310, 31], [1384, 292, 33, 40], [1000, 747, 148, 26], [1154, 1, 589, 56], [935, 129, 949, 90], [983, 862, 234, 23], [1643, 609, 220, 239], [1643, 323, 209, 13], [668, 656, 107, 125], [1643, 772, 42, 91], [554, 331, 10, 110], [1647, 757, 221, 24], [1450, 858, 385, 77], [553, 663, 12, 113], [1900, 128, 6, 49], [1597, 773, 193, 72], [1026, 833, 394, 35], [540, 556, 28, 112], [1137, 554, 52, 67], [553, 446, 12, 111], [16, 577, 308, 23], [158, 768, 380, 95], [1426, 334, 14, 109], [437, 255, 44, 94], [779, 645, 210, 37], [1031, 884, 130, 27], [1643, 661, 46, 112], [517, 772, 45, 66], [1634, 881, 239, 37], [960, 573, 147, 37], [552, 555, 13, 101], [1006, 393, 79, 25], [1210, 772, 43, 91], [1007, 888, 210, 43], [1415, 331, 13, 41], [1521, 653, 118, 131], [1022, 471, 11, 42], [1251, 302, 145, 29], [1621, 663, 20, 116], [559, 671, 207, 224], [1411, 449, 17, 42], [559, 446, 82, 106], [1781, 563, 81, 35], [1427, 772, 73, 88], [541, 772, 33, 95], [1405, 439, 21, 114], [1416, 556, 13, 111], [1121, 317, 87, 19], [568, 526, 167, 25], [778, 663, 42, 111], [1631, 440, 14, 119], [558, 391, 239, 84], [572, 417, 209, 239], [828, 351, 257, 45], [1899, 1, 5, 63], [971, 664, 26, 113], [1639, 402, 234, 81], [1192, 442, 18, 50], [1845, 666, 16, 46], [1762, 671, 100, 33], [568, 747, 147, 26], [1433, 637, 129, 24], [1550, 62, 276, 66], [1033, 630, 98, 34], [1643, 290, 26, 45], [117, 0, 587, 53], [563, 512, 218, 220], [973, 627, 19, 37], [1361, 91, 533, 38], [613, 563, 159, 29], [444, 266, 24, 72], [985, 720, 223, 92], [1643, 334, 57, 110], [1119, 485, 84, 25], [1836, 439, 27, 120], [12, 521, 315, 28], [355, 367, 208, 18], [466, 671, 94, 33], [322, 773, 23, 97], [776, 772, 8, 83], [1415, 769, 12, 87], [988, 621, 225, 18], [1636, 773, 32, 93], [189, 845, 127, 21], [1870, 63, 31, 69], [1293, 555, 127, 37], [1341, 779, 201, 84], [559, 663, 15, 110], [1826, 551, 33, 121], [320, 632, 20, 166], [1848, 685, 13, 95], [1846, 773, 15, 59], [1044, 866, 121, 72], [985, 661, 11, 118], [1631, 666, 14, 106], [351, 356, 212, 20], [775, 664, 16, 109], [1849, 591, 13, 78], [1774, 772, 70, 95], [372, 270, 181, 75], [55, 316, 271, 44], [321, 708, 25, 72], [1189, 331, 21, 116], [1259, 454, 168, 26], [1841, 450, 19, 40], [801, 53, 804, 72], [1100, 775, 93, 93], [1204, 772, 31, 95], [4, 577, 309, 48], [803, 298, 287, 37], [1819, 454, 42, 33], [1138, 594, 271, 71], [730, 898, 291, 40], [343, 551, 42, 113], [559, 562, 15, 102], [1599, 1, 211, 63], [343, 663, 40, 110], [1160, 442, 49, 59], [1643, 331, 30, 50], [749, 442, 32, 115], [1622, 440, 20, 113], [564, 583, 218, 238], [1900, 4, 14, 63], [12, 711, 421, 66], [144, 323, 136, 38], [703, 456, 74, 27], [1006, 616, 112, 13], [460, 262, 23, 77], [1415, 664, 14, 113], [1000, 726, 147, 21], [1626, 920, 281, 16], [327, 262, 95, 26], [1820, 443, 36, 59], [1500, 283, 312, 44], [558, 552, 34, 112], [319, 881, 249, 41], [400, 550, 316, 117], [1237, 241, 155, 117], [748, 338, 32, 113], [828, 396, 253, 26], [777, 772, 22, 92], [992, 661, 14, 105], [49, 431, 117, 19], [321, 544, 43, 193], [1127, 454, 99, 56], [1426, 772, 16, 91], [1609, 331, 36, 48], [1849, 479, 13, 80], [1101, 327, 209, 44], [1216, 649, 213, 20], [1427, 554, 21, 95], [776, 686, 7, 88], [779, 880, 211, 35], [564, 891, 217, 40], [193, 186, 55, 7], [1154, 552, 54, 73], [1121, 442, 90, 21], [743, 456, 32, 21], [781, 691, 215, 186], [543, 855, 709, 69], [1291, 887, 339, 49], [776, 552, 21, 112], [1115, 363, 11, 84], [235, 351, 45, 9], [321, 667, 23, 26], [295, 538, 48, 242], [1465, 290, 106, 46], [24, 566, 291, 19], [502, 262, 20, 73], [1334, 515, 227, 38], [798, 626, 43, 6], [148, 844, 114, 20], [871, 418, 161, 47], [1, 400, 312, 40], [558, 443, 33, 112], [1629, 555, 15, 51], [561, 320, 167, 15], [1149, 333, 59, 67], [1345, 129, 562, 40], [559, 338, 16, 106], [1641, 335, 27, 109], [1404, 552, 22, 119], [325, 605, 47, 175], [17, 514, 309, 20], [168, 431, 110, 20], [340, 337, 52, 110], [1584, 610, 216, 50], [1020, 421, 9, 44], [553, 880, 218, 35], [357, 644, 69, 139], [1193, 446, 169, 74], [1427, 334, 26, 116], [1114, 888, 306, 48], [1618, 353, 192, 65], [479, 331, 83, 27], [362, 2, 429, 61], [1849, 363, 13, 84], [521, 298, 15, 36], [468, 609, 221, 52], [561, 700, 639, 83], [1412, 556, 16, 43], [1322, 443, 223, 90], [559, 663, 33, 113], [346, 543, 85, 125], [1844, 341, 17, 34], [465, 63, 982, 67], [799, 529, 55, 34], [612, 60, 498, 69], [1887, 1, 18, 63], [829, 508, 10, 47], [1344, 31, 385, 34], [515, 290, 10, 44], [1723, 555, 126, 41], [519, 663, 43, 52], [322, 666, 23, 63], [1619, 551, 23, 116], [336, 772, 34, 92], [790, 620, 204, 15], [500, 445, 184, 88], [1332, 577, 218, 62], [828, 509, 8, 44], [342, 442, 24, 117], [741, 442, 36, 57], [579, 592, 560, 70], [869, 63, 476, 66], [543, 281, 263, 47], [954, 663, 40, 59], [1384, 551, 44, 55], [242, 860, 359, 75], [682, 555, 87, 43], [1127, 283, 340, 41], [828, 590, 203, 31], [1200, 288, 14, 50], [511, 288, 9, 46], [492, 265, 24, 70], [343, 660, 18, 117], [559, 772, 40, 91], [1670, 552, 189, 21], [419, 330, 298, 114], [1426, 772, 34, 91], [966, 298, 228, 40], [506, 291, 11, 43], [775, 334, 21, 112], [1125, 414, 252, 51], [559, 333, 30, 111], [1566, 703, 219, 58], [1859, 775, 12, 88], [1607, 573, 179, 70], [1644, 552, 13, 59], [1486, 2, 226, 57], [1426, 663, 30, 110], [0, 16, 4, 51], [454, 898, 365, 38], [1836, 1, 65, 64], [1622, 326, 21, 129], [659, 95, 611, 35], [1441, 425, 399, 87], [476, 273, 28, 62], [1681, 663, 177, 22], [372, 128, 870, 120], [1846, 331, 15, 73], [784, 866, 431, 70], [1210, 334, 40, 113], [322, 555, 24, 124], [216, 193, 36, 20], [0, 12, 2, 41], [322, 446, 23, 124], [1260, 233, 633, 77], [775, 859, 227, 16], [436, 273, 23, 62], [1500, 840, 360, 31], [888, 243, 635, 75], [7, 544, 314, 99], [991, 661, 30, 113], [287, 1, 901, 49], [1766, 346, 101, 25], [1166, 663, 45, 63], [1456, 898, 353, 38], [1788, 23, 61, 5], [1838, 552, 23, 50], [1627, 667, 17, 58], [25, 913, 211, 23], [1220, 708, 494, 83], [1211, 384, 12, 60], [1696, 772, 157, 29], [1644, 702, 10, 72], [1644, 485, 11, 67], [992, 772, 16, 92], [0, 0, 110, 17], [12, 837, 780, 59], [736, 331, 43, 51], [1860, 558, 40, 121], [0, 128, 14, 56], [1328, 648, 268, 26], [1411, 667, 17, 52], [321, 822, 69, 20], [408, 306, 109, 28], [1266, 702, 213, 61], [1532, 670, 228, 73], [992, 772, 33, 91], [432, 469, 217, 73], [346, 736, 19, 37], [1824, 920, 81, 16], [1824, 334, 37, 59], [43, 414, 241, 37], [1836, 487, 39, 261], [0, 8, 13, 56], [458, 570, 213, 75], [1082, 302, 51, 11], [776, 663, 8, 60], [32, 1, 292, 59], [343, 331, 22, 124], [336, 891, 377, 45], [1192, 475, 161, 67], [559, 772, 17, 91], [492, 648, 243, 24], [817, 275, 596, 32], [1860, 670, 40, 117], [1634, 649, 204, 23], [1261, 168, 641, 33], [767, 834, 235, 76], [33, 131, 310, 56], [1276, 744, 345, 39], [433, 702, 222, 61], [1121, 519, 260, 40], [1387, 440, 40, 57], [776, 493, 20, 59], [345, 573, 24, 92], [1117, 409, 356, 110], [1119, 359, 14, 85], [850, 895, 341, 41], [1076, 543, 468, 96], [1428, 697, 10, 77], [7, 4, 112, 60], [983, 708, 515, 84], [1068, 96, 792, 36], [1428, 552, 12, 44], [790, 720, 429, 64], [7, 64, 908, 66], [1211, 704, 12, 69], [1211, 443, 11, 45], [994, 720, 13, 53], [1859, 599, 45, 282], [688, 30, 38, 12], [39, 862, 179, 22], [36, 450, 247, 19], [516, 64, 422, 61], [34, 264, 92, 27], [341, 303, 53, 20], [37, 372, 74, 23], [830, 353, 166, 25], [35, 79, 205, 32], [33, 80, 205, 30], [154, 16, 73, 30], [519, 264, 91, 26], [828, 353, 170, 25], [37, 468, 46, 22], [37, 263, 90, 29], [838, 532, 105, 23], [2, 3, 114, 58], [838, 534, 37, 20], [133, 11, 99, 38], [46, 22, 46, 17], [829, 353, 167, 24], [520, 264, 91, 26], [273, 87, 130, 21], [1426, 303, 46, 20], [31, 78, 212, 33], [44, 21, 50, 20], [38, 371, 75, 25], [35, 77, 201, 34], [138, 13, 91, 37], [341, 304, 53, 18], [130, 12, 36, 37], [39, 469, 44, 21], [36, 372, 75, 23], [557, 303, 61, 21], [341, 303, 54, 20], [837, 531, 109, 25], [0, 3, 115, 53], [135, 11, 98, 37], [38, 469, 45, 20], [132, 10, 104, 39], [814, 334, 277, 67], [1643, 303, 64, 19], [1207, 302, 69, 22], [36, 264, 96, 30], [775, 301, 22, 22], [515, 261, 98, 30], [52, 404, 72, 18], [274, 86, 129, 22], [11, 12, 100, 46], [836, 531, 40, 30], [36, 372, 74, 23], [35, 76, 199, 34], [1426, 303, 47, 21], [43, 22, 52, 18], [34, 80, 204, 29], [742, 350, 23, 17], [133, 13, 54, 37], [37, 265, 89, 26], [134, 8, 103, 40], [853, 355, 143, 22], [959, 577, 108, 36], [517, 262, 96, 28], [11, 13, 100, 45], [167, 16, 61, 30], [38, 468, 44, 21], [211, 82, 186, 28], [1210, 305, 65, 17], [737, 345, 32, 24], [274, 86, 129, 22], [31, 79, 211, 31], [826, 354, 215, 39], [831, 577, 111, 35], [559, 304, 57, 18], [37, 467, 45, 24], [343, 304, 52, 18], [36, 81, 205, 28], [818, 353, 229, 42], [29, 71, 283, 55], [133, 11, 102, 34], [810, 332, 282, 68], [1743, 7, 34, 37], [832, 353, 162, 23], [1643, 301, 64, 24], [1214, 304, 61, 18], [14, 16, 93, 30], [36, 372, 75, 24], [130, 12, 37, 41], [289, 86, 112, 22], [813, 344, 255, 66], [1124, 445, 87, 49], [38, 148, 274, 16], [816, 338, 274, 70], [1817, 3, 75, 57], [1739, 3, 94, 50], [834, 530, 43, 30], [841, 532, 49, 22], [38, 188, 180, 39], [350, 330, 227, 115], [1208, 303, 68, 21], [558, 303, 58, 21], [52, 405, 71, 17], [132, 10, 103, 39], [36, 264, 91, 27], [839, 534, 37, 19], [817, 351, 224, 31], [552, 264, 65, 23], [11, 6, 99, 53], [0, 6, 115, 51], [570, 263, 61, 26], [146, 328, 129, 32], [558, 303, 58, 21], [10, 9, 101, 44], [38, 265, 92, 27], [891, 422, 134, 41], [558, 304, 58, 19], [341, 298, 87, 28], [1196, 5, 683, 61], [1219, 526, 36, 20], [38, 146, 275, 20], [41, 498, 40, 18], [231, 188, 134, 40], [62, 421, 31, 17], [41, 406, 238, 48], [411, 87, 29, 18], [146, 324, 133, 39], [519, 262, 94, 28], [742, 350, 23, 17], [121, 1, 609, 64], [1825, 681, 24, 16], [45, 22, 47, 18], [36, 464, 82, 30], [109, 0, 628, 68], [893, 473, 132, 38], [676, 5, 524, 56], [21, 370, 272, 88], [829, 351, 169, 27], [737, 567, 33, 22], [836, 528, 113, 31], [344, 304, 50, 18], [831, 352, 167, 25], [40, 372, 72, 23], [253, 81, 169, 30], [45, 21, 49, 19], [1426, 304, 46, 19], [36, 464, 70, 29], [141, 322, 142, 42], [795, 628, 229, 37], [825, 351, 175, 27], [137, 6, 486, 55], [829, 334, 240, 67], [20, 20, 76, 23], [16, 254, 125, 46], [354, 332, 227, 118], [564, 339, 209, 40], [343, 264, 85, 26], [517, 262, 94, 28], [18, 253, 125, 49], [27, 76, 267, 53], [1739, 7, 41, 39], [1081, 301, 43, 35], [35, 374, 76, 20], [541, 296, 260, 37], [146, 328, 129, 32], [1481, 294, 390, 44], [474, 293, 329, 40], [6, 6, 110, 46], [44, 21, 51, 20], [812, 322, 263, 67], [27, 457, 259, 43], [841, 533, 52, 23], [36, 266, 91, 23], [815, 344, 246, 48], [815, 328, 209, 57], [36, 404, 244, 49], [1087, 303, 34, 33], [1647, 305, 58, 17], [332, 439, 516, 112], [1427, 304, 44, 18], [41, 83, 120, 23], [823, 343, 250, 96], [522, 347, 30, 23], [1219, 527, 36, 18], [519, 263, 92, 25], [37, 405, 233, 42], [836, 531, 108, 26], [1119, 297, 165, 35], [742, 349, 23, 18], [130, 12, 36, 36], [568, 263, 64, 26], [40, 188, 178, 38], [824, 353, 215, 39], [773, 297, 28, 27], [608, 269, 24, 16], [355, 259, 302, 35], [688, 335, 479, 78], [563, 339, 222, 45], [477, 261, 32, 32], [960, 575, 108, 39], [776, 298, 22, 25], [893, 475, 134, 35], [44, 22, 48, 17], [891, 424, 137, 38], [37, 881, 173, 39], [1643, 339, 221, 43], [773, 302, 316, 39], [69, 84, 364, 28], [1126, 336, 83, 40], [832, 526, 120, 34], [1125, 296, 651, 76], [894, 424, 132, 38], [1188, 296, 93, 34], [1334, 287, 508, 71], [838, 531, 62, 20], [775, 298, 24, 27], [21, 13, 86, 35], [891, 422, 138, 40], [39, 468, 43, 21], [1607, 346, 30, 23], [163, 17, 64, 29], [1644, 303, 64, 20], [1080, 277, 549, 62], [42, 83, 118, 23], [13, 18, 64, 28], [36, 265, 92, 26], [63, 421, 30, 16], [1216, 304, 58, 18], [555, 263, 65, 25], [37, 464, 70, 31], [38, 334, 87, 19], [33, 370, 92, 29], [18, 252, 142, 50], [45, 84, 40, 21], [132, 13, 39, 36], [820, 347, 210, 45], [340, 305, 54, 14], [816, 319, 256, 72], [162, 20, 66, 24], [42, 497, 40, 20], [343, 305, 51, 16], [290, 86, 111, 23], [289, 86, 112, 22], [1740, 6, 41, 40], [38, 470, 43, 17], [792, 335, 293, 65], [343, 264, 93, 28], [118, 7, 137, 61], [411, 331, 367, 123], [9, 74, 423, 49], [19, 70, 404, 58], [832, 575, 110, 38], [1817, 2, 76, 55], [355, 552, 545, 108], [342, 263, 88, 27], [7, 15, 67, 34], [293, 87, 112, 19], [733, 343, 41, 26], [737, 565, 34, 25], [104, 0, 611, 65], [457, 261, 224, 33], [235, 186, 130, 39], [738, 344, 31, 25], [567, 345, 223, 56], [895, 475, 130, 35], [410, 87, 31, 20], [123, 5, 507, 59], [808, 303, 296, 37], [1104, 297, 690, 71], [1123, 447, 88, 40], [1087, 304, 33, 30], [38, 146, 277, 20], [558, 264, 61, 24], [733, 338, 43, 22], [568, 344, 223, 61], [43, 22, 51, 18], [839, 534, 34, 20], [80, 84, 346, 27], [42, 498, 39, 18], [130, 13, 37, 35], [410, 87, 33, 21], [1259, 5, 598, 63], [1648, 348, 30, 21], [1427, 303, 45, 21], [1823, 789, 29, 21], [333, 296, 250, 37], [734, 343, 39, 27], [62, 421, 31, 17], [468, 256, 293, 44], [1123, 295, 661, 79], [27, 466, 249, 26], [816, 498, 291, 68], [1648, 347, 31, 23], [1823, 787, 30, 24], [1171, 345, 31, 25], [1138, 298, 267, 36], [826, 527, 218, 34], [163, 17, 64, 29], [1187, 294, 95, 36], [18, 471, 271, 49], [892, 472, 136, 40], [37, 465, 70, 29], [1823, 569, 29, 21], [835, 339, 179, 44], [11, 8, 151, 61], [775, 299, 23, 25], [566, 338, 214, 90], [1737, 1, 75, 54], [890, 423, 135, 39], [1124, 446, 86, 44], [462, 257, 121, 41], [51, 404, 73, 19], [605, 268, 27, 18], [1345, 287, 491, 76], [51, 404, 73, 19], [517, 263, 97, 27], [113, 8, 140, 57], [1738, 6, 43, 38], [38, 374, 66, 18], [34, 463, 84, 30], [433, 258, 103, 42], [838, 534, 37, 18], [1123, 555, 89, 51], [116, 8, 137, 57], [1742, 7, 37, 40], [1213, 303, 63, 20], [132, 15, 54, 33], [679, 8, 546, 53], [364, 261, 266, 31], [838, 530, 105, 26], [251, 81, 170, 30], [794, 628, 225, 36], [558, 303, 59, 21], [1105, 297, 320, 38], [112, 2, 154, 64], [679, 331, 491, 85], [698, 340, 437, 69], [38, 400, 248, 57], [1607, 346, 36, 24], [1215, 305, 59, 16], [562, 337, 222, 46], [28, 465, 247, 25], [1834, 4, 57, 57], [557, 303, 61, 19], [1195, 4, 685, 62], [891, 474, 135, 38], [737, 567, 33, 23], [555, 263, 68, 23], [1740, 4, 92, 49], [124, 3, 513, 59], [340, 305, 55, 17], [161, 18, 66, 28], [1125, 337, 82, 36], [14, 251, 147, 50], [459, 261, 222, 34], [1191, 5, 676, 63], [566, 447, 211, 101], [1609, 350, 22, 16], [39, 81, 106, 27], [1127, 338, 86, 33], [129, 15, 38, 33], [1599, 335, 264, 45], [37, 467, 47, 24], [839, 534, 36, 19], [40, 333, 78, 19], [124, 5, 509, 57], [567, 345, 231, 55], [1427, 304, 45, 19], [1739, 3, 73, 49], [1738, 2, 92, 54], [1736, 8, 47, 34], [697, 8, 533, 52], [1641, 343, 216, 35], [410, 328, 360, 119], [566, 451, 208, 91], [1823, 677, 29, 23], [739, 23, 56, 19], [9, 358, 281, 99], [735, 458, 34, 23], [1080, 303, 48, 34], [14, 67, 287, 64], [1249, 5, 613, 62], [1408, 296, 70, 34], [344, 263, 92, 31], [1389, 346, 31, 24], [741, 350, 24, 17], [337, 298, 65, 29], [358, 438, 237, 123], [291, 319, 511, 103], [568, 340, 210, 84], [936, 67, 962, 61], [841, 533, 69, 20], [46, 85, 37, 20], [1740, 7, 41, 39], [827, 527, 251, 36], [27, 95, 218, 29], [1498, 292, 366, 44], [778, 301, 309, 37], [59, 420, 35, 18], [1607, 345, 31, 24], [19, 15, 92, 32], [1825, 4, 65, 55], [1424, 304, 46, 17], [1823, 567, 29, 23], [44, 83, 36, 23], [838, 533, 89, 19]], [0.99999, 0.99991, 0.9999, 0.99982, 0.99981, 0.99974, 0.99965, 0.99943, 0.99941, 0.99869, 0.99831, 0.99825, 0.99694, 0.99636, 0.99412, 0.99127, 0.9898, 0.98483, 0.97774, 0.93805, 0.88458, 0.86001, 0.84943, 0.8473, 0.736, 0.53938, 0.45822, 0.29832, 0.2838, 0.27665, 0.24931, 0.19617, 0.19478, 0.13705, 0.11758, 0.11646, 0.10832, 0.06309, 0.05168, 0.04196, 0.03818, 0.03737, 0.03528, 0.03366, 0.03138, 0.03074, 0.02618, 0.02023, 0.01478, 0.01334, 0.97264, 0.94616, 0.91371, 0.90383, 0.89898, 0.8978, 0.8885, 0.8745, 0.87176, 0.81369, 0.7989, 0.78822, 0.78781, 0.78316, 0.75236, 0.62207, 0.54245, 0.5303, 0.50867, 0.5066, 0.48514, 0.47839, 0.45398, 0.42043, 0.34371, 0.33741, 0.30621, 0.2919, 0.29134, 0.2741, 0.2472, 0.23846, 0.20585, 0.18447, 0.15531, 0.14954, 0.13937, 0.11338, 0.10148, 0.09081, 0.08811, 0.06506, 0.06214, 0.05923, 0.05566, 0.05072, 0.04862, 0.04381, 0.04291, 0.03979, 0.03873, 0.03662, 0.03365, 0.03353, 0.029, 0.0288, 0.02702, 0.02661, 0.02658, 0.02518, 0.0247, 0.02303, 0.02242, 0.02175, 0.02103, 0.01982, 0.01976, 0.0186, 0.01833, 0.01828, 0.01823, 0.01816, 0.01733, 0.01623, 0.01545, 0.0153, 0.01521, 0.01475, 0.01372, 0.01356, 0.0133, 0.01329, 0.01289, 0.01283, 0.01233, 0.0118, 0.01151, 0.01149, 0.01124, 0.01115, 0.01087, 0.0108, 0.01071, 0.01067, 0.01062, 0.01004, 0.00994, 0.0099, 0.00987, 0.0092, 0.00848, 0.00846, 0.00841, 0.00833, 0.00821, 0.00821, 0.00818, 0.00811, 0.008, 0.00796, 0.00784, 0.00773, 0.00758, 0.00742, 0.00741, 0.00733, 0.00729, 0.00724, 0.00724, 0.00716, 0.00708, 0.00704, 0.00699, 0.00694, 0.00692, 0.00692, 0.00681, 0.00672, 0.00664, 0.00664, 0.00657, 0.00601, 0.00599, 0.00591, 0.00561, 0.00557, 0.00554, 0.00552, 0.00546, 0.00543, 0.00508, 0.00494, 0.00494, 0.0049, 0.00486, 0.00481, 0.00472, 0.00465, 0.00465, 0.00455, 0.00454, 0.00453, 0.00453, 0.00442, 0.00439, 0.00436, 0.00435, 0.00435, 0.00432, 0.00423, 0.00422, 0.00407, 0.00405, 0.00383, 0.00375, 0.00368, 0.00364, 0.00362, 0.00361, 0.00353, 0.00351, 0.00351, 0.00345, 0.00344, 0.00337, 0.00334, 0.00333, 0.0033, 0.00326, 0.00323, 0.00316, 0.00312, 0.00311, 0.00309, 0.00304, 0.00303, 0.00301, 0.00296, 0.00277, 0.00256, 0.00254, 0.00253, 0.00252, 0.00239, 0.00238, 0.00233, 0.00233, 0.00218, 0.00214, 0.0021, 0.0021, 0.00201, 0.002, 0.00194, 0.00187, 0.00173, 0.00172, 0.00171, 0.00161, 0.00161, 0.00159, 0.00159, 0.00159, 0.00158, 0.00153, 0.00152, 0.00151, 0.00146, 0.00141, 0.00139, 0.00139, 0.00136, 0.0013, 0.0013, 0.00128, 0.00119, 0.00116, 0.00113, 0.00112, 0.0011, 0.00109, 0.00108, 0.00107, 0.00106, 0.00105, 0.00105, 0.00101, 0.00101, 0.001, 0.001, 0.00098, 0.00097, 0.00095, 0.00094, 0.00094, 0.00094, 0.00092, 0.00092, 0.00089, 0.00088, 0.00087, 0.00086, 0.00083, 0.00082, 0.00081, 0.00079, 0.00078, 0.00077, 0.00077, 0.00075, 0.00073, 0.00072, 0.0007, 0.00068, 0.00065, 0.00065, 0.00064, 0.00063, 0.00063, 0.00063, 0.00062, 0.00062, 0.00061, 0.00061, 0.0006, 0.0006, 0.00058, 0.00058, 0.00058, 0.00056, 0.00056, 0.00055, 0.00054, 0.00053, 0.00052, 0.0005, 0.0005, 0.0005, 0.0005, 0.00048, 0.00048, 0.00048, 0.00048, 0.00047, 0.00046, 0.00046, 0.00046, 0.00045, 0.00045, 0.00044, 0.00044, 0.00044, 0.00044, 0.00044, 0.00043, 0.00043, 0.00043, 0.00042, 0.00042, 0.00042, 0.00041, 0.0004, 0.0004, 0.0004, 0.0004, 0.00039, 0.00038, 0.00038, 0.00038, 0.00037, 0.00037, 0.00037, 0.00037, 0.00036, 0.00036, 0.00036, 0.00035, 0.00035, 0.00034, 0.00034, 0.00033, 0.00032, 0.00032, 0.0003, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00028, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00026, 0.00026, 0.00026, 0.00026, 0.00026, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00023, 0.00022, 0.00022, 0.00022, 0.00022, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.00019, 0.00019, 0.00019, 0.00019, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00017, 0.00017, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.87483, 0.84621, 0.82211, 0.79902, 0.78075, 0.63674, 0.62605, 0.60498, 0.43889, 0.37044, 0.22935, 0.18705, 0.18072, 0.17, 0.0996, 0.08162, 0.07392, 0.07356, 0.06677, 0.06393, 0.05384, 0.0371, 0.02747, 0.02189, 0.02157, 0.02085, 0.02069, 0.01723, 0.01652, 0.01482, 0.01352, 0.01344, 0.01343, 0.01179, 0.01091, 0.01027, 0.01023, 0.00975, 0.00856, 0.0078, 0.00618, 0.00544, 0.00539, 0.00498, 0.00447, 0.00427, 0.00376, 0.00372, 0.00365, 0.00356, 0.0031, 0.00255, 0.0024, 0.00236, 0.00216, 0.00205, 0.00195, 0.0019, 0.00189, 0.00172, 0.00157, 0.00156, 0.00154, 0.00153, 0.0015, 0.00129, 0.00128, 0.00125, 0.0012, 0.00118, 0.00113, 0.00112, 0.00108, 0.00107, 0.00104, 0.00103, 0.00101, 0.00099, 0.00097, 0.00095, 0.00093, 0.00088, 0.00083, 0.0008, 0.00076, 0.00074, 0.00074, 0.00073, 0.00071, 0.00069, 0.00069, 0.00068, 0.00065, 0.00063, 0.00062, 0.00061, 0.0006, 0.00059, 0.00058, 0.00054, 0.00053, 0.00052, 0.00051, 0.0005, 0.00049, 0.00049, 0.00045, 0.00045, 0.00043, 0.00042, 0.00039, 0.00039, 0.00039, 0.00038, 0.00038, 0.00037, 0.00037, 0.00036, 0.00036, 0.00035, 0.00033, 0.00033, 0.00033, 0.00029, 0.00029, 0.00028, 0.00028, 0.00027, 0.00027, 0.00027, 0.00027, 0.00026, 0.00026, 0.00025, 0.00024, 0.00023, 0.00023, 0.00023, 0.00023, 0.00022, 0.00022, 0.00022, 0.00021, 0.00021, 0.00021, 0.0002, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05], [2, 2, 6, 6, 2, 5, 6, 6, 6, 6, 6, 1, 5, 6, 2, 5, 2, 6, 1, 5, 4, 2, 6, 1, 6, 6, 6, 2, 2, 6, 5, 5, 5, 6, 6, 2, 1, 6, 6, 6, 6, 6, 2, 5, 2, 1, 2, 2, 2, 2, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 12, 12, 12, 11, 11, 12, 11, 12, 12, 12, 11, 12, 13, 12, 17, 11, 16, 11, 11, 12, 16, 12, 11, 13, 12, 16, 17, 13, 16, 12, 11, 11, 15, 16, 11, 13, 13, 12, 12, 16, 12, 16, 12, 12, 12, 13, 13, 15, 11, 13, 17, 12, 11, 13, 15, 13, 13, 13, 11, 16, 16, 11, 11, 12, 16, 14, 11, 13, 13, 15, 13, 18, 12, 11, 18, 15, 17, 17, 11, 13, 13, 15, 13, 13, 16, 13, 12, 11, 13, 11, 15, 13, 13, 15, 16, 11, 13, 14, 15, 11, 13, 16, 17, 14, 12, 12, 15, 16, 17, 13, 16, 12, 13, 12, 11, 12, 13, 12, 12, 13, 13, 15, 16, 14, 12, 16, 13, 13, 13, 13, 13, 15, 12, 13, 15, 18, 17, 13, 17, 16, 12, 15, 13, 14, 11, 17, 11, 12, 13, 12, 13, 17, 11, 12, 13, 15, 18, 12, 11, 13, 13, 18, 15, 16, 13, 15, 18, 14, 11, 13, 13, 16, 13, 13, 11, 12, 12, 11, 18, 15, 16, 12, 11, 15, 11, 15, 16, 16, 13, 13, 13, 13, 13, 15, 11, 11, 18, 11, 13, 13, 13, 11, 13, 15, 12, 12, 13, 13, 12, 15, 15, 15, 17, 12, 13, 13, 13, 12, 12, 14, 11, 15, 13, 11, 12, 14, 13, 11, 14, 18, 18, 12, 18, 11, 17, 17, 15, 11, 18, 18, 12, 11, 13, 16, 13, 15, 15, 13, 15, 15, 18, 15, 11, 15, 12, 15, 11, 12, 12, 15, 16, 16, 13, 12, 12, 16, 17, 12, 11, 14, 17, 12, 13, 18, 11, 11, 12, 15, 12, 12, 13, 16, 13, 11, 15, 13, 11, 11, 12, 12, 12, 15, 15, 13, 15, 16, 12, 17, 11, 13, 13, 13, 17, 15, 13, 15, 11, 12, 11, 14, 12, 15, 14, 15, 13, 18, 13, 16, 14, 17, 16, 15, 11, 17, 15, 15, 17, 13, 13, 15, 12, 11, 11, 18, 15, 12, 11, 18, 15, 17, 16, 18, 12, 17, 14, 14, 15, 15, 11, 14, 13, 16, 17, 12, 12, 15, 14, 16, 13, 12, 16, 17, 17, 16, 18, 14, 12, 15, 15, 11, 12, 15, 12, 16, 15, 16, 13, 11, 12, 13, 15, 13, 11, 15, 13, 16, 12, 12, 13, 12, 15, 15, 13, 16, 14, 17, 18, 11, 17, 18], ["Others", "ArrowButton", "<PERSON><PERSON>", "Checkbox", "CloseButton", "Inputbox", "Icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MinimizeButton", "Radiobutton", "Cell", "Title", "SectionTitle", "SelectedTab", "BrowserTab", "SectionTab", "PopupTitle", "Logo", "ExcelSheet"], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], ["checked"]], "text": [[[0, 0, 1920, 937], [1760, 14, 1767, 24], [1816, 14, 1823, 24], [49, 24, 89, 37], [137, 19, 160, 41], [167, 19, 227, 42], [741, 25, 792, 39], [38, 84, 134, 105], [138, 84, 238, 106], [259, 89, 302, 106], [305, 88, 343, 103], [345, 89, 400, 106], [39, 149, 67, 161], [68, 149, 89, 161], [90, 149, 116, 164], [118, 152, 132, 161], [133, 149, 153, 161], [154, 149, 202, 161], [203, 152, 216, 161], [217, 149, 251, 161], [251, 149, 277, 161], [278, 152, 313, 164], [70, 199, 112, 213], [114, 200, 144, 213], [146, 200, 187, 216], [259, 200, 292, 213], [293, 200, 337, 213], [39, 268, 123, 287], [368, 270, 404, 284], [448, 268, 459, 287], [487, 268, 499, 287], [521, 266, 560, 288], [563, 267, 609, 284], [342, 305, 393, 322], [560, 305, 614, 322], [777, 307, 797, 319], [1098, 312, 1111, 325], [1209, 305, 1273, 323], [1428, 305, 1469, 322], [1644, 305, 1704, 322], [39, 335, 91, 349], [93, 339, 109, 349], [111, 335, 126, 349], [153, 336, 239, 351], [527, 352, 545, 365], [744, 352, 761, 365], [829, 355, 889, 373], [891, 356, 934, 373], [938, 356, 996, 377], [1178, 352, 1195, 365], [1394, 352, 1411, 365], [1611, 352, 1628, 365], [1652, 351, 1674, 365], [1832, 351, 1840, 366], [39, 375, 69, 391], [71, 374, 109, 391], [53, 406, 122, 421], [54, 422, 62, 434], [64, 422, 92, 437], [840, 436, 874, 449], [878, 436, 888, 447], [901, 434, 988, 450], [38, 470, 81, 487], [531, 462, 541, 475], [748, 462, 757, 475], [1182, 462, 1191, 475], [1398, 462, 1408, 475], [1615, 462, 1624, 475], [1832, 462, 1841, 475], [839, 486, 856, 499], [877, 486, 888, 497], [901, 485, 988, 500], [43, 500, 51, 512], [52, 500, 81, 515], [840, 536, 872, 552], [877, 536, 888, 547], [892, 534, 942, 551], [1219, 529, 1254, 544], [531, 572, 540, 585], [744, 572, 761, 585], [1178, 572, 1195, 585], [1394, 571, 1411, 585], [1611, 571, 1629, 585], [1828, 572, 1845, 585], [871, 588, 902, 601], [991, 588, 1035, 601], [527, 682, 545, 695], [744, 682, 762, 695], [961, 682, 978, 695], [1178, 682, 1195, 695], [1394, 682, 1411, 695], [1611, 682, 1627, 695], [1827, 682, 1845, 695], [527, 792, 545, 805], [744, 792, 762, 805], [961, 792, 978, 805], [1178, 792, 1195, 805], [1394, 792, 1412, 805], [1611, 792, 1628, 805], [1827, 792, 1845, 805], [71, 894, 124, 909], [124, 893, 182, 907]], ["1 1 MENU Ui Path\" Search Absence Calendar Trong <PERSON>h <PERSON> Click and drag on the calendar or select date range. Select Date Range View Teams Balances Today < > July 2023 Sunday Monday Tue X Thursday Friday Saturday Balance as of 07/06/2023 25 26 Select Date Range 28 29 30 Jul 1 Per Plan Volunteering 5 Days From * MM/DD/YYYY Total 2 3 5 6 7 8 To * MM/DD/YYYY 5 Days Type * (empty) Today 9 10 12 13 14 15 Next Cancel 16 17 18 19 20 21 22 23 24 25 26 27 28 29 Request Absence", "1", "1", "MENU", "Ui", "Path\"", "Search", "Absence", "Calendar", "Trong", "Canh", "<PERSON><PERSON><PERSON>", "Click", "and", "drag", "on", "the", "calendar", "or", "select", "date", "range.", "Select", "Date", "Range", "View", "Teams", "Balances", "Today", "<", ">", "July", "2023", "Sunday", "Monday", "<PERSON><PERSON>", "X", "Thursday", "Friday", "Saturday", "Balance", "as", "of", "07/06/2023", "25", "26", "Select", "Date", "Range", "28", "29", "30", "Jul", "1", "Per", "Plan", "Volunteering", "5", "Days", "From", "*", "MM/DD/YYYY", "Total", "2", "3", "5", "6", "7", "8", "To", "*", "MM/DD/YYYY", "5", "Days", "Type", "*", "(empty)", "Today", "9", "10", "12", "13", "14", "15", "Next", "Cancel", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "Request", "Absence"], [0.98, 0.99, 0.99, 0.99, 0.99, 0.54, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.74, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.95, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.97, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99]], "relations": [[[16, 21], [15, 20], [17, 14], [12, 11], [1, 9], [3, 8], [6, 7], [2, 5], [2, 4]], [0.99998, 0.99998, 0.99991, 0.99997, 0.99997, 0.98533, 0.97471, 0.99126, 0.99998], [[37, 266, 86, 21], [37, 334, 90, 16], [37, 469, 44, 18], [38, 374, 71, 18], [41, 500, 10, 12], [51, 500, 30, 15], [52, 406, 70, 15], [54, 421, 8, 12], [62, 421, 30, 15], [143, 322, 137, 43], [153, 334, 86, 16], [688, 5, 534, 53], [740, 23, 52, 15], [828, 355, 169, 23], [831, 528, 255, 35], [839, 435, 50, 15], [839, 485, 17, 13], [839, 534, 50, 18], [876, 486, 13, 11], [876, 533, 65, 19], [890, 422, 139, 44], [890, 472, 139, 44], [891, 533, 50, 19], [900, 433, 87, 17], [900, 483, 87, 16]], [0, 0, 0, 0, 0, 0, 0, 0, 0], ["Label", "Context", "Header"]], "content_type": null, "version": "23.4.10.0"}