[{"Text": "Email", "Value": null, "Id": 46, "ParentId": 35, "DOMId": "86", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|63\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 494, 41, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "clouddownload", "Value": null, "Id": 30, "ParentId": 25, "DOMId": "56", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|33\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "582, 1018, 24, 45", "Attributes": {"tag": "I", "class": "material-icons right"}, "Anchor": null}, {"Text": "Address", "Value": null, "Id": 40, "ParentId": 32, "DOMId": "71", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|48\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 273, 61, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "Last Name", "Value": null, "Id": 48, "ParentId": 36, "DOMId": "91", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|68\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 494, 79, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "First Name", "Value": null, "Id": 50, "ParentId": 37, "DOMId": "96", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|73\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 604, 80, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "Phone Number", "Value": null, "Id": 42, "ParentId": 33, "DOMId": "76", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|53\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 384, 111, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "Company Name", "Value": null, "Id": 38, "ParentId": 31, "DOMId": "66", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|43\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 273, 124, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "Role in Company", "Value": null, "Id": 44, "ParentId": 34, "DOMId": "81", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|58\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 384, 128, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}, {"Text": "Download Excel", "Value": null, "Id": 29, "ParentId": 25, "DOMId": "55", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|32\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "208, 1026, 175, 27", "Attributes": {}, "Anchor": null}, {"Text": null, "Value": null, "Id": 28, "ParentId": 22, "DOMId": "98", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|75\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOM", "Area": "760, 711, 145, 45", "Attributes": {"tag": "INPUT", "type": "submit", "class": "btn uiColorButton"}, "Anchor": null}, {"Text": "EN", "Value": null, "Id": 24, "ParentId": 16, "DOMId": "39", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|24\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "None", "Area": "469, 207, 152, 58", "Attributes": {"tag": "DIV", "class": "col l3 hide-on-med-and-down right dropButton"}, "Anchor": null}, {"Text": "Input Forms", "Value": null, "Id": 9, "ParentId": 4, "DOMId": "23", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|9\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "1667, 128, 148, 80", "Attributes": {"tag": "A"}, "Anchor": null}, {"Text": "Shortest Path", "Value": null, "Id": 10, "ParentId": 5, "DOMId": "25", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|11\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "1816, 128, 162, 80", "Attributes": {"tag": "A"}, "Anchor": null}, {"Text": "Movie Search", "Value": null, "Id": 11, "ParentId": 6, "DOMId": "27", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|13\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "1978, 128, 163, 80", "Attributes": {"tag": "A"}, "Anchor": null}, {"Text": "RPA Stock Market", "Value": null, "Id": 13, "ParentId": 8, "DOMId": "31", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|17\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "2342, 128, 197, 80", "Attributes": {"tag": "A"}, "Anchor": null}, {"Text": "Invoice Extraction", "Value": null, "Id": 12, "ParentId": 7, "DOMId": "29", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|15\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "2141, 128, 201, 80", "Attributes": {"tag": "A"}, "Anchor": null}, {"Text": "RPA Challenge", "Value": null, "Id": 2, "ParentId": 1, "DOMId": "20", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|6\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "0, 128, 311, 80", "Attributes": {"tag": "A", "class": "brand-logo"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 25, "ParentId": 21, "DOMId": "54", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|32\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOMFeatures", "Area": "14, 1018, 607, 45", "Attributes": {"tag": "A", "class": " col s12 m12 l12 btn waves-effect waves-light uiColorPrimary center"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 39, "ParentId": 31, "DOMId": "67", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|44\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "774, 297, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Company Name", "Value": null, "Id": 38, "ParentId": 31, "DOMId": "66", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|43\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 273, 124, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 41, "ParentId": 32, "DOMId": "72", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|49\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "1339, 297, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Address", "Value": null, "Id": 40, "ParentId": 32, "DOMId": "71", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|48\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 273, 61, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 43, "ParentId": 33, "DOMId": "77", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|54\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "774, 408, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Phone Number", "Value": null, "Id": 42, "ParentId": 33, "DOMId": "76", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|53\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 384, 111, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 45, "ParentId": 34, "DOMId": "82", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|59\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "1339, 408, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Role in Company", "Value": null, "Id": 44, "ParentId": 34, "DOMId": "81", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|58\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 384, 128, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 47, "ParentId": 35, "DOMId": "87", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|64\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "774, 518, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Email", "Value": null, "Id": 46, "ParentId": 35, "DOMId": "86", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|63\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 494, 41, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 49, "ParentId": 36, "DOMId": "92", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|69\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "1339, 518, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "Last Name", "Value": null, "Id": 48, "ParentId": 36, "DOMId": "91", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|68\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "1339, 494, 79, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": null, "Value": null, "Id": 51, "ParentId": 37, "DOMId": "97", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|74\\\\\"}\",\"Type\":2}", "ElementType": "InputBox", "ElementTypeSource": "DOM", "Area": "774, 628, 537, 57", "Attributes": {"tag": "INPUT", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": {"Text": "First Name", "Value": null, "Id": 50, "ParentId": 37, "DOMId": "96", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|73\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "774, 604, 80, 21", "Attributes": {"tag": "LABEL"}, "Anchor": null}}, {"Text": "Instructions", "Value": null, "Id": 23, "ParentId": 16, "DOMId": "38", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|23\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "14, 207, 455, 70", "Attributes": {"tag": "DIV", "class": "col s12 m12 l9 "}, "Anchor": null}, {"Text": "Good luck", "Value": null, "Id": 20, "ParentId": 15, "DOMId": "52", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|30\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "14, 917, 607, 63", "Attributes": {"tag": "DIV", "class": "instructionsText"}, "Anchor": null}, {"Text": "START", "Value": null, "Id": 26, "ParentId": 21, "DOMId": "59", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|36\\\\\"}\",\"Type\":2}", "ElementType": "<PERSON><PERSON>", "ElementTypeSource": "DOM", "Area": "14, 1075, 607, 68", "Attributes": {"tag": "BUTTON", "class": "waves-effect col s12 m12 l12 btn-large uiColorButton"}, "Anchor": null}, {"Text": "1. The goal of this challenge is to create a workflow that will input data from a spreadsheet into the form fields on the screen.", "Value": null, "Id": 17, "ParentId": 15, "DOMId": "49", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|27\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "14, 305, 607, 179", "Attributes": {"tag": "DIV", "class": "instructionsText"}, "Anchor": null}, {"Text": "2. Beware The fields will change position on the screen after every submission throughout 10 rounds thus the workflow must correctly identify where each spreadsheet record must be typed every time.", "Value": null, "Id": 18, "ParentId": 15, "DOMId": "50", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|28\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "14, 483, 607, 217", "Attributes": {"tag": "DIV", "class": "instructionsText"}, "Anchor": null}, {"Text": "3. The actual countdown of the challenge will begin once you click the Start button until then you may submit the form as many times as you wish without receiving penalties.", "Value": null, "Id": 19, "ParentId": 15, "DOMId": "51", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|29\\\\\"}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "14, 700, 607, 217", "Attributes": {"tag": "DIV", "class": "instructionsText"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 4, "ParentId": 3, "DOMId": "22", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|8\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1667, 128, 148, 80", "Attributes": {"tag": "LI"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 5, "ParentId": 3, "DOMId": "24", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|10\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1816, 128, 162, 80", "Attributes": {"tag": "LI"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 6, "ParentId": 3, "DOMId": "26", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|12\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1978, 128, 163, 80", "Attributes": {"tag": "LI"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 8, "ParentId": 3, "DOMId": "30", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|16\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "2342, 128, 197, 80", "Attributes": {"tag": "LI"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 7, "ParentId": 3, "DOMId": "28", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|14\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "2141, 128, 201, 80", "Attributes": {"tag": "LI"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 16, "ParentId": 15, "DOMId": "37", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|22\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "14, 207, 607, 70", "Attributes": {"tag": "DIV", "class": "row"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 21, "ParentId": 15, "DOMId": "53", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|31\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "14, 981, 607, 94", "Attributes": {"tag": "DIV"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 31, "ParentId": 27, "DOMId": "65", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|42\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "774, 269, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 32, "ParentId": 27, "DOMId": "70", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|47\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1339, 269, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 33, "ParentId": 27, "DOMId": "75", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|52\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "774, 380, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 34, "ParentId": 27, "DOMId": "80", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|57\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1339, 380, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 35, "ParentId": 27, "DOMId": "85", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|62\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "774, 490, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 36, "ParentId": 27, "DOMId": "90", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|67\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1339, 490, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 37, "ParentId": 27, "DOMId": "95", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|72\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "774, 600, 537, 110", "Attributes": {"tag": "DIV", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 3, "ParentId": 1, "DOMId": "21", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|7\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "1667, 128, 872, 80", "Attributes": {"tag": "UL", "class": "right hide-on-med-and-down"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 1, "ParentId": -1, "DOMId": "19", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|5\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "0, 128, 2539, 80", "Attributes": {"tag": "DIV", "class": "nav-wrapper uiColorPrimary"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 27, "ParentId": 22, "DOMId": "62", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|39\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "760, 269, 1130, 442", "Attributes": {"tag": "DIV", "class": "row"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 22, "ParentId": 14, "DOMId": "61", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|38\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "760, 269, 1130, 487", "Attributes": {"tag": "FORM", "class": "ng-untouched ng-pristine ng-invalid"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 15, "ParentId": 14, "DOMId": "36", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|21\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "0, 207, 635, 1252", "Attributes": {"tag": "DIV", "class": "instructions col s3 m3 l3 uiColorSecondary"}, "Anchor": null}, {"Text": null, "Value": null, "Id": 14, "ParentId": -1, "DOMId": "35", "DriverId": "{\"Data\":\"{\\\\\"BrowserType\\\\\": 3,\\\\\"ElementId\\\\\":\\\\\"*********|0|20\\\\\"}\",\"Type\":2}", "ElementType": "Container", "ElementTypeSource": "DOM", "Area": "0, 207, 2539, 1252", "Attributes": {"tag": "DIV", "class": "row parent"}, "Anchor": null}]