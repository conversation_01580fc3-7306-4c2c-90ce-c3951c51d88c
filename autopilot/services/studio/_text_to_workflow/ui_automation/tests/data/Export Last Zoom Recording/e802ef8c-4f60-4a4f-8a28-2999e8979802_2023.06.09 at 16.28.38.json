{"SiteTitle": "My Profile - Zoom", "SiteURL": "https://uipath.zoom.us/profile", "Action": "click", "ElementId": 89, "RawDOM": {"Children": [{"Children": [{"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "About", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2786, 140, 13", "ClientRegion": "412, 2683, 140, 13", "Id": 23, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Blog", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2811, 140, 16", "ClientRegion": "412, 2708, 140, 16", "Id": 46, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Customers", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2827, 140, 16", "ClientRegion": "412, 2724, 140, 16", "Id": 47, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Our Team", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2843, 140, 16", "ClientRegion": "412, 2740, 140, 16", "Id": 48, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Careers", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2858, 140, 16", "ClientRegion": "412, 2755, 140, 16", "Id": 49, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Integrations", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2874, 140, 16", "ClientRegion": "412, 2771, 140, 16", "Id": 50, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Partners", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2890, 140, 16", "ClientRegion": "412, 2787, 140, 16", "Id": 51, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Investors", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2905, 140, 16", "ClientRegion": "412, 2802, 140, 16", "Id": 52, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Press", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2921, 140, 16", "ClientRegion": "412, 2818, 140, 16", "Id": 53, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "ESG Responsibillity", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2937, 140, 16", "ClientRegion": "412, 2834, 140, 16", "Id": 54, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Media Kit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2952, 140, 16", "ClientRegion": "412, 2849, 140, 16", "Id": 55, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "How to Videos", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2968, 140, 16", "ClientRegion": "412, 2865, 140, 16", "Id": 56, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Developer Platform", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 2984, 140, 16", "ClientRegion": "412, 2881, 140, 16", "Id": 57, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Ventures", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 3000, 140, 16", "ClientRegion": "412, 2897, 140, 16", "Id": 58, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2332, 2811, 140, 204", "ClientRegion": "412, 2708, 140, 204", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2332, 2756, 190, 300", "ClientRegion": "412, 2653, 190, 300", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Download", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2786, 160, 13", "ClientRegion": "602, 2683, 160, 13", "Id": 25, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meetings Client", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2811, 160, 16", "ClientRegion": "602, 2708, 160, 16", "Id": 59, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Rooms Client", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2827, 160, 16", "ClientRegion": "602, 2724, 160, 16", "Id": 60, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Browser Extension", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2843, 160, 16", "ClientRegion": "602, 2740, 160, 16", "Id": 61, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Outlook Plug-in", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2858, 160, 16", "ClientRegion": "602, 2755, 160, 16", "Id": 62, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Lync Plug-in", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2874, 160, 16", "ClientRegion": "602, 2771, 160, 16", "Id": 63, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Android App", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2890, 160, 16", "ClientRegion": "602, 2787, 160, 16", "Id": 64, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Virtual Backgrounds", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2522, 2905, 160, 16", "ClientRegion": "602, 2802, 160, 16", "Id": 65, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2522, 2811, 160, 110", "ClientRegion": "602, 2708, 160, 110", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2522, 2756, 210, 300", "ClientRegion": "602, 2653, 210, 300", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Sales", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2786, 150, 13", "ClientRegion": "812, 2683, 150, 13", "Id": 27, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "1.888.799.8854", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2811, 150, 16", "ClientRegion": "812, 2708, 150, 16", "Id": 66, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Contact Sales", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2827, 150, 16", "ClientRegion": "812, 2724, 150, 16", "Id": 67, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Plans  Pricing", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2843, 150, 16", "ClientRegion": "812, 2740, 150, 16", "Id": 68, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Request a Demo", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2858, 150, 16", "ClientRegion": "812, 2755, 150, 16", "Id": 69, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Webinars and Events", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2732, 2874, 150, 16", "ClientRegion": "812, 2771, 150, 16", "Id": 70, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2732, 2811, 150, 79", "ClientRegion": "812, 2708, 150, 79", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2732, 2756, 200, 300", "ClientRegion": "812, 2653, 200, 300", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Support", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2786, 150, 13", "ClientRegion": "1012, 2683, 150, 13", "Id": 29, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Test Zoom", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2811, 150, 16", "ClientRegion": "1012, 2708, 150, 16", "Id": 71, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Account", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2827, 150, 16", "ClientRegion": "1012, 2724, 150, 16", "Id": 72, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Support Center", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2843, 150, 16", "ClientRegion": "1012, 2740, 150, 16", "Id": 73, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Learning Center", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2858, 150, 16", "ClientRegion": "1012, 2755, 150, 16", "Id": 74, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "<PERSON><PERSON><PERSON>", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2874, 150, 16", "ClientRegion": "1012, 2771, 150, 16", "Id": 75, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Contact Us", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2890, 150, 16", "ClientRegion": "1012, 2787, 150, 16", "Id": 76, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Accessibility", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2905, 150, 16", "ClientRegion": "1012, 2802, 150, 16", "Id": 77, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Developer Support", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2921, 150, 16", "ClientRegion": "1012, 2818, 150, 16", "Id": 78, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Privacy, Security, Legal Policies, and Modern Slavery Act Transparency Statement", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2932, 2937, 150, 63", "ClientRegion": "1012, 2834, 150, 63", "Id": 79, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2932, 2811, 150, 188", "ClientRegion": "1012, 2708, 150, 188", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2932, 2756, 200, 300", "ClientRegion": "1012, 2653, 200, 300", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Language", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3132, 2786, 280, 13", "ClientRegion": "1212, 2683, 280, 13", "Id": 31, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "English", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3148, 2816, 40, 14", "ClientRegion": "1228, 2713, 40, 14", "Id": 144, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "3132, 2756, 280, 300", "ClientRegion": "1212, 2653, 280, 300", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2332, 2756, 1080, 300", "ClientRegion": "412, 2653, 1080, 300", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Copyright 2023 Zoom Video Communications, Inc. All rights reserved. Terms Privacy Trust Center Acceptable Use Guidelines Legal  Compliance Do Not Sell/Share My Personal Information Cookie Preferences", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2332, 3056, 1238, 16", "ClientRegion": "412, 2953, 1238, 16", "Id": 14, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 2756, 1903, 380", "ClientRegion": "0, 2653, 1903, 380", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Support", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3333, 103, 75, 40", "ClientRegion": "1413, 0, 75, 40", "Id": 15, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "1.888.799.8854", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3408, 103, 125, 40", "ClientRegion": "1488, 0, 125, 40", "Id": 16, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Contact Sales", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3553, 103, 116, 40", "ClientRegion": "1633, 0, 116, 40", "Id": 17, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Request a Demo", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3669, 103, 154, 40", "ClientRegion": "1749, 0, 154, 40", "Id": 18, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Search", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3280, 103, 48, 40", "ClientRegion": "1360, 0, 48, 40", "Id": 108, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "3240, 103, 583, 40", "ClientRegion": "1320, 0, 583, 40", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Products", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2089, 143, 76, 64", "ClientRegion": "169, 40, 76, 64", "Id": 81, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Solutions", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2194, 143, 79, 64", "ClientRegion": "274, 40, 79, 64", "Id": 82, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Resources", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2303, 143, 89, 64", "ClientRegion": "383, 40, 89, 64", "Id": 83, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Plans  Pricing", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2422, 166, 117, 20", "ClientRegion": "502, 63, 117, 20", "Id": 36, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2074, 143, 480, 65", "ClientRegion": "154, 40, 480, 65", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Schedule", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3518, 143, 105, 64", "ClientRegion": "1598, 40, 105, 64", "Id": 37, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Join", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3624, 143, 64, 64", "ClientRegion": "1704, 40, 64, 64", "Id": 38, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Host", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3703, 164, 41, 21", "ClientRegion": "1783, 61, 41, 21", "Id": 125, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "3518, 143, 305, 65", "ClientRegion": "1598, 40, 305, 65", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 143, 1903, 65", "ClientRegion": "0, 40, 1903, 65", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Logo", "Href": null, "ElementType": "Icon", "AbsoluteRegion": "1944, 162, 110, 25", "ClientRegion": "24, 59, 110, 25", "Id": 32, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 103, 1903, 106", "ClientRegion": "0, 0, 1903, 106", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Profile", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 215, 294, 40", "ClientRegion": "0, 112, 294, 40", "Id": 84, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meetings", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 257, 294, 40", "ClientRegion": "0, 154, 294, 40", "Id": 85, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Webinars", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 299, 294, 40", "ClientRegion": "0, 196, 294, 40", "Id": 86, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Personal Audio Conference", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 341, 294, 40", "ClientRegion": "0, 238, 294, 40", "Id": 87, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Personal Contacts", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 383, 294, 40", "ClientRegion": "0, 280, 294, 40", "Id": 88, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Recordings", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 425, 294, 40", "ClientRegion": "0, 322, 294, 40", "Id": 89, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Settings", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 529, 294, 40", "ClientRegion": "0, 426, 294, 40", "Id": 90, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Scheduler", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 571, 294, 40", "ClientRegion": "0, 468, 294, 40", "Id": 91, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Reports", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 613, 294, 40", "ClientRegion": "0, 510, 294, 40", "Id": 92, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Account Profile", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1920, 655, 294, 40", "ClientRegion": "0, 552, 294, 40", "Id": 93, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meeting Summary with Zoom IQ", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1954, 478, 157, 38", "ClientRegion": "34, 375, 157, 38", "Id": 126, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 215, 294, 482", "ClientRegion": "0, 112, 294, 482", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Learning Center", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1955, 775, 243, 21", "ClientRegion": "35, 672, 243, 21", "Id": 94, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Video Tutorials", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1955, 812, 243, 21", "ClientRegion": "35, 709, 243, 21", "Id": 95, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Knowledge Base", "Href": null, "ElementType": "Text", "AbsoluteRegion": "1955, 849, 243, 21", "ClientRegion": "35, 746, 243, 21", "Id": 96, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 745, 294, 141", "ClientRegion": "0, 642, 294, 141", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 215, 294, 671", "ClientRegion": "0, 112, 294, 671", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Personal", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2268, 555, 1507, 32", "ClientRegion": "348, 452, 1507, 32", "Id": 98, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zmail", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 685, 280, 32", "ClientRegion": "360, 582, 280, 32", "Id": 129, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Not set", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 692, 50, 18", "ClientRegion": "640, 589, 50, 18", "Id": 163, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Add", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3703, 685, 60, 32", "ClientRegion": "1783, 582, 60, 32", "Id": 164, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 685, 1203, 32", "ClientRegion": "640, 582, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 685, 1495, 32", "ClientRegion": "360, 582, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Language", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 749, 280, 32", "ClientRegion": "360, 646, 280, 32", "Id": 130, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "English", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 756, 50, 18", "ClientRegion": "640, 653, 50, 18", "Id": 165, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 749, 59, 32", "ClientRegion": "1784, 646, 59, 32", "Id": 166, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 749, 1203, 32", "ClientRegion": "640, 646, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 749, 1495, 32", "ClientRegion": "360, 646, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Time Zone", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 813, 280, 32", "ClientRegion": "360, 710, 280, 32", "Id": 131, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "GMT300 Bucharest", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 820, 155, 18", "ClientRegion": "640, 717, 155, 18", "Id": 167, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 813, 59, 32", "ClientRegion": "1784, 710, 59, 32", "Id": 168, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 813, 1203, 32", "ClientRegion": "640, 710, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 813, 1495, 32", "ClientRegion": "360, 710, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Date Format", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 877, 280, 32", "ClientRegion": "360, 774, 280, 32", "Id": 132, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "mm/dd/yyyy", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 884, 82, 18", "ClientRegion": "640, 781, 82, 18", "Id": 169, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": " Example 06/09/2023", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2674, 884, 154, 18", "ClientRegion": "754, 781, 154, 18", "Id": 170, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 877, 59, 32", "ClientRegion": "1784, 774, 59, 32", "Id": 171, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 877, 1203, 32", "ClientRegion": "640, 774, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 877, 1495, 32", "ClientRegion": "360, 774, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Time Format", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 941, 280, 32", "ClientRegion": "360, 838, 280, 32", "Id": 133, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Use 12-hour time Example 0200 PM", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 948, 261, 18", "ClientRegion": "640, 845, 261, 18", "Id": 172, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 941, 59, 32", "ClientRegion": "1784, 838, 59, 32", "Id": 173, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 941, 1203, 32", "ClientRegion": "640, 838, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 941, 1495, 32", "ClientRegion": "360, 838, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Add Phone Number", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3609, 621, 166, 32", "ClientRegion": "1689, 518, 166, 32", "Id": 162, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Phone", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 621, 280, 32", "ClientRegion": "360, 518, 280, 32", "Id": 190, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Not set", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 621, 1037, 32", "ClientRegion": "640, 518, 1037, 32", "Id": 191, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 621, 1317, 32", "ClientRegion": "360, 518, 1317, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 621, 1495, 64", "ClientRegion": "360, 518, 1495, 64", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 597, 1507, 376", "ClientRegion": "348, 494, 1507, 376", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 531, 1507, 442", "ClientRegion": "348, 428, 1507, 442", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meeting", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2268, 1029, 1507, 32", "ClientRegion": "348, 926, 1507, 32", "Id": 100, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Personal Meeting ID", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1095, 280, 104", "ClientRegion": "360, 992, 280, 104", "Id": 134, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "*** *** *581", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1102, 69, 18", "ClientRegion": "640, 999, 69, 18", "Id": 222, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2637, 1097, 32, 30", "ClientRegion": "717, 994, 32, 30", "Id": 223, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1095, 1144, 32", "ClientRegion": "640, 992, 1144, 32", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "https//uipath.zoom.us/j/*******581", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1135, 700, 21", "ClientRegion": "640, 1032, 700, 21", "Id": 207, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Disabled", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1166, 14, 32", "ClientRegion": "640, 1063, 14, 32", "Id": 208, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Use this ID for instant meetings", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2578, 1174, 215, 18", "ClientRegion": "658, 1071, 215, 18", "Id": 209, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1095, 1144, 104", "ClientRegion": "640, 992, 1144, 104", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 1095, 59, 32", "ClientRegion": "1784, 992, 59, 32", "Id": 193, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1095, 1203, 104", "ClientRegion": "640, 992, 1203, 104", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1095, 1495, 104", "ClientRegion": "360, 992, 1495, 104", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Personal Link", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1238, 92, 18", "ClientRegion": "360, 1135, 92, 18", "Id": 175, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2375, 1237, 32, 22", "ClientRegion": "455, 1134, 32, 22", "Id": 176, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1238, 127, 18", "ClientRegion": "360, 1135, 127, 18", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Not set yet.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1231, 1098, 32", "ClientRegion": "640, 1128, 1098, 32", "Id": 177, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Customize", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3674, 1237, 73, 20", "ClientRegion": "1754, 1134, 73, 20", "Id": 194, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1231, 1203, 32", "ClientRegion": "640, 1128, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1231, 1495, 32", "ClientRegion": "360, 1128, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Host Key", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1295, 280, 32", "ClientRegion": "360, 1192, 280, 32", "Id": 135, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "********", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1302, 43, 18", "ClientRegion": "640, 1199, 43, 18", "Id": 179, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2611, 1297, 32, 30", "ClientRegion": "691, 1194, 32, 30", "Id": 180, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 1295, 59, 32", "ClientRegion": "1784, 1192, 59, 32", "Id": 181, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1295, 1203, 32", "ClientRegion": "640, 1192, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1295, 1495, 32", "ClientRegion": "360, 1192, 1495, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1071, 1507, 256", "ClientRegion": "348, 968, 1507, 256", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1005, 1507, 322", "ClientRegion": "348, 902, 1507, 322", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Account", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2268, 1383, 1507, 32", "ClientRegion": "348, 1280, 1507, 32", "Id": 102, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "License", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1449, 280, 176", "ClientRegion": "360, 1346, 280, 176", "Id": 136, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meeting", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1497, 220, 32", "ClientRegion": "640, 1394, 220, 32", "Id": 196, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "1000 participants", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2780, 1504, 120, 18", "ClientRegion": "860, 1401, 120, 18", "Id": 212, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2908, 1504, 32, 22", "ClientRegion": "988, 1401, 32, 22", "Id": 213, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2780, 1497, 983, 32", "ClientRegion": "860, 1394, 983, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1497, 1203, 32", "ClientRegion": "640, 1394, 1203, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Webinars", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1545, 220, 32", "ClientRegion": "640, 1442, 220, 32", "Id": 198, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "1000 attendees", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2780, 1552, 111, 18", "ClientRegion": "860, 1449, 111, 18", "Id": 214, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2899, 1552, 32, 22", "ClientRegion": "979, 1449, 32, 22", "Id": 215, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2780, 1545, 983, 32", "ClientRegion": "860, 1442, 983, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1545, 1203, 32", "ClientRegion": "640, 1442, 1203, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom Whiteboard", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1593, 220, 32", "ClientRegion": "640, 1490, 220, 32", "Id": 200, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Unlimited editable boards with standard features", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2780, 1593, 983, 32", "ClientRegion": "860, 1490, 983, 32", "Id": 201, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1593, 1203, 32", "ClientRegion": "640, 1490, 1203, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Licensed", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1456, 63, 18", "ClientRegion": "640, 1353, 63, 18", "Id": 210, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2631, 1458, 24, 18", "ClientRegion": "711, 1355, 24, 18", "Id": 211, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1449, 1203, 32", "ClientRegion": "640, 1346, 1203, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1449, 1203, 176", "ClientRegion": "640, 1346, 1203, 176", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1449, 1495, 176", "ClientRegion": "360, 1346, 1495, 176", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "********", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1657, 1215, 32", "ClientRegion": "640, 1554, 1215, 32", "Id": 137, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Account NumberAccount No.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1664, 84, 18", "ClientRegion": "360, 1561, 84, 18", "Id": 157, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1657, 1495, 32", "ClientRegion": "360, 1554, 1495, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1425, 1507, 264", "ClientRegion": "348, 1322, 1507, 264", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1359, 1507, 330", "ClientRegion": "348, 1256, 1507, 330", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Sign In", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2268, 1745, 1507, 32", "ClientRegion": "348, 1642, 1507, 32", "Id": 104, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Sign-In Email", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1811, 280, 32", "ClientRegion": "360, 1708, 280, 32", "Id": 138, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "ste***uipath.com", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1818, 126, 18", "ClientRegion": "640, 1715, 126, 18", "Id": 224, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2702, 1814, 32, 30", "ClientRegion": "782, 1711, 32, 30", "Id": 225, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1811, 174, 32", "ClientRegion": "640, 1708, 174, 32", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1811, 1495, 32", "ClientRegion": "360, 1708, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Linked Accounts", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1876, 280, 32", "ClientRegion": "360, 1773, 280, 32", "Id": 139, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Single Sign-On", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 1876, 1215, 32", "ClientRegion": "640, 1773, 1215, 32", "Id": 140, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1876, 1495, 32", "ClientRegion": "360, 1773, 1495, 32", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Signed-<PERSON> <PERSON><PERSON>", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 1940, 280, 33", "ClientRegion": "360, 1837, 280, 33", "Id": 141, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Sign Me Out From All Devices", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2544, 1941, 235, 32", "ClientRegion": "624, 1838, 235, 32", "Id": 185, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2787, 1948, 24, 18", "ClientRegion": "867, 1845, 24, 18", "Id": 186, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 1940, 1203, 33", "ClientRegion": "640, 1837, 1203, 33", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 1940, 1495, 33", "ClientRegion": "360, 1837, 1495, 33", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1787, 1507, 185", "ClientRegion": "348, 1684, 1507, 185", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 1721, 1507, 251", "ClientRegion": "348, 1618, 1507, 251", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Others", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2268, 2029, 1507, 32", "ClientRegion": "348, 1926, 1507, 32", "Id": 106, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Calendar and Contacts Integration", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 2095, 280, 128", "ClientRegion": "360, 1992, 280, 128", "Id": 142, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "We support the following services Google Calendar, Microsoft Exchange, and Microsoft Office 365", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 2095, 1203, 32", "ClientRegion": "640, 1992, 1203, 32", "Id": 217, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "If you want to add your contacts by importing a CSV file, go to Personal Contacts.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 2127, 1203, 32", "ClientRegion": "640, 2024, 1203, 32", "Id": 218, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2095, 1203, 64", "ClientRegion": "640, 1992, 1203, 64", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Configure Calendar and Contacts Service", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 2191, 319, 32", "ClientRegion": "640, 2088, 319, 32", "Id": 203, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2095, 1203, 128", "ClientRegion": "640, 1992, 1203, 128", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 2095, 1495, 128", "ClientRegion": "360, 1992, 1495, 128", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Data Configuration", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2280, 2255, 280, 437", "ClientRegion": "360, 2152, 280, 437", "Id": 143, "ImageBase64": null}, {"Children": [{"Children": [{"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Meetings/webinars/whiteboards data center", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 2262, 313, 18", "ClientRegion": "640, 2159, 313, 18", "Id": 219, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Zoom data centers process real-time meeting/webinar video, audio, and shared content when you host meetings and webinars. They can also process your Zoom Whiteboard data. Selecting all data centers can provide the best experience for participants joining from all regions. Opting out of data centers may limit participants joining from those regions from using CRC, Dial-in, Call Me, and Invite by Phone.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2560, 2295, 700, 105", "ClientRegion": "640, 2192, 700, 105", "Id": 220, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2255, 724, 149", "ClientRegion": "640, 2152, 724, 149", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "3723, 2255, 40, 24", "ClientRegion": "1803, 2152, 40, 24", "Id": 221, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2255, 1203, 149", "ClientRegion": "640, 2152, 1203, 149", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Australia", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2425, 84, 21", "ClientRegion": "640, 2322, 84, 21", "Id": 241, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Brazil", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2457, 62, 21", "ClientRegion": "640, 2354, 62, 21", "Id": 242, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Canada", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2489, 77, 21", "ClientRegion": "640, 2386, 77, 21", "Id": 243, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "China", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2521, 64, 21", "ClientRegion": "640, 2418, 64, 21", "Id": 244, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Germany", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2553, 86, 21", "ClientRegion": "640, 2450, 86, 21", "Id": 245, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Hong Kong SAR", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2585, 133, 21", "ClientRegion": "640, 2482, 133, 21", "Id": 246, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "India", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2617, 58, 21", "ClientRegion": "640, 2514, 58, 21", "Id": 247, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Ireland", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2560, 2649, 71, 21", "ClientRegion": "640, 2546, 71, 21", "Id": 248, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Japan", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2425, 66, 21", "ClientRegion": "947, 2322, 66, 21", "Id": 249, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Mexico", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2457, 74, 21", "ClientRegion": "947, 2354, 74, 21", "Id": 250, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Netherlands", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2489, 108, 21", "ClientRegion": "947, 2386, 108, 21", "Id": 251, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Singapore", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2521, 94, 21", "ClientRegion": "947, 2418, 94, 21", "Id": 252, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Switzerland", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2553, 103, 21", "ClientRegion": "947, 2450, 103, 21", "Id": 253, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "Taiwan", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2585, 70, 21", "ClientRegion": "947, 2482, 70, 21", "Id": 254, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "United States", "Href": null, "ElementType": "CheckBox", "AbsoluteRegion": "2867, 2617, 117, 21", "ClientRegion": "947, 2514, 117, 21", "Id": 255, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2420, 600, 256", "ClientRegion": "640, 2317, 600, 256", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2560, 2255, 1203, 437", "ClientRegion": "640, 2152, 1203, 437", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2280, 2255, 1495, 437", "ClientRegion": "360, 2152, 1495, 437", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 2071, 1507, 621", "ClientRegion": "348, 1968, 1507, 621", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 2005, 1507, 687", "ClientRegion": "348, 1902, 1507, 687", "Id": 0, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Edit", "Href": null, "ElementType": "Text", "AbsoluteRegion": "3704, 389, 59, 32", "ClientRegion": "1784, 286, 59, 32", "Id": 109, "ImageBase64": null}, {"Children": [{"Children": [], "Attributes": [], "IsRoot": false, "Text": "Full name<PERSON><PERSON><PERSON>", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2548, 379, 152, 26", "ClientRegion": "628, 276, 152, 26", "Id": 145, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "<PERSON>", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2548, 413, 152, 21", "ClientRegion": "628, 310, 152, 21", "Id": 146, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2548, 379, 152, 65", "ClientRegion": "628, 276, 152, 65", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 379, 1507, 128", "ClientRegion": "348, 276, 1507, 128", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": "When you join meetings, webinars, chats or channels hosted on Zoom, your profile information, including your name and profile picture, may be visible to other participants or members. Your name and email address will also be visible to the account owner and host when you join meetings, webinars, chats or channels on their account while youre signed in. The account owner and others in the meeting, webinar, chat or channel can share this information with apps and others.", "Href": null, "ElementType": "Text", "AbsoluteRegion": "2322, 255, 1067, 84", "ClientRegion": "402, 152, 1067, 84", "Id": 189, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "2268, 239, 1507, 2453", "ClientRegion": "348, 136, 1507, 2453", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 207, 1903, 2549", "ClientRegion": "0, 104, 1903, 2549", "Id": 0, "ImageBase64": null}], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "Container", "AbsoluteRegion": "1920, 103, 1903, 3033", "ClientRegion": "0, 0, 1903, 3033", "Id": 0, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "2316, 428, 32, 30", "ClientRegion": "396, 325, 32, 30", "Id": 256, "ImageBase64": null}, {"Children": [], "Attributes": [], "IsRoot": false, "Text": null, "Href": null, "ElementType": "<PERSON><PERSON>", "AbsoluteRegion": "3743, 1023, 60, 60", "ClientRegion": "1823, 920, 60, 60", "Id": 257, "ImageBase64": null}], "Attributes": [], "IsRoot": true, "Text": null, "Href": null, "ElementType": "None", "AbsoluteRegion": "0, 0, 0, 0", "ClientRegion": "0, 0, 0, 0", "Id": 0, "ImageBase64": null}, "ParsedDOM": "<Window><Container><Container><Container><Container><Text Id=\"23\" Text=\"About\" /><Container><Text Id=\"46\" Text=\"Zoom Blog\" /><Text Id=\"47\" Text=\"Customers\" /><Text Id=\"48\" Text=\"Our Team\" /><Text Id=\"49\" Text=\"Careers\" /><Text Id=\"50\" Text=\"Integrations\" /><Text Id=\"51\" Text=\"Partners\" /><Text Id=\"52\" Text=\"Investors\" /><Text Id=\"53\" Text=\"Press\" /><Text Id=\"54\" Text=\"ESG Responsibillity\" /><Text Id=\"55\" Text=\"Media Kit\" /><Text Id=\"56\" Text=\"How to Videos\" /><Text Id=\"57\" Text=\"Developer Platform\" /><Text Id=\"58\" Text=\"Zoom Ventures\" /></Container></Container><Container><Text Id=\"25\" Text=\"Download\" /><Container><Text Id=\"59\" Text=\"Meetings Client\" /><Text Id=\"60\" Text=\"Zoom Rooms Client\" /><Text Id=\"61\" Text=\"Browser Extension\" /><Text Id=\"62\" Text=\"Outlook Plug-in\" /><Text Id=\"63\" Text=\"Lync Plug-in\" /><Text Id=\"64\" Text=\"Android App\" /><Text Id=\"65\" Text=\"Zoom Virtual Backgrounds\" /></Container></Container><Container><Text Id=\"27\" Text=\"Sales\" /><Container><Text Id=\"66\" Text=\"1.888.799.8854\" /><Text Id=\"67\" Text=\"Contact Sales\" /><Text Id=\"68\" Text=\"Plans  Pricing\" /><Text Id=\"69\" Text=\"Request a Demo\" /><Text Id=\"70\" Text=\"Webinars and Events\" /></Container></Container><Container><Text Id=\"29\" Text=\"Support\" /><Container><Text Id=\"71\" Text=\"Test Zoom\" /><Text Id=\"72\" Text=\"Account\" /><Text Id=\"73\" Text=\"Support Center\" /><Text Id=\"74\" Text=\"Learning Center\" /><Text Id=\"75\" Text=\"Feedback\" /><Text Id=\"76\" Text=\"Contact Us\" /><Text Id=\"77\" Text=\"Accessibility\" /><Text Id=\"78\" Text=\"Developer Support\" /><Text Id=\"79\" Text=\"Privacy, Security, Legal Policies, and Modern Slavery Act Transparency Statement\" /></Container></Container><Container><Text Id=\"31\" Text=\"Language\" /><Text Id=\"144\" Text=\"English\" /></Container></Container><Text Id=\"14\" Text=\"Copyright 2023 Zoom Video Communications, Inc. All rights reserved. Terms Privacy Trust Center Acceptable Use Guidelines Legal  Compliance Do Not Sell/Share My Personal Information Cookie Preferences\" /></Container><Container><Container><Text Id=\"15\" Text=\"Support\" /><Text Id=\"16\" Text=\"1.888.799.8854\" /><Text Id=\"17\" Text=\"Contact Sales\" /><Text Id=\"18\" Text=\"Request a Demo\" /><Text Id=\"108\" Text=\"Search\" /></Container><Container><Container><Text Id=\"81\" Text=\"Products\" /><Text Id=\"82\" Text=\"Solutions\" /><Text Id=\"83\" Text=\"Resources\" /><Text Id=\"36\" Text=\"Plans  Pricing\" /></Container><Container><Text Id=\"37\" Text=\"Schedule\" /><Text Id=\"38\" Text=\"Join\" /><Text Id=\"125\" Text=\"Host\" /></Container></Container><Icon Id=\"32\" Text=\"Zoom Logo\" /></Container><Container><Container><Container><Text Id=\"84\" Text=\"Profile\" /><Text Id=\"85\" Text=\"Meetings\" /><Text Id=\"86\" Text=\"Webinars\" /><Text Id=\"87\" Text=\"Personal Audio Conference\" /><Text Id=\"88\" Text=\"Personal Contacts\" /><Text Id=\"89\" Text=\"Recordings\" /><Text Id=\"90\" Text=\"Settings\" /><Text Id=\"91\" Text=\"Scheduler\" /><Text Id=\"92\" Text=\"Reports\" /><Text Id=\"93\" Text=\"Account Profile\" /><Text Id=\"126\" Text=\"Meeting Summary with Zoom IQ\" /></Container><Container><Text Id=\"94\" Text=\"Zoom Learning Center\" /><Text Id=\"95\" Text=\"Video Tutorials\" /><Text Id=\"96\" Text=\"Knowledge Base\" /></Container></Container><Container><Container><Text Id=\"98\" Text=\"Personal\" /><Container><Container><Text Id=\"129\" Text=\"Zmail\" /><Container><Text Id=\"163\" Text=\"Not set\" /><Text Id=\"164\" Text=\"Add\" /></Container></Container><Container><Text Id=\"130\" Text=\"Language\" /><Container><Text Id=\"165\" Text=\"English\" /><Text Id=\"166\" Text=\"Edit\" /></Container></Container><Container><Text Id=\"131\" Text=\"Time Zone\" /><Container><Text Id=\"167\" Text=\"GMT300 Bucharest\" /><Text Id=\"168\" Text=\"Edit\" /></Container></Container><Container><Text Id=\"132\" Text=\"Date Format\" /><Container><Text Id=\"169\" Text=\"mm/dd/yyyy\" /><Text Id=\"170\" Text=\" Example 06/09/2023\" /><Text Id=\"171\" Text=\"Edit\" /></Container></Container><Container><Text Id=\"133\" Text=\"Time Format\" /><Container><Text Id=\"172\" Text=\"Use 12-hour time Example 0200 PM\" /><Text Id=\"173\" Text=\"Edit\" /></Container></Container><Container><Text Id=\"162\" Text=\"Add Phone Number\" /><Container><Text Id=\"190\" Text=\"Phone\" /><Text Id=\"191\" Text=\"Not set\" /></Container></Container></Container></Container><Container><Text Id=\"100\" Text=\"Meeting\" /><Container><Container><Text Id=\"134\" Text=\"Personal Meeting ID\" /><Container><Container><Container><Text Id=\"222\" Text=\"*** *** *581\" /><Button Id=\"223\" /></Container><Text Id=\"207\" Text=\"https//uipath.zoom.us/j/*******581\" /><Text Id=\"208\" Text=\"Disabled\" /><Text Id=\"209\" Text=\"Use this ID for instant meetings\" /></Container><Text Id=\"193\" Text=\"Edit\" /></Container></Container><Container><Container><Text Id=\"175\" Text=\"Personal Link\" /><Button Id=\"176\" /></Container><Container><Text Id=\"177\" Text=\"Not set yet.\" /><Text Id=\"194\" Text=\"Customize\" /></Container></Container><Container><Text Id=\"135\" Text=\"Host Key\" /><Container><Text Id=\"179\" Text=\"********\" /><Button Id=\"180\" /><Text Id=\"181\" Text=\"Edit\" /></Container></Container></Container></Container><Container><Text Id=\"102\" Text=\"Account\" /><Container><Container><Text Id=\"136\" Text=\"License\" /><Container><Container><Text Id=\"196\" Text=\"Meeting\" /><Container><Text Id=\"212\" Text=\"1000 participants\" /><Button Id=\"213\" /></Container></Container><Container><Text Id=\"198\" Text=\"Zoom Webinars\" /><Container><Text Id=\"214\" Text=\"1000 attendees\" /><Button Id=\"215\" /></Container></Container><Container><Text Id=\"200\" Text=\"Zoom Whiteboard\" /><Text Id=\"201\" Text=\"Unlimited editable boards with standard features\" /></Container><Container><Text Id=\"210\" Text=\"Licensed\" /><Button Id=\"211\" /></Container></Container></Container><Container><Text Id=\"137\" Text=\"********\" /><Text Id=\"157\" Text=\"Account NumberAccount No.\" /></Container></Container></Container><Container><Text Id=\"104\" Text=\"Sign In\" /><Container><Container><Text Id=\"138\" Text=\"Sign-In Email\" /><Container><Text Id=\"224\" Text=\"ste***uipath.com\" /><Button Id=\"225\" /></Container></Container><Container><Text Id=\"139\" Text=\"Linked Accounts\" /><Text Id=\"140\" Text=\"Single Sign-On\" /></Container><Container><Text Id=\"141\" Text=\"Signed-In Device\" /><Container><Text Id=\"185\" Text=\"Sign Me Out From All Devices\" /><Button Id=\"186\" /></Container></Container></Container></Container><Container><Text Id=\"106\" Text=\"Others\" /><Container><Container><Text Id=\"142\" Text=\"Calendar and Contacts Integration\" /><Container><Container><Text Id=\"217\" Text=\"We support the following services Google Calendar, Microsoft Exchange, and Microsoft Office 365\" /><Text Id=\"218\" Text=\"If you want to add your contacts by importing a CSV file, go to Personal Contacts.\" /></Container><Text Id=\"203\" Text=\"Configure Calendar and Contacts Service\" /></Container></Container><Container><Text Id=\"143\" Text=\"Data Configuration\" /><Container><Container><Container><Text Id=\"219\" Text=\"Meetings/webinars/whiteboards data center\" /><Text Id=\"220\" Text=\"Zoom data centers process real-time meeting/webinar video, audio, and shared content when you host meetings and webinars. They can also process your Zoom Whiteboard data. Selecting all data centers can provide the best experience for participants joining from all regions. Opting out of data centers may limit participants joining from those regions from using CRC, Dial-in, Call Me, and Invite by Phone.\" /></Container><CheckBox Id=\"221\" /></Container><Container><CheckBox Id=\"241\" Text=\"Australia\" /><CheckBox Id=\"242\" Text=\"Brazil\" /><CheckBox Id=\"243\" Text=\"Canada\" /><CheckBox Id=\"244\" Text=\"China\" /><CheckBox Id=\"245\" Text=\"Germany\" /><CheckBox Id=\"246\" Text=\"Hong Kong SAR\" /><CheckBox Id=\"247\" Text=\"India\" /><CheckBox Id=\"248\" Text=\"Ireland\" /><CheckBox Id=\"249\" Text=\"Japan\" /><CheckBox Id=\"250\" Text=\"Mexico\" /><CheckBox Id=\"251\" Text=\"Netherlands\" /><CheckBox Id=\"252\" Text=\"Singapore\" /><CheckBox Id=\"253\" Text=\"Switzerland\" /><CheckBox Id=\"254\" Text=\"Taiwan\" /><CheckBox Id=\"255\" Text=\"United States\" /></Container></Container></Container></Container></Container><Container><Text Id=\"109\" Text=\"Edit\" /><Container><Text Id=\"145\" Text=\"Full nameStefan Adam\" /><Text Id=\"146\" Text=\"Stefan Adam\" /></Container></Container><Text Id=\"189\" Text=\"When you join meetings, webinars, chats or channels hosted on Zoom, your profile information, including your name and profile picture, may be visible to other participants or members. Your name and email address will also be visible to the account owner and host when you join meetings, webinars, chats or channels on their account while youre signed in. The account owner and others in the meeting, webinar, chat or channel can share this information with apps and others.\" /></Container></Container></Container><Button Id=\"256\" /><Button Id=\"257\" /></Window>", "TypeIntoText": null}