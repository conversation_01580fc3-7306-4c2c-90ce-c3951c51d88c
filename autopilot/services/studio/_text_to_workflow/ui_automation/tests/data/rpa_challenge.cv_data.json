{"controls": [[[8, 722, 460, 60], [573, 464, 120, 41], [1002, 236, 406, 35], [582, 239, 407, 32], [581, 151, 407, 32], [582, 328, 405, 32], [1002, 321, 406, 39], [581, 410, 408, 38], [358, 70, 87, 42], [428, 686, 31, 25], [7, 679, 461, 40], [1900, 861, 21, 20], [1004, 149, 404, 34], [1900, 0, 20, 19], [8, 679, 458, 39], [538, 11, 658, 42], [1749, 5, 151, 61], [2, 716, 479, 62], [1596, 62, 153, 28], [1872, 859, 28, 20], [1142, 383, 271, 31], [1226, 100, 90, 23], [3, 479, 462, 29], [0, 507, 483, 28], [1784, 0, 120, 71], [3, 675, 468, 39], [1476, 100, 104, 23], [4, 678, 26, 36], [1761, 62, 144, 30], [1897, 839, 8, 40], [1126, 399, 263, 29], [1898, 853, 9, 23], [1871, 804, 31, 28], [1881, 853, 23, 26], [1073, 267, 321, 32], [0, 449, 37, 29], [1887, 859, 15, 17], [1879, 804, 21, 64], [1885, 807, 15, 31], [1750, 179, 155, 26], [1589, 86, 164, 25], [1222, 92, 95, 23], [1226, 108, 93, 23], [0, 370, 467, 31], [1084, 213, 311, 36], [1198, 177, 204, 29], [1890, 804, 24, 75], [1781, 209, 126, 29], [1704, 837, 213, 42], [1121, 180, 295, 41], [1474, 92, 106, 23], [1819, 875, 78, 6], [1826, 804, 71, 23], [1621, 100, 104, 22], [1617, 92, 111, 22], [596, 378, 394, 66], [1, 284, 35, 31], [1476, 108, 103, 23], [1087, 356, 313, 44], [1887, 757, 19, 122], [12, 205, 269, 31], [1586, 97, 168, 25], [1026, 317, 374, 39], [1215, 84, 108, 23], [1750, 163, 155, 26], [31, 675, 42, 39], [1900, 11, 7, 51], [1611, 188, 79, 23], [1584, 160, 164, 28], [1247, 94, 48, 34], [1887, 801, 17, 59], [1168, 180, 210, 89], [1119, 287, 283, 37], [1898, 806, 12, 73], [1556, 226, 238, 26], [1472, 86, 111, 20], [1576, 193, 213, 29], [0, 452, 473, 39], [1824, 836, 74, 21], [0, 501, 16, 34], [1619, 180, 91, 23], [1727, 100, 16, 31], [701, 559, 282, 29], [10, 677, 86, 39], [0, 337, 481, 31], [1622, 125, 94, 20], [1896, 854, 8, 22], [920, 479, 64, 29], [1219, 61, 120, 122], [1880, 724, 22, 144], [1737, 191, 170, 28], [1203, 413, 188, 31], [1825, 812, 70, 23], [1839, 796, 59, 50], [14, 677, 241, 39], [1225, 117, 98, 20], [820, 64, 69, 26], [1620, 108, 102, 21], [1750, 95, 154, 27], [1540, 210, 254, 28], [1572, 177, 197, 28], [1890, 616, 16, 211], [839, 75, 77, 20], [1681, 188, 66, 23], [739, 774, 1152, 23], [1814, 843, 86, 22], [604, 444, 378, 30], [1821, 235, 83, 23], [1827, 708, 78, 23], [1899, 743, 6, 62], [1560, 240, 222, 29], [1829, 732, 71, 23], [1821, 227, 81, 23], [1859, 702, 43, 179], [1821, 220, 81, 23], [1829, 716, 75, 23], [1726, 807, 192, 58], [0, 337, 16, 36], [0, 711, 5, 42], [1815, 854, 84, 25], [823, 61, 201, 26], [1825, 699, 79, 23], [1815, 171, 89, 23], [1897, 713, 12, 92], [1891, 12, 11, 53], [1619, 117, 98, 20], [1729, 62, 27, 50], [1023, 193, 370, 73], [1228, 116, 92, 65], [1872, 832, 29, 41], [1578, 113, 174, 24], [1871, 814, 31, 35], [1891, 108, 16, 210], [1679, 180, 64, 23], [1539, 171, 54, 24], [1476, 116, 104, 23], [1219, 155, 110, 25], [1543, 188, 53, 23], [1823, 243, 82, 25], [1891, 61, 16, 167], [1898, 81, 6, 53], [1824, 276, 81, 23], [1862, 807, 37, 34], [1853, 864, 51, 13], [1828, 741, 72, 22], [1165, 432, 220, 29], [1896, 180, 12, 89], [1824, 667, 81, 23], [1832, 724, 70, 23], [12, 394, 463, 32], [1792, 188, 73, 23], [1826, 752, 76, 129], [1824, 268, 81, 23], [1750, 114, 151, 23], [1824, 259, 81, 23], [1432, 843, 460, 36], [7, 427, 470, 37], [0, 529, 16, 36], [1824, 675, 81, 23], [1824, 284, 81, 23], [1855, 873, 45, 6], [1824, 251, 81, 23], [1824, 579, 81, 23], [1824, 308, 81, 23], [1824, 292, 81, 23], [1824, 300, 81, 23], [1824, 315, 81, 23], [1824, 587, 81, 23], [1824, 572, 81, 23], [1824, 491, 81, 23], [1824, 499, 81, 23], [1824, 548, 81, 23], [1824, 564, 81, 23], [1824, 556, 81, 23], [1824, 323, 81, 23], [1824, 331, 81, 23], [1824, 339, 81, 23], [1824, 347, 81, 23], [1824, 356, 81, 23], [1824, 364, 81, 23], [1824, 372, 81, 23], [1824, 380, 81, 23], [1824, 388, 81, 23], [1824, 396, 81, 23], [1824, 403, 81, 23], [1824, 411, 81, 23], [1824, 419, 81, 23], [1824, 427, 81, 23], [1824, 435, 81, 23], [1824, 444, 81, 23], [1824, 452, 81, 23], [1824, 460, 81, 23], [1824, 468, 81, 23], [1824, 476, 81, 23], [1824, 484, 81, 22], [1824, 507, 81, 23], [1824, 515, 81, 23], [1824, 523, 81, 23], [1824, 532, 81, 23], [1824, 540, 81, 23], [1824, 595, 81, 23], [1824, 603, 81, 23], [1824, 611, 81, 23], [1824, 636, 81, 23], [1824, 620, 81, 23], [1824, 628, 81, 23], [1824, 644, 81, 23], [1824, 652, 81, 23], [539, 680, 1360, 25], [1544, 180, 50, 23], [14, 677, 38, 37], [1825, 683, 80, 23], [1656, 141, 85, 20], [1825, 691, 79, 25], [681, 804, 1204, 31], [1322, 394, 77, 26], [1824, 660, 81, 23], [1327, 383, 74, 29], [1319, 402, 78, 26], [1617, 80, 125, 15], [1007, 752, 201, 30], [1103, 752, 201, 30], [1199, 752, 201, 30], [1295, 752, 201, 30], [1391, 752, 201, 30], [1487, 752, 201, 30], [863, 752, 201, 30], [767, 752, 201, 30], [850, 64, 66, 26], [968, 829, 891, 34], [597, 226, 384, 40], [1579, 221, 66, 20], [1822, 204, 80, 23], [1824, 779, 73, 23], [1463, 62, 125, 30], [1897, 757, 7, 51], [720, 770, 197, 27], [995, 152, 410, 28], [1795, 56, 102, 135], [1656, 133, 86, 20], [479, 875, 59, 4], [1528, 257, 209, 28], [816, 770, 197, 27], [912, 770, 197, 27], [1008, 770, 197, 27], [1104, 770, 197, 27], [1200, 770, 197, 27], [1296, 770, 197, 27], [1392, 770, 197, 27], [1488, 770, 197, 27], [728, 713, 1168, 25], [718, 785, 197, 26], [1172, 529, 230, 28], [1701, 858, 215, 19], [1562, 172, 39, 23], [1669, 172, 74, 22], [1896, 671, 12, 101], [452, 682, 14, 34], [1771, 187, 66, 24], [1198, 512, 205, 29], [1667, 166, 77, 20], [1897, 201, 7, 43], [1862, 460, 37, 364], [406, 678, 58, 38], [1897, 708, 7, 47], [1140, 460, 240, 31], [1319, 355, 82, 57], [1828, 749, 72, 22], [1535, 273, 204, 29], [1754, 81, 146, 28], [1599, 9, 154, 56], [1558, 707, 192, 29], [628, 389, 349, 36], [1427, 869, 473, 12], [1897, 735, 7, 51], [1891, 686, 14, 88], [591, 326, 341, 31], [1140, 477, 238, 31], [1162, 496, 227, 28], [0, 473, 16, 33], [718, 799, 199, 28], [1824, 188, 81, 25], [1225, 95, 43, 31], [1823, 196, 81, 23], [747, 614, 1130, 25], [1576, 801, 213, 26], [880, 821, 1007, 30], [1668, 708, 64, 23], [862, 785, 197, 26], [1006, 785, 197, 26], [1102, 785, 197, 26], [1198, 785, 197, 26], [1294, 785, 197, 26], [1390, 785, 197, 26], [1486, 785, 197, 26], [707, 817, 207, 26], [1190, 61, 140, 29], [1668, 700, 64, 22], [1860, 779, 44, 23], [1896, 83, 12, 90], [1739, 147, 165, 23], [1522, 721, 200, 29], [1332, 215, 557, 62], [1551, 688, 197, 29], [1668, 755, 64, 23], [1581, 672, 197, 28], [1654, 227, 241, 25], [1793, 716, 74, 23], [626, 782, 205, 28], [1764, 708, 76, 23], [1608, 141, 91, 20], [1898, 722, 8, 55], [895, 521, 69, 28], [1897, 221, 7, 45], [1666, 732, 66, 23], [921, 471, 66, 29], [1609, 705, 236, 28], [1886, 837, 16, 23], [626, 770, 205, 27], [1894, 64, 11, 108], [1563, 180, 42, 23], [1222, 127, 103, 17], [1899, 759, 8, 49], [1706, 180, 56, 23], [1635, 707, 61, 24], [1466, 78, 115, 18], [1516, 737, 203, 27], [723, 708, 60, 23], [1708, 220, 68, 24], [729, 573, 226, 31], [662, 559, 189, 31], [661, 543, 188, 28], [996, 707, 61, 24], [756, 707, 61, 24], [788, 707, 61, 24], [819, 707, 61, 24], [852, 707, 61, 24], [884, 707, 61, 24], [915, 707, 61, 24], [948, 707, 61, 24], [1028, 707, 61, 24], [1059, 707, 61, 24], [1092, 707, 61, 24], [1124, 707, 61, 24], [1155, 707, 61, 24], [1188, 707, 61, 24], [1220, 707, 61, 24], [1251, 707, 61, 24], [1284, 707, 61, 24], [1316, 707, 61, 24], [1347, 707, 61, 24], [1380, 707, 61, 24], [1412, 707, 61, 24], [1443, 707, 61, 24], [1476, 707, 61, 24], [1508, 707, 61, 24], [1539, 707, 61, 24], [1572, 707, 61, 24], [1604, 707, 61, 24], [611, 323, 1284, 39], [1666, 686, 67, 20], [1897, 237, 7, 43], [1744, 781, 81, 21], [1668, 716, 64, 23], [1215, 136, 114, 15], [6, 440, 10, 32], [1667, 693, 66, 21], [780, 647, 1093, 23], [700, 737, 203, 27], [1715, 188, 58, 23], [1667, 724, 65, 23], [6, 204, 9, 32], [636, 820, 66, 23], [709, 707, 192, 29], [1656, 149, 84, 21], [1790, 180, 78, 23], [1028, 262, 873, 26], [1603, 688, 238, 29], [1891, 722, 14, 85], [1861, 798, 37, 28], [694, 499, 303, 39], [1301, 517, 78, 21], [0, 367, 16, 36], [1750, 804, 82, 23], [915, 777, 958, 31], [627, 798, 204, 29], [0, 372, 35, 31], [1581, 180, 48, 23], [862, 799, 199, 28], [958, 799, 199, 28], [1054, 799, 199, 28], [1150, 799, 199, 28], [1246, 799, 199, 28], [1342, 799, 199, 28], [1438, 799, 199, 28], [1225, 179, 100, 24], [712, 820, 70, 21], [1328, 223, 76, 35], [913, 490, 67, 26], [904, 498, 72, 24], [1788, 675, 83, 23], [844, 737, 204, 27], [940, 737, 204, 27], [1036, 737, 204, 27], [1132, 737, 204, 27], [1228, 737, 204, 27], [1324, 737, 204, 27], [1420, 737, 204, 27], [1320, 425, 65, 28], [1661, 237, 73, 21], [886, 707, 193, 29], [1078, 707, 193, 29], [1174, 707, 193, 29], [1270, 707, 193, 29], [1366, 707, 193, 29], [1462, 707, 193, 29], [982, 707, 193, 29], [1322, 410, 71, 26], [913, 62, 240, 33], [1652, 699, 60, 24], [1562, 165, 38, 23], [780, 583, 1085, 23], [706, 721, 199, 29], [1666, 677, 68, 21], [1653, 715, 57, 24], [1897, 105, 8, 40], [1816, 829, 76, 22], [4, 361, 11, 32], [1328, 532, 68, 22], [1, 314, 376, 29], [876, 605, 85, 21], [1579, 320, 199, 28], [1591, 754, 229, 26], [1711, 845, 101, 20], [1689, 708, 64, 23], [1183, 446, 201, 32], [1750, 130, 152, 23], [1295, 411, 62, 23], [1579, 336, 199, 29], [885, 612, 78, 22], [1672, 243, 224, 26], [1653, 763, 59, 25], [1579, 304, 199, 28], [604, 356, 386, 39], [1885, 828, 14, 35], [1668, 158, 82, 20], [1827, 796, 69, 23], [9, 273, 544, 89], [1301, 524, 77, 22], [913, 636, 69, 23], [1672, 669, 71, 20], [0, 287, 444, 53], [1099, 144, 130, 15], [602, 744, 1267, 25], [1899, 201, 7, 43], [1579, 353, 199, 28], [1760, 812, 80, 23], [1833, 755, 67, 23], [850, 721, 201, 29], [946, 721, 201, 29], [1042, 721, 200, 29], [1138, 721, 201, 29], [1234, 721, 200, 29], [1330, 721, 201, 29], [1426, 721, 201, 29], [1579, 656, 201, 30], [1716, 699, 69, 24], [1897, 185, 7, 45], [1578, 127, 187, 28], [625, 754, 204, 28], [1897, 273, 7, 43], [1716, 708, 68, 23], [1723, 818, 79, 25], [1764, 796, 74, 23], [838, 639, 171, 31], [1584, 784, 220, 27], [1897, 257, 7, 44], [735, 688, 197, 29], [1579, 369, 200, 28], [1584, 289, 203, 29], [1651, 691, 62, 25], [903, 505, 69, 27], [912, 688, 195, 29], [1008, 688, 195, 29], [1167, 688, 197, 29], [1263, 688, 197, 29], [1359, 688, 197, 29], [1455, 688, 197, 29], [1030, 323, 842, 31], [1784, 700, 76, 22], [1707, 235, 70, 25], [1612, 172, 114, 22], [1313, 204, 92, 64], [1750, 845, 97, 21], [1678, 754, 216, 26], [904, 628, 69, 22], [1669, 246, 75, 20], [1721, 716, 73, 23], [1671, 661, 73, 20], [1578, 641, 202, 27], [1715, 243, 70, 26], [1897, 694, 7, 45], [1744, 773, 81, 21], [1897, 290, 7, 42], [1897, 306, 7, 42], [1897, 322, 7, 43], [1897, 529, 7, 44], [1897, 337, 7, 44], [1897, 353, 7, 44], [1897, 369, 7, 43], [1897, 386, 7, 42], [1897, 402, 7, 43], [1897, 418, 7, 43], [1897, 433, 7, 44], [1897, 449, 7, 43], [1897, 466, 7, 42], [1897, 482, 7, 42], [1897, 505, 7, 44], [1897, 554, 7, 42], [1897, 570, 7, 42], [1897, 586, 7, 43], [1579, 385, 200, 27], [1236, 267, 156, 95], [1897, 601, 7, 44], [1251, 543, 183, 31], [1669, 254, 75, 20], [1780, 683, 81, 23], [1523, 817, 208, 27], [632, 815, 195, 29], [1764, 787, 75, 24], [1897, 622, 7, 43], [1657, 674, 205, 29], [1783, 691, 78, 23], [886, 620, 76, 23], [1897, 655, 7, 40], [1716, 768, 178, 29], [1579, 400, 200, 28], [1897, 638, 7, 41], [1752, 92, 22, 23], [1777, 268, 84, 23], [431, 677, 34, 34], [1671, 270, 71, 20], [1670, 262, 73, 20], [1671, 653, 73, 20], [1579, 416, 200, 29], [928, 840, 935, 36], [1579, 449, 200, 28], [1579, 433, 200, 28], [1579, 465, 200, 27], [1579, 480, 200, 28], [1579, 496, 200, 28], [1579, 512, 200, 29], [1579, 529, 200, 28], [1579, 545, 200, 28], [1579, 561, 200, 27], [1579, 576, 200, 28], [1579, 592, 200, 29], [1579, 609, 200, 28], [1823, 771, 73, 23], [1632, 873, 272, 6], [1603, 133, 80, 20], [1779, 667, 82, 23], [1579, 625, 201, 28], [1897, 671, 7, 40], [1707, 799, 184, 33], [1594, 721, 216, 29], [1779, 660, 82, 23], [1593, 738, 254, 26], [1284, 111, 176, 28], [790, 550, 1111, 27], [1716, 691, 70, 25], [1899, 215, 7, 45], [1671, 278, 72, 20], [1897, 169, 7, 44], [1006, 344, 893, 65], [883, 672, 190, 29], [1834, 763, 66, 25], [0, 397, 16, 37], [1724, 810, 86, 25], [1476, 125, 104, 20], [1715, 779, 69, 26], [1731, 180, 49, 25], [1336, 517, 66, 21], [1715, 251, 71, 25], [1743, 14, 38, 48], [753, 672, 195, 29], [1218, 144, 112, 17], [828, 625, 171, 29], [1749, 716, 76, 23], [972, 672, 194, 28], [1716, 801, 76, 28], [1068, 672, 194, 28], [1164, 672, 194, 28], [1260, 672, 194, 28], [1356, 672, 194, 28], [1452, 672, 194, 28], [1680, 259, 216, 26], [1640, 158, 84, 20], [1331, 370, 74, 33], [1712, 707, 181, 26], [1607, 273, 233, 28], [1348, 540, 67, 22], [1594, 770, 227, 27], [1308, 418, 64, 26], [985, 22, 66, 23], [886, 64, 66, 26], [1896, 807, 5, 55], [1671, 286, 72, 19], [1715, 267, 72, 26], [626, 546, 47, 23], [1703, 658, 193, 26], [1521, 289, 198, 29], [1695, 627, 201, 26], [1694, 515, 202, 26], [1694, 531, 202, 26], [1694, 339, 202, 26], [1694, 355, 202, 26], [1694, 370, 202, 27], [1694, 386, 202, 26], [1694, 402, 202, 26], [1694, 419, 202, 26], [1694, 435, 202, 26], [1694, 450, 202, 27], [1694, 466, 202, 26], [1694, 482, 202, 26], [1694, 499, 202, 25], [1694, 546, 202, 27], [1694, 323, 202, 25], [1694, 306, 202, 26], [1694, 562, 202, 26], [1694, 578, 202, 26], [1671, 645, 73, 20], [1694, 595, 202, 26], [1694, 611, 202, 26], [711, 466, 270, 33], [1696, 642, 200, 26], [801, 656, 200, 31], [1671, 293, 73, 20], [1671, 309, 73, 20], [1671, 301, 73, 20], [1671, 622, 73, 20], [1671, 317, 73, 20], [1671, 589, 73, 21], [1671, 614, 73, 20], [1671, 325, 73, 21], [1671, 334, 73, 20], [1671, 342, 73, 20], [1671, 350, 73, 20], [1671, 358, 73, 20], [1671, 366, 73, 20], [1671, 374, 73, 19], [1671, 381, 73, 20], [1671, 389, 73, 20], [1671, 397, 73, 20], [1671, 405, 73, 20], [1671, 413, 73, 21], [1671, 422, 73, 20], [1671, 430, 73, 20], [1671, 438, 73, 20], [1671, 446, 73, 20], [1671, 454, 73, 20], [1671, 462, 73, 19], [1671, 469, 73, 20], [1671, 477, 73, 20], [1671, 485, 73, 20], [1671, 501, 73, 21], [1671, 510, 73, 20], [1671, 518, 73, 20], [1671, 526, 73, 20], [1671, 534, 73, 20], [1671, 542, 73, 20], [1671, 581, 73, 20], [1671, 565, 73, 20], [1671, 573, 73, 20], [1671, 550, 73, 19], [1671, 493, 73, 20], [1671, 557, 73, 20], [1671, 598, 73, 20], [1671, 606, 73, 20], [1671, 638, 73, 19], [1671, 630, 73, 20], [51, 675, 66, 39], [1692, 290, 204, 26], [903, 411, 83, 33], [1862, 825, 37, 32], [1354, 270, 150, 34], [785, 608, 198, 29], [902, 292, 1010, 24], [803, 817, 208, 27], [947, 817, 208, 27], [1043, 817, 208, 27], [1139, 817, 208, 27], [1235, 817, 208, 27], [1331, 817, 208, 27], [1427, 817, 208, 27], [2, 238, 34, 45], [1766, 105, 25, 23], [1144, 545, 208, 28], [1837, 785, 62, 25], [1717, 826, 87, 26], [1570, 130, 322, 36], [1768, 11, 138, 128], [20, 449, 350, 29], [0, 204, 16, 34], [1334, 524, 68, 22], [487, 831, 154, 35], [6, 169, 8, 33], [902, 196, 1002, 28], [24, 722, 133, 55], [1899, 185, 7, 43], [396, 565, 79, 22], [393, 559, 82, 20], [491, 81, 270, 30], [1715, 732, 74, 23], [638, 539, 47, 24], [1606, 108, 288, 40], [1867, 42, 38, 227], [1875, 760, 26, 48], [474, 876, 102, 5], [640, 240, 707, 32], [909, 64, 72, 28], [767, 517, 1128, 24], [552, 270, 907, 84], [1715, 836, 92, 23], [994, 641, 167, 29], [1003, 636, 70, 23], [472, 840, 438, 39], [0, 341, 36, 34], [1333, 155, 73, 23], [1775, 724, 78, 23], [565, 276, 452, 81], [945, 358, 969, 26], [1718, 172, 45, 23], [1708, 691, 187, 26], [6, 397, 9, 31], [1417, 540, 70, 23], [48, 209, 334, 219], [792, 1, 407, 59], [1750, 700, 75, 22], [1898, 62, 6, 44], [1573, 144, 238, 25], [1644, 210, 226, 26], [1773, 171, 102, 24], [898, 656, 194, 30], [1781, 61, 121, 65], [1751, 171, 77, 24], [1718, 163, 44, 26], [919, 611, 65, 23], [1407, 545, 167, 29], [1897, 157, 8, 38], [1870, 697, 34, 31], [6, 265, 10, 34], [1709, 724, 72, 23], [920, 620, 64, 23], [1103, 135, 785, 26], [566, 872, 590, 9], [651, 691, 201, 26], [1048, 656, 201, 30], [1144, 656, 201, 30], [1240, 656, 201, 30], [1336, 656, 201, 30], [1432, 656, 201, 30], [717, 270, 1167, 46], [537, 821, 76, 20], [1899, 7, 14, 56], [1764, 132, 53, 23], [165, 609, 313, 64], [1871, 787, 30, 23], [1030, 498, 277, 26], [1823, 553, 70, 241], [0, 617, 3, 62], [1064, 641, 199, 27], [1160, 641, 199, 27], [1256, 641, 199, 27], [1352, 641, 199, 27], [1448, 641, 199, 27], [127, 722, 1794, 58], [1752, 194, 64, 25], [1780, 867, 119, 12], [720, 656, 199, 30], [1740, 187, 56, 26], [625, 832, 264, 28], [476, 64, 78, 25], [1296, 403, 62, 22], [1316, 267, 84, 90], [1862, 323, 36, 382], [1526, 831, 299, 29], [0, 433, 16, 45], [1750, 212, 67, 23], [1767, 836, 79, 24], [1322, 315, 83, 41], [1876, 64, 28, 125], [1696, 724, 198, 26], [14, 400, 437, 47], [1272, 614, 608, 37], [1063, 28, 65, 24], [1605, 149, 103, 21], [1870, 752, 34, 33], [403, 262, 73, 23], [1736, 62, 29, 31], [1869, 713, 35, 31], [719, 61, 213, 32], [492, 875, 87, 4], [624, 708, 202, 28], [22, 284, 270, 32], [946, 463, 201, 31], [1598, 180, 62, 23], [1224, 581, 659, 37], [1219, 548, 664, 37], [596, 180, 393, 83], [1900, 842, 16, 37], [624, 721, 207, 28], [1065, 625, 199, 29], [1161, 625, 199, 29], [1257, 625, 199, 29], [1353, 625, 199, 29], [1449, 625, 199, 29], [1871, 0, 31, 65], [912, 403, 72, 28], [1690, 62, 62, 25], [1765, 149, 86, 21], [1096, 513, 249, 28], [844, 77, 165, 40], [1317, 647, 559, 37], [1758, 180, 65, 25], [1349, 821, 495, 47], [939, 177, 44, 92], [399, 345, 77, 26], [1575, 61, 293, 34], [1870, 666, 34, 31], [537, 526, 1359, 42], [1033, 22, 69, 23], [1411, 529, 162, 28], [1500, 240, 196, 29], [1859, 730, 41, 31], [1870, 680, 34, 32], [1747, 220, 72, 23], [1057, 22, 62, 24], [1601, 125, 80, 22], [555, 798, 162, 32], [1764, 124, 59, 24], [1502, 180, 84, 23], [1219, 515, 665, 37], [393, 550, 83, 21], [1225, 149, 104, 20], [962, 270, 449, 87], [6, 279, 10, 34], [1767, 820, 81, 23], [939, 352, 670, 49], [916, 460, 71, 31], [0, 671, 3, 40], [1750, 204, 66, 23], [1763, 141, 59, 21], [1898, 812, 8, 56], [1783, 53, 126, 466], [444, 837, 1179, 28], [1753, 56, 151, 23], [79, 675, 63, 41], [1238, 143, 39, 34], [1334, 7, 119, 60], [1848, 768, 47, 34], [1775, 97, 25, 21], [992, 625, 165, 29], [565, 531, 133, 32], [1597, 172, 70, 22], [1491, 141, 94, 20], [635, 446, 352, 51], [1319, 235, 86, 31], [1831, 139, 63, 218], [399, 573, 76, 22], [1744, 229, 78, 21], [1900, 69, 6, 56], [36, 526, 411, 183], [1055, 229, 841, 26], [480, 61, 299, 31], [13, 352, 642, 90], [471, 875, 340, 4], [752, 543, 212, 31], [1340, 160, 151, 28], [1770, 828, 78, 24], [447, 677, 17, 29], [1866, 91, 35, 312], [1761, 116, 67, 24], [937, 628, 57, 23], [0, 715, 9, 68], [1491, 133, 94, 22], [740, 641, 197, 29], [251, 677, 212, 34], [1694, 875, 202, 6], [6, 721, 28, 59], [1660, 130, 197, 26], [448, 699, 16, 17], [0, 447, 13, 56], [1800, 837, 77, 22], [1184, 128, 172, 31], [1296, 125, 163, 26], [0, 311, 16, 32], [1089, 36, 58, 24], [1759, 157, 108, 23], [1864, 64, 37, 31], [0, 312, 39, 30], [1747, 62, 32, 30], [997, 232, 407, 36], [1621, 160, 202, 26], [1026, 416, 355, 25], [1759, 108, 85, 25], [0, 535, 397, 33], [409, 290, 68, 28], [1513, 336, 199, 29], [1870, 737, 34, 32], [594, 154, 390, 27], [1278, 529, 153, 28], [472, 429, 74, 21], [19, 400, 245, 30], [1190, 160, 112, 18], [538, 815, 172, 31], [1723, 62, 36, 27], [896, 446, 89, 31], [0, 476, 3, 32], [12, 719, 52, 59], [734, 479, 247, 31], [1212, 9, 121, 56], [1347, 177, 158, 33], [1749, 62, 83, 25], [935, 449, 498, 51], [485, 738, 176, 26], [1758, 100, 96, 23], [489, 798, 157, 32], [1671, 785, 198, 28], [1512, 369, 198, 29], [1887, 4, 20, 64], [418, 699, 43, 15], [0, 402, 36, 37], [437, 0, 929, 60], [490, 770, 159, 29], [1515, 385, 195, 29], [0, 235, 16, 53], [1882, 875, 38, 4], [1008, 400, 230, 26], [1758, 92, 109, 23], [1793, 875, 80, 4], [355, 719, 108, 61], [684, 282, 301, 44], [1334, 117, 67, 19], [282, 760, 151, 17], [1512, 353, 200, 28], [1598, 165, 71, 21], [559, 785, 188, 28], [747, 590, 218, 31], [1785, 141, 78, 20], [1514, 400, 195, 30], [1712, 157, 49, 23], [2, 601, 55, 75], [485, 752, 163, 31], [10, 433, 280, 42], [1493, 171, 92, 23], [487, 722, 183, 28], [1045, 202, 812, 64], [1147, 58, 136, 126], [1414, 532, 76, 23], [1020, 287, 397, 23], [927, 479, 164, 32], [1008, 422, 397, 29], [600, 737, 217, 27], [614, 531, 67, 23], [943, 485, 445, 47], [1078, 529, 223, 28], [10, 183, 125, 99], [0, 504, 3, 31], [1893, 870, 26, 6], [1342, 190, 149, 34], [626, 554, 47, 23], [24, 721, 68, 59], [983, 370, 419, 42], [1103, 152, 158, 14], [901, 392, 982, 55], [1718, 84, 49, 20], [474, 73, 71, 22], [696, 875, 356, 6], [1603, 815, 276, 31], [350, 774, 1543, 42], [587, 331, 83, 25], [948, 636, 56, 23], [1860, 876, 51, 5], [636, 81, 305, 31], [853, 482, 992, 28], [115, 532, 349, 31], [446, 740, 893, 32], [1810, 878, 96, 3], [587, 243, 85, 25], [1788, 125, 81, 20], [490, 784, 158, 31], [1160, 872, 554, 9], [1022, 449, 274, 28], [1069, 161, 144, 19], [474, 435, 71, 23], [468, 59, 176, 23], [618, 672, 201, 29], [1376, 356, 27, 67], [1091, 22, 62, 26], [1787, 133, 75, 20], [1462, 4, 123, 63], [1776, 108, 104, 21], [0, 176, 15, 34], [0, 342, 14, 56], [1488, 163, 97, 23], [487, 869, 455, 10], [540, 831, 199, 37], [4, 427, 12, 24], [754, 491, 217, 31], [932, 452, 913, 26], [1093, 135, 127, 16], [1715, 78, 52, 18], [15, 177, 289, 29], [1487, 157, 98, 21], [1, 559, 376, 34], [0, 111, 14, 70], [12, 763, 45, 14], [1001, 419, 843, 26], [1072, 108, 817, 25], [478, 861, 701, 16], [559, 466, 290, 39], [24, 256, 289, 32], [567, 413, 414, 32], [545, 510, 434, 36], [1086, 125, 319, 28], [78, 37, 273, 352], [1890, 64, 14, 69], [0, 502, 7, 33], [951, 611, 39, 24], [472, 510, 70, 20], [1003, 268, 410, 50], [1017, 62, 210, 30], [566, 505, 132, 36], [824, 872, 557, 9], [0, 469, 8, 39], [1010, 128, 304, 27], [1316, 383, 546, 51], [950, 620, 43, 23], [384, 711, 1135, 28], [697, 59, 685, 47], [566, 220, 909, 52], [0, 598, 3, 48], [947, 59, 182, 25], [4, 119, 12, 34], [0, 279, 2, 36], [1376, 385, 209, 29], [467, 457, 1359, 56], [0, 638, 2, 49], [16, 611, 1871, 46], [0, 444, 3, 34], [1338, 108, 74, 21], [1, 595, 13, 83], [1459, 127, 131, 31], [45, 325, 336, 197], [391, 542, 85, 21], [744, 512, 227, 28], [1032, 433, 281, 26], [474, 444, 72, 22], [469, 872, 242, 7], [1538, 875, 322, 6], [745, 528, 223, 27], [1165, 430, 647, 39], [1485, 149, 100, 21], [1214, 59, 113, 69], [1490, 854, 381, 20], [1326, 179, 76, 68], [325, 581, 106, 23], [0, 562, 3, 37], [1403, 875, 354, 6], [155, 257, 317, 28], [474, 421, 71, 21], [880, 460, 160, 31], [993, 628, 91, 23], [1089, 72, 794, 101], [625, 875, 324, 6], [932, 386, 462, 39], [645, 513, 182, 25], [1714, 870, 183, 9], [469, 869, 207, 8], [1727, 6, 27, 61], [137, 669, 1784, 43], [391, 653, 1504, 39], [0, 592, 5, 89], [1214, 179, 677, 34], [252, 641, 1537, 29], [971, 136, 938, 48], [599, 61, 289, 26], [911, 245, 71, 21], [947, 446, 145, 32], [58, 372, 312, 309], [1874, 67, 28, 56], [0, 339, 3, 34], [372, 270, 103, 18], [0, 559, 16, 47], [969, 383, 436, 78], [475, 565, 66, 20], [1754, 0, 39, 63], [405, 254, 70, 23], [1008, 125, 397, 50], [0, 232, 2, 53], [1165, 864, 720, 15], [588, 235, 159, 34], [1728, 0, 74, 82], [1007, 356, 400, 23], [1464, 875, 349, 6], [1197, 163, 697, 26], [827, 875, 354, 6], [955, 875, 354, 6], [1082, 875, 356, 6], [1211, 875, 354, 6], [1076, 59, 821, 69], [133, 284, 273, 26], [1334, 207, 67, 29], [1311, 6, 25, 59], [472, 501, 70, 21], [4, 370, 58, 31], [712, 103, 597, 30], [672, 523, 430, 40], [373, 757, 84, 21], [16, 284, 147, 175], [1600, 876, 256, 5], [227, 509, 120, 178], [0, 207, 3, 32], [1273, 477, 609, 44], [578, 122, 402, 50], [752, 526, 636, 36], [945, 389, 941, 25], [620, 562, 49, 23], [0, 532, 5, 33], [0, 672, 8, 45], [477, 573, 65, 20], [1570, 62, 207, 199], [1752, 78, 140, 18], [4, 603, 29, 78], [0, 779, 464, 100], [159, 488, 1762, 48], [1335, 196, 550, 34], [745, 158, 154, 22], [491, 61, 642, 39], [1104, 7, 805, 58], [313, 586, 164, 38], [482, 517, 60, 19], [151, 521, 146, 158], [1762, 875, 79, 6], [0, 359, 3, 45], [0, 0, 475, 62], [7, 561, 198, 56], [1898, 873, 20, 6], [1353, 363, 151, 35], [1296, 875, 325, 6], [792, 875, 325, 6], [903, 875, 325, 6], [547, 441, 330, 69], [239, 561, 167, 123], [6, 149, 271, 32], [328, 260, 89, 25], [1716, 876, 195, 5], [0, 399, 3, 35], [475, 557, 67, 20], [0, 58, 17, 92], [961, 429, 876, 68], [550, 83, 495, 95], [480, 524, 62, 20], [817, 460, 1063, 31], [0, 312, 3, 31], [519, 875, 76, 4], [420, 449, 55, 29], [589, 265, 156, 31], [1117, 0, 33, 62], [1440, 9, 25, 56], [1, 675, 13, 42], [1300, 359, 147, 36], [588, 158, 87, 22], [0, 6, 8, 54], [0, 361, 9, 45], [475, 370, 54, 22], [475, 405, 54, 18], [476, 378, 53, 22], [406, 246, 67, 22], [0, 268, 7, 50], [485, 396, 67, 23], [370, 592, 1503, 42], [475, 411, 54, 20], [667, 501, 24, 65], [483, 873, 454, 6], [620, 521, 65, 23], [1158, 66, 719, 35], [1202, 149, 93, 18], [7, 341, 67, 32], [80, 381, 389, 33], [1272, 416, 615, 42], [1262, 292, 617, 37], [0, 394, 8, 48], [590, 39, 318, 21], [0, 231, 472, 38], [545, 132, 432, 23], [10, 273, 490, 40], [1761, 0, 89, 62], [475, 491, 65, 22], [477, 532, 66, 20], [400, 309, 77, 26], [1056, 171, 168, 10], [12, 36, 205, 346], [365, 573, 1488, 31], [1161, 42, 741, 25], [476, 386, 53, 22], [1278, 447, 606, 42], [759, 336, 139, 18], [0, 152, 13, 56], [993, 180, 409, 25], [9, 638, 220, 40], [595, 265, 386, 28], [586, 176, 891, 68], [313, 776, 972, 24], [985, 620, 118, 23], [0, 820, 2, 61], [0, 149, 34, 32], [477, 540, 68, 20], [471, 814, 688, 49], [0, 532, 2, 36], [700, 262, 144, 100], [153, 144, 136, 209], [1568, 6, 27, 61], [0, 141, 17, 40], [370, 620, 990, 33], [1091, 67, 313, 290], [1008, 350, 389, 15], [576, 114, 369, 25], [478, 364, 68, 22], [477, 347, 68, 23], [474, 402, 101, 29], [1775, 18, 122, 30], [487, 388, 65, 23], [475, 550, 68, 18], [859, 334, 104, 20], [0, 204, 491, 83], [985, 612, 123, 22], [488, 380, 65, 23], [431, 796, 1086, 34], [144, 653, 102, 25], [1376, 320, 113, 34], [581, 444, 105, 22], [578, 875, 87, 6], [282, 680, 100, 23], [481, 50, 78, 13], [570, 179, 413, 24], [583, 177, 210, 50], [781, 259, 150, 109], [478, 356, 68, 22], [1758, 0, 144, 18], [0, 229, 7, 56], [475, 452, 71, 22], [471, 876, 220, 5], [856, 455, 656, 31], [67, 787, 923, 89], [6, 400, 78, 34], [1006, 331, 87, 26], [476, 84, 61, 20], [0, 147, 3, 34], [1587, 37, 298, 31], [431, 578, 1053, 34], [843, 158, 91, 20], [0, 361, 14, 70], [20, 743, 380, 34], [0, 176, 3, 34], [1278, 169, 127, 12], [1115, 262, 150, 103], [1281, 355, 595, 38], [1676, 1, 209, 62], [1367, 355, 140, 29], [980, 77, 541, 96], [1852, 4, 49, 81], [90, 440, 142, 186], [0, 3, 26, 57], [488, 372, 67, 23], [558, 4, 178, 53], [0, 303, 8, 43], [12, 760, 449, 20], [1084, 1, 249, 61], [1105, 86, 248, 26], [1066, 62, 267, 28], [73, 645, 276, 31], [723, 864, 637, 15], [948, 864, 637, 15], [1495, 61, 269, 34], [0, 559, 8, 48], [459, 39, 225, 23], [1291, 177, 114, 15], [0, 405, 3, 51], [542, 113, 453, 40], [135, 564, 231, 112], [476, 92, 61, 20], [544, 876, 91, 3], [790, 350, 144, 108], [289, 590, 140, 19], [958, 444, 27, 50], [268, 559, 209, 31], [555, 342, 727, 53], [1020, 207, 364, 31], [0, 102, 3, 51], [12, 56, 425, 146], [943, 441, 41, 6], [1030, 345, 350, 17], [507, 457, 534, 81], [1759, 39, 136, 26], [1100, 0, 88, 62], [0, 202, 9, 37], [648, 0, 559, 29], [0, 576, 3, 53], [477, 482, 62, 23], [50, 124, 376, 213], [616, 554, 432, 36], [4, 587, 472, 86], [863, 443, 118, 13], [1595, 58, 170, 23], [0, 435, 8, 45], [1294, 4, 34, 63], [0, 245, 4, 51], [13, 138, 490, 123], [1758, 3, 128, 40], [631, 99, 320, 23], [1280, 254, 125, 15], [1083, 17, 535, 45], [1354, 171, 51, 9], [1, 201, 474, 37], [1000, 114, 261, 22], [4, 721, 14, 62], [2, 576, 498, 50], [1456, 106, 398, 33], [0, 575, 14, 133], [10, 465, 478, 76], [2, 100, 24, 61], [578, 265, 84, 78], [859, 55, 822, 52], [173, 292, 150, 178], [371, 28, 869, 37], [462, 677, 7, 37], [4, 718, 451, 21], [1000, 429, 393, 38], [664, 61, 921, 73], [496, 545, 731, 37], [599, 378, 368, 28], [12, 95, 476, 50], [0, 169, 8, 45], [0, 801, 2, 62], [1347, 353, 58, 4], [1706, 1, 42, 64], [477, 474, 60, 23], [106, 715, 315, 10], [575, 457, 131, 53], [1485, 1, 417, 48], [1287, 342, 118, 17], [419, 710, 44, 6], [220, 760, 166, 17], [476, 468, 59, 21], [423, 765, 41, 12], [0, 336, 8, 37], [877, 70, 444, 48], [20, 235, 230, 37], [382, 537, 1539, 70], [1321, 103, 456, 26], [463, 768, 654, 48], [1170, 254, 150, 114], [19, 609, 289, 34], [710, 168, 165, 112], [419, 873, 50, 8], [1742, 1, 12, 61], [861, 430, 123, 15], [113, 498, 166, 134], [577, 501, 189, 20], [0, 603, 147, 56], [1421, 1, 34, 66], [168, 669, 260, 10], [1321, 314, 540, 51], [0, 55, 27, 128], [1901, 0, 17, 16], [96, 658, 388, 21], [0, 660, 363, 19], [526, 67, 466, 58], [0, 75, 3, 73], [1071, 169, 326, 19], [0, 711, 485, 12], [0, 61, 10, 51], [583, 268, 407, 48], [1140, 172, 137, 104], [14, 529, 447, 66], [295, 58, 49, 97], [0, 62, 376, 66], [489, 543, 888, 70], [564, 166, 922, 48], [376, 672, 966, 36], [723, 474, 1052, 81], [1174, 1, 117, 100], [7, 589, 114, 23], [7, 568, 339, 44], [1464, 37, 120, 28], [584, 287, 327, 31], [307, 878, 161, 3], [213, 573, 204, 39], [1404, 161, 380, 31], [1354, 352, 51, 2], [132, 664, 122, 17], [1333, 39, 120, 26], [215, 537, 263, 37], [1015, 0, 318, 27], [1004, 176, 407, 16], [1901, 722, 13, 85], [1746, 0, 61, 19], [1628, 42, 123, 23], [558, 303, 409, 28], [211, 655, 123, 23], [200, 672, 230, 11], [618, 201, 340, 31], [578, 326, 62, 42], [933, 144, 576, 66], [252, 606, 219, 39], [15, 14, 241, 42], [15, 76, 150, 34], [17, 74, 147, 34], [1759, 22, 132, 20], [1467, 20, 106, 26], [1221, 20, 91, 27], [586, 117, 92, 16], [1599, 18, 133, 29], [15, 13, 236, 44], [586, 117, 92, 16], [149, 687, 146, 22], [1756, 21, 126, 22], [18, 616, 111, 26], [1337, 18, 107, 30], [150, 687, 146, 21], [18, 614, 111, 31], [18, 614, 112, 28], [145, 688, 154, 22], [0, 677, 479, 40], [1572, 19, 339, 26], [1006, 117, 106, 17], [12, 8, 242, 50], [8, 68, 204, 46], [15, 75, 153, 33], [210, 740, 54, 23], [19, 70, 242, 44], [13, 7, 250, 52], [359, 72, 86, 38], [21, 17, 80, 30], [17, 74, 146, 35], [1217, 20, 97, 26], [1764, 17, 123, 30], [10, 723, 457, 56], [208, 740, 57, 24], [16, 153, 393, 27], [1, 69, 414, 46], [1005, 116, 106, 19], [586, 292, 104, 21], [19, 16, 65, 31], [19, 71, 232, 46], [13, 565, 102, 30], [131, 152, 263, 27], [586, 292, 104, 22], [208, 741, 56, 23], [1594, 17, 143, 30], [20, 210, 390, 48], [56, 679, 382, 38], [2, 2, 297, 59], [22, 17, 77, 31], [3, 722, 472, 58], [584, 383, 55, 17], [1335, 19, 112, 26], [1465, 20, 111, 26], [1755, 21, 126, 23], [585, 117, 92, 16], [1216, 21, 97, 25], [22, 450, 419, 31], [46, 3, 833, 63], [27, 344, 240, 27], [1006, 116, 106, 19], [11, 191, 409, 125], [1, 676, 473, 40], [578, 468, 108, 33], [1, 65, 214, 48], [132, 683, 186, 29], [12, 343, 436, 31], [19, 370, 429, 32], [42, 288, 392, 31], [40, 287, 396, 28], [0, 608, 399, 44], [1004, 204, 38, 19], [27, 152, 169, 27], [17, 242, 75, 22], [587, 292, 103, 21], [23, 479, 242, 27], [361, 71, 85, 39], [1465, 21, 110, 27], [1385, 15, 513, 35], [578, 464, 116, 40], [52, 3, 828, 62], [1577, 19, 328, 26], [37, 5, 875, 59], [18, 189, 408, 130], [1757, 21, 110, 23], [1462, 20, 114, 26], [14, 12, 272, 75], [16, 241, 74, 23], [36, 537, 331, 26], [1333, 19, 114, 27], [29, 210, 236, 25], [0, 1, 306, 57], [14, 215, 419, 59], [131, 480, 275, 25], [585, 206, 68, 15], [1224, 0, 669, 70], [2, 225, 448, 94], [24, 210, 390, 48], [0, 343, 467, 27], [11, 75, 186, 30], [33, 452, 412, 33], [274, 451, 177, 27], [0, 614, 235, 29], [509, 109, 289, 39], [14, 224, 396, 55], [584, 206, 69, 15], [20, 16, 64, 32], [2, 72, 432, 41], [19, 78, 139, 24], [0, 375, 477, 82], [1759, 22, 105, 22], [1496, 16, 392, 42], [15, 568, 101, 26], [23, 68, 231, 49], [1598, 21, 138, 23], [33, 316, 304, 25], [584, 382, 55, 17], [1002, 203, 46, 22], [1217, 20, 96, 27], [27, 510, 401, 33], [1, 70, 228, 47], [19, 150, 373, 97], [20, 16, 80, 31], [1463, 20, 106, 27], [0, 2, 329, 59], [1337, 19, 233, 28], [1167, 14, 256, 42], [57, 538, 353, 71], [0, 3, 302, 52], [1264, 5, 612, 59], [89, 682, 369, 31], [1755, 22, 102, 21], [4, 270, 462, 80], [10, 68, 247, 51], [1217, 19, 227, 26], [88, 681, 372, 34], [0, 63, 448, 124], [27, 282, 401, 60], [1215, 20, 94, 27], [20, 344, 411, 74], [26, 374, 232, 26], [19, 615, 109, 27], [1008, 294, 66, 16], [40, 289, 190, 24], [136, 682, 183, 30], [583, 115, 96, 20], [42, 3, 768, 65], [18, 316, 355, 25], [28, 2, 840, 64], [0, 608, 391, 43], [1334, 18, 112, 31], [57, 534, 354, 75], [14, 506, 439, 40], [31, 726, 409, 50], [32, 286, 451, 29], [1004, 117, 107, 17], [26, 181, 245, 26], [18, 382, 424, 73], [64, 152, 351, 28], [213, 289, 213, 26], [26, 476, 417, 34], [583, 207, 70, 16], [13, 612, 129, 34], [1593, 18, 140, 30], [1470, 12, 415, 46], [19, 376, 430, 84], [1005, 204, 38, 20], [7, 132, 423, 91], [19, 468, 389, 105], [18, 17, 65, 29], [1623, 16, 221, 31], [1, 512, 448, 91], [1006, 293, 67, 18], [30, 479, 407, 28], [17, 241, 76, 23], [19, 614, 109, 28], [21, 510, 264, 25], [19, 337, 417, 85], [203, 740, 66, 25], [1460, 20, 119, 24], [0, 537, 393, 27], [0, 183, 510, 84], [363, 72, 79, 38], [21, 210, 255, 27], [50, 151, 264, 29], [1761, 21, 103, 22], [34, 288, 254, 25], [1358, 19, 184, 28], [147, 507, 251, 28], [19, 674, 454, 52], [363, 72, 79, 36], [139, 687, 162, 23], [584, 121, 99, 20], [1220, 22, 92, 19], [16, 343, 304, 29], [38, 10, 461, 47], [16, 566, 101, 29], [1753, 15, 137, 34], [582, 121, 100, 20], [9, 216, 411, 57], [0, 580, 440, 93], [32, 281, 404, 64], [5, 68, 399, 46], [1333, 18, 109, 29], [1591, 17, 142, 30], [16, 161, 427, 96], [144, 687, 152, 22], [365, 72, 76, 35], [13, 565, 107, 32], [208, 740, 57, 23], [1005, 206, 37, 18], [476, 102, 689, 62], [0, 566, 347, 30], [20, 682, 273, 31], [8, 339, 436, 82], [29, 145, 383, 69], [12, 40, 247, 69], [16, 178, 354, 28], [1000, 290, 81, 26], [27, 5, 869, 61], [253, 452, 199, 27], [13, 374, 293, 26], [0, 609, 385, 42], [543, 106, 181, 31], [45, 76, 259, 35], [21, 79, 136, 23], [21, 709, 448, 77], [9, 186, 418, 129], [1610, 21, 128, 19], [24, 371, 413, 35], [997, 111, 117, 26], [1799, 4, 53, 59], [572, 464, 121, 40], [28, 483, 429, 45], [17, 680, 291, 32], [21, 17, 66, 31], [539, 141, 477, 44], [578, 122, 106, 19], [21, 17, 63, 30], [1244, 10, 635, 63], [32, 3, 876, 63], [139, 686, 166, 25], [17, 194, 382, 45], [10, 673, 469, 95], [22, 150, 247, 28], [0, 2, 847, 63], [5, 461, 426, 116], [1001, 203, 49, 23], [201, 285, 237, 30], [581, 289, 116, 30], [589, 117, 88, 16], [13, 613, 131, 27], [46, 646, 431, 62], [1215, 20, 99, 25], [25, 449, 423, 32], [812, 396, 625, 74], [0, 608, 390, 44], [15, 65, 421, 66], [64, 152, 346, 26], [18, 567, 96, 27], [1228, 17, 141, 30], [29, 726, 419, 50], [1332, 21, 113, 26], [0, 183, 463, 139], [10, 75, 276, 44], [196, 315, 233, 29], [1756, 17, 141, 33], [0, 566, 345, 29], [1487, 14, 406, 42], [96, 680, 367, 35], [2, 614, 216, 29], [667, 464, 310, 49], [320, 346, 135, 23], [601, 474, 63, 20], [389, 81, 26, 18], [537, 387, 460, 65], [203, 739, 66, 27], [11, 341, 432, 34], [9, 543, 426, 47], [296, 347, 159, 21], [0, 205, 446, 59], [575, 111, 108, 27], [205, 636, 84, 222], [1063, 11, 262, 51], [12, 73, 414, 111], [1005, 112, 405, 31], [17, 200, 374, 37], [328, 4, 1008, 58], [0, 97, 401, 50], [925, 307, 492, 56], [445, 8, 805, 48], [1493, 15, 403, 43], [203, 735, 173, 33], [20, 79, 135, 24], [55, 317, 358, 39], [1006, 294, 67, 17], [388, 81, 28, 19], [104, 681, 348, 33], [0, 450, 464, 31], [21, 368, 426, 42], [15, 254, 416, 64], [32, 402, 359, 32], [12, 380, 435, 77], [1180, 14, 252, 40], [1224, 0, 663, 71], [6, 231, 430, 56], [0, 610, 389, 40], [103, 400, 260, 29], [38, 286, 397, 30], [379, 78, 46, 25], [0, 375, 486, 84], [1187, 19, 209, 38], [8, 614, 206, 29], [18, 681, 290, 32], [362, 71, 84, 38], [22, 726, 450, 51], [985, 131, 426, 50], [587, 117, 104, 15], [486, 114, 126, 27], [26, 184, 405, 136], [1464, 19, 114, 27], [0, 537, 391, 28], [1464, 19, 284, 27], [202, 735, 176, 33], [1264, 251, 623, 120], [11, 66, 442, 99], [6, 670, 495, 98], [1267, 4, 616, 62], [579, 202, 84, 26], [56, 536, 357, 74], [998, 414, 422, 38], [10, 606, 362, 43], [1592, 17, 148, 29], [1480, 12, 423, 51], [16, 241, 73, 22], [204, 743, 62, 20], [1185, 16, 284, 36], [25, 301, 421, 89], [26, 462, 383, 112], [191, 614, 118, 255], [0, 614, 219, 29], [1466, 19, 114, 28], [122, 681, 332, 29], [12, 517, 396, 45], [31, 286, 455, 29], [944, 451, 554, 92], [180, 316, 232, 27], [27, 677, 465, 89], [995, 113, 120, 20], [501, 107, 295, 42], [582, 465, 111, 42], [25, 537, 350, 28], [588, 118, 49, 13], [20, 209, 316, 28], [362, 73, 82, 35], [77, 151, 340, 29], [584, 206, 62, 16], [357, 73, 87, 31], [586, 382, 52, 18], [26, 374, 422, 87], [88, 210, 290, 26], [1285, 0, 585, 69], [26, 344, 235, 27], [146, 213, 1241, 61], [24, 479, 234, 28], [139, 143, 1230, 51], [1005, 114, 104, 21], [69, 70, 276, 34], [15, 240, 78, 25], [1803, 0, 47, 65], [20, 191, 373, 45], [1496, 17, 206, 30], [1004, 123, 109, 21], [20, 150, 179, 29], [585, 381, 54, 20], [1337, 19, 224, 27], [598, 109, 363, 36], [18, 315, 357, 26], [973, 288, 432, 45], [1613, 14, 236, 34], [16, 569, 351, 53], [1213, 19, 101, 28], [32, 311, 399, 73], [1596, 17, 141, 31], [0, 63, 470, 99], [17, 539, 138, 22], [10, 120, 404, 53], [543, 428, 562, 95], [29, 110, 319, 25], [1599, 16, 275, 30], [490, 105, 667, 57], [274, 451, 181, 28], [583, 126, 93, 18], [1334, 18, 116, 28], [25, 673, 476, 96], [1000, 126, 114, 17], [0, 611, 390, 38], [14, 566, 103, 29], [13, 680, 295, 32], [11, 612, 143, 32], [760, 86, 713, 74]], [0.99997, 0.99996, 0.99967, 0.9996, 0.99955, 0.99953, 0.99945, 0.9982, 0.99676, 0.99545, 0.99157, 0.98983, 0.98709, 0.98528, 0.04504, 0.03801, 0.01193, 0.11907, 0.08301, 0.07654, 0.04855, 0.03802, 0.03396, 0.03359, 0.03326, 0.03274, 0.02869, 0.02804, 0.02608, 0.02578, 0.02221, 0.02127, 0.02055, 0.02042, 0.01915, 0.01664, 0.01654, 0.01647, 0.01485, 0.01416, 0.01391, 0.01307, 0.01298, 0.01252, 0.01232, 0.01214, 0.01112, 0.00961, 0.00942, 0.00928, 0.0085, 0.00821, 0.0073, 0.00712, 0.0069, 0.00678, 0.00672, 0.00666, 0.00658, 0.00636, 0.00632, 0.00628, 0.00613, 0.00606, 0.00585, 0.00565, 0.00552, 0.00543, 0.00538, 0.00508, 0.00504, 0.00501, 0.00497, 0.0049, 0.00481, 0.0048, 0.0048, 0.00474, 0.00472, 0.00463, 0.00456, 0.00455, 0.00439, 0.00439, 0.00435, 0.00434, 0.00422, 0.0042, 0.00417, 0.00415, 0.00414, 0.00404, 0.00399, 0.00391, 0.00359, 0.00353, 0.00352, 0.00349, 0.00348, 0.00347, 0.00346, 0.00345, 0.00339, 0.0033, 0.00324, 0.00323, 0.0032, 0.00311, 0.0031, 0.00302, 0.00301, 0.003, 0.003, 0.00296, 0.00296, 0.00288, 0.00286, 0.00283, 0.00281, 0.00281, 0.00279, 0.00279, 0.00278, 0.00277, 0.00276, 0.00273, 0.00271, 0.00271, 0.00266, 0.00264, 0.0026, 0.00256, 0.00254, 0.00249, 0.00247, 0.00246, 0.00245, 0.00239, 0.00238, 0.00235, 0.00233, 0.00226, 0.00225, 0.00225, 0.00225, 0.00224, 0.00223, 0.00221, 0.00221, 0.0022, 0.0022, 0.00219, 0.00219, 0.00218, 0.00218, 0.00216, 0.00216, 0.00216, 0.00215, 0.00214, 0.0021, 0.0021, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00208, 0.00207, 0.00207, 0.00207, 0.00206, 0.00205, 0.00204, 0.00203, 0.00202, 0.00202, 0.00201, 0.00198, 0.00196, 0.00195, 0.00194, 0.00193, 0.00193, 0.0019, 0.00189, 0.00189, 0.00189, 0.00189, 0.00189, 0.00189, 0.00189, 0.00189, 0.00188, 0.00187, 0.00184, 0.00184, 0.00181, 0.0018, 0.00179, 0.00179, 0.00177, 0.00174, 0.00171, 0.00169, 0.00167, 0.00165, 0.00164, 0.00164, 0.00164, 0.00164, 0.00164, 0.00164, 0.00164, 0.00164, 0.00164, 0.00163, 0.00163, 0.00161, 0.00161, 0.00161, 0.0016, 0.00157, 0.00156, 0.00156, 0.00154, 0.00153, 0.00153, 0.00152, 0.00152, 0.00151, 0.00151, 0.00151, 0.00149, 0.00148, 0.00145, 0.00145, 0.00145, 0.00143, 0.00143, 0.00141, 0.0014, 0.0014, 0.00139, 0.00136, 0.00136, 0.00135, 0.00135, 0.00134, 0.00133, 0.00133, 0.00133, 0.00132, 0.00131, 0.00131, 0.00131, 0.00131, 0.00131, 0.00131, 0.00131, 0.00131, 0.00131, 0.0013, 0.0013, 0.00126, 0.00126, 0.00126, 0.00125, 0.00125, 0.00125, 0.00124, 0.00124, 0.00124, 0.00123, 0.00121, 0.0012, 0.0012, 0.00119, 0.00119, 0.00118, 0.00118, 0.00118, 0.00117, 0.00117, 0.00117, 0.00116, 0.00116, 0.00116, 0.00115, 0.00114, 0.00114, 0.00112, 0.00112, 0.00112, 0.00111, 0.00111, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.0011, 0.00109, 0.00109, 0.00108, 0.00108, 0.00108, 0.00108, 0.00107, 0.00107, 0.00107, 0.00107, 0.00106, 0.00106, 0.00106, 0.00105, 0.00105, 0.00105, 0.00104, 0.00104, 0.00103, 0.00103, 0.00103, 0.00102, 0.00102, 0.00102, 0.00102, 0.00102, 0.00102, 0.00101, 0.00101, 0.00101, 0.00101, 0.00101, 0.00101, 0.00101, 0.001, 0.001, 0.001, 0.00099, 0.00099, 0.00098, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00097, 0.00096, 0.00096, 0.00096, 0.00095, 0.00093, 0.00093, 0.00093, 0.00093, 0.00093, 0.00092, 0.00092, 0.00092, 0.00092, 0.00092, 0.00092, 0.00092, 0.00091, 0.00091, 0.00091, 0.00091, 0.0009, 0.0009, 0.0009, 0.0009, 0.0009, 0.0009, 0.0009, 0.00089, 0.00088, 0.00088, 0.00086, 0.00086, 0.00085, 0.00085, 0.00085, 0.00085, 0.00084, 0.00084, 0.00084, 0.00083, 0.00083, 0.00083, 0.00083, 0.00083, 0.00083, 0.00083, 0.00083, 0.00082, 0.00082, 0.00082, 0.00082, 0.00082, 0.00081, 0.00081, 0.00081, 0.0008, 0.00079, 0.00078, 0.00077, 0.00076, 0.00076, 0.00076, 0.00075, 0.00075, 0.00075, 0.00075, 0.00075, 0.00075, 0.00075, 0.00075, 0.00075, 0.00073, 0.00073, 0.00073, 0.00073, 0.00073, 0.00073, 0.00073, 0.00072, 0.00072, 0.00072, 0.00072, 0.00072, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.00071, 0.0007, 0.0007, 0.0007, 0.0007, 0.00069, 0.00069, 0.00069, 0.00069, 0.00069, 0.00068, 0.00068, 0.00068, 0.00068, 0.00068, 0.00068, 0.00067, 0.00067, 0.00067, 0.00067, 0.00067, 0.00067, 0.00066, 0.00066, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00065, 0.00064, 0.00064, 0.00064, 0.00064, 0.00064, 0.00064, 0.00063, 0.00062, 0.00062, 0.00061, 0.00061, 0.00061, 0.00061, 0.0006, 0.0006, 0.0006, 0.0006, 0.00059, 0.00059, 0.00059, 0.00059, 0.00059, 0.00059, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00058, 0.00057, 0.00057, 0.00056, 0.00055, 0.00055, 0.00055, 0.00055, 0.00054, 0.00054, 0.00054, 0.00054, 0.00054, 0.00054, 0.00054, 0.00054, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00053, 0.00052, 0.00052, 0.00051, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.0005, 0.00049, 0.00049, 0.00049, 0.00049, 0.00048, 0.00048, 0.00048, 0.00048, 0.00048, 0.00048, 0.00048, 0.00048, 0.00048, 0.00047, 0.00047, 0.00047, 0.00047, 0.00046, 0.00046, 0.00046, 0.00046, 0.00046, 0.00046, 0.00045, 0.00045, 0.00045, 0.00045, 0.00045, 0.00044, 0.00044, 0.00043, 0.00043, 0.00043, 0.00043, 0.00042, 0.00042, 0.00042, 0.00042, 0.00042, 0.00041, 0.00041, 0.0004, 0.0004, 0.0004, 0.0004, 0.0004, 0.00039, 0.00039, 0.00038, 0.00038, 0.00037, 0.00037, 0.00037, 0.00037, 0.00037, 0.00036, 0.00036, 0.00036, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00035, 0.00034, 0.00034, 0.00034, 0.00033, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00032, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.00031, 0.0003, 0.0003, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00029, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00028, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00027, 0.00026, 0.00026, 0.00026, 0.00026, 0.00026, 0.00025, 0.00025, 0.00025, 0.00025, 0.00025, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00024, 0.00023, 0.00023, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00022, 0.00021, 0.00021, 0.00021, 0.00021, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.00019, 0.00019, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00018, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00017, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.99547, 0.93258, 0.27413, 0.18293, 0.07584, 0.05812, 0.04456, 0.04295, 0.03199, 0.02654, 0.02354, 0.02181, 0.02153, 0.02044, 0.02015, 0.01815, 0.01709, 0.01409, 0.01301, 0.01276, 0.01228, 0.0119, 0.01034, 0.00867, 0.0085, 0.00697, 0.00652, 0.0065, 0.00639, 0.00634, 0.00595, 0.0057, 0.00528, 0.00527, 0.00503, 0.00493, 0.00453, 0.00419, 0.00395, 0.00377, 0.00352, 0.0034, 0.00314, 0.00295, 0.00276, 0.00268, 0.00236, 0.00234, 0.00229, 0.00213, 0.00211, 0.00201, 0.00176, 0.00175, 0.00159, 0.00159, 0.00159, 0.00153, 0.0015, 0.0015, 0.00131, 0.00125, 0.00121, 0.00118, 0.00116, 0.00115, 0.00105, 0.00104, 0.00099, 0.00098, 0.00087, 0.00084, 0.00084, 0.00082, 0.0008, 0.00079, 0.00078, 0.00076, 0.00074, 0.00071, 0.0007, 0.00068, 0.00068, 0.00067, 0.00065, 0.00061, 0.00061, 0.00058, 0.00058, 0.00057, 0.00056, 0.00055, 0.00054, 0.00053, 0.00049, 0.00048, 0.00044, 0.00043, 0.00043, 0.00042, 0.00041, 0.0004, 0.00039, 0.00038, 0.00038, 0.00037, 0.00036, 0.00036, 0.00036, 0.00036, 0.00035, 0.00035, 0.00034, 0.00034, 0.00032, 0.00031, 0.00031, 0.00026, 0.00026, 0.00025, 0.00025, 0.00025, 0.00024, 0.00023, 0.00023, 0.00023, 0.00023, 0.00022, 0.00021, 0.00021, 0.00021, 0.00021, 0.00021, 0.0002, 0.0002, 0.0002, 0.0002, 0.00019, 0.00019, 0.00019, 0.00018, 0.00018, 0.00018, 0.00016, 0.00016, 0.00016, 0.00016, 0.00016, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00015, 0.00014, 0.00014, 0.00014, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00013, 0.00012, 0.00012, 0.00012, 0.00012, 0.00011, 0.00011, 0.00011, 0.00011, 0.0001, 0.0001, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 9e-05, 8e-05, 8e-05, 8e-05, 8e-05, 8e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 7e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 6e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 5e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 4e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 3e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 2e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 1e-05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [2, 2, 5, 5, 5, 5, 5, 5, 2, 6, 2, 1, 5, 1, 5, 5, 2, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 11, 12, 11, 11, 13, 13, 11, 13, 12, 12, 12, 12, 12, 13, 11, 13, 11, 16, 13, 11, 11, 16, 13, 16, 11, 12, 17, 13, 11, 17, 11, 13, 13, 12, 12, 13, 12, 12, 17, 11, 13, 13, 11, 16, 11, 12, 12, 13, 12, 15, 12, 11, 11, 16, 16, 12, 13, 13, 13, 16, 12, 15, 13, 15, 13, 13, 13, 12, 13, 13, 12, 13, 12, 16, 13, 15, 15, 11, 15, 14, 12, 11, 11, 17, 12, 11, 13, 13, 12, 13, 15, 13, 13, 12, 13, 13, 11, 12, 18, 12, 13, 13, 13, 12, 11, 13, 11, 13, 13, 15, 12, 12, 17, 12, 13, 11, 13, 15, 12, 14, 12, 16, 17, 14, 11, 13, 12, 18, 11, 11, 18, 13, 16, 11, 16, 13, 12, 17, 12, 13, 17, 12, 13, 15, 13, 17, 12, 15, 15, 15, 11, 13, 12, 11, 13, 13, 12, 11, 13, 13, 16, 15, 15, 13, 11, 11, 13, 12, 18, 13, 13, 11, 12, 11, 16, 13, 11, 13, 18, 12, 13, 17, 12, 13, 14, 12, 13, 13, 17, 12, 17, 16, 18, 11, 11, 11, 15, 12, 16, 13, 11, 15, 17, 17, 11, 18, 11, 15, 17, 16, 13, 13, 12, 13, 11, 11, 12, 13, 16, 12, 12, 11, 13, 13, 15, 17, 16, 18, 12, 15, 11, 12, 12, 11, 14, 13, 13, 15, 12, 12, 14, 13, 13, 11, 18, 13, 15, 12, 13, 17, 18, 13, 16, 15, 13, 17, 12, 15, 17, 13, 11, 18, 15, 18, 13, 14, 15, 17, 14, 11, 13, 13, 12, 11, 13, 15, 15, 13, 12, 15, 15, 11, 13, 12, 13, 12, 13, 13, 13, 11, 16, 11, 14, 12, 16, 12, 17, 11, 11, 12, 12, 16, 17, 15, 15, 16, 13, 15, 13, 15, 11, 17, 16, 14, 14, 13, 18, 13, 17, 16, 11, 11, 12, 13, 11, 15, 16, 13, 16, 13, 12, 16, 15, 17, 18, 12, 13, 11, 13, 12, 14, 18, 12, 16, 13, 12, 12, 18, 15, 17, 15, 13, 11, 16, 16, 13, 18, 16, 17, 13, 17, 15, 13, 15, 13, 17, 11, 15, 17, 11, 13, 11, 15, 13, 12, 13, 11, 13, 15, 13, 14, 12, 14, 15, 12, 13, 11, 13, 17, 11, 15, 11, 16, 11, 13, 18, 16, 17, 14, 13], ["Others", "ArrowButton", "<PERSON><PERSON>", "Checkbox", "CloseButton", "Inputbox", "Icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MinimizeButton", "Radiobutton", "Cell", "Title", "SectionTitle", "SelectedTab", "BrowserTab", "SectionTab", "PopupTitle", "Logo", "ExcelSheet"], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], ["checked"]], "text": [[[0, 0, 1920, 880], [19, 18, 80, 45], [85, 16, 250, 53], [1220, 24, 1261, 42], [1262, 24, 1310, 38], [1338, 24, 1402, 38], [1404, 24, 1439, 38], [1468, 24, 1513, 38], [1515, 24, 1570, 38], [1598, 24, 1653, 38], [1656, 24, 1731, 38], [1759, 24, 1789, 38], [1791, 24, 1834, 38], [1836, 24, 1889, 38], [19, 78, 163, 102], [392, 83, 412, 97], [586, 119, 625, 130], [627, 119, 677, 130], [1006, 118, 1033, 130], [1035, 118, 1046, 130], [1048, 119, 1111, 133], [19, 156, 32, 173], [35, 155, 72, 173], [75, 155, 120, 179], [123, 155, 144, 173], [146, 155, 183, 173], [186, 155, 284, 178], [286, 155, 303, 174], [306, 156, 328, 173], [331, 156, 395, 173], [398, 159, 414, 174], [19, 183, 107, 201], [109, 184, 151, 201], [153, 183, 185, 202], [188, 183, 241, 206], [243, 184, 291, 202], [294, 183, 341, 201], [344, 186, 360, 202], [20, 212, 144, 235], [146, 212, 184, 230], [187, 213, 221, 230], [224, 212, 271, 230], [274, 212, 327, 230], [330, 216, 357, 230], [360, 213, 394, 230], [586, 208, 611, 219], [613, 208, 651, 219], [1006, 207, 1040, 219], [20, 244, 92, 260], [20, 290, 37, 309], [41, 291, 119, 309], [122, 290, 159, 308], [162, 290, 215, 309], [217, 290, 249, 309], [252, 290, 329, 313], [332, 290, 410, 313], [413, 294, 440, 309], [586, 296, 648, 310], [649, 296, 687, 307], [1006, 295, 1032, 307], [1034, 296, 1071, 307], [19, 318, 53, 336], [56, 322, 123, 337], [126, 318, 174, 336], [177, 322, 232, 341], [234, 318, 346, 337], [20, 347, 130, 370], [132, 348, 153, 365], [156, 347, 225, 365], [229, 348, 272, 365], [275, 348, 308, 365], [311, 347, 398, 365], [401, 348, 452, 365], [20, 376, 107, 399], [110, 376, 183, 399], [185, 376, 247, 395], [249, 376, 300, 395], [304, 376, 427, 399], [585, 384, 636, 396], [20, 405, 85, 423], [89, 406, 140, 423], [143, 406, 169, 424], [172, 405, 230, 428], [233, 408, 288, 428], [291, 405, 340, 424], [20, 455, 37, 472], [40, 454, 77, 473], [80, 455, 143, 473], [147, 454, 258, 473], [261, 454, 282, 473], [284, 455, 317, 472], [321, 454, 419, 477], [421, 454, 453, 472], [20, 482, 77, 506], [80, 486, 130, 501], [133, 486, 170, 505], [173, 482, 219, 501], [221, 483, 254, 500], [258, 483, 306, 501], [308, 482, 374, 501], [377, 482, 420, 501], [604, 477, 661, 491], [19, 511, 65, 530], [68, 515, 106, 534], [109, 515, 155, 534], [158, 511, 228, 530], [229, 512, 263, 530], [265, 511, 313, 530], [317, 515, 342, 530], [345, 514, 403, 534], [20, 540, 75, 559], [79, 544, 104, 559], [106, 544, 144, 563], [147, 540, 191, 558], [194, 540, 269, 558], [272, 540, 362, 563], [21, 569, 114, 592], [20, 618, 74, 637], [77, 618, 124, 637], [150, 690, 241, 704], [245, 690, 291, 704], [213, 744, 262, 758]], ["RPA Challenge Input Forms Shortest Path Movie Search Invoice Extraction RPA Stock Market Instructions EN Phone Number Role in Company 1. The goal of this challenge is to create a workflow that will input data from a spreadsheet into the form fields on the Last Name Email screen. 2. Beware! The fields will change position on Company Name First Name the screen after every submission throughout 10 rounds thus the workflow must correctly identify where each spreadsheet Address record must be typed every time. 3. The actual countdown of the challenge will begin once you click the Start button until SUBMIT then you may submit the form as many times as you wish without receiving penalties. Good luck! DOWNLOAD EXCEL START", "RPA", "Challenge", "Input", "Forms", "Shortest", "Path", "Movie", "Search", "Invoice", "Extraction", "RPA", "Stock", "Market", "Instructions", "EN", "Phone", "Number", "Role", "in", "Company", "1.", "The", "goal", "of", "this", "challenge", "is", "to", "create", "a", "workflow", "that", "will", "input", "data", "from", "a", "spreadsheet", "into", "the", "form", "fields", "on", "the", "Last", "Name", "Email", "screen.", "2.", "Beware!", "The", "fields", "will", "change", "position", "on", "Company", "Name", "First", "Name", "the", "screen", "after", "every", "submission", "throughout", "10", "rounds", "thus", "the", "workflow", "must", "correctly", "identify", "where", "each", "spreadsheet", "Address", "record", "must", "be", "typed", "every", "time.", "3.", "The", "actual", "countdown", "of", "the", "challenge", "will", "begin", "once", "you", "click", "the", "Start", "button", "until", "SUBMIT", "then", "you", "may", "submit", "the", "form", "as", "many", "times", "as", "you", "wish", "without", "receiving", "penalties.", "Good", "luck!", "DOWNLOAD", "EXCEL", "START"], [0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.98, 0.99, 0.99, 0.99]], "relations": [[[11, 10], [12, 9], [13, 8], [5, 3], [6, 2], [7, 1], [4, 0]], [0.99998, 0.99998, 0.99998, 0.99998, 0.99998, 0.99998, 0.99998], [[581, 151, 407, 32], [581, 410, 408, 38], [582, 328, 405, 32], [582, 239, 407, 32], [585, 117, 91, 12], [585, 207, 65, 12], [585, 295, 101, 15], [585, 383, 52, 12], [1002, 321, 406, 39], [1002, 236, 406, 35], [1004, 149, 404, 34], [1006, 117, 104, 16], [1006, 207, 34, 12], [1006, 295, 65, 12]], [0, 0, 0, 0, 0, 0, 0], ["Label", "Context", "Header"]], "content_type": null, "version": "23.4.10.2"}