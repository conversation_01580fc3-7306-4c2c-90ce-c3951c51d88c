[{"Text": "France", "Value": "France", "Id": 17, "ParentId": 8, "DOMId": "152", "DriverId": "{\"Data\":\"{\\\"BrowserType\\\":3,\\\"BrowserWindow\\\":5572782,\\\"ElementId\\\":\\\"{\\\\\\\"tabId\\\\\\\":881917881,\\\\\\\"frameId\\\\\\\":0,\\\\\\\"elementId\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"virtualId\\\\\\\":\\\\\\\"\\\\\\\"}\\\",\\\"PortableNativeHostPid\\\":0,\\\"WindowsNativeHostPid\\\":43432}\",\"Type\":2}", "ElementType": "DropDown", "ElementTypeSource": "DOMFeatures", "Area": "955, 19, 480, 58", "Attributes": {"tag": "DIV", "type": "text", "class": "ac-ls-dropdown-select"}, "Anchor": {"Text": "France", "Value": null, "Id": 35, "ParentId": 17, "DOMId": "155", "DriverId": "{\"Data\":\"{\\\"BrowserType\\\":3,\\\"BrowserWindow\\\":5572782,\\\"ElementId\\\":\\\"{\\\\\\\"tabId\\\\\\\":881917881,\\\\\\\"frameId\\\\\\\":0,\\\\\\\"elementId\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"virtualId\\\\\\\":\\\\\\\"\\\\\\\"}\\\",\\\"PortableNativeHostPid\\\":0,\\\"WindowsNativeHostPid\\\":43432}\",\"Type\":2}", "ElementType": "Text", "ElementTypeSource": "DOM", "Area": "978, 23, 407, 49", "Attributes": {"tag": "SPAN", "class": "ac-ls-dropdown-copy"}, "Anchor": null, "TextAnchor": null, "IsTextNode": false}, "TextAnchor": null, "IsTextNode": false}]