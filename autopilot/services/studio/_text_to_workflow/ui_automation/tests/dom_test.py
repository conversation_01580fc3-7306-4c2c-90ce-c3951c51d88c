import json
import os
import sys
import unittest
import xml.etree.ElementTree as ET

import cv2
from uipath_cv_client.client import CvClient
from uipath_cv_client.cv_dom_extractor import CVDomExtractor
from uipath_cv_client.dom_processing import DomSourceType, get_dom_tree_from_elements, parse_raw_dom

from services.studio._text_to_workflow.ui_automation.object_repository_parser import ObjectRepositoryParser
from services.studio._text_to_workflow.ui_automation.options import opt

dirname = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(dirname, "data")


class DOMTests(unittest.TestCase):
    def _get_dom(self, file_path: str, img_path=None, is_or_dom: bool = False):
        image = None
        if img_path is not None:
            image = cv2.imread(img_path, cv2.IMREAD_COLOR)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            dom_extractor = CVDomExtractor(CvClient.from_legacy_opt(opt))
            raw_dom = dom_extractor.extract_visual_dom_from_image(image)
            dom_type = DomSourceType.CV

        if file_path is not None:
            with open(file_path) as f:
                json_content = json.load(f)
                if is_or_dom:
                    or_parser = ObjectRepositoryParser()
                    raw_dom = or_parser.parse(json_content)
                    dom_type = DomSourceType.ObjectRepository
                else:
                    raw_dom = json_content["RawDOM"]
                    dom_type = DomSourceType.Driver

        dom = parse_raw_dom(
            raw_dom,
            dom_type=dom_type,
            options=opt,
            image=image,
            file_path=file_path,
            use_cv_cache=True,
        )
        return dom

    def _find_elem_with_id(self, dom, element_id):
        element = self._find_element_with_property_value(dom, "Id", element_id)
        return element

    def _find_element_with_property_value(self, dom, property_name, property_value):
        element = dom.find(f".//*[@{property_name}='{property_value}']")
        return element

    def test_button(self):
        dom = self._get_dom(os.path.join(data_path, r"expedia_step1.json"))
        button = self._find_elem_with_id(dom, 212)
        assert "Jul 4" in button.get("Text")
        button = self._find_elem_with_id(dom, 209)
        assert "Jul 3" in button.get("Text")

        # cv data overrites raw dom
        button = self._find_elem_with_id(dom, 101)
        assert button.tag == "InputBox"

    def test_cv_dom(self):
        img_path = os.path.join(data_path, r"rpa_challenge.png")
        dom = self._get_dom(None, img_path)
        submit_button = self._find_element_with_property_value(dom, "Text", "SUBMIT")
        assert submit_button.tag == "Button"

        firstNameNode = self._find_element_with_property_value(dom, "Text", "First Name")
        assert firstNameNode is not None

        addressNode = self._find_element_with_property_value(dom, "Text", "Address")
        assert addressNode is not None

    def test_or(self):
        json_path = os.path.join(data_path, "object_repository", "netsuite.json")
        dom = self._get_dom(json_path, None, is_or_dom=True)

        emailNode = self._find_element_with_property_value(dom, "Label", "Email")
        assert emailNode is not None
        assert emailNode.tag == "InputBox"

        submitNode = self._find_element_with_property_value(dom, "Label", "Submit")
        assert submitNode is not None
        assert submitNode.tag == "Button"

    def test_or_big(self):
        json_path = os.path.join(data_path, "object_repository", "uipath_assistant.json")
        dom = self._get_dom(json_path, None, is_or_dom=True)

        genericButtonNode = self._find_element_with_property_value(dom, "Label", "ContinueButton")
        assert genericButtonNode is not None
        assert genericButtonNode.tag == "Button"

    # def test_remove_icons(self):
    #     dom = self._get_dom(os.path.join(data_path, r"workday.json"))
    #     icon = self._find_elem_with_id(dom, 1814)
    #     assert icon is None

    def _get_element_innertext(self, element: ET.Element):
        text = ""

        if ("Text" in element.attrib) and (element.attrib["Text"] is not None):
            text = element.attrib["Text"]
        for child in element:
            text = text + " " + self._get_element_innertext(child)
        return text

    def test_order_elements(self):
        dom = self._get_dom(os.path.join(data_path, r"workday_step4.json"))
        from_container = dom.find(".//*[@Id='28']..")
        from_label = from_container[0]
        assert from_label.get("Text") == "From"
        from_inputbox = from_container[1]
        assert from_inputbox.tag == "InputBox"

        from_inputbox_inner_text = self._get_element_innertext(from_inputbox)
        assert "MM/DD/YYYY" in from_inputbox_inner_text.replace(" ", "")

    def test_order_elements_2(self):
        dom = self._get_dom(os.path.join(data_path, r"expedia_step1.json"))
        # container with input elements
        container = dom.find(".//*[@Id='217']..")
        assert container[1].tag == "Container"
        assert "Jul 3" in container[1][0].get("Text")
        assert "Jul 4" in container[1][1].get("Text")

        assert container[2].get("Text") == "Add a flight"
        assert container[3].get("Text") == "Add a car"

        # navbar
        nav = dom.find(".//*[@Id='101']..")
        assert [e.get("Text") for e in nav] == [
            "Stays",
            "Flights",
            "Cars",
            "Packages",
            "Things to do",
            "Cruises",
        ]

    def test_order_elements_3(self):
        dom = self._get_dom(os.path.join(data_path, r"rpa_challenge.json"))
        # container with input boxes
        container = dom.find(".//*[@Text='Phone Number']../..")
        assert [e[0].get("Text") for e in container] == [
            "Phone Number",
            "Role in Company",
            "Last Name",
            "Email",
            "Company Name",
            "First Name",
            "Address",
        ]

    def test_text_attribute(self):
        with open(os.path.join(dirname, "./rawdom/raw_dom_1.json"), encoding="utf-8") as f:
            raw_dom = json.load(f)
        dom = parse_raw_dom(
            raw_dom,
            dom_type=DomSourceType.Driver,
            options=opt,
            image=None,
            file_path=None,
            use_cv_cache=False,
        )
        assert dom[0].attrib["Text"] == "Apply"

    def test_button_inside_inputbox(self):
        with open(os.path.join(dirname, "./rawdom/amazon_search_icon.json"), encoding="utf-8") as f:
            raw_dom = json.load(f)
        dom_elements = get_dom_tree_from_elements(raw_dom)
        xml_dom = parse_raw_dom(
            dom_elements,
            dom_type=DomSourceType.Driver,
            options=opt,
            image=None,
            file_path=None,
            use_cv_cache=False,
        )
        elem = xml_dom.find(".//*[@Id='24']")
        assert elem is not None
        assert elem.tag == "Button"

    def test_merge_inner_text(self):
        with open(os.path.join(dirname, "./rawdom/apple_dropdown.json"), encoding="utf-8") as f:
            raw_dom = json.load(f)
        dom_elements = get_dom_tree_from_elements(raw_dom)
        xml_dom = parse_raw_dom(
            dom_elements,
            dom_type=DomSourceType.Driver,
            options=opt,
            image=None,
            file_path=None,
            use_cv_cache=False,
        )
        assert len(xml_dom) == 1
        assert xml_dom[0].attrib["Text"] == "France"

    def test_button_with_icon_and_text(self):
        # test if flat elements are reordered correctly using the relative area
        # this is important for understand button content
        with open(os.path.join(dirname, "./rawdom/expedia_button.json"), encoding="utf-8") as f:
            dom_elements = json.load(f)
        xml_dom = parse_raw_dom(
            dom_elements,
            dom_type=DomSourceType.Driver,
            options=opt,
            image=None,
            file_path=None,
            use_cv_cache=False,
        )
        assert len(xml_dom) == 1  # only one 1 button
        button = xml_dom[0]
        # inside the button there are Icon and Text
        assert button[0].tag == "Icon"
        assert button[1].tag == "Text"


if __name__ == "__main__":
    suite = unittest.defaultTestLoader.loadTestsFromTestCase(DOMTests)
    success = unittest.TextTestRunner().run(suite).wasSuccessful()
    sys.exit(not success)
