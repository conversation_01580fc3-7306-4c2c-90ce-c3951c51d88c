import time
import xml.etree.ElementTree as ET
from typing import Dict, List

import langchain.chains
import langchain.prompts
import langchain.schema
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.ui_automation.action import Step, build_actions_definition_str, build_step_examples
from services.studio._text_to_workflow.ui_automation.models.autopilot.prompting.text_to_ui_activities_message import (
    chat_screen_shot_info as chat_screen_shot_info,
)
from services.studio._text_to_workflow.ui_automation.models.autopilot.prompting.text_to_ui_activities_message import (
    object_dom_instruction as object_dom_instruction,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.text_to_action_message import (
    agent_important_considerations,
)
from services.studio._text_to_workflow.ui_automation.models.uia_state import StateProcessor, UIAState
from services.studio._text_to_workflow.ui_automation.utils.llm_gateway import clean_text

default_prompt_config: Dict = {
    "max_dom_tokens": 4000,
    "max_url_size": 150,
}


def build_previous_steps_str(steps: List[Step], prompt_config: dict) -> str:
    """Returns a string representation of the previous steps"""
    if len(steps) == 0 or sum([len(step.actions) for step in steps]) == 0:
        return "None"
    steps_representation = []

    step_index = 0

    for current_step in steps:
        metadata_representation = []  # such as title, url, etc
        if len(current_step.actions) == 0:
            continue

        if current_step.screen_info:
            # maybe we could do a better trimming here for the url instead of just max size
            # eg: firstly trim query params if there are any present

            url = current_step.screen_info["url"]
            title = current_step.screen_info["title"]
            max_url_size = prompt_config["max_url_size"]
            url_and_title = f"  Url: {url[:max_url_size]}\nTitle: {title}" if url else f"Title: {title}"
            screen_info = url_and_title
            metadata_representation.append(screen_info)

        action_format = prompt_config["action_format"]
        actions_representation = ["  Actions:"] + [f"  - {action.prompt_action_repr(format=action_format)}" for action in current_step.actions]

        step_representation = "\n".join([f"Step {step_index + 1}:"] + metadata_representation + actions_representation)
        step_index = step_index + 1
        steps_representation.append(step_representation)
    return "\n".join(steps_representation)


def build_step_str(step: Step) -> str:
    step_str = ""
    model_observation = step.additional_parameters["observation"]
    step_str += " - Observation: " + model_observation + "\n"
    if len(step.actions) > 1:
        step_str += " - Actions:\n"
    else:
        step_str += " - Action:"
    for action in step.actions:
        if len(step.actions) > 1:
            step_str += f"  - {action.description}"
        else:
            step_str += f" {action.description}"
        # for extract data action
        if action.result is not None:
            step_str += f"\n    result: {action.result}"
        if len(action.execution_error_message) > 0:
            step_str += f"\n    execution error: {action.execution_error_message}"
    return step_str


def build_variables_str(variables: Dict[str, str], prompt_config: dict) -> str:
    """Returns a string representation of the variables"""
    variables_str = ""
    if len(variables) > 0:
        variables_str = "  " + ",  ".join([f"${v}" for v, v_type in variables.items()])
    return variables_str


def build_dom_str(dom: ET.Element, prompt_config: dict) -> str:
    """Returns a string representation of the DOM"""
    dom_str = ET.tostring(dom, encoding="utf-8").decode("utf-8")
    dom_str = truncate_dom_str(dom_str, prompt_config.get("max_dom_tokens", 10000))
    return dom_str


def build_user_prompt_data(uia_state: UIAState, prompt_config: dict) -> dict:
    all_variables = {**uia_state.input_variables, **uia_state.output_variables, **uia_state.variables}
    url = uia_state.url[: prompt_config["max_url_size"]] if uia_state.url else None
    title_and_url = f"Title: {uia_state.title}\nURL: {url}" if url else f"Title: {uia_state.title}"

    restrict_dom_to_visible_elements = prompt_config.get("restrict_dom_to_visible_elements", False)
    sp = StateProcessor(uia_state)
    if restrict_dom_to_visible_elements:
        xml_dom = sp.get_xml_dom_for_visible_nodes()
    else:
        xml_dom = uia_state.xml_dom
    nowgmt = time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())

    prompt_data = {
        "task_description": uia_state.task_description,
        "dom_str": build_dom_str(xml_dom, prompt_config),
        "previous_steps_str": build_previous_steps_str(uia_state.previous_steps, prompt_config),
        "title_and_url": title_and_url,
        "date": nowgmt,
        # only this is used in the prompt
        "all_variables_str": build_variables_str(all_variables, prompt_config),
        # for logging
        "variables_str": build_variables_str(uia_state.variables, prompt_config),
        "input_variables_str": build_variables_str(uia_state.input_variables, prompt_config),
        "output_variables_str": build_variables_str(uia_state.output_variables, prompt_config),
        "image_base64": uia_state.image_base64,
        "reflection_str": "Proposed plan: " + uia_state.reflection if len(uia_state.reflection) > 0 else "",
    }

    if prompt_config["is_ui_agent"]:
        summary_str = uia_state.summary
        if len(summary_str) == 0 and len(uia_state.previous_steps) > 0:
            summary_str = uia_state.previous_steps[-1].additional_parameters["summary"]
        prompt_data["summary_str"] = summary_str

    for key, value in prompt_data.items():
        if key != "image_base64":
            prompt_data[key] = clean_text(value)
    return prompt_data


def build_autopilot_system_prompt_data(image_base64, screen_properties: dict, prompt_config) -> dict:
    action_format = prompt_config["action_format"]

    is_object_dom = "object_dom" in screen_properties and screen_properties["object_dom"]
    is_desktop = "desktop" in screen_properties and screen_properties["desktop"]

    actions_definitions_str = build_actions_definition_str(False, is_desktop, action_format)
    steps_examples = build_step_examples(False, is_desktop, action_format)

    if image_base64 is not None:
        additional_screen_info = chat_screen_shot_info
    else:
        additional_screen_info = ""

    action_format = prompt_config["action_format"]

    return {
        "actions_definitions_str": actions_definitions_str,
        "steps_examples": steps_examples,
        "additional_screen_info": additional_screen_info,
        "object_dom_instruction": object_dom_instruction if is_object_dom else "",
    }


def build_ui_agent_system_prompt_data(image_base64, screen_properties: dict, prompt_config) -> dict:
    is_ui_agent = prompt_config["is_ui_agent"]
    action_format = prompt_config["action_format"]
    is_desktop = "desktop" in screen_properties and screen_properties["desktop"]

    actions_definitions_str = build_actions_definition_str(is_ui_agent, is_desktop, action_format)
    steps_examples = build_step_examples(is_ui_agent, is_desktop, action_format)

    return {
        "actions_definitions_str": actions_definitions_str,
        "steps_examples": steps_examples,
        "agent_important_considerations": agent_important_considerations,
    }


def build_user_message(
    user_message_template: str | None, user_prompt_data: dict, llm_model_name: str, first_text: bool = False
) -> langchain.schema.HumanMessage:
    user_message_content = []

    if "image_base64" in user_prompt_data and user_prompt_data["image_base64"] is not None:
        image_base64 = user_prompt_data["image_base64"]
        is_openai_compatible = (
            "gpt" in llm_model_name
            or "InternVL" in llm_model_name
            or "Qwen" in llm_model_name
            or "o3-mini" in llm_model_name
            or "gemini" in llm_model_name
            or "meta-llama" in llm_model_name
        )

        if not is_openai_compatible or (settings.USE_LLM_GATEWAY and "gemini" in llm_model_name):
            user_message_content.append(
                {
                    "type": "image/jpeg",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                }
            )
        elif is_openai_compatible:  # default
            user_message_content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                }
            )
        else:
            raise ValueError(f"Not supported LLM model with image: {llm_model_name}")

    if user_message_template is not None and user_message_template != "":
        user_message_content.append(
            {
                "type": "text",
                "text": user_message_template.format(**user_prompt_data),
            }
        )
    if first_text:
        user_message_content = user_message_content[::-1]

    return langchain.schema.HumanMessage(role="user", content=user_message_content)
