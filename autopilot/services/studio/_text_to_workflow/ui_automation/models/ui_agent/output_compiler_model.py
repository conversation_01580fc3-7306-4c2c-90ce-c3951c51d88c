import copy
from typing import List

import langchain.chains
import langchain.prompts
import langchain.schema

from services.studio._text_to_workflow.ui_automation.action import Step, ValidationException
from services.studio._text_to_workflow.ui_automation.llm_chat_providers import get_chat_provider, get_model_name
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_user_message,
    build_user_prompt_data,
    default_prompt_config,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.history_processor import HistoryProcessorModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.compute_output_prompt_message import (
    output_compiler_screen_shot_info,
    output_compiler_system_message,
    output_compiler_user_message,
)
from services.studio._text_to_workflow.ui_automation.utils import log_utils
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json
from services.studio._text_to_workflow.ui_automation.utils.ui_agent_types import CompilerResponse
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class OutputCompilerModel:
    def __init__(self, options):
        self.options = copy.deepcopy(options)
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))
        self.use_structured_output = False
        self.history_processor_model = HistoryProcessorModel(options)
        # increase max output tokens for final response
        self.options["engine_config"]["max_tokens"] = 3000

    def build_messages(self, uia_state: UIAState, current_step: Step, req_options: dict | None = None) -> List[langchain.schema.BaseMessage]:
        system_prompt_data = {"additional_screen_info": output_compiler_screen_shot_info}
        system_message_text = output_compiler_system_message.format(**system_prompt_data)

        user_prompt_data = build_user_prompt_data(uia_state, self.prompt_config)

        current_step_str = self.history_processor_model.build_step_str(current_step)
        history_str = self.history_processor_model.build_history_str(uia_state=uia_state)

        user_message_template = output_compiler_user_message
        user_prompt_data["history_str"] = history_str
        user_prompt_data["current_step_str"] = current_step_str

        model_name = get_model_name(self.options, req_options=req_options)

        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        messages.append(build_user_message(user_message_template, user_prompt_data, llm_model_name=model_name))
        return messages

    async def predict(self, uia_state: UIAState, current_step: Step, predict_info: dict, req_options: dict | None = None) -> dict | None:
        messages = self.build_messages(uia_state, current_step=current_step, req_options=req_options)
        response_format = CompilerResponse if self.use_structured_output else None

        chat_provider = get_chat_provider(
            self.options, req_options=req_options, consuming_feature_type=ConsumingFeatureType.SCREEN_AGENT_INFO_EXTRACTION, response_format=response_format
        )
        try:
            out = await chat_provider.send_message(messages=messages, predict_info=predict_info)

            # print("compiled output:", out)
            if log_utils.ENABLE_STREAM_LOG:
                payload = {
                    "model_thought": "Output Compiler",
                    "model_actions": out.content,
                    "screenshot": uia_state.image_base64,
                }
                log_utils.send_stream_log(payload)

            if self.use_structured_output:
                out_dict = out.model_dump()
                console_logger.debug("\nOuput compiler output: %s", str(out_dict))
            else:
                console_logger.debug("\nOuput compiler output: %s", str(out.content))
                out_dict = parse_message_json(str(out.content))

            if out_dict is None or "task_result" not in out_dict:
                console_logger.error("Task_result not found in output")
                return out_dict
            else:
                return out_dict["task_result"]
        except ValidationException as ve:
            console_logger.error("Validation error: %s", ve.message)
            raise ve
        except Exception as e:
            console_logger.error("Error in output compiler: %s", e)
            raise e
