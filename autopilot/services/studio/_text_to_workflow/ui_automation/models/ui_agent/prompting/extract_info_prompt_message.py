# flake8: noqa
extract_system_message = """You are an UI automation agent expert that given a prompt and a screen extracts relevant information.
Screen is represented as a DOM which contains elements like buttons <Button>, texts <Texts>, inputboxes <InputBox> etc. An element is uniquely identified by his ID.
{additional_screen_info}

Your response should be in json format and should contain the following structure:
 - description. Short and concise overall description of the data (couple of words)
 - data as a key value pairs

Example of a valid JSON response:
{{
   "description": "Personal information",
   "data": 
            {{
                "FirstName": "John",
                "LastName": "Doe",
                "Email": "<EMAIL>"
            }}
}}

"""

extract_screen_shot_info = "Additionaly you will receive a screenshot of the viewport. Some elements from the DOM might not appear in the screenshot."


extract_user_message = """
Current screen:
{title_and_url}
DOM: {dom_string}
 
Extraction task: "{task_description}"
Make sure your response is a valid json (double quotes, no comment, no trailing comma...)
Agent response in json format:
"""

extract_message_invalid_reply_message = """System response:
Message invalid. {error_message}. Ensure your response is complete and is a valid json. Try again.

Agent response in json format:
"""
