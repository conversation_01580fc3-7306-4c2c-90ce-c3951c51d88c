import asyncio
import copy
import time
from typing import List, Tuple

import langchain.schema

from services.studio._text_to_workflow.ui_automation.action import ActionType, Step
from services.studio._text_to_workflow.ui_automation.cache import CacheableUIAgentStep, add_review_observation_to_cache, get_review_observation_from_cache
from services.studio._text_to_workflow.ui_automation.llm_chat_providers import (
    LOGGER,
    get_chat_provider,
    get_model_name,
)
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_user_message,
    default_prompt_config,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.objects import ModelResponse
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.refresh_history_prompt_message import (
    refresh_observation_system_message,
    refresh_observation_user_message,
)
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json
from services.studio._text_to_workflow.utils.inference.llm_schema import (
    ConsumingFeatureType,
)


class HistoryProcessorModel:
    def __init__(self, options: dict):
        self.options = copy.deepcopy(options)
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))
        self.use_cache = True
        self.consuming_feature_type = ConsumingFeatureType.SCREEN_AGENT
        self.summary_interval = self.prompt_config["summary_interval"]

    def build_messages(
        self,
        states_actions: list[str],
        current_observation: str,
        screenshot_base64: str,
        task_description: str,
        req_options: dict | None = None,
    ) -> List[langchain.schema.BaseMessage]:
        steps_list_str = []

        nowgmt = time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime())

        for i in range(len(states_actions)):
            state_action_message = states_actions[i]
            step_msg = f"Step {i}:\n {state_action_message}"
            steps_list_str.append(step_msg)

        history_msg = "\n".join(steps_list_str)
        user_prompt_elements = {
            "date": nowgmt,
            "task_description": task_description,
            "screen_observation": current_observation,
            "execution_trace_str": history_msg,
            "image_base64": screenshot_base64,
        }

        system_message_text = refresh_observation_system_message
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        model_name = get_model_name(self.options, req_options)
        messages.append(build_user_message(refresh_observation_user_message, user_prompt_elements, llm_model_name=model_name))

        return messages

    async def review_last_step(
        self, uia_state: UIAState, step_model_response: ModelResponse, predict_info: dict, req_options: dict | None = None, fire_and_forget=False
    ) -> bool:
        session_id = uia_state.session_id
        image_base64 = uia_state.image_base64
        task_description = uia_state.task_description

        chat_provider = get_chat_provider(
            self.options,
            req_options,
            self.consuming_feature_type,
        )

        history_observation: list[Tuple[str, str]] = []
        reviewed_observation_infos: dict[int, str] = {}
        if self.use_cache:
            reviewed_observation_infos_list = get_review_observation_from_cache(session_id)
            reviewed_observation_infos = {item[1]: item[0] for item in reviewed_observation_infos_list}
        else:
            return False

        for i in range(len(uia_state.previous_steps)):
            step = uia_state.previous_steps[i]
            if i in reviewed_observation_infos:
                step.additional_parameters["reviewed_observation"] = reviewed_observation_infos[i]["reviewed_observation"]

            step_description = self.build_step_str(step)
            history_observation.append(step_description)

        current_step_observation = step_model_response.observation

        messages = self.build_messages(history_observation, current_step_observation, image_base64, task_description, req_options)

        async def review_and_save(messages):
            try:
                start_time = time.time()
                reviewed_obs_predict_info = {}
                gpt_response = await chat_provider.send_message(messages, reviewed_obs_predict_info)
                elapsed_time = time.time() - start_time
                console_logger.debug(f"Review operation elapsed:{elapsed_time}")
                response = parse_message_json(str(gpt_response.content))
                console_logger.debug(f"Initial observation:{current_step_observation}")
                console_logger.debug(f"response:{response}")
                if response is not None and "reviewed_observation" in response:
                    step_model_response.review_observation = response["reviewed_observation"]
                    reviewed_observation_info = {
                        "reviewed_observation": response["reviewed_observation"],
                        "token_usage": reviewed_obs_predict_info["requests"][0]["token_usage"],
                        "model": reviewed_obs_predict_info["requests"][0]["model"],
                    }
                    add_review_observation_to_cache(session_id, len(uia_state.previous_steps), reviewed_observation_info)
            except Exception as e:
                LOGGER.exception(f"Error in review_and_save: {e}")

        if fire_and_forget:
            asyncio.ensure_future(review_and_save(messages))
        else:
            await review_and_save(messages)

        return True

    async def build_history_messages(self, uia_state: UIAState, req_options: dict | None = None) -> List[langchain.schema.BaseMessage]:
        previously_cached_agent_steps: List[CacheableUIAgentStep] = []
        # if self.use_cache:
        #     previously_cached_agent_steps = get_data_from_cache(uia_state.session_id)

        model_name = get_model_name(self.options, req_options)

        number_of_last_k__not_summarized_steps = 2

        history_observation: list[str] = []
        messages: list[langchain.schema.BaseMessage] = []

        for i in range(len(uia_state.previous_steps) - 1, -1, -1):
            step = uia_state.previous_steps[i]
            if i >= number_of_last_k__not_summarized_steps:
                step_str = self.build_step_str(step)
                history_observation.append(step_str)
            else:
                cached_step = previously_cached_agent_steps[i]
                image = cached_step.uia_state["image_base64"]
                if len(history_observation) > 0:
                    history_message = "\n".join(history_observation)
                    history_message = f"History:\n{history_message}"
                    history_message = f"{history_message}\n\nScreenshot:"
                    history_observation.clear()

                    user_msg = build_user_message(history_message, {"image_base64": image}, llm_model_name=model_name, first_text=True)
                else:
                    user_msg = build_user_message("Screenshot:", {"image_base64": image}, llm_model_name=model_name, first_text=True)
                action_str = ""

                if len(step.actions) > 1:
                    action_str += " - Actions:\n"
                else:
                    action_str += " - Action:"
                for action in step.actions:
                    if len(step.actions) > 1:
                        action_str += f"  - {action.description}"
                    else:
                        action_str += f" {action.description}"
                    # for extract data action
                    if action.result is not None:
                        action_str += f"\n    result: {action.result}"
                    if action.execution_error_message is not None or len(action.execution_error_message) > 0:
                        action_str += f"\n    execution error: {action.execution_error_message}"
                ai_message = langchain.schema.AIMessage(role="ai", content=action_str)
                messages.append(user_msg)
                messages.append(ai_message)

        return messages

    def build_step_str(self, step: Step) -> str:
        step_str = ""
        if step.additional_parameters is None:
            raise ValueError("step.additional_parameters is None")

        if "reviewed_observation" in step.additional_parameters:
            model_observation = step.additional_parameters["reviewed_observation"]
            assert isinstance(model_observation, str)
        else:
            model_observation = step.additional_parameters["observation"]
        step_str += " - Observation: " + model_observation + "\n"
        if len(step.actions) > 1:
            step_str += " - Actions:\n"
        else:
            step_str += " - Action:"
        for action in step.actions:
            if len(step.actions) > 1:
                step_str += f"  - {action.description}"
            else:
                step_str += f" {action.description}"
            # for extract data action
            if action.result is not None:
                step_str += f"\n    result: {action.result}"
            if len(action.execution_error_message) > 0:
                step_str += f"\n    execution error: {action.execution_error_message}"
        return step_str

    def build_extracted_data_str(self, previous_steps: List[Step], start_k: int = 0, end_k: int = -1) -> str:
        extrated_data_str = ""
        steps = previous_steps[start_k:end_k] if end_k > 0 else previous_steps[start_k:]
        for step in steps:
            if step.actions[0].name == ActionType.ExtractInfo:
                action = step.actions[0]
                result = action.result
                if "token_usage" in result and "data" in result:
                    data = result["data"]
                else:
                    data = result
                extrated_data_str += f" - {action.description}\n"
                extrated_data_str += f"   {str(data)}\n"
        return extrated_data_str

    def build_previous_steps_str(self, previous_steps: List[Step], start_k: int = 0, end_k: int = -1) -> str:
        previous_step_str = ""
        data_messages = []
        steps = previous_steps[start_k:end_k] if end_k > 0 else previous_steps[start_k:]
        for i, step in enumerate(steps):
            if step.additional_parameters is None:
                raise ValueError("step.additional_parameters is None")
            previous_step_str = f"Step {start_k + i + 1}:\n"
            previous_step_str += self.build_step_str(step)
            data_messages.append(previous_step_str)

        if len(data_messages) > 0:
            previous_step_str = "\n".join(data_messages)
        return previous_step_str

    def build_history_str(self, uia_state: UIAState) -> str:
        if len(uia_state.previous_steps) == 0:
            return ""

        if len(uia_state.previous_steps) >= 2 * self.summary_interval:
            summary_str = uia_state.summary
            last_summarized_k = (len(uia_state.previous_steps) - self.summary_interval) // self.summary_interval * self.summary_interval
            start_k = 0
            end_k = last_summarized_k
            extracted_data_str = self.build_extracted_data_str(uia_state.previous_steps, start_k=0, end_k=last_summarized_k)
            previous_step_str = self.build_previous_steps_str(uia_state.previous_steps, start_k=last_summarized_k)
            history_template = """Previously extracted data:
{extracted_data_str}
Summary of previous steps from step {start_k} to step {end_k}:
{summary_str}
Last executed steps:
{previous_step_str}
"""
            history_str = history_template.format(
                extracted_data_str=extracted_data_str,
                summary_str=summary_str,
                previous_step_str=previous_step_str,
                start_k=start_k + 1,
                end_k=end_k,
            )
        else:
            previous_step_str = self.build_previous_steps_str(uia_state.previous_steps, start_k=0)
            history_template = """Last executed steps:
{previous_step_str}
"""
            history_str = history_template.format(
                previous_step_str=previous_step_str,
            )
        return history_str
