from services.studio._text_to_workflow.ui_automation.action import Step


class ModelResponse:
    def __init__(self, observation: str, reasoning: str, step: Step):
        self.observation = observation
        self.reasoning = reasoning
        self.step = step
        self.review_observation = None

    def to_dict(self):
        return {
            "observation": self.observation,
            "reasoning": self.reasoning,
            "step": self.step.serialize(format="json"),
            "review_observation": self.review_observation,
        }
