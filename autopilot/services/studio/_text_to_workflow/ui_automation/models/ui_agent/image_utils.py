from typing import List, Optional, <PERSON>ple

import cv2
import numpy as np

DEFAULT_COLOR_PALETTE = [
    "A351FB",
    "FF4040",
    "FFA1A0",
    "FF7633",
    "FFB633",
    "D1D435",
    "4CFB12",
    "94CF1A",
    "40DE8A",
    "1B9640",
    "00D6C1",
    "2E9CAA",
    "00C4FF",
    "364797",
    "6675FF",
    "0019EF",
    "863AFF",
    "530087",
    "CD3AFF",
    "FF97CA",
    "FF39C9",
]


class BoxAnnotator:
    """
    A class for drawing bounding boxes on an image using detections provided.

    Attributes:
        color (Union[Color, ColorPalette]): The color to draw the bounding box,
            can be a single color or a color palette
        thickness (int): The thickness of the bounding box lines, default is 2
        text_color (Color): The color of the text on the bounding box, default is white
        text_scale (float): The scale of the text on the bounding box, default is 0.5
        text_thickness (int): The thickness of the text on the bounding box,
            default is 1
        text_padding (int): The padding around the text on the bounding box,
            default is 5

    """

    def __init__(
        self,
        thickness: int = 3,  # 1 for seeclick 2 for mind2web and 3 for demo
        text_color: tuple[int, int, int] = (0, 0, 0),
        text_scale: float = 0.5,  # 0.8 for mobile/web, 0.3 for desktop # 0.4 for mind2web
        text_thickness: int = 2,  # 1, # 2 for demo
        text_padding: int = 10,
        avoid_overlap: bool = True,
    ):
        self.color_palette: List[str] = DEFAULT_COLOR_PALETTE
        self.thickness: int = thickness
        self.text_color = text_color
        self.text_scale: float = text_scale
        self.text_thickness: int = text_thickness
        self.text_padding: int = text_padding
        self.avoid_overlap: bool = avoid_overlap

    def annotate(
        self,
        scene: np.ndarray,
        detections: np.ndarray,
        labels: Optional[List[str]] = None,
        image_size: Optional[Tuple[int, int]] = None,
    ) -> np.ndarray:
        """
        Draws bounding boxes on the frame using the detections provided.

        Args:
            scene (np.ndarray): The image on which the bounding boxes will be drawn
            detections (Detections): The bounding boxes in xyxy format
            labels (Optional[List[str]]): An optional list of labels
                corresponding to each detection. If `labels` are not provided,
        Returns:
            np.ndarray: The image with the bounding boxes drawn on it

        """

        if image_size is None:
            image_size = (scene.shape[1], scene.shape[0])
        font = cv2.FONT_HERSHEY_SIMPLEX
        for i in range(len(detections)):
            x1, y1, x2, y2 = detections[i].astype(int)
            idx = i % len(self.color_palette)

            color = self.color_palette[idx]
            r, g, b = (int(color[i : i + 2], 16) for i in range(0, 6, 2))
            cv2.rectangle(
                img=scene,
                pt1=(x1, y1),
                pt2=(x2, y2),
                color=(b, g, r),
                thickness=self.thickness,
            )

            if labels is None:
                text = str(idx)
            else:
                text = labels[i]

            text_width, text_height = cv2.getTextSize(
                text=text,
                fontFace=font,
                fontScale=self.text_scale,
                thickness=self.text_thickness,
            )[0]

            if not self.avoid_overlap:
                text_x = x1 + self.text_padding
                text_y = y1 - self.text_padding

                text_background_x1 = x1
                text_background_y1 = y1 - 2 * self.text_padding - text_height

                text_background_x2 = x1 + 2 * self.text_padding + text_width
                text_background_y2 = y1
                # text_x = x1 - self.text_padding - text_width
                # text_y = y1 + self.text_padding + text_height
                # text_background_x1 = x1 - 2 * self.text_padding - text_width
                # text_background_y1 = y1
                # text_background_x2 = x1
                # text_background_y2 = y1 + 2 * self.text_padding + text_height
            else:
                text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2 = get_optimal_label_pos(
                    self.text_padding, text_width, text_height, x1, y1, x2, y2, detections, image_size
                )

            cv2.rectangle(
                img=scene,
                pt1=(text_background_x1, text_background_y1),
                pt2=(text_background_x2, text_background_y2),
                color=(b, g, r),
                thickness=cv2.FILLED,
            )
            # import pdb; pdb.set_trace()
            box_color = (r, g, b)
            luminance = 0.299 * box_color[0] + 0.587 * box_color[1] + 0.114 * box_color[2]
            text_color = (0, 0, 0) if luminance > 160 else (255, 255, 255)
            cv2.putText(
                img=scene,
                text=text,
                org=(text_x, text_y),
                fontFace=font,
                fontScale=self.text_scale,
                # color=self.text_color.as_rgb(),
                color=text_color,
                thickness=self.text_thickness,
                lineType=cv2.LINE_AA,
            )
        return scene


def box_area(box):
    return (box[2] - box[0]) * (box[3] - box[1])


def intersection_area(box1, box2):
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    return max(0, x2 - x1) * max(0, y2 - y1)


def IoU(box1, box2, return_max=True):
    intersection = intersection_area(box1, box2)
    union = box_area(box1) + box_area(box2) - intersection
    if box_area(box1) > 0 and box_area(box2) > 0:
        ratio1 = intersection / box_area(box1)
        ratio2 = intersection / box_area(box2)
    else:
        ratio1, ratio2 = 0, 0
    if return_max:
        return max(intersection / union, ratio1, ratio2)
    else:
        return intersection / union


def get_optimal_label_pos(text_padding, text_width, text_height, x1, y1, x2, y2, detections, image_size):
    """check overlap of text and background detection box, and get_optimal_label_pos,
    pos: str, position of the text, must be one of 'top left', 'top right', 'outer left', 'outer right' TODO: if all are overlapping, return the last one, i.e. outer right
    Threshold: default to 0.3
    """

    def get_is_overlap(detections, text_background_x1, text_background_y1, text_background_x2, text_background_y2, image_size):
        is_overlap = False
        for i in range(len(detections)):
            bbox = detections[i].astype(int)
            if IoU([text_background_x1, text_background_y1, text_background_x2, text_background_y2], bbox) > 0.3:
                is_overlap = True
                break
        # check if the text is out of the image
        if text_background_x1 < 0 or text_background_x2 > image_size[0] or text_background_y1 < 0 or text_background_y2 > image_size[1]:
            is_overlap = True
        return is_overlap

    # if pos == 'top left':
    text_x = x1 + text_padding
    text_y = y1 - text_padding

    text_background_x1 = x1
    text_background_y1 = y1 - 2 * text_padding - text_height

    text_background_x2 = x1 + 2 * text_padding + text_width
    text_background_y2 = y1
    is_overlap = get_is_overlap(detections, text_background_x1, text_background_y1, text_background_x2, text_background_y2, image_size)
    if not is_overlap:
        return text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2

    # elif pos == 'outer left':
    text_x = x1 - text_padding - text_width
    text_y = y1 + text_padding + text_height

    text_background_x1 = x1 - 2 * text_padding - text_width
    text_background_y1 = y1

    text_background_x2 = x1
    text_background_y2 = y1 + 2 * text_padding + text_height
    is_overlap = get_is_overlap(detections, text_background_x1, text_background_y1, text_background_x2, text_background_y2, image_size)
    if not is_overlap:
        return text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2

    # elif pos == 'outer right':
    text_x = x2 + text_padding
    text_y = y1 + text_padding + text_height

    text_background_x1 = x2
    text_background_y1 = y1

    text_background_x2 = x2 + 2 * text_padding + text_width
    text_background_y2 = y1 + 2 * text_padding + text_height

    is_overlap = get_is_overlap(detections, text_background_x1, text_background_y1, text_background_x2, text_background_y2, image_size)
    if not is_overlap:
        return text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2

    # elif pos == 'top right':
    text_x = x2 - text_padding - text_width
    text_y = y1 - text_padding

    text_background_x1 = x2 - 2 * text_padding - text_width
    text_background_y1 = y1 - 2 * text_padding - text_height

    text_background_x2 = x2
    text_background_y2 = y1

    is_overlap = get_is_overlap(detections, text_background_x1, text_background_y1, text_background_x2, text_background_y2, image_size)
    if not is_overlap:
        return text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2

    return text_x, text_y, text_background_x1, text_background_y1, text_background_x2, text_background_y2


def add_rulers(image: np.ndarray, font_scale: float = 0.4) -> np.ndarray:
    # Get the dimensions of the original image
    h, w, c = image.shape

    # Create a new blank image with extra padding
    padding = 50
    new_h, new_w = h + padding, w + padding
    background = np.ones((new_h, new_w, c), dtype=np.uint8) * 255  # White background

    # Insert the original image at the correct position
    background[padding:, padding:] = image

    # Define font settings
    font = cv2.FONT_HERSHEY_SIMPLEX
    # font_scale = font_scale # Smaller text size
    color = (0, 0, 0)  # Black color
    thickness = 1

    text = 0
    # Add horizontal ruler (top)
    for i in range(0, w + 1, 50):
        cv2.line(background, (i + padding, padding - 7), (i + padding, padding + 7), color, 1)  # Increased tick size
        cv2.putText(background, str(text), (i + padding - 10, padding - 12), font, font_scale, color, thickness)
        text += 10

        # Subdivisions
        for j in range(1, 10):
            x = i + j * 5 + padding
            tick_length = 4 if j % 5 != 0 else 6  # Middle intervals slightly bigger
            cv2.line(background, (x, padding - tick_length), (x, padding + tick_length), color, 1)

    text = 0
    # Add vertical ruler (left)
    for i in range(0, h + 1, 50):
        cv2.line(background, (padding - 7, i + padding), (padding + 7, i + padding), color, 1)  # Increased tick size
        cv2.putText(background, str(text), (10, i + padding + 3), font, font_scale, color, thickness)  # Closer text
        text += 10

        # Subdivisions
        for j in range(1, 10):
            y = i + j * 5 + padding
            tick_length = 4 if j % 5 != 0 else 6  # Middle intervals slightly bigger
            cv2.line(background, (padding - tick_length, y), (padding + tick_length, y), color, 1)
    return background


if __name__ == "__main__":
    image_path = r"c:\\work\\test.png"
    image = cv2.imread(image_path)
    # add_rulers(image_path)
    out_image = add_rulers(image, font_scale=0.55)
    image_path = cv2.imwrite("C:\\work\\test_with_rulers2.png", out_image)

    # Save or show the new image
    cv2.imshow("Image with Rulers", out_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
