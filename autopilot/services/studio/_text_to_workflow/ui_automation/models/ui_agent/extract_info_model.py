import copy
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Tuple

import langchain.chains
import langchain.prompts
import langchain.schema

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LOGGER, get_chat_provider, get_model_name
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import build_dom_str, build_user_message, default_prompt_config
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.extract_info_prompt_message import (
    extract_message_invalid_reply_message,
    extract_screen_shot_info,
    extract_system_message,
    extract_user_message,
)
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import ValidationException, parse_message_json
from services.studio._text_to_workflow.ui_automation.utils.ui_agent_types import ExtractionResponse
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class ExtractInfoModel:
    def __init__(self, options):
        self.options = copy.deepcopy(options)
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))
        self.use_structured_output = False
        self.options["engine_config"]["max_tokens"] = 3000

    def build_extract_info_prompt(
        self,
        dom: ET.Element,
        user_prompt: str,
        image_base64: str | None,
        title: str | None,
        url: str | None,
        req_options: dict | None = None,
    ) -> List[langchain.schema.BaseMessage]:
        examples = []  # TODO: add examples

        dom_string = build_dom_str(dom, self.prompt_config)

        messages = []
        image_info_text = ""
        if image_base64 is not None:
            image_info_text = extract_screen_shot_info
        system_message_prompt = extract_system_message.format(additional_screen_info=image_info_text)
        system_message = langchain.schema.SystemMessage(role="system", content=system_message_prompt)
        messages.append(system_message)
        for example in examples:
            user_message_content = extract_user_message.format(
                dom_string=example[0],
                task_description=example[1],
            )
            user_message = langchain.schema.HumanMessage(role="user", content=user_message_content)
            messages.append(user_message)
            assistant_message_content = example[2]
            assistant_message = langchain.schema.AIMessage(role="assistant", content=assistant_message_content)
            messages.append(assistant_message)

        url = url[: self.prompt_config["max_url_size"]] if url else None
        title_and_url = f"Title: {title}\nURL: {url}" if url else f"Title: {title}"

        extract_prompt_data = {
            "title_and_url": title_and_url,
            "dom_string": dom_string,
            "task_description": user_prompt,
            "image_base64": image_base64,
        }

        model_name = get_model_name(self.options, req_options=req_options)
        user_message = build_user_message(extract_user_message, extract_prompt_data, llm_model_name=model_name)
        messages.append(user_message)

        return messages

    async def predict_with_retries(self, messages, predict_info, retry_number=0, req_options: dict | None = None) -> dict | None:
        """
        Predicts the next step with retries if ValidationException is raised
        """

        resposnse_format = ExtractionResponse if self.use_structured_output else None

        chat_provider = get_chat_provider(
            self.options, req_options=req_options, consuming_feature_type=ConsumingFeatureType.SCREEN_AGENT_INFO_EXTRACTION, response_format=resposnse_format
        )
        gpt_response = await chat_provider.send_message(messages=messages, predict_info=predict_info)
        max_vadidation_retries = 2
        try:
            if self.use_structured_output:
                extracted_data = gpt_response.model_dump()
            else:
                content_str = gpt_response.content if isinstance(gpt_response.content, str) else str(gpt_response.content)
                extracted_data = parse_message_json(content_str)
            return extracted_data
        except ValidationException as ve:
            predict_info["ValidationException"] = predict_info.get("ValidationException", 0) + 1
            if retry_number < max_vadidation_retries:
                reply_message_content = extract_message_invalid_reply_message.format(error_message=ve.message)
                reply_message = langchain.schema.HumanMessage(content=reply_message_content)
                gpt_message = langchain.schema.AIMessage(content=gpt_response.content)
                messages.append(gpt_message)
                messages.append(reply_message)
                return await self.predict_with_retries(
                    messages,
                    predict_info,
                    retry_number=retry_number + 1,
                    req_options=req_options,
                )
            else:
                raise ve
        except Exception as e:
            LOGGER.info(f"Error in parsing the response: {gpt_response.content}")
            raise e

    async def predict(
        self,
        prompt: str,
        dom: ET.Element,
        image_base64: str | None = None,
        title: str | None = None,
        url: str | None = None,
        req_options: dict | None = None,
    ) -> Tuple[dict | None, Dict[str, Any]]:
        messages = self.build_extract_info_prompt(dom=dom, user_prompt=prompt, image_base64=image_base64, title=title, url=url, req_options=req_options)

        predict_info = {"messages": messages}
        extracted_data = await self.predict_with_retries(messages=messages, predict_info=predict_info, req_options=req_options)
        console_logger.debug("\nExtracted_data %s", extracted_data)
        return extracted_data, predict_info
