import copy
import json
import xml.etree.ElementTree as ET
from typing import List, Optional

import langchain.schema

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import get_chat_provider, get_model_name
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_ui_agent_system_prompt_data,
    build_user_message,
    build_user_prompt_data,
    default_prompt_config,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.history_processor import HistoryProcessorModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.reflection_text_to_action_message import (
    reflection_chat_agent_user_message,
    reflection_chat_system_message,
)
from services.studio._text_to_workflow.ui_automation.utils import log_utils
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json
from services.studio._text_to_workflow.ui_automation.utils.ui_agent_types import ReflectionResponse
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class Reflection(object):
    def __init__(self, analysis: str | None, candidates_solutions: str | None, final_solution: str | None):
        self.analysis = analysis
        self.candidates_solutions = candidates_solutions
        self.final_solution = final_solution

    @staticmethod
    def parse(message: str, format="json"):
        if format == "json":
            try:
                reflection = parse_message_json(message)
                if reflection is None:
                    return None
                return Reflection(reflection.get("analysis"), reflection.get("candidates_solutions"), reflection.get("final_solution"))
            except json.JSONDecodeError:
                print("Invalid JSON syntax")
                return None
        else:
            if "<plan>" in message and "</plan>" in message:
                try:
                    message = message.strip().replace("&", "&amp;")
                    start_msg_idx = message.index("<plan>")
                    end_msg_idx = message.index("</plan>") + len("</plan>")
                    reflection_plan_xml = message[start_msg_idx:end_msg_idx]

                    tree = ET.fromstring(reflection_plan_xml)
                    analysis = None
                    candidates_solutions = None
                    final_solution = None

                    for elem in tree:
                        if elem.tag == "analysis":
                            analysis = elem.text
                        elif elem.tag == "candidates_solutions":
                            candidates_solutions = elem.text
                        elif elem.tag == "final_solution":
                            final_solution = elem.text
                    return Reflection(analysis, candidates_solutions, final_solution)
                except ValueError:
                    print("Invalid XML syntax")
                    return None
                except ET.ParseError:
                    print("Invalid XML syntax")
                    return None
            return None


class ReflectionModel:
    def __init__(self, options):
        self.options = copy.deepcopy(options)
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))
        self.use_structured_output = options.get("engine_config", {}).get("structured_output", False)
        self.history_processor_model = HistoryProcessorModel(options)

    def build_messages(
        self,
        uia_state: UIAState,
        req_options: dict | None = None,
    ) -> List[langchain.schema.BaseMessage]:
        user_prompt_data = build_user_prompt_data(uia_state, self.prompt_config)
        history_str = self.history_processor_model.build_history_str(uia_state)
        user_prompt_data["history_str"] = history_str
        screen_properties = {"object_dom": uia_state.is_object_dom, "desktop": uia_state.is_desktop}
        system_prompt_data = build_ui_agent_system_prompt_data(uia_state.image_base64, screen_properties, self.prompt_config)
        system_message_text = reflection_chat_system_message.format(**system_prompt_data)
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        model_name = get_model_name(self.options, req_options=req_options)
        messages.append(build_user_message(reflection_chat_agent_user_message, user_prompt_data, llm_model_name=model_name))
        return messages

    async def predict(
        self,
        uia_state: UIAState,
        predict_info: dict,
        req_options: dict | None = None,
    ) -> Optional[str]:
        reflection_plan = None
        messages = self.build_messages(uia_state, req_options)
        chat_provider = get_chat_provider(
            self.options,
            req_options=req_options,
            consuming_feature_type=ConsumingFeatureType.SCREEN_AGENT,
            response_format=ReflectionResponse if self.use_structured_output else None,
        )
        reflection_output = await chat_provider.send_message(messages=messages, predict_info=predict_info)
        if self.use_structured_output:
            return reflection_output.final_solution

        if log_utils.ENABLE_STREAM_LOG:
            payload = {
                "model_thought": "Reflection",
                "model_actions": reflection_output.content,
                "screenshot": uia_state.image_base64,
            }
            log_utils.send_stream_log(payload)

        console_logger.debug("\nReflection ouptut: %s", str(reflection_output.content))

        reflection = Reflection.parse(str(reflection_output.content))
        if reflection is not None:
            reflection_plan = reflection.final_solution

        return reflection_plan
