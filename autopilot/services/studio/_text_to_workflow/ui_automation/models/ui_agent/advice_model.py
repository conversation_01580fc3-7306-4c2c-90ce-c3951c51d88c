import base64
import json
from copy import deepcopy
from io import Bytes<PERSON>
from typing import Any, Dict, List, Optional

import langchain.schema
from PIL import Image

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import get_chat_provider
from services.studio._text_to_workflow.ui_automation.options import advice_model_options, agent_prompt_options, opt


class AdviceModel:
    TASK_TEMPLATE = """
<Task>
  <Description>
    {task_description}
  </Description>
</Task>
"""

    AGENT_ACTION_TEMPLATE = """
<Action>
    <Description>{action_description}</Description>
    <MethodType>{action_method_type}</MethodType>
    <TargetParameters>{action_target_parameters}</TargetParameters>
    <ExecutionParameters>{action_execution_parameters}</ExecutionParameters>
    <Outcome>{action_outcome}</Outcome>
</Action>
"""

    DEMO_ACTION_TEMPLATE = """
<Action>
    <MethodType>{action_method_type}</MethodType>
    <TargetParameters>{action_target_parameters}</TargetParameters>
    <ExecutionParameters>{action_execution_parameters}</ExecutionParameters>
</Action>
"""

    ADVICE_SYSTEM_MESSAGE = """You are a UI automation advisor. Your role is to analyze execution traces and provide step-by-step instructions for completing tasks.
Focus on the semantic meaning of actions rather than specific values."""

    ADVICE_USER_MESSAGE = """Given this task and its execution trace, can you generate a indented list with step by step instructions
Instead of actual values the user provides, which might change from one execution to the other, use semantic names
So you don't say "Set user name to Stefan Petrescu", you say "Set user name to <user_name>"
Do add details about localizing elements to interact with

{formatted_task}"""

    REINFORCED_MESSAGE = """
Instead of actual values the user provides, which might change from one execution to the other, use semantic names
So you don't say "Set user name to Stefan Petrescu", you say "Set user name to <user_name>"
Don't reveal the actual values of the elements, just use the semantic names
"""

    ADVICE_GENERALIZATION_MESSAGE = """
The agent's mission is to take any natural-language task description plus its associated step-by-step plan and transform that plan into a reusable template. By stripping out all instance-specific data—hard-coded strings, numbers, names, URLs, file paths, etc.—the result becomes a generalized blueprint that can execute the same logic across new examples.

This process applies to every incoming pair of (1) a free-form task description and (2) a concrete execution plan. The agent should assume that the plan may embed values unique to this one run; its job is to extract those and leave only placeholders and structural logic.

The agent will receive: the natural-language description of what to do, and the matching plan text containing literal values (for example, "Click 'Submit'", "Set the date to...", or "Loop three times").

A "value literal" is any specific datum that varies from call to call: a name, a string, a number, a date, a filename, even a CSS selector or button label. By contrast, a placeholder is a generic token—something like <user_name> or <article_title>—that stands in for unknown content and must remain intact. For the purposes of this task the URL is not considered a value literal, because the agent starts from a specific URL and can't open new urls directly.

First, scan the entire plan to identify every literal. Replace each with an appropriate placeholder or a loop construct if the same pattern repeats, and convert any hard-coded branching into conditional phrasing ("If X is present, then…"). Preserve the original step numbering in principle but collapse repetitive sequences into a single step with an implicit loop. Keep the logical flow and dependencies exactly the same.

Wrap only the final, cleaned-up template inside a [plan]…[/plan] block. That block must consist solely of the generalized pseudocode in natural-language style, numbered in hierarchy (for example, 1, 1.1, 1.2, 2, 2.1, etc.), without any explanatory text or commentary around it.

[example]
Given a plan that says "Type 'Hello, Alice' into the chat box, click 'Send', then repeat for 'Hello, Bob'," the agent would output:

[plan]
1. For each name in <recipient_list>:
  1.1 Enter "Hello, <name>" into the <message_field>.
  1.2 Click the <send_button>.
[/plan]
[/example]

This ensures the end user sees only the steps they can reuse, free of one-off details.

[task]
{task_description}
[/task]

[plan]
{plan}
[/plan]
"""

    def __init__(self, options: Optional[Dict[str, Any]] = None) -> None:
        self.options = deepcopy(options or opt)
        self.options["prompt_options"].update(agent_prompt_options)
        self.max_width = advice_model_options["image_max_width"]
        self.max_height = advice_model_options["image_max_height"]
        self.use_images = advice_model_options["use_images"]

    def _extract_step(self, iteration: Dict[str, Any]) -> str:
        """Extract and format actions from iterations."""
        actions = []
        for action in iteration["Actions"]:
            if False and "description" in action["InputAction"]["Action"]:
                action_description = action["InputAction"]["Action"]["description"]
                action_outcome = action["InputAction"]["Action"].get("execution_error_message", "success")
                action_method_type = action["InputAction"]["Action"]["method_type"]
                action_target_parameters = action["DOMElement"] if "DOMElement" in action else action["InputAction"]["Action"]["parameters"]
                action_execution_parameters = {x: y for x, y in action.get("ExecutionActionData", {}).items() if x != "ImageBase64"}

                actions.append(
                    self.AGENT_ACTION_TEMPLATE.format(
                        action_description=action_description,
                        action_method_type=action_method_type,
                        action_target_parameters=action_target_parameters,
                        action_execution_parameters=action_execution_parameters,
                        action_outcome=action_outcome,
                    )
                )
            else:
                action_method_type = action["InputAction"]["Action"]["method_type"]
                action_target_parameters = action["DOMElement"] if "DOMElement" in action else action["InputAction"]["Action"]["parameters"]
                action_execution_parameters = {x: y for x, y in action.get("ExecutionActionData", {}).items() if x != "ImageBase64"}

                actions.append(
                    self.DEMO_ACTION_TEMPLATE.format(
                        action_method_type=action_method_type,
                        action_target_parameters=action_target_parameters,
                        action_execution_parameters=action_execution_parameters,
                    )
                )
        return "\n".join(actions)

    def _extract_task(self, trace: Dict[str, Any]) -> str:
        """Extract task and format it with actions."""
        task_prompt = trace["Prompt"]["Prompt"]
        if "\n---\n" in task_prompt:
            task_prompt = task_prompt.split("\n---\n")[0]

        return self.TASK_TEMPLATE.format(task_description=task_prompt)

    def _process_image(self, image_base64: str) -> str:
        """Process an image by resizing if needed and return the processed base64 string.

        Args:
            image_base64: Base64 encoded image string

        Returns:
            Processed base64 string
        """
        # Decode and load image
        img_data = base64.b64decode(image_base64)
        img = Image.open(BytesIO(img_data))
        original_width, original_height = img.size

        # Resize if needed
        if original_width > self.max_width or original_height > self.max_height:
            # Calculate new dimensions maintaining aspect ratio
            ratio = min(self.max_width / original_width, self.max_height / original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)

            # Resize image
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert back to base64
            buffered = BytesIO()
            if img.mode == "RGBA":
                img = img.convert("RGB")
            img.save(buffered, format="JPEG")
            return base64.b64encode(buffered.getvalue()).decode("utf-8")
        else:
            return image_base64

    def build_messages(self, trace_data: Dict[str, Any]) -> List[langchain.schema.BaseMessage]:
        """Build messages for the chat provider."""
        formatted_task = self._extract_task(trace_data)

        # Start with the text content
        content = [{"type": "text", "text": self.ADVICE_USER_MESSAGE.format(formatted_task=formatted_task)}]

        # Add images if enabled
        if self.use_images:
            for iteration in trace_data["Iterations"]:
                if "ImageBase64" in iteration:
                    # Process image
                    image_base64 = self._process_image(iteration["ImageBase64"])
                    iteration_actions = self._extract_step(iteration)
                    # Add image
                    content.append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}})
                    # Add step label
                    content.append({"type": "text", "text": iteration_actions})

            # Add reinforced message after images
            content.append({"type": "text", "text": self.REINFORCED_MESSAGE})

        return [langchain.schema.SystemMessage(role="system", content=self.ADVICE_SYSTEM_MESSAGE), langchain.schema.HumanMessage(role="user", content=content)]

    def build_generalize_messages(self, task_description: str, plan: str) -> List[langchain.schema.BaseMessage]:
        return [
            langchain.schema.SystemMessage(
                role="system",
                content="You are a UI automation advisor. Your role is to analyze execution traces and provide step-by-step instructions for completing tasks. Focus on the semantic meaning of actions rather than specific values.",
            ),
            langchain.schema.HumanMessage(role="user", content=self.ADVICE_GENERALIZATION_MESSAGE.format(task_description=task_description, plan=plan)),
        ]

    async def predict(self, trace_data: Dict[str, Any]) -> str:
        """Generate advice based on trace data which contains both task description and execution trace."""
        messages = self.build_messages(trace_data)

        with open("/tmp/messages.json", "w") as f:
            messages2 = [x.dict() for x in messages]
            json.dump(messages2, f)

        chat_provider = get_chat_provider(self.options)
        response = await chat_provider.send_message(messages=messages, predict_info={})

        task_description = trace_data["Prompt"]["Prompt"].split("\nDetailed advice:\n")[0].strip()
        plan = response.content

        try:
            response_gen = await chat_provider.send_message(messages=self.build_generalize_messages(task_description, plan), predict_info={})
            advice = response_gen.content
            if "[plan]" in advice:
                advice = advice.split("[plan]")[1]
            if "[/plan]" in advice:
                advice = advice.split("[/plan]")[0].strip()
        except Exception as e:
            print(f"Error generalizing advice: {e}")
            advice = plan

        print(f"Response: {advice}")

        return advice
