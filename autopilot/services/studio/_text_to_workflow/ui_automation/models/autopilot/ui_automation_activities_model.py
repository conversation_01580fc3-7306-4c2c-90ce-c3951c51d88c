import xml.etree.ElementTree as ET
from typing import List

import langchain.chains
import langchain.prompts
import langchain.schema

from services.studio._text_to_workflow.ui_automation.action import Step
from services.studio._text_to_workflow.ui_automation.action_definition import ActionType
from services.studio._text_to_workflow.ui_automation.llm_chat_providers import (
    LOGGER,
    get_chat_provider,
    get_model_name,
)
from services.studio._text_to_workflow.ui_automation.models.autopilot.prompting.text_to_ui_activities_message import (
    chat_message_invalid_reply_message,
    chat_system_message,
    chat_user_message,
    response_default_instruction,
)
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_autopilot_system_prompt_data,
    build_user_message,
    build_user_prompt_data,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import (
    ConsumingFeatureType,
)


class UiAutomationActivitiesModel:
    def __init__(self, options):
        self.options = options
        self.prompt_config = options.get("prompt_options", {})
        self.action_format = self.prompt_config["action_format"]
        self.is_ui_agent = self.prompt_config["is_ui_agent"]
        consuming_feature_type = ConsumingFeatureType.UI_AUTOMATION
        self.consuming_feature_type = consuming_feature_type
        assert self.prompt_config["is_ui_agent"] is False, "Wrong prompt options for UiAutomationActivitiesModel"

    def validate_step(self, step: Step, dom: ET.Element):
        """
        Validates the step actions against the dom
        - Return False if FIRST action is invalid (unexist element_id)
        - Otherwise stop at the first invalid action
        """
        last_valid_idx = 0
        for idx, action in enumerate(step.actions, 0):
            parameters = action.parameters
            if "element_id" in parameters:
                # auto convert element_id to int
                parameters["element_id"] = int(parameters["element_id"])
                element_id = parameters["element_id"]
                should_check_element_id = True
                if action.name == ActionType.Scroll and element_id <= 0:
                    # skip validation for scroll action
                    should_check_element_id = False
                if should_check_element_id:
                    element = dom.find(f".//*[@Id='{str(element_id)}']")
                    if element is None:
                        if idx == 0:
                            # raise only first action element id is incorrect
                            error_message = (
                                f"Action '{action.description}' is invalid. Element Id '{element_id}' for method {action.name} does not exist in the dom!"  # noqa
                            )
                            return False, error_message
                        else:
                            # stop here
                            break
            last_valid_idx = idx
        # remove the actions starting from the first invalid action
        step.actions = step.actions[: last_valid_idx + 1]
        return True, None

    def build_response_instruction(self):
        """Create instruction for giving response with format and optional reasoning"""
        response_instruction = response_default_instruction.format(action_format=self.action_format)
        return response_instruction

    def build_messages(
        self,
        uia_state: UIAState,
        predict_info: dict,
        req_options: dict | None = None,
    ) -> List[langchain.schema.BaseMessage]:
        user_prompt_elements = build_user_prompt_data(uia_state, self.prompt_config)
        user_prompt_elements["response_instruction"] = self.build_response_instruction()
        screen_properties = {
            "object_dom": uia_state.is_object_dom,
            "desktop": uia_state.is_desktop,
        }
        system_prompt_elements = build_autopilot_system_prompt_data(uia_state.image_base64, screen_properties, self.prompt_config)

        system_message_text = chat_system_message.format(**system_prompt_elements)
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        model_name = get_model_name(self.options, req_options)
        messages.append(build_user_message(chat_user_message, user_prompt_elements, llm_model_name=model_name))

        predict_info.update(
            {
                "data": user_prompt_elements,
                "messages": messages,
            }
        )
        return messages

    async def predict(
        self,
        uia_state: UIAState,
        req_options: dict | None = None,
    ) -> tuple[Step, dict]:
        predict_info = {}
        messages = self.build_messages(uia_state, predict_info=predict_info, req_options=req_options)
        step = await self.predict_with_retries(uia_state, messages, predict_info, req_options=req_options)

        if step is not None:
            step.screen_info = {"Title": uia_state.title, "Url": uia_state.url}
        return step, predict_info

    async def predict_with_retries(
        self,
        ui_state: UIAState,
        messages: List[langchain.schema.BaseMessage],
        predict_info: dict,
        retry_number: int = 0,
        req_options: dict | None = None,
    ) -> Step:
        """
        Predicts the next step, retry if step validation failed is raised
        If step validation failed twice, return a finish failure step
        """

        chat_provider = get_chat_provider(
            self.options,
            req_options,
            self.consuming_feature_type,
        )
        gpt_response = await chat_provider.send_message(messages, predict_info)
        max_vadidation_retries = 2
        try:
            # both parsing and actions validation can raise ValidationException
            predicted_step = Step.parse(
                str(gpt_response.content),
                is_ui_agent=False,
                format=self.action_format,
            )

            # this removes the actions which have invalid target elements
            validataion_status, error_message = self.validate_step(predicted_step, ui_state.xml_dom)
            if validataion_status:
                return predicted_step
            else:
                print("Step validation error: ", error_message)
                predict_info["ValidationException"] = predict_info.get("ValidationException", 0) + 1
                if retry_number < max_vadidation_retries:
                    reply_message_content = chat_message_invalid_reply_message.format(error_message=error_message)
                    reply_message = langchain.schema.HumanMessage(content=reply_message_content)
                    gpt_message = langchain.schema.AIMessage(content=gpt_response.content)
                    messages.append(gpt_message)
                    messages.append(reply_message)
                    return await self.predict_with_retries(
                        ui_state,
                        messages,
                        predict_info,
                        retry_number=retry_number + 1,
                        req_options=req_options,
                    )
                else:
                    return Step.create_finish_failure_step("Finish since can not create valid actions", False)
        except Exception as e:
            LOGGER.error(f"Error parsing LLM response: {gpt_response}")
            raise e
