import copy
import time
import xml.etree.ElementTree as ET
from typing import List

import langchain.prompts
import langchain.schema
from openai import RateLimitError
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LLMChat
from services.studio._text_to_workflow.ui_automation.models.autopilot.prompting.summarize_prompt_message import summarize_system_message, summarize_user_message
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import default_prompt_config
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class SummarizeModel:
    def __init__(self, options):
        self.options = options
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))

    def create_summarize_messages(self, data: dict) -> List[langchain.schema.BaseMessage]:
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=summarize_system_message)]
        user_message_content = []
        user_message_text = summarize_user_message.format(dom_string=data["dom_string"])
        user_message_content.append(
            {
                "type": "text",
                "text": user_message_text,
            }
        )

        messages.append(langchain.schema.HumanMessage(role="user", content=user_message_text))
        return messages

    async def predict(self, dom: ET.Element, title: str, url: str, req_options: dict | None = None) -> str:
        dom_str = ET.tostring(dom, encoding="utf-8").decode("utf-8")
        dom_str = truncate_dom_str(dom_str)

        screen = f"DOM: {dom_str}"
        if url is not None and url != "":
            screen = "\n".join([f"Url: {url}", screen])
        if title is not None and title != "":
            screen = "\n".join([f"Title: {title}", screen])

        data = {"dom_string": screen}

        messages = self.create_summarize_messages(data)

        retry_if_ratelimiting = True
        predict_info = {}

        summary = ""
        while retry_if_ratelimiting:
            try:
                chat_provider = LLMChat(self.options, consuming_feature_type=ConsumingFeatureType.DEFAULT)
                response: langchain.schema.BaseMessage = await chat_provider.send_message(messages, predict_info)
                summary = str(response.content)
                retry_if_ratelimiting = False
            except RateLimitError:
                time.sleep(5)
            except Exception as e:
                retry_if_ratelimiting = False
                raise e

        return summary
