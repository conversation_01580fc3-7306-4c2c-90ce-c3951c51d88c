autopilot_step_examples = [
    {"description": "Go to employees page", "actions": [{"name": "click", "description": "Click on the employees tab", "parameters": {"element_id": 14}}]},
    {
        "description": "Remove last recording",
        "actions": [
            {"name": "click", "description": "Select the last recording item", "parameters": {"element_id": 46}},
            {"name": "click", "description": "Press the remove button", "parameters": {"element_id": 18}},
            {"name": "finish", "description": "Last video was removed. Task completed", "parameters": {}},
        ],
    },
    {
        "description": "Set flight details and search",
        "actions": [
            {
                "name": "type_into",
                "description": "Type 'Bucharest' in the departure input box, create an input variable $departure",
                "parameters": {"element_id": 31, "variable": "$departure", "default_value": "Bucharest"},
            },
            {
                "name": "type_into",
                "description": "Type 'London' in the destination input box, create an input variable $destination",
                "parameters": {"element_id": 32, "variable": "$departure", "default_value": "London"},
            },
            {
                "name": "type_into",
                "description": "Type into the date input box '07/10/2023', create an input variable $date",
                "parameters": {"element_id": 47, "variable": "$date", "default_value": "07/10/2023"},
            },
            {"name": "click", "description": "Click on Search button", "parameters": {"element_id": 50}},
        ],
    },
    {
        "description": "Fill in birthday and birth country in the application form using input variables",
        "actions": [
            {
                "name": "type_into",
                "description": "Type into the birthday input box using $birthDay variable, create default value",
                "parameters": {"element_id": 123, "variable": "$birthDay", "default_value": "01/01/1990"},
            },
            {
                "name": "type_into",
                "description": "Type into the birth country input box using $birthCountry variable, create default value",
                "parameters": {"element_id": 132, "variable": "$birthCountry", "default_value": "USA"},
            },
        ],
    },
    {
        "description": "Get the total order amount",
        "actions": [
            {
                "name": "get_text",
                "description": "Get the total order amount from the total price element",
                "parameters": {"element_id": 88, "variable": "$totalAmount"},
            }
        ],
    },
    {
        "description": "Finish with sucess",
        "actions": [{"name": "finish", "description": "Task completed, the information required was extracted.", "parameters": {}}],
    },
]
