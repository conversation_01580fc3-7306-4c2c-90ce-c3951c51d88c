import os
import time

import dotenv
import langchain.schema
from langfuse import <PERSON><PERSON>
from openai import AsyncAzureOpenAI, AsyncOpenAI

from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.utils.inference.llm_gateway_model import LLMGatewayNormalizedModel, LLMGeminiModel

dotenv.load_dotenv()
langfuse = Langfuse(
    secret_key=os.environ.get("LANGFUSE_SECRET_KEY"),
    public_key=os.environ.get("LANGFUSE_PUBLIC_KEY"),
    host=os.environ.get("LANGFUSE_HOST", "http://localhost:3000"),
)
TRACE_FLAG = int(os.environ.get("LANGFUSE_TRACE", "0")) == 1


def get_azure_openai_client():
    return AsyncAzureOpenAI(
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT", ""),
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    )


def get_openai_client(base_url: str, api_key: str):
    return AsyncOpenAI(
        base_url=base_url,
        api_key=api_key,
    )


def to_langchain_message(message: dict):
    if message["role"] == "system":
        return langchain.schema.SystemMessage(content=message["content"])
    elif message["role"] == "user":
        return langchain.schema.HumanMessage(content=message["content"])
    elif message["role"] == "assistant":
        return langchain.schema.AIMessage(content=message["content"])
    else:
        raise ValueError(f"Unknown role: {message['role']}")


def adapt_image_message(message: dict, model_name: str, use_llm_gateway: bool) -> dict:
    # different LLM providers have different content types for images
    # for example, OpenAI uses "image_url" while Gemini uses "image/jpeg"
    if message["role"] == "user" and isinstance(message["content"], list):
        for content in message["content"]:
            if "image" in content["type"]:
                if "gemini" in model_name.lower():
                    if use_llm_gateway:
                        content["type"] = "image/jpeg"
                    else:
                        content["type"] = "image_url"
                elif "gpt" in model_name.lower():
                    content["type"] = "image_url"
                    content["image_url"]["detail"] = "high"
    return message


async def send_messages(
    messages: list[dict],
    options: dict,
    metadata: dict | None = None,  # session_id and request_id for Langfuse tracing
    predict_info: dict | None = None,  # to output token usages, number of requests to service response
) -> str | None:
    # Create generation in Langfuse
    model_name = options["model_name"]
    max_tokens = options.get("max_tokens", 1000)
    temperature = options.get("temperature", 0.0)
    token_usage = {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}
    if TRACE_FLAG:
        assert metadata is not None, "Metadata must be provided for tracing"
        session_id = metadata["session_id"]
        request_id = metadata["request_id"]
        trace = langfuse.trace(id=f"{session_id}_{request_id}", session_id=session_id, name=request_id)
        generation_name = metadata["generation_name"]
        generation = trace.generation(
            name=generation_name,
            model=model_name,
            model_parameters={"maxTokens": str(max_tokens), "temperature": str(temperature)},
            input=messages,
            metadata={},
        )
    try:
        messages = [adapt_image_message(msg, model_name, use_llm_gateway=settings.USE_LLM_GATEWAY) for msg in messages]
        if settings.USE_LLM_GATEWAY and "gemini" in model_name.lower() or "gpt" in model_name.lower():
            # llm gateway client uses langchain schema
            langchain_messages = [to_langchain_message(msg) for msg in messages]
            client = LLMGatewayNormalizedModel(
                deployment_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                request_timeout=60,
            )
            start_time = time.time()
            response = await client.ainvoke(langchain_messages)
            request_time = time.time() - start_time
            response_content = str(response.content)
            if hasattr(response, "usage_metadata"):
                token_usage = {
                    "input_tokens": response.usage_metadata["input_tokens"],
                    "output_tokens": response.usage_metadata["output_tokens"],
                    "total_tokens": response.usage_metadata["total_tokens"],
                }
            else:
                raise Exception("LLM Gateway response does not contain usage metadata.")
        else:
            if "gemini" in model_name.lower():
                client = LLMGeminiModel(
                    deployment_name=model_name,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    thinking_budget=0,
                )
                langchain_messages = [to_langchain_message(msg) for msg in messages]
                start_time = time.time()
                response = await client.ainvoke(langchain_messages)
                request_time = time.time() - start_time
                response_content = str(response.content)
            else:
                if "gpt" in model_name.lower():
                    client = get_azure_openai_client()
                else:
                    client = get_openai_client(
                        base_url=options["base_url"],
                        api_key=options["api_key"],
                    )
                start_time = time.time()
                response = await client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
                request_time = time.time() - start_time
                response_content = str(response.choices[0].message.content)
    except Exception as e:
        console_logger.error(f"Error: {e}")
        raise e
    if TRACE_FLAG:
        generation.end(output=response_content)
        # langfuse.flush()

    if predict_info is not None:
        predict_info["requests"] = predict_info.get("requests", []) + [
            {
                "model": model_name,
                "time": request_time,
                "token_usage": token_usage,
            }
        ]
    return response_content
