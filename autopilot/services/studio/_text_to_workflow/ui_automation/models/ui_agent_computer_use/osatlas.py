# https://huggingface.co/osunlp/UGround-V1-7B
import re

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionUserMessageParam

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils


class OSAtlasPredictor(utils.GroundingBaseModel):
    def __init__(self, model_name: str, options: dict):
        self.client = AsyncOpenAI(
            base_url=options["base_url"],
            api_key=options["api_key"],
        )

        # "OS-Copilot/OS-Atlas-Base-7B"
        self.model_name = model_name

    def format_openai_template(self, description: str, image_base64: str) -> list[ChatCompletionUserMessageParam]:
        message_template = """In this UI screenshot, what is the position of the element corresponding to the command: '{command}' (with bbox)?
Only return bboxes with format [[x1,y1,x2,y2]]."""
        prompt_text = message_template.format(command=description)
        return [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt_text,
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                    },
                ],
            }
        ]

    def parse_response(self, response_text: str) -> tuple[int, int, int, int] | tuple[int, int] | None:
        """
        Extract label and normalized coordinates from a response.
        Supported formats:
        1. Double-bracket format:
            e.g., "view calendar[[731, 731, 927, 753]]"
            Returns: ("view calendar", ('bbox', (731, 731, 927, 753)))
        2. Parentheses format:
            e.g., "type <EMAIL> into email input box(342,558),(551,609)"
            Returns: ("type <EMAIL> into email input box", ('bbox', (342, 558, 551, 609)))

        Returns:
        A tuple (label, (type, coordinates)) if parsing succeeds; otherwise (None, None).
        """
        # Try double-bracket pattern first.
        pattern1 = r"^(.*?)\s*\[\[\s*(.*?)\s*\]\]$"
        match1 = re.match(pattern1, response_text)
        if match1:
            label = match1.group(1).strip()
            coords_str = match1.group(2)
            try:
                coords = [int(x.strip()) for x in coords_str.split(",")]
                if len(coords) == 4:
                    return (coords[0], coords[1], coords[2], coords[3])
                elif len(coords) == 2:
                    return (coords[0], coords[1])
            except Exception as e:
                console_logger.error(f"Error parsing coordinates in double-bracket format: {e}")
                return None

        # If not matched, try the parentheses format.
        # This pattern expects a label followed by two sets of parenthesized numbers.
        pattern2 = r"^(.*?)\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*,\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)$"
        match2 = re.match(pattern2, response_text)
        if match2:
            label = match2.group(1).strip()
            if label is None or label == "":
                console_logger.error("Label is None in parentheses format")
            try:
                x1 = int(match2.group(2))
                y1 = int(match2.group(3))
                x2 = int(match2.group(4))
                y2 = int(match2.group(5))
                return (x1, y1, x2, y2)
            except Exception as e:
                console_logger.error(f"Error parsing coordinates in parentheses format: {e}")
                return None

        console_logger.error("Response format not recognized.")
        return None

    async def predict(self, description: str, image_base64: str) -> utils.GroundingOutput:
        messages = self.format_openai_template(description, image_base64)
        completion = await self.client.chat.completions.create(model=self.model_name, messages=messages, temperature=0.0)

        response_content = completion.choices[0].message.content
        if response_content is None:
            raise ValueError("OSAtlas response is None")
        console_logger.debug("OSAtlas response: %s", response_content)
        bboxes_normalized = self.parse_response(response_content)
        if bboxes_normalized is None:
            raise ValueError("OSAtlas bboxes_normalized is None")

        console_logger.debug("OSAtlas bboxes: %s", response_content)
        image = utils.image_base64_to_pil_image(image_base64)
        bbox = utils.convert_bbox(bboxes_normalized, image.size)
        console_logger.debug("Coords: %s", bbox)
        out = utils.GroundingOutput(description=response_content, bbox=bbox, position=None)
        return out
