class State(object):
    """
    Class representing the state of a computer.
    """

    def __init__(self, task: str, image_base64: str, previous_steps: list):
        self.task = task
        self.image_base64 = image_base64
        self.previous_steps = previous_steps


class ExecutionState(object):
    def __init__(self, model_name: str, session_id: str, request_id: str, predict_info: dict, execution_info: dict):
        self.model_name = model_name
        self.session_id = session_id
        self.request_id = request_id
        # for storing request token usage, etc.
        self.predict_info = predict_info
        # for storing temporal execution related information action review result etc
        self.execution_info = execution_info
