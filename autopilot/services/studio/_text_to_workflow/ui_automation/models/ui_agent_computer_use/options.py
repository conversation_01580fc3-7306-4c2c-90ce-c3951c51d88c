opt = {
    "planner": {
        "model": {
            "use_llm_gateway": True,
            "model_name": "gpt-4.1-mini-2025-04-14",
            # "model_name": "gemini-2.5-pro-exp-03-25",
            # gpt-4.1-2025-04-14
            # gemini-2.5-pro-exp-03-25
            # gemini-2.0-flash-001,
            # gemini-2.5-flash-preview-05-20
            # gpt-4.1-mini-2025-04-14
            # gpt-4o-2024-08-06
            # "anthropic.claude-3-7-sonnet-20250219-v1:0",
            # for api compatible models
            # "model_name": "OpenGVLab/InternVL3-78B",
            "base_url": "http://34.27.47.186:8005/v1",
            "api_key": "123",
        }
    },
    "grounder": {
        "model": {
            # "model_name": "OS-Copilot/OS-Atlas-Base-7B",
            # "model_name": "osunlp/UGround-V1-7B",
            # "base_url": "http://34.27.47.186:8009/v1",
            "model_name": "ByteDance-Seed/UI-TARS-1.5-7B",
            "base_url": "http://34.27.47.186:8007/v1",
            "api_key": "123",
        }
    },
}
