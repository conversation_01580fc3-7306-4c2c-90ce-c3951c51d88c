# https://huggingface.co/osunlp/UGround-V1-7B
import re

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionUserMessageParam

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils


class UGroundPredictor(utils.GroundingBaseModel):
    def __init__(self, model_name: str, options: dict):
        self.client = AsyncOpenAI(
            base_url=options["base_url"],
            api_key=options["api_key"],
        )
        # "osunlp/UGround-V1-7B"
        self.model_name = model_name

    def format_openai_template(self, description: str, image_base64: str) -> list[ChatCompletionUserMessageParam]:
        return [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                    },
                    {
                        "type": "text",
                        "text": f"""
    Your task is to help the user identify the precise coordinates (x, y) of a specific area/element/object on the screen based on a description.

    - Your response should aim to point to the center or a representative point within the described area/element/object as accurately as possible.
    - If the description is unclear or ambiguous, infer the most relevant area or element based on its likely context or purpose.
    - Your answer should be a single string (x, y) corresponding to the point of the interest.

    Description: {description}

    Answer:""",
                    },
                ],
            },
        ]

    def parse_response(self, response_text: str) -> tuple[int, int] | None:
        """response text should be (x, y) with x, y integers"""
        pattern = r"^\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*$"
        match = re.match(pattern, response_text)
        if match:
            x = int(match.group(1))
            y = int(match.group(2))
            return (x, y)
        else:
            console_logger.error("Response format not recognized.")
            return None

    async def predict(self, description: str, image_base64: str) -> utils.GroundingOutput:
        messages = self.format_openai_template(description, image_base64)

        completion = await self.client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            temperature=0,  # REMEMBER to set temperature to ZERO!
            # REMEMBER to set temperature to ZERO!
            # REMEMBER to set temperature to ZERO!
        )

        response_content = completion.choices[0].message.content
        if response_content is None:
            raise ValueError("UGround response is None")
        console_logger.debug("UGround response: %s", response_content)
        position = self.parse_response(response_content)
        if position is None:
            raise ValueError("UGround position is None")
        x, y = position
        image = utils.image_base64_to_pil_image(image_base64)
        x, y = utils.convert_position((x, y), image.size)
        console_logger.debug("Coords: %s", (x, y))
        out = utils.GroundingOutput(description=response_content, bbox=None, position=(x, y))
        return out
