import datetime
import re
import time
from typing import Dict, Optional

from services.studio._text_to_workflow.ui_automation.computer_use.common.computer_action import ComputerUseAction
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.computer_use.utils.keyboard_utils import KeyMapper
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import State
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uitars import get_absolute_coords

system_message_template = """You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
```
Thought: ...
Action: ...
```

## Action Space

click(start_box='<|box_start|>(x1,y1)<|box_end|>')
drag(start_box='<|box_start|>(x1,y1)<|box_end|>', end_box='<|box_start|>(x3,y3)<|box_end|>')
hotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.
type(content='xxx') # Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\n at the end of content.
scroll(start_box='<|box_start|>(x1,y1)<|box_end|>', direction='down or up or right or left') # Show more information on the `direction` side.
wait() # Sleep for 5s and take a screenshot to check for any changes.
finished()


## Note
- Use'English' in `Thought` part.
- Generate a well-defined and practical strategy in the `Thought` section, summarizing your next move and its objective.


## User Instruction
"""

user_message_template = """
Current Date: {current_date}

Task: {task}
"""


def extract_thought_action(block: str) -> Dict[str, Optional[str]]:
    """
    Extracts the thought and action from a block containing Thought and Action.

    Args:
        block (str): Text block containing 'Thought: ...' and 'Action: ...'.

    Returns:
        Dict[str, Optional[str]]: Dictionary with keys: 'thought', 'action'
    """
    thought = None
    action = None

    thought_match = re.search(r"Thought:\s*(.*?)\s*(?=Action:|$)", block, re.DOTALL)
    if thought_match:
        thought = thought_match.group(1).strip()

    action_match = re.search(r"Action:\s*(.*)", block, re.DOTALL)
    if action_match:
        action = action_match.group(1).strip()

    return {"thought": thought, "action": action}


def extract_action_info(action_line: str) -> Dict[str, Optional[object]]:
    """
    Extracts the action name, start_box, end_box, content, direction, and key from an action string.

    Args:
        action_line (str): The action part (e.g., from 'Action: ...').

    Returns:
        Dict[str, Optional[object]]: Dictionary with keys: action, start_box (tuple), end_box (tuple), content (str), direction (str), key (str)
    """
    result: Dict[str, Optional[object]] = {"action": None, "start_box": None, "end_box": None, "content": None, "direction": None, "key": None}

    # Extract action name
    action_match = re.match(r"^(\w+)\(", action_line)
    if action_match:
        result["action"] = str(action_match.group(1))

    # Extract start_box
    start_box_match = re.search(r"start_box='\(([^,]+),([^\)]+)\)'", action_line)
    if start_box_match:
        result["start_box"] = (int(start_box_match.group(1)), int(start_box_match.group(2)))

    # Extract end_box (only for drag)
    end_box_match = re.search(r"end_box='\(([^,]+),([^\)]+)\)'", action_line)
    if end_box_match:
        result["end_box"] = (int(end_box_match.group(1)), int(end_box_match.group(2)))

    # Extract content (only for type)
    content_match = re.search(r"content='(.*?)'", action_line, re.DOTALL)
    if content_match:
        result["content"] = content_match.group(1)

    # Extract direction (only for scroll)
    direction_match = re.search(r"direction='(.*?)'", action_line)
    if direction_match:
        result["direction"] = direction_match.group(1)

    # Extract key (only for hotkey)
    key_match = re.search(r"key='(.*?)'", action_line)
    if key_match:
        result["key"] = key_match.group(1)

    return result


def add_box_token(input_string):
    # Step 1: Split the string into individual actions
    if "Action: " in input_string and "start_box=" in input_string:
        suffix = input_string.split("Action: ")[0] + "Action: "
        actions = input_string.split("Action: ")[1:]
        processed_actions = []
        for action in actions:
            action = action.strip()
            # Step 2: Extract coordinates (start_box or end_box) using regex
            coordinates = re.findall(r"(start_box|end_box)='\((\d+),\s*(\d+)\)'", action)

            updated_action = action  # Start with the original action
            for coord_type, x, y in coordinates:
                # Convert x and y to integers
                updated_action = updated_action.replace(f"{coord_type}='({x},{y})'", f"{coord_type}='<|box_start|>({x},{y})<|box_end|>'")
            processed_actions.append(updated_action)

        # Step 5: Reconstruct the final string
        final_string = suffix + "\n\n".join(processed_actions)
    else:
        final_string = input_string
    return final_string


class UITARSAgent(object):
    def __init__(self, options: dict, add_image_to_response: bool = False):
        self.options = options
        self.add_image_to_response = add_image_to_response

    def build_messages(self, state: State):
        print(datetime.datetime.now().strftime("%Y-%m-%d"))
        system_message = {
            "role": "system",
            "content": system_message_template,
        }
        user_message = {
            "role": "user",
            "content": user_message_template.format(task=state.task, current_date=datetime.datetime.now().strftime("%Y-%m-%d")),
        }

        messages = [system_message, user_message]

        k = 4  # number of steps with images
        if len(state.previous_steps) > 0:
            for i, item in enumerate(state.previous_steps):
                if i >= len(state.previous_steps) - k:
                    if "image" in item and item["image"] is not None:
                        image_base64 = item["image"]
                    elif "additional_parameters" in item and "image_base64" in item["additional_parameters"]:
                        image_base64 = item["additional_parameters"]["image_base64"]
                    else:
                        continue

                    user_image_message = {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                            },
                        ],
                    }

                    messages.append(user_image_message)
                assistant_response = item["additional_parameters"].get("base_response", None)
                assistant_message = {
                    "role": "assistant",
                    "content": add_box_token(assistant_response),
                }
                messages.append(assistant_message)

        user_image_message = {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{state.image_base64}"},
                },
            ],
        }

        messages.append(user_image_message)
        return messages

    def create_state(self, request_body: dict) -> State:
        """
        Create a state object for the executor.
        """
        state = State(
            task=request_body["userTask"],
            image_base64=request_body["image"],
            previous_steps=request_body.get("previousSteps", []),
        )
        return state

    async def predict_request(self, request_body: dict) -> dict:
        # Extract the state from the request body
        state = self.create_state(request_body)
        step = await self.predict(state)
        step = step.to_response_dict()
        return {"step": step}

    async def predict(self, state: State, metadata: Optional[dict] = None) -> ComputerUseStep:
        start = time.time()
        messages = self.build_messages(state)
        response_content = await llm_client.send_messages(
            messages,
            options={
                "model_name": "ByteDance-Seed/UI-TARS-1.5-7B",
                # "model_name": "ByteDance-Seed/UI-TARS-72B-DPO",
                "base_url": "http://34.27.47.186:8007/v1",
                "api_key": "123",
            },
            metadata=metadata,
        )
        elapsed_time = time.time() - start
        console_logger.debug("UITARS Agent response time: %s", elapsed_time)
        console_logger.debug("UITARS Agent response: %s", response_content)
        if response_content is None:
            raise ValueError("UITARS Agent response is None")
        uitars_response = response_content

        parsed_step = extract_thought_action(response_content)
        if parsed_step["thought"] is None or parsed_step["action"] is None:
            parsed_step = {"thought": "", "action": response_content}

        if parsed_step["action"] is None:
            raise ValueError("UITARS Agent action is None")

        console_logger.debug("Parsed step: %s", parsed_step)
        parsed_action = extract_action_info(parsed_step["action"])

        if parsed_action["action"] is None:
            raise ValueError("UITARS Agent action is None")

        console_logger.debug("Parsed action: %s", parsed_action)

        if parsed_action["start_box"] is not None:
            parsed_action["start_box"] = list(get_absolute_coords(parsed_action["start_box"], state.image_base64))

        if parsed_action["end_box"] is not None:
            parsed_action["end_box"] = list(get_absolute_coords(parsed_action["end_box"], state.image_base64))

        console_logger.debug("Parsed action with absolute coordinates: %s", parsed_action)
        method_type = str(parsed_action["action"]).lower()

        description = parsed_step["action"] if parsed_step["action"] else ""
        action = ComputerUseAction(name=method_type, description=description, parameters={})
        match method_type:
            case "click":
                action.parameters["position"] = parsed_action["start_box"]
            case "scroll":
                action.parameters["position"] = parsed_action["start_box"]
                action.parameters["direction"] = parsed_action["direction"]
            case "drag":
                x, y = parsed_action["start_box"]
                x_end, y_end = parsed_action["end_box"]
                action.parameters["path"] = [
                    {"x": x, "y": y},
                    {"x": x_end, "y": y_end},
                ]
            case "type":
                action.parameters["value"] = parsed_action["content"]
                action.name = "type_into"
            case "hotkey":
                action.name = "keypress"
                keys = parsed_action["key"].split(" ")
                action.parameters["keys"] = [KeyMapper.map_openai_key(key) for key in keys]

            case "wait":
                action.name = "wait_load_completed"
            case "finished":
                action.name = "finish"
            case _:
                raise ValueError(f"Unknown action type: {method_type}")

        step = ComputerUseStep(
            description=parsed_step["thought"] if parsed_step["thought"] else "",
            actions=[action],
            additional_parameters={"base_response": uitars_response},
        )
        if self.add_image_to_response:
            step.additional_parameters["image_base64"] = state.image_base64

        return step


if __name__ == "__main__":
    # Example usage
    data = [
        "click(start_box='<|box_start|>(10,20)<|box_end|>')",
        "left_double(start_box='<|box_start|>(30,40)<|box_end|>')",
        "right_single(start_box='<|box_start|>(50,60)<|box_end|>')",
        "drag(start_box='<|box_start|>(70,80)<|box_end|>', end_box='<|box_start|>(90,100)<|box_end|>')",
        "hotkey(key='ctrl c')",
        "type(content='hello world')",
        "scroll(start_box='<|box_start|>(110,120)<|box_end|>', direction='down')",
        "wait()",
        "finished()",
        "call_user()",
    ]

    for line in data:
        print(extract_action_info(line))
