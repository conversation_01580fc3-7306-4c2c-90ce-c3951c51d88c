import datetime
import time

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.planning_state import PlanningState
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import TaskPlanAction
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

system_template = """You are a computer use agent that performs computer-related tasks via an executor agent.
You will be given a task, a current screen image, and a list of previous sub-tasks. You need to predict the next sub-task to perform by the executor agent. This sub-task should be valid for the current screen only.
The possible sub-task types are: "set_value", "act", "wait", "extract_data", "finish". Here are the detailed sub-task definitions:

{{"type": "set_value", "description": "...", "parameters": {{"element_description": "str", "value": "str"}}}}  # use it to set a value in an input box, datepicker, combobox etc.
For set_value sub-task, you need to give a short and precise description of the element to set the value and the actual value to be set. The element must be seen in the screen.

{{"type": "act", "description": "...", "parameters": {{}}}} # use it to perform some simple actions on the screen. These actions should be valid for the current screen only and shouldn't be too complex such that executor agent can't perform it.
Take into account that executor agent is not that smart to understand complex actions.

{{"type": "wait_load_completed", "description": "...", "parameters": {{}}}} # use it to wait for the screen to load. This sub-task should be used when you want to wait for the screen to load completely before performing any other actions.

{{"type": "extract_info_from_screen", "description": "...", "parameters": {{"prompt": "str","label":"str"}}}} # Extract information from screen using a LLM. Use it to extract some data from the screen to fulfill the task. The prompt should be precise such that llm model could extract the requested data. Also associate an output_label for the extracted data to be used in the task completion process.

{{"type": "finish", "description": "...", "parameters": {{"status": "Literal('success', 'failure', 'user_input')"}}}}  # Indicate that the task is completed successfully, stopped with failure or require additional user input to procceed. This action should be used when the task is completed and no further actions are needed or when the task is impossible to be completed, like being stuck in a loop.


Describe your sub-task description in a single sentence. Is critical to unambiguously describe what the executor agent should do.

Example of valid sub-task descriptions: 
- "Set value 'John' in the first name input box.
- "Set value 'Thu 28' in the date input box."
- "Close the edit user form."
- "Scroll down to the last element from the list."
- "Wait for the page to load."
- "Extract the email address from user tab."
- "Finish the task with success."

set_value sub task description must always start with "Set value".

Your response must be a valid json string with the following fields:
{{
    "type": str  ## one of the valid sub-task types
    "description": ## descripion of the sub-task ,
    "parameters": ## optional, a dictionary for sub-task parameters in needed
}}

Example of valid sub-tasks:
{{
    "type": "set_value",
    "description": "Set value 'iPhone 14' in the available phones combo box.",
    "parameters": {{
        "element_description": "available phones combo box",
        "value": "iPhone 14"
    }}
}}

{{
    "type": "act",
    "description": "Submit the form by clicking the 'Submit' button.",
    "parameters": {{}}   
}}

{{
    "type": "act",
    "description": "Check the checkbox 'I agree with the terms and conditions'",
    "parameters": {{}}   
}}

{{
    "type": "extract_info_from_screen",
    "description": "Extract the product name and price from the screen.",
    "parameters": {{
        "prompt": "Extract the product name and price from the the product details page.",
        "label": "product_name_and_price"
    }}
}}

{{
    "type": "wait_load_completed",
    "description": "Wait for table items to load. Currently a spinner is present.",
    "parameters": {{}}
}}

{{
    "type": "finish",
    "description": "Task completed, the information required was extracted.",
    "parameters": {{
        "status": "success"
    }}
}}


Here are some general rules:
- Cookies, advertisements, and other unrelevant pop-ups should be closed.
- Do not login/register unless it is stated in the task.
- Make sure the element you want to interact with is visible on the screen. If not then you must scroll to see it. Never create a sub task on not visible elements.
- Adapt your sub-task based on the feedback received from the executor agent. If previous sub-task is not performed correctly, you need to adapt the sub-task and try again.

"""

user_message_template = """Here are the current information:
The current date is (YYYY-MM-DD): {current_date}

Task: {task}

Previous sub-tasks:
{history}

You answer in the following format:
Observation: Describe the current screen and analysis of the previous executed sub-tasks (1 line)
Reasoning: Does the task finish? If not give the nex sub-task (1 line)?
Next sub-task:
<sub-task in json format specified in the system message>
"""


class TaskPlanner(object):
    def __init__(self, options: dict):
        self.options = options

    def build_messages(self, state: PlanningState):
        print(datetime.datetime.now().strftime("%Y-%m-%d"))
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }
        contents = [
            {
                "type": "text",
                "text": user_message_template.format(
                    task=state.task,
                    history=state.history.build_history_str(),
                    current_date=datetime.datetime.now().strftime("%Y-%m-%d"),
                ),
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{state.image_base64}"},
            },
        ]
        user_message = {
            "role": "user",
            "content": contents,
        }
        messages = [system_message, user_message]
        return messages

    async def predict(self, state: PlanningState) -> TaskPlanAction:
        start = time.time()
        messages = self.build_messages(state)
        metadata = {
            "session_id": state.session_id,
            "request_id": "request_" + str(len(state.previous_steps)),
            "generation_name": "planner",
        }
        response_content = await llm_client.send_messages(
            messages,
            options=self.options["planner"]["model"],
            metadata=metadata,
        )
        elapsed_time = time.time() - start
        console_logger.debug("Planner elapsed time: %s", elapsed_time)
        console_logger.debug("Planner response: %s", response_content)
        if response_content is None:
            raise ValueError("Planner response is None")
        response_json = parse_message_json(str(response_content))
        action = TaskPlanAction.from_dict(response_json)
        if action is None:
            raise ValueError("Planner response is not valid")
        return action
