import json

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import TaskPlanAction


class History:
    """
    Class to manage the history of the UI agent's actions.
    """

    def __init__(self):
        self.previous_substasks: list[TaskPlanAction] = []
        self.current_subtask: TaskPlanAction | None = None

    def build_history_str(self) -> str:
        """
        Build a string representation of the previous steps taken.
        """
        all_subtasks = self.previous_substasks + [self.current_subtask] if self.current_subtask else self.previous_substasks
        history_str = ""
        for index, subtask in enumerate(all_subtasks):
            history_str += f"Subtask {index}: {subtask.description}\n"
            for step in subtask.previous_steps:
                history_str += f"\t - {step.description}\n"
                if len(step.actions) == 0:
                    continue
                step_result = step.actions[0].result
                if step_result is not None:
                    step_result_str = f"Step result: {json.dumps(step_result)}"
                    history_str += f"\t\t{step_result_str}\n"
        history_str = history_str.strip()
        console_logger.debug("History string: %s", history_str)
        return history_str
