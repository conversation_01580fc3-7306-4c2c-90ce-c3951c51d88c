from services.studio._text_to_workflow.ui_automation.cache import (
    add_image_to_cache,
)
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.llm_client import TRACE_FLAG, langfuse
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.planning_state import PlanningState
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.review_and_plan import ReviewAndPlan
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.task_planner import TaskPlanner
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.task_reviewer import ReviewStatus, TaskReviewer
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.output_compiler import OutputComplier
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uitars_agent import UITARSAgent
from services.studio._text_to_workflow.utils import request_utils


class UIMasterSlaveAgent(object):
    def __init__(self, options: dict):
        self.options = options
        self.planner = TaskPlanner(options)
        self.task_reviewer = TaskReviewer(options)
        self.combine_review_and_plan = True
        self.reviewer_and_planner = ReviewAndPlan(options)
        self.executor = UITARSAgent(options)  # we can Claude or OpenAI
        self.output_compiler = OutputComplier(options)
        self.history_dict = {}
        self.current_substasks = []

    async def predict_request(self, request_body: dict) -> dict:
        request_context = request_utils.get_request_context()
        session_id = request_context.ui_task_session_id if request_context else None
        if session_id is None:
            raise ValueError("Session ID is None. Cannot retrieve history.")

        # Extract the state from the request body
        state = PlanningState(
            session_id=session_id,
            task=request_body["userTask"],
            image_base64=request_body["image"],
            previous_steps=request_body.get("previousSteps", []),
        )
        return await self.predict(state)

    def create_executor_query(self, sub_task: str, previous_steps: list[ComputerUseStep], image_base64: str) -> dict:
        """
        Create a state object for the executor.
        """

        return {
            "userTask": sub_task,
            "image": image_base64,
            "previousSteps": [step.to_response_dict() for step in previous_steps],
        }

    async def review_and_plan_separate(self, state: PlanningState) -> tuple[ReviewStatus | None, bool]:
        review_status: ReviewStatus | None = None
        if state.history.current_subtask is not None:
            review_status = await self.task_reviewer.predict(state)
            if review_status.status == "fail" or review_status.status == "succeed":
                state.history.previous_substasks.append(state.history.current_subtask)
                state.history.current_subtask = None
            elif len(state.history.current_subtask.previous_steps[-1].actions) == 0:
                # exectutor returned "finished" in last step
                state.history.current_subtask = None
            elif review_status.status == "continue":
                if review_status.refined_subtask is not None and review_status.refined_subtask.strip() != "":
                    state.history.current_subtask.description = review_status.refined_subtask

        if state.history.current_subtask is None:
            plan_output = await self.planner.predict(state)
            plan_output.id = len(state.history.previous_substasks)
            is_new_subtask = True
        else:
            plan_output = state.history.current_subtask
            is_new_subtask = False
        return review_status, is_new_subtask

    async def review_and_plan_together(self, state: PlanningState) -> tuple[ReviewStatus | None, bool]:
        review_status: ReviewStatus | None = None
        is_new_subtask = False
        if len(state.previous_steps) > 0:
            review_and_plan = await self.reviewer_and_planner.predict(state)
            if review_and_plan is None:
                raise ValueError("Review and Plan result is None. Cannot proceed.")
            review_status = review_and_plan.review_status
            if review_status.status == "fail" or review_status.status == "succeed":
                is_new_subtask = True
                if state.history.current_subtask is not None:
                    state.history.previous_substasks.append(state.history.current_subtask)
                state.history.current_subtask = review_and_plan.plan_subtask
                state.history.current_subtask.id = len(state.history.previous_substasks)
            elif state.history.current_subtask is not None and state.history.current_subtask.previous_steps[-1].actions[0].name == "finish":
                state.history.current_subtask = None
            elif review_status.status == "continue":
                is_new_subtask = False
                if review_status.refined_subtask is not None and review_status.refined_subtask.strip() != "":
                    state.history.current_subtask.description = review_status.refined_subtask
        else:
            plan_output = await self.planner.predict(state)
            plan_output.id = len(state.history.previous_substasks)
            state.history.current_subtask = plan_output
            is_new_subtask = True
        # if "scroll" in  state.history.current_subtask.description.lower():
        #     print("scroll detected")

        return review_status, is_new_subtask

    async def predict(self, state: PlanningState) -> dict:
        # Simulate a prediction process

        review_status: ReviewStatus | None = None
        is_new_subtask = False

        if self.combine_review_and_plan:
            review_status, is_new_subtask = await self.review_and_plan_together(state)
        else:
            review_status, is_new_subtask = await self.review_and_plan_separate(state)

        plan_output = state.history.current_subtask
        if plan_output is None:
            raise ValueError("Plan output is None. Cannot proceed.")

        step: dict | None = None

        match plan_output.action_type:
            case "wait_load_completed":
                action = {
                    "method_type": plan_output.action_type,
                    "description": plan_output.description,
                    "parameters": {},
                }
                actions = [action]
                step = {"description": plan_output.description, "actions": actions}
            case "extract_info_from_screen":
                action = {
                    "method_type": plan_output.action_type,
                    "description": plan_output.description,
                    "parameters": plan_output.parameters,
                }
                action["parameters"]["enableDOM"] = True
                actions = [action]
                step = {"description": plan_output.description, "actions": actions}
            case "finish":
                if plan_output.parameters.get("status", "").lower() == "sucess":
                    task_output = await self.output_compiler.predict(state.get_base_state(), history=state.history.build_history_str())
                else:
                    task_output = ""

                action = {
                    "method_type": plan_output.action_type,
                    "description": plan_output.description,
                    "parameters": plan_output.parameters,
                    "result": task_output,
                }
                actions = [action]
                step = {"description": plan_output.description, "actions": actions}
            case "act" | "set_value":
                if is_new_subtask:
                    state.history.current_subtask = plan_output
                if plan_output.action_type == "set_value":
                    executor_task = f"{plan_output.description}. Element description: {plan_output.parameters['element_description']}. Value to set: {plan_output.parameters['value']}"
                else:
                    executor_task = plan_output.description
                sub_task = executor_task

                executor_query = self.create_executor_query(sub_task, state.history.current_subtask.previous_steps, state.image_base64)
                executor_state = self.executor.create_state(executor_query)
                executor_result_step = await self.executor.predict(executor_state)
                # executor_result_step.image = executor_state.image_base64
                # state.history.current_subtask.previous_steps.append(executor_result_step)
                step_dict = executor_result_step.to_response_dict()

                if executor_result_step.actions[0].name == "finish":
                    step_dict["actions"] = []

                step = step_dict

        if step is not None:
            if "additional_parameters" not in step:
                step["additional_parameters"] = {}
            step["additional_parameters"]["parent_subtask"] = plan_output.to_dict()
            if review_status is not None:
                step["additional_parameters"]["preview_step_review_status"] = review_status.to_dict()

        add_image_to_cache(state.session_id, step_index=len(state.previous_steps), image=state.image_base64)

        if TRACE_FLAG:
            # add result summary to trace output
            session_id = state.session_id
            request_id = "request_" + str(len(state.previous_steps))
            trace = langfuse.trace(id=f"{session_id}_{request_id}", session_id=session_id, name=request_id)
            trace_output = {
                "reviewer": {
                    "status": review_status.status if review_status else None,
                    "refresh_subtask": review_status.refined_subtask if review_status else None,
                },
                "planner": {"id": plan_output.id, "description": plan_output.description},
                "action": step["actions"][0]["description"] if len(step["actions"]) > 0 else None,
            }
            trace.update(output=trace_output)
            langfuse.flush()

        return {"step": step}
