from collections import OrderedDict

from services.studio._text_to_workflow.ui_automation.cache import get_images_from_cache
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.history import History
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import TaskPlanAction
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import State


class PlanningState(object):
    """
    Class representing the state of a computer.
    """

    def __init__(self, session_id: str, task: str, image_base64: str, previous_steps: list):
        self.task = task
        self.session_id = session_id
        self.image_base64 = image_base64
        self.previous_steps = previous_steps
        self.history = self._create_history()

    def _create_history(self):
        """
        Create a history object.
        """

        subtasks: OrderedDict[int, TaskPlanAction] = OrderedDict()

        images = get_images_from_cache(self.session_id)
        images_dict = {item[1]: item[0] for item in images}

        current_subtask = None
        for index, step in enumerate(self.previous_steps):
            computer_use_step = ComputerUseStep.from_request_dict(step)
            if computer_use_step.additional_parameters is not None and computer_use_step.additional_parameters["parent_subtask"] is not None:
                parent_subtask = TaskPlanAction.from_dict(computer_use_step.additional_parameters["parent_subtask"])
                parent_subtask_status_response = None

                if index < len(self.previous_steps) - 1:
                    next_step = ComputerUseStep.from_request_dict(self.previous_steps[index + 1])
                    if next_step.additional_parameters is not None:
                        parent_subtask_status_response = next_step.additional_parameters.get("preview_step_review_status", None)

                parent_subtask_id: int = parent_subtask.id
                if parent_subtask_id not in subtasks:
                    subtasks[parent_subtask_id] = parent_subtask
                else:
                    subtasks[parent_subtask_id].description = parent_subtask.description

                if index in images_dict:
                    computer_use_step.image = images_dict[index]

                subtasks[parent_subtask_id].previous_steps.append(computer_use_step)
                parent_subtask_status = "continue"
                if parent_subtask_status_response is not None:
                    parent_subtask_status = parent_subtask_status_response["status"]
                if parent_subtask_status == "succeed" or parent_subtask_status == "fail":
                    current_subtask = None
                else:
                    current_subtask = subtasks[parent_subtask_id]

        history = History()
        history.previous_substasks = [item for item in list(subtasks.values()) if item != current_subtask]
        history.current_subtask = current_subtask

        return history

    def get_base_state(self) -> State:
        """
        Get the base state of the computer.
        """
        return State(
            task=self.task,
            image_base64=self.image_base64,
            previous_steps=self.previous_steps,
        )
