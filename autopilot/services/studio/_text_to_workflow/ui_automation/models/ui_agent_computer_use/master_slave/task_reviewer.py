import datetime
import io
import json
import time
from typing import Union

import cv2
import numpy as np
import pybase64
from PIL import Image

from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.planning_state import PlanningState
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

system_template = """You are a computer use agent that performs computer-related tasks via an executor agent. You scheduled a subtask to the executor agent and now you need to review the executor agent step.
As an input you will receive the subtask description, the previous image corresponding with the last action predicted by the  executor annotated with a red cross, the current screen image, and all the previous actions done by the executor agent step. You need to review the executor agent steps and decide if it is correct to continue the execution.
Your response should contain a status with one of the following values:
- "succeed": the subtask is completed
- "fail": if the executor agent is stuck in a loop or the predicted step by executor agents removes all the progress made so far.
- "continue": is not "succeed" or "fail".

The response should be in json format and have the following fields:
{{
    "observation": "...", # describe the screen in the context of the subtask"
    "reasoning": "...", # analyze the previous actions and the current prediction of the executor agent step. Look at the red cross from the previous image if it's present to undertand if last action was correct or not.
    "status": Literal["succeed","fail","continue"]
    "refined_subtask": "...", # if the status is "continue" you can provide a refined subtask description to be used by the executor agent. This field is optional but is important when you realize that the executor agent is not interacting with the correct element or the subtask is not well defined. Adjust the current subtask description to be more specific. For example, if the subtask is "click on the button" and the executor agent is clicking on the wrong button, you can change the subtask to "click on the button with text 'submit'". This will help the executor agent to understand what it needs to do next.
}}

Here are some examples of valid responses:
{{
    "observation": "The screen shows the booking page with the calendar for checking date open.",
    "reasoning": "The executor agent is trying to click the next month button to select the proper month",
    "status": "continue"    
}}
"""

user_message_template = """Here are the current information:
The current date is (YYYY-MM-DD): {current_date}

Subtask: {task}

Previous actions:
{history}

Given the current screenshot and the execution trace for the task provide your response in json format.
Make sure your response is a valid json (double quotes, no comment, no trailing comma...). Be as accurate as possible
"""


class State(object):
    def __init__(self, task: str, sub_task: str, image_base64: str, history: list):
        self.task = task
        self.sub_task = sub_task
        self.image_base64 = image_base64
        self.history = history


class ReviewStatus(object):
    def __init__(self, observation: str, reasoning: str, status: str, refined_subtask: str | None = None):
        self.observation = observation
        self.reasoning = reasoning
        self.status = status
        self.refined_subtask = refined_subtask

    @classmethod
    def from_dict(cls, data: dict) -> Union["ReviewStatus", None]:
        if not isinstance(data, dict):
            return None
        if "status" not in data:
            raise ValueError("status is not in the response")
        status_val = data.get("status", "").lower()
        if status_val not in ["succeed", "fail", "continue"]:
            raise ValueError("status is not valid")
        return cls(
            observation=data.get("observation", ""),
            reasoning=data.get("reasoning", ""),
            status=data.get("status", ""),
            refined_subtask=data.get("refined_subtask", None),
        )

    def to_dict(self) -> dict:
        return {
            "observation": self.observation,
            "reasoning": self.reasoning,
            "status": self.status,
            "refined_subtask": self.refined_subtask,
        }


class TaskReviewer(object):
    def __init__(self, options: dict):
        self.options = options

    def build_messages(self, state: PlanningState):
        print(datetime.datetime.now().strftime("%Y-%m-%d"))
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }
        image_base64 = state.image_base64

        def previous_steps_str(previous_steps: list[ComputerUseStep]) -> str:
            previous_steps_str = ""

            for step in previous_steps:
                previous_steps_str += f"\t - {step.description}\n"
                if len(step.actions) == 0:
                    continue
                step_result = step.actions[0].result
                if step_result is not None:
                    step_result_str = f"Step result: {json.dumps(step_result)}"
                    previous_steps_str += f"\t\t{step_result_str}\n"

            return previous_steps_str.strip()

        previous_steps = state.history.current_subtask.previous_steps

        previous_history = previous_steps_str(previous_steps)
        last_step = state.history.current_subtask.previous_steps[-1]
        last_step_image = last_step.image
        last_step_image = self.annotate_image(last_step_image, last_step)

        contents = [
            {
                "type": "text",
                "text": user_message_template.format(
                    task=state.history.current_subtask.description,
                    history=previous_history,
                    current_date=datetime.datetime.now().strftime("%Y-%m-%d"),
                ),
            },
            {
                "type": "text",
                "text": "Last step screenshot with red cross on the last action position",
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{last_step_image}"},
            },
            {
                "type": "text",
                "text": "Current screenshot",
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            },
        ]
        user_message = {
            "role": "user",
            "content": contents,
        }

        messages = [system_message, user_message]

        return messages

    def annotate_image(self, image_base64: str, predicted_step: ComputerUseStep) -> str:
        image_blob = pybase64.b64decode(str(image_base64), validate=True)
        image_np = np.frombuffer(image_blob, np.uint8)
        image_array = cv2.imdecode(image_np, 1)
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        if predicted_step is not None and predicted_step.actions is not None and len(predicted_step.actions) > 0:
            action = predicted_step.actions[0]

            if "position" in action.parameters:
                position = action.parameters["position"]
                x1 = int(position[0])
                y1 = int(position[1])
                # draw a red cross on the image
                image_array = cv2.drawMarker(image_array, (x1, y1), (255, 0, 0), markerType=cv2.MARKER_CROSS, markerSize=10, thickness=2)
                pimage = Image.fromarray(image_array)

                with io.BytesIO() as bio:
                    pimage.save(bio, "png")
                    image_data = bio.getvalue()
                    image_string = pybase64.b64encode(image_data).decode("utf-8")
                    return image_string
        return image_base64

    async def predict(self, state: PlanningState) -> ReviewStatus:
        start = time.time()
        messages = self.build_messages(state)
        metadata = {
            "session_id": state.session_id,
            "request_id": "request_" + str(len(state.previous_steps)),
            "generation_name": "reviewer",
        }
        response_content = await llm_client.send_messages(
            messages,
            options=self.options["planner"]["model"],
            metadata=metadata,
        )
        elapsed_time = time.time() - start
        console_logger.debug("Task reviewer took %s seconds", elapsed_time)
        console_logger.debug("Reviewer raw response: %s", response_content)
        if response_content is None:
            raise ValueError("Planner response is None")
        response_json = parse_message_json(str(response_content))
        status = ReviewStatus.from_dict(response_json)
        if status is None:
            raise ValueError("Reviewer response is not valid")
        return status
