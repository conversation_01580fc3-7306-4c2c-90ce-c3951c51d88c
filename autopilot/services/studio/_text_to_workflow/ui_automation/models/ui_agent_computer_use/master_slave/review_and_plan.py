import datetime
import io
import json
import time
from typing import Union

import cv2
import numpy as np
import pybase64
from PIL import Image

from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.planning_state import PlanningState
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.task_reviewer import ReviewStatus
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import TaskPlanAction
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

system_template = """You are a computer use agent that performs computer-related tasks via an executor agent. Your task is to review and plan the next subtask step for the executor agent.
As an input you will receive the main task description, the previous image corresponding with the last action predicted by the  executor annotated with a red cross if there is one, the current screen image, and all the previous actions done by the executor agent.
First you need to review the executor agent steps and decide if it is correct to continue the execution or not.
The review should contain a status with one of the following values:
- "succeed": the subtask is completed
- "fail": if the executor agent is stuck in a loop or the predicted step by executor agents removes all the progress made so far.
- "continue": is not "succeed" or "fail".

In case the status is fail or succeed or there is no previous substask, you need to plan the next subtask step for the executor agent. 
The subtask can have one of the following types:

{{"type": "set_value", "description": "...", "parameters": {{"element_description": "str", "value": "str"}}}} # use it to set a value in an input box, datepicker, combobox etc. This action should be used when you want to set a value to a particular element.
For set_value action, you need to give a short and precise description of the element to set the value and the actual value to be set. The element must be seen in the screen.

{{"type": "act", "description": "...", "parameters": {{}}}} # use it to perform some simple action on the screen. This action should be valid for the current screen only and shouldn't be too complex such that executor agent can't perform it.
For act action, you need to give a concise and precision description of the action to be performed. Take into account that executor agent is not that smart to understand complex actions.

{{"type": "wait_load_completed", "description": "...", "parameters": {{}}}} # use it to wait for the screen to load. This action should be used when you want to wait for the screen to load completely before performing any other actions.

{{"type": "extract_info_from_screen", "description": "...", "parameters": {{"prompt": "str","label":"str"}}}} # Extract information from screen using a LLM. Use it to extract some data from the screen to fulfill the task. The prompt should be precise such that llm model could extract the requested data. Also associate an output_label for the extracted data to be used in the task completion process.

{{"type": "finish", "description": "...", "parameters": {{"status": "Literal('success', 'failure', 'user_input')"}}}}  # Indicate that the main task is completed successfully, stopped with failure or require additional user input to procceed. his action should be used when the task is completed and no further actions are needed or when the task is impossible to be completed, like being stuck in a loop.

Describe your substask description in a single sentence. Is critical to unambiguously describe what the executor agent should do.
If there are several similar elements, and you wish to interact with one them in a particular substask, discriminate it in the description. For example:
- "Click the 'Search' button on the top right corner next to the login button."


The full response should be a valid json string and should have the following structure:
{{
   "review_subtask": {{
    "observation": "...", # describe the screen in the context of the subtask"
    "reasoning": "...", # analyze the previous actions and the current prediction of the executor agent step. Look at the red cross from the previous image if it's present to undertand if last action was correct or not.
    "status": Literal["succeed","fail","continue"]
    "refined_subtask": "...", # if the status is "continue" you can provide a refined subtask description to be used by the executor agent. This field is optional but is important when you realize that the executor agent is not interacting with the correct element or the subtask is not well defined. Adjust the current subtask description to be more specific. For example, if the subtask is "click on the button" and the executor agent is clicking on the wrong button, you can change the subtask to "click on the button with text 'submit'". This will help the executor agent to understand what it needs to do next.
    }}
    "plan_subtask": {{
    "type": str  ## one of the valid action types
    "description": ## descripion of the action,
    "parameters": ## optional, a dictionary for action parameters in needed
    }}
}}



Here are some examples of valid responses:
{{ 
    "review_subtask": {{
    "observation": "The screen shows the booking page with the calendar for checking date open.",
    "reasoning": "The executor agent is trying to click the next month button to select the proper month",
    "status": "continue"
    }}    
}}

{{ 
    "review_subtask": {{
    "observation": "The screen shows the booking page with the destination set to 'Bucharest'.",
    "reasoning": "The executor successfully set the destination to 'Bucharest'.",
    "status": "succeed",
    }}   
    "plan_subtask": {{ 
    "type": "set_value",
    "description": "Set value 'Check-in date' with 25 May 2025.",
    "parameters": {{
        "element_description": "'Check-in date' input box",
        "value": "25 May 2025"
        }}
    }}
}}


{{ 
    "review_subtask": {{
    "observation": "The screen displays the User Profile page",
    "reasoning": "The executor successfully navigated to the User Profile page.",
    "status": "succeed",
    }}   
    "plan_subtask": {{ 
    "type": "act",
    "description": "Scroll down to find all the additional information.",
    "parameters": {{        
        }}
    }}
}}

{{ 
    "review_subtask": {{
        "observation": "The product details page shows the product name and price.",
        "reasoning": "The executor agent successfully navigated to the product details page.",
        "status": "succeed",
    }}   
    "plan_subtask": {{ 
        "type": "extract_info_from_screen",
        "description": "Extract the product name and price from the screen.",
        "parameters": {{
            "prompt": "Extract the product name and price from the the product details page.",
            "label": "product_name_and_price"
        }}
    }}
}}

{{ 
    "review_subtask": {{
        "observation": "The screen displays the flight from 'New York' to 'Los Angeles' with the date set to '2025-05-25'.",
        "reasoning": "The flight details data was succesfully extracted from the screen.",
        "status": "succeed",
    }}   
    "plan_subtask": {{ 
        "type": "finish",
        "description": "Task completed, the information required in the main task was extracted.
            "parameters": {{
            "status": "success"
        }}
    }}
}}

Here are some general rules:
- Cookies, advertisements, and other unrelevant pop-ups should be closed.
- Do not login/register unless it is stated in the task.
- Make sure the element you want to interact with is visible on the screen. If not then you must scroll to see it. Never take an action on not visible elements.
- adapt your actions based on the feedback received from the executor agent. If the action is not performed correctly, you need to adapt your actions and try again.
- Do not consider the task completed only because the last subtask is completed. The task is completed when all the requirements are met or all the data found and extracted.
"""

user_message_template = """Here are the current information:
The current date is (YYYY-MM-DD): {current_date}

Task: {task}
Current subtask: {sub_task}

Previous actions:
{history}

Given the current screenshot and the execution trace for the task provide your response in json format.
Make sure your response is a valid json (double quotes, no comment, no trailing comma...). Be as accurate as possible
"""


class State(object):
    def __init__(self, task: str, sub_task: str, image_base64: str, history: list):
        self.task = task
        self.sub_task = sub_task
        self.image_base64 = image_base64
        self.history = history


class ReviewAndPlanResult(object):
    def __init__(self, review_status: ReviewStatus, plan_subtask: TaskPlanAction | None = None):
        self.review_status = review_status
        self.plan_subtask = plan_subtask

    @classmethod
    def from_dict(cls, data: dict) -> Union["ReviewAndPlanResult", None]:
        if not isinstance(data, dict):
            return None
        review_status = ReviewStatus.from_dict(data.get("review_subtask", {}))
        plan_subtask = None
        plan_subtask_dict = data.get("plan_subtask", {})
        if plan_subtask_dict is not None and "type" in plan_subtask_dict:
            plan_subtask = TaskPlanAction.from_dict(data.get("plan_subtask", {}))
        if review_status is None:
            raise ValueError("review_subtask is not valid")

        return cls(review_status, plan_subtask)


class ReviewAndPlan(object):
    def __init__(self, options: dict):
        self.options = options

    def build_messages(self, state: PlanningState):
        print(datetime.datetime.now().strftime("%Y-%m-%d"))
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }
        image_base64 = state.image_base64

        def get_step_description(step: ComputerUseStep, extract_options: dict | None = None) -> str:
            step_description = ""
            if extract_options is None or "extract_description" in extract_options:
                step_description = f"\t - {step.description}\n"

            if extract_options is not None and "extract_result" in extract_options:
                if len(step.actions) > 0:
                    step_result = step.actions[0].result
                    if step_result is not None:
                        step_result_str = f"Step result: {json.dumps(step_result)}"
                        step_description += f"\t\t{step_result_str}\n"
            return step_description

        def previous_steps_str(previous_steps: list[ComputerUseStep]) -> str:
            previous_steps_str = ""

            for step in previous_steps:
                previous_steps_str += get_step_description(step)

            return previous_steps_str.strip()

        previous_steps = state.history.current_subtask.previous_steps

        previous_history = previous_steps_str(previous_steps)
        last_step = state.history.current_subtask.previous_steps[-1]
        last_step_image = last_step.image
        last_step_image = self.annotate_image(last_step_image, last_step)

        last_action_description = get_step_description(last_step, {"extract_description": True, "extract_result": False})

        extracted_data_in_last_step = get_step_description(last_step, {"extract_description": False, "extract_result": True}).strip()

        if extracted_data_in_last_step != "":
            last_message = f"Current screen and the extracted data from the last step:\n{extracted_data_in_last_step}"
        else:
            last_message = "Current screen"

        contents = [
            {
                "type": "text",
                "text": user_message_template.format(
                    task=state.task,
                    sub_task=state.history.current_subtask.description,
                    history=previous_history,
                    current_date=datetime.datetime.now().strftime("%Y-%m-%d"),
                ),
            },
            {
                "type": "text",
                "text": f"Last action: {last_action_description}",
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{last_step_image}"},
            },
            {
                "type": "text",
                "text": last_message,
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            },
        ]
        user_message = {
            "role": "user",
            "content": contents,
        }

        messages = [system_message, user_message]

        return messages

    def annotate_image(self, image_base64: str, predicted_step: ComputerUseStep) -> str:
        image_blob = pybase64.b64decode(str(image_base64), validate=True)
        image_np = np.frombuffer(image_blob, np.uint8)
        image_array = cv2.imdecode(image_np, 1)
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        if predicted_step is not None and predicted_step.actions is not None and len(predicted_step.actions) > 0:
            action = predicted_step.actions[0]

            if "position" in action.parameters:
                position = action.parameters["position"]
                x1 = int(position[0])
                y1 = int(position[1])
                # draw a red cross on the image
                image_array = cv2.drawMarker(image_array, (x1, y1), (255, 0, 0), markerType=cv2.MARKER_CROSS, markerSize=10, thickness=2)
                pimage = Image.fromarray(image_array)

                with io.BytesIO() as bio:
                    pimage.save(bio, "png")
                    image_data = bio.getvalue()
                    image_string = pybase64.b64encode(image_data).decode("utf-8")
                    return image_string
        return image_base64

    async def predict(self, state: PlanningState) -> ReviewAndPlanResult:
        start = time.time()
        messages = self.build_messages(state)
        response_content = await llm_client.send_messages(
            messages,
            options=self.options["planner"]["model"],
        )
        elapsed_time = time.time() - start
        console_logger.debug("Task reviewer took %s seconds", elapsed_time)
        console_logger.debug("Reviewer raw response: %s", response_content)
        if response_content is None:
            raise ValueError("Planner response is None")
        response_json = parse_message_json(str(response_content))
        status = ReviewAndPlanResult.from_dict(response_json)
        if status is None:
            raise ValueError("Reviewer response is not valid")
        return status
