import base64
import io
import json
import re
from abc import ABC, abstractmethod
from io import Bytes<PERSON>
from typing import Optional

import cv2
import numpy as np
import pybase64
from json_minify import json_minify
from json_repair import repair_json
from PIL import Image


class ValidationException(Exception):
    def __init__(self, message: str):
        self.message = message


def parse_message_json(message: str) -> dict:
    message = message.strip()

    # 1. Try extracting between triple backticks (```json ... ```)
    #    The '([\s\S]+?)' part matches across multiple lines (non‐greedy).
    code_block_pattern = r"```json\s*([\s\S]+?)```"
    code_block_match = re.search(code_block_pattern, message, re.DOTALL)

    if code_block_match:
        # If we find a code block, extract just that part
        json_str = code_block_match.group(1).strip()
    else:
        # 2. Fallback to finding the first {...} block in the text
        bracket_pattern = r"\{.*\}"
        bracket_match = re.search(bracket_pattern, message, re.DOTALL)
        if not bracket_match:
            # If there's no match at all, return None or raise an error
            raise ValidationException("Reponse does not have correct json format")
        json_str = bracket_match.group(0).strip()

    # 3. Remove comments, etc. before parsing
    try:
        json_str = json_minify(json_str)
        data = json.loads(json_str)
    except json.JSONDecodeError:
        try:
            json_str = repair_json(json_str)
            data = json.loads(json_str)
        except json.JSONDecodeError:
            raise ValidationException("Reponse does not have correct json format")
    return data


def convert_bbox(normalized_bbox, image_size, resize_size=1000):
    """
    Convert a normalized bounding box in 0-1000 range to absolute pixel values.
    image_size is (width, height).
    """
    width, height = image_size
    x1, y1, x2, y2 = normalized_bbox
    x1_abs = int(x1 / resize_size * width)
    y1_abs = int(y1 / resize_size * height)
    x2_abs = int(x2 / resize_size * width)
    y2_abs = int(y2 / resize_size * height)
    return (x1_abs, y1_abs, x2_abs, y2_abs)


def convert_position(normalized_position, image_size, resize_size=1000):
    """
    Convert a normalized bounding box in 0-1000 range to absolute pixel values.
    image_size is (width, height).
    """
    width, height = image_size
    x, y = normalized_position
    x_abs = int(x / resize_size * width)
    y_abs = int(y / resize_size * height)
    return (x_abs, y_abs)


def image_base64_to_pil_image(image_base64: str) -> Image.Image:
    """
    Convert a base64 string to a PIL Image.
    """
    # Decode base64 to bytes
    image_data = base64.b64decode(image_base64)

    # Load image using PIL
    image = Image.open(BytesIO(image_data))
    return image


def pil_image_to_base64(image: Image.Image) -> str:
    """
    Convert a PIL Image to a base64 string.
    """
    with BytesIO() as bio:
        image.save(bio, format="PNG")
        image_data = bio.getvalue()
        image_base64 = base64.b64encode(image_data).decode("utf-8")
        return image_base64


def annotate_image(image_base64: str, position: tuple[int, int]) -> str:
    image_blob = pybase64.b64decode(str(image_base64), validate=True)
    image_np = np.frombuffer(image_blob, np.uint8)
    image_array = cv2.imdecode(image_np, 1)
    image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)

    x1 = int(position[0])
    y1 = int(position[1])
    # draw a red cross on the image
    image_array = cv2.drawMarker(image_array, (x1, y1), (255, 0, 0), markerType=cv2.MARKER_CROSS, markerSize=10, thickness=3)
    pimage = Image.fromarray(image_array)

    with io.BytesIO() as bio:
        pimage.save(bio, "png")
        image_data = bio.getvalue()
        image_string = pybase64.b64encode(image_data).decode("utf-8")
        return image_string


class GroundingOutput:
    def __init__(self, description: str, bbox: tuple[int, int, int, int], position: tuple[int, int], end_position: Optional[tuple[int, int]] = None):
        self.description = description
        self.bbox = bbox
        self.position = position
        self.end_position = end_position  # for drag action


class GroundingBaseModel(ABC):
    @abstractmethod
    async def predict(self, description: str, image_base64: str) -> GroundingOutput:
        pass
