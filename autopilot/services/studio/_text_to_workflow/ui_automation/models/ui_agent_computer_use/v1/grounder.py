from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.holo1 import Holo1Predictor
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.osatlas import OSAtlasPredictor
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import State
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uground import UGroundPredictor
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uitars import UITARSPredictor


class Grounder(object):
    def __init__(self, options: dict):
        self.options = options
        self.model_name = self.options["model"]["model_name"]
        if "os-atlas" in self.model_name.lower():
            self.predictor = OSAtlasPredictor(model_name=self.model_name, options=self.options["model"])
        elif "uground" in self.model_name.lower():
            self.predictor = UGroundPredictor(model_name=self.model_name, options=self.options["model"])
        elif "ui-tars" in self.model_name.lower():
            self.predictor = UITARSPredictor(model_name=self.model_name, options=self.options["model"])
        elif "holo1" in self.model_name.lower():
            self.predictor = Holo1Predictor(model_name=self.model_name, options=self.options["model"])
        else:
            raise ValueError(f"Unknown model name: {self.model_name}")

    async def predict(self, state: State, action_description: str) -> utils.GroundingOutput:
        ret = await self.predictor.predict(description=action_description, image_base64=state.image_base64)
        if ret is None:
            raise ValueError("Prediction returned None")
        console_logger.debug("Ground model response: %s", ret.__dict__)
        return ret
