from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client, utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import ExecutionState, State
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

system_template = """You are a computer use agent that review an action predicted by another agent.
You will be given a description of an action and a screenshot with potentially a red cross on the position of the mouse action.
The target element must be fully visible, corresponds to the action description, and the mouse position must be inside the element bounding box.
Your task is to review if the action is valid. Possible errors with corresponding error codes are:

element_not_visible: the action targets an element that is not fully visible on the screenshot. The element is either outside the screenshot or partially outside the screenshot. In this case a scroll action should be used first.
wrong_coordinates: mouse position coordinates marked by the red + is not inside the target element bounding box.
uncertain_element: the target element marked with a red + on the screenshot is not the correct element intended by the action description. This can happen if there are multiples elements like buttons or input boxes having similar labels. Return this error when you are uncertain. The target element must correspond exactly to the action description.


Return result a valid JSON format:
{{
    "status": true|false,  # true if the action is valid, false otherwise
    "code": "element_not_visible"|"wrong_coordinates"|"uncertain_element",  # Error code if the action is not valid
    "reason": "Reason why the action is not valid",  # Leave empty if the action is valid
}}
"""

user_message_template = """Here are the current information:
Action description: {action_description}

Review the action. The target element must be fully visible, corresponds to the action description, and the mouse position must be inside the element bounding box.
Answer with valid JSON format mentioned in the system message.
### AI Response:
"""


class ActionReviewer(object):
    def __init__(self, options: dict):
        self.options = options["uipath-computer-use"]

    def build_messages(self, state: State, action_description: str, position: tuple[int, int]) -> list[dict]:
        image_annotated = utils.annotate_image(state.image_base64, position)
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }
        contents = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_annotated}"},
            },
            {
                "type": "text",
                "text": user_message_template.format(
                    action_description=action_description,
                ),
            },
        ]

        user_message = {
            "role": "user",
            "content": contents,
        }
        messages = [system_message, user_message]
        return messages

    async def predict(self, state: State, action_description: str, position: tuple[int, int], execution_state: ExecutionState) -> dict:
        messages = self.build_messages(state, action_description, position)
        response_content = await llm_client.send_messages(
            messages,
            options={"model_name": execution_state.model_name},
            metadata={"session_id": execution_state.session_id, "request_id": execution_state.request_id, "generation_name": "action_review"},
            predict_info=execution_state.predict_info,
        )
        console_logger.debug("Action review: %s", response_content)
        if response_content is None:
            raise ValueError("Action review is None")
        response_json = parse_message_json(response_content)
        return response_json
