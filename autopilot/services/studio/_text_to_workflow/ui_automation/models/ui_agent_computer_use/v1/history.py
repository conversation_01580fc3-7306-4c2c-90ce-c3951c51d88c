from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger


def build_history_str(state, end_index: int = -1) -> str:
    """
    Build a string representation of the previous steps taken.
    """
    if end_index == -1:
        end_index = len(state.previous_steps)
    computer_use_steps = [ComputerUseStep.from_request_dict(request_step) for index, request_step in enumerate(state.previous_steps) if index < end_index]
    history_str = ""
    for i, step in enumerate(computer_use_steps):
        assert step.additional_parameters is not None, "Additional parameters must not be None"
        history_str += f" - Step {i + 1}: {step.additional_parameters['thought']}\n"
        history_str += f"     action: {step.description}\n"
        if "extracted_data" in step.additional_parameters:
            history_str += f"     extracted data: {step.additional_parameters['extracted_data']}\n"
        history_str += "\n"
    history_str = history_str.strip()
    console_logger.debug("History string: %s", history_str)
    return history_str
