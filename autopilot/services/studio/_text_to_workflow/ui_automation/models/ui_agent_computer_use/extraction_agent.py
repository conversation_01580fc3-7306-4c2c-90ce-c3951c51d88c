import datetime
import time
import copy
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Tuple

from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.llm_client import langfuse

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import TaskPlanAction
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.master_slave.planning_state import PlanningState
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

# flake8: noqa
system_template = """You are an UI automation agent expert that given a prompt and a screenshot extracts relevant information.

Your response should be in json format and should contain the following structure:
 - description. Short and concise overall description of the data (couple of words)
 - data as a key value pairs

Example of a valid JSON response:
{{
   "description": "Personal information",
   "data": 
            {{
                "FirstName": "John",
                "LastName": "Doe",
                "Email": "<EMAIL>"
            }}
}}

"""

user_template = """ 
Extraction task: "{task_description}"
Make sure your response is a valid json (double quotes, no comment, no trailing comma...)
Agent response in json format:
"""


class ExtractionAgent:
    def __init__(self, options):
        self.options = options

    def build_messages(self, request_body: dict):
        user_prompt = request_body["prompt_description"]
        image_base64 = request_body["image"]
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }
        contents = [
            {
                "type": "text",
                "text": user_template.format(
                    task_description=user_prompt,
                ),
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
            },
        ]
        user_message = {
            "role": "user",
            "content": contents,
        }
        messages = [system_message, user_message]
        return messages

    async def predict_request(self, request_body: dict):
        request_context = request_utils.get_request_context()
        session_id = request_context.ui_task_session_id if request_context else None
        if session_id is None:
            raise ValueError("Session ID is None.")

        return await self.predict(request_body, session_id)

    async def predict(self, request_body, session_id: str):
        start = time.time()
        messages = self.build_messages(request_body)
        metadata = {"session_id": session_id, "generation_name": "extraction"}

        response_content = await llm_client.send_messages(
            messages,
            options=self.options["planner"]["model"],
            metadata=metadata,
        )
        elapsed_time = time.time() - start
        console_logger.debug("Extraction elapsed time: %s", elapsed_time)
        console_logger.debug("Extraction response: %s", response_content)
        if response_content is None:
            raise ValueError("Extraction response is None")
        response_json = parse_message_json(str(response_content))
        return response_json
