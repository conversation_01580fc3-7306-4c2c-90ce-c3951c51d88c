import json
from typing import Any, Literal

from openai import AsyncOpenAI
from pydantic import BaseModel, ConfigDict

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.uitars import smart_resize


class FunctionDefinition(BaseModel):
    """Function definition data structure.

    Attributes:
        name: name of the function.
        description: description of the function.
        parameters: JSON schema for the function parameters.
        strict: Whether to enable strict schema adherence when generating the function call.
    """

    name: str
    description: str = ""
    parameters: dict[str, Any] = {}
    strict: bool = True


class ClickAction(BaseModel):
    """Click at specific coordinates on the screen."""

    model_config = ConfigDict(
        extra="forbid",
        json_schema_serialization_defaults_required=True,
        json_schema_mode_override="serialization",
        use_attribute_docstrings=True,
    )

    action: Literal["click"] = "click"
    x: int
    """The x coordinate, number of pixels from the left edge."""
    y: int
    """The y coordinate, number of pixels from the top edge."""


function_definition = FunctionDefinition(
    name="click_action",
    description=ClickAction.__doc__ or "",
    parameters=ClickAction.model_json_schema(),
    strict=True,
)


def get_localization_prompt_structured_output(image_base64, instruction: str) -> list[dict[str, Any]]:
    guidelines: str = "Localize an element on the GUI image according to my instructions and output a click position. You must output a valid JSON format."

    return [
        {
            "role": "system",
            "content": json.dumps([function_definition.model_dump()]),
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                },
                {"type": "text", "text": f"{guidelines}\n{instruction}"},
            ],
        },
    ]


class Holo1Predictor(utils.GroundingBaseModel):
    def __init__(self, model_name: str, options: dict):
        self.client = AsyncOpenAI(
            base_url=options["base_url"],
            api_key=options["api_key"],
        )
        self.model_name = model_name

    @staticmethod
    def parse_click_string(click_str: str) -> list[int]:
        # Expects format: Click(x, y)
        import re

        match = re.match(r"Click\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)", click_str.strip())
        if not match:
            raise ValueError(f"Invalid click string format: {click_str}")
        x, y = int(match.group(1)), int(match.group(2))
        return [x, y]

    async def predict(self, description: str, image_base64: str) -> utils.GroundingOutput:
        image = utils.image_base64_to_pil_image(image_base64)
        width, height = image.size
        resized_height, resized_width = smart_resize(height, width, min_pixels=4 * 28 * 28, max_pixels=1280 * 28 * 28)
        image = image.resize(size=(resized_width, resized_height), resample=None)  # type: ignore
        image_base64 = utils.pil_image_to_base64(image)

        messages = get_localization_prompt_structured_output(image_base64, description)
        completion = await self.client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            temperature=0,
        )
        response_content = completion.choices[0].message.content
        if response_content is None:
            raise ValueError("holo1 response is None")
        console_logger.debug("holo1: response %s", response_content)
        try:
            coordinates = ClickAction.model_validate(json.loads(response_content)["arguments"])
            position = [coordinates.x, coordinates.y]
            # position = self.parse_click_string(response_content)
        except Exception as e:
            console_logger.error("Failed to parse holo1 response: %s", e)
            raise

        # absolute_coords = get_absolute_coords(position, image_base64)
        x = int(round(position[0] * width / resized_width))
        y = int(round(position[1] * height / resized_height))
        # new_coordinate = (int(model_output_width / new_width * width), int(model_output_height / new_height * height))
        out = utils.GroundingOutput(
            description=response_content,
            bbox=None,
            position=(x, y),
            end_position=None,
        )
        return out
