from typing import Optional

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI()

# Allow requests from frontend dev server (typically http://localhost:3000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory log store
logs = []
last_id = 0


class LogEntry(BaseModel):
    model_thought: Optional[str] = None
    model_actions: Optional[str] = None
    screenshot: Optional[str] = None


@app.get("/logs")
def get_logs(since_id: int = 0):
    new_logs = [log for log in logs if log["id"] > since_id]
    total_count = len(logs)
    return {"logs": new_logs, "total_count": total_count}


@app.post("/add_log")
def add_log(entry: LogEntry):
    global last_id
    last_id += 1
    logs.append({"id": last_id, "model_thought": entry.model_thought, "model_actions": entry.model_actions, "screenshot": entry.screenshot})
    return {"status": "ok", "id": last_id}


@app.post("/reset")
def reset_logs():
    global logs, last_id, version_id
    logs = []
    last_id = 0
    return {"status": "cleared"}
