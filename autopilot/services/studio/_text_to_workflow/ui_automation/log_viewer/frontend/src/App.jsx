import React, { useEffect, useState, useRef } from 'react'

const API_BASE = 'http://localhost:8000' // Update if your FastAPI is on another URL

function App() {
  const [logs, setLogs] = useState([])
  const lastIdRef = useRef(0)

  useEffect(() => {
    // Fetch once now
    fetchNewLogs()
    // Then poll every 3s
    const interval = setInterval(fetchNewLogs, 500)
    return () => clearInterval(interval)
  }, [])

  function fetchNewLogs() {
    fetch(`${API_BASE}/logs?since_id=${lastIdRef.current}`)
      .then(res => res.json())
      .then(data => {
        const { logs: newLogs, total_count } = data

        // Case 1: Possibly an external reset
        // - If total_count is 0 and we currently have lastIdRef != 0,
        //   that means our logs were cleared externally.
        if (total_count === 0 && lastIdRef.current !== 0) {
          console.log('Detected external reset of logs')
          setLogs([])
          lastIdRef.current = 0
        }
        // Case 2: We have new logs
        else if (newLogs.length > 0) {
          // Append them
          setLogs(prev => {
            const updated = [...prev, ...newLogs]
            // Update our lastIdRef to the highest new log ID
            const maxNewId = newLogs.reduce((maxVal, log) => {
              return log.id > maxVal ? log.id : maxVal
            }, lastIdRef.current)
            lastIdRef.current = maxNewId
            return updated
          })
        }
        // Case 3: newLogs is empty, but total_count != 0
        // => no new logs, do nothing
      })
      .catch(err => console.error('Error fetching logs:', err))
  }
  
  function handleReset() {
    fetch(`${API_BASE}/reset`, { method: 'POST' })
      .then((res) => res.json())
      .then(() => {
        setLogs([])
        lastIdRef.current = 0
      })
      .catch((err) => console.error('Error resetting logs:', err))
  }

  return (
    <div style={{ padding: '1rem' }}>
      <button onClick={handleReset}>Reset</button>
      <div style={{ marginTop: '1rem' }}>
        {logs.map((log) => (
          <div
            key={log.id}
            style={{
              marginBottom: '1rem',
              paddingBottom: '1rem',
              borderBottom: '1px solid #ccc'
            }}
          >
            {/* 1. Show image first (full width) */}
            {log.screenshot && (
              <img
                src={`data:image/png;base64,${log.screenshot}`}
                alt="Screenshot"
                style={{
                  width: '100%',       // full width
                  marginBottom: '0.5rem'
                }}
              />
            )}

            {/* 2. Model Thought */}
            {log.model_thought && (
              <div style={{ marginBottom: '0.5rem' , whiteSpace: 'pre-wrap' }}>
                <strong>Thought:</strong> {log.model_thought}
              </div>
            )}

            {/* 3. Model Actions (different background color) */}
            {log.model_actions && (
              <div
                style={{
                  backgroundColor: '#f0f0f0',
                  padding: '0.5rem',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap'
                }}
              >
                <strong>Prediction:</strong> {log.model_actions}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default App
