# Author: <PERSON>, <PERSON>
import j<PERSON>
from pathlib import Path

import typing_extensions as t

from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


def get_typedef_json_file(converted_workflow_dir_path: Path):
    typedef_json_path = converted_workflow_dir_path / "Main.typedef.json"
    typedef_json = {}
    if typedef_json_path.exists():
        with open(typedef_json_path, "r", encoding="utf-8-sig") as f:
            typedef_json = json.load(f)
    return typedef_json


def load_metadata_file(metadata_path: Path) -> dict[Path, dict]:
    metadata = yaml_load(metadata_path) or {}
    metadata = {Path(item["id"]): item for item in metadata}
    return metadata


def load_subsets_file(subsets_path: Path) -> dict[str, list[str]]:
    subsets = yaml_load(subsets_path)
    return subsets


def load_subset(subset_path: Path) -> dict[Path, dict]:
    ds = {}
    for file_path in sorted(subset_path.rglob("*.yaml")):
        if file_path.is_file():
            ds[file_path] = yaml_load(file_path)
    return ds


def load_api_workflow_demonstrations(demonstrations_path: Path) -> dict[Path, dict]:
    ds = {}
    for demonstrations_directory in demonstrations_path.iterdir():
        for file_path in sorted(demonstrations_directory.rglob("*.json")) + sorted(demonstrations_directory.rglob("*.yaml")):
            if file_path.is_file():
                if file_path.suffix == ".json":
                    ds[file_path] = json.load(file_path.open("r"))
                else:
                    ds[file_path] = yaml_load(file_path)
    return ds


def load_dataset(dataset_path: Path, ignored_subsets: set | None = None) -> dict[str, dict[Path, dict]]:
    if ignored_subsets is None:
        ignored_subsets = set()
    dataset = {}
    for subset in dataset_path.iterdir():
        if not subset.is_dir():
            continue
        if subset.name.startswith(".errors"):
            continue
        if subset.name in ignored_subsets:
            continue
        dataset[subset.name] = load_subset(subset)
    return dataset


def load_workflow_generation_subsets_file(target_framework: TargetFramework) -> dict[str, list[str]]:
    subset_file_path = paths.get_workflow_generation_dataset_path(target_framework) / "subsets.yaml"
    return load_subsets_file(subset_file_path)


def load_workflow_generation_subset(target_framework: TargetFramework, subset: str) -> dict[Path, dict]:
    subset_path = paths.get_workflow_generation_dataset_path(target_framework, subset)
    return load_subset(subset_path)


def load_activity_retriever_subset(target_framework: TargetFramework, subset: str) -> dict[Path, dict]:
    subset_path = paths.get_wf_gen_activity_retriever_dataset_path(target_framework, subset)
    return load_subset(subset_path)


@t.overload
def load_workflow_generation_dataset(target_framework: TargetFramework, **kwargs) -> dict[str, dict[Path, dict]]: ...


@t.overload
def load_workflow_generation_dataset(target_framework: None, **kwargs) -> dict[str, dict[str, dict[Path, dict]]]: ...


def load_workflow_generation_dataset(
    target_framework: TargetFramework | None, **kwargs
) -> dict[str, dict[str, dict[Path, dict]]] | dict[str, dict[Path, dict]]:
    if target_framework is None:
        return {
            "Portable": load_dataset(paths.get_workflow_generation_dataset_path("Portable"), **kwargs),
            "Windows": load_dataset(paths.get_workflow_generation_dataset_path("Windows"), **kwargs),
        }
    return load_dataset(paths.get_workflow_generation_dataset_path(target_framework), **kwargs)


def load_sequence_generation_metadata_file(target_framework: TargetFramework) -> dict[str, list[str]]:
    metadata_file_path = paths.get_sequence_generation_dataset_path(target_framework) / "metadata.yaml"
    return yaml_load(metadata_file_path)


def load_sequence_generation_subsets_file(target_framework: TargetFramework) -> dict[str, list[str]]:
    subset_file_path = paths.get_sequence_generation_dataset_path(target_framework) / "subsets.yaml"
    return load_subsets_file(subset_file_path)


def load_sequence_generation_subset(target_framework: TargetFramework, subset: str) -> dict[Path, dict]:
    subset_path = paths.get_sequence_generation_dataset_path(target_framework, subset)
    return load_subset(subset_path)


def load_sequence_generation_dataset() -> dict[str, dict[str, dict[Path, dict]]]:
    return {
        "Portable": load_dataset(paths.get_sequence_generation_dataset_path("Portable")),
        "Windows": load_dataset(paths.get_sequence_generation_dataset_path("Windows")),
    }
