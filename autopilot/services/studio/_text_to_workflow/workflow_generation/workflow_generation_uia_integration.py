import asyncio
import uuid

from services.studio._text_to_workflow.common.constants import DELEGATE_HANDLER_KEY, DELEGATE_VARIABLES_KEY, SEQUENCE_ACTIVITY_NAME
from services.studio._text_to_workflow.ui_automation.ui_automation_endpoint import AUTOPILOT_TASK as UiAutomationTask
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema
from services.studio._text_to_workflow.workflow_generation.config import constants

ACTION_ACTIVITY_MAP = {
    "click": constants.UI_CLICK_ACTIVITY_NAME,
    "type_into": constants.UI_TYPEINTO_ACTIVITY_NAME,
    "get_text": constants.UI_GETTEXT_ACTIVITY_NAME,
    "select": constants.UI_SELECTITEM_ACTIVITY_NAME,
}

LOGGER = AppInsightsLogger()


def _build_sequence(prompt, activities):
    return {"thought": prompt, "activity": SEQUENCE_ACTIVITY_NAME, "params": {"Activities": activities}}


def _build_uia_activity(
    action: dict,
    activity_type: str,
    app_card_id: str,
    variables: list[workflow_generation_schema.Variable],
    variables_map: dict[str, str],
    expected_inputs: dict,
    query: str,
    allow_assign_activity_on_uia_expansion: bool,
) -> list[dict]:
    activities = []
    activity: dict = {
        "thought": action["description"].replace(" $", " ").replace("[[", "").replace("]]", ""),
        "activity": activity_type,
        "params": {"ScopeIdentifier": app_card_id, "Target": {"Reference": action["box_reference"]}},
    }

    variable_name = action["variable"][1:] if "variable" in action else None
    if variable_name is not None:
        activity["thought"] = activity["thought"].replace(action["variable"], variable_name)

    raw_default_value = (
        action["default_value"].strip("'") if "default_value" in action and action["default_value"] is not None and action["default_value"] != "" else ""
    )
    should_assign_default_value_for_sequence = raw_default_value != "" and raw_default_value in query

    if activity_type == constants.UI_TYPEINTO_ACTIVITY_NAME:
        _set_property_with_potential_default(
            property_name="Text",
            action=action,
            variables_map=variables_map,
            expected_inputs=expected_inputs,
            activities=activities,
            activity=activity,
            variable_name=variable_name,
            allow_assign_activity_on_uia_expansion=allow_assign_activity_on_uia_expansion,
            should_assign_default_value_for_sequence=should_assign_default_value_for_sequence,
        )
    elif activity_type == constants.UI_GETTEXT_ACTIVITY_NAME and variable_name is not None:
        if variable_name not in variables_map:
            variables.append({"name": variable_name, "type": "string"})

        activity["params"]["Text"] = "[[" + variable_name + "]]"
    elif activity_type == constants.UI_SELECTITEM_ACTIVITY_NAME:
        _set_property_with_potential_default(
            property_name="Item",
            action=action,
            variables_map=variables_map,
            expected_inputs=expected_inputs,
            activities=activities,
            activity=activity,
            variable_name=variable_name,
            allow_assign_activity_on_uia_expansion=allow_assign_activity_on_uia_expansion,
            should_assign_default_value_for_sequence=should_assign_default_value_for_sequence,
        )

    activities.append(activity)
    return activities


def _set_property_with_potential_default(
    property_name: str,
    action: dict,
    variables_map: dict[str, str],
    expected_inputs: dict,
    activities: list[dict],
    activity: dict,
    variable_name: str | None,
    allow_assign_activity_on_uia_expansion: bool,
    should_assign_default_value_for_sequence: bool,
):
    if should_assign_default_value_for_sequence:
        activity["params"][property_name] = _format_default_value(action["default_value"])
        return

    if (
        allow_assign_activity_on_uia_expansion
        and variable_name is not None
        and variable_name not in variables_map
        and action["default_value"] not in [None, ""]
        and variable_name not in expected_inputs
    ):
        _add_assign_activity(action, activities, variable_name)

    if variable_name is not None:
        if variable_name.startswith("[[") and variable_name.endswith("]]"):
            out_var = variable_name.replace("&quot;", '"')
            if not out_var.endswith(".ToString()]]"):
                out_var = out_var[:-2] + ".ToString()]]"
            activity["params"][property_name] = out_var
        else:
            activity["params"][property_name] = "[[" + variable_name + "]]"


def _add_assign_activity(action: dict, activities: list[dict], variable_name: str):
    assign_title = "Set " + variable_name.replace("[[", "").replace("]]", "")
    activities.append(
        {
            "thought": assign_title,
            "activity": constants.ASSIGN_ACTIVITY_NAME + "<string>",
            "params": {
                "To": "[[" + variable_name + "]]",
                "Value": _format_default_value(action["default_value"]),
            },
        }
    )


def _format_default_value(default_value: str):
    # Wrap everything as a string because the expected value is always a string and the model never returns anything else (e.g. variables) as defaults.
    if default_value.startswith("[[") and default_value.endswith("]]"):
        return default_value

    return '[["' + default_value.strip("'\"").replace('"', '\\"') + '"]]'


def _build_uia_scope(prompt, reference, idx) -> tuple[dict, str, list[dict]]:
    app_card_id = str(uuid.uuid4())
    app_card_body = []
    app_card = {
        "thought": prompt.replace("[[", "").replace("]]", ""),
        "activity": constants.UI_APPLICATION_CARD_ACTIVITY_NAME,
        "params": {
            "ScopeGuid": app_card_id,
            "Version": "V2",
            "AttachMode": "ByInstance",
            "CloseMode": "Never",
            "Body": {DELEGATE_VARIABLES_KEY: [{"name": "WSSessionData", "type": "System.Object"}], DELEGATE_HANDLER_KEY: app_card_body},
            "TargetApp": {"Reference": reference},
        },
    }

    if idx != 0:
        app_card["params"]["OpenMode"] = "Never"

    return app_card, app_card_id, app_card_body


def _build_element_to_screen_map(objects) -> dict[str, dict]:
    element_to_screen_map = {}
    for obj in objects:
        if obj["type"] == "App" and "children" in obj and obj["children"] is not None:
            for screen in obj["children"]:
                if screen["type"] == "Screen" and "children" in screen and screen["children"] is not None:
                    _map_elements_to_screens(screen["children"], screen, element_to_screen_map)

    return element_to_screen_map


async def _expand_uia_scope(
    uia_scope: dict,
    variables: list[workflow_generation_schema.Variable],
    objects: list[workflow_generation_schema.UIObject],
    query: str,
    idx: int,
    allow_assign_activity_on_uia_expansion: bool,
):
    uia_node = uia_scope["node"]

    if "params" not in uia_node or "AutoGenerationOptions" not in uia_node["params"]:
        return uia_node

    auto_generation_opts = uia_node["params"]["AutoGenerationOptions"]

    if "Prompt" not in auto_generation_opts:
        return uia_node

    expected_inputs, expected_outputs = {}, {}

    if "ExpectedInputs" in auto_generation_opts:
        for input_var in auto_generation_opts["ExpectedInputs"]:
            expected_inputs[input_var] = "str"

    if "ExpectedOutputs" in auto_generation_opts:
        for output_var in auto_generation_opts["ExpectedOutputs"]:
            expected_outputs[output_var] = "str"

    variables_map = {item["name"]: _map_csharp_type_to_python(item["type"]) for item in variables}

    prompt = auto_generation_opts["Prompt"]
    request = {
        "userTask": prompt,
        "title": prompt,
        "DOMType": "ObjectRepository",
        "variables": variables_map,
        "input_variables": expected_inputs,
        "output_variables": expected_outputs,
        "rawDOM": {"children": objects},
    }

    element_to_screen_map = _build_element_to_screen_map(objects)

    response, _ = await UiAutomationTask.predict(request)

    activities = []
    app_card_reference, app_card, app_card_id, app_card_body = None, None, None, None

    if "step" not in response:
        return uia_scope["node"]
    step = response["step"]

    for step_idx, action in enumerate(step["actions"]):
        if action["method_type"] == "finish":
            break

        # Get the screen for the current element
        current_app_card_reference = element_to_screen_map.get(action["box_reference"], None)
        if current_app_card_reference is None:
            print(f"WARN: Could not find screen for element {action['box_reference']}")
            continue

        # The screen changed, so we need to create a new app card
        if app_card_reference != current_app_card_reference:
            app_card_reference = current_app_card_reference

            app_card, app_card_id, app_card_body = _build_uia_scope(prompt, app_card_reference["reference"], idx + step_idx)
            activities.append(app_card)

        # Add the action to the app card
        activity_type = ACTION_ACTIVITY_MAP.get(action["method_type"], None)
        if activity_type is not None and app_card_id is not None and app_card_body is not None:
            app_card_body.extend(
                _build_uia_activity(
                    action=action,
                    activity_type=activity_type,
                    app_card_id=app_card_id,
                    variables=variables,
                    variables_map=variables_map,
                    expected_inputs=expected_inputs,
                    query=query,
                    allow_assign_activity_on_uia_expansion=allow_assign_activity_on_uia_expansion,
                )
            )  # type: ignore

    if len(activities) == 0:
        return uia_scope["node"]  # return the original scope if we didn't manage to generate any activities

    if len(activities) == 1:
        return activities[0]  # return the single app card if we only have one

    return _build_sequence(prompt, activities)


async def expand_uia_scopes(
    variables: list[workflow_generation_schema.Variable],
    objects: list[workflow_generation_schema.UIObject],
    uia_scopes: list[dict],
    query: str,
    allow_assign_activity_on_uia_expansion: bool,
):
    try:
        # Get all NApplicationCard activities, expand each scope with the prompt, fix them up acording to their screen and add them to the workflow
        uia_results_and_errors = await asyncio.gather(
            *(
                _expand_uia_scope(
                    uia_scope=uia_scope,
                    variables=variables,
                    objects=objects,
                    query=query,
                    idx=idx,
                    allow_assign_activity_on_uia_expansion=allow_assign_activity_on_uia_expansion,
                )
                for idx, uia_scope in enumerate(uia_scopes)
            ),
            return_exceptions=True,
        )
        uia_results = []
        for result in uia_results_and_errors:
            if isinstance(result, Exception):
                LOGGER.exception(f"Error while expanding UIA scopes: {result}, {result.__traceback__}")
            else:
                uia_results.append(result)

        for i in range(0, len(uia_results)):
            uia_scope = uia_scopes[i]
            uia_result = uia_results[i]

            parent = uia_scope["parent"]

            # if parent is not a list - eg: "If" activity, we try to find the handler which contains the uia_scope
            if isinstance(parent, dict):
                for key in ["Body", "Handler", "Then", "Else"]:
                    if key in parent and isinstance(parent[key], list):
                        parent = parent[key]
                        break

            if isinstance(parent, list):
                # Get index of uia_scope["node"] in parent
                uia_scope_idx = parent.index(uia_scope["node"])
                parent.insert(uia_scope_idx, uia_result)
                parent.remove(uia_scope["node"])

    except Exception as e:
        LOGGER.exception(f"Error while expanding UIA scopes: {e}")


def _map_elements_to_screens(objects, screen, element_to_screen_map):
    for obj in objects:
        if obj["type"] == "Element":
            element_to_screen_map[obj["reference"]] = screen
        if "children" in obj and obj["children"] is not None:
            _map_elements_to_screens(obj["children"], screen, element_to_screen_map)


def _map_csharp_type_to_python(csharp_type):
    """
    Maps a given C# type or its fully qualified name to its Python equivalent.

    Args:
    csharp_type (str): The C# type to be mapped.

    Returns:
    str: The corresponding Python type.
    """
    base_type_mapping = {
        # Shorthand types
        "int": "int",
        "long": "int",
        "float": "float",
        "double": "float",
        "bool": "bool",
        "char": "str",
        "string": "str",
        "byte": "int",
        "sbyte": "int",
        "short": "int",
        "ushort": "int",
        "uint": "int",
        "ulong": "int",
        "decimal": "float",
        # Fully qualified types
        "system.int32": "int",
        "system.int64": "int",
        "system.single": "float",
        "system.double": "float",
        "system.boolean": "bool",
        "system.char": "str",
        "system.string": "str",
        "system.byte": "int",
        "system.sbyte": "int",
        "system.int16": "int",
        "system.uint16": "int",
        "system.uint32": "int",
        "system.uint64": "int",
        "system.decimal": "float",
    }

    # Normalize input to handle variations in case
    normalized_type = csharp_type.lower()

    normalized_type = normalized_type.replace("system.collections.generic.list", "list")
    normalized_type = normalized_type.replace("system.collections.generic.ilist", "list")
    normalized_type = normalized_type.replace("system.collections.ienumerable", "list")

    # Check for array or List<T> patterns
    if normalized_type.endswith("[]"):
        element_type = normalized_type[:-2]  # Extract type of the elements
        python_element_type = base_type_mapping.get(element_type, "Any")
        return f"list[{python_element_type}]"
    elif normalized_type.startswith("list<") and normalized_type.endswith(">"):
        element_type = normalized_type[5:-1]  # Extract type of the elements
        python_element_type = base_type_mapping.get(element_type, "Any")
        return f"list[{python_element_type}]"
    else:
        return base_type_mapping.get(normalized_type, "NoneType")
