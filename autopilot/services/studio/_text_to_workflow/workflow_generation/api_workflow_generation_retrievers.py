import json
import pathlib as pt

import numpy as np

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ActivityType
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset
from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import DemonstrationsIndexBase, DemonstrationsRetrieverBase


class APIWorkflowDemonstrationsRetriever(DemonstrationsRetrieverBase):
    def __init__(self, config_name: str, dataset_path: pt.Path, retriever_path: pt.Path, activities_retriever: APIActivitiesRetriever) -> None:
        super().__init__(config_name, dataset_path, retriever_path)

        self.index = {(dataset_path.name): APIWorkflowDemonstrationsIndex(self.config, dataset_path, retriever_path, activities_retriever)}

    def get_demonstration_description(self, demo: dict):
        return demo.get("query", None)

    def get_relevant(
        self,
        query,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        ignored_namespaces: set[str] = set(),
        ignored_identifiers: set[str] = set(),
        ignored_activities: set[str] = set(),
        verbose: bool = False,
    ) -> dict[str, list[dict]]:
        config = self.config["demonstration_ranking"]
        demonstrations = {}
        for index in self.index:
            demos = self.apply_reranking_and_mmr_transformations_on_demonstrations_similarities(
                config, self.index[index], query, query_embedding, connections_embedding, ignored_namespaces, ignored_identifiers, ignored_activities, verbose
            )
            demonstrations[index] = demos

        # ensure ordering
        demonstrations = {
            subset: sorted(demos, key=lambda demo: demo["similarity"], reverse=config["sorting_order"] == "descending")
            for subset, demos in demonstrations.items()
        }

        return demonstrations

    @property
    def is_loaded(self) -> bool:
        return True


class APIWorkflowDemonstrationsIndex(DemonstrationsIndexBase):
    activities_retriever: APIActivitiesRetriever

    def __init__(self, config: dict, dataset_path: pt.Path, retriever_path: pt.Path, activities_retriever: APIActivitiesRetriever) -> None:
        self.identifier = dataset_path.name
        state_path = retriever_path / f"state.{self.identifier}.pkl"
        self.activities_retriever = activities_retriever
        super().__init__(config, self.identifier, dataset_path, retriever_path, state_path)

    def get_item_description(self, item, filepath):
        return item.get("query", json.dumps(item.get("input")))

    def get_used_activities_from_item(self, item):
        return item.get("used_activities", []) + item.get("used_triggers", [])

    def should_skip_item(self, filepath, item) -> bool:
        # TO DO: add logic to skip items that are not relevant to the query
        return False

    def activity_exists_in_retriever(self, activity_name: str, activity_type: ActivityType) -> tuple[bool, bool]:
        return self.activities_retriever.activity_exists(activity_name, activity_type)

    def gather_activities(self, workflow, strip_templates=False):
        # api workflow retriever does not support gathering iou_activities
        raise NotImplementedError("gather iou_activities is not implemented for APIWorkflowDemonstrationsIndex")

    def get_by_index(self, index: int) -> dict:
        with self.store.state["index2filepath"][index].open("r") as f:
            if f.name.endswith(".json"):
                return json.load(f)
            else:
                return yaml_load(f)

    def get_identifier(self, path: pt.Path) -> str:
        parts = path.relative_to(paths.get_autopilot_samples_dataset_path()).parts
        name = parts[-1].removesuffix(".json")
        return f"{parts[-2]}.{name}"

    def build(self) -> tuple[dict, dict]:
        texts, filepath2index, index2filepath = [], {}, {}
        workflows, plans = [], []
        retrieved_activity_names: list[list[str]] = []

        items = workflow_generation_dataset.load_api_workflow_demonstrations(self.dataset_path).items()

        if "output" in list(list(items)[0][1].keys()):
            items = [(v[0], v[1]["input"]) for v in items]

        self._process_items(items, texts, filepath2index, index2filepath, workflows, plans, retrieved_activity_names, self.dataset_path)
        return self.build_state(texts, workflows, plans, retrieved_activity_names, filepath2index, index2filepath)

    def get_activity_info(self, activity_id: str, activity_type: ActivityType) -> ActivityDefinition | None:
        return self.activities_retriever.get_activity_info(activity_id, activity_type)

    def _get_relevant_activities(self, used_activities: list[str]) -> list[str]:
        return [activity for activity in used_activities if activity.startswith(tuple(constants.API_ACTIVITY_NAMES))]
