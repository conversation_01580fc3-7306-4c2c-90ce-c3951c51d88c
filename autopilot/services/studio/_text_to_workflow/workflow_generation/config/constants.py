# Prompting

from typing import Literal, TypeAlias

DYNAMIC_ACTIVITY_DETAILS_DEFAULT = "NotConfigured"

CONNECTIONS_PREFER_MESSAGE = "If you have to choose between 2 activities of the same domain, prefer activities where the field HasConnection=true. E.g. for 2 activities to send email, prefer the one where HasConnection=true."  # fmt: skip

TESTCASE_PLANNING_ADDITIONAL_INSTRUCTIONS = """- When you are asked to 'Verify' or 'Check' something that does not imply taking an action afterwards (and hence is NOT an If conditional), you should write 'Verify that' in the plan.
Additionaly, if the verification step refers to something on the screen (e.g. some text, a value, a message), you must add a Get step before the Verify step.
- IMPORTANT: Given a user request like "In SomePage, fill in all the details and submit." or "In SomeOtherPage, type the company name and click on this button.", you should write this as a single step in the plan.
- When it looks like the user query or part of the user query requires UI interaction, follow these rules:
  - A plan step cannot just be "Attach to screen" or "Open application" followed by e.g. "Click on ..." or "Get Text ..." or "Type ...", but must rather be like "Open ... and click ..." or "Attach to ... and get ...".
  - Similarly, a plan step cannot be "Open '...' and navigate to '...' screen". It must be rather "Open '...', navigate to '...' screen and <some UI action>".
- Make sure to specify expected inputs and outputs for UI automation steps."""  # noqa

TESTCASE_GENERATION_ADDITIONAL_INSTRUCTIONS = """# Important Instructions
When choosing the activity for a plan step where you have to Get something which may be related to a previous step for which you used UiPath.UIAutomationNext.Activities.NApplicationCard, you must also use UiPath.UIAutomationNext.Activities.NApplicationCard.
    Example:
    Plan cutout:
    ```
    5. Fill in the flight search form
    6. Get the first price
    ```
    In the plan step 6, you should use UiPath.UIAutomationNext.Activities.NApplicationCard with ExpectedOutputs to get the first price because it is related to the previous step where you filled in the flight search form using UiPath.UIAutomationNext.Activities.NApplicationCard and flights normally have prices.
When setting the ExpectedInputs for a UI Automation step:
    If the plan step mentions a specific value for example a number, an email or some text, you should use that value in the ExpectedInputs but make sure to pass it within double quotes, e.g. - '"<EMAIL>"'.
    If the plan step mentions a variable, you should use the variable name in the ExpectedInputs without double quotes, e.g. - myVariableName.
When setting the ExpectedOutputs for a UI Automation step you can only use variables names there, which you must pass without double quotes, e.g. - myVariableName. If the plan step involves getting a value from the UI, you must add a variable for it to the ExpectedOutputs.
If some of the steps imply UI Automation, you must chain the UiPath.UIAutomationNext.Activities.NApplicationCard activities through ExpectedInputs and ExpectedOutputs which can take variables or constant strings as input and can output UI text as variables.
If you need to parse or format the expected input before passing it to the UI step, add an Assign activity before-hand.
"""  # noqa

# Activities

UI_APPLICATION_CARD_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NApplicationCard"
UI_CLICK_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NClick"
UI_TYPEINTO_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NTypeInto"
UI_GETTEXT_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NGetText"
UI_SELECTITEM_ACTIVITY_NAME = "UiPath.UIAutomationNext.Activities.NSelectItem"
UI_PACKAGE_ACTIVITIES_ALLOWLIST = {
    UI_APPLICATION_CARD_ACTIVITY_NAME,
    "UiPath.Core.Activities.LoadImage",
    "UiPath.Core.Activities.SaveImage",
    "UiPath.Core.Activities.SAP.CallTransaction",
    "UiPath.Core.Activities.SAP.Login",
    "UiPath.Core.Activities.SAP.Logon",
    "UiPath.Core.Activities.SAP.ReadStatusbar",
}
ASSIGN_ACTIVITY_NAME = "UiPath.Core.Activities.Assign"

PREFERRED_NAMESPACES: tuple[str, ...] = ("UiPath.Core.Activities", "System.Activities.Statements")

ExcelPackageOption: TypeAlias = Literal["ExcelOnline", "ExcelDesktopBusiness", "ExcelDesktopLegacy"]
MailPackageOption: TypeAlias = Literal["MailOnline", "MailDesktopBusiness", "MailDesktopLegacy"]

EXCLUSIVE_NAMESPACES: dict[str | ExcelPackageOption | MailPackageOption, set[str]] = {
    "ExcelOnline": {"UiPath.MicrosoftOffice365.Activities.Excel", "UiPath.MicrosoftOffice365.Activities.Excel.Triggers"},
    "ExcelDesktopBusiness": {"UiPath.Excel.Activities.Business", "UiPath.Excel.Activities.Business.ChartModifications"},
    "ExcelDesktopLegacy": {"UiPath.Excel.Activities", "UiPath.CSV.Activities"},
    "MailOnline": {"UiPath.MicrosoftOffice365.Activities.Mail", "UiPath.MicrosoftOffice365.Activities.Mail.Triggers"},
    "MailDesktopBusiness": {"UiPath.Mail.Activities.Business"},
    "MailDesktopLegacy": {
        "UiPath.Mail.Activities",
        "UiPath.Mail.Activities.Outlook",
        "UiPath.Mail.Outlook.Activities",
        "UiPath.Mail.Exchange.Activities",
        "UiPath.Mail.IMAP.Activities",
        "UiPath.Mail.LotusNotes.Activities",
        "UiPath.Mail.POP3.Activities",
        "UiPath.Mail.SMTP.Activities",
    },
}

FORCED_MAIL_KEYWORDS: dict[str, MailPackageOption] = {
    "365": "MailOnline",
    "outlook": "MailDesktopLegacy",
    "exchange": "MailDesktopLegacy",
    "imap": "MailDesktopLegacy",
    "lotus": "MailDesktopLegacy",
    "pop3": "MailDesktopLegacy",
    "smtp": "MailDesktopLegacy",
    "gmail": "MailOnline",
    "gsuite": "MailOnline",
}

FORCED_EXCEL_KEYWORDS: dict[str, ExcelPackageOption] = {
    "excel online": "ExcelOnline",
    "studiox": "ExcelDesktopBusiness",
    "excel modern": "ExcelDesktopBusiness",
    "modern excel": "ExcelDesktopBusiness",
}

FORCED_EXCEL_KEYWORD_GROUPS: dict[tuple[str, str], ExcelPackageOption] = {
    ("excel", "365"): "ExcelOnline",
    ("excel", "desktop"): "ExcelDesktopLegacy",
    ("excel", ":\\"): "ExcelDesktopLegacy",  # Should match absolute Windows paths
    ("excel", "onedrive"): "ExcelOnline",
    ("excel", "gsuite"): "ExcelOnline",
    ("excel", "google sheets"): "ExcelOnline",
}


EXCLUSIVE_DESKTOP_PACKAGES = {
    "Excel": "UiPath.Excel.Activities",
    "Mail": "UiPath.Mail.Activities",
}
