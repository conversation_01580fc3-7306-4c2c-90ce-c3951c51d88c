custom_patch_instructions: |-
  ## Custom patch format

  You should generate search and replace hunks / blocks in a custom patch.
  A patch can contain multiple hunks.
  Each hunk should be enclosed: starting with `<<<<<<< SEARCH` and ending with `>>>>>>> REPLACE`.
  Inside a hunk you should provide 2 sections, separated by `=======`.
  The first SEARCH section contains the original lines from the workflow with prepended line numbers.
  The second REPLACE section contains the replacement lines without any prepended line numbers.
  This approach is similar to git merge conflict style hunks.

  This format looks like:
  ```
  <<<<<<< SEARCH
    1|<original_line_1>
    2|<original_line_2>
  ...
    n|<original_line_n>
  =======
  <replacement_line_1>
  <replacement_line_2>
  ...
  <replacement_line_m>
  >>>>>>> REPLACE
  ```
  A patch can contain multiple such search and replace hunks fenced under the same patch.

  For each hunk / block of changes:
  1. For the first SEARCH section (with original lines):
    a. Preserve the exact indentation and formatting of the original lines
    b. Use the line numbers from the user provided workflow yaml
    c. Include only the section that you intend to change, not the entire workflow.
    d. You can optionally use a line with "..." to skip middle lines if the SEARCH section is too long (3 lines or more). Keep at least one line before and after the "...". Use only one "...".

  2. For the second REPLACE section (with replacement lines):
    a. The replacement lines should be complete and after applying the patch, the result should be valid YAML
    b. DO NOT use line numbers here
    c. Beware that all provided original lines will be replaced with these replacement lines.

  Note that all the hunks in a patch will be applied simultaneously, so provide non-overlapping hunks (search sections should not overlap).
  Optimize for brevity in the search section. Remember: use ellipsis "..." if the search section is too long. (more than 3 lines)

  ### Examples
  Source user provided workflow yaml:
  ```
    1|root:
    2|  elements:
    3|  - inner_key:
    4|      inner_value: 1
    5|      inner_string: Hello
  ```

  Assume the solution workflow yaml should be like this (after applying the patch):
  ```
  root:
    elements:
    - inner_altered_key:
        inner_value: 3
        inner_string: Hello
  ```

  The custom patch needs only one hunk in this case:
  ```
  <<<<<<< SEARCH
    3|  - inner_key:
    4|      inner_value: 1
  =======
    - inner_altered_key
        inner_value: 3
  >>>>>>> REPLACE
  ```

  For the sake of example, we could have done this with multiple hunks, like so.
  ```
  <<<<<<< SEARCH
    3|  - inner_key:
  =======
    - inner_altered_key
  >>>>>>> REPLACE
  <<<<<<< SEARCH
    4|      inner_value: 1
  =======
        inner_value: 3
  >>>>>>> REPLACE
  ```
  Do this only for separate hunks that are not immediately adjacent to each other.

  Here's a different example that uses "..." to skip middle lines. In this particular case, the entire workflow is replaced.
  ```
  <<<<<<< SEARCH
    1|root:
  ...
    5|      inner_string: Hello
  =======
  root:
    elements: []
  >>>>>>> REPLACE
  ```
