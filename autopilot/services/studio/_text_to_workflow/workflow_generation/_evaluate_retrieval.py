import pathlib

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.embeddingsdb import create_dynamic_activity_full_id
from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset, workflow_generation_task


def dataset_path(name: str = "train") -> pathlib.Path:
    return pathlib.Path(__file__).parent / name


def get_sample_plan(sample: dict) -> str:
    return sample["plan"]


def get_activity_full_id(node):
    activity_name, _ = extract_generic_pattern(node["activity"])
    if activity_name in constants.DAP_ACTIVITY_NAMES:
        if "params" in node and "UiPathActivityTypeId" in node["params"]:
            return create_dynamic_activity_full_id(activity_name, node["params"]["UiPathActivityTypeId"])
        elif "uiPathActivityTypeId" in node:
            return create_dynamic_activity_full_id(activity_name, node["uiPathActivityTypeId"])
    return activity_name


def get_sample_trigger(sample: dict) -> str:
    node = sample["process"]["trigger"]
    return get_activity_full_id(node)


def get_sample_activities(sample: dict, mode: str = "workflow") -> list[str]:
    activities = []
    root_activities = sample["process"]["workflow"] if mode == "workflow" else sample["process_sequence"]
    _get_node_activities(root_activities, activities)
    return activities


def _get_node_activities(node: list | dict, activities: list[str]) -> None:
    if isinstance(node, list):
        for child in node:
            _get_node_activities(child, activities)
    elif isinstance(node, dict):
        if "activity" in node:
            activities.append(get_activity_full_id(node))
        for value in node.values():
            if isinstance(value, list) or isinstance(value, dict):
                _get_node_activities(value, activities)


def recall(expected: list[str], retrieved: list[str]) -> float:
    expected_set = set(expected)
    retrieved_set = set(retrieved)
    return len(expected_set.intersection(retrieved_set)) / len(expected_set)


def iou(expected: list[str], retrieved: list[str]) -> float:
    expected_set = set(expected)
    retrieved_set = set(retrieved)
    return len(expected_set.intersection(retrieved_set)) / len(expected_set.union(retrieved_set))


def evaluate_retrieval(components: str, tag: str):
    workflow_generation = workflow_generation_task.WorkflowGenerationTask("prompt.yaml").load()
    testset = workflow_generation_dataset.load_workflow_generation_subset("test")

    trigger_recall_overall, trigger_iou_overall = 0, 0
    activities_recall_overall, activities_iou_overall = 0, 0
    total = 0
    for sample in testset.values():
        sample_tag = sample["name"].split("[", 1)[1].split("]", 1)[0]
        if tag != "all" and tag != sample_tag:
            continue
        total += 1
        print("=====================================")
        print(f"{sample['name']}: {sample['description']}")
        # get expected and retrieved triggers and activities
        expected_plan = get_sample_plan(sample)
        expected_trigger = get_sample_trigger(sample)
        expected_activities = get_sample_activities(sample)
        result = workflow_generation.run(
            sample["description"],
            [],
            run_planning=True,
            run_generation=False,
        )
        generated_plan = result["plan"]
        retrieved_triggers = [trigger["name"] for trigger in result["retrieved_triggers"]]
        retrieved_activities = [activity["name"] for activity in result["retrieved_activities"]]
        # show plan
        print(f"Expected plan:\n{expected_plan}")
        print(f"Generated plan:\n{generated_plan}")
        # calcuate recall and iou
        trigger_recall = recall([expected_trigger], retrieved_triggers)
        trigger_iou = iou([expected_trigger], retrieved_triggers)
        activities_recall = recall(expected_activities, retrieved_activities)
        activities_iou = iou(expected_activities, retrieved_activities)
        # update overall recall and iou
        trigger_recall_overall += trigger_recall
        trigger_iou_overall += trigger_iou
        activities_recall_overall += activities_recall
        activities_iou_overall += activities_iou
        # print results
        missed_triggers = list(set([expected_trigger]).difference(retrieved_triggers))
        missed_activities = list(set(expected_activities).difference(retrieved_activities))
        if components in ["trigger", "all"]:
            print(f"Trigger Recall/IoU: {trigger_recall}/{trigger_iou}")
            print(f"Expected trigger:\n{expected_trigger}")
            print(f"Retrieved triggers:\n{yaml_dump(retrieved_triggers)}")
            print(f"Mised trigger:\n{yaml_dump(missed_triggers)}")
        if components in ["activities", "all"]:
            print(f"Activities Recall/IoU: {activities_recall}/{activities_iou}")
            print(f"Expected activities:\n{yaml_dump(expected_activities)}")
            print(f"Retrieved activities:\n{yaml_dump(retrieved_activities)}")
            print(f"Mised activities:\n{yaml_dump(missed_activities)}")
    # print overall recall and iou
    print("=====================================")
    if components in ["trigger", "all"]:
        print(f"Overall Trigger Recall/IoU: {trigger_recall_overall / total}/{trigger_iou_overall / total}")
    if components in ["activities", "all"]:
        print(f"Activities Recall/IoU: {activities_recall_overall / total}/{activities_iou_overall / total}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Evaluate retrieval performance.")
    parser.add_argument("--components", choices=["trigger", "activities", "all"])
    parser.add_argument("--tag", default="all")

    args = parser.parse_args()
    evaluate_retrieval(args.components, args.tag)
