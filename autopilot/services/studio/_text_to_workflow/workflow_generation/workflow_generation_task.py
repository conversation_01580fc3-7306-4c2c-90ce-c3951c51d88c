import copy
import pathlib

import langchain.cache
import langchain.chains
import langchain.chat_models
import langchain.embeddings
import langchain.globals
import langchain.prompts
import langchain.schema
import langchain_community
import langdetect
import numpy as np
from langchain_core.language_models import BaseChatModel

from services.studio._text_to_workflow.common.activity_retriever import <PERSON>R<PERSON>riever
from services.studio._text_to_workflow.common.constants import SEQUENCE_GENERATION_INSERTION_PHRASE
from services.studio._text_to_workflow.common.exceptions import ParsingException
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    Connection,
    SubsetName,
    TargetFramework,
    UIObject,
    Variable,
)
from services.studio._text_to_workflow.common.state_store import clear_retrievers_cache
from services.studio._text_to_workflow.common.walkers import PlanBuilder, SequenceGenerationCurrentActivityReplacer
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import Model<PERSON>anager
from services.studio._text_to_workflow.utils import errors, paths
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time
from services.studio._text_to_workflow.utils.translate.translate_text_schema import TextTranslationRequest
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask
from services.studio._text_to_workflow.utils.workflow_utils import load_workflow_instance
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_retrievers, workflow_generation_schema
from services.studio._text_to_workflow.workflow_generation.config import constants
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import get_ignored_namespaces
from services.studio._text_to_workflow.workflow_generation.services.helpers.sequence_generation_helper import get_plan_whole
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent

TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")
LOGGER = AppInsightsLogger()

Supported_Excel_Packages: list[constants.ExcelPackageOption] = ["ExcelOnline", "ExcelDesktopBusiness", "ExcelDesktopLegacy"]
Supported_Mail_Packages: list[constants.MailPackageOption] = ["MailOnline", "MailDesktopBusiness", "MailDesktopLegacy"]

# TODO: Max tokens should be inferred dynamically based on the query,
# along with the max tokens
MAX_MODEL_TOKENS_THRESHOLD = 100


@log_execution_time("workflow_generation_build")
def run_workflow_generation_build():
    clear_retrievers_cache()
    WorkflowGenerationTask("prompt.yaml")


class WorkflowGenerationTask:
    prompt_builder_component: WorkflowGenerationPromptBuilderComponent
    postprocess_component: WorkflowGenerationPostProcessComponent
    workflow_generation_plan_service: WorkflowGenerationActivityRetrievalService

    def __init__(self, config_name: str) -> None:
        self.config_dir = (pathlib.Path(__file__).parent / "config").absolute()
        self.config_path = self.config_dir / config_name
        self.config = yaml_load(self.config_path)

        self.workflow_dataset_path = paths.get_workflow_generation_dataset_path()
        self.workflow_retriever_path = paths.get_workflow_generation_retriever_path()
        self.sequence_dataset_path = paths.get_sequence_generation_dataset_path()
        self.sequence_retriever_path = paths.get_sequence_generation_retriever_path()
        self.activities_retriever = ActivitiesRetriever()
        self.connection_embeddings_retriever = ConnectionEmbeddingsRetriever(
            self.activities_retriever, ModelManager().get_embeddings_model("activities_embedding_model")
        )

        self.workflow_demo_retriever = workflow_generation_retrievers.DemonstrationsRetriever(
            config_name,
            self.workflow_dataset_path,
            self.workflow_retriever_path,
            self.activities_retriever,
        )
        self.sequence_demo_retriever = workflow_generation_retrievers.DemonstrationsRetriever(
            config_name,
            self.sequence_dataset_path,
            self.sequence_retriever_path,
            self.activities_retriever,
        )

    def load(self) -> "WorkflowGenerationTask":
        self.prompt_builder_component = WorkflowGenerationPromptBuilderComponent(self.activities_retriever)

        self.workflow_generation_plan_service = WorkflowGenerationActivityRetrievalService(
            self.activities_retriever, self.connection_embeddings_retriever, self.prompt_builder_component
        )

        dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(self.activities_retriever)
        self.postprocess_component = WorkflowGenerationPostProcessComponent(
            self.activities_retriever, dynamic_activities_component, self.workflow_generation_plan_service.ignored_activities
        )

        return self

    def get_db_hash(self) -> str | None:
        return self.activities_retriever.db_hash

    @log_execution_time("WorkflowGenerationModelPrediction")
    async def _generate(
        self,
        gen_model: BaseChatModel,
        description: str,
        variables: list[workflow_generation_schema.Variable],
        triggers: list[ActivityDefinition],
        activities: list[ActivityDefinition],
        additional_type_definitions: str,
        plan: str,
        demonstrations: dict[SubsetName, list[dict]],
        mode: ActivitiesGenerationMode,
        existing_plan: str = "",
        existing_workflow: str = "",
    ) -> tuple[str, str, str, TokenUsage]:
        gen_params = {"additional_instructions": ""}
        if mode == "testcase":
            # Treat test cases as sequences with further instructions
            gen_params["additional_instructions"] = constants.TESTCASE_GENERATION_ADDITIONAL_INSTRUCTIONS
            mode = "sequence"

        system_message = langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["gen_system_template"][mode]).format(**gen_params)
        assistant_message_template = langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["gen_assistant_template"])
        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["gen_user_template"][mode])

        gen_messages = [system_message]

        demo_messages = self.prompt_builder_component.build_generation_demonstration_messages(
            demonstrations,
            user_message_template,
            assistant_message_template,
            gen_model,
            mode,
            self.config["demonstration_ranking"].get("sorting_order") == "ascending",
        )
        gen_messages.extend(demo_messages)

        user_message = self.prompt_builder_component.build_user_message(
            description,
            variables,
            triggers,
            activities,
            additional_type_definitions,
            plan,
            gen_model,
            gen_messages,
            user_message_template,
            mode,
            existing_plan=existing_plan,
            existing_workflow=existing_workflow,
        )
        gen_messages.append(user_message)

        messages = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
        inputs = {}
        prompt = messages.format(**inputs)
        chat_chain = messages | gen_model

        result, reasoning_content, usage = await self._perform_generation(gen_model, inputs, chat_chain)
        return result, reasoning_content, prompt, usage

    async def _perform_generation(self, gen_model: BaseChatModel, inputs: dict, chat_chain, can_retry: bool = True) -> tuple[str, str, TokenUsage]:
        with langchain_community.callbacks.get_openai_callback() as cb:
            result = await chat_chain.ainvoke(inputs)
            reasoning_content = None

            if isinstance(result, list):
                # If the model returns a list, the second item is the actual result
                # The first item is the reasoning
                reasoning_content = result[0]["reasoning_content"]["text"]
                result = result[1]["text"]
            else:
                reasoning_content = None
                result = result.content

            usage = workflow_generation_schema.TokenUsage(
                model=gen_model.model_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )

        # If the model is at the max tokens or close to it, increase the limit to 4096 and retry the generation
        # The model may return slightly more or a bit less tokens than the max tokens.
        if can_retry and abs(usage.completion_tokens - gen_model.max_model_tokens) < MAX_MODEL_TOKENS_THRESHOLD:
            gen_model.max_model_tokens = 4096
            return await self._perform_generation(gen_model, inputs, chat_chain, can_retry=False)

        return result, reasoning_content, usage

    async def run(
        self,
        query: str,
        variables: list[workflow_generation_schema.Variable],
        additional_type_definitions: str,
        connections: list[Connection],
        target_framework: TargetFramework,
        objects: list[workflow_generation_schema.UIObject],
        message_emitter: IMessageEmitter | None = None,
        model_options: dict[str, ModelOptions] | None = None,
        localization: str | None = None,
        mode: ActivitiesGenerationMode = "workflow",
        use_plan: str | None = None,
        existing_plan: str = "",
        existing_workflow: str = "",
        ignored_namespaces: set[str] | None = None,
        ignored_identifiers: set[str] | None = None,  # dataset identifiers
        *,
        stitch_workflow_on_sequence_generation: bool = False,
        eval_mode: bool = False,
    ) -> workflow_generation_schema.WorkflowGenerationTaskResult:
        ignored_namespaces = ignored_namespaces or set()
        ignored_identifiers = ignored_identifiers or set()
        duration = workflow_generation_schema.WorkflowGenerationDuration()
        workflow_result = self._init_workflow_result()
        consuming_feature_type = self._get_consuming_feature(mode)

        # Change query to english
        if localization and not localization.startswith("en"):
            query = await self._translate_user_query(query, consuming_feature_type)

        duration.set_checkpoint("init")
        # Run planning and activities retrieval
        existing_workflow_object = load_workflow_instance(query, existing_workflow) if existing_workflow is not None and existing_workflow != "" else None
        generation_settings = self._build_generation_settings(query, target_framework, existing_workflow_object, model_options)

        plan_retrieval_result = await self.workflow_generation_plan_service.generate_relevant_activities(
            query, existing_workflow_object, connections, mode, target_framework, generation_settings, eval_mode
        )

        duration.set_checkpoint("planning")
        # If the plan is junk, return the junk workflow response
        if plan_retrieval_result.generation.score == -1:
            return self._get_junk_workflow_response(query, plan_retrieval_result, workflow_result)

        plan: str = plan_retrieval_result.generation.plan if use_plan == "" or use_plan is None else use_plan

        # TODO: the "whole" plan is just a logging artifact, should we remove it? or move it to the evaluation?
        plan_whole = get_plan_whole(mode, plan, existing_plan, stitch_workflow_on_sequence_generation)

        demonstrations = self.get_demonstrations(
            query,
            target_framework,
            mode,
            ignored_namespaces,
            ignored_identifiers,
            plan_retrieval_result.ignored_activities,
            plan_retrieval_result.query_embedding,
            plan_retrieval_result.connections_embedding,
        )
        duration.set_checkpoint("demonstrations_retrieval")

        activities, triggers, jit_types, activities_config_map = await self.retrieve_activities_metadata(
            target_framework, mode, plan_retrieval_result, plan_retrieval_result.connections_by_key
        )
        duration.set_checkpoint("metadata_retrieval")

        if message_emitter:
            await message_emitter.emit_debug_message(f"Retrieved {len(activities)} activities and {len(triggers)} triggers.")

            activity_names = "\n- ".join(activities.keys())
            trigger_names = "\n- ".join(triggers.keys())

            await message_emitter.emit_debug_message(f"Triggers:\n- {trigger_names}")
            await message_emitter.emit_debug_message(f"Activities:\n- {activity_names}")

        activities_and_triggers = list(activities.values()) + list(triggers.values())

        # run workflow generation
        gen_model = ModelManager().get_llm_model(generation_settings.model_options["generation"], consuming_feature_type)
        if not gen_model.has_max_model_tokens():  # type: ignore
            if len(query) > 100:
                # Add 1 token for each 2 characters over 100
                plan_tokens = max(4096, int(1400 + max(len(query) - 100, 0) / 2))
                gen_model.max_model_tokens = plan_tokens  # type: ignore
            else:
                gen_model.max_model_tokens = min(1400, 400 * len(plan.split("\n")))  # type: ignore

        # TODO: separate triggers from activities in the generation prompt
        try:
            workflow_raw_yaml, reasoning_content, generation_prompt, generation_usage = await self._generate(
                gen_model,
                query,
                variables,
                list(triggers.values()),
                list(activities.values()),
                additional_type_definitions,
                plan,
                demonstrations,
                mode,
                existing_plan,
                existing_workflow,
            )
        except ParsingException:
            # Try to generate again with a better model
            gen_model = ModelManager().get_llm_model("workflow_generation_large_model", consuming_feature_type)
            workflow_raw_yaml, reasoning_content, generation_prompt, generation_usage = await self._generate(
                gen_model,
                query,
                variables,
                list(triggers.values()),
                list(activities.values()),
                additional_type_definitions,
                plan,
                demonstrations,
                mode,
                existing_plan,
                existing_workflow,
            )

        duration.set_checkpoint("generation")
        if reasoning_content and message_emitter is not None:
            await message_emitter.emit_debug_message(f"Reasoning: {reasoning_content}")

        workflow_result, sequence_result = await self._perform_post_processing(
            gen_model,
            query,
            variables,
            target_framework,
            objects,
            localization,
            mode,
            existing_workflow,
            stitch_workflow_on_sequence_generation,
            consuming_feature_type,
            plan_retrieval_result,
            jit_types,
            activities_config_map,
            activities_and_triggers,
            workflow_raw_yaml,
        )

        duration.set_checkpoint("post_processing")

        out = {
            "plan": plan,
            "plan_whole": plan_whole,
            "steps": plan.split("\n"),
            "workflow_result": workflow_result,
            "sequence_result": sequence_result,
            "retrieved_activities": list(activities.values()),
            "retrieved_triggers": list(triggers.values()),
            "retrieval_prompt": plan_retrieval_result.prompt,
            "retrieval_usage": plan_retrieval_result.token_usage,
            "generation_prompt": generation_prompt,
            "generation_usage": generation_usage,
            "demonstrations": demonstrations,
            "duration": duration.to_dict(),
        }

        out = self._set_prompt_result_scores(out, plan_retrieval_result.generation)
        return out

    def _init_workflow_result(self):
        workflow_result: workflow_generation_schema.WorkflowGenerationResult = {
            "is_response_valid_yaml": False,
            "workflow_raw": "",
            "workflow_valid": "",
            "used_trigger": "",
            "used_activities": [],
            "used_jit_types": [],
            "used_connectors": [],
            "used_packages": [],
            "used_variables": [],
            "used_local_variables": [],
            "used_namespaces": [],
        }

        return workflow_result

    async def _translate_user_query(self, query, consuming_feature_type):
        query_language = "en"
        try:
            query_language = langdetect.detect(query)
        except langdetect.lang_detect_exception.LangDetectException as ex:
            # on langdetect error failure, we have some undefined behaviour, as it will raise an exception with code "5"
            # the error code "5" will raise issues in the exception handling middelware
            # wrapping the exception to a custom one to handle it properly
            LOGGER.exception(f"Langdetect failed to detect language: {ex}")
        if query_language != "en":
            request: TextTranslationRequest = {"target_language": "English", "input_string": query, "feature": consuming_feature_type}
            query = await TRANSLATE_TASK.translate_single_str(request)
        return query

    async def _perform_post_processing(
        self,
        model: BaseChatModel,
        query: str,
        variables: list[Variable],
        target_framework: TargetFramework,
        objects: list[UIObject],
        localization: str | None,
        mode: ActivitiesGenerationMode,
        existing_workflow: str,
        stitch_workflow_on_sequence_generation: bool,
        consuming_feature_type: ConsumingFeatureType,
        plan_retrieval_result: ActivityRetrievalResult,
        jit_types: list[str],
        activities_config_map: dict[str, dict],
        activities_and_triggers: list[ActivityDefinition],
        workflow_raw_yaml: str,
    ):
        workflow_raw_whole = workflow_raw_yaml
        if mode == "sequence" or mode == "testcase":  # some postprocessing to merge the sequence into the existing workflow
            workflow_raw_whole, workflow_raw_yaml = await self.postprocess_component.postprocess_sequence_generation(
                model, workflow_raw_yaml, existing_workflow, stitch_workflow_on_sequence_generation
            )

        _postprocess_args = [
            jit_types,
            activities_config_map,
            plan_retrieval_result.connections_by_key,
            target_framework,
            objects,
            activities_and_triggers,
            variables,
            localization,
        ]
        workflow_result = await self.postprocess_component.postprocess_generation(
            model,
            workflow_raw_yaml,
            query,
            *_postprocess_args,
            allow_triggers=mode == "workflow",
            allow_assign_activity_on_uia_expansion=mode == "testcase",
            consuming_feature_type=consuming_feature_type,
        )

        if (mode == "sequence" or mode == "testcase") and stitch_workflow_on_sequence_generation:
            sequence_result = copy.deepcopy(workflow_result)
            workflow_result = await self.postprocess_component.postprocess_generation(
                model,
                workflow_raw_whole,
                query,
                *_postprocess_args,
                allow_triggers=mode == "workflow",
                allow_assign_activity_on_uia_expansion=mode == "testcase",
                consuming_feature_type=consuming_feature_type,
            )
            return workflow_result, sequence_result
        else:
            return workflow_result, workflow_result

    def _build_generation_settings(
        self, query: str, target_framework: TargetFramework, existing_workflow_object: Workflow | None, model_options: dict[str, ModelOptions] | None
    ):
        existing_workflow_activities_or_triggers = (
            (
                (existing_workflow_object.activities + [existing_workflow_object.trigger])
                if existing_workflow_object.trigger is not None
                else existing_workflow_object.activities
            )
            if existing_workflow_object is not None
            else []
        )
        generation_settings = GenerationSettingsBuilder.build_generation_settings(
            query,
            target_framework,
            model_options,
            [activity.fqn for activity in existing_workflow_activities_or_triggers],
            Supported_Excel_Packages,
            Supported_Mail_Packages,
        )
        if generation_settings.default_excel_package is None:
            if target_framework == "Windows":
                generation_settings.default_excel_package = "ExcelDesktopLegacy"
            else:
                generation_settings.default_excel_package = "ExcelOnline"
        if generation_settings.default_mail_package is None:
            if target_framework == "Windows":
                generation_settings.default_mail_package = "MailDesktopLegacy"
            else:
                generation_settings.default_mail_package = "MailOnline"
        return generation_settings

    async def retrieve_activities_metadata(
        self,
        target_framework: TargetFramework,
        mode: ActivitiesGenerationMode,
        plan_retrieval_result: ActivityRetrievalResult,
        connections_by_key: dict[str, Connection],
    ):
        try:
            activities_list = list(
                self.activities_retriever.get_by_class_name(activity_class_name, target_framework, "activity")
                for activity_class_name in plan_retrieval_result.retrieved_activities
            )
            triggers_list = list(
                self.activities_retriever.get_by_class_name(trigger_class_name, target_framework, "trigger")
                for trigger_class_name in plan_retrieval_result.retrieved_triggers
            )
        except Exception as e:
            retrieved_activities_list = str.join("\n", plan_retrieval_result.retrieved_activities)
            retrieved_triggers_list = str.join("\n", plan_retrieval_result.retrieved_triggers)

            raise ValueError(f"Failed to retrieve activities or triggers. Original list: {retrieved_activities_list} {retrieved_triggers_list}.\n" + str(e))

        activities = {activity["fullClassName"]: activity for activity in activities_list if activity is not None}
        triggers = {trigger["fullClassName"]: trigger for trigger in triggers_list if trigger is not None}
        basic_triggers, basic_activities = self.activities_retriever.get_basic_activities(target_framework, mode)
        for trigger in basic_triggers:
            if trigger["fullClassName"] not in triggers:
                triggers[trigger["fullClassName"]] = trigger

        for activity in basic_activities:
            if activity["fullClassName"] not in activities:
                activities[activity["fullClassName"]] = activity

        jit_types, activities_config_map = await WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
            activities, triggers, connections_by_key
        )

        return activities, triggers, jit_types, activities_config_map

    def get_demonstrations(
        self,
        query: str,
        target_framework: TargetFramework,
        mode: ActivitiesGenerationMode,
        ignored_namespaces: set[str],
        ignored_identifiers: set[str],
        ignored_activities: set[str],
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
    ):
        demo_retriever = self.workflow_demo_retriever if mode == "workflow" else self.sequence_demo_retriever
        max_train_demonstrations_count = self.config["demonstration_ranking"]["generation_demonstrations_limit"]
        # number of demonstrations to be retrieved for each subset. -1 means retrieve all demonstrations from that subset.
        demonstrations_count: dict[SubsetName, int] = {
            "train": max_train_demonstrations_count,
            "static": -1,
        }
        demonstrations_count["uia"] = 1
        if mode == "testcase":
            demonstrations_count["uia"] = 2
            demonstrations_count["testcase"] = 1
        # if mode == "sequence":  # This seems to not be taken into account anymore: for planning, mmr_docs seem to be used
        #     demonstrations_count["train"] *= 2  # use more demonstrations for planning in sequence generation
        # The number of train demonstrations retrieved here is not demonstrations_count["train"], but config's mmr_docs
        demonstrations = demo_retriever.get_relevant(
            query,
            query_embedding,
            connections_embedding,
            demonstrations_count,
            target_framework,
            ignored_namespaces,
            ignored_identifiers,
            ignored_activities,
        )
        # more demonstrations could have been used for planning; assert that the similarity order in train demonstrations is decreasing
        self._prepare_demonstrations_for_generation(demonstrations, demonstrations_count["train"], target_framework)

        return demonstrations

    def _prepare_demonstrations_for_generation(self, demonstrations: dict[SubsetName, list[dict]], train_limit: int, target_framework: TargetFramework):
        if self.config["demonstration_ranking"].get("sorting_order") == "ascending":
            demonstrations["train"] = demonstrations["train"][::-1]
        if target_framework == "Windows":
            n_force_windows = self.config["demonstration_ranking"].get("force_windows_demonstrations", 0)
            windows_demos_indices = [i for i, demo in enumerate(demonstrations["train"]) if demo["target_framework"] == "Windows"][:n_force_windows]
            remaining_indices = sorted(set(range(train_limit)) - set(windows_demos_indices))[: train_limit - n_force_windows]
            demonstrations["train"] = [demonstrations["train"][i] for i in sorted(windows_demos_indices + remaining_indices)]
        demonstrations["train"] = demonstrations["train"][:train_limit]
        if self.config["demonstration_ranking"].get("sorting_order") == "ascending":
            demonstrations["train"] = demonstrations["train"][::-1]

    def _set_prompt_result_scores(self, out: dict, plan_retrieval_result: workflow_generation_schema.ActivityRetrievalGeneration):
        out["prompt_class"] = "junk" if plan_retrieval_result.score == -1 else "valid" if plan_retrieval_result.score > 20 else "ambiguous"
        out["prompt_score"] = plan_retrieval_result.score
        return out

    def _get_consuming_feature(self, mode):
        return ConsumingFeatureType.WORKFLOW_GENERATION if mode == "workflow" else ConsumingFeatureType.SEQUENCE_GENERATION

    async def generate_workflow(
        self,
        query: str,
        connections: list[Connection],
        target_framework: TargetFramework,
        model_options: dict[str, ModelOptions] | None,
        localization: str | None = None,
        message_emitter: IMessageEmitter | None = None,
        mode: ActivitiesGenerationMode = "workflow",
    ) -> workflow_generation_schema.WorkflowGenerationTaskResult:
        return await self.run(
            query=query,
            variables=[],
            additional_type_definitions="",
            connections=connections,
            objects=[],
            target_framework=target_framework,
            model_options=model_options,
            localization=localization,
            mode=mode,
            message_emitter=message_emitter,
        )

    async def generate_sequence(
        self,
        query: str,
        existing_workflow: str,
        variables: list[workflow_generation_schema.Variable],
        additional_type_definitions: str,
        connections: list[Connection],
        target_framework: TargetFramework,
        objects: list[workflow_generation_schema.UIObject],
        model_options: dict[str, ModelOptions] | None,
        localization: str | None = None,
        is_test_case: bool = False,
        message_emitter: IMessageEmitter | None = None,
        *,
        stitch_workflow: bool = False,
    ) -> workflow_generation_schema.WorkflowGenerationTaskResult:
        workflow_object = load_workflow_instance(query, existing_workflow)
        SequenceGenerationCurrentActivityReplacer().replace(workflow_object)
        existing_plan = PlanBuilder().build(workflow_object)
        if SEQUENCE_GENERATION_INSERTION_PHRASE not in existing_plan:
            raise errors.MissingCurrentActivityError()
        ignored_namespaces = self._get_ignored_namespaces(workflow_object, target_framework)
        existing_workflow = workflow_object.lmyaml(include_packages=False, include_arguments=False, include_variables=False)

        return await self.run(
            query=query,
            variables=variables,
            additional_type_definitions=additional_type_definitions,
            connections=connections,
            target_framework=target_framework,
            model_options=model_options,
            localization=localization,
            mode="testcase" if is_test_case else "sequence",
            objects=objects,
            existing_plan=existing_plan,
            existing_workflow=existing_workflow,
            ignored_namespaces=ignored_namespaces,
            stitch_workflow_on_sequence_generation=stitch_workflow,
            message_emitter=message_emitter,
        )

    def _get_ignored_namespaces(self, workflow: Workflow, target_framework: TargetFramework) -> set[str]:
        if target_framework != "Windows":
            return set()
        return get_ignored_namespaces(workflow, self.activities_retriever, target_framework)

    def _get_junk_workflow_response(
        self, query: str, plan_retrieval_result: ActivityRetrievalResult, workflow_result: workflow_generation_schema.WorkflowGenerationResult
    ) -> workflow_generation_schema.WorkflowGenerationTaskResult:
        junk_workflow = Workflow(
            query,
            plan_retrieval_result.generation.plan,
            {
                "processName": "InvalidQuery",
                "packages": [],
                "workflow": [{"thought": "", "activity": "Please improve your query.", "params": {}, "currentActivity": True}],
            },
        ).lmyaml()

        workflow_result["is_response_valid_yaml"] = True
        workflow_result["workflow_valid"] = junk_workflow

        out = {
            "plan": "",
            "plan_whole": "",
            "steps": [],
            "workflow_result": workflow_result,
            "sequence_result": workflow_result,
            "retrieved_activities": [],
            "retrieved_triggers": [],
            "retrieved_activities_after_pruning": [],
            "retrieved_triggers_after_pruning": [],
            "retrieved_packages": [],
            "retrieval_prompt": "",
            "retrieval_usage": TokenUsage(),
            "generation_prompt": "",
            "generation_usage": TokenUsage(),
            "demonstrations": {},
            "duration": {},
        }
        self._set_prompt_result_scores(out, plan_retrieval_result.generation)
        return out
