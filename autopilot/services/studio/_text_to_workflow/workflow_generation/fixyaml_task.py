import pathlib
import re

import langchain.cache
import langchain.chains
import langchain.chains.llm
import langchain.chat_models
import langchain.embeddings
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
import langchain_core.language_models

from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import FixWorkflowYamlRequest, FixWorkflowYamlResponse


class FixWorkflowYamlTask:
    def __init__(self, config_name: str) -> None:
        self.config_dir = (pathlib.Path(__file__).parent / "config").absolute()
        self.config_path = self.config_dir / config_name
        self.config = yaml_load(self.config_path)

    @log_execution_time("WorkflowGenerationFixYaml")
    async def run(
        self,
        model: langchain_core.language_models.BaseChatModel,
        request: FixWorkflowYamlRequest,
        allow_triggers: bool = True,
        merge_conflicting_keys_in_yaml: bool = False,
    ) -> FixWorkflowYamlResponse:
        mode = "with_triggers" if allow_triggers else "without_triggers"
        system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["system_msg"][mode])
        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["user_msg_template"])

        chat_messages = [system_message_template]

        for example in self.config["examples"][mode]:
            assistant_message = langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["assistant_msg_template"]).format(
                fixed_workflow=example["fixed_workflow"]
            )  # noqa
            user_message = user_message_template.format(errors=example["errors"], workflow=example["workflow"])  # noqa

            chat_messages.append(user_message)
            chat_messages.append(assistant_message)

        chat_messages.append(user_message_template)
        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(chat_messages)

        chat_chain = chat_prompt | model

        response = FixWorkflowYamlResponse(fixed_workflow={}, prompt="")
        with langchain_community.callbacks.get_openai_callback():
            prompt = chat_chain.get_prompts()[0].format(**request)
            result = (await chat_chain.ainvoke(request)).content
            response = self._load_completion_yaml(result, merge_conflicting_keys_in_yaml=merge_conflicting_keys_in_yaml)

        return {"fixed_workflow": response, "prompt": prompt}

    def _load_completion_yaml(self, output: str, merge_conflicting_keys_in_yaml: bool = False) -> dict:  # type: ignore
        if output.startswith("```yaml"):
            match = re.match(r"```yaml(.*)```", output, re.DOTALL)
        elif output.startswith("```"):
            match = re.match(r"```(.*)```", output, re.DOTALL)
        else:
            match = re.match(r"(.*)", output, re.DOTALL)
        if not match:
            raise Exception("ERROR: Could not load yaml from output, format incorrect.")

        return yaml_load(match.group(1), merge_conflicting_keys=merge_conflicting_keys_in_yaml)
