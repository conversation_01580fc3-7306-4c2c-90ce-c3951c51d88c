import math
import re
from copy import deepcopy
from typing import Any

import apted
import Levenshtein
import typing_extensions as t

from services.studio._text_to_workflow.common.params import get_param_value_category
from services.studio._text_to_workflow.common.schema import WorkflowDict
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

EvalMode: t.TypeAlias = t.Literal["noparams", "exact", "levenshtein"]
FilterParamsMode: t.<PERSON>lias = t.Literal["expression", "property"] | None

T1 = """
trigger:
  activity: A
workflow:
- activity: B
"""

T2 = """
trigger:
  activity: A
workflow:
- activity: C
"""

T3 = """
trigger:
  activity: A
workflow:
- activity: B
- activity: C
"""

T4 = """
trigger:
  activity: A
workflow:
- activity: If
  params:
    Condition: '[[condition]]'
    Then:
    - activity: B
    Else:
    - activity: C
"""

T5 = """
trigger:
  activity: A
workflow:
- activity: ForEach
  params:
    Body:
      variables:
      - name: currentItem
      Handler:
      - activity: B
"""

TP1 = """
trigger:
  activity: A
  params: {}
workflow:
- activity: B
  params:
    K1: V1
    K2: V2
"""

TP2 = """
trigger:
  activity: C
  params: {}
workflow:
- activity: B
  params:
    K1: V1
    K2: V3
"""


def exact_match_score(target: dict, predicted: dict, filter: t.Literal["expression", "property"] | None = None) -> float:
    if not target and not predicted:
        return -1.0
    params = set(target.keys()).union(predicted.keys())
    nom, denom = 0, 0
    for param in params:
        target_param = target.get(param, "")
        predicted_param = predicted.get(param, "")
        if filter and filter != get_param_value_category(target_param):
            continue
        nom += target_param == predicted_param
        denom += 1
    if denom == 0:
        return -1.0
    return nom / denom


def levenshtein_score(target: dict, predicted: dict, filter: t.Literal["expression", "property"] | None = None) -> float:
    if not target and not predicted:
        return -1.0
    params = set(target.keys()).union(predicted.keys())
    nom, denom = 0, 0
    for param in params:
        target_param = target.get(param, "")
        predicted_param = predicted.get(param, "")
        if isinstance(target_param, (dict, list)):
            continue
        if isinstance(predicted_param, (dict, list)):
            continue
        if filter and filter != get_param_value_category(target_param):
            continue
        nom += Levenshtein.ratio(str(target_param), str(predicted_param))
        denom += 1
    if denom == 0:
        return -1.0
    return nom / denom


class APTEDNode:
    last_id = 0

    def __init__(self, value: str, params, children: list["APTEDNode"] | None = None, id: int | None = None):
        if id is None:
            APTEDNode.last_id += 1
            id = APTEDNode.last_id

        self.id = id
        self.value = value
        self.params = params
        self.children = children or []

    def __repr__(self):
        """Returns a string representation of the node."""
        return str(self.value) + "-" + str(self.id)

    def __len__(self):
        """Returns the number of nodes in the tree, including the root node."""
        length = 1
        for node in self.children:
            length += len(node)
        return length

    def to_string(self, _repr: list[str], level: int = 0):
        """Returns a string representation of the node."""
        _repr.append("\t" * level + repr(self))
        for child in self.children:
            child.to_string(_repr, level + 1)

    def add_child(self, child: "APTEDNode"):
        """Adds a child to the node."""
        self.children.append(child)


class CoreWfAPTEDNode(APTEDNode):
    last_id = 0

    def __init__(self, value: str, params, children: list["APTEDNode"] | None = None):
        CoreWfAPTEDNode.last_id += 1
        super().__init__(value, params, id=CoreWfAPTEDNode.last_id, children=children)

    @staticmethod
    def gather_params_from_node(node: dict) -> dict | list:
        params_copy = deepcopy(node.get("params", {}))
        return CoreWfAPTEDNode._gather_params(params_copy)

    @staticmethod
    def _gather_params(params_copy) -> dict | list:
        if isinstance(params_copy, dict):
            params_copy = {k: CoreWfAPTEDNode._gather_params(v) for k, v in params_copy.items() if not (isinstance(v, dict) and "activity" in v)}
        elif isinstance(params_copy, list):
            params_copy = [CoreWfAPTEDNode._gather_params(v) for v in params_copy if not (isinstance(v, dict) and "activity" in v)]
        return params_copy

    @staticmethod
    def gather_children(workflow):
        nodes = []
        if isinstance(workflow, dict):
            if "Then" in workflow and "Else" in workflow:
                if isinstance(workflow["Then"], dict) and "params" in workflow["Then"]:
                    then_params = CoreWfAPTEDNode.gather_params_from_node(workflow["Then"])
                else:
                    then_params = {}
                then_node = CoreWfAPTEDNode("Then", params=then_params)
                for v in workflow["Then"]:
                    children = CoreWfAPTEDNode.gather_children(v)
                    for c in children:
                        then_node.add_child(c)
                if isinstance(workflow["Else"], dict) and "params" in workflow["Else"]:
                    else_params = CoreWfAPTEDNode.gather_params_from_node(workflow["Else"])
                else:
                    else_params = {}
                else_node = CoreWfAPTEDNode("Else", params=else_params)
                for v in workflow.get("Else", []) or []:  # found an examle with Else set to None (non iterable)
                    children = CoreWfAPTEDNode.gather_children(v)
                    for c in children:
                        else_node.add_child(c)
                nodes.append(then_node)
                nodes.append(else_node)
            else:
                if "activity" in workflow:
                    params = CoreWfAPTEDNode.gather_params_from_node(workflow)
                    node = CoreWfAPTEDNode(workflow["activity"], params)
                    # print("found new node", workflow["activity"])
                    for v in workflow.values():
                        children = CoreWfAPTEDNode.gather_children(v)
                        for c in children:
                            node.add_child(c)
                    nodes.append(node)
                else:
                    for _, v in workflow.items():
                        # print("folowing key", k)
                        children = CoreWfAPTEDNode.gather_children(v)
                        for c in children:
                            nodes.append(c)
        if isinstance(workflow, list):
            for v in workflow:
                for c in CoreWfAPTEDNode.gather_children(v):
                    nodes.append(c)
        return nodes


class APTEDTree:
    def __init__(self, root: APTEDNode):
        self.root = root

    def __repr__(self):
        """Returns a string representation of the tree."""
        _repr = []
        for child in self.root.children:
            child.to_string(_repr, 0)
        return "\n".join(_repr)

    def __len__(self):
        """Returns the number of nodes in the tree. The root node is not counted."""
        return len(self.root) - 1


class CoreWfAPTEDTree(APTEDTree):
    """
    This class is used to compute the tree edit distance for RPA Workflow generation (CoreWf).
    """

    def __init__(self, process: WorkflowDict | dict[str, Any]):
        root_children = []
        if trigger := process.get("trigger"):
            params = CoreWfAPTEDNode.gather_params_from_node(deepcopy(trigger.get("params", {})))
            root_children.append(CoreWfAPTEDNode(trigger["activity"], params))
        root_children.extend(CoreWfAPTEDNode.gather_children(process["workflow"]))
        root = CoreWfAPTEDNode("[ROOT]", params={}, children=root_children)
        super().__init__(root)


class CustomConfig(apted.Config):
    def __init__(self, match_params: EvalMode = "noparams", filter_params: FilterParamsMode = None):
        super(CustomConfig, self).__init__()
        self.match_params = match_params
        self.filter_params = filter_params

    def delete(self, node):
        """Calculates the cost of deleting a node"""
        return 1

    def insert(self, node: APTEDNode):
        """Calculates the cost of inserting a node"""
        return 1

    def param_match(self, node1: APTEDNode, node2: APTEDNode):
        if node1 is None or node2 is None:
            return -1.0
        if self.match_params == "exact":
            return exact_match_score(node1.params, node2.params, self.filter_params)
        elif self.match_params == "levenshtein":
            return levenshtein_score(node1.params, node2.params, self.filter_params)
        # else:
        return -1.0

    def rename(self, node1: APTEDNode, node2: APTEDNode):
        """Calculates the cost of renaming the label of the source node
        to the label of the destination node"""
        if node1 is None or node2 is None:
            return 1.0
        param_match = self.param_match(node1, node2)
        if param_match < 0.0:
            score = int(node1.value != node2.value)
        else:
            score = 0.5 * int(node1.value != node2.value) + 0.5 * (1 - param_match)
        # print(node1.value, node2.value, score, param_match)
        return score

    def children(self, node: APTEDNode):
        """Returns children of node"""
        return getattr(node, "children", [])


def core_wf_workflow_edit_score(
    gt_workflow_dict: WorkflowDict | dict[str, Any],
    result_workflow_dict: WorkflowDict | dict[str, Any],
    param_match: EvalMode = "noparams",
    filter_params: FilterParamsMode | None = None,
    compute_mapping: bool = True,
) -> tuple[float, int, float, list[tuple[str, str, float, float]]]:
    tree_gt = CoreWfAPTEDTree(gt_workflow_dict)
    tree_res = CoreWfAPTEDTree(result_workflow_dict)
    return _workflow_edit_score(tree_gt, tree_res, param_match, filter_params, compute_mapping)


def _workflow_edit_score(
    tree_gt: APTEDTree,
    tree_res: APTEDTree,
    param_match: EvalMode = "noparams",
    filter_params: FilterParamsMode | None = None,
    compute_mapping: bool = True,
) -> tuple[float, int, float, list[tuple[str, str, float, float]]]:
    config = CustomConfig(param_match, filter_params)
    ted = apted.APTED(tree_gt.root, tree_res.root, config)
    distance: int = ted.compute_edit_distance()  # type: ignore
    # theoretically maximum number of edits is the sum of the number of nodes in the two trees
    # we normalize to the max length tree to give a more intutitve score, but we threshold at 0 to avoid negative scores.
    # normalized_distance = distance / (len(tree_gt) + len(tree_res))
    normalized_distance = distance / max(len(tree_gt), len(tree_res))
    ted_score = max(0, 1 - normalized_distance)
    if compute_mapping:
        mapping = [(node1, node2, config.rename(node1, node2), config.param_match(node1, node2)) for (node1, node2) in ted.compute_edit_mapping()]
    else:
        mapping = None
    return ted_score, tree_gt, tree_res, mapping  # type: ignore


def parse_tree_structure(structure):
    # Regular expression to match the lines and their hierarchy
    pattern = re.compile(r"^(\s*)((\d+(\.?))*)\s*(.*)")
    lines = structure.splitlines()

    root = APTEDNode("[ROOT]", params="0")
    nodes = {"0": root}
    node = None

    for line in lines:
        match = pattern.match(line)
        if match:
            indentation, index, _, _, text = match.groups()
            if (index == "" or index is None) and (node is not None):
                node.value = node.value + "\n" + line

            index = index.strip(".")
            levels = index.split(".")
            node_index = "0." + index
            parent_node_index = ".".join(["0"] + levels[:-1])

            node = APTEDNode(text.strip(), params=node_index)
            nodes[node_index] = node
            nodes[parent_node_index].add_child(node)
        elif node is not None:
            node.value = node.value + "\n" + line
    return root


def get_steps_plan(structure) -> list[str]:
    # Regular expression to match the lines and their hierarchy
    pattern = re.compile(r"^(\s*)((\d+(\.?))*)\s*(.*)")
    lines = structure.splitlines()
    nodes = {}
    node = None

    for line in lines:
        match = pattern.match(line)
        if match:
            indentation, index, _, _, text = match.groups()
            if (index == "" or index is None) and (node is not None):
                node.value = node.value + "\n" + line

            index = index.strip(".")
            node_index = "0." + index

            node = APTEDNode(text.strip(), params=node_index)
            nodes[node_index] = node
        elif node is not None:
            node.value = node.value + "\n" + line
    return [node.value for node in nodes.values()]


class CustomPlanMatchingConfig(apted.Config):
    def __init__(self, hard_threshold=None, cross_encoder_embeddings_model=None):
        super(CustomPlanMatchingConfig, self).__init__()
        self.similarity_hard_threshold = hard_threshold
        self.cross_encoder_embeddings_model = cross_encoder_embeddings_model

    def delete(self, node):
        """Calculates the cost of deleting a node"""
        return 1

    def insert(self, node: APTEDNode):
        """Calculates the cost of inserting a node"""
        return 1

    def rename(self, node1: APTEDNode, node2: APTEDNode):
        """Calculates the cost of renaming the label of the source node
        to the label of the destination node"""
        if node1 is None or node2 is None:
            return 1.0
        if self.cross_encoder_embeddings_model is not None:
            x = [[node1.value, node2.value]]
            score = self.cross_encoder_embeddings_model.predict(x)[0]
            score = 1 / (1 + math.exp(-score))  # sigmoid TODO: the model shoudl compute the sigmoid
            score = 1 - score
        else:
            score = 1 - Levenshtein.ratio(node1.value, node2.value)
        if self.similarity_hard_threshold is not None and self.similarity_hard_threshold > 0:
            if (1 - score) < self.similarity_hard_threshold:
                score = 1
        return score

    def children(self, node: APTEDNode):
        """Returns children of node"""
        return getattr(node, "children", [])


class PlanActivityMatchingConfig(apted.Config):
    def __init__(self, hard_threshold=None, cross_encoder_embeddings_model=None):
        super(PlanActivityMatchingConfig, self).__init__()
        self.similarity_hard_threshold = hard_threshold
        self.cross_encoder_embeddings_model = cross_encoder_embeddings_model

    def delete(self, node):
        """Calculates the cost of deleting a node"""
        return 1

    def insert(self, node: APTEDNode):
        """Calculates the cost of inserting a node"""
        return 1

    def rename(self, node1: APTEDNode, node2: APTEDNode):
        """Calculates the cost of renaming the label of the source node
        to the label of the destination node"""
        if node1 is None or node2 is None:
            return 1.0

        node2_list = node2.value.split(",")
        score = 1.0 - int(node1.value in node2_list)
        return score

    def children(self, node: APTEDNode):
        """Returns children of node"""
        return getattr(node, "children", [])


def planning_edit_score(ground_truth_plan: str, predicted_plan: str, hard_threshold_value=None, cross_encoder_model=None):
    if cross_encoder_model:
        cfg = CustomPlanMatchingConfig(hard_threshold_value, cross_encoder_embeddings_model=cross_encoder_model)
    else:
        cfg = PlanActivityMatchingConfig()
    ground_truth_tree = parse_tree_structure(ground_truth_plan)
    predicted_plan_tree = parse_tree_structure(predicted_plan)

    ted = apted.APTED(ground_truth_tree, predicted_plan_tree, cfg)
    distance: int = ted.compute_edit_distance()  # type: ignore
    # theoretically maximum number of edits is the sum of the number of nodes in the two trees
    # we normalize to the max length tree to give a more intutitve score, but we threshold at 0 to avoid negative scores.
    # normalized_distance = distance / (len(tree_gt) + len(tree_res))
    normalized_distance = distance / max(len(ground_truth_tree), len(predicted_plan_tree))
    ted_score = max(0, 1 - normalized_distance)
    return ted_score


def same_activity_in_mapping(item):
    return item[0] is not None and item[1] is not None and item[0].value == item[1].value


def print_results(ted_score, tree1, tree2, mapping):
    from pprint import pprint

    print("ted_score", ted_score)
    print("=" * 10)
    print(tree1)
    print("=" * 10)
    print(tree2)
    for node1, node2, distance, param_match in mapping[1:]:
        print("=" * 10)
        print(node1, node2, distance, param_match)
        if node1 is not None:
            print("-" * 10, "params node1")
            pprint(node1.params)
        if node2 is not None:
            print("-" * 10, "params node2")
            pprint(node2.params)
        if node1 is not None:
            print("-" * 10, "children node1")
            pprint(node1.children)
        if node2 is not None:
            print("-" * 10, "children node2")
            pprint(node2.children)


if __name__ == "__main__":
    fixtures = [
        (T1, T2, "noparams", None, 0.5, -1),
        (T1, T2, "exact", None, 0.5, -1),
        (T1, T3, "noparams", None, 0.66, -1),
        (T3, T4, "noparams", None, 0.5, -1),
        (T1, T5, "noparams", None, 0.66, -1),
        (TP1, TP2, "noparams", None, 0.5, -1),
        (TP1, TP2, "exact", None, 0.375, 0.5),
        (TP1, TP2, "levenshtein", None, 0.4375, 0.75),
    ]
    for i, (t1, t2, param_match, filter_params, expected_score, expected_param_score) in enumerate(fixtures):
        source1 = yaml_load(t1)
        source2 = yaml_load(t2)
        ted_score, tree1, tree2, mapping = core_wf_workflow_edit_score(source1, source2, param_match=param_match, filter_params=filter_params)
        if abs(expected_score - ted_score) > 0.01:
            print(f"ERROR on ted_score {i}: {ted_score} != {expected_score}")
            print_results(ted_score, tree1, tree2, mapping)
        else:
            print(".", end="")
        param_scores = [m[3] for m in mapping if m[3] >= 0]
        param_score = sum(param_scores) / len(param_scores) if param_scores else -1.0
        if abs(expected_param_score - param_score) > 0.01:
            print(f"ERROR on param_score {i}: {param_score} != {expected_param_score}")
            print_results(ted_score, tree1, tree2, mapping)
        else:
            print(".", end="")
