import gradio as gr

import services.studio._text_to_workflow.utils.dotnet_dynamic_activities_discovery as ddad
from services.studio._text_to_workflow.common import connections_loader
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.workflow_generation import workflow_generation_task
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowGenerationModelOptions

text2workflow = workflow_generation_task.WorkflowGenerationTask(config_name="prompt.yaml").load()
dataset_connections_path = paths.get_dataset_connections_cache_path()
ddad.load_cached_connections(dataset_connections_path)
TENENT_ID, CONNECTIONS = connections_loader.get_connections_data()
LOCALIZATION = None
REQUEST_CONTEXT = get_testing_request_context(LOCALIZATION, TENENT_ID)


async def predict(query, target_framework, generation_mode, run_generation, planning_model):
    generation_model = None
    model_options: WorkflowGenerationModelOptions = {
        "planning": {"model_name": planning_model or None},
        "generation": {"model_name": generation_model},
    }

    result = await text2workflow.run(
        query=query,
        variables=[],
        additional_type_definitions="",
        connections=CONNECTIONS,
        target_framework=target_framework,
        objects=[],
        # request_context=REQUEST_CONTEXT,
        model_options=model_options,
        localization=LOCALIZATION,
        mode=generation_mode,
        existing_plan="",
        # use_plan_demo=use_plan_demo,
        existing_workflow="",
        ignored_identifiers=None,
        stitch_workflow_on_sequence_generation=generation_mode == "sequence",
        run_generation=run_generation,
    )

    if not run_generation:
        return result["plan"], result["retrieval_prompt"], None
    else:
        return result["plan"], result["retrieval_prompt"], result["workflow_result"]["workflow_valid"]


# Gradio Interface
def create_interface():
    with gr.Blocks() as demo:
        # Input and dropdown
        query_input = gr.Textbox(label="Query")
        with gr.Row():
            target_framework = gr.Dropdown(choices=["Windows", "Portable"], label="Target Framework")
            generation_mode = gr.Dropdown(choices=["workflow", "sequence"], label="Generation Mode")
            planning_model_input = gr.Dropdown(choices=["gpt4o", "llama3.1-70B", "llama3.1-70B-planning-finetuned"], label="Planning Model", value="gpt4o")
            run_generation_toggle = gr.Checkbox(label="Generation", value=False)

        predict_plan_button = gr.Button("Predict")

        # Output area with tabs
        with gr.Tabs():
            with gr.Tab("Plan and Generation"):
                plan_output = gr.Textbox(label="Plan", interactive=False, lines=10)
                generation_output = gr.Textbox(label="Generation", interactive=False, lines=30)
            with gr.Tab("Plan Prompt"):
                plan_prompt_output = gr.Textbox(label="Plan Prompt", interactive=False, lines=50)

        # Define button actions
        predict_plan_button.click(
            fn=predict,
            inputs=[query_input, target_framework, generation_mode, run_generation_toggle, planning_model_input],
            outputs=[plan_output, plan_prompt_output, generation_output],
        )

    return demo


# Launch the demo
demo = create_interface()
demo.launch(server_name="0.0.0.0")
