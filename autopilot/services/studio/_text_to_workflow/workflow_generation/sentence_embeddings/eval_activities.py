import yaml
from sentence_transformers import SentenceTransformer
from sentence_transformers.evaluation import InformationRetrievalEvaluator

# Load a model
# model = SentenceTransformer('all-mpnet-base-v2')
# model = SentenceTransformer('output_train_msmarco_retrieval_20240911/checkpoint-219600')
# model = SentenceTransformer('output_train_msmarco_20240911/checkpoint-11400')
# model = SentenceTransformer('artifacts/v4') # 0.9625 DN, 0.6125 CO, 0.70625 CA, 0.95 DE
# model = SentenceTransformer('output_train_stella_dataset_20241025_10_demos_and_act_20241025/checkpoint-1800', trust_remote_code=True)

# 96.98 DE, 58.18 CA, 97.41 CO, 96.55 DN
model = SentenceTransformer("output_train_stella_dataset_20241101_15_20241101/checkpoint-734000", trust_remote_code=True)

dataset_folder = "dataset_20241126_09"
with open(f"{dataset_folder}/workflows_map.yaml") as fin:
    workflows_map = yaml.load(fin, Loader=yaml.FullLoader)
    test_data = workflows_map["test"]
    train_data = workflows_map["train"]

# attribute = "description"
# attribute = "category"
# attribute = "content"
attribute = "displayName"

corpus, queries, relevant_docs = {}, {}, {}

activities = {}  # activity name -> attribute
relevant_activities = {}  # step -> set of relevant activities
for workflow in test_data:
    for step in test_data[workflow]["steps"]:
        query = step["step"]
        relevant = step["activity"]
        relevant_activities.setdefault(query, set())
        relevant_activities[query].add(relevant)
        activities[relevant] = step[attribute]

# for workflow in train_data:
#     for step in train_data[workflow]["steps"]:
#         activities[relevant] = step[attribute]


activity_ids = {}
activity_list = sorted(activities.keys())
for i, activity in enumerate(activity_list):
    corpus[i] = activities[activity]
    activity_ids[activity] = i

query_ids = {}
query_list = sorted(relevant_activities.keys())
for i, query in enumerate(query_list):
    queries[i] = query
    query_ids[query] = i
    relevant_docs[i] = set(activity_ids[relevant] for relevant in relevant_activities[query])

# Given queries, a corpus and a mapping with relevant documents, the InformationRetrievalEvaluator computes different IR metrics.
ir_evaluator = InformationRetrievalEvaluator(
    queries=queries,
    corpus=corpus,
    relevant_docs=relevant_docs,
    name=f"{attribute}-activities-retriever",
)
results = ir_evaluator(model)
# pprint(results)
# print(ir_evaluator.primary_metric)
# print(results[ir_evaluator.primary_metric])
print(f"{attribute}-activities-retriever_dot_recall@5", results[f"{attribute}-activities-retriever_dot_recall@5"])
print(f"{attribute}-activities-retriever_cosine_recall@5", results[f"{attribute}-activities-retriever_cosine_recall@5"])
