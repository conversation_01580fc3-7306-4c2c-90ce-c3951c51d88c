import json
import os
import pickle
import random
import re
from datetime import datetime

import yaml
from sentence_transformers import SentenceTransformer
from tqdm import tqdm

from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score


def gather_activities_tree(workflow, strip_templates=True):
    if isinstance(workflow, dict):
        activities = {}
        if "activity" in workflow:
            activity = workflow["activity"]
            if strip_templates:
                activity = activity.split("<", 1)[0]
            if activity in (
                "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity",
                "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity",
            ):
                activity = activity + "@" + workflow["params"]["UiPathActivityTypeId"]
        else:
            activity = None
        activities[activity] = []
        for _, v in workflow.items():
            item = gather_activities_tree(v, strip_templates)
            if item is not None:
                # activities[activity][k] = item
                activities[activity].append(item)
        if activity is None:
            activities = activities[activity]
        else:
            if len(activities[activity]) == 0:
                activities = activity
    elif isinstance(workflow, list):
        activities = []
        for v in workflow:
            item = gather_activities_tree(v, strip_templates)
            if item is not None:
                activities.append(item)
    else:
        activities = []
    if len(activities) == 0:
        return None
    if isinstance(activities, list) and len(activities) == 1:
        activities = activities[0]
    return activities


def gather_activities(workflow, strip_templates=True, include_thoughts=True):
    activities = []
    if isinstance(workflow, dict):
        if "activity" in workflow:
            activity = workflow["activity"]
            if strip_templates:
                activity = activity.split("<", 1)[0]
            if activity in (
                "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity",
                "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity",
            ):
                activity = activity + "@" + workflow["params"]["UiPathActivityTypeId"]
            if include_thoughts:
                thought = workflow.get("thought", "")
                activities.append({"activity": activity, "step": thought})
            else:
                activities.append(activity)
        for _, v in workflow.items():
            activities.extend(gather_activities(v, strip_templates))
    elif isinstance(workflow, list):
        for v in workflow:
            activities.extend(gather_activities(v, strip_templates))
    return activities


def load_workflow_yaml(file_path):
    ret = {"file_path": file_path}
    with open(file_path) as fin:
        data = yaml_load(fin)
    ret["query"] = data["description"]
    ret["workflow"] = data["process"]

    ret["steps"] = gather_activities(data["process"])
    ret["activities_tree"] = gather_activities_tree(data["process"])
    return ret


def load_sequences_yaml(file_path):
    ret = {"file_path": file_path}
    with open(file_path) as fin:
        data = yaml_load(fin)
    ret["query"] = data["description_sequence"]
    ret["workflow"] = {"workflow": data["process_sequence"]}

    ret["steps"] = gather_activities(data["process_sequence"])
    ret["activities_tree"] = gather_activities_tree(data["process_sequence"])
    return ret


def compute_iou_score(activities, activities2):
    return len(set(activities) & set(activities2)) / len(set(activities) | set(activities2))


def camel_case_split(identifier):
    matches = re.finditer(".+?(?:(?<=[a-z])(?=[A-Z])|(?<=[A-Z])(?=[A-Z][a-z])|$)", identifier)
    ret = " ".join([m.group(0) for m in matches])
    for ch in "._@-":
        if ch in ret:
            ret = ret.replace(ch, " ")
    ret = ret.replace("Ui Path", "UiPath")
    ret = ret.replace("G Suite", "GSuite")
    return ret


def load_activities_map():
    retrievers = {}
    retrievers_folder = "/workspace/data/Retrievers/ActivityRetriever"
    for file in os.listdir(retrievers_folder):
        if file.endswith(".pkl"):
            print(file)
            with open(f"{retrievers_folder}/{file}", "rb") as f:
                state = pickle.load(f)
            retrievers[file] = state

    activities_map = {}
    for key in retrievers:
        for activity in tqdm(retrievers[key]["activities"], f"gather activities from {key}"):
            activity_id = activity["fullActivityId"]
            if activity_id in activities_map:
                # print("Duplicate activity:", activity_id)
                continue
            activity["content"] = camel_case_split(activity.get("fullClassName", activity_id))
            activity["displayName"] = activity.get("displayName", camel_case_split(activity_id.split(".")[-1]))
            activity["description"] = activity.get("description", camel_case_split(activity_id))
            activity["category"] = camel_case_split(activity.get("mapped_category", "unknown"))
            # activity["mapped_category"] = camel_case_split(activity.get("mapped_category", "unknown"))
            activities_map[activity_id] = activity
    return activities_map


def load_workflows_map():
    source_paths = {
        "train": [
            "/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train",
            "/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/train",
        ],
        "test": [
            "/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test",
            "/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/test",
        ],
    }
    workflows_map = {k: {} for k in source_paths}
    for key, paths in source_paths.items():
        for path in paths:
            for fname in tqdm(os.listdir(path), f"workflows_map {key} {path}"):
                if not fname.endswith(".yaml"):
                    print(fname)
                    continue
                file_path = os.path.join(path, fname)
                file_data = load_workflow_yaml(file_path)
                workflows_map[key][file_path] = file_data
                # if len(workflows_map[key]) > 2:
                #     break
    return workflows_map


def load_sequences_map():
    source_paths = {
        "train": [
            "/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/train",
            "/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/train",
        ],
        "test": [
            "/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/test",
            "/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/test",
        ],
    }
    sequences_map = {k: {} for k in source_paths}
    for key, paths in source_paths.items():
        for path in paths:
            for folder in tqdm(os.listdir(path), f"sequences_map {key} {path}"):
                for fname in os.listdir(os.path.join(path, folder)):
                    if not fname.endswith(".yaml"):
                        print(fname)
                        continue
                    file_path = os.path.join(path, folder, fname)
                    file_data = load_sequences_yaml(file_path)
                    sequences_map[key][file_path] = file_data
                    # if len(sequences_map[key]) > 2:
                    #     break
    return sequences_map


fixed_scores = [
    ("print", "log message", "fixed_scores", 0.99),
    ("page count", "number of pages", "fixed_scores", 0.99),
    ("add new line to Excel", "write row to Excel", "fixed_scores", 0.9),
    ("append", "assign", "fixed_scores", 0.7),
    ("Email to Asana", "Create Asana task from email", "fixed_scores", 0.99),
    ("Email to Jira", "Create Jira task from email", "fixed_scores", 0.99),
    ("Email to Slack", "Send message on slack when receiving an email", "fixed_scores", 0.99),
    ("Generate text completion based on email subject and body", "Summarize Text", "fixed_scores", 0.6),
    ("Download from OneDrive and Upload to GDrive", "When a new file is uploaded to OneDrive store it in GDrive", "fixed_scores", 0.9),
    ("Check if the current item", "if", "fixed_scores", 0.8),
    ("Model an If-Then-Else condition in a workflow", "Check if the current item", 0.8),
    ("Generate text completion based on", "Generate text using a suitable Large Language Model (LLM)", "fixed_scores", 0.9),
    ("save to variable", "Set Variable Value", "fixed_scores", 0.9),
    ("save to variable", "Save a value to use in later activities", "fixed_scores", 0.9),
    ("set value to output argument", "Set Variable Value", "fixed_scores", 0.9),
    ("Read DataTable from Excel workbook", "Read excel file", "fixed_scores", 0.9),
    ("google sheet", "excel file on Gdrive", "fixed_scores", 0.9),
    ("Loop through each row", "For each row", "fixed_scores", 0.9),
    ("Sync Google Drive folder to OneDrive folder", "Download file from GDrive folder and upload to OneDrive folder", "fixed_scores", 0.7),
    ("Salesforce issue", "Salesforce case", "fixed_scores", 0.99),
    ("Save to Excel", "Create Excel file with", "fixed_scores", 0.8),
    ("When a file is added to a folder in OneDrive", "When a new file is uploaded to a folder in OneDrive", "fixed_scores", 0.99),
    ("store it in a OneDrive", "save file to OneDrive", "fixed_scores", 0.9),
    ("Important Gmails", "Important emails in Gmail", "fixed_scores", 0.9),
    ("GDrive", "Google Drive", "fixed_scores", 0.99),
    ("employee is hired in Workday", "adds an employee in Workday", "fixed_scores", 0.9),
    # ("", "", "fixed_scores", 0.9),
]
# k1: [[c111, c112], [c121, c122]
# k2: [[c211, c212], [c221, c222]]
# c111, c112 (0.9) > k1, c111 (0.8) > k1, k2 ~ c11, c21 (0.1) ~ c111, c121
exclusive_clusters = {
    "email": [
        [
            "Gmail",
            "GSuite",
            "Google Email",
            "on gmail received",
            "gmail email",
            "gmail message",
            "send email in gmail",
            "gmail notification",
        ],
        [
            "Outlook",
            "MicrosoftOffice365 email",
            "Microsoft Email",
            "Office365 email",
            "o365 email",
            "on email receive in outlook",
            "outlook email",
            "outlook message",
            "send email in outlook",
            "outlook notification",
        ],
    ],
    "message": [
        [
            "Slack",
            "Slack channel",
            "Slack chat message",
            "send Slack message",
            "notify me on slack",
            "slack post",
            "slack notification",
            "notify team on slack",
            "post on Slack channel",
        ],
        ["Teams", "MSTeams", "Teams channel", "Teams chat message", "Microsoft Teams", "send Teams message", "notify me on teams", "post on MSTeams channel"],
    ],
    "files": [
        ["local file", "read file from local folder", "write file to local folder", "local storage", "local directory", "local file system"],
        [
            "OneDrive",
            "One Drive",
            "read file from onedrive",
            "write file to onedrive",
            "MicrosoftOffice365 file",
            "onedrive spreadsheet",
            "onedrive document",
            "onedrive folder",
        ],
        [
            "GDrive",
            "Google Drive",
            "G Drive",
            "Google Cloud",
            "read file from gdrive",
            "write file to google drive",
            "GSuite file",
            "gdrive spreadsheet",
            "gdrive document",
            "gdrive folder",
        ],
    ],
    "applications with accounts": [
        [
            "workday",
            "Workday",
            "workday employee",
            "workday account",
            "workday hire",
            "workday pre-hire",
            "create workday prehire",
            "workday worker",
            "workday position",
            "workday candidate",
            "workday pto",
            "workday time off request",
        ],
        ["salesforce", "salesforce account", "salesforce opportunity", "salesforce lead"],
        ["bamboo", "bamboo hr", "bamboohr", "BambooHR", "bamboo employee", "bamboo account", "bamboo time off request", "bamboo employee file"],
    ],
}
related_clusters = {"email": {"files"}, "files": {"email"}}


def load_static_dataset():
    train_static = []
    for fs in tqdm(fixed_scores, "fixed_scores"):
        sf0e = stella.encode(fs[0], normalize_embeddings=True)
        mf0e = msmarco.encode(fs[0], normalize_embeddings=True)
        sf1e = stella.encode(fs[1], normalize_embeddings=True)
        mf1e = msmarco.encode(fs[1], normalize_embeddings=True)
        train_static.append(
            {
                "sentence1": fs[0],
                "sentence2": fs[1],
                "score": fs[-1],
                "stella_score": float(sf0e.dot(sf1e)),
                "msmarco_score": float(mf0e.dot(mf1e)),
                "bucket": "fixed_scores",
            }
        )
    stella_cluster_embeddings = {}
    msmarco_cluster_embeddings = {}
    for k, vcs in tqdm(exclusive_clusters.items(), "static clusters"):
        if k not in stella_cluster_embeddings:
            stella_cluster_embeddings[k] = stella.encode(k, normalize_embeddings=True)
        ske = stella_cluster_embeddings[k]
        if k not in msmarco_cluster_embeddings:
            msmarco_cluster_embeddings[k] = msmarco.encode(k, normalize_embeddings=True)
        mke = msmarco_cluster_embeddings[k]
        for vs in vcs:
            for i, v in enumerate(vs):
                if v not in stella_cluster_embeddings:
                    stella_cluster_embeddings[v] = stella.encode(v, normalize_embeddings=True)
                sve = stella_cluster_embeddings[v]
                if v not in msmarco_cluster_embeddings:
                    msmarco_cluster_embeddings[v] = msmarco.encode(v, normalize_embeddings=True)
                mve = msmarco_cluster_embeddings[v]
                train_static.append(
                    {
                        "sentence1": k,
                        "sentence2": v,
                        "stella_score": float(ske.dot(sve)),
                        "msmarco_score": float(mke.dot(mve)),
                        "score": 0.8,
                        "bucket": "fixed_scores",
                    }
                )
                for v2 in vs[i + 1 :]:
                    if v2 not in stella_cluster_embeddings:
                        stella_cluster_embeddings[v2] = stella.encode(v2, normalize_embeddings=True)
                    sv2e = stella_cluster_embeddings[v2]
                    if v2 not in msmarco_cluster_embeddings:
                        msmarco_cluster_embeddings[v2] = msmarco.encode(v2, normalize_embeddings=True)
                    mv2e = msmarco_cluster_embeddings[v2]
                    train_static.append(
                        {
                            "sentence1": v,
                            "sentence2": v2,
                            "stella_score": float(sve.dot(sv2e)),
                            "msmarco_score": float(mve.dot(mv2e)),
                            "score": 0.95,
                            "bucket": "fixed_scores",
                        }
                    )
                for k2, vcs2 in exclusive_clusters.items():
                    if k == k2:
                        continue
                    if k2 not in stella_cluster_embeddings:
                        stella_cluster_embeddings[k2] = stella.encode(k2, normalize_embeddings=True)
                    sk2e = stella_cluster_embeddings[k2]
                    if k2 not in msmarco_cluster_embeddings:
                        msmarco_cluster_embeddings[k2] = msmarco.encode(k2, normalize_embeddings=True)
                    mk2e = msmarco_cluster_embeddings[k2]
                    train_static.append(
                        {
                            "sentence1": k,
                            "sentence2": k2,
                            "stella_score": float(ske.dot(sk2e)),
                            "msmarco_score": float(mke.dot(mk2e)),
                            "score": 0.2,
                            "bucket": "fixed_scores",
                        }
                    )
                    if k2 in related_clusters.get(k, {}):
                        continue
                    for vs2 in vcs2:
                        for v2 in vs2:
                            if v2 not in stella_cluster_embeddings:
                                stella_cluster_embeddings[v2] = stella.encode(v2, normalize_embeddings=True)
                            sv2e = stella_cluster_embeddings[v2]
                            if v2 not in msmarco_cluster_embeddings:
                                msmarco_cluster_embeddings[v2] = msmarco.encode(v2, normalize_embeddings=True)
                            mv2e = msmarco_cluster_embeddings[v2]
                            train_static.append(
                                {
                                    "sentence1": v,
                                    "sentence2": v2,
                                    "stella_score": float(sve.dot(sv2e)),
                                    "msmarco_score": float(mve.dot(mv2e)),
                                    "score": 0.2,  # similar ones (e.g. "Gmail" and "GDrive") should be excluded by related_clusters
                                    "bucket": "fixed_scores",
                                }
                            )
    return train_static


def query_similarity(wf1_map, wf2_map):
    query = wf1_map["query"]
    stella_emb = wf1_map["query_stella_embedding"]
    msmarco_emb = wf1_map["query_msmarco_embedding"]
    workflow = wf1_map["workflow"]
    file_path1 = wf1_map["file_path"]

    query2 = wf2_map["query"]
    stella_emb2 = wf2_map["query_stella_embedding"]
    msmarco_emb2 = wf2_map["query_msmarco_embedding"]
    workflow2 = wf2_map["workflow"]
    file_path2 = wf2_map["file_path"]
    iou_score = compute_iou_score(
        [s["activity"] for s in wf1_map["steps"] if s["activity"] not in FILTERED_ACTIVITIES],
        [s["activity"] for s in wf2_map["steps"] if s["activity"] not in FILTERED_ACTIVITIES],
    )
    if iou_score < 0.01:
        edit_score = iou_score
    else:
        edit_score, _, _, _ = core_wf_workflow_edit_score(workflow, workflow2, param_match="levenshtein", filter_params=None, compute_mapping=False)
    score = 0.55 * edit_score + 0.45

    return {
        "sentence1": query,
        "sentence2": query2,
        "score": score,
        "edit_score": edit_score,
        "stella_score": float(stella_emb.dot(stella_emb2)),
        "msmarco_score": float(msmarco_emb.dot(msmarco_emb2)),
        "iou_score": iou_score,
        "file_path1": file_path1,
        "file_path2": file_path2,
        "bucket": f"query_similarity_{int(10 * score)}",
    }


def activity_matches(file_path, step, activities_map, activity_id, item):
    stella_score = float(step["step_stella_embedding"].dot(activities_map[activity_id][f"{item}_stella_embedding"]))
    msmarco_score = float(step["step_msmarco_embedding"].dot(activities_map[activity_id][f"{item}_msmarco_embedding"]))
    if match:
        score = max(0.7, msmarco_score if ACTIVITIES_SCORE == "msmarco" else stella_score)
        bucket = f"activity_step_{item}_{int(10 * score)}_positive"
    elif category_match:
        score = max(0.55, min(0.65, msmarco_score if ACTIVITIES_SCORE == "msmarco" else stella_score))
        bucket = f"activity_step_{item}_{int(10 * score)}_category_match"
    else:
        score = min(0.5, msmarco_score if ACTIVITIES_SCORE == "msmarco" else stella_score)
        bucket = f"activity_step_{item}_{int(10 * score)}_negative"

    return {
        "sentence1": step["step"],
        "sentence2": activities_map[activity_id][item],
        "score": score,
        "stella_score": stella_score,
        "msmarco_score": msmarco_score,
        "file_path": file_path,
        "activity_id": activity_id,
        "activity_name": activities_map[activity_id]["fullClassName"],
        "bucket": bucket,
    }


if __name__ == "__main__":
    PREDICTIONS = ["activities", "demonstrations"]
    # PREDICTIONS = ["activities"]
    # PREDICTIONS = ["demonstrations"]
    TRAIN_SAMPLE_LIMIT = 20 * 1000
    TEST_SAMPLE_LIMIT = 1000
    EVAL_SAMPLE_LIMIT = 1000
    ACTIVITIES_SCORE = "stella"
    NEGATIVES_ACTIVITIES_MATCHES = 100
    FILTERED_ACTIVITIES = set(["UiPath.Core.Activities.ManualTrigger"])

    version = datetime.now().strftime("%Y%m%d_%H")
    os.makedirs(f"dataset_{version}", exist_ok=True)

    stella = SentenceTransformer("dunzhang/stella_en_400M_v5", trust_remote_code=True)
    msmarco = SentenceTransformer("msmarco-distilbert-base-tas-b", trust_remote_code=True)

    train_static = load_static_dataset()
    with open(f"dataset_{version}/train_static.yaml", "w") as fout:
        yaml.dump(train_static, fout)

    activities_map = load_activities_map()
    with open(f"dataset_{version}/activities_map.yaml", "w") as fout:
        yaml.dump(activities_map, fout)

    workflows_map_raw = load_workflows_map()
    with open(f"dataset_{version}/workflows_map_raw.yaml", "w") as fout:
        yaml.dump(workflows_map_raw, fout)

    sequences_map = load_sequences_map()
    with open(f"dataset_{version}/sequences_map.yaml", "w") as fout:
        yaml.dump(sequences_map, fout)

    # match activities in workflows
    unmatched_activities = set()
    workflows_map = {}
    for name, subset in workflows_map_raw.items():
        workflows_map[name] = {}
        for path, workflow in subset.items():
            all_found = True
            for step in workflow["steps"]:
                if step["activity"] not in activities_map:
                    all_found = False
                    unmatched_activities.add(step["activity"])
                    print(f"ERROR loading {path} {step['activity']}")
                else:
                    step["content"] = activities_map[step["activity"]]["content"]
                    step["displayName"] = activities_map[step["activity"]]["displayName"]
                    step["description"] = activities_map[step["activity"]]["description"]
                    step["category"] = activities_map[step["activity"]]["category"]
            if all_found:
                workflows_map[name][path] = workflow
    with open(f"dataset_{version}/workflows_map.yaml", "w") as fout:
        yaml.dump(workflows_map, fout)
    unmatched_activities = sorted(unmatched_activities)
    with open(f"dataset_{version}/unmatched_activities.yaml", "w") as fout:
        yaml.dump(unmatched_activities, fout)

    for activity_id, _ in tqdm(activities_map.items(), "embedding activity"):
        for item in ("content", "displayName", "description", "category"):
            activities_map[activity_id][f"{item}_stella_embedding"] = stella.encode(activities_map[activity_id][item], normalize_embeddings=True)
            activities_map[activity_id][f"{item}_msmarco_embedding"] = msmarco.encode(activities_map[activity_id][item], normalize_embeddings=True)

    for setname in workflows_map:
        for file_path in tqdm(workflows_map[setname], f"query_embedding workflows {setname}"):
            workflows_map[setname][file_path]["query_stella_embedding"] = stella.encode(workflows_map[setname][file_path]["query"], normalize_embeddings=True)
            workflows_map[setname][file_path]["query_msmarco_embedding"] = msmarco.encode(workflows_map[setname][file_path]["query"], normalize_embeddings=True)
            for step in workflows_map[setname][file_path]["steps"]:
                step["step_stella_embedding"] = stella.encode(step["step"], normalize_embeddings=True)
                step["step_msmarco_embedding"] = msmarco.encode(step["step"], normalize_embeddings=True)

    for setname in sequences_map:
        for file_path in tqdm(sequences_map[setname], f"query_embedding sequences {setname}"):
            sequences_map[setname][file_path]["query_stella_embedding"] = stella.encode(sequences_map[setname][file_path]["query"], normalize_embeddings=True)
            sequences_map[setname][file_path]["query_msmarco_embedding"] = msmarco.encode(sequences_map[setname][file_path]["query"], normalize_embeddings=True)

    train_query_similarities = []
    if "demonstrations" in PREDICTIONS:
        train_paths = list(workflows_map["train"].keys()) + list(sequences_map["train"].keys())
    else:
        train_paths = []
    for i, train_path in enumerate(tqdm(train_paths, "train_query_similarities")):
        mapp = workflows_map["train"] if train_path in workflows_map["train"] else sequences_map["train"]
        for train_path2 in train_paths[i + 1 :]:
            mapp2 = workflows_map["train"] if train_path2 in workflows_map["train"] else sequences_map["train"]
            train_query_similarities.append(query_similarity(mapp[train_path], mapp2[train_path2]))
    with open(f"dataset_{version}/train_query_similarities.json", "w") as fout:
        json.dump(train_query_similarities, fout, sort_keys=True, indent=2)

    test_query_similarities = []
    if "demonstrations" in PREDICTIONS:
        test_paths = list(workflows_map["test"].keys()) + list(sequences_map["test"].keys())
        train_paths = list(workflows_map["train"].keys()) + list(sequences_map["train"].keys())
    else:
        test_paths = []
        train_paths = []
    for test_path in tqdm(test_paths, "test_query_similarities"):
        mapp = workflows_map["test"] if test_path in workflows_map["test"] else sequences_map["test"]
        for train_path in train_paths:
            mapp2 = workflows_map["train"] if train_path in workflows_map["train"] else sequences_map["train"]
            test_query_similarities.append(query_similarity(mapp[test_path], mapp2[train_path]))
    with open(f"dataset_{version}/test_query_similarities.json", "w") as fout:
        json.dump(test_query_similarities, fout, sort_keys=True, indent=2)

    train_activity_matches = []
    if "activities" in PREDICTIONS:
        train_paths = list(workflows_map["train"].keys())
    else:
        train_paths = []
    for train_path in tqdm(train_paths, "train_activity_matches"):
        for step in workflows_map["train"][train_path]["steps"]:
            for activity_id in activities_map:
                match = step["activity"] == activity_id
                category_match = activities_map[step["activity"]]["category"] == activities_map[activity_id]["category"]
                if not match and random.randint(0, len(activities_map)) > NEGATIVES_ACTIVITIES_MATCHES:
                    continue
                for item in ("content", "displayName", "description", "category"):
                    train_activity_matches.append(activity_matches(train_path, step, activities_map, activity_id, item))
    with open(f"dataset_{version}/train_activity_matches.json", "w") as fout:
        json.dump(train_activity_matches, fout, sort_keys=True, indent=2)

    test_activity_matches = []
    if "activities" in PREDICTIONS:
        test_paths = list(workflows_map["test"].keys())
    else:
        test_paths = []
    for test_path in tqdm(test_paths, "test_activity_matches"):
        for step in workflows_map["test"][test_path]["steps"]:
            for activity_id in activities_map:
                match = step["activity"] == activity_id
                category_match = activities_map[step["activity"]]["category"] == activities_map[activity_id]["category"]
                if not match and random.randint(0, len(activities_map)) > NEGATIVES_ACTIVITIES_MATCHES:
                    continue
                for item in ("content", "displayName", "description", "category"):
                    test_activity_matches.append(activity_matches(train_path, step, activities_map, activity_id, item))
    with open(f"dataset_{version}/test_activity_matches.json", "w") as fout:
        json.dump(test_activity_matches, fout, sort_keys=True, indent=2)

    # format1 - readable
    #    sentence1, sentence2, score, bucket, other_useful_info
    # format 2 - for train (only info allowed in train for file1)
    # file1
    #    sentence1, sentence2, score
    # file2
    #    each type of sample: list of ids of that type from file1 (id is the count of the sample)
    #
    # format 3 - for evals (different format than train)
    #    sentence1 random samples with counts for each type of sample
    #    sentence2
    #    score

    readable_train_scores_by_bucket_raw, readable_test_scores_by_bucket_raw = {}, {}
    for subset in (
        train_static,
        train_query_similarities,
        train_activity_matches,
    ):
        for sample in tqdm(subset, "readable_train_scores_by_bucket"):
            bucket = sample["bucket"]
            readable_train_scores_by_bucket_raw.setdefault(bucket, {"stella_score_sum": 0, "msmarco_score_sum": 0, "score_sum": 0, "count": 0})
            readable_train_scores_by_bucket_raw[bucket]["stella_score_sum"] += sample["stella_score"]
            readable_train_scores_by_bucket_raw[bucket]["msmarco_score_sum"] += sample["msmarco_score"]
            readable_train_scores_by_bucket_raw[bucket]["score_sum"] += sample["score"]
            readable_train_scores_by_bucket_raw[bucket]["count"] += 1
    for subset in (
        test_query_similarities,
        test_activity_matches,
    ):
        for sample in tqdm(subset, "readable_test_scores_by_bucket"):
            bucket = sample["bucket"]
            readable_test_scores_by_bucket_raw.setdefault(bucket, {"stella_score_sum": 0, "msmarco_score_sum": 0, "score_sum": 0, "count": 0})
            readable_test_scores_by_bucket_raw[bucket]["stella_score_sum"] += sample["stella_score"]
            readable_test_scores_by_bucket_raw[bucket]["msmarco_score_sum"] += sample["msmarco_score"]
            readable_test_scores_by_bucket_raw[bucket]["score_sum"] += sample["score"]
            readable_test_scores_by_bucket_raw[bucket]["count"] += 1

    readable_train_dataset, readable_test_dataset = [], []
    for subset in (
        train_static,
        train_query_similarities,
        train_activity_matches,
    ):
        for sample in tqdm(subset, "readable_train_dataset"):
            bucket = sample["bucket"]
            raw_bucket_size = readable_train_scores_by_bucket_raw[bucket]["count"]
            if raw_bucket_size > TRAIN_SAMPLE_LIMIT:
                if random.randint(0, raw_bucket_size) < TRAIN_SAMPLE_LIMIT:
                    readable_train_dataset.append(sample)
            else:
                readable_train_dataset.append(sample)
    for subset in (
        test_query_similarities,
        test_activity_matches,
    ):
        for sample in tqdm(subset, "readable_test_dataset"):
            bucket = sample["bucket"]
            raw_bucket_size = readable_test_scores_by_bucket_raw[bucket]["count"]
            if raw_bucket_size > TEST_SAMPLE_LIMIT:
                if random.randint(0, raw_bucket_size) < TEST_SAMPLE_LIMIT:
                    readable_test_dataset.append(sample)
            else:
                readable_test_dataset.append(sample)

    with open(f"dataset_{version}/readable_train_scores_by_bucket_raw.json", "w") as fout:
        json.dump(readable_train_scores_by_bucket_raw, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/readable_test_scores_by_bucket_raw.json", "w") as fout:
        json.dump(readable_test_scores_by_bucket_raw, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/readable_train_dataset.json", "w") as fout:
        json.dump(readable_train_dataset, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/readable_test_dataset.json", "w") as fout:
        json.dump(readable_test_dataset, fout, sort_keys=True, indent=2)

    train_dataset, test_dataset = [], []
    train_sample_ids, test_sample_ids = {}, {}
    train_dataset_by_bucket, test_dataset_by_bucket = {}, {}

    for sample in tqdm(readable_train_dataset, "train_sample_ids"):
        train_dataset.append({"sentence1": sample["sentence1"], "sentence2": sample["sentence2"], "score": sample["score"]})
        bucket = sample["bucket"]
        train_sample_ids.setdefault(bucket, [])
        train_sample_ids[bucket].append(len(train_dataset) - 1)
        train_dataset_by_bucket.setdefault(bucket, {})
        train_dataset_by_bucket[bucket].setdefault("count", 0)
        train_dataset_by_bucket[bucket]["count"] += 1
        train_dataset_by_bucket[bucket].setdefault("scores_sum", 0)
        train_dataset_by_bucket[bucket]["scores_sum"] += sample["score"]

    for sample in tqdm(readable_test_dataset, "test_sample_ids"):
        test_dataset.append({"sentence1": sample["sentence1"], "sentence2": sample["sentence2"], "score": sample["score"]})
        bucket = sample["bucket"]
        test_sample_ids.setdefault(bucket, [])
        test_sample_ids[bucket].append(len(test_dataset) - 1)
        test_dataset_by_bucket.setdefault(bucket, {})
        test_dataset_by_bucket[bucket].setdefault("count", 0)
        test_dataset_by_bucket[bucket]["count"] += 1
        test_dataset_by_bucket[bucket].setdefault("scores_sum", 0)
        test_dataset_by_bucket[bucket]["scores_sum"] += sample["score"]

    with open(f"dataset_{version}/train_dataset_by_bucket.json", "w") as fout:
        json.dump(train_dataset_by_bucket, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/test_dataset_by_bucket.json", "w") as fout:
        json.dump(test_dataset_by_bucket, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/train_dataset.json", "w") as fout:
        json.dump(train_dataset, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/test_dataset.json", "w") as fout:
        json.dump(test_dataset, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/train_sample_ids.json", "w") as fout:
        json.dump(train_sample_ids, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/test_sample_ids.json", "w") as fout:
        json.dump(test_sample_ids, fout, sort_keys=True, indent=2)

    train_eval_samples_raw, test_eval_samples_raw = [], []
    train_eval_samples = {"sentence1": [], "sentence2": [], "score": []}
    test_eval_samples = {"sentence1": [], "sentence2": [], "score": []}
    for _, ids in tqdm(train_sample_ids.items(), "train_eval_samples"):
        if len(ids) < EVAL_SAMPLE_LIMIT:
            sample_ids = ids
        else:
            sample_ids = random.sample(ids, EVAL_SAMPLE_LIMIT)
        for i in sample_ids:
            train_eval_samples["sentence1"].append(train_dataset[i]["sentence1"])
            train_eval_samples["sentence2"].append(train_dataset[i]["sentence2"])
            train_eval_samples["score"].append(train_dataset[i]["score"])
            train_eval_samples_raw.append(train_dataset[i])
    for _, ids in tqdm(test_sample_ids.items(), "test_eval_samples"):
        if len(ids) < EVAL_SAMPLE_LIMIT:
            sample_ids = ids
        else:
            sample_ids = random.sample(ids, EVAL_SAMPLE_LIMIT)
        for i in sample_ids:
            test_eval_samples["sentence1"].append(test_dataset[i]["sentence1"])
            test_eval_samples["sentence2"].append(test_dataset[i]["sentence2"])
            test_eval_samples["score"].append(test_dataset[i]["score"])
            test_eval_samples_raw.append(test_dataset[i])

    with open(f"dataset_{version}/train_eval_samples.json", "w") as fout:
        json.dump(train_eval_samples, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/test_eval_samples.json", "w") as fout:
        json.dump(test_eval_samples, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/train_eval_samples_raw.json", "w") as fout:
        json.dump(train_eval_samples_raw, fout, sort_keys=True, indent=2)
    with open(f"dataset_{version}/test_eval_samples_raw.json", "w") as fout:
        json.dump(test_eval_samples_raw, fout, sort_keys=True, indent=2)
