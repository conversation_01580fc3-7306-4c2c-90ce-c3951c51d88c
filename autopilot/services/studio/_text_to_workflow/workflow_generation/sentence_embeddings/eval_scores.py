import json
import random

import numpy as np
import yaml
from sentence_transformers import SentenceTransformer, evaluation
from tqdm import tqdm

# from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

# Define the model. Either from scratch of by loading a pre-trained model
# model = SentenceTransformer('msmarco-distilbert-base-tas-b')
# eval on devset
# average error 0.6100480594330648 0.13407913542250383 224803
# average score for 1 0.1627719705999146 0.1397190445297333 10789
# average score for 0.0 0.6468709976329466 0.05823676451395598 202433
# average score for 0.7 0.12320298955495687 0.062396088445923585 389
# average score for 0.3 0.39211262394224783 0.0876503995759134 11192
# eval on evalset
# average error 0.5927388234310383 0.16841223788964285 12594
# average score for 1 0.1503429751956757 0.14348582735873366 689
# average score for 0.0 0.664452762636993 0.06803273031798289 9972
# average score for 0.7 0.10279554150520123 0.057930419005330504 109
# average score for 0.3 0.39706072880510707 0.08871675403242521 1824

# model = SentenceTransformer('checkpoints_train_multisampled/20000') # 0.8864
# model = SentenceTransformer('output_train_multisampled') # 0.8991
# model = SentenceTransformer('checkpoints_train_on_full_export/13000') # 0.79
# eval on devset - 110 it/s
# average error 0.03033965093672319 0.07009369060283666 224803
# average score for 1 0.058657775368289075 0.13993191485531714 10789
# average score for 0.0 0.02102454765460708 0.04802361460634974 202433
# average score for 0.7 0.12893906547508382 0.11691385934540391 389
# average score for 0.3 0.1680992011476795 0.12634104320148112 11192
# eval on evalset - 110 it/s
# average error 0.08299874005904785 0.1311441483820636 12594
# average score for 1 0.233946475953253 0.30665317271822257 689
# average score for 0.0 0.04951304092034453 0.08259183266730041 9972
# average score for 0.7 0.15465327509499469 0.13889618759638048 109
# average score for 0.3 0.2047674119127342 0.12211340756928853 1824
# model = SentenceTransformer("output_train_with_demo") # 0.8968
# model = SentenceTransformer("output_train_with_demo_20231129_v3") # 0.8996
# model = SentenceTransformer("output_train_mpnet") # 0.919
# model = SentenceTransformer("checkpoints_train_mpnet/20000") # 0.9053
# model = SentenceTransformer("output_train_msmarco_train3") # 0.9012, top5 89.62 old eval 0.909
# model = SentenceTransformer("output_train_noampnowd")
# model = SentenceTransformer("msmarco-trained") # 0.7996, top5 94.07 current prod v0.0.2
# model = SentenceTransformer("checkpoints_train_all-mpnet-base_20240319/62500") # 0.7703, top5 49.62
# model = SentenceTransformer("all-mpnet-base-v2") # 0.6707, top5 53.33
# model = SentenceTransformer('intfloat/multilingual-e5-small') # 0.70, top5 54.81
# model = SentenceTransformer('msmarco-distilbert-dot-v5') # 0.6978, top5 57.77
# model = SentenceTransformer('msmarco-distilbert-base-tas-b') # 0.7013, top5 61.48 20240403 un-trained
# model = SentenceTransformer("Salesforce/SFR-Embedding-Mistral") # 0.6399, top5 66.66 (might be new best)
# model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1") # 0.7222, top5 74.07 (might be new best)
# model = SentenceTransformer('output_train_msmarco_20240319') # 0.9377, top5 96.29, best 20240403
# model = SentenceTransformer('output_train_msmarco2_20240320') # 0.9350, top5 92.59
# model = SentenceTransformer('output_train_mxbai_20240403') # 0.9238, top5 93.33
# model = SentenceTransformer('output_train_mxbaiv2_20240403') # 0.9319, top5 96.29
# model = SentenceTransformer('output_train_msmarco_20240404') # 93.47, top5 96.29

# ds 20240404
# model = SentenceTransformer("msmarco-trained") # 78.39, top5 83.7 current prod v0.0.2
# model = SentenceTransformer('output_train_msmarco_20240319') # 85.44, top5 88.14, best 20240403
# model = SentenceTransformer('output_train_msmarco_20240404') # 93.52, top5 88.14
# model = SentenceTransformer("output_train_msmarco_20240404_new_ds")  # 94.57, top5 99.25

# ds 20240716
# model = SentenceTransformer("output_train_msmarco_20240716")  # test_20240716: 94.16, top5 95.72 train_20240716: 95.55 top5 99.94
# ['If', 'Check the attachment file extension', 0.97, 5] (0.97, 0.4724089503288269, 0.49759104967117307)
# ['Check the attachment file extension', 'If', 0.97, 5] (0.97, 0.4724089503288269, 0.49759104967117307)
# ['Check the attachment file extension', 'If', 0.97, 4] (0.97, 0.4724089503288269, 0.49759104967117307)
# ['Check the attachment file extension', 'If', 0.97, 5] (0.97, 0.4724089503288269, 0.49759104967117307)
# ['Check the attachment file extension', 'If there are any unread Outlook emails', 0.97, 5] (0.97, 0.4715123772621155, 0.4984876227378845)
# ['If email is from uipath address', 'Check the attachment file extension', 0.97, 5] (0.97, 0.46237242221832275, 0.5076275777816772)
# ['Insert DataTable in Document', 'WordInsertDataTable', 0.97, 4] (0.97, 0.4589880108833313, 0.5110119891166687)
# ['Sync Google Drive folder to OneDrive folder', 'Sync ExampleFolder folder in OneDrive to Google Drive', 0.4520833333333332, 13] (0.4520833333333332, 0.9678642153739929, 0.5157808820406597)
# ['Clone DataTable and save to variable', 'Extract target currency exchange rate and set value to output argument', 0.97, 5] (0.97, 0.4465242028236389, 0.5234757971763611)
# ['Increment SheetNumber', 'Save a value to use in later activities.', 0.97, 3] (0.97, 0.4152013063430786, 0.5547986936569214)
# ['Find and load Workday employee based on ID', 'Get Record', 0.97, 4] (0.97, 0.4098321199417114, 0.5601678800582885)
# ['Increment SheetNumber', 'Set Variable Value', 0.97, 4] (0.97, 0.409204363822937, 0.560795636177063)
# ['Download attachments from Outlook email', 'Download Email Attachments', 0.4307692307692308, 10] (0.4307692307692308, 0.9928327202796936, 0.5620634895104628)
# ['Download email attachments', 'Download attachments from Outlook email', 0.4307692307692308, 10] (0.4307692307692308, 0.9928327202796936, 0.5620634895104628)
# ['Increment SheetNumber', 'Extract target currency exchange rate and set value to output argument', 0.97, 5] (0.97, 0.36632224917411804, 0.6036777508258819)
# ['Increment SheetNumber', 'Assign email value', 0.97, 5] (0.97, 0.3623617887496948, 0.6076382112503051)
# ['Increment SheetNumber', 'Clone DataTable and save to variable', 0.97, 5] (0.97, 0.3025653660297394, 0.6674346339702606)
# ['Clone DataTable and save to variable', 'Set Variable Value', 0.97, 4] (0.97, 0.29779794812202454, 0.6722020518779754)
# ['Clone DataTable and save to variable', 'Save a value to use in later activities.', 0.97, 3] (0.97, 0.29663461446762085, 0.6733653855323791)
# ['Clone DataTable and save to variable', 'Assign email value', 0.97, 5] (0.97, 0.24217116832733154, 0.7278288316726684)
# topk errors on train
# ['Retrieve a record in Dropbox using UiPath Integration Service.', 'Delete a record in Box.', 0.31294117647058817, 11] (0.31294117647058817, 0.4947907030582428, 0.18184952658765463)
# ['Search for payments based on given criteria in Stripe.', 'List all records in Sugar Sell.', 0.24705882352941172, 11] (0.24705882352941172, 0.4314533472061157, 0.184394523676704)
# ['Suspend a member from a team in Dropbox.', 'Triggered when an existing file is moved to trash in Box.', 0.28865979381443296, 11] (0.28865979381443296, 0.47399699687957764, 0.18533720306514467)
# ['Throw an exception previously caught in an exception handling block.', 'Execute activities in an exception handling block.', 0.75, 6] (0.75, 0.9410406351089478, 0.19104063510894775)
# ['Triggered when a new company is created in HubSpot CRM.', 'Trigger when a new application is created in Greenhouse.', 0.4918918918918918, 11] (0.4918918918918918, 0.29872623085975647, 0.19316566103213534)
# ['Perform activities on each application group in Azure Virtual Desktop that matches the filter criteria.', 'Perform activities on each session host within a specified host pool in Azure Virtual Desktop.', 0.75, 6] (0.75, 0.9431724548339844, 0.19317245483398438)
# ['Write data to a cell in Microsoft Excel.', 'Automatically fills a range of cells in Microsoft Excel.', 0.4666666666666667, 9] (0.4666666666666667, 0.6664297580718994, 0.19976309140523274)
# ['Create or update a network security group in Azure.', 'Create or update a security rule in a network security group in Azure.', 0.75, 6] (0.75, 0.9516197443008423, 0.20161974430084229)
# ['Microsoft Email', 'Microsoft Teams', 0.5133333333333333, 2] (0.5133333333333333, 0.31077098846435547, 0.20256234486897784)
# ['Microsoft Teams', 'Microsoft Email', 0.5133333333333333, 2] (0.5133333333333333, 0.31077098846435547, 0.20256234486897784)
# ['Create a folder at the specified location in OneDrive or SharePoint.', 'Move a file or folder to a specified destination folder in OneDrive or SharePoint.', 0.5319999999999999, 9] (0.5319999999999999, 0.7419465780258179, 0.20994657802581795)
# ['Delete a specified blob in Azure Blob Storage.', 'Delete a specified blob container in Azure.', 0.75, 6] (0.75, 0.9624974131584167, 0.21249741315841675)
# ['Find and replace values in Microsoft Excel.', 'Execute a sequence of activities in Microsoft Excel.', 0.75, 6] (0.75, 0.5326329469680786, 0.2173670530319214)
# ['Write information in a selected cell in Excel Online.', 'Retrieve the most recent email matching the search criteria from Microsoft Office 365 Outlook.', 0.75, 6] (0.75, 0.5259894728660583, 0.22401052713394165)
# ['Move File/Folder', 'Write Emails Summary to File', 0.22272727272727275, 10] (0.22272727272727275, 0.4572156071662903, 0.23448833443901754)
# ['MicrosoftOffice365 file', 'MicrosoftOffice365 email', 0.6255319148936169, 2] (0.6255319148936169, 0.38391223549842834, 0.24161967939518858)
# ['Manual Trigger', 'If', 0.0, 10] (0.0, 0.24875390529632568, 0.24875390529632568)
# ['Download Gmail attachments for further use.', 'Send an email message using Google Workspace (Gmail).', 0.75, 6] (0.75, 0.4190850555896759, 0.3309149444103241)
# ['Search For Excel File', 'Search for the file', 0.5249999999999999, 10] (0.5249999999999999, 0.9162927865982056, 0.39129278659820566)
# ['Reply to the email with the label in the body content', 'Send email with the issue title using Outlook', 0.37142857142857133, 10] (0.37142857142857133, 0.8567754030227661, 0.4853468315941948)

# model = SentenceTransformer("output_train_msmarco_20240717")  # test_20240717: 94.45, top5 95.72 train_20240717: 95.55 top5 99.94
# topk errors on testset
# ['Add Data Table to Slide', 'Replace a shape in a Microsoft PowerPoint presentation with data from a DataTable.', 0.97, 3] (0.97, 0.5943716764450073, 0.37562832355499265)
# ['Get File or Folder', 'Get file or folder', 0.6222222222222221, 9] (0.6222222222222221, 0.9999999403953552, 0.3777777181731331)
# ['Unzip files from the new zip file', 'Extract/Unzip Files', 0.97, 4] (0.97, 0.5922054648399353, 0.37779453516006467)
# ['Paste Chart/Picture into Document', 'Paste content from the clipboard into Microsoft Word.', 0.97, 3] (0.97, 0.5796464681625366, 0.39035353183746335)
# ['Find and load Workday employee based on ID', 'Get Record', 0.97, 4] (0.97, 0.5695144534111023, 0.4004855465888977)
# ['Trigger when a new employee is created in Workday', 'Find and load Workday employee based on ID', 0.3384615384615384, 8] (0.3384615384615384, 0.7406810522079468, 0.40221951374640835)
# ['Invoke custom code to populate a DataTable with sales data', 'Programming', 0.7, 7] (0.7, 0.2970770597457886, 0.4029229402542114)
# ['Send GET HTTP request to exchangerate-api.com', 'Compose and execute HTTP requests to interact with web services and retrieve responses.', 0.97, 3] (0.97, 0.564802885055542, 0.405197114944458)
# ['Send GET HTTP request to exchangerate-api.com', 'HTTP Request', 0.97, 4] (0.97, 0.5564233064651489, 0.41357669353485105)
# ['Save Document As', 'WordSaveAs', 0.97, 4] (0.97, 0.5505836009979248, 0.41941639900207517)
# ['GSuite Gmail', 'Gmail', 0.4117647058823529, 12] (0.4117647058823529, 0.8390223383903503, 0.4272576325079974)
# ['Unzip files from the new zip file', 'Extracts all contents of a zipped (compressed) file.', 0.97, 3] (0.97, 0.5169541835784912, 0.45304581642150876)
# ['Invoke custom code to populate a DataTable with sales data', 'Invoke Code', 0.97, 4] (0.97, 0.4420480728149414, 0.5279519271850586)
# ['Upload a file to a folder in OneDrive', 'Upload the merged PDF file to a specified Google Drive folder', 0.39999999999999997, 10] (0.39999999999999997, 0.9364770650863647, 0.5364770650863648)
# ['Invoke custom code to populate a DataTable with sales data', 'Invoke VB.NET or C# code synchronously, with the option to pass input arguments and return out arguments.', 0.97, 3] (0.97, 0.4218933880329132, 0.5481066119670868)
# ['Increment the sheet number', 'Extract target currency exchange rate and set value to output argument', 0.97, 5] (0.97, 0.4166271686553955, 0.5533728313446045)
# ['Extract target currency exchange rate and set value to output argument', 'Save a value to use in later activities.', 0.97, 3] (0.97, 0.396511971950531, 0.573488028049469)
# ['Extract target currency exchange rate and set value to output argument', 'Set Variable Value', 0.97, 4] (0.97, 0.3917137384414673, 0.5782862615585327)
# ['Clone DataTable and save to variable', 'Set Variable Value', 0.97, 4] (0.97, 0.3276793956756592, 0.6423206043243408)
# ['Clone DataTable and save to variable', 'Assign email value', 0.97, 5] (0.97, 0.30411258339881897, 0.665887416601181)
# topk errors on trainset
# ['Perform activities on each network interface within the current Azure subscription matching the filter criteria.', 'Upload a file to Azure Blob Storage.', 0.75, 6] (0.75, 0.****************, 0.*****************)
# ['Retrieve all disks in the current Azure subscription.', 'Upload a file to Azure Blob Storage.', 0.75, 6] (0.75, 0.****************, 0.****************)
# ['Upload downloaded files to the Documents folder', 'Download the newly created file from SharePoint', 0.****************, 10] (0.****************, 0.****************, 0.****************)
# ['Delete a computer from the directory in NetIQ eDirectory.', 'Set the expiration date for a user account in NetIQ eDirectory.', 0.75, 6] (0.75, 0.****************, 0.****************)
# ['Download a blob from Azure storage and save it to a local file.', 'Generate vector representations of input for machine learning models and algorithms in Azure.', 0.75, 6] (0.75, 0.****************, 0.*****************)
# ['Delete a lifecycle policy in Azure Active Directory.', 'Remove an owner from a group in Azure Active Directory.', 0.75, 6] (0.75, 0.****************, 0.*****************)
# ['Delete a specified blob container in Azure.', 'Upload a file to Azure Blob Storage.', 0.75, 6] (0.75, 0.****************, 0.****************)
# ['Detach a data disk from a virtual machine in Azure.', 'Retrieve a list of storage accounts in Azure.', 0.75, 6] (0.75, 0.***************, 0.*****************)
# ['Trigger an automation workflow when a new file is created in a specified location in Microsoft Office 365.', 'Move an email to a folder within the same mailbox in Microsoft Office 365 Outlook.', 0.75, 6] (0.75, 0.****************, 0.****************)
# ['Get all security rules in a network security group in Azure.', 'Retrieve all disks associated with a specified virtual machine in Azure.', 0.75, 6] (0.75, 0.****************, 0.*****************)
# ['Retrieve details about a specified blob in Azure Blob Containers.', 'Retrieve the list of blobs in a specified container in Azure Storage.', 0.75, 6] (0.75, 0.****************, 0.*****************)
# ['Retrieve the list of network interfaces associated with a virtual machine in Azure.', 'Retrieve all disks associated with a specified virtual machine in Azure.', 0.75, 6] (0.75, 0.****************, 0.****************)
# ['Microsoft Email', 'Microsoft Teams', 0.5133333333333333, 2] (0.5133333333333333, 0.2844961881637573, 0.22883714516957598)
# ['Create a collection of items with the same type as the first specified element.', 'Perform activities on each element of an enumeration in a workflow.', 0.75, 6] (0.75, 0.520479679107666, 0.22952032089233398)
# ['Perform activities on each element of an enumeration in a workflow.', 'Execute activities for each email matching the filter criteria in Microsoft Office 365 Outlook.', 0.***************, 9] (0.***************, 0.5822002291679382, 0.23652121682225924)
# ['Log the PDF file name', 'Write text to storage', 0.23333333333333334, 10] (0.23333333333333334, 0.*****************, 0.2634507874647776)
# ['Loop through each page of the PDF file', 'Single Excel Process Scope', 0.196875, 10] (0.196875, 0.****************, 0.27068850994110105)
# ['Interact with Microsoft Excel application using ExcelApplicationCard.', 'Sort a column in Microsoft Excel.', 0.75, 6] (0.75, 0.*****************, 0.28445112705230713)
# ['Interact with Microsoft Excel application using ExcelApplicationCard.', 'Format a range of cells in Microsoft Excel.', 0.75, 6] (0.75, 0.****************, 0.3138633370399475)
# ['For each email in the Gmail inbox', 'For each opportunity that matches, set variable value', 0.32558139534883723, 10] (0.32558139534883723, 0.7266655564308167, 0.****************)

# Define your train examples. You need more than just two examples...
# with open("dev_dataset.json") as fin:
#     full_dataset = json.load(fin)
# with open("dev_ids.yaml", "r") as f:
#     ids = yaml_load(f)

# test accuracy at 5 is 0.9456521739130435
# model = SentenceTransformer('output_train_msmarco_retrieval_v3_20240912/checkpoint-230000')

# test accuracy at 5 is 0.358695652173913
# model = SentenceTransformer("output_train_msmarco_demos_v5_20240912/checkpoint-261240")

# test accuracy at 5 is 0.9456521739130435
model = SentenceTransformer("output_train_msmarco_retrieval_v5_20240916/checkpoint-1791000")

# accuracy at 5 is 0.9347826086956522
# model = SentenceTransformer("artifacts/v4")

sample_size = 200
dataset_name = "test"
with open(f"dataset_20240916/{dataset_name}_activities_map.yaml") as fin:
    activities_map = yaml.load(fin, Loader=yaml.FullLoader)
with open(f"dataset_20240916/{dataset_name}_workflows_dataset.yaml") as fin:
    demo_dataset = yaml.load(fin, Loader=yaml.FullLoader)
with open(f"dataset_20240916/{dataset_name}_dataset_type_indices_readable.json") as fin:
    full_dataset = json.load(fin)
with open(f"dataset_20240916/{dataset_name}_ids.yaml", "r") as f:
    ids = yaml.load(f, Loader=yaml.FullLoader)

id2name = {v: k for k, v in ids.items()}

eval_counts = {}
eval_sentences1, eval_sentences2, eval_scores = [], [], []
for t in full_dataset:
    eval_counts[t] = 0
    for s1, s2, score, _ in full_dataset[t]:
        eval_counts[t] += 1
        eval_sentences1.append(s1)
        eval_sentences2.append(s2)
        eval_scores.append(score)
        if eval_counts[t] > sample_size:
            break
evaluator = evaluation.EmbeddingSimilarityEvaluator(eval_sentences1, eval_sentences2, eval_scores, show_progress_bar=True)
print("evaluation", evaluator(model))

error_classes = {}
results = []
# dataset = []
# for t in eval_counts:
#     fdt = [d for d in full_dataset if d[3] == t]
#     if len(fdt) < sample_size:
#         dataset.extend(fdt)
#         continue
#     # print(t, len(fdt), sample_size)
#     dataset.extend(random.sample(fdt, sample_size))
# for d in full_dataset:
#     t1, t2, score, _type = d
#     if _type in (0, 1, 2, 4):
#         if random.random() < great_prob:
#             dataset.append(d)
#     if _type in (3, 5, 6, 7):
#         if random.random() < good_prob:
#             dataset.append(d)
dataset = []
for t in full_dataset:
    if len(full_dataset[t]) <= sample_size:
        dataset.extend(full_dataset[t])
    else:
        dataset.extend(random.sample(full_dataset[t], sample_size))

for d in tqdm(dataset, "errors compute"):
    t1, t2, score, _type = d
    e1 = model.encode(t1, normalize_embeddings=True)
    e2 = model.encode(t2, normalize_embeddings=True)
    prediction = float(e1.dot(e2))
    # print("=====")
    # print(t1)
    # print(t2)
    # print(score)
    # print(prediction)
    error = abs(score - prediction)
    results.append((score, prediction, error))
    if _type not in error_classes:
        error_classes[_type] = []
    error_classes[_type].append(prediction)
    # if _type == 7:
    #     print(d)
    #     print(prediction)
err = np.array(results)[:, 2]

print("average error", np.mean(err), np.std(err), len(results))
for _type, prediction in error_classes.items():
    arr = np.array(prediction)
    print("average prediction", np.mean(arr), "std", np.std(arr), len(prediction), "for", id2name[_type])

print("topk errors")
err_top = np.argsort(err)
for i in err_top[-20:]:
    print(dataset[i], results[i])

print("computing accuracy of retrieval based on activities descriptions")
activities_embeddings = []
for a in tqdm(activities_map.values(), "activities"):
    activities_embeddings.append(model.encode(a["description"], normalize_embeddings=True))
activities_embeddings = np.array(activities_embeddings)
positions = []
for d in tqdm(demo_dataset, "errors on demos"):
    for step in d["steps"]:
        activity_description = step["description"]
        step_display_name = step["step"]
        ade = model.encode(activity_description, normalize_embeddings=True)
        se = model.encode(step_display_name, normalize_embeddings=True)
        prediction = se.dot(ade)
        se_preds = activities_embeddings.dot(se)
        se_preds = np.sort(se_preds)
        position = se_preds.shape[0] - np.searchsorted(se_preds, prediction)
        positions.append(position)
pos_values, pos_counts = np.unique(np.array(positions), return_counts=True)
print(pos_counts)
for i in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 20):
    acc = np.sum(pos_counts[:i]) / np.sum(pos_counts)
    print("accuracy at", i, "is", acc)
