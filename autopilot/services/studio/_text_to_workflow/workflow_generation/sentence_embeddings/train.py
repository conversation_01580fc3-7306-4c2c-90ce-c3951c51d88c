import json
import random
from datetime import datetime
from typing import Iterator

import numpy as np
import torch
import wandb
import yaml
from datasets import Dataset, load_dataset
from sentence_transformers import (
    SentenceTransformer,
    SentenceTransformerTrainer,
    SentenceTransformerTrainingArguments,
)
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator, InformationRetrievalEvaluator, SimilarityFunction
from sentence_transformers.losses import CosineSimilarityLoss
from sentence_transformers.sampler import BatchSampler, SetEpochMixin
from tqdm import tqdm


def get_demos_evaluator_and_data(test_workflows, train_workflows):
    corpus, queries, relevant_docs = {}, {}, {}
    for i, item in enumerate(test_workflows):
        queries[i] = test_workflows[item]["query"]
    for i, item in enumerate(train_workflows):
        corpus[i] = train_workflows[item]["query"]

    ious = np.zeros((len(test_workflows), len(train_workflows)))
    filtered_activities = set(["UiPath.Core.Activities.ManualTrigger"])
    for i, item1 in tqdm(enumerate(test_workflows), "build ious"):
        activities1 = {i["activity"] for i in test_workflows[item1]["steps"] if i["activity"] not in filtered_activities}
        activity_pairs1 = set(
            step["activity"] + "+" + next_step["activity"]
            for step, next_step in zip(test_workflows[item1]["steps"], test_workflows[item1]["steps"][1:], strict=False)
        )
        for j, item2 in enumerate(train_workflows):
            activities2 = {i["activity"] for i in train_workflows[item2]["steps"] if i["activity"] not in filtered_activities}
            activity_pairs2 = set(
                step["activity"] + "+" + next_step["activity"]
                for step, next_step in zip(train_workflows[item2]["steps"], train_workflows[item2]["steps"][1:], strict=False)
            )
            ious[i, j] = len(activities1 & activities2) / len(activities1 | activities2) + len(activity_pairs1 & activity_pairs2) / len(
                activity_pairs1 | activity_pairs2
            )

    max_indices = np.argmax(ious, axis=1)
    for i in range(len(test_workflows)):
        relevant_ids = np.argwhere(ious[i][max_indices[i]] == ious[i]).flatten()
        relevant_ids = [int(k) for k in relevant_ids]
        if len(relevant_ids) < 20:
            relevant_docs[i] = set(relevant_ids)

    # Given queries, a corpus and a mapping with relevant documents, the InformationRetrievalEvaluator computes different IR metrics.
    ir_evaluator = InformationRetrievalEvaluator(
        queries=queries,
        corpus=corpus,
        relevant_docs=relevant_docs,
        name="demo-retriever",
    )
    return ir_evaluator, corpus, queries, relevant_docs, ious


def get_demos_evaluator(test_workflows, train_workflows):
    ir_evaluator, corpus, queries, relevant_docs, ious = get_demos_evaluator_and_data(test_workflows, train_workflows)
    return ir_evaluator


def get_activities_evaluator(attribute, test_workflows, train_workflows):
    corpus, queries, relevant_docs = {}, {}, {}

    activities = {}  # activity name -> attribute
    relevant_activities = {}  # step -> set of relevant activities
    for workflow in test_workflows:
        for step in test_workflows[workflow]["steps"]:
            query = step["step"]
            relevant = step["activity"]
            relevant_activities.setdefault(query, set())
            relevant_activities[query].add(relevant)
            activities[relevant] = step[attribute]

    for workflow in train_workflows:
        for step in train_workflows[workflow]["steps"]:
            activities[relevant] = step[attribute]

    activity_ids = {}
    activity_list = sorted(activities.keys())
    for i, activity in tqdm(enumerate(activity_list), f"{attribute} activity_list {len(activity_list)}"):
        corpus[i] = activities[activity]
        activity_ids[activity] = i

    query_ids = {}
    query_list = sorted(relevant_activities.keys())
    for i, query in tqdm(enumerate(query_list), f"{attribute} query_list {len(query_list)}"):
        queries[i] = query
        query_ids[query] = i
        relevant_docs[i] = set(activity_ids[relevant] for relevant in relevant_activities[query])

    # Given queries, a corpus and a mapping with relevant documents, the InformationRetrievalEvaluator computes different IR metrics.
    ir_evaluator = InformationRetrievalEvaluator(
        queries=queries,
        corpus=corpus,
        relevant_docs=relevant_docs,
        name=f"{attribute}-activities-retriever",
    )
    return ir_evaluator


class RandomTypeBatchSampler(SetEpochMixin, BatchSampler):
    def __init__(
        self,
        dataset: Dataset,
        batch_size: int,
        drop_last: bool,
        valid_label_columns: list[str] | None = None,
        generator: torch.Generator | None = None,
    ) -> None:
        super().__init__(dataset, batch_size, drop_last)
        self.dataset = dataset
        self.dataset_by_types = train_sample_ids  # hack: can't get this as param so getting a "global" with it
        self.dataset_types = list(self.dataset_by_types.keys())

    def __iter__(self) -> Iterator[list[int]]:
        if self.dataset.name == "train":
            for _ in range(len(self.dataset) // self.batch_size):
                indices = []
                for random_type in random.choices(self.dataset_types, k=self.batch_size):
                    random_idx = random.choice(self.dataset_by_types[random_type])
                    indices.append(random_idx)
                # print(indices)
                yield indices
        else:
            for i in range(0, len(self.dataset), self.batch_size):
                yield list(range(i, min(i + self.batch_size, len(self.dataset))))

    def __len__(self) -> int:
        # if self.drop_last: # ignore drop_last
        #     return len(self.dataset) // self.batch_size
        # else:
        return (len(self.dataset) + self.batch_size - 1) // self.batch_size


class RandomTypeSampleTrainer(SentenceTransformerTrainer):
    def __init__(self, *args, **kwargs):
        SentenceTransformerTrainer.__init__(self, *args, **kwargs)

    def get_batch_sampler(
        self,
        dataset: Dataset,
        batch_size: int,
        drop_last: bool,
        valid_label_columns: list[str] | None = None,
        generator: torch.Generator | None = None,
    ) -> BatchSampler | None:
        return RandomTypeBatchSampler(dataset=dataset, batch_size=batch_size, drop_last=drop_last, valid_label_columns=valid_label_columns, generator=generator)


dataset_folder = "dataset_20241209_14"
# dataset_folder = "dataset_manual"
version = ""
# model_type = "msmarco"
model_type = "stella"
# task = "demo"
task = "retrieval"
today_str = datetime.today().strftime("%Y%m%d")

if __name__ == "__main__":
    wandb.init(project="wingman@sentence_embeddings", name=f"{model_type}_{dataset_folder}_{today_str}{version}")

    if model_type == "msmarco":
        model = SentenceTransformer("msmarco-distilbert-base-tas-b", trust_remote_code=True)
    else:
        model = SentenceTransformer("dunzhang/stella_en_400M_v5", trust_remote_code=True)

    print("loading the train dataset")
    train_dataset = load_dataset("json", data_files=f"{dataset_folder}/train_dataset.json", split="train")
    train_dataset.name = "train"
    print("loaded the train dataset")

    print("loading the test dataset")
    test_dataset = load_dataset("json", data_files=f"{dataset_folder}/test_dataset.json", split="train")
    test_dataset.name = "test"
    print("loaded the test dataset")

    print("loading the train sample ids")
    with open(f"{dataset_folder}/train_sample_ids.json") as fin:
        train_sample_ids = json.load(fin)
    print("loaded the train sample ids")

    print("loading the eval samples")
    with open(f"{dataset_folder}/train_eval_samples.json") as fin:
        train_eval_samples = json.load(fin)
    with open(f"{dataset_folder}/test_eval_samples.json") as fin:
        test_eval_samples = json.load(fin)
    print("loaded the eval samples")

    with open(f"{dataset_folder}/workflows_map.yaml") as fin:
        workflows_map = yaml.load(fin, Loader=yaml.FullLoader)
        test_workflows = workflows_map["test"]
        train_workflows = workflows_map["train"]

    loss = CosineSimilarityLoss(model)
    # loss = CoSENTLoss(model)
    # loss = AnglELoss(model)

    args = SentenceTransformerTrainingArguments(
        # Required parameter:
        output_dir=f"output_train_{model_type}_{dataset_folder}_{today_str}{version}",
        # Optional training parameters:
        num_train_epochs=200,
        per_device_train_batch_size=256,
        per_device_eval_batch_size=256,
        # warmup_ratio=0.1,
        warmup_steps=1000,
        learning_rate=1e-5,
        weight_decay=0.1,
        # fp16=False,  # Set to False if your GPU can't handle FP16
        # bf16=False,  # Set to True if your GPU supports BF16
        # batch_sampler=ContinualBatchSampler,  # Losses using "in-batch negatives" benefit from no duplicates
        # Optional tracking/debugging parameters:
        eval_on_start=True,
        eval_strategy="steps",
        eval_steps=1000,
        save_strategy="steps",
        save_steps=1000,
        save_total_limit=2,
        logging_steps=1000,
        load_best_model_at_end=True,
        run_name=f"train_{model_type}_{dataset_folder}_{today_str}{version}",  # Used in W&B if `wandb` is installed
        report_to="wandb",
    )

    print("build similarity evaluators")
    train_sim_evaluator = EmbeddingSimilarityEvaluator(
        sentences1=train_eval_samples["sentence1"],
        sentences2=train_eval_samples["sentence2"],
        scores=train_eval_samples["score"],
        main_similarity=SimilarityFunction.COSINE,
        name="sts-train",
    )

    test_sim_evaluator = EmbeddingSimilarityEvaluator(
        sentences1=test_eval_samples["sentence1"],
        sentences2=test_eval_samples["sentence2"],
        scores=test_eval_samples["score"],
        main_similarity=SimilarityFunction.COSINE,
        name="sts-dev",
    )
    print("built similarity evaluators")

    print("build dev evaluators")
    if task == "demo":
        dev_evaluators = [
            train_sim_evaluator,
            test_sim_evaluator,
            get_demos_evaluator(test_workflows, train_workflows),
        ]
    else:
        dev_evaluators = [
            train_sim_evaluator,
            test_sim_evaluator,
            get_demos_evaluator(test_workflows, train_workflows),
            get_activities_evaluator("description", test_workflows, train_workflows),
            get_activities_evaluator("category", test_workflows, train_workflows),
            get_activities_evaluator("content", test_workflows, train_workflows),
            get_activities_evaluator("displayName", test_workflows, train_workflows),
        ]
    for dev_evaluator in dev_evaluators:
        dev_evaluator(model)
    print("built dev evaluators")

    trainer = RandomTypeSampleTrainer(
        model=model,
        args=args,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        loss=loss,
        evaluator=dev_evaluators,
    )
    print("Starting train...")
    trainer.train()
