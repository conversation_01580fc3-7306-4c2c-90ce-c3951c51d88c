FROM nvidia/cuda:12.2.0-devel-ubuntu22.04
RUN apt update
RUN apt install -y wget
RUN apt install -y gcc g++
RUN apt install -y libstdc++6 libc6-dev libopenblas-dev libatlas-cpp-0.6-tools
ARG CONDA_VERSION=py310_22.11.1-1
RUN apt install -y vim
RUN apt install -y python3-pip
RUN pip install --upgrade pip
RUN apt install -y git
RUN apt install -y python3-packaging
COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt