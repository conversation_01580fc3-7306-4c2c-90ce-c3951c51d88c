import torch
import yaml
from sentence_transformers import SentenceTransformer
from sentence_transformers.util import cos_sim
from train import get_demos_evaluator_and_data

# Load a model
# model = SentenceTransformer('all-mpnet-base-v2') # 0.4523809523809524
# model = SentenceTransformer("msmarco-distilbert-base-tas-b") # 0.3968253968253968
# model = SentenceTransformer('dunzhang/stella_en_400M_v5', trust_remote_code=True) # 0.46825396825396826
# model = SentenceTransformer('output_train_msmarco_demos_v5_20240912/checkpoint-261240') # 0.48412698412698413
# model = SentenceTransformer('output_train_msmarco_retrieval_v3_20240912/checkpoint-230000') # 0.5317460317460317
# model = SentenceTransformer('output_train_msmarco_demos_v5_20240916/checkpoint-261240') # 0.48412698412698413

# model = SentenceTransformer('output_train_stella_retrieval_v16_20241011/checkpoint-1550000', trust_remote_code=True)
# model = SentenceTransformer('output_train_stella_dataset_20241025_10_demos_and_act_20241025/checkpoint-1400', trust_remote_code=True) # 0.5859030837004405
# model = SentenceTransformer('output_train_stella_dataset_20241031_05_20241031/checkpoint-243000', trust_remote_code=True) # 0.6839622641509434
# model = SentenceTransformer('output_train_stella_dataset_20241031_05_20241031/checkpoint-731000', trust_remote_code=True) # 0.6839622641509434
# model = SentenceTransformer('output_train_stella_dataset_20241101_15_20241101_AnglELoss/checkpoint-293000', trust_remote_code=True) # 0.6698113207547169
# model = SentenceTransformer('output_train_stella_dataset_20241106_13_20241106_AnglELoss/checkpoint-130000', trust_remote_code=True) # 0.6698113207547169
model = SentenceTransformer(
    "output_train_stella_dataset_20241101_15_20241101/checkpoint-734000", trust_remote_code=True
)  # mean error:  0.02330183505345836 std error 0.029776030492731528


# NORMALIZE_EMBEDDINGS = False
dataset_folder = "dataset_20241126_09"
with open(f"{dataset_folder}/workflows_map.yaml") as fin:
    workflows_map = yaml.load(fin, Loader=yaml.FullLoader)
    test_data = workflows_map["test"]
    train_data = workflows_map["train"]


ir_evaluator, corpus, queries, relevant_docs, ious = get_demos_evaluator_and_data(test_data, train_data)

results = ir_evaluator(model)
print(f"{results['demo-retriever_cosine_recall@5']=}")

query_embeddings = model.encode(
    queries,
    show_progress_bar=True,
    batch_size=16,
    convert_to_tensor=True,
    # normalize_embeddings=NORMALIZE_EMBEDDINGS,
)
corpus_embeddings = model.encode(
    corpus,
    show_progress_bar=True,
    batch_size=16,
    convert_to_tensor=True,
    # normalize_embeddings=NORMALIZE_EMBEDDINGS,
)

tp, fn = 0, 0
results = cos_sim(query_embeddings, corpus_embeddings)  # torch.mm(query_embeddings, corpus_embeddings.transpose(0, 1))
for qid, query in queries.items():
    relevant_ids = relevant_docs.get(qid)
    if relevant_ids is None or len(relevant_ids) > 20:  # usually all docs are relevant with score 0.0 (so all are irrelevant)
        continue
    sorted_hits = torch.argsort(results[qid], descending=True)
    item_positions = {}
    ctp, cfn = 0, 0
    for r in relevant_ids:
        r_position = (sorted_hits == r).nonzero(as_tuple=True)[0]
        item_positions[r] = r_position.item()
        if item_positions[r] < 30:  # this is because we use top30 in inference
            ctp += 1
        else:
            cfn += 1
    tp += ctp
    fn += cfn
    recall = ctp / (ctp + cfn)
    print(f"{qid}, {recall=}, relevant {len(relevant_ids)}: {query}")
    if ctp / (ctp + cfn) < 0.9:
        # import pdb; pdb.set_trace()
        print("\t", "best predictions")
        for cid in sorted_hits[:10]:
            print("\t", cid.item(), results[qid, cid.item()].item(), ious[qid, cid.item()], corpus[cid.item()])
        print("\t", "correct")
        for cid, pos in item_positions.items():
            print("\t", cid, results[qid, cid].item(), ious[qid, cid], pos, corpus[cid])
        # print(qid, query, fn)
        # if qid == 50:
        #     import pdb; pdb.set_trace()
        # pass
print("recall", tp / (tp + fn), "tp", tp, "fn", fn)
