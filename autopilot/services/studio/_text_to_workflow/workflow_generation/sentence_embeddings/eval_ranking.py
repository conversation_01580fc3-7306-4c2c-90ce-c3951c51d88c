import yaml
from sentence_transformers import SentenceTransformer
from train import get_activities_evaluator, get_demos_evaluator

# demo-retriever 0.43174622741930435
# description-activities-retriever 0.76875
# category-activities-retriever 0.40625
# content-activities-retriever 0.4625
# displayName-activities-retriever 0.79375
# model = SentenceTransformer("msmarco-distilbert-base-tas-b")

# demo-retriever 0.5597709448670988
# description-activities-retriever 0.8625
# category-activities-retriever 0.4875
# content-activities-retriever 0.71875
# displayName-activities-retriever 0.825
# model = SentenceTransformer('dunzhang/stella_en_400M_v5', trust_remote_code=True)

# demo-retriever 0.5580282787013556
# description-activities-retriever 0.94375
# category-activities-retriever 0.74375
# content-activities-retriever 0.5875
# displayName-activities-retriever 0.95
# model = SentenceTransformer('output_train_msmarco_retrieval_v3_20240912/checkpoint-230000')

# demo-retriever 0.5422448811871889
# description-activities-retriever 0.675
# category-activities-retriever 0.35
# content-activities-retriever 0.44375
# displayName-activities-retriever 0.7875
# model = SentenceTransformer('output_train_msmarco_demos_v5_20240916/checkpoint-261240')

# demo-retriever 0.4895964434425973
# description-activities-retriever 0.95625
# category-activities-retriever 0.74375
# content-activities-retriever 0.64375
# displayName-activities-retriever 0.96875
# model = SentenceTransformer("output_train_msmarco_retrieval_v5_20240916/checkpoint-1791000")

# demo-retriever 0.6111420274881814
# description-activities-retriever 0.95
# category-activities-retriever 0.7
# content-activities-retriever 0.6125
# displayName-activities-retriever 0.9625
# model = SentenceTransformer('artifacts/v4')

# demo-retriever 0.5422448811871889
# description-activities-retriever 0.675
# category-activities-retriever 0.35
# content-activities-retriever 0.44375
# displayName-activities-retriever 0.7875
# model = SentenceTransformer('output_train_msmarco_demos_v5_20240912/checkpoint-261240')

# demo-retriever 0.6421468587434974
# description-activities-retriever 0.9698275862068966
# category-activities-retriever 0.5301724137931034
# content-activities-retriever 0.9612068965517241
# displayName-activities-retriever 0.9267241379310345
model = SentenceTransformer("output_train_stella_dataset_20241101_15_20241101/checkpoint-734000", trust_remote_code=True)
dataset_folder = "dataset_20241126_09"

with open(f"{dataset_folder}/workflows_map.yaml") as fin:
    workflows_map = yaml.load(fin, Loader=yaml.FullLoader)
    test_workflows = workflows_map["test"]
    train_workflows = workflows_map["train"]

dev_evaluators = [
    get_demos_evaluator(test_workflows, train_workflows),
    get_activities_evaluator("description", test_workflows, train_workflows),
    get_activities_evaluator("category", test_workflows, train_workflows),
    get_activities_evaluator("content", test_workflows, train_workflows),
    get_activities_evaluator("displayName", test_workflows, train_workflows),
]
for dev_evaluator in dev_evaluators:
    results = dev_evaluator(model)
    print(dev_evaluator.name, results[f"{dev_evaluator.name}_dot_recall@5"])
