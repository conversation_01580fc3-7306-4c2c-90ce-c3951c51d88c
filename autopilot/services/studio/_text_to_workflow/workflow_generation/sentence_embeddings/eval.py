import json
from pprint import pprint

import numpy as np
import torch.nn as nn
from sentence_transformers import SentenceTransformer
from tqdm import tqdm

# dataset_folder = "dataset_20241108_16"
dataset_folder = "dataset_20241126_09"
# dataset_folder = "dataset_20241025_10_demos_and_act"
# dataset_folder = "dataset_manual"
# version = "v17"
# model_type = "msmarco"
# model_type = "stella"
# task = "demo"
# task = "retrieval"

# model = SentenceTransformer("msmarco-distilbert-base-tas-b", trust_remote_code=True)
# 0.37
# train average error:   0.08129128471252545
# test average error:  0.24466766622987862

# model = SentenceTransformer('dunzhang/stella_en_400M_v5', trust_remote_code=True)
# 0.32
# train average error:  0.1182743760503429
# test average error:  0.08237267476088828

# model = SentenceTransformer("output_train_msmarco_retrieval_v22_20241016/checkpoint-2600", trust_remote_code=True)
# 0.06
# model = SentenceTransformer('output_train_msmarco_retrieval_v19_20241014/checkpoint-10000', trust_remote_code=True)
# test average error:  0.18524720461051897

# model = SentenceTransformer('output_train_msmarco_retrieval_v19_20241014/checkpoint-20000', trust_remote_code=True)
# test average error:  0.18164855037598204

# model = SentenceTransformer('output_train_msmarco_retrieval_v20_20241014/checkpoint-10000', trust_remote_code=True)
# train average error:  0.022147067690120547
# test average error:  0.18092872470271465
# model = SentenceTransformer('output_train_msmarco_retrieval_v20_20241014/checkpoint-90000', trust_remote_code=True)
# train average error:  0.015340901281184384
# test average error:  0.18341642991487409

# model = SentenceTransformer('output_train_msmarco_retrieval_v26_20241021/checkpoint-31200', trust_remote_code=True)
# model = SentenceTransformer('output_train_msmarco_dataset_20241022_15_20241022/checkpoint-24600', trust_remote_code=True)
# model = SentenceTransformer('output_train_stella_dataset_20241025_10_demos_and_act_20241025/checkpoint-203900', trust_remote_code=True)
# model = SentenceTransformer('output_train_stella_dataset_20241031_05_20241031/checkpoint-46000', trust_remote_code=True)

model = SentenceTransformer(
    "output_train_stella_dataset_20241101_15_20241101/checkpoint-734000", trust_remote_code=True
)  # mean error:  0.02330183505345836 std error 0.029776030492731528
# model = SentenceTransformer('output_train_stella_dataset_20241108_16_20241108/checkpoint-213000', trust_remote_code=True) # mean error:  0.02313981674515068 std error 0.02990007032588685


# train_dataset = load_dataset("json", data_files=f"{dataset_folder}/readable_train_dataset.json", split="train")
# test_dataset = load_dataset("json", data_files=f"{dataset_folder}/readable_test_dataset.json", split="train")

with open(f"{dataset_folder}/readable_test_dataset.json") as f:
    dataset = json.load(f)

with open("eval.log.txt", "w") as fout:
    cos = nn.CosineSimilarity(dim=0, eps=1e-6)
    errors = []
    err_count, err_sum, err_sum2 = 0, 0, 0
    mini_batch_size = 256  # 16 sec: 1024, 17 sec: 256, 21 sec: 64, 34 sec: 16
    mini_batch = []
    for i, _ in enumerate(pbar := tqdm(dataset, "dataset")):
        if i % mini_batch_size != 0:
            continue
        mini_batch = dataset[i : i + mini_batch_size]
        sentences1 = [item["sentence1"] for item in mini_batch]
        sentences2 = [item["sentence2"] for item in mini_batch]
        query_embeddings = model.encode(sentences1 + sentences2, batch_size=mini_batch_size * 2, convert_to_tensor=True)
        for i, item in enumerate(mini_batch):
            gt_score = item["score"]
            bucket = item["bucket"]
            pred = cos(query_embeddings[i], query_embeddings[i + len(mini_batch)]).item()
            error = abs(pred - gt_score)
            if error > 0.3:
                print("==" * 10, len(errors), file=fout)
                pprint(item, stream=fout)
                print(f"{pred=}, {gt_score=}, {error=}", file=fout, flush=True)
                errors.append((error, pred, gt_score, item))
            err_count += 1
            err_sum += error
            err_sum2 += error**2
            pbar.set_postfix(
                {"big errors": len(errors), "mean error": err_sum / err_count, "std error": np.sqrt(err_sum2 / err_count - (err_sum / err_count) ** 2)}
            )

print("mean error: ", err_sum / err_count, "std error", np.sqrt(err_sum2 / err_count - (err_sum / err_count) ** 2))


# for error, pred, gt_score, item in sorted(errors, key=lambda x: -x[0])[:10]:
#     print("-" * 10, f"{pred=}, {gt_score=}, {error=}", "-" * 10)
#     pprint(item)
