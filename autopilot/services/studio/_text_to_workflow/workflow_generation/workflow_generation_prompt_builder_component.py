import collections
import copy

import langchain.cache
import langchain.chains
import langchain.chat_models
import langchain.embeddings
import langchain.globals
import langchain.prompts
import langchain.schema
from langchain_core.language_models import BaseChatModel

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    PlanStep,
    SubsetName,
    WorkflowDict,
)
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector, CollapseWorkflowSubsequencesPruner
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.activity_utils import (
    build_activity_type_definitions,
    build_additional_type_definitions,
    build_trigger_type_definitions,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.workflow_utils import replace_blobs_with_hash
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema
from services.studio._text_to_workflow.workflow_generation.config import constants

LOGGER = AppInsightsLogger()
# we have a confirmed case of expecting 16384 tokens usage but the returned usage was 16404 (resulting in an overflow)
# there seem to be a problem with the way prompt is estimated in the model, adding this tolerance until resolved
NUMBER_OF_TOKENS_INACCURACY_TOLERANCE = 32


class WorkflowGenerationPromptBuilderComponent:
    def __init__(self, activities_retriever: ActivitiesRetriever):
        self.activities_retriever = activities_retriever
        self.workflow_pruner = CollapseWorkflowSubsequencesPruner()

    def build_generation_demonstration(
        self,
        demonstration: dict,
        user_message_template: langchain.prompts.HumanMessagePromptTemplate,
        assistant_message_template: langchain.prompts.AIMessagePromptTemplate,
        mode: ActivitiesGenerationMode,
    ) -> list[langchain.schema.BaseMessage]:
        triggers, activities = self.get_demonstration_activities(demonstration, include_existing_workflow_activities=False)

        reverse_activities_config_map = {item["activityTypeId"]: item["fullClassName"] for item in (triggers + activities) if item["activityTypeId"]}

        trigger_type_definitions = build_trigger_type_definitions(triggers)
        activity_type_definitions = build_activity_type_definitions(activities)
        additional_type_definitions = ""
        if mode == "workflow":
            demonstration["process"].pop("packages", None)
            demonstration["process"].pop("namespaceImports", None)
            demonstration["process"].pop("arguments", None)
            demonstration["process"].pop("variables", None)
            user_message = user_message_template.format(
                instructions="",
                trigger_type_definitions=trigger_type_definitions,
                activity_type_definitions=activity_type_definitions,
                additional_type_definitions=additional_type_definitions,
                description=demonstration["description"],
                plan=demonstration["plan"],
            )
            WorkflowGenerationPromptBuilderComponent._use_fake_activities(demonstration["process"], reverse_activities_config_map)
            process = demonstration["process"]
        else:
            demonstration["process_existing"].pop("packages", None)
            demonstration["process_existing"].pop("namespaceImports", None)
            variables = demonstration["process_existing"].pop("arguments", [])
            arguments = demonstration["process_existing"].pop("variables", [])
            combined = arguments + variables
            user_message = user_message_template.format(
                instructions="",
                activity_type_definitions=activity_type_definitions,
                additional_type_definitions=additional_type_definitions,
                variables=yaml_dump(combined).strip(),
                description=demonstration["description_sequence"],
                existing_plan=demonstration["plan_existing"],
                plan=demonstration["plan_sequence"],
                existing_workflow=yaml_dump(demonstration["process_existing"], default_style="'").strip(),
            )
            WorkflowGenerationPromptBuilderComponent._use_fake_activities(demonstration["process_existing"], reverse_activities_config_map)
            WorkflowGenerationPromptBuilderComponent._use_fake_activities(
                {"workflow": demonstration["process_sequence"]},
                reverse_activities_config_map,
            )
            process = demonstration["process_sequence"]
        process_yaml = yaml_dump(process, default_style="'").strip()  # TODO: look into why we need this default style here
        assistant_message = assistant_message_template.format(process=process_yaml)
        return [user_message, assistant_message]

    def get_curated_demonstration_workflow(self, demonstration: workflow_generation_schema.WfGenDataPointV2) -> tuple[WorkflowDict, WorkflowDict | None]:
        """
        Formats a demonstration and prepares it to be used as part of a prompt.
        Returns the solution workflow alongside the existing workflow (if it exists).
        """
        triggers, activities = self.get_demonstration_activities(demonstration, include_existing_workflow_activities=True)
        reverse_activities_config_map = {item["activityTypeId"]: item["fullClassName"] for item in (triggers + activities) if item["activityTypeId"]}
        workflow = self._curate_workflow(demonstration["solution_workflow"], reverse_activities_config_map)
        existing_workflow = None
        if (existing_workflow := demonstration.get("existing_workflow_sequence")) is not None:
            existing_workflow = self._curate_workflow(existing_workflow, reverse_activities_config_map)
        return workflow, existing_workflow

    def _curate_workflow(self, workflow: WorkflowDict, reverse_activities_config_map: dict) -> WorkflowDict:
        workflow = copy.deepcopy(workflow)
        WorkflowGenerationPromptBuilderComponent._use_fake_activities(workflow, reverse_activities_config_map)  # type: ignore
        workflow.pop("processName", None)  # type: ignore
        workflow.pop("packages", None)  # type: ignore
        return workflow

    def get_demonstration_activities(
        self,
        demonstration: workflow_generation_schema.WfGenDataPointV2,
        include_existing_workflow_activities: bool = False,
    ) -> tuple[list[ActivityDefinition], list[ActivityDefinition]]:
        trigger_full_ids = demonstration.get("retrieved_triggers", demonstration.get("used_triggers", []))
        activity_full_ids = demonstration.get("retrieved_activities", demonstration.get("used_activities", []))

        if include_existing_workflow_activities and (existing_workflow := demonstration.get("existing_workflow_sequence")) is not None:
            activities_and_triggers = ActivitiesAndTriggersCollector().collect(Workflow("", "", existing_workflow))
            activity_full_ids.extend(activity.activity_id for activity in activities_and_triggers["activity"])
            trigger_full_ids.extend(trigger.activity_id for trigger in activities_and_triggers["trigger"])

        triggers: list[ActivityDefinition] = []
        for trigger_full_id in trigger_full_ids:
            trigger = self.activities_retriever.get(trigger_full_id, demonstration["target_framework"], "trigger")
            if not trigger:
                # demonstration embeddings contain activities that might not be available on certain environments
                # commented this part as a quick fix, needs to be adressed properly
                # raise ValueError(f"Demonstration trigger not found in index. {trigger_name}")
                continue
            triggers.append(trigger)
        activities: list[ActivityDefinition] = []
        for activity_full_id in activity_full_ids:
            activity = self.activities_retriever.get(activity_full_id, demonstration["target_framework"], "activity")
            if not activity:
                # raise ValueError(f"Demonstration activity not found in index. {activity_name}")
                continue
            activities.append(activity)

        return triggers, activities

    def build_generation_demonstration_messages(
        self,
        demonstrations: dict[SubsetName, list[dict]],
        user_msg_template: langchain.prompts.HumanMessagePromptTemplate,
        ai_msg_template: langchain.prompts.AIMessagePromptTemplate,
        gen_model: BaseChatModel,
        mode: ActivitiesGenerationMode,
        sort_in_ascending_order: bool = False,
    ) -> list[langchain.schema.BaseMessage]:
        demonstration_messages = []

        # always include all demonstrations retrieved from the static, uia and testcase set
        for demonstration in demonstrations["uia"] + demonstrations["static"] + demonstrations.get("testcase", []):
            try:  # generation should not fail if demonstration generation fails
                demo_messages = self.build_generation_demonstration(demonstration, user_msg_template, ai_msg_template, mode)
                demonstration_messages.extend(demo_messages)
            except Exception as e:
                LOGGER.exception(f"Failed to build generation demonstration for {demonstration.get('description', 'unk')} {e}")

        # read demonstrations in reverse order to include the top demonstrations first
        demonstration_messages_train = []
        for demonstration in sorted(demonstrations["train"], key=lambda demo: demo["similarity"], reverse=True):
            try:
                demo_messages = self.build_generation_demonstration(demonstration, user_msg_template, ai_msg_template, mode)
                demonstration_messages_train = demo_messages + demonstration_messages_train
            except Exception as e:
                LOGGER.exception(f"Failed to build generation demonstration for {demonstration.get('description', 'unk')} {e}")
                continue

            num_tokens = gen_model.get_num_tokens_from_messages(demonstration_messages + demonstration_messages_train)
            assert num_tokens is not None

            max_generation_tokens_by_model = gen_model.max_total_tokens

            # if demos take more than 30% (~4900 for gpt35) of tokens, stop
            if num_tokens > 0.3 * max_generation_tokens_by_model:
                # if demo takes more than 40% (~6500 for gpt35), remove the last pair
                if num_tokens > 0.4 * max_generation_tokens_by_model:
                    demonstration_messages_train = demonstration_messages_train[2:]  # each demo has 2 messages
                    LOGGER.warning(f"Cutting last demo. Demos tokens: {num_tokens}. Having {len(demonstration_messages_train) // 2} left.")
                if len(demonstration_messages_train) != 2 * len(demonstrations["train"]):  # log message only in case there are still more to add
                    LOGGER.info(f"Demonstrations too long: {num_tokens}. using only top {len(demonstration_messages_train) // 2} demonstration(s)")
                break
        if sort_in_ascending_order:
            demonstration_messages_train = demonstration_messages_train[::-1]
            for i in range(0, len(demonstration_messages_train), 2):
                demonstration_messages_train[i], demonstration_messages_train[i + 1] = (
                    demonstration_messages_train[i + 1],
                    demonstration_messages_train[i],
                )
        demonstration_messages.extend(demonstration_messages_train)
        return demonstration_messages

    def build_user_message(
        self,
        description: str,
        variables: list[workflow_generation_schema.Variable],
        triggers: list[ActivityDefinition],
        activities: list[ActivityDefinition],
        additional_type_definitions: str,
        plan: str,
        gen_model: BaseChatModel,
        gen_messages: list[langchain.schema.BaseMessage],
        user_message_template: langchain.prompts.HumanMessagePromptTemplate,
        mode: ActivitiesGenerationMode,
        existing_plan: str = "",
        existing_workflow: str = "",
    ) -> langchain.schema.BaseMessage:
        instructions = []
        instructions.append(constants.CONNECTIONS_PREFER_MESSAGE)
        instructions = "\n".join(instructions)

        inputs = {
            "instructions": instructions,
            "description": description,
            "plan": plan,
            "activity_type_definitions": build_activity_type_definitions(activities),
            "additional_type_definitions": build_additional_type_definitions(
                activities,
                additional_type_definitions,
            ),
        }
        if mode == "workflow":
            inputs["trigger_type_definitions"] = build_trigger_type_definitions(triggers)
        else:
            inputs["variables"] = yaml_dump(variables).strip()
            inputs["existing_plan"] = existing_plan
            inputs["existing_workflow"], _ = replace_blobs_with_hash(existing_workflow, use_b64_charset=True)

        user_message = user_message_template.format(**inputs)
        num_tokens = gen_model.get_num_tokens_from_messages(gen_messages + [user_message])

        if gen_model.max_tokens is None:
            return user_message

        prompt_maximum_tokens = gen_model.max_total_tokens - gen_model.max_tokens - NUMBER_OF_TOKENS_INACCURACY_TOLERANCE
        if num_tokens < prompt_maximum_tokens:
            return user_message

        if mode == "sequence" and existing_workflow:
            # Assumption: at this point, if the function did not return yet, pruning is still neeeded
            # determine how many tokens we can still use for the existing workflow
            inputs["existing_workflow"] = ""
            draft_user_message = user_message_template.format(**inputs)
            max_allowed_token_size = prompt_maximum_tokens - gen_model.get_num_tokens_from_messages(gen_messages + [draft_user_message])

            # prune the existing workflow
            existing_workflow = self.prune_existing_workflow(existing_workflow, gen_model, max_allowed_token_size)
            inputs["existing_workflow"] = existing_workflow
            user_message = user_message_template.format(**inputs)
            return user_message

        raise errors.PromptOverflowError()

    def prune_existing_workflow(self, serialized_workflow: str, gen_model: BaseChatModel, max_allowed_token_size: int) -> str:
        """Prune the existing workflow until the prompt fits within the max allowed token size."""
        num_tokens = gen_model.get_num_tokens(serialized_workflow)
        if num_tokens <= max_allowed_token_size:
            return serialized_workflow

        LOGGER.warning(f"Prompt too long: {num_tokens}. Attempting to prune existing workflow.")

        # the pruning edits the workflow object, so we receive a serialized str workflow as input
        existing_workflow_dict = yaml_load(serialized_workflow)
        existing_workflow = Workflow("", "", existing_workflow_dict)

        for _pruning_level_attempt in self.workflow_pruner.iteratively_prune_workflow(existing_workflow):
            serialized_workflow = existing_workflow.lmyaml()
            num_tokens = gen_model.get_num_tokens(serialized_workflow)
            LOGGER.warning(f"Attempting to prune existing workflow {_pruning_level_attempt}. Current workflow size: {num_tokens} tokens.")
            if num_tokens < max_allowed_token_size:
                return serialized_workflow

        raise errors.PromptOverflowError()

    @staticmethod
    def prune_steps_for_user_message(steps: list[PlanStep], min_per_step: int = 3) -> list[PlanStep]:
        steps = copy.deepcopy(steps)
        for step in steps:
            # remove activities until only 3 activities per step are left
            if step["activities"] and len(step["activities"]) > min_per_step:
                if "additionalTypeDefinitions" in step["activities"][0]:
                    del step["activities"][0]["additionalTypeDefinitions"]
                else:
                    step["activities"].pop()
            # remove triggers until only 3 trigger per step is left
            elif step["triggers"] and len(step["triggers"]) > min_per_step:
                if "additionalTypeDefinitions" in step["triggers"][0]:
                    del step["triggers"][0]["additionalTypeDefinitions"]
                else:
                    step["triggers"].pop()
            # remove additional type definitions until prompt fits in model context
            else:
                removed_additional_type_definitions = False
                for activity in step["activities"]:
                    if "additionalTypeDefinitions" in activity:
                        del activity["additionalTypeDefinitions"]
                        removed_additional_type_definitions = True
                        break
                for trigger in step["triggers"]:
                    if "additionalTypeDefinitions" in trigger:
                        del trigger["additionalTypeDefinitions"]
                        removed_additional_type_definitions = True
                        break
                if not removed_additional_type_definitions:
                    raise errors.PromptOverflowError()

        return steps

    @staticmethod
    def _use_fake_activities(process: dict, reverse_activities_config_map: dict):
        if "trigger" in process:
            WorkflowGenerationPromptBuilderComponent._migrate_fake_activity_name(process["trigger"], reverse_activities_config_map)

        nodes = collections.deque(process["workflow"])
        while nodes:
            node = nodes.pop()
            if isinstance(node, dict):
                if "activity" in node:
                    WorkflowGenerationPromptBuilderComponent._migrate_fake_activity_name(node, reverse_activities_config_map)
                for v in node.values():
                    nodes.append(v)
            elif isinstance(node, list):
                for v in node:
                    nodes.append(v)

    @staticmethod
    def _migrate_fake_activity_name(node: dict, reverse_activities_config_map: dict):
        # there are 2 variations in the current dataset, for now, we will handle both:
        if "params" in node and "UiPathActivityTypeId" in node["params"]:
            # OLD FORMAT, we should delete this once all demos are migrated to the new format
            typeid = node["params"]["UiPathActivityTypeId"]
            node["activity"] = reverse_activities_config_map[typeid]
            del node["params"]["UiPathActivityTypeId"]
            return

        if "uiPathActivityTypeId" in node:
            typeid = node["uiPathActivityTypeId"]
            node["activity"] = reverse_activities_config_map[typeid]
            del node["uiPathActivityTypeId"]
