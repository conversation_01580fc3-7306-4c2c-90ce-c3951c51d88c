import re
import traceback

import langchain_core.language_models
import typing_extensions as t

from services.studio._text_to_workflow.common import embeddingsdb, helpers
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.constants import PRODUCTIVITY_ACTIVITY2CONNECTION
from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.common.params import get_param_value_category, try_extract_input_variable_reference
from services.studio._text_to_workflow.common.schema import (
    PARAM_VALUE_CATEGORY_COMPATIBILITIES_MAP,
    ActivityDefinition,
    ActivityType,
    Connection,
    TargetFramework,
    Variable,
)
from services.studio._text_to_workflow.common.walkers import SequenceGenerationGenericActivityReplacer
from services.studio._text_to_workflow.common.workflow import Activity, Workflow
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.translate.translate_text_schema import MultiTextTranslationRequest
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask
from services.studio._text_to_workflow.utils.workflow_utils import add_workflow_ids
from services.studio._text_to_workflow.utils.yaml_utils import YAMLError, yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema
from services.studio._text_to_workflow.workflow_generation.config.constants import UI_APPLICATION_CARD_ACTIVITY_NAME
from services.studio._text_to_workflow.workflow_generation.fixyaml_task import FixWorkflowYamlTask
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityError,
    FixWorkflowYamlRequest,
    MissingParamError,
    ParamValueMismatchError,
    PathSegment,
    UndefinedVariableError,
    WorkflowProcessingError,
    WorkflowProcessingErrorType,
    YamlStructureError,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_uia_integration import expand_uia_scopes

TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")
FIXYAML_TASK = FixWorkflowYamlTask("fixyaml_prompt.yaml")
LOGGER = AppInsightsLogger()


class WorkflowGenerationPostProcessComponent:
    def __init__(
        self,
        activities_retriever: ActivitiesRetriever,
        dynamic_activities_component: WorkflowGenerationDynamicActivitiesComponent,
        ignored_uia_activities: dict[TargetFramework, set[str]],
    ):
        self.activities_retriever = activities_retriever
        self.ignored_activities = ignored_uia_activities
        self.dynamic_activities_component = dynamic_activities_component

        self._placeholder_trigger = {
            "activity": self.activities_retriever.placeholder_trigger(),
            "thought": "Manual Trigger",
        }

    async def _load_output_yaml(
        self,
        model: langchain_core.language_models.BaseChatModel | None,
        output: str,
        allow_triggers: bool,
        merge_lists_on_conflicting_keys_in_yaml: bool = False,
    ) -> tuple[dict, list[WorkflowProcessingError]]:
        loaded = {}
        output = output.removeprefix("```yaml\n")
        output = output.removesuffix("```")
        errors: list[WorkflowProcessingError] = []

        try:
            loaded = yaml_load(output, merge_conflicting_keys=merge_lists_on_conflicting_keys_in_yaml)
        except YAMLError:
            LOGGER.warning("YAMLError in output. Trying to fix single quotes.")
            # TODO: This is a hack to fix the single quotes in the output. We should find a better way to do this.
            # enforce_single_quotes_on_yaml_values is not working as expected.
            lines = output.splitlines()
            lines_fixed = []
            for line in lines:
                parts = line.split(": ", 1)
                if len(parts) > 1 and parts[1].strip() != "{}":
                    if not parts[1].strip().startswith("'"):
                        line = f"{parts[0]}: '{parts[1]}"
                    if not parts[1].strip().endswith("'"):
                        line += "'"
                    line = line.replace("\\'", "''")
                lines_fixed.append(line)
            process_str = "\n".join(lines_fixed)
            try:
                loaded = yaml_load(process_str, merge_conflicting_keys=merge_lists_on_conflicting_keys_in_yaml)
            except YAMLError as e:
                LOGGER.warning(f"YAMLError in output. Trying to use the LLM to fix the YAML. Exception {e}")

                if model is None:
                    errors.append(YamlStructureError(workflow_path=[], type=WorkflowProcessingErrorType.INVALID_STRUCTURE))
                    return loaded, errors

                fixyaml_request: FixWorkflowYamlRequest = {
                    "errors": str(e),
                    "workflow": process_str,
                }

                try:
                    result = await FIXYAML_TASK.run(model, fixyaml_request, allow_triggers, merge_lists_on_conflicting_keys_in_yaml)
                    return result["fixed_workflow"], errors
                except Exception as e:
                    LOGGER.warning(f"YAMLError in output. {WorkflowProcessingErrorType.INVALID_STRUCTURE.name} Exception {e}")
                    errors.append(YamlStructureError(workflow_path=[], type=WorkflowProcessingErrorType.INVALID_STRUCTURE))
                    traceback.print_exc()
        return loaded, errors

    async def _postprocess_workflow(
        self,
        workflow_dict: dict,
        activities_config_map: dict[str, dict],
        jit_types: dict[str, dict],
        name2fqn: dict[str, str],
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
        allow_triggers: bool = True,
        draft_only: bool = False,
    ) -> tuple[dict, list[ActivityDefinition], list[dict], list[WorkflowProcessingError]]:
        if not isinstance(workflow_dict, dict):
            LOGGER.warning(f"Output workflow is not dict but a {type(workflow_dict)}")
            return workflow_dict, [], [], []  # we do not consider this a generation error, we should never reach this point if our logic is correct

        errors: list[WorkflowProcessingError] = []

        if process_name := workflow_dict.get("processName", None):
            workflow_dict["processName"] = process_name.translate(str.maketrans("", "", r"!?,.:;/<>\|"))

        additional_used_activities = []
        uia_scopes = []

        if allow_triggers:
            item_additional_used_activities, processing_errors = await self._postprocess_workflow_trigger(
                workflow_dict,
                activities_config_map,
                jit_types,
                name2fqn,
                connections_by_key,
                target_framework,
                draft_only,
            )
            additional_used_activities.extend(item_additional_used_activities)
            errors.extend(processing_errors)

        workflow_dict["workflow"] = workflow_dict.get("workflow", [])
        for activity in workflow_dict["workflow"]:
            item_additional_used_activities, item_uia_scopes, processing_errors = await self._postprocess_workflow_activity(
                activity,
                workflow_dict["workflow"],
                activities_config_map,
                jit_types,
                name2fqn,
                connections_by_key,
                target_framework,
                draft_only,
            )
            additional_used_activities.extend(item_additional_used_activities)
            errors.extend(processing_errors)
            uia_scopes.extend(item_uia_scopes)

        return workflow_dict, additional_used_activities, uia_scopes, errors

    async def _postprocess_workflow_trigger(
        self,
        process: dict,
        activities_config_map: dict[str, dict],
        jit_types: dict[str, dict],
        name2fqn: dict[str, str],
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
        draft_only: bool = False,  # if the workflow is a draft (e.g. we might send it to the LLM to fix it), we want to omit any configuration that are redundant for the LLM to fix
    ) -> t.Tuple[list[ActivityDefinition], list[WorkflowProcessingError]]:
        if not isinstance(process, dict):
            LOGGER.warning(f"Process is not dict but a {type(process)}")
            return [], []  # we do not consider this a generation error, we should never reach this point if our logic is correct

        if "trigger" not in process or not isinstance(process["trigger"], dict):
            LOGGER.warning("Trigger is missing. Adding a  manual trigger.")
            process["trigger"] = self._placeholder_trigger
            return [], []

        if not isinstance(process["trigger"], dict):
            LOGGER.warning(f"{WorkflowProcessingErrorType.INVALID_TRIGGER_STRUCTURE.name} It should be a dict but it's a {type(process)}")
            process["trigger"] = self._placeholder_trigger
            return [], [YamlStructureError(workflow_path=[], type=WorkflowProcessingErrorType.INVALID_TRIGGER_STRUCTURE)]

        node = process["trigger"]
        if "activity" not in node:
            LOGGER.warning(f"{WorkflowProcessingErrorType.MISSING_TRIGGER_TYPE.name} 'activity' is not in node. Node has {list(node.keys())}")
            node["activity"] = self._placeholder_trigger["activity"]
            node["thought"] = node.get("thought", "") + " (invalid trigger)"
            node["params"] = {}
            return [], [YamlStructureError(workflow_path=[], type=WorkflowProcessingErrorType.MISSING_TRIGGER_TYPE)]

        if not isinstance(node["activity"], str):
            LOGGER.warning(f"{WorkflowProcessingErrorType.TRIGGER_TYPE_NOT_STRING.name} It should be a string but it's a {type(node['activity'])}")
            node["activity"] = self._placeholder_trigger["activity"]
            node["thought"] = node.get("thought", "") + " (invalid trigger)"
            node["params"] = {}
            return [], [YamlStructureError(workflow_path=[], type=WorkflowProcessingErrorType.TRIGGER_TYPE_NOT_STRING)]

        if not draft_only:
            # if workflow is just a draft, we don't want to add an empty params object
            WorkflowGenerationPostProcessComponent._ensure_valid_params(node)
        if node["activity"] in name2fqn:
            # TODO: We should remove this probably
            fqn = name2fqn[node["activity"]]
            print("INFO: Replaced name with fqn", node["activity"], fqn)
            node["activity"] = fqn

        if not draft_only and node["activity"] in activities_config_map:
            self.dynamic_activities_component.configure_dynamic_connected_activity(activities_config_map, node)
            return [], []

        if not draft_only:
            WorkflowGenerationPostProcessComponent._add_connection_details(connections_by_key, node)

        activity_name, thought, connections = (
            node["activity"],
            node.get("thought", ""),
            list(connections_by_key.values()),
        )
        activity_name = self._try_fix_activity_name(activity_name, thought, connections, target_framework, "trigger")

        activity_full_id_exists, activity_full_class_name_exists = self.activities_retriever.activity_exists(activity_name, target_framework, "trigger")
        if activity_full_id_exists:
            if activity_name != node["activity"]:
                print("INFO: Replaced activity fqn", node["activity"], activity_name)
                node["activity"] = activity_name
            return [], []
        elif activity_full_class_name_exists:
            # A DAP activity that was retrieved for a demo was actually used without it being also retrieved from the index
            if not draft_only:
                additional_used_activities = await self.dynamic_activities_component.configure_dap_notretrieved(
                    node,
                    activities_config_map,
                    jit_types,
                    activity_name,
                    connections_by_key,
                    target_framework,
                )
                return additional_used_activities, []
            return [], []

        LOGGER.warning(f"{WorkflowProcessingErrorType.TRIGGER_DOES_NOT_EXIST.name} Trigger activity type: {node['activity']}. Keys: {list(node.keys())}")
        process["trigger"]["activity"] = self.activities_retriever.placeholder_trigger()
        return [], [ActivityError(type=WorkflowProcessingErrorType.TRIGGER_DOES_NOT_EXIST, activity_identifier=node["activity"], workflow_path=[])]

    async def postprocess_sequence_generation(
        self,
        model: langchain_core.language_models.BaseChatModel | None,
        result: str,
        existing_workflow: str,
        stitch_workflow: bool,
    ) -> tuple[str, str]:
        loaded = {}
        try:
            # note that at this point, result is not wrapped in a "workflow" key (it's just a sequence of activities)
            loaded, _ = await self._load_output_yaml(model, result, allow_triggers=False)
        except YAMLError:
            LOGGER.warning("YAMLError in sequence generation output")
            return result, result

        if loaded is None:
            loaded = {}

        if "workflow" not in loaded:
            loaded = {"workflow": loaded}
        sequence_as_workflow = yaml_dump(loaded, default_style="'").strip()

        if not existing_workflow or not stitch_workflow:
            return sequence_as_workflow, sequence_as_workflow

        # variant that uses the workflow api to do the replacement
        existing_workflow_lmyaml = yaml_load(existing_workflow)
        existing_workflow_object = Workflow("", "", existing_workflow_lmyaml)
        try:
            sequence = [
                Activity(
                    activity_lmyaml,
                    existing_workflow_object.arguments,
                    existing_workflow_object.variables,
                    parent=None,
                )
                for activity_lmyaml in loaded["workflow"]
            ]
        except Exception as e:
            LOGGER.error(f"Error converting Yaml to Activity objects: {e}")
            traceback.print_exc()
            return sequence_as_workflow, sequence_as_workflow
        SequenceGenerationGenericActivityReplacer(sequence).replace(existing_workflow_object)

        return existing_workflow_object.lmyaml(), sequence_as_workflow

    async def _postprocess_workflow_activity(
        self,
        node: dict,
        parent: dict,
        activity_config: dict,
        jit_types: dict[str, dict],
        name2fqn: dict[str, str],
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
        draft_only: bool = False,  # if the workflow is a draft (e.g. we might send it to the LLM to fix it), we want to omit any configuration that are redundant for the LLM to fix
        current_path: list[PathSegment] = [],
    ) -> t.Tuple[list[ActivityDefinition], list[dict], list[WorkflowProcessingError]]:
        if not isinstance(node, dict):
            LOGGER.warning(f"Node is not dict but a {type(node)}")
            return [], [], []  # we do not consider this a generation error, we should never reach this point if our logic is correct

        uia_scopes: list[dict] = []
        errors: list[WorkflowProcessingError] = []
        additional_used_activities: list[ActivityDefinition] = []
        has_activity_name = "activity" in node

        if has_activity_name:
            current_path = current_path + [PathSegment(segment_type="activity", name=node["activity"])]
            if not draft_only:
                # if workflow is just a draft, we don't want to add an empty params object or thought
                WorkflowGenerationPostProcessComponent._ensure_valid_params(node)
                if "thought" not in node:
                    node["thought"] = ""
            if node["activity"] in name2fqn:
                # TODO: We should remove this probably
                fqn = name2fqn[node["activity"]]
                print("INFO: Replaced name with fqn", node["activity"], fqn)
                node["activity"] = fqn
            if (
                node["activity"] in activity_config
                and "configuration" in activity_config[node["activity"]]
                and activity_config[node["activity"]]["configuration"] is not None
            ):
                # verify it's actually an activity and not a trigger
                _, is_connector_activity = self.activities_retriever.activity_exists(node["activity"], target_framework, "activity")
                if not draft_only and is_connector_activity:
                    # in draft mode, we don't want to configure DAP specific properties (they bloat the workflow and are irrelevant for the LLM)
                    self.dynamic_activities_component.configure_dynamic_connected_activity(activity_config, node)
                elif not is_connector_activity:
                    # the following validity check might be redundant due to the previous if checks, but keeping it for future proofing this part
                    _, is_connector_trigger = self.activities_retriever.activity_exists(node["activity"], target_framework, "trigger")
                    if is_connector_trigger:
                        LOGGER.warning(f"{WorkflowProcessingErrorType.TRIGGER_USED_IN_WORKFLOW.name} {node['activity']} {list(node.keys())}")
                        errors.append(
                            ActivityError(
                                type=WorkflowProcessingErrorType.TRIGGER_USED_IN_WORKFLOW,
                                activity_identifier=self._get_activity_node_identifier(node),
                                workflow_path=current_path,
                            )
                        )

                    else:
                        LOGGER.warning(f"{WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST.name} {node['activity']} {list(node.keys())}")
                        errors.append(
                            ActivityError(
                                type=WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST,
                                activity_identifier=self._get_activity_node_identifier(node),
                                workflow_path=current_path,
                            )
                        )

                    node["activity"] = self.activities_retriever.placeholder_activity()
            else:
                activity_name, thought, connections = (
                    node["activity"],
                    node.get("thought", ""),
                    list(connections_by_key.values()),
                )
                activity_name = self._try_fix_activity_name(activity_name, thought, connections, target_framework, "activity")

                activity_full_id_exists, activity_full_class_name_exists = self.activities_retriever.activity_exists(
                    activity_name,
                    target_framework,
                    "activity",
                )
                if activity_full_id_exists and activity_name != node["activity"]:
                    print("INFO: Replaced activity fqn", node["activity"], activity_name)
                    node["activity"] = activity_name

                if not draft_only and not activity_full_id_exists and activity_full_class_name_exists:
                    # in draft mode, we don't want to configure DAP specific properties (they bloat the workflow and are irrelevant for the LLM)
                    # A DAP activity that was retrieved for a demo was actually used without it being also retrieved from the index
                    current_additional_used_activities = await self.dynamic_activities_component.configure_dap_notretrieved(
                        node,
                        activity_config,
                        jit_types,
                        activity_name,
                        connections_by_key,
                        target_framework,
                    )
                    additional_used_activities.extend(current_additional_used_activities)

                if not activity_full_id_exists and not activity_full_class_name_exists:
                    LOGGER.warning(f"{WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST.name} {activity_name} {list(node.keys())}")
                    errors.append(
                        ActivityError(type=WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST, activity_identifier=activity_name, workflow_path=current_path)
                    )
            if not draft_only:
                WorkflowGenerationPostProcessComponent._add_connection_details(connections_by_key, node)

            if node["activity"] == UI_APPLICATION_CARD_ACTIVITY_NAME:
                uia_scopes.append({"node": node, "parent": parent})

        # process any nested activities within the current activity and extract any errors
        for key, value in node.items():
            if isinstance(value, dict):
                path_segment = [PathSegment(segment_type="property", name=key)] if key != "params" else []
                item_additional_used_activities, item_uia_scopes, item_errors = await self._postprocess_workflow_activity(
                    value,
                    node,
                    activity_config,
                    jit_types,
                    name2fqn,
                    connections_by_key,
                    target_framework,
                    draft_only,
                    current_path + path_segment,
                )
                errors.extend(item_errors)
                additional_used_activities.extend(item_additional_used_activities)
                uia_scopes.extend(item_uia_scopes)

            elif isinstance(value, list):
                for i, elem in enumerate(value):
                    if isinstance(elem, dict):
                        item_additional_used_activities, item_uia_scopes, item_errors = await self._postprocess_workflow_activity(
                            elem,
                            node,
                            activity_config,
                            jit_types,
                            name2fqn,
                            connections_by_key,
                            target_framework,
                            draft_only,
                            current_path + [PathSegment(segment_type="property", name=f"{key}[{i}]")],
                        )
                        errors.extend(item_errors)
                        additional_used_activities.extend(item_additional_used_activities)
                        uia_scopes.extend(item_uia_scopes)
        return additional_used_activities, uia_scopes, errors

    async def postprocess_generation(
        self,
        model: langchain_core.language_models.BaseChatModel | None,
        workflow_raw_yaml: str,
        query: str,
        jit_types: dict[str, dict],
        activities_config_map: dict[str, dict],
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
        objects: list[workflow_generation_schema.UIObject],
        activities_and_triggers: list[ActivityDefinition],
        variables: list[workflow_generation_schema.Variable],
        localization: str | None,
        allow_triggers: bool = True,
        allow_assign_activity_on_uia_expansion: bool = False,
        consuming_feature_type: ConsumingFeatureType = ConsumingFeatureType.DEFAULT,
    ) -> workflow_generation_schema.WorkflowGenerationResult:
        parsing_details = await self.process_and_validate_generation(
            model=model,
            workflow_raw_yaml=workflow_raw_yaml,
            query=query,
            jit_types=jit_types,
            activities_config_map=activities_config_map,
            connections_by_key=connections_by_key,
            target_framework=target_framework,
            objects=objects,
            activities_and_triggers=activities_and_triggers,
            variables=variables,
            localization=localization,
            consuming_feature_type=consuming_feature_type,
            allow_triggers=allow_triggers,
            allow_assign_activity_on_uia_expansion=allow_assign_activity_on_uia_expansion,
        )
        return parsing_details.workflow_generation

    async def process_and_validate_generation(
        self,
        model: langchain_core.language_models.BaseChatModel | None,
        workflow_raw_yaml: str,
        query: str,
        jit_types: dict[str, dict],
        activities_config_map: dict[str, dict],
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
        objects: list[workflow_generation_schema.UIObject],
        activities_and_triggers: list[ActivityDefinition],
        variables: list[workflow_generation_schema.Variable],
        localization: str | None,
        consuming_feature_type: ConsumingFeatureType = ConsumingFeatureType.DEFAULT,
        draft_only: bool = False,
        add_activity_ids: bool = False,
        allow_triggers: bool = True,
        allow_assign_activity_on_uia_expansion: bool = False,
        merge_lists_on_conflicting_keys_in_yaml: bool = False,
    ) -> workflow_generation_schema.ParsedWorkflowDetails:
        """
        This function is used to postprocess the generation of a workflow.

        allow_triggers: this should be inhibited for sequence generation
        allow_assign_activity_on_uia_expansion: this should be enabled for testcase generation
        """
        # Post-process YAML for awareness of user context
        workflow_preprocessed_yaml = self.set_workflow_user_context(workflow_raw_yaml)
        workflow_dict, yaml_parsing_errors = await self._load_output_yaml(
            model, workflow_preprocessed_yaml, allow_triggers, merge_lists_on_conflicting_keys_in_yaml
        )

        # remove trigger if it's not allowed
        if not allow_triggers:
            if "trigger" in workflow_dict:
                del workflow_dict["trigger"]
            workflow_dict_workflow = workflow_dict.get("workflow", [])
            if len(workflow_dict_workflow) > 0:
                first_activity_definition = self.activities_retriever.get(workflow_dict_workflow[0]["activity"], target_framework)
                if first_activity_definition is not None and first_activity_definition.get("isTrigger", False):
                    del workflow_dict_workflow[0]

        is_response_valid_yaml = workflow_dict != {}
        dap_name2fqn = {}
        for fqn in activities_config_map:
            dap_name2fqn[fqn.split(".")[-1]] = fqn
        workflow_valid_dict, additional_used_activities, uia_scopes, processing_errors = await self._postprocess_workflow(
            workflow_dict=workflow_dict,
            activities_config_map=activities_config_map,
            jit_types=jit_types,
            name2fqn=dap_name2fqn,
            connections_by_key=connections_by_key,
            target_framework=target_framework,
            allow_triggers=allow_triggers,
            draft_only=draft_only,
        )
        self.remove_null_values(workflow_valid_dict)

        # If we are generating a test case with UI Automation, we need to expand the UI Automation scopes
        if not draft_only and objects and uia_scopes:
            # Convert back to mode for compatibility with existing code
            await expand_uia_scopes(variables, objects, uia_scopes, query, allow_assign_activity_on_uia_expansion)

        is_activities_triggers_map = {}
        for activity_or_trigger in activities_and_triggers:
            if activity_or_trigger.get("activityTypeId", None) is not None:
                is_activities_triggers_map[activity_or_trigger["fullActivityId"]] = activity_or_trigger

        for activity in additional_used_activities:
            if activity.get("activityTypeId", None) is not None:
                is_activities_triggers_map[activity["fullActivityId"]] = activity

        used_trigger, used_activities, used_packages = self.get_used_entities(workflow_valid_dict, target_framework)
        used_trigger_and_activities = []
        if used_trigger is not None:
            used_trigger_and_activities.append(used_trigger)
        used_trigger_and_activities.extend(used_activities)

        if add_activity_ids:
            # having ids makes identifying errors easier - especially if we want to pass them to the LLM to fix
            add_workflow_ids(workflow_valid_dict)

        used_variables, used_local_variables, used_namespaces, activity_errors = self._get_workflow_details(
            process=workflow_valid_dict,
            available_definitions=activities_and_triggers,
            activities_and_triggers=is_activities_triggers_map,
            existing_variables=variables,
            target_framework=target_framework,
            skip_validation=not draft_only,  # we only want to validate the activity params if we are not in draft mode
        )
        used_jit_types = WorkflowGenerationPostProcessComponent.get_used_jit_types(used_trigger_and_activities, jit_types, activities_config_map)
        used_connectors = self.get_used_connectors(used_trigger_and_activities, connections_by_key, target_framework)

        # add extra information to workflow yaml, but only if we are not in draft mode - those workflows might be sent back to the LLM to fix.
        if isinstance(workflow_valid_dict, dict) and not draft_only:
            workflow_valid_dict["packages"] = used_packages
            workflow_valid_dict["variables"] = used_variables
            workflow_valid_dict["namespaceImports"] = used_namespaces

        self._postprocess_thoughts(workflow_valid_dict)

        # change language for display names
        if localization and not localization.startswith("en"):
            await self._translate_display_names(workflow_valid_dict, localization, consuming_feature_type)

        workflow_valid_yaml = yaml_dump(workflow_valid_dict)

        # remove arguments from the workflow for now, we don't use them or configure them properly yet
        if isinstance(workflow_valid_dict, dict):
            workflow_valid_dict.pop("arguments", None)

        workflow_gen_result = workflow_generation_schema.WorkflowGenerationResult(
            is_response_valid_yaml=is_response_valid_yaml,
            workflow_raw=workflow_raw_yaml,
            workflow_valid=workflow_valid_yaml,
            used_trigger=used_trigger,
            used_activities=used_activities,
            used_jit_types=used_jit_types,
            used_connectors=used_connectors,
            used_packages=used_packages,
            used_variables=used_variables,
            used_local_variables=used_local_variables,
            used_namespaces=used_namespaces,
        )
        return workflow_generation_schema.ParsedWorkflowDetails(
            workflow_generation=workflow_gen_result,
            post_generation_errors=processing_errors + activity_errors + yaml_parsing_errors,
        )

    def set_workflow_user_context(self, workflow_raw_yaml: str) -> str:
        workflow_preprocessed_yaml = workflow_raw_yaml
        request_context = request_utils.get_request_context()
        if request_context is not None:
            # asserts to raise awareness in case of issues with the request context
            assert request_context.email is not None
            assert request_context.first_name is not None
            assert request_context.last_name is not None

            workflow_preprocessed_yaml = workflow_preprocessed_yaml.replace("<EMAIL>", request_context.email)
            workflow_preprocessed_yaml = workflow_preprocessed_yaml.replace(
                "autopilotuseremail_autopilot_com",
                request_context.email.replace("@", "_").replace(".", "_"),  # type: ignore
            )
            workflow_preprocessed_yaml = workflow_preprocessed_yaml.replace("AutopilotUser123", request_context.first_name)
            workflow_preprocessed_yaml = workflow_preprocessed_yaml.replace("StudioUser123", request_context.last_name)

            if workflow_preprocessed_yaml.find("https://autopilot-my.sharepoint.com") != -1:
                company_domain_pattern = r"(?<=@)[\w-]+(^\.)*"
                company_matches = re.search(company_domain_pattern, request_context.email)
                if company_matches is not None:
                    workflow_preprocessed_yaml = workflow_preprocessed_yaml.replace(
                        "https://autopilot-my.sharepoint.com",
                        "https://{company}-my.sharepoint.com".format(company=company_matches.group()),
                    )
        return workflow_preprocessed_yaml

    def remove_null_values(self, process: dict) -> None:
        if not isinstance(process, dict):
            LOGGER.warning(f"Process is not dict but a {type(process)}")
            return
        self._remove_null_values(process.get("trigger", {}))
        for activity in process["workflow"]:
            self._remove_null_values(activity)

    def _remove_null_values(self, node: dict) -> None:
        if not isinstance(node, dict):
            LOGGER.warning(f"Node is not dict but a {type(node)}")
            return
        nulls = []
        for key, value in node.items():
            if value is None:
                nulls.append(key)
        for key in nulls:
            del node[key]
        for value in node.values():
            if isinstance(value, dict):
                self._remove_null_values(value)
            elif isinstance(value, list):
                for elem in value:
                    if isinstance(elem, dict):
                        self._remove_null_values(elem)

    def get_used_entities(self, process: dict, target_framework: TargetFramework) -> tuple[str | None, list[str], list[str]]:
        if not process or not isinstance(process, dict):
            return "", [], []
        trigger_node = process.get("trigger", {})
        trigger = None
        if "activity" in trigger_node:
            trigger = WorkflowGenerationPostProcessComponent._get_full_activity_id(trigger_node)
        activities = []
        for node in process["workflow"]:
            self._get_used_activities(node, activities)
        packages = self._get_used_packages(trigger, activities, target_framework)
        return trigger, activities, packages

    def get_used_connectors(
        self,
        trigger_and_activities: list[str],
        connections_by_key: dict,
        target_framework: TargetFramework,
    ) -> list[dict[str, str]]:
        used_connectors = {}
        for trigger_or_activity in trigger_and_activities:
            trigger_or_activity = self.activities_retriever.get(trigger_or_activity, target_framework)
            if trigger_or_activity is None or trigger_or_activity.get("connectorKey", None) is None:
                continue
            trigger_or_activity_connector_key = trigger_or_activity["connectorKey"]
            trigger_or_activity_connection_id = connections_by_key.get(trigger_or_activity_connector_key, {}).get("connectionId", None)
            trigger_or_activity_connector = {"connectionId": trigger_or_activity_connection_id, "connector": trigger_or_activity_connector_key}
            used_connectors[tuple(trigger_or_activity_connector.items())] = trigger_or_activity_connector
        return list(used_connectors.values())

    def _get_used_activities(self, node: dict, activities: list[str]) -> None:
        if not isinstance(node, dict):
            LOGGER.warning(f"Node is not dict but a {type(node)}")
            return
        if "activity" in node and node["activity"] not in activities:
            activities.append(WorkflowGenerationPostProcessComponent._get_full_activity_id(node))
        for value in node.values():
            if value and isinstance(value, dict):
                self._get_used_activities(value, activities)
            elif isinstance(value, list):
                for elem in value:
                    if elem and isinstance(elem, dict):
                        self._get_used_activities(elem, activities)

    def _get_used_packages(
        self,
        trigger: str | None,
        activities: list[str],
        target_framework: TargetFramework,
    ) -> list[str]:
        packages = []
        if trigger is not None:
            package = self.activities_retriever.get_versioned_package(trigger, target_framework, "trigger")
            if package and package not in packages and not package.startswith("System.Activities,"):
                packages.append(package)
        for activity in activities:
            package = self.activities_retriever.get_versioned_package(activity, target_framework, "activity")
            if package and package not in packages and not package.startswith("System.Activities,"):
                packages.append(package)
        return packages

    @staticmethod
    def _get_used_variable_type(activity: ActivityDefinition, node_activity: str, param_name: str) -> str | None:
        param_type = activity["params"][param_name]["type"]
        # non-generic OutArgument variables are of type object
        variable_type = "object"
        # param_type might be OutArgument<T> or OutArgument<specific_type>, eg: OutArgument<DataRow>
        # if it is OutArgument<T>, generic_param_definition is "object" or "class", so we try to use the activity type inferred by the model for the activity
        # if generic_param_definition is not "object", we use the specific type from the param_type
        param_match = re.search(r"OutArgument<(.*)>", param_type)
        if param_match:
            variable_type = param_match.group(1)
            generic_param_definition = activity["genericParamDefinition"].get(variable_type, None)
            if generic_param_definition is not None:
                predicted_activity_type_match = re.search(r"<(.*)>", node_activity)
                if predicted_activity_type_match is not None:  # the model predicted a type, e.g. MyActivity<string>
                    variable_type = predicted_activity_type_match.group(1)
                else:
                    variable_type = generic_param_definition

        return variable_type

    def _validate_activity_draft(
        self,
        activity_node: dict,
        available_definitions: list[ActivityDefinition],
        path: list[PathSegment],
        wf_variables: dict[str, str],
        existing_variables: set[str],
    ) -> list[WorkflowProcessingError]:
        if "activity" not in activity_node:
            return []  # this is not an activity node, so we don't want to validate it

        errors: list[WorkflowProcessingError] = []

        activity_name, _ = helpers.extract_generic_pattern(activity_node["activity"])
        matching_definitions = [a for a in available_definitions if a["fullClassName"] == activity_name]

        if len(matching_definitions) > 1:
            raise ValueError(f"Multiple activity definitions found for {activity_name}")

        if len(matching_definitions) == 0:
            return [
                ActivityError(
                    workflow_path=path,
                    activity_identifier=self._get_activity_node_identifier(activity_node),
                    type=WorkflowProcessingErrorType.UNEXPECTED_ACTIVITY_USED,
                )
            ]
        if "params" not in activity_node:
            return []
        if not isinstance(activity_node["params"], dict):
            return [
                ActivityError(
                    workflow_path=path,
                    activity_identifier=self._get_activity_node_identifier(activity_node),
                    type=WorkflowProcessingErrorType.INVALID_PARAMS_STRUCTURE,
                )
            ]

        activity_def = matching_definitions[0]
        if len(activity_def["params"]) == 0:
            return []

        for param_name, param_value in activity_node["params"].items():
            if param_name not in activity_def["params"]:
                errors.append(
                    MissingParamError(workflow_path=path, activity_identifier=self._get_activity_node_identifier(activity_node), param_name=param_name)
                )
                continue

            param_type_category = activity_def["params"].get(param_name, {}).get("category")
            input_variable_name = try_extract_input_variable_reference(param_value, param_type_category)
            if input_variable_name is not None and not (input_variable_name in existing_variables or input_variable_name in wf_variables):
                # input variable is not yet defined, so we add it to the list of errors
                errors.append(
                    UndefinedVariableError(
                        workflow_path=path,
                        activity_identifier=self._get_activity_node_identifier(activity_node),
                        param_name=param_name,
                        variable_name=input_variable_name,
                    )
                )
                continue

            value_category = get_param_value_category(param_value)
            if (
                param_type_category in PARAM_VALUE_CATEGORY_COMPATIBILITIES_MAP
                and value_category not in PARAM_VALUE_CATEGORY_COMPATIBILITIES_MAP[param_type_category]
            ):
                errors.append(
                    ParamValueMismatchError(
                        workflow_path=path,
                        activity_identifier=self._get_activity_node_identifier(activity_node),
                        param_name=param_name,
                        value=param_value,
                        param_category=param_type_category,
                        value_category=value_category,
                    )
                )

        return errors

    @staticmethod
    def _get_activity_node_identifier(node: dict) -> str:
        return node.get("id", node["activity"])

    @staticmethod
    def _get_variables_from_activity_params(node, variables, local_variables, renamed_variables, existing_variables, activity):
        for param_name in node.get("params", {}):
            if param_name not in activity["params"]:
                continue
            if not isinstance(node["params"][param_name], str):
                continue
                # strip whitespace from expression.
            match = re.match(r"\[\[\s*(.*)\s*\]\]", node["params"][param_name])
            if match is None:
                continue
            expression = match.group(1)
            # Replace invalid named variables with previously corrected valid ones.
            for original_name, new_name in renamed_variables.items():
                expression = expression.replace(original_name, new_name)
            if activity["params"][param_name]["type"].startswith("OutArgument"):
                # make sure variable name is valid
                old_variable_name = expression
                new_variable_name = re.sub(r"\W", "_", old_variable_name)
                if new_variable_name not in local_variables and new_variable_name not in existing_variables:
                    # infer variable type from activity
                    variable_type = WorkflowGenerationPostProcessComponent._get_used_variable_type(activity, node["activity"], param_name)
                    variable = {"name": new_variable_name, "type": variable_type}
                    # register variable and update activity node
                    variables.append(variable)
                    if old_variable_name != new_variable_name:
                        renamed_variables[old_variable_name] = new_variable_name
                    node["params"][param_name] = f"[[{new_variable_name}]]"
            else:
                # update activity node
                node["params"][param_name] = f"[[{expression}]]"

    @staticmethod
    def _get_variables_from_uia_activity(
        node: dict,
        variables: list[dict],
        local_variables: dict[str, str],
        renamed_variables: dict[str, str],
        existing_variables: set[str],
    ) -> None:
        if "params" not in node:
            node["params"] = {}
        params = node["params"]
        if "AutoGenerationOptions" in params:
            if not isinstance(params["AutoGenerationOptions"], dict):
                params["AutoGenerationOptions"] = {}
            auto_gen_options = params["AutoGenerationOptions"]
            if "ExpectedInputs" in auto_gen_options and not isinstance(auto_gen_options["ExpectedInputs"], list):
                auto_gen_options["ExpectedInputs"] = []
            if "ExpectedOutputs" in auto_gen_options and not isinstance(auto_gen_options["ExpectedOutputs"], list):
                auto_gen_options["ExpectedOutputs"] = []
        expected_outputs = []
        for output in params.get("AutoGenerationOptions", {}).get("ExpectedOutputs", []):
            if not isinstance(output, str):
                LOGGER.warning(f"Expected output is not a string but a {type(output)}")
                continue
                # strip whitespace from variable name and expression markers
            old_variable_name = output.strip().removeprefix("[[").removesuffix("]]").strip()
            # make sure the variable name is valid
            new_variable_name = re.sub(r"\W", "_", old_variable_name)
            # register the new variable
            variable = {"name": new_variable_name, "type": "string"}
            expected_outputs.append(f"[[{new_variable_name}]]")
            if variable["name"] not in existing_variables and variable["name"] not in local_variables:
                variables.append(variable)
            if old_variable_name != new_variable_name:
                renamed_variables[old_variable_name] = new_variable_name
        if "AutoGenerationOptions" not in node["params"]:
            node["params"]["AutoGenerationOptions"] = {}
        node["params"]["AutoGenerationOptions"]["ExpectedOutputs"] = expected_outputs

    @staticmethod
    def _get_variables_from_activity_variables(
        node: dict,
        local_variables: dict[str, str],
        renamed_variables: dict[str, str],
        existing_variables: set[str],
    ) -> None:
        for item in node.get("variables", []):
            # defaults in case of faulty model response
            item["name"] = item.get("name", "currentItem")
            item["type"] = item.get("type", "object")
            # strip whitespace from variable name
            old_variable_name = item["name"].strip()
            # make sure the variable name is valid
            new_variable_name = re.sub(r"\W", "_", old_variable_name)
            # update the variable name
            item["name"] = new_variable_name
            if item["name"] not in local_variables and item["name"] not in existing_variables:
                # register the new variable
                local_variables[new_variable_name] = item["type"]
                if old_variable_name != new_variable_name:
                    renamed_variables[old_variable_name] = new_variable_name

    @staticmethod
    def get_used_jit_types(used_trigger_and_activities: list[str], jit_types: dict[str, dict], activities_config_map: dict[str, dict]) -> dict[str, dict]:
        """Returns a list of jit types used by current activities, removing duplicates"""
        used_jit_types = {}
        for jit_activity_name, activity_types in jit_types.items():
            activity_definition = activities_config_map[jit_activity_name]
            qualified_activity_name = embeddingsdb.create_dynamic_activity_full_id(
                activity_definition["actualActivityName"], activity_definition["activityTypeId"]
            )

            if qualified_activity_name not in used_trigger_and_activities:
                continue
            for jit_type in activity_types:
                assembly_name = jit_type["EntityAssemblyKey"]["AssemblyName"]
                if assembly_name not in used_jit_types:
                    used_jit_types[assembly_name] = jit_type

        return list(used_jit_types.values())

    def get_used_variables(
        self,
        process: dict,
        activities_and_triggers: dict,
        existing_variables: list[Variable],
        target_framework: TargetFramework,
    ) -> tuple[list[Variable], list[Variable], list[str]]:
        variables, local_variables, namespaces, _ = self._get_workflow_details(process, [], activities_and_triggers, existing_variables, target_framework)
        return variables, local_variables, namespaces

    def _get_workflow_details(
        self,
        process: dict,
        available_definitions: list[ActivityDefinition],
        activities_and_triggers: dict,
        existing_variables: list[Variable],
        target_framework: TargetFramework,
        skip_validation: bool = True,
    ) -> tuple[list[Variable], list[Variable], list[str], list[WorkflowProcessingError]]:
        """Extracts details from a workflow dictionary, such as variables, local variables, renamed variables, and namespaces"""
        activity_errors: list[WorkflowProcessingError] = []
        existing_variables_set = {variable["name"] for variable in existing_variables}
        if not process or not isinstance(process, dict):
            return [], [], [], []
        variables, local_variables, renamed_variables, namespaces = [], {}, {}, []
        trigger_errors = self._get_details_from_activity(
            process.get("trigger", {}),
            available_definitions,
            variables,
            local_variables,
            renamed_variables,
            namespaces,
            activities_and_triggers,
            existing_variables_set,
            target_framework,
            skip_validation,
        )
        activity_errors.extend(trigger_errors)

        for node in process["workflow"]:
            node_errors = self._get_details_from_activity(
                node,
                available_definitions,
                variables,
                local_variables,
                renamed_variables,
                namespaces,
                activities_and_triggers,
                existing_variables_set,
                target_framework,
                skip_validation,
            )
            activity_errors.extend(node_errors)

        local_variables = [Variable(name=key, type=value) for key, value in local_variables.items()]
        # Add existing variables to local variables
        variables.extend(existing_variables)

        return variables, local_variables, list(namespaces), activity_errors

    def _get_details_from_activity(
        self,
        node: dict,
        available_definitions: list[ActivityDefinition],
        variables: list[dict],
        local_variables: dict[str, str],
        renamed_variables: dict[str, str],
        namespaces: list[str],
        activities_and_triggers: dict,
        existing_variables: set[str],
        target_framework: TargetFramework,
        skip_validation: bool = False,
        current_path: list[PathSegment] = [],
    ) -> list[WorkflowProcessingError]:
        if not isinstance(node, dict):
            LOGGER.warning(f"Node is not dict but a {type(node)}")
            return []  # we don't want to return errors here, we want to skip the current object
        activity = None
        if "activity" in node:
            activity_full_id = WorkflowGenerationPostProcessComponent._get_full_activity_id(node)

            # Attempt to retrieve it from the activities_and_triggers list since it has the DAP-updated type definitions,
            # otherwise retrieve it from the retriever (in case it was not a relevant activity but came from an example's type definitions)
            # activities_and_triggers is keyed by activityTypeId to prevent conflicts from same fqn (ConnectorActivity)
            activity = activities_and_triggers.get(activity_full_id, None) or self.activities_retriever.get(activity_full_id, target_framework, None)
            if activity is not None:
                for ns in activity["additionalNamespaces"].splitlines():
                    if ns and ns not in namespaces:
                        namespaces.append(ns)
        activity_errors = []

        if activity is not None:
            if "params" in node and not isinstance(node["params"], dict):  # add draft only param here
                node["params"] = {}

            if not skip_validation:
                wf_variables = {**local_variables, **renamed_variables}
                activity_errors.extend(self._validate_activity_draft(node, available_definitions, current_path, wf_variables, existing_variables))

            # UIA special case
            if activity["fullClassName"] == "UiPath.UIAutomationNext.Activities.NApplicationCard":
                WorkflowGenerationPostProcessComponent._get_variables_from_uia_activity(
                    node,
                    variables,
                    local_variables,
                    renamed_variables,
                    existing_variables,
                )
            else:
                WorkflowGenerationPostProcessComponent._get_variables_from_activity_params(
                    node,
                    variables,
                    local_variables,
                    renamed_variables,
                    existing_variables,
                    activity,
                )
        WorkflowGenerationPostProcessComponent._get_variables_from_activity_variables(node, local_variables, renamed_variables, existing_variables)

        if "activity" in node:
            current_path = current_path + [PathSegment(segment_type="activity", name=node["activity"])]

        for key, value in node.items():
            if isinstance(value, list):
                for i, elem in enumerate(value):
                    if elem and isinstance(elem, dict):
                        node_errors = self._get_details_from_activity(
                            elem,
                            available_definitions,
                            variables,
                            local_variables,
                            renamed_variables,
                            namespaces,
                            activities_and_triggers,
                            existing_variables,
                            target_framework,
                            skip_validation,
                            current_path + [PathSegment(segment_type="property", name=f"{key}[{i}]")],
                        )
                        activity_errors.extend(node_errors)

            elif isinstance(value, dict):
                path_segment = [PathSegment(segment_type="property", name=key)] if key != "params" else []
                node_errors = self._get_details_from_activity(
                    value,
                    available_definitions,
                    variables,
                    local_variables,
                    renamed_variables,
                    namespaces,
                    activities_and_triggers,
                    existing_variables,
                    target_framework,
                    skip_validation,
                    current_path + path_segment,
                )
                activity_errors.extend(node_errors)

        return activity_errors

    def _postprocess_thoughts(self, worfklow_dict: dict):
        """
        Processes a workflow dictionary and applies different changes to the thoughts in the workflow.

        Args:
            worfklow_dict (dict): The workflow dict dictionary to process.

        """
        # remove potential " - using {category}" from thoughts
        self._apply_thoughts_transformation(worfklow_dict, lambda s: s.split(" - using")[0] if s else s)

    def _apply_thoughts_transformation(self, data: dict | list, func: t.Callable[[str], str]) -> None:
        """
        Processes a workflow dictionary and tries to apply a transformation function to "thought" value.

        Args:
            data (dict): The workflow dict dictionary to process.
            func (function): The function to apply to each "thought" value.

        """
        if isinstance(data, dict):
            for key, value in data.items():
                if key == "thought":
                    data[key] = func(value)
                else:
                    self._apply_thoughts_transformation(value, func)
        elif isinstance(data, list):
            for item in data:
                self._apply_thoughts_transformation(item, func)

    def _get_thoughts(self, data, values):
        if isinstance(data, dict):
            for key, value in data.items():
                if key == "thought":
                    values.append(value)
                else:
                    self._get_thoughts(value, values)
        elif isinstance(data, list):
            for item in data:
                self._get_thoughts(item, values)
        return values

    def _replace_thoughts(self, data, processed_values):
        if isinstance(data, dict):
            for key, value in data.items():
                if key == "thought":
                    data[key] = processed_values.pop(0)
                else:
                    self._replace_thoughts(value, processed_values)
        elif isinstance(data, list):
            for _, item in enumerate(data):
                self._replace_thoughts(item, processed_values)

    def _try_fix_activity_name(
        self,
        activity_name: str,
        thought: str,
        connections: list[Connection],
        target_framework: TargetFramework,
        activity_type: ActivityType,
    ):
        fixed_activity_name = self.activities_retriever.try_fix_activity_name(
            activity_name, thought, connections, target_framework, activity_type, self.ignored_activities[target_framework]
        )
        if fixed_activity_name is not None and fixed_activity_name != activity_name:
            print(f"Fixed activity name from {activity_name} to {fixed_activity_name}")
            return fixed_activity_name
        return activity_name

    @staticmethod
    def _get_full_activity_id(activity: dict) -> str:
        """Use fullname@typeid for DAP, else use fully qualified name for regular"""
        full_activity_id, _ = extract_generic_pattern(activity["activity"])
        if type_id := activity.get("params", {}).get("UiPathActivityTypeId"):
            full_activity_id = embeddingsdb.create_dynamic_activity_full_id(full_activity_id, type_id)
        elif type_id := activity.get("uiPathActivityTypeId"):
            full_activity_id = embeddingsdb.create_dynamic_activity_full_id(full_activity_id, type_id)
        return full_activity_id

    async def _translate_display_names(self, workflow_dict: dict, localization: str, feature: ConsumingFeatureType) -> None:
        # maybe do a deep copy
        display_names = []
        self._get_thoughts(workflow_dict, display_names)
        request: MultiTextTranslationRequest = {
            "target_language": localization,
            "input_strings": display_names,
            "feature": feature,
        }
        response = await TRANSLATE_TASK.translate_multi_str(request)
        if response is None:
            LOGGER.error("Translation failed. Response is None.")
        elif not isinstance(response, list):
            LOGGER.error("Translation failed. Response is not a list.")
        elif len(response) != len(display_names):
            LOGGER.error(f"Translation failed. Different number of translated strings. Needed {len(display_names)} but got {len(response)}")
        else:
            self._replace_thoughts(workflow_dict, response)

    @staticmethod
    def _add_connection_details(connections_by_key: dict[str, Connection], node: dict) -> None:
        productivity_connection_key = PRODUCTIVITY_ACTIVITY2CONNECTION.get(node["activity"], None)
        if productivity_connection_key in connections_by_key:
            node["connectorKey"] = productivity_connection_key
            node["params"]["ConnectionId"] = connections_by_key[productivity_connection_key]["connectionId"]

    @staticmethod
    def _ensure_valid_params(node: dict) -> None:
        if "params" not in node:
            LOGGER.info("Added empty params", node)
            node["params"] = {}
        if not isinstance(node["params"], dict):
            LOGGER.warning(f"Node params is not dict but a {type(node['params'])}")
            node["params"] = {}
