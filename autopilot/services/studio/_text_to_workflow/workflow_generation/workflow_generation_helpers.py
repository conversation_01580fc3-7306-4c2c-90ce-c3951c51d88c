import json
import pathlib

from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.common.schema import Variable
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.common.workflow import Activity, Workflow
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowActivityDetails


def get_config_path(name: str = "prompt.yaml") -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / "config" / name


def get_identifier(path: pathlib.Path) -> str:
    task, target_framework, _split, name, *_ = path.relative_to(paths.get_autopilot_samples_dataset_path()).parts
    name = name.removesuffix(".yaml")  # for workflow generation
    return f"{target_framework}/{name}"


def load_predefined_workflow_from_dump(path: pathlib.Path | None) -> dict | None:
    """
    Path can contain either
    - a dump of the request: "POST...\n\n{..."
    - a json: containing the request body of a request "{..."
    - a yaml: containing the actual current worklow
    """

    if path is None:
        return None

    with path.open("r") as fd:
        content = fd.read()
        if content[:4] == "POST":
            _headers, content = content.split("\n\n", 1)

        if content[:1] == "{":
            return yaml_load(json.loads(content)["currentWorkflow"])
        else:
            return yaml_load(fd)


def load_predefined_variables_from_dump(path: pathlib.Path | None) -> list[Variable] | None:
    """
    Path can contain either
    - a dump of the request: "POST...\n\n{..."
    - a json or yaml containing the actual variables list
    """
    if path is None:
        return None

    with path.open("r") as fd:
        content = fd.read()
        if content[:4] == "POST":
            _headers, content = content.split("\n\n", 1)
            return json.loads(content)["availableVariables"]
        elif content[:1] == "[":
            return json.loads(content)
        else:
            return yaml_load(fd)


def load_predefined_additional_type_definitions(path: pathlib.Path | None) -> str | None:
    if path is None:
        return None
    with path.open("r") as fd:
        content = fd.read()
        if content[:4] == "POST":
            _headers, content = content.split("\n\n", 1)
            return json.loads(content)["availableAdditionalTypeDefinitions"]
        return content


def get_workflow_activities(workflow: Workflow) -> WorkflowActivityDetails:
    """Returns the activities and triggers of a workflow, removing duplicates"""
    activities = ActivitiesAndTriggersCollector().collect(workflow)
    retrieved_activities: dict[str, Activity] = {}
    for activity in activities["activity"]:
        activity_name, _ = extract_generic_pattern(activity.get_model_activity_name())
        retrieved_activities[activity_name] = activity

    if len(activities["trigger"]) > 0:
        trigger = activities["trigger"][0]
        trigger_name, _ = extract_generic_pattern(trigger.get_model_activity_name())
        return WorkflowActivityDetails(trigger_full_name=trigger_name, trigger=trigger, activities=retrieved_activities)

    return WorkflowActivityDetails(trigger_full_name=None, trigger=None, activities=retrieved_activities)
