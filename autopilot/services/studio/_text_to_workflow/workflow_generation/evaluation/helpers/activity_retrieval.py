from dataclasses import dataclass

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflowDataPoint
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import (
    GenerationSettings,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityRetrievalGeneration,
    ProposedActivity,
    WfGenDataPointV2,
)


class ActivityUsageDetails:
    """Used to represent the location of a specific activity in the proposed."""

    def __init__(self, type_full_name: str):
        self.type_full_name = type_full_name
        self.missed_count = 0
        self.correctly_identified_count = 0
        self.missing_from_proposal = 0
        self.indexes: list[ActivityLocationIndex] = []


class ActivityLocationIndex:
    """Tracks the position of an activity in the proposal dataset. Useful when deciding how to finetune the list of candidates.
    The lower an index it, the more relevant the activity is according to the embedding model.
    "-1" means activity is missing from the proposal dataset."""

    def __init__(self, query: str, matched: bool, query_index: int | None = None, workflow_index: int | None = None):
        self.query = query
        self.query_index: int | None = query_index
        self.workflow_index: int | None = workflow_index
        self.matched = matched


@dataclass
class ActivityRetrievalEval:
    sample: WfGenDataPointV2 | ApiWorkflowDataPoint
    elapsed_time: float
    raw_activities_precision: float | None
    extended_activities_precision: float | None
    raw_activities_recall: float | None
    extended_trigger_precision: float | None
    raw_trigger_recall: float | None
    extended_trigger_recall: float | None
    extended_activities_recall: float | None
    score_precision: float
    baseline_activities_recall: float | None
    baseline_trigger_recall: float | None
    input_activity_indexes: dict[str, ActivityLocationIndex]
    ambiguities_similarity: float
    plan_similarity: float
    proposed_triggers: list[ProposedActivity]
    proposed_activities: list[ProposedActivity]
    retrieved_triggers: list[str]
    retrieved_activities: list[str]
    generation_settings: GenerationSettings
    generation: ActivityRetrievalGeneration
    token_usage: TokenUsage
    demonstrations: list[WfGenDataPointV2] | list[ApiWorkflowDataPoint]
    prompt: str
    raw_response: str
