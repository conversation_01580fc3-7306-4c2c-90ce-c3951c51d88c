import pathlib
import pickle
import typing as t

from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework
from services.studio._text_to_workflow.utils.paths import get_autopilot_samples_dataset_path, get_partial_eval_results_path
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset
from services.studio._text_to_workflow.workflow_generation.config.constants import ExcelPackageOption, MailPackageOption
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WfGenDataPointV2

Supported_Excel_Packages: list[ExcelPackageOption] = ["ExcelOnline", "ExcelDesktopBusiness"]
Supported_Mail_Packages: list[MailPackageOption] = ["MailOnline", "MailDesktopBusiness", "MailDesktopLegacy"]


def get_corresponding_pkl_path(path: pathlib.Path | str) -> pathlib.Path:
    """
    Given a path to a file, return the path to the corresponding .pkl file in the partial eval results folder.
    For paths containing either WF_GEN_V2_DATASET_NAME or API_WF_DATASET_NAME.
    """
    posix_path = pathlib.Path(path)
    # get relative path to the autopilot samples dataset
    relative_path = posix_path.relative_to(get_autopilot_samples_dataset_path())
    # move file in the partial eval results folder, keep the rest of the file structure
    result_path = get_partial_eval_results_path() / relative_path

    # change the extension to .pkl
    return result_path.with_suffix(".pkl")


def get_wf_generation_dataset(
    target_frameworks: list[TargetFramework],
    subsets: list[SubsetName],
) -> dict[str, WfGenDataPointV2]:
    dataset_dict: dict[str, WfGenDataPointV2] = {}
    for target_framework in target_frameworks:
        for subset in subsets:
            raw_dataset = workflow_generation_dataset.load_activity_retriever_subset(target_framework, subset)
            dataset_dict |= {path.as_posix(): WfGenDataPointV2(**data) for path, data in raw_dataset.items()}

    return dataset_dict


def get_cached_datapoint(path: pathlib.Path) -> t.Any:
    """
    Given a path to a file, return the serialized datapoint.
    """
    try:
        with path.open("rb") as f:
            return pickle.load(f)
    except Exception as e:
        raise RuntimeError(f"Unexpected error while extracting datapoint from {path}: {e}") from None
