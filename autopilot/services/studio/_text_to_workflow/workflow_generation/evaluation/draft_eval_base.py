import html
import json
import pathlib
import typing as t
from abc import abstractmethod

import numpy as np

from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import (
    same_activity_in_mapping,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.eval_base import BaseEvalService
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.draft_generation import DraftGenerationEval
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityError,
    EditPatchStructureError,
    InvalidConnectionError,
    InvalidExpressionError,
    MissingParamError,
    MissingRequiredFieldError,
    ParamValueMismatchError,
    UndefinedVariableError,
    WorkflowProcessingError,
)


class BaseDraftEvalService(BaseEvalService):
    """Base class for draft evaluation"""

    def __init__(
        self,
        output_file_prefix: str,
        batch_size: int = 4,
        retry_count: int = 3,
        force_recompute: bool = False,
    ):
        super().__init__(output_file_prefix, batch_size, retry_count)
        self.force_recompute = force_recompute

        # Load the common templates and configs
        eval_config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "eval_templates.yaml"
        eval_config = yaml_load(eval_config_path)
        self.common_templates = eval_config["common"]
        self.draft_generation_templates = eval_config["draft_generation"]

    @abstractmethod
    async def _evaluate_sample(self, path: pathlib.Path, sample: t.Any, connections: list[Connection]):
        """Method to be implemented by subclasses to evaluate a specific sample"""
        raise NotImplementedError()

    def _print_samples(self, log_file: pathlib.Path):
        with log_file.open("w") as f:
            f.write(self.common_templates["html_template_start"])
            f.write(self.draft_generation_templates["body_template_start"])

            eval_results: list[DraftGenerationEval] = t.cast(list[DraftGenerationEval], self.results)

            # collect all samples with critique data or exceptions
            serialized_exception_samples: list[str] = []

            for eval in eval_results:
                # determine which of the used activities were not proposed activities
                proposed_activities = {a["fullClassName"] for a in eval.proposed_activities}
                used_activities = {a for a in eval.ground_truth_activities}
                missed_activities = used_activities - proposed_activities

                serialized_demonstrations: list[str] = [self.serialize_demonstration(demonstration) for demonstration in eval.demonstrations]

                raw_gt_wf = self.serialize_workflow(eval.solution_workflow)
                existing_wf = self.serialize_workflow(eval.existing_workflow_sequence)
                raw_gen_wf = self.serialize_workflow(eval.raw_workflow)
                processed_draft_workflow = self.serialize_workflow(eval.processed_draft_workflow)

                post_generation_errors = eval.post_generation_errors or []

                # exclude undefined variable errors
                post_generation_errors = [error for error in post_generation_errors if not isinstance(error, UndefinedVariableError)]

                formatted_post_generation_errors = [self._format_generation_errors(error) for error in post_generation_errors]
                # add the index to the formatted post generation errors
                formatted_post_generation_errors = [f"{i + 1}. {error}" for i, error in enumerate(formatted_post_generation_errors)]
                error_text = "<hr>".join(formatted_post_generation_errors)

                # format the TED nodes mapping
                formatted_ted_nodes_mapping = html.escape(
                    "\n".join([f"same {m[0]}" if same_activity_in_mapping(m) else f"distinct {m}" for m in eval.ted_nodes_mapping[1:]])
                )

                # format the prompt
                messages = eval.prompt.to_messages()
                prompt = html.escape(f"\nSYSTEM:\n {messages[0].content}\n\nUSER:\n {messages[1].content}")

                f.write(
                    self.draft_generation_templates["sample_row_template"].format(
                        query=eval.query,
                        elapsed_time=eval.elapsed_time,
                        gen_wf=raw_gen_wf,
                        gen_wf_processed=processed_draft_workflow,
                        gt_wf=raw_gt_wf,
                        existing_workflow=existing_wf,
                        lev_score=eval.lev_score,
                        lev_score_no_extras=eval.lev_score_no_extras,
                        activity_tree_accuracy=eval.activity_tree_accuracy,
                        ted_score=eval.ted_score,
                        ted_levenstein_params_score=eval.ted_levenstein_params_score,
                        levenstein_params_score=eval.levenstein_params_score,
                        ted_exact_params_score=eval.ted_exact_params_score,
                        exact_params_score=eval.exact_params_score,
                        demonstrations="\n".join(serialized_demonstrations),
                        missed_activities="<br>".join(missed_activities),
                        token_usage=json.dumps(eval.token_usage.to_json(), indent=4),
                        post_generation_errors=error_text,
                        ted_nodes_mapping=formatted_ted_nodes_mapping,
                        prompt=prompt,
                        raw_model_prediction=html.escape(eval.raw_model_prediction),
                        tld_score=eval.tld_score if eval.tld_score is not None else -1,
                    )
                )

                # format all rows which either have a non-empty critique or exceptions - for better visibility
                if len(post_generation_errors) > 0:
                    serialized_exception_samples.append(
                        self.draft_generation_templates["errors_row_template"].format(
                            query=eval.query,
                            exceptions=error_text,
                            gt_wf=raw_gt_wf,
                            gen_wf_processed=raw_gen_wf,
                        )
                    )
            # build row with averages
            f.write(
                self.draft_generation_templates["averages_row_template"].format(
                    elapsed_time=np.mean([eval.elapsed_time for eval in eval_results]),
                    lev_score=np.mean([eval.lev_score for eval in eval_results]),
                    lev_score_no_extras=np.mean([eval.lev_score_no_extras for eval in eval_results]),
                    activity_tree_accuracy=np.mean([eval.activity_tree_accuracy for eval in eval_results]),
                    ted_score=np.mean([eval.ted_score for eval in eval_results]),
                    ted_levenstein_params_score=np.mean([eval.ted_levenstein_params_score for eval in eval_results]),
                    levenstein_params_score=np.mean([eval.levenstein_params_score for eval in eval_results]),
                    ted_exact_params_score=np.mean([eval.ted_exact_params_score for eval in eval_results]),
                    exact_params_score=np.mean([eval.exact_params_score for eval in eval_results]),
                    tld_score=np.mean(tld_scores) if len(tld_scores := [eval.tld_score for eval in eval_results if eval.tld_score is not None]) > 0 else -1,
                )
            )

            exceptions_section = (
                self.common_templates["parsing_exceptions_section_template"].format(
                    exception_count=len(serialized_exception_samples), exception_rows="\n".join(serialized_exception_samples)
                )
                if len(serialized_exception_samples) > 0
                else ""
            )

            # Write general information
            model_settings = self._get_model_settings()
            f.write(
                self.draft_generation_templates["general_info_template"].format(
                    exceptions_section=exceptions_section,
                    prompt=html.escape(self._get_system_prompt_config()).replace("\n", "<br>"),
                    model=json.dumps(model_settings, indent=4),
                )
            )
            f.write(self.common_templates["html_template_end"])

    @abstractmethod
    def serialize_demonstration(self, demonstration) -> str:
        raise NotImplementedError()

    @abstractmethod
    def serialize_workflow(self, workflow) -> str:
        raise NotImplementedError()

    @staticmethod
    def _format_generation_errors(error: WorkflowProcessingError) -> str:
        processed_segments = []
        current_segment = ""
        for segment in error.workflow_path:
            if segment.segment_type == "activity":
                # if we have a current segment, we need to add it to the processed segments and reset it
                if current_segment != "":
                    processed_segments.append(current_segment)
                current_segment = segment.name
            elif segment.segment_type == "property":
                # add the property to the current activity segment
                current_segment += f".{segment.name}"

        if current_segment != "":
            processed_segments.append(current_segment)

        path = "   =>   ".join(processed_segments) if processed_segments else "ROOT"
        return html.escape(f"{path}: {BaseDraftEvalService._extract_error_specific_info(error)} {error.type.name}")

    @staticmethod
    def _extract_error_specific_info(error: WorkflowProcessingError) -> str:
        if isinstance(error, UndefinedVariableError):
            return f"{error.activity_identifier}: Property {error.param_name} has undefined variable {error.variable_name}."
        elif isinstance(error, InvalidExpressionError):
            return f"{error.activity_identifier}: Property {error.param_name} has invalid expression {error.expression}. Exception: {error.error}."
        elif isinstance(error, MissingParamError):
            return f"{error.activity_identifier}: Property {error.param_name} is hallucinated."
        elif isinstance(error, ParamValueMismatchError):
            return f"{error.activity_identifier}: Property {error.param_name} of type {error.param_category} has an incompatible value of type {error.value_category}."
        elif isinstance(error, MissingRequiredFieldError):
            return f"{error.activity_identifier}: Property {error.field_name} is missing."
        elif isinstance(error, ActivityError):
            return f"{error.activity_identifier}: Activity definition error."
        elif isinstance(error, InvalidConnectionError):
            return f"Unable to connect to '{error.connector_key}'. Please check that this connection is properly configured."
        elif isinstance(error, EditPatchStructureError):
            return f"Unsolved merge conflict: {error.error}"
        return ""

    def _get_model_settings(self):
        """Override this method to return the model settings used for evaluation"""
        raise NotImplementedError()

    def _get_system_prompt_config(self):
        """Override this method to return the system prompt configuration"""
        raise NotImplementedError()
