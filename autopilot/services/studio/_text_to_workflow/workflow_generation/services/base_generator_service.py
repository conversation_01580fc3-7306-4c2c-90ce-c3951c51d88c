import pathlib
import re
import typing as t
from abc import abstractmethod

import langchain_community.callbacks
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompt_values import PromptValue
from langchain_core.runnables import Runnable
from openai import LengthFinishReasonError, PermissionDeniedError

from services.studio._text_to_workflow.models.model_manager import Model<PERSON>anager
from services.studio._text_to_workflow.utils.errors import DraftGenerationException
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.unidiff_utils import DiffingException, MergeConflict
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixFlairType
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import EditPatchStructureError

LOGGER = AppInsightsLogger()
LEADING_FENCE_PATTERN = re.compile(r"^```\s*")
TRAILING_FENCE_PATTERN = re.compile(r"```\s*$")

CUSTOM_PATCH_INSTRUCTIONS_PLACEHOLDER = "custom_patch_instructions"


class BaseDraftGeneratorService:
    """Base class for generator services that use LLM models."""

    def __init__(
        self,
        model_name: str,
        consuming_feature_type: ConsumingFeatureType,
        retry_count: int = 3,
    ):
        self.model_name = model_name
        self.consuming_feature_type = consuming_feature_type
        self.retry_count = retry_count

        patch_config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "patch_format_prompt.yaml"
        self.patch_prompt_config = yaml_load(patch_config_path)

    def _initialize_model(self, query: str, min_model_tokens: int, max_model_tokens: int, token_per_query_character: int) -> BaseChatModel:
        """Initialize the LLM model with appropriate token settings based on query length."""
        model = ModelManager().get_llm_model(self.model_name, self.consuming_feature_type)

        # For long queries, we will increase the max tokens
        if not model.has_max_model_tokens():  # type: ignore
            lower_token_limit = max(len(query) * token_per_query_character, min_model_tokens)  # tokens per query character, minimum 1024 tokens
            model.max_model_tokens = min(lower_token_limit, max_model_tokens)  # type: ignore # maximum 2048 tokens
        return model

    def _get_formatted_demonstrations(
        self,
        model: BaseChatModel,
        demonstrations: list[t.Any],
        prompt_template: str,
        max_allowed_tokens: int = 128000,
        **serialization_kwargs,
    ) -> list[str]:
        demonstration_messages: list[str] = []
        tokens_count = 0

        for demonstration in demonstrations:
            try:
                demo_message = self.serialize_demonstration(demonstration, prompt_template, **serialization_kwargs)

                # ensure max token limit is not exceeded
                demo_message_tokens = model.get_num_tokens(demo_message)
                if demo_message_tokens + tokens_count > max_allowed_tokens:
                    return demonstration_messages

                demonstration_messages.append(demo_message)
                tokens_count += demo_message_tokens
            except Exception as e:
                LOGGER.exception(f"Failed to build generation demonstration for {demonstration.query} {e}")
                continue

        return demonstration_messages

    @abstractmethod
    def serialize_demonstration(self, demonstration: t.Any, prompt_template: str, **serialization_kwargs) -> str:
        """
        Serialize a demonstration into a string format suitable for the model.

        Args:
            demonstration (t.Any): The demonstration data to serialize.
            prompt_template (str): The template to use for formatting the demonstration.
            **serialization_kwargs: Additional keyword arguments for serialization customization.

        Returns:
            str: The serialized demonstration.
        """
        raise NotImplementedError("Subclasses must implement this method")

    def _apply_diff_to_workflow(self, raw_diff_prediction: str, serialized_workflow: str) -> tuple[str, list[EditPatchStructureError]]:
        """
        Apply a diff to a workflow serialization.
        """
        try:
            raw_diff_prediction = LEADING_FENCE_PATTERN.sub("", raw_diff_prediction)  # remove leading ``` if any
            raw_diff_prediction = TRAILING_FENCE_PATTERN.sub("", raw_diff_prediction)  # remove trailing ``` if any
            return MergeConflict(raw_diff_prediction, flair=WorkflowFixFlairType.MergeHybrid).patch(serialized_workflow)
        except DiffingException as e:
            LOGGER.warning(f"Failed to patch workflow: {e}")
            raise e

    def _get_flair_specific_prompt_values(self, mode: str, prompt_config: dict[str, t.Any]) -> dict:
        flair_specific_prompt_values = {}
        for section_name, section_choices in prompt_config.items():
            flair_specific_prompt_values[section_name] = section_choices["default"]
            if mode in section_choices:
                section_value = section_choices[mode]
                # the flair specfific sections can contain custom patch instructions, we need to apply them, if needed
                if CUSTOM_PATCH_INSTRUCTIONS_PLACEHOLDER in section_value:
                    section_value = section_value.format(**self.patch_prompt_config)
                flair_specific_prompt_values[section_name] = section_value
        return flair_specific_prompt_values

    async def _get_model_response(
        self, query: str, model: BaseChatModel | Runnable, messages: PromptValue, max_token_for_overflow_retry: int
    ) -> tuple[t.Any, TokenUsage]:
        result = None
        usage = None
        """Get a response from the LLM model with retry logic."""
        for i in range(self.retry_count):
            try:
                with langchain_community.callbacks.get_openai_callback() as cb:
                    result = await model.ainvoke(messages)
                    usage = TokenUsage(
                        model=model.model_name,  # type: ignore
                        prompt_tokens=cb.prompt_tokens,
                        completion_tokens=cb.completion_tokens,
                        total_tokens=cb.total_tokens,
                    )
                break  # If successful, exit the loop
            except Exception as e:
                if hasattr(e, "status_code") and e.status_code == 401 or isinstance(e, PermissionDeniedError):  # type: ignore
                    raise e

                if isinstance(e, LengthFinishReasonError):
                    # for LengthFinishReasonError lets increase the max completion tokens
                    LOGGER.warning(f"LengthFinishReasonError - Completion Tokens exceeded for content {e}.")
                    model.max_model_tokens = max_token_for_overflow_retry  # type: ignore

                LOGGER.exception(f"⚠️ Model Generation failed with: {e}.")
                if i == self.retry_count - 1:
                    LOGGER.exception(f"❌ Fatal exception for '{query}': {e}.")
                    raise e

                continue

        if result is None or usage is None:
            raise DraftGenerationException("Failed to get model response after retries")

        # can throw an exception when using structured output unless we configure the generation to return the result as a string
        # also, we need to patch this for Claude reasoning
        if not isinstance(result.content, str):
            raise DraftGenerationException(f"Expected string content from LLM response, got {type(result.content)}")

        return result.content, usage
