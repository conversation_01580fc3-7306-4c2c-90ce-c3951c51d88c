# WARNING: Keep the list of activities ordered from most to least relevant
# track the relevant activities for a given namespace
from services.studio._text_to_workflow.common.schema import TargetFramework

NAMESPACES_CORRESPONDENCE_MAP: dict[str, set[str]] = {
    "UiPath.Database.Activities": {"UiPath.Database.Activities.ExecuteQuery", "UiPath.Core.Activities.ForEachRow"},
    "UiPath.Excel.Activities.Business": {"UiPath.Excel.Activities.Business.ExcelProcessScopeX"},
    "UiPath.Word.Activities": {"UiPath.Word.Activities.WordApplicationScope", "UiPath.Word.Activities.WordAppendText"},
    "UiPath.Presentations.Activities": {"UiPath.Presentations.Activities.PowerPointApplicationScope"},
}

# track a collection of activities which are related to each other, either because they complement or substitute each other really well
# If either one of these activities is present, the other one should be considered as well
# these can be seen as connex components in an activity graph.
ACTIVITIES_CORRESPONDENCE_TABLE_WINDOWS = [
    [
        "UiPath.Core.Activities.BuildDataTable",
        "UiPath.Core.Activities.GenerateDataTable",
        "UiPath.Core.Activities.AddDataColumn",
        "UiPath.Core.Activities.AddDataRow",
    ],
]

ACTIVITIES_CORRESPONDENCE_TABLE: list[list[str]] = [
    ["UiPath.MicrosoftOffice365.Activities.Mail.GetEmailListConnections", "UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections"],
    ["UiPath.Database.Activities.DatabaseConnect", "UiPath.Database.Activities.DatabaseDisconnect"],
    ["UiPath.DataService.Activities.DeleteEntityRecord", "UiPath.Database.Activities.ExecuteNonQuery"],
    [
        "UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_Channel",
        "UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User",
    ],
    ["UiPath.MicrosoftOffice365.Activities.Excel.WriteRangeConnections", "UiPath.MicrosoftOffice365.Activities.Excel.CreateWorkbookConnections"],
    [
        "UiPath.Core.Activities.GenerateDataTable",
        "UiPath.Core.Activities.AddDataColumn",
        "UiPath.Core.Activities.AddDataRow",
    ],
    [
        "UiPath.MicrosoftOffice365.Activities.Files.ForEachFileFolderConnections",
        "UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections",
        "UiPath.MicrosoftOffice365.Activities.Files.GetFileFolderConnections",
        "UiPath.MicrosoftOffice365.Activities.Files.DownloadFileConnections",
    ],
    [
        "UiPath.GSuite.Activities.ForEachFileFolderConnections",
        "UiPath.GSuite.Activities.GetFileListConnections",
        "UiPath.GSuite.Activities.GetFileFolderConnections",
        "UiPath.GSuite.Activities.DownloadFileConnections",
    ],
    ["UiPath.Mail.Exchange.Activities.GetExchangeMailMessages", "UiPath.Mail.Outlook.Activities.GetOutlookMailMessages"],
    ["UiPath.GSuite.Activities.DownloadEmailConnections", "UiPath.GSuite.Activities.DownloadAttachmentsConnections"],
    [
        "UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesContent_Generation",
    ],
]

# track a collection of activities which are related to each other
# as soon as an activity is added the ones in the corresponding collection should be considered as well
# these can be seen as edges in an oriented graph.
ACTIVITIES_DIRECTED_CORRESPONDENCE_TABLE: dict[str, list[str]] = {
    "UiPath.Core.Activities.CreateFile": ["UiPath.Core.Activities.CreateDirectory"],
    "UiPath.Web.Activities.HttpClient": ["UiPath.Web.Activities.DeserializeJson", "UiPath.Web.Activities.DeserializeJson"],
    "UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesContext_Grounding_Search": [
        "UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesContent_Generation"
    ],
}


def expand_with_related_activities(input_activities: set[str], target_framework: TargetFramework) -> set[str]:
    """Find activities that either complement or substitute the given ones"""
    new_activities: set[str] = set(input_activities)

    # TO DO: we might want to this recursively once we expand the mapping tables, for now, this is not needed
    new_activities.update(
        *[
            NAMESPACES_CORRESPONDENCE_MAP[namespace]
            for in_activity in input_activities
            for namespace in NAMESPACES_CORRESPONDENCE_MAP
            if in_activity.startswith(namespace)
        ]
    )

    new_activities.update(
        *[corresponding_activities for act, corresponding_activities in ACTIVITIES_DIRECTED_CORRESPONDENCE_TABLE.items() if act in input_activities]
    )

    new_activities.update(*[activities for in_activity in input_activities for activities in ACTIVITIES_CORRESPONDENCE_TABLE if in_activity in activities])
    if target_framework == "Windows":
        new_activities.update(
            *[activities for in_activity in input_activities for activities in ACTIVITIES_CORRESPONDENCE_TABLE_WINDOWS if in_activity in activities]
        )

    return new_activities
