from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.workflow_utils import indentation_re, renumber_plan_steps

LOGGER = AppInsightsLogger()


def get_plan_whole(
    mode: ActivitiesGenerationMode,
    plan: str,
    existing_plan: str,
    stitch_workflow_on_sequence_generation: bool,
):
    if mode == "sequence":
        return _postprocess_sequence_generation_plan(plan, existing_plan, stitch_workflow_on_sequence_generation)
    return plan


def _postprocess_sequence_generation_plan(result: str, existing_plan: str, stitch_plan: bool) -> str:
    # TODO: should we renumber the sequence plan for consistency? we could also leave it as is
    # currently, model can generate plan starting either from 1. or from sqgen insertion locus (e.g. 3.2.4)
    if not existing_plan or existing_plan == f"1. {constants.SEQUENCE_GENERATION_INSERTION_PHRASE}" or not stitch_plan:
        # return renumber_plan_steps(results)
        return result

    # strip result of global indentation
    result_indentations = [match.group() if (match := indentation_re.search(line)) is not None else "" for line in result.splitlines()]
    result_global_indentation = min(result_indentations, key=len)
    result_steps = [line[len(result_global_indentation) :] for line in result.splitlines()]

    # find the insertion point
    plan_steps = existing_plan.splitlines()
    insertion_indices = [i for i, step in enumerate(plan_steps) if step.endswith(constants.SEQUENCE_GENERATION_INSERTION_PHRASE)]
    if len(insertion_indices) != 1:
        LOGGER.warning("Multiple or no {constants.SEQUENCE_GENERATION_INSERTION_PHRASE} found in the existing plan. Skipping postprocessing.")
        return result
    insertion_index = insertion_indices[0]

    # insert the new steps
    insertion_index_indentation = indentation_re.search(plan_steps[insertion_index]).group()
    inner_plan_steps = [f"{insertion_index_indentation}{step}" for step in result_steps]
    include_bounding_markers = False
    if include_bounding_markers:
        inner_plan_steps = ["<start sequence generation>"] + inner_plan_steps + ["<end sequence generation>"]

    plan_steps[insertion_index : insertion_index + 1] = inner_plan_steps

    # renumber the whole plan
    return renumber_plan_steps("\n".join(plan_steps))
