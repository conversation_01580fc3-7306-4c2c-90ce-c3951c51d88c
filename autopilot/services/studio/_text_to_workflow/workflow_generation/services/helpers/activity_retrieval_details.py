from itertools import zip_longest

import numpy as np

from services.studio._text_to_workflow.utils.activity_utils import remove_duplicate_proposed_activities
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ProposedActivity


class ActivityRetrievalDetails:
    """An aggregation from multiple sources of the activities/triggers that could be used to build a workflow.
    These can be created either from query embeddings or from the existing workflow embeddings."""

    def __init__(
        self,
        relevant_triggers: list[ProposedActivity] = [],  # the triggers proposed resulting from the query/workflow
        activity_steps: list[list[ProposedActivity]] = [],  # the activities proposed resulting from each step of the query/ activity in a workflow
        basic_activities: list[ProposedActivity] = [],
        basic_triggers: list[ProposedActivity] = [],
        ground_truth_activities: list[ProposedActivity] = [],
        ground_truth_triggers: list[ProposedActivity] = [],
    ):
        self.basic_activities = basic_activities
        self.basic_triggers = basic_triggers
        self.relevant_triggers = relevant_triggers
        self.activity_steps = activity_steps
        self.ground_truth_activities = ground_truth_activities
        self.ground_truth_triggers = ground_truth_triggers

    def organize_activities_by_priority(self) -> list[list[ProposedActivity]]:
        """Split the activities into buckets based on their order.
        The assumption is that the activities that are closer to the beginning of the list are more likely to be used"""
        ground_truth_activities = self.basic_activities + self.ground_truth_activities
        return self._build_activity_buckets([step for step in self.activity_steps], ground_truth_activities)

    def organize_triggers_by_priority(self) -> list[list[ProposedActivity]]:
        """Split the triggers into buckets based on their order.
        The assumption is that the triggers that are closer to the beginning of the list are more likely to be used"""
        ground_truth_activities = self.basic_triggers + self.ground_truth_triggers
        return self._build_activity_buckets([self.relevant_triggers], ground_truth_activities)

    """
    HOW THE SORTING BY PRIORITY WORKS:

    This is used to organize the proposed activities/triggers derived EITHER from the query embeddings OR the workflow embeddings.
    For each query step or existing workflow activities we will have a set of proposed activities/triggers, such as:

    |  Embedding   | Proposed Activities|
    |    ---       |  --- |  --- |  --- |
    | query step 1 | act1 | act2 | act3 | <-- each row ordered from the most to least relevant
    | query step 2 | act4 | act5 | act6 |
    | query step 3 | act7 | act8 | act2 |

    So, we want to transpose this matrix and group the activities/triggers by their order of appearance in the retrieval AND remove duplicates.
    Activities will be grouped in buckets, where the first bucket will contain the most relevant activities, the second bucket will contain the second most relevant activities, and so on.
    From the above input, we will end up with the following:

    [
        [ act1, act4, act7], # 1st most relevant
        [ act2, act5, act8], # 2nd most relevant
        [ act3, act6], # 3rd most relevant - duplicates are removed
        .......
    ]

    This allows us to easily aggregate activities from multiple wf/query steps and order them by relevance.
    """

    @staticmethod
    def _build_activity_buckets(
        details: list[list[ProposedActivity]],
        ground_truth_activities: list[ProposedActivity],
    ) -> list[list[ProposedActivity]]:
        """We want to sort the activities in buckets, based on the ranking order they appeared in the retrieval
        This helps finetune how to much of the dataset should be included in the demonstration
        """
        # transpose by the ordering - see comment above for more details
        activities_buckets = list(zip_longest(*np.array(details, dtype="object"), fillvalue=None))

        if len(activities_buckets) == 0:
            return [ground_truth_activities]

        activity_names: set[str] = set([act["type_full_name"] for act in ground_truth_activities])
        result: list[list[ProposedActivity]] = list()

        for bucket in activities_buckets:
            bucket_results = []

            for act in bucket:
                if act is None or act["type_full_name"] in activity_names:
                    continue
                activity_names.add(act["type_full_name"])
                bucket_results.append(act)

            result.append(bucket_results)

        # Add the ground truth activities to the first bucket
        result[0] = remove_duplicate_proposed_activities(ground_truth_activities + result[0])
        return result
