from services.studio._text_to_workflow.common import constants as common_constants
from services.studio._text_to_workflow.common.activity_retriever import LOGGER, ActivitiesRetriever
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.workflow_generation.config import constants
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import GenerationSettings


def _get_ignored_activities_for_target_framework(
    activities_retriever: ActivitiesRetriever,
    target_framework: TargetFramework,
) -> set[str]:
    uia_activities = activities_retriever.get_all_by_package_name(common_constants.UIA_PACKAGE_NAME, target_framework)
    uia_activities_fcn = [a["fullClassName"] for a in uia_activities]

    return {a for a in uia_activities_fcn if a not in constants.UI_PACKAGE_ACTIVITIES_ALLOWLIST} | common_constants.PRODUCTIVITY_IGNORED_ACTIVITIES


def get_ignored_activities_map(
    activities_retriever: ActivitiesRetriever,
) -> dict[TargetFramework, set[str]]:
    return {
        "Portable": _get_ignored_activities_for_target_framework(activities_retriever, "Portable"),
        "Windows": _get_ignored_activities_for_target_framework(activities_retriever, "Windows"),
    }


def get_ignored_namespaces(
    workflow: Workflow | None,
    activities_retriever: ActivitiesRetriever,
    target_framework: TargetFramework,
    generation_settings: GenerationSettings | None = None,
) -> set[str]:
    existing_activities = ActivitiesAndTriggersCollector().collect(workflow) if workflow is not None else {}
    contains_namespaces = {k: False for k in constants.EXCLUSIVE_NAMESPACES}

    for activity_type, activities in existing_activities.items():
        for activity in activities:
            activity_info = activities_retriever.get(activity.activity_id, target_framework, activity_type)
            if activity_info is None:
                LOGGER.warning(f"Could not find activity info for: {activity.activity_id}")
                continue
            for key, namespaces in constants.EXCLUSIVE_NAMESPACES.items():
                if activity_info["namespace"] in namespaces:
                    contains_namespaces[key] = True
    ignored_namespaces = set()
    if contains_namespaces["ExcelOnline"]:
        # if online is used, ignore all desktop namespaces
        ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["ExcelDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"]
    else:
        # Otherwise ignore online and either business or legacy
        if contains_namespaces["ExcelDesktopBusiness"] and not contains_namespaces["ExcelDesktopLegacy"]:
            # ignore legacy and excel online if the workflow contains business
            ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"] | constants.EXCLUSIVE_NAMESPACES["ExcelOnline"]
        elif contains_namespaces["ExcelDesktopLegacy"] and not contains_namespaces["ExcelDesktopBusiness"]:
            # ignore business if the workflow contains legacy
            ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["ExcelDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["ExcelOnline"]
        else:
            # if neither or both are included, we will try to use the package from generation settings
            ignored_namespaces |= _get_default_ignored_excel_namespace(generation_settings, target_framework)

    if contains_namespaces["MailOnline"]:
        # if online is used, ignore all desktop namespaces
        ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["MailDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["MailDesktopLegacy"]
    else:
        # Otherwise ignore online and either business or legacy
        if contains_namespaces["MailDesktopBusiness"] and not contains_namespaces["MailDesktopLegacy"]:
            # ignore legacy and mail online if the workflow contains business
            ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["MailDesktopLegacy"] | constants.EXCLUSIVE_NAMESPACES["MailOnline"]
        elif contains_namespaces["MailDesktopLegacy"] and not contains_namespaces["MailDesktopBusiness"]:
            # ignore business if the workflow contains legacy
            ignored_namespaces |= constants.EXCLUSIVE_NAMESPACES["MailDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["MailOnline"]
        else:
            ignored_namespaces |= _get_default_ignored_mail_namespace(generation_settings, target_framework)

    return ignored_namespaces


def _get_default_ignored_excel_namespace(generation_settings: GenerationSettings | None, target_framework: TargetFramework) -> set[str]:
    default_excel_package = generation_settings.default_excel_package if generation_settings is not None else None
    if default_excel_package is None and target_framework == "Portable":
        default_excel_package = "ExcelOnline"

    match default_excel_package:
        case "ExcelOnline":
            return constants.EXCLUSIVE_NAMESPACES["ExcelDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"]
        case "ExcelDesktopBusiness":
            return constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"] | constants.EXCLUSIVE_NAMESPACES["ExcelOnline"]
        case _:
            return constants.EXCLUSIVE_NAMESPACES["ExcelDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["ExcelOnline"]


def _get_default_ignored_mail_namespace(generation_settings: GenerationSettings | None, target_framework: TargetFramework) -> set[str]:
    default_mail_package = generation_settings.default_mail_package if generation_settings is not None else None
    if default_mail_package is None and target_framework == "Portable":
        default_mail_package = "MailOnline"

    match default_mail_package:
        case "MailOnline":
            return constants.EXCLUSIVE_NAMESPACES["MailDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["MailDesktopLegacy"]
        case "MailDesktopBusiness":
            return constants.EXCLUSIVE_NAMESPACES["MailDesktopLegacy"] | constants.EXCLUSIVE_NAMESPACES["MailOnline"]
        case _:
            return constants.EXCLUSIVE_NAMESPACES["MailDesktopBusiness"] | constants.EXCLUSIVE_NAMESPACES["MailOnline"]
