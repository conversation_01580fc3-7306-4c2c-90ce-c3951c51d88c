import numpy as np

from services.studio._text_to_workflow.common.schema import Connection


class ConnectionIndexState:
    connections: list[Connection]
    embeddings: np.ndarray
    connector_key_to_embedding: dict[str, np.ndarray]

    def __init__(self, connections: list[Connection], embeddings: np.ndarray, connector_key_to_embedding: dict[str, np.ndarray]) -> None:
        self.connections = connections
        self.embeddings = embeddings
        self.connector_key_to_embedding = connector_key_to_embedding
