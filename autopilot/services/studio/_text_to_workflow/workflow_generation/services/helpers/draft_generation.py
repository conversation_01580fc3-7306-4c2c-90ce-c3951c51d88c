from dataclasses import dataclass

import typing_extensions as t
from langchain_core.prompt_values import PromptValue

from services.studio._text_to_workflow.common.schema import ActivityDefinition, WorkflowDict
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ParsedWorkflowDetails, WfGenDataPointV2

DraftDemonstrationType: t.TypeAlias = t.Literal["Workflow", "Activity", "Patch"]


@dataclass
class DraftCreationDemonstration:
    query: str
    workflow: dict | WorkflowDict
    type: DraftDemonstrationType
    data_point: WfGenDataPointV2 | None = None
    existing_workflow: dict | WorkflowDict | None = None
    stitched_workflow: dict | WorkflowDict | None = None
    # TODO: this looks more like a union of 3 different types of demonstrations, should we split this?


@dataclass
class DraftGenerationResult:
    token_usage: TokenUsage
    result_workflow_details: ParsedWorkflowDetails
    demonstrations: list[DraftCreationDemonstration]
    trigger_defs: list[ActivityDefinition]
    activity_defs: list[ActivityDefinition]
    jit_types: dict[str, dict]
    activities_config_map: dict[str, dict]
    prompt: PromptValue
    raw_model_prediction: str  # TODO: ensure guarding at the service level to avoid exposure
