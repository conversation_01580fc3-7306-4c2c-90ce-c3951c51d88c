import numpy as np
import tqdm

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetrieverBase
from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class ConnectionEmbeddingsRetriever(StateBuilder):
    config: dict

    def __init__(self, activities_retriever: ActivitiesRetrieverBase, embedding_model: EmbeddingModel) -> None:
        self.activities_retriever = activities_retriever
        self.embedding_model = embedding_model
        self.store = StateStore((paths.get_connection_embeddings_retriever_path() / "state.pkl").as_posix(), self, lazy_load=settings.DEBUG_MODE)

    def build(self) -> tuple[dict, dict]:
        connector_keys = self.activities_retriever.get_all_connectors()
        connector_key_to_words_embedding = {}

        # First collect all words from all connector keys
        all_words = []
        connector_key_to_word_indices = {}
        current_index = 0

        for connector_key in connector_keys:
            words = [word for word in connector_key.split("-") if word != "uipath"]
            connector_key_to_word_indices[connector_key] = (current_index, current_index + len(words))
            current_index += len(words)
            all_words.extend(words)

        # Get embeddings for all words in one batch
        with tqdm.tqdm(total=len(connector_keys), desc="Building connections index", disable=not settings.DEBUG_MODE) as pbar:
            all_embeddings = self.embedding_model.encode_batch(all_words, batch_size=64, instruction_set="icl", instruction_type="query")
            pbar.update(len(connector_keys))

        # Recompose embeddings for each connector key
        for connector_key, (start_idx, end_idx) in connector_key_to_word_indices.items():
            connector_embeddings = all_embeddings[start_idx:end_idx]
            connector_key_to_words_embedding[connector_key] = np.mean(connector_embeddings, axis=0)

        state = {"connectorKey2Embedding": connector_key_to_words_embedding}
        state_info = {"connector_keys": connector_keys}
        print(f"Built connections index for {len(connector_keys)} connectors.")
        return state, state_info

    def get_connections_embedding(self, connections: list[Connection], query_embedding: np.ndarray):
        connections_by_key: dict[str, Connection] = {}
        for connection in connections:
            if connection["connector"] not in connections_by_key or connection.get("isDefault", True):
                connections_by_key[connection["connector"]] = connection

        connections_embeddings = []
        for connector_key in connections_by_key.keys():
            if connector_key not in self.store.state["connectorKey2Embedding"]:
                LOGGER.warning(f"Connector key {connector_key} not found in the connections index")
                continue

            connections_embeddings.append(self.store.state["connectorKey2Embedding"][connector_key])

        # If no connections are found or connections are not found in the index, return the query embedding
        if len(connections_embeddings) == 0:
            return query_embedding, connections_by_key

        connections_embedding = np.mean(connections_embeddings, axis=0)
        return connections_embedding, connections_by_key
