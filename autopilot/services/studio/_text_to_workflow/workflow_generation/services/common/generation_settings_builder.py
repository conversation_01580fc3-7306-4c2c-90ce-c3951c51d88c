from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.workflow_generation.config import constants
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import GenerationSettings

DEFAULT_RETRIEVAL_MODEL_NAME = "activity_retrieval_gemini_model"


class GenerationSettingsBuilder:
    @staticmethod
    def build_generation_settings(
        query: str,
        target_framework: TargetFramework,
        model_options: dict[str, ModelOptions] | None,
        used_activity_names: list[str],
        supported_excel_packages: list[constants.ExcelPackageOption],
        supported_mail_packages: list[constants.MailPackageOption],
    ) -> GenerationSettings:
        excel_package: constants.ExcelPackageOption | None = None
        mail_package: constants.MailPackageOption | None = None

        # if our result workflow contains any activity from ExcelOnline or the legacy ExcelDesktopBusiness pack, we should restrict the generation to use only that respective package
        for package in supported_excel_packages:
            if any(activity.startswith(ns) for activity in used_activity_names for ns in constants.EXCLUSIVE_NAMESPACES[package]):
                excel_package = package
                break

        # if our result workflow contains any activity from MailOnline or MailDesktop pack, we should restrict the generation to use only that respective package
        for package in supported_mail_packages:
            if any(activity.startswith(ns) for activity in used_activity_names for ns in constants.EXCLUSIVE_NAMESPACES[package]):
                mail_package = package
                break

        if target_framework == "Windows":
            query_lower = query.lower()
            if mail_package is None:
                for keyword, package in constants.FORCED_MAIL_KEYWORDS.items():
                    if keyword in query_lower:
                        mail_package = package
                        break

            if excel_package is None:
                excel_set = False
                for keyword, package in constants.FORCED_EXCEL_KEYWORDS.items():
                    if keyword in query_lower:
                        excel_package = package
                        excel_set = True
                        break

                if not excel_set:
                    for keyword_group, package in constants.FORCED_EXCEL_KEYWORD_GROUPS.items():
                        if all(keyword in query_lower for keyword in keyword_group):
                            excel_package = package
                            break

        if model_options is None:
            model_options = {}

        if "generation" not in model_options or model_options["generation"] is None or model_options["generation"]["model_name"] is None:
            model_options["generation"] = {"model_name": "workflow_generation_gemini_model"}

        if "retrieval" not in model_options or model_options["retrieval"] is None or model_options["retrieval"]["model_name"] is None:
            model_options["retrieval"] = {"model_name": DEFAULT_RETRIEVAL_MODEL_NAME}

        return GenerationSettings(model_options, excel_package, mail_package)
