name: Send receipt
description: Whenever I receive a JPG with a receipt on Gmail, extract the document data and notify my team on the receipts channel in Slack
original_name: Send receipt
plan: |-
  1. Email Received
  2. Download Email Attachments
  3. For Each
    3.1. Extract Document Data
    3.2. Send Message to Channel
refined plan: |-
  1. <PERSON><PERSON> received with JPG attachment
  2. Download JPG Attachments
  3. For each JPG Attachment
    3.1. Extract Document Data as Receipt
    3.2. Notify team on receipts channel in Slack
retrieved_triggers:
- UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
retrieved_activities:
- UiPath.GSuite.Activities.DownloadAttachmentsConnections
- UiPath.Core.Activities.ForEach`1
- UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData`1
- 37a305b2-89b1-315d-b73f-1778839a6c47
process:
  processName: "ExtractReceiptDataAndSendToSlack"
  packages:
  - UiPath.DocumentUnderstanding.Activities, v2.4.0
  - UiPath.GSuite.Activities, v2.4.0-preview
  - UiPath.IntegrationService.Activities, v0.0.25
  - UiPath.System.Activities, v23.10.0-preview
  - UiPath.WebAPI.Activities, v1.18.0
  trigger:
    thought: "Email Received"
    activity: UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
    params:
      AuthScopesInvalid: "False"
      BrowserFolderId: "INBOX"
      BrowserFolderName: "Inbox"
      FilterExpression: "(ParentFolders[?ID=='INBOX'])&&(HasAttachments==`true`)"
      IncludeAttachments: "True"
      MarkAsRead: "True"
      Result: "[[email]]"
      WithAttachmentsOnly: "True"
      Operation: "EMAIL_RECEIVED"
      ObjectName: "Message"
  workflow:
  - thought: "Download Email Attachments"
    activity: UiPath.GSuite.Activities.DownloadAttachmentsConnections
    params:
      AuthScopesInvalid: "False"
      ExcludeInlineAttachments: "True"
      FilterByFileNames: "*.jpg"
      Result: "[[attachments]]"
      SearchMode: "UseSimple"
      Email: "[[email]]"
  - thought: "For Each"
    activity: UiPath.Core.Activities.ForEach<UiPath.Platform.ResourceHandling.ILocalResource>
    params:
      Values: "[[attachments]]"
      Body:
        variables:
        - name: "currentItem"
          type: "ILocalResource"
        Handler:
        - thought: "Extract Document Data"
          activity: UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData`1
          params:
            DocType: "Receipts"
            ProjectId: "00000000-0000-0000-0000-000000000000"
            FileInput: "[[currentItem]]"
        - thought: "Send Message to Channel"
          activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
          params:
            UiPathActivityTypeId: "37a305b2-89b1-315d-b73f-1778839a6c47"
            channel: "#receipts"
            text: "[[string.Format(\"Spent: {0}\", _out_ExtractDocumentDataWithDocumentData_Generic_1_2__ExtractionResults.Data.TotalAmount)]]"
