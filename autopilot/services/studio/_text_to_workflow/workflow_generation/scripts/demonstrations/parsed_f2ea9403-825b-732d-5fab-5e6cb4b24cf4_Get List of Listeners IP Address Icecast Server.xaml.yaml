name: Log IP listeners
description: Extract and log the IP addresses of current listeners from an Icecast server's XML response, skipping entries without listener information.
plan: |-
  1. Manual Trigger
  2. Log Message
  3. Assign
  4. HTTP Request
  5. Deserialize XML
  6. Log Message
  7. If Any Listeners?
    7.1. For Each
      7.1.1. If
        *******. Continue
      7.1.2. Log Message
      7.1.3. Add To Collection
refined plan: |-
  1. Manual Trigger
  2. Log initializing message
  3. Assign an empty list to store IP addresses
  4. HTTP Request to get XML response from Icecast server
  5. Deserialize XML
  6. Log Message with total listeners count
  7. If there are any listeners
    7.1. For Each element in XML "icestats"->"source"->"listeners"
      7.1.1. If element contains "listeners" case insensitive
        *******. Continue
      7.1.2. Log Message with IP address of element
      7.1.3. Add To Collection of IP addresses
process:
  processName: "Get_List_of_Listeners_IP_Address_Icecast_Server"
  packages:
  - UiPath.Excel.Activities, v[2.12.3]
  - UiPath.System.Activities, v[22.4.1]
  - UiPath.UIAutomation.Activities, v[22.4.5]
  - UiPath.WebAPI.Activities, v[1.11.1]
  workflow:
  - thought: "Log Message"
    displayName: "Log Message"
    activity: UiPath.Core.Activities.LogMessage
    id: LogMessage_1
    params:
      Level: "Info"
      Message: "[[\"Get list of listeners IP addresses...\"]]"
  - thought: "Assign"
    displayName: "Assign"
    activity: UiPath.Core.Activities.Assign<System.Collections.Generic.List<System.String>>
    id: Assign_2
    params:
      To: "[[out_LstIPListResponse]]"
      Value: "[[new List<string>()]]"
  - thought: "HTTP Request"
    displayName: "HTTP Request"
    activity: UiPath.Web.Activities.HttpClient
    id: HttpClient_2
    params:
      AcceptFormat: "XML"
      AuthenticationType: "None"
      BodyFormat: "application/xml"
      EnableSSLVerification: "False"
      Method: "GET"
      Password: "abcdefg"
      TimeoutMS: "6000"
      Username: "admin"
      ClientCertificate: "[[null]]"
      ClientCertificatePassword: "[[null]]"
      EndPoint: "[[in_GetListenerURL]]"
      Result: "[[strXMLResponse]]"
      StatusCode: "[[intResponseStatus]]"
  - thought: "Deserialize XML"
    displayName: "Deserialize XML"
    activity: UiPath.Web.Activities.DeserializeXml
    id: DeserializeXml_1
    params:
      XMLDocument: "[[xmlDocument]]"
      XMLString: "[[strXMLResponse]]"
  - thought: "Log Message"
    displayName: "Log Message"
    activity: UiPath.Core.Activities.LogMessage
    id: LogMessage_5
    params:
      Level: "Info"
      Message: "[[\"Total Listeners Currently: \" + xmlDocument.Element(\"icestats\").Element(\"source\").Element(\"Listeners\").Value.ToString()]]"
  - thought: "If Any Listeners?"
    displayName: "If Any Listeners?"
    activity: System.Activities.Statements.If
    id: If_1
    params:
      Condition: "[[xmlDocument.Element(\"icestats\").Element(\"source\").Element(\"Listeners\").Value.ToString() != \"0\"]]"
      Then:
      - thought: "For Each"
        displayName: "For Each"
        activity: UiPath.Core.Activities.ForEach<System.Xml.Linq.XElement>
        id: ForEach`1_3
        params:
          Values: "[[xmlDocument.Element(\"icestats\").Element(\"source\").Elements()]]"
          Body:
            variables:
            - name: "item"
              type: "XElement"
            Handler:
            - thought: "If"
              displayName: "If"
              activity: System.Activities.Statements.If
              id: If_2
              params:
                Condition: "[[item.ToString().ToUpper().Contains(\"LISTENERS\")]]"
                Then:
                - thought: "Continue"
                  displayName: "Continue"
                  activity: UiPath.Core.Activities.Continue
                  id: Continue_1
            - thought: "Log Message"
              displayName: "Log Message"
              activity: UiPath.Core.Activities.LogMessage
              id: LogMessage_6
              params:
                Level: "Info"
                Message: "[[item.Element(\"IP\").Value.ToString()]]"
            - thought: "Add To Collection"
              displayName: "Add To Collection"
              activity: System.Activities.Statements.AddToCollection<System.String>
              id: AddToCollection`1_2
              params:
                Collection: "[[out_LstIPListResponse]]"
                Item: "[[item.Element(\"IP\").Value.ToString()]]"
            DisplayName: "Body"
  variables:
  - name: strUsername
    type: System.String
  - name: sstrPassword
    type: System.Security.SecureString
  - name: strXMLResponse
    type: System.String
  - name: intResponseStatus
    type: System.Int32
  - name: xmlDocument
    type: System.Xml.Linq.XDocument
  arguments:
  - direction: In
    name: in_GetListenerURL
    type: System.String
  - direction: Out
    name: out_LstIPListResponse
    type: System.Collections.Generic.List<System.String>
