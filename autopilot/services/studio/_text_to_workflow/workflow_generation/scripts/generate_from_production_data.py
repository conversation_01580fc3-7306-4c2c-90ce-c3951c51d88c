import argparse
import asyncio
import json
import os
import pathlib
import time
import traceback

import dotenv
import langchain.chat_models
import langchain.prompts
import langchain.schema
import tqdm
import typing_extensions as t

from services.studio._text_to_workflow.common import typedefs, typedefs_parser
from services.studio._text_to_workflow.common.schema import WorkflowDict
from services.studio._text_to_workflow.common.walkers import BuildActivityConfigurationContext, DAPClassNameTranslator, PlanBuilder, WorkflowPruner
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import testing
from services.studio._text_to_workflow.utils.inference.llm_gateway_model import LLMGatewayModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.workflow_utils import get_workflow_summary, has_inner_activity, iterate_activities
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

query_generation_system_prompt = """You are an RPA expert working with UiPath Studio.
You can read workflow yaml proficiently and can reverse engineer what is happening inside one.
You are also able to summarize the explanation into only one sentence, using knowledge that you've gathered over the years.
Although not obvious at first, some workflows are more complex than others. Instead of explaining verbatim, try to offer a concise summary.
You must avoid generic formulations and stick to specific information that you can infer from the workflow.
Your answer should be imperative, as you would be demanding for the specific workflow to be created.
Avoid formulations starting with "Create a UiPath workflow that ..." or "Implement a workflow that ...", as the user is already familiar with the task.
You can start directly with what "..." would have contained.
"""
# You are able to assess how concise and specific your answer is with a number between 0 and 10.

query_generation_user_prompt = """The workflow name is "{identifier}" and the process name is "{process}".

This is a tree indented overview of the workflow with activities in the format: "<activity> (<thought>) <| separated parameters snippet>".
```plaintext
{summary}
```

This is the detailed workflow
```yaml
{workflow}
```

Try to figure out the main idea of the workflow in one sentence as if you were asking someone to create it.
"""


plan_refinement_system_prompt = """You are an RPA expert working with UiPath Studio.
You can read workflow yaml proficiently and can reverse engineer what is happening inside one.
You are also able to annotate a plan of the workflow with a brief natural language sentence for each step.
Although not obvious at first, some workflows are more complex than others. Instead of explaining verbatim, try to offer concise short imperative statements.
Try to avoid generic formulations and stick to specific information when you can infer from the workflow. Keep your statements short and concise.
Do not alter the structure of the plan. Keep all the steps in the plan, but refine to statements inside them.
Only change the sentences that can be converted from generic to concise, but keep it simple, specific and imperative.
"""

plan_refinement_user_prompt = """The workflow name is "{identifier}" and the process name is "{process}".

The workflow was generated based on this query: {query}

This is a tree indented overview of the workflow with activities in the format: "<activity> (<thought>) <| separated parameters snippet>".
```plaintext
{summary}
```

This is the detailed workflow
```yaml
{workflow}
```

Try to change the sentence of each step in the following plan with more concise and specific ones based on the information provided thus far.
```yaml
{plan}
```

Output the refined plan. Keep the structure and the indexing of all the steps the same.
"""

plan_refinement_demonstrations_path = pathlib.Path(__file__).absolute().parent / "demonstrations"
plan_refinement_demonstration_examples = [
    plan_refinement_demonstrations_path / "[Demo] On Email with Receipt Send on Slack.yaml",
    plan_refinement_demonstrations_path / "parsed_f2ea9403-825b-732d-5fab-5e6cb4b24cf4_Get List of Listeners IP Address Icecast Server.xaml.yaml",
]


def load_data(
    root_path: pathlib.Path,
    verbose: int = 2,
    bounds: t.Tuple[int | None, int | None] = (None, None),
    limit: int | None = None,
    allowlist: set | None = None,
    disallowlist: set | None = None,
) -> t.Tuple[dict[str, WorkflowDict], dict[str, str], list[pathlib.Path]]:
    """Loads workflows recursively from a root directory.

    Args:
        root_path (pathlib.Path): The root directory to start loading workflows from.
        verbose (int, optional): Verbosity level. Defaults to 2.
            - If set to 0: nothing will be printed.
            - If set to 1: only the progress bar will be printed.
            - If set to 2: errors will be printed as well.
        bounds (t.Tuple[int, int], optional): Lower and upper bounds for the size of the workflow. Defaults to (None, None).
        limit (int, optional): Limit the number of workflows to load. Defaults to None.
        allowlist (set, optional): A set of paths to allow. Defaults to None.
        disallowlist (set, optional): A set of paths to disallow. Defaults to None.

    Returns:
        t.Tuple[dict[str, dict], dict[str, str], list[pathlib.Path]]: A tuple containing the dataset, raw dataset, and invalid workflows.
    """

    def printif(verbosity_level, *args, **kwargs):
        if verbose >= verbosity_level:
            print(*args, **kwargs)

    invalid = []
    dataset = {}
    dataset_raw = {}
    lowerbound, upperbound = bounds
    # for path in root_path.iterdir():
    #     if not path.name.endswith(".yaml"):
    #         continue
    listing = [*root_path.rglob("*.yaml")]
    if allowlist is not None:
        _original_len = len(listing)
        listing = [p for p in listing if p.as_posix() in allowlist]
        printif(1, f"Keeping only {len(listing)} from {_original_len} as per allowlist.")
    if disallowlist is not None:
        _original_len = len(listing)
        listing = [p for p in listing if p.as_posix() not in disallowlist]
        printif(1, f"Keeping only {len(listing)} from {_original_len} as per disallowlist.")

    looping_object = tqdm.tqdm(listing, disable=verbose <= 0, desc="Loading workflows")
    for path in looping_object:
        if limit is not None and len(dataset) >= limit:
            break
        with open(path, encoding="utf-8-sig") as fd:
            # with open(path, encoding="utf-8-sig") as fd:
            try:
                workflow_serialization = fd.read()
                workflow = yaml_load(workflow_serialization)
                if "process" in workflow:  # in case we have a full datapoint
                    workflow = workflow["process"]
                    workflow_serialization = yaml_dump(workflow)
            except Exception as e:
                printif(2, f"Failed ({e}) to load workflow at {path}")
                invalid.append(path)
                continue
            if not workflow:
                printif(2, f"Empty workflow at {path}")
                invalid.append(path)
                continue
            if lowerbound is not None and len(workflow_serialization) < lowerbound:
                printif(2, f"Workflow too small ({len(workflow_serialization)}) at {path}")
                invalid.append(path)
                continue
            if upperbound is not None and len(workflow_serialization) > upperbound:
                printif(2, f"Workflow too large ({len(workflow_serialization)}) at {path}")
                invalid.append(path)
                continue
            dataset[path.as_posix()] = workflow
            dataset_raw[path.as_posix()] = workflow_serialization
    return dataset, dataset_raw, invalid


def get_name(path: str):
    name = pathlib.Path(path).name
    if name.endswith(".yaml"):
        name = name[:-5]
    if name.endswith(".xaml"):
        name = name[:-5]
    try:
        _, _, name = name.split("_", 2)
    except ValueError:
        pass
    return name


async def prompt_llm_for_query(
    request_context: RequestContext,
    workflow: Workflow,
    dry_run: bool = False,
) -> str:
    system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(query_generation_system_prompt)
    user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(query_generation_user_prompt)

    chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages([system_message_template, user_message_template])
    model = LLMGatewayModel(
        feature_type=ConsumingFeatureType.WORKFLOW_GENERATION,
        deployment_name="gpt-4o-2024-08-06",
        temperature=0,
        max_tokens=200,
        max_retries=10,
        context=request_context,
    )

    workflow_dict = DAPClassNameTranslator().translate(workflow).to_dict()

    chain = chat_prompt | model
    inputs = {
        "workflow": yaml_dump(workflow_dict),
        "summary": get_workflow_summary(workflow_dict),
        "identifier": get_name(workflow_dict["processName"]),
        "process": workflow_dict["processName"],
    }
    if dry_run:
        return chat_prompt.format(**inputs)
    try:
        response = await chain.ainvoke(inputs)
    except ValueError as e:
        print(e)
        traceback.print_exc()
        time.sleep(10)
        response = await chain.ainvoke(inputs)
    return str(response.content)


async def prompt_llm_for_queries_and_iterate(
    dataset: dict[str, str],
    loaded_dataset: dict[str, WorkflowDict] | None = None,
    dry_run=False,
    verbose=True,
) -> t.AsyncGenerator[t.Tuple[str, str], None]:
    for identifier, workflow in tqdm.tqdm(dataset.items(), disable=not verbose, desc="Generating queries"):
        if loaded_dataset is not None:
            loaded_workflow = loaded_dataset[identifier]
        else:
            loaded_workflow = yaml_load(workflow)
        # print(identifier)
        request_context = testing.build_testing_request_context()
        content = await prompt_llm_for_query(request_context, Workflow("", "", loaded_workflow), dry_run)
        yield identifier, content


async def prompt_llm_for_queries(dataset: dict[str, str]) -> dict[str, str]:
    result = {}
    async for identifier, query in prompt_llm_for_queries_and_iterate(dataset):
        result[identifier] = query
    return result


async def prompt_llm_for_plan_refinement(
    workflow: str,
    query: str,
    plan: str,
    loaded_workflow: WorkflowDict | None = None,
    identifier: str = "N/A",
    dry_run: bool = False,
) -> str:
    if loaded_workflow is None:
        loaded_workflow = yaml_load(workflow)
    assert isinstance(loaded_workflow, dict)

    system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(plan_refinement_system_prompt)
    user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(plan_refinement_user_prompt)

    demonstrations = []
    for demonstration_path in plan_refinement_demonstration_examples:
        demonstration = yaml_load(demonstration_path)
        demo = {
            "identifier": demonstration["name"],
            "process": demonstration["process"]["processName"],
            "query": demonstration["description"],
            "workflow": yaml_dump(demonstration["process"]),
            "summary": get_workflow_summary(loaded_workflow),
            "plan": demonstration["plan"],
        }
        demonstrations.append(user_message_template.format(**demo))
        demonstrations.append(langchain.prompts.AIMessagePromptTemplate.from_template(demonstration["refined plan"]))

    chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages([system_message_template, *demonstrations, user_message_template])

    model = LLMGatewayModel(
        feature_type=ConsumingFeatureType.WORKFLOW_GENERATION, deployment_name="gpt-4o-2024-08-06", temperature=0, max_tokens=1000, max_retries=10
    )
    chain = chat_prompt | model

    inputs = {
        "identifier": identifier,
        "process": loaded_workflow.get("process", "<unavailable>"),
        "query": query,
        "workflow": workflow,
        "summary": get_workflow_summary(loaded_workflow),  # type: ignore
        "plan": plan,
    }
    if dry_run:
        return chat_prompt.format(**inputs)
    try:
        response = await chain.ainvoke(inputs)
    except ValueError as e:
        print(e)
        import time
        import traceback

        traceback.print_exc()
        time.sleep(10)
        return ""
    return str(response.content)


async def prompt_llm_for_plans_refinement_and_iterate(
    dataset: dict[str, str], queries: dict[str, str], plans: dict[str, str], loaded_dataset: dict[str, WorkflowDict] | None = None, dry_run=False, verbose=True
) -> t.AsyncGenerator[t.Tuple[str, str], None]:
    for identifier, workflow in tqdm.tqdm(dataset.items(), disable=not verbose, desc="Generating plans refinement"):
        query = queries[identifier]
        plan = plans[identifier]
        loaded_workflow = loaded_dataset[identifier] if loaded_dataset is not None else None
        new_plan = await prompt_llm_for_plan_refinement(workflow, query, plan, loaded_workflow, identifier, dry_run)
        yield identifier, new_plan


async def prompt_llm_for_plans_refinement(
    dataset: dict[str, str],
    queries: dict[str, str],
    plans: dict[str, str],
    loaded_dataset: dict[str, WorkflowDict],
) -> dict[str, str]:
    result = {}
    async for identifier, plan in prompt_llm_for_plans_refinement_and_iterate(dataset, queries, plans, loaded_dataset=loaded_dataset):
        result[identifier] = plan
    return result


def load_queries(root_path: pathlib.Path) -> dict[str, str]:
    if not os.path.exists(root_path):
        raise ValueError("You should generate the queries beforehand.")
    with open(root_path / "_queries.json", "r") as fd:
        return json.load(fd)


async def generate_and_dump_queries(root_path: pathlib.Path, dataset: dict[str, str], overwrite=False):
    def flush(queries):
        with open(root_path / "_queries.txt", "w") as fd:
            for identifier, query in queries.items():
                fd.write(f"{identifier}\n{query}\n\n")
        with open(root_path / "_queries.json", "w") as fd:
            json.dump(queries, fd, indent=2)

    queries = {}
    if not overwrite and (queries_path := root_path / "_queries.json").exists():
        inp = None
        while inp not in {"y", "n"}:
            inp = input(f"There are queries generated at {queries_path}.\nDo you wish to load them? y/n")
        if inp == "y":
            queries = json.load(open(queries_path))
            dataset = {k: v for k, v in dataset.items() if k not in queries}
            print(f"Loaded {len(queries)} from disk. {len(dataset)} remaining.")
        elif inp == "n":
            print("Starting fresh with 0 queries.")
    async for identifier, query in prompt_llm_for_queries_and_iterate(dataset):
        queries[identifier] = query
        if len(queries) and len(queries) % 100 == 0:
            flush(queries)
    flush(queries)


def build_activity_configs(identifier, workflow, queries, plans, activities_index):
    global_variables = workflow.get("variables")
    arguments = workflow.get("arguments", [])

    activity_configs = []
    for node, _ancestry, local_variables in iterate_activities(workflow.get("workflow", [])):
        activity = node.get("activity")
        params = node.get("params", {})
        params = {k: v for k, v in params.items() if not has_inner_activity(v)}

        activity_data = activities_index.get_by_name(activity)
        if activity_data is None:
            print(activity)
            continue

        activity_configuration_datapoint = {
            "workflow_name": pathlib.Path(identifier).name,
            "workflow_description": queries[identifier],
            "activity_id": activity,
            "activity_fqn": activity,
            "activity_display_name": activity_data["displayName"],
            "plan": plans[identifier],
            "arguments": arguments,
            "global_variables": global_variables,
            "local_variables": local_variables,
            "params": params,
            "activity_typedef": activity_data["typeDefinition"],
            "additional_typedefs": activity_data["additionalTypeDefinitions"],
        }
        # yield activity_configuration_datapoint
        activity_configs.append(activity_configuration_datapoint)
    return activity_configs


def dump_activities_dataset(
    root_path: pathlib.Path,
    dataset: dict[str, WorkflowDict],
    *,
    output_name="v2",
    verbose=True,
    prune=False,
    exclude_function=None,
    custom_plan_filename="_plans.json",
):
    typedefs.load()
    assert len(typedefs._activity_typedefs), "No loaded typedefs"

    queries = json.load(open(root_path / "_queries.json"))
    without_query = [(k, "Prerequisites: no query") for k in dataset if k not in queries]
    # assert all(k in queries for k in dataset), f"Missing queries {len(missing_queries := set(dataset) - set(queries))} {list(missing_queries)[:5]}"
    assert len(without_query) < max(20, 0.4 * len(dataset)), f"Missing queries {len(without_query)} {without_query[:5]}"
    if without_query:
        print(f"{len(without_query)} workflows do not have a query out of the {len(dataset)}.")
        dataset = {k: v for k, v in dataset.items() if k in queries}
        print(f"Going forward with {len(dataset)} datapoints.")

    plans = json.load(open(root_path / custom_plan_filename))
    # assert all(k in plans for k in dataset), f"Missing plans {set(dataset) - set(plans)}"
    without_plan = [(k, "Prerequisites: no plan") for k in dataset if k not in plans]
    assert len(without_plan) < max(20, 0.4 * len(dataset)), f"Missing plans {len(without_plan)} {without_plan[:5]}"
    if without_plan:
        print(f"{len(without_plan)} workflows do not have a plan out of the {len(dataset)}.")
        dataset = {k: v for k, v in dataset.items() if k in plans}
        print(f"Going forward with {len(dataset)} datapoints.")

    typedefs_path = root_path / "_typedefs.json"
    if typedefs_path.exists():
        all_workflows_typedefs = json.load(open(typedefs_path))
    else:
        all_workflows_typedefs = {}
        print("WARNING! _typedefs.json file not found")

    skipped_workflows = [] + without_plan
    output_activities_dirpath = root_path.with_name(f"{root_path.name}_activity_configuration_{output_name}")
    output_activities_dirpath.mkdir(exist_ok=True)
    for workflow_path, workflow in tqdm.tqdm(dataset.items(), disable=not verbose, desc="Dumping activity configurations"):
        # if len(plans[workflow_path].splitlines()) > 10:
        #     print(f"Skipping workflow. Too large. {workflow_path}")
        #     skipped_workflows.append(workflow_path)
        #     continue
        workflow_name = pathlib.Path(workflow_path).name
        workflow_dir = output_activities_dirpath / f"[Production] {workflow_name}"

        try:
            wf = Workflow(
                queries[workflow_path], plans[workflow_path], workflow
            )  # note that the plan here might already be pruned (could create future inconsistencies)
        except Exception as e:
            print("Exception loading workflow! To look into", workflow_path, e)
            skipped_workflows.append((workflow_path, f"Exception: {e}"))
            continue
        if prune:
            WorkflowPruner().prune(wf)

        if exclude_function is not None and (exclusion_reason := exclude_function(wf, plans[workflow_path])):
            skipped_workflows.append((workflow_path, f"Filtered out: {exclusion_reason}"))
            continue

        # typedefs
        workflow_typedefs = all_workflows_typedefs.get(workflow_path, {})
        workflow_activity_typedefs, workflow_additional_typedefs = {}, {}
        for activity_id, workflow_typedef in workflow_typedefs.items():
            activity_typedef = typedefs_parser.parse_typedef(workflow_typedef["TypeDefinition"])
            workflow_activity_typedefs[activity_id] = activity_typedef
            additional_typedefs = typedefs_parser.parse_typedefs(workflow_typedef["AdditionalTypeDefinitions"])
            workflow_additional_typedefs.update(additional_typedefs)

        # TODO: Fix this when necessary.
        activity_configs = BuildActivityConfigurationContext(True, workflow_name, workflow_activity_typedefs, workflow_additional_typedefs).convert_all(wf)  # type: ignore
        if not activity_configs:
            skipped_workflows.append((workflow_path, "No activities"))
            continue
        workflow_dir.mkdir(exist_ok=True)

        for i, activity_config in enumerate(activity_configs):
            activity_name = activity_config["activity_id"]
            with open(workflow_dir / f"{i:02}___{activity_name}.yaml", "w") as fd:
                yaml_dump(activity_config)

    if skipped_workflows:
        with open(output_activities_dirpath / "_skipped_workflows.txt", "w") as fd:
            for workflow_path, reason in skipped_workflows:
                fd.write(f"{reason} {workflow_path}\n")


def load_queries_and_plans(root_path: pathlib.Path, dataset: dict[str, str]):
    queries = json.load(open(root_path / "_queries.json"))
    without_query = [(k, "Prerequisites: no query") for k in dataset if k not in queries]
    # assert all(k in queries for k in dataset), f"Missing queries {len(missing_queries := set(dataset) - set(queries))} {list(missing_queries)[:5]}"
    assert len(without_query) < max(20, 0.1 * len(dataset)), f"Missing queries {len(without_query)} {without_query[:5]}"
    if without_query:
        print(f"{len(without_query)} workflows do not have a query out of the {len(dataset)}.")
        dataset = {k: v for k, v in dataset.items() if k in queries}
        print(f"Going forward with {len(dataset)} datapoints.")

    plans = json.load(open(root_path / "_plans.json"))
    # assert all(k in plans for k in dataset), f"Missing plans {set(dataset) - set(plans)}"
    without_plan = [(k, "Prerequisites: no plan") for k in dataset if k not in plans]
    assert len(without_plan) < max(20, 0.1 * len(dataset)), f"Missing plans {len(without_plan)} {without_plan[:5]}"
    if without_plan:
        print(f"{len(without_plan)} workflows do not have a plan out of the {len(dataset)}.")
        dataset = {k: v for k, v in dataset.items() if k in plans}
        print(f"Going forward with {len(dataset)} datapoints.")
    return queries, plans, dataset


async def refine_plans(root_path: pathlib.Path, dataset: dict[str, str], loaded_dataset: dict[str, WorkflowDict], overwrite=False, always_load=False):
    queries, plans, filtered_datset = load_queries_and_plans(root_path, dataset)
    dataset = {k: v for k, v in dataset.items() if k in filtered_datset}
    loaded_dataset = {k: v for k, v in loaded_dataset.items() if k in filtered_datset}

    def flush(refined_plans):
        with open(root_path / "_plans_refined.txt", "w") as fd:
            for identifier, plan in refined_plans.items():
                summary = get_workflow_summary(loaded_dataset[identifier])
                indented_refined = "\n".join(f"  {line}" for _i, line in enumerate(plan.splitlines()))
                indented_plan = "\n".join(f"  {line}" for _i, line in enumerate(plans[identifier].splitlines()))
                fd.write(f"{identifier}\nQuery: {queries[identifier]}\nPlan:\n{indented_plan}\nRefined plan:\n{indented_refined}\nSummary\n{summary}\n\n")
        with open(root_path / "_plans_refined.json", "w") as fd:
            json.dump(refined_plans, fd)

    # refined_plans = prompt_llm_for_plans_refinement(dataset, queries, plans, loaded_dataset)
    refined_plans = {}
    remaining_to_process = dataset
    if not overwrite and (plans_path := root_path / "_plans_refined.json").exists():
        inp = "y" if always_load else None
        while inp not in {"y", "n"}:
            inp = input(f"There are refined plans generated at {plans_path}.\nDo you wish to load them? y/n")
        if inp == "y":
            refined_plans = json.load(open(plans_path))
            remaining_to_process = {k: v for k, v in dataset.items() if k not in refined_plans}
            print(f"Loaded {len(refined_plans)} from disk. {len(remaining_to_process)} remaining.")
        elif inp == "n":
            print("Starting fresh with 0 refined plans.")
    async for identifier, plan in prompt_llm_for_plans_refinement_and_iterate(remaining_to_process, queries, plans, loaded_dataset):
        refined_plans[identifier] = plan
        if len(refined_plans) and len(refined_plans) % 100 == 0:  # this flushing gets quadratic for disc writes, avoid using often or implement appending
            flush(refined_plans)
    flush(refined_plans)


def dump_plans_from_workflow(root_path: pathlib.Path, dataset: dict[str, WorkflowDict], prune=False, overwrite=False):
    plans = {}
    remaining_to_process = dataset
    if not overwrite and (plans_path := root_path / "_plans.json").exists():
        inp = None
        while inp not in {"y", "n"}:
            inp = input(f"There are plans generated at {plans_path}.\nDo you wish to load them? y/n")
        if inp == "y":
            plans = json.load(open(plans_path))
            remaining_to_process = {k: v for k, v in dataset.items() if k not in plans}
            print(f"Loaded {len(plans)} from disk. {len(remaining_to_process)} remaining.")
        elif inp == "n":
            print("Starting fresh with 0 plans.")
    for path, datapoint in remaining_to_process.items():
        try:
            wf = Workflow("", "", datapoint)
        except Exception as e:
            print("Exception loading workflow! To look into", path, e)
            continue
        if prune:
            WorkflowPruner().prune(wf)
        try:
            plans[path] = PlanBuilder().build(wf)
        except Exception as e:
            print("Exception building plan!", e, path)

    with open(root_path / "_plans.json", "w") as fd:
        json.dump(plans, fd)
    with open(root_path / "_plans.txt", "w") as fd:
        for identifier, plan in plans.items():
            fd.write(f"{identifier}\n{plan}\n\n")
    if os.path.exists(queries_path := root_path / "_queries.json"):
        with open(queries_path) as fd:
            queries = json.load(fd)
        with open(root_path / "_queries_and_plans.txt", "w") as fd:
            for path, plan in plans.items():
                query = queries.get(path, "<no_query_availabel>")
                summary = get_workflow_summary(dataset[path])
                fd.write(f"{path}\n{'-' * 100}\nQuery: {query}\nPlan:\n{plan}\nSummary\n{summary}\n\n")


def gather_and_dump_typedefs(root_path: pathlib.Path, source_path: pathlib.Path):
    typedef_paths = list(source_path.rglob("**/typeDef.json")) + list(source_path.rglob("**/typedef.json"))
    typedefs = {}
    for path in typedef_paths:
        with open(path) as fd:
            typedef_object = json.load(fd)
        if not typedef_object:
            continue
        workflow_identifier = root_path / path.parent.relative_to(source_path).with_suffix(".yaml")
        if not workflow_identifier.exists():
            continue
        typedefs[workflow_identifier.as_posix()] = typedef_object

    with open(root_path / "_typedefs.json", "w") as fd:
        json.dump(typedefs, fd, indent=2)


def main(args):
    allowlist, disallowlist = None, None
    if args.partial:  # helpful when you have the dataset partially processed
        allowlist = set(json.load(open(args.data_path / "_queries.json")).keys())
    if args.remaining is not None:  # helpful when you want to process the remaining dataset
        # disallowlist = set(json.load(open(args.data_path / "_queries.json")).keys())
        # disallowlist = set(json.load(open(args.data_path / "_plans.json")).keys())
        if not args.remaining.exists():
            args.remaining = args.data_path / args.remaining
        assert args.remaining.exists()
        disallowlist = set(json.load(open(args.remaining)).keys())
    dataset, dataset_raw, invalid_workflows = load_data(
        args.data_path, verbose=True, bounds=(10, 100_000), limit=args.limit, allowlist=allowlist, disallowlist=disallowlist
    )
    print(f"Loaded {len(dataset)} workflows. {len(invalid_workflows)} invalid workflows.")
    if args.what_to_generate in {"queries", "qp", "qpa", "all"}:
        asyncio.run(generate_and_dump_queries(args.data_path, dataset_raw, overwrite=args.overwrite))
    if args.what_to_generate in {"plans", "qp", "pa", "qpa", "all"}:
        # return generate_and_dump_plans(args.data_path, dataset_raw)
        dump_plans_from_workflow(args.data_path, dataset, prune=args.prune, overwrite=args.overwrite)
    if args.what_to_generate in {"activities", "pa", "qpa", "all"}:
        # return dump_activities_dataset(args.data_path, dataset)
        dump_activities_dataset(args.data_path, dataset, prune=args.prune)
    if args.what_to_generate in {"plan_refinement"}:
        asyncio.run(refine_plans(args.data_path, dataset_raw, dataset))
    if args.what_to_generate in {"typedefs"}:
        gather_and_dump_typedefs(
            pathlib.Path("/workspace/data/workflows/parsed_portable2"), pathlib.Path("/workspace/data/workflows/portable_converted_unzipped")
        )
        gather_and_dump_typedefs(
            pathlib.Path("/workspace/data/workflows/parsed_windows2"), pathlib.Path("/workspace/data/workflows/windows_converted_unzipped")
        )


if __name__ == "__main__":
    dotenv.load_dotenv(override=True)
    parser = argparse.ArgumentParser(description="Generate queries from data")
    parser.add_argument(
        "what_to_generate", choices=["queries", "plans", "activities", "qp", "pa", "qpa", "all", "plan_refinement", "typedefs"], help="What to generate."
    )
    parser.add_argument("data_path", type=pathlib.Path, help="Path to the data file.")
    parser.add_argument("--limit", type=int, help="Limit the number of workflows.")

    # more specific flags
    # dumping plans
    parser.add_argument("--prune", action="store_true", help="Prune the workflows of irrelevant activities.")

    # processing lots of data
    parser.add_argument("--overwrite", action="store_true", help="Skip loading and overwrite existing results.")
    parser.add_argument("--partial", action="store_true", help="Specific parameter. Avoid using.")
    parser.add_argument("--remaining", type=pathlib.Path, help="Specify a json to load. Its dict keys / list items will be considered processed records.")
    args = parser.parse_args()
    main(args)
