import argparse
import collections
import math
import pathlib

import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
import tqdm

from services.studio._text_to_workflow.utils.workflow_utils import get_workflow_summary, iterate_activities, iterate_and_apply
from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import load_data


def dump_summaries(dataset: dict[str, str], output_path: pathlib.Path):
    with open(output_path / "_summaries.txt", "w") as fd:
        for identifier, workflow in tqdm.tqdm(dataset.items()):
            fd.write(f"{identifier}\n{get_workflow_summary(workflow)}\n\n")


def dump_length_histogram(dataset: dict[str, str], output_path: pathlib.Path):
    sizes = [*map(len, dataset.values())]
    print(f"There are {sizes.count(0)} with 0 size")
    log_sizes = [*map(math.log10, [s for s in sizes if s > 0])]
    fig, axs = plt.subplots(1, 2, figsize=(10, 5))

    # Plot the first histogram
    axs[0].hist(sizes, bins=50)
    axs[0].set_title("Sizes Histogram")
    axs[0].set_xlabel("Sizes")
    axs[0].set_ylabel("Frequency")

    # Plot the second histogram
    axs[1].hist(log_sizes, bins=50)
    axs[1].set_title("Log Sizes Histogram")
    axs[1].set_xlabel("Log Sizes (powers of 10)")
    axs[1].set_ylabel("Frequency")
    axs[1].xaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: f"$10^{{{int(x)}}}$"))

    fig.savefig(output_path / "_sizes_histogram.png")


def dump_packages(dataset: dict[str, str], output_path: pathlib.Path):
    package_names_counter = collections.Counter()
    versions_counter = collections.Counter()
    packages = collections.defaultdict(collections.Counter)
    for _path, workflow in dataset.items():
        for package in workflow.get("packages", []):
            package_name, package_version = package.split(",")
            package_names_counter[package_name] += 1
            packages[package_name].update([package_version])
            versions_counter.update([package_version])

    n_packages = sum(package_names_counter.values())

    with open(output_path / "packages_all.txt", "w") as fd:
        for package_name in sorted(package_names_counter):
            fd.write(f"{package_name}\n")
    with open(output_path / "packages_versions.txt", "w") as fd:
        for count, version in versions_counter.most_common():
            fd.write(f"{count:3} {version}\n")
    with open(output_path / "packages_most_common.txt", "w") as fd:
        for package_name, count in package_names_counter.most_common():
            fd.write(f"{count:3} {count / n_packages * 100:3.0f}% {package_name}\n")
            for version, count in packages[package_name].most_common():
                fd.write(f"  {count:3} {version}\n")


def dump_activities(dataset: dict[str, str], output_path: pathlib.Path):
    thoughts = collections.Counter()
    activities = collections.Counter()
    activities_params = collections.defaultdict(collections.Counter)

    def update_counters(node, _ancestry, _variables):
        if "activity" not in node or "thought" not in node:
            return
        thoughts[node["thought"]] += 1
        activities[node["activity"]] += 1
        activities_params[node["activity"]].update(node.get("params", {}).keys())

    for _path, workflow in tqdm.tqdm(dataset.items()):
        iterate_and_apply(workflow, update_counters)

    n_activities = sum(activities.values())

    with open(output_path / "_thoughts_most_common.txt", "w") as fd:
        for thought, count in thoughts.most_common():
            fd.write(f"{count:3} {thought}\n")
    with open(output_path / "_thoughts_sorted.txt", "w") as fd:
        for thought in sorted(thoughts.keys()):
            fd.write(f"{thought}\n")

    with open(output_path / "_activities_sorted.txt", "w") as fd:
        for activity in sorted(activities.keys()):
            fd.write(f"{activity}\n")
    with open(output_path / "_activities_most_common.txt", "w") as fd:
        for activity, count in activities.most_common():
            fd.write(f"{count:3} {count / n_activities * 100:5.2f}% {activity}\n")
    with open(output_path / "_activities_params_by_most_common.txt", "w") as fd:
        for activity, count in activities.most_common():
            fd.write(f"{count:3} {count / n_activities * 100:3.0f}% {activity}\n")
            for param, c in activities_params[activity].most_common():
                fd.write(f"  {c:3} {c / count * 100:3.0f}% {param}\n")
    with open(output_path / "_activities_params_by_sorted_name.txt", "w") as fd:
        for activity, count in sorted(activities.most_common(), key=lambda x: x[0]):
            fd.write(f"{activity}\n")
            for param, c in activities_params[activity].most_common():
                fd.write(f"  {c:3} {c / count * 100:3.0f}% {param}\n")


def dump_variables(dataset: dict[str, str], output_path: pathlib.Path):
    all_variables = collections.Counter()
    variable_types = collections.Counter()

    def gather_vars(workflow):
        workflow_variables = set()
        for _node, _ancestry, variables in iterate_activities(workflow):
            # if any(v["name"] is None for v in variables):
            #     print(_path)
            #     return
            variables_str = [f"{v.get('name', '')}:{v.get('type', '')}" for v in variables]
            workflow_variables |= set(variables_str)
        all_variables.update(workflow_variables)
        variable_types.update([v.split(":")[1] for v in workflow_variables])

    for _path, workflow in tqdm.tqdm(dataset.items()):
        gather_vars(workflow)

    n_variable_types = sum(variable_types.values())

    with open(output_path / "_variables_most_common.txt", "w") as fd:
        for var, count in all_variables.most_common():
            fd.write(f"{count:3} {var}\n")
    with open(output_path / "_variables.txt", "w") as fd:
        for var in sorted(all_variables.keys()):
            fd.write(f"{var}\n")
    with open(output_path / "_variable_types_most_common.txt", "w") as fd:
        for var, count in variable_types.most_common():
            fd.write(f"{count:3} {count / n_variable_types * 100:3.0f}% {var}\n")
    with open(output_path / "_variable_types.txt", "w") as fd:
        for var in sorted(variable_types.keys()):
            fd.write(f"{var}\n")


def main(args):
    dataset, dataset_raw, invalid_workflows = load_data(args.data_path, limit=args.limit)

    output_path = args.data_path.with_name(args.data_path.name + "_statistics")
    output_path.mkdir(exist_ok=True)
    with open(output_path / "_invalid_workflows.txt", "w") as fd:
        for path in invalid_workflows:
            fd.write(f"{path.as_posix()}\n")
    dump_packages(dataset, output_path)
    dump_activities(dataset, output_path)
    dump_length_histogram(dataset_raw, output_path)
    dump_summaries(dataset, output_path)
    dump_variables(dataset, output_path)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate queries from data")
    parser.add_argument("data_path", type=pathlib.Path, help="Path to the data file")
    parser.add_argument("--limit", type=int, help="Limit the number of workflows to load")
    args = parser.parse_args()
    main(args)
