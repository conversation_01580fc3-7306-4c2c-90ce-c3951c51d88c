import argparse
import collections
import html
import pathlib
import re
from typing import Any, Dict, List

import pandas as pd


def expand_paths(paths: List[pathlib.Path]) -> List[pathlib.Path]:
    """Expand directories into all the .html files within them."""
    expanded_paths = []
    for input_path in paths:
        if input_path.is_dir():
            expanded_paths.extend(p for p in input_path.iterdir() if p.name.endswith(".html"))
        else:
            expanded_paths.append(input_path)
    return expanded_paths


def parse_prediction(prediction_output: str) -> Dict[str, Any]:
    """Parse an inner html prediction output into a dictionary."""

    section_re = re.compile(r">(?P<section>[^<]*?)</button>\s*<pre style=\"display:none\">(?P<content>[^<]*?)(?=</pre)", re.DOTALL)
    score_re = re.compile(r"^\s*(?P<metric>[\w ]+)\s(?P<value>-?\d+\.?\d*)", re.MULTILINE)

    def get_sections():
        sections = {}
        for match in section_re.finditer(prediction_output):
            d = match.groupdict()
            sections[d["section"]] = html.unescape(d["content"])
        return sections

    def get_scores():
        return {metric: float(value) for metric, value in score_re.findall(prediction_output)}

    lines = prediction_output.strip().splitlines()
    query = lines[0]
    query = re.sub(r"^<.*?>|<.*?>$", "", query)  # remove header tags around query
    query = re.sub(r"^\d+\s*/\s*\d+\s*", "", query)  # remove index from beginning of query

    sections = get_sections()
    scores = get_scores()

    _remaining = score_re.sub("", section_re.sub("", prediction_output))
    sections["_remaining"] = _remaining

    return {"html": prediction_output, **sections, "query": query, **scores}


def _parse_footer(prediction_footer: str) -> Dict[str, Any]:
    """Parse the footer of the prediction output."""

    def get_scores():
        score_re = re.compile(r"^\s*(?P<metric>(AVERAGE|average)\s\w[\w ]+)\:?\s\s?(?P<value>\d+\.?\d*)", re.MULTILINE)
        return {match["metric"]: float(match["value"]) for match in score_re.finditer(prediction_footer)}

    return get_scores()


def parse_html(path: pathlib.Path) -> tuple[list[dict[str, Any]], dict[str, Any]]:
    """Parse the html output of the model into a list of dictionaries."""
    _preamble, *predictions = re.split(r"\*\*\*", path.read_text())
    if predictions:
        predictions[-1], _footer = re.split(r"(?=<pre>\nFULLY VALID COUNT)", predictions[-1], maxsplit=1)
    else:
        _footer = _preamble
    parsed_datapoints = [parse_prediction(p) for p in predictions]
    parsed_datapoints = [datapoint | {"index": i} for i, datapoint in enumerate(parsed_datapoints, 1)]
    parsed_aggregates = _parse_footer(_footer)
    return parsed_datapoints, parsed_aggregates


def load_data(paths: List[pathlib.Path]) -> List[Dict[str, Any]]:
    """Load the data from the html files into a list of dictionaries."""
    data, aggregates = [], []
    for p in paths:
        identifier = p.absolute().as_posix()
        datapoints, _aggregates = parse_html(p)
        data.extend({"identifier": identifier, **datapoint} for datapoint in datapoints)
        aggregates.append({"identifier": identifier, **_aggregates})
    return data, aggregates


def print_summary(df: pd.DataFrame):
    """Print summary statistics for some metrics."""
    statistics = collections.defaultdict(dict)
    for name, agg in df.groupby("identifier"):
        statistics["pted"][name] = agg["TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE"].describe().drop("count")
        statistics["lev"][name] = agg["PLAN SCORE"].describe().drop("count")
        statistics["time"][name] = agg["elapsed time"].describe().drop("count")

    for metric in ["pted", "lev", "time"]:
        print(metric)
        m = pd.DataFrame(statistics[metric])
        _names = m.columns.tolist()
        print(m.rename(columns=lambda x: str(_names.index(x) + 1)).to_string())  # noqa: B023
        print("Legend:")
        print("\n".join(f"{i}: {name}" for i, name in enumerate(_names, 1)))
        print()


def print_details(df: pd.DataFrame):
    """Print details for each query."""
    for i, (query, agg) in enumerate(df.groupby("query"), 1):
        print("=" * 50)
        print(f"{i} / 30. {query}")
        print("=" * 50)
        print()

        name_to_data = dict(zip(agg["name"].tolist(), agg.to_dict("records"), strict=False))
        # model_order = [
        #     'gpt4',
        #     'claude-3-opus',
        # ]
        # order = sorted(name_to_data.keys())
        order = list(name_to_data.keys())
        for name in order:
            datapoint = name_to_data[name]
            lev = float(datapoint["PLAN SCORE"])
            pted = float(datapoint["TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE"])

            print(f"{name} lev {lev:.3f} pted {pted:.3f}")
            print("-" * 30)
            print(datapoint["plan"])
            print()

        print("Ideal")
        print("-" * 30)
        print(datapoint["ideal"])  # pick ground truth from the last datapoint
        print()

        print("\n" * 5)


def main(args):
    paths = expand_paths(args.input)
    paths = [p for p in paths if not any(re.search(pattern, p.name) for pattern in args.exclude)]
    loaded_datapoints, _loaded_aggregates = load_data(paths)
    df = pd.DataFrame(loaded_datapoints).dropna(axis=1)
    df["name"] = df["identifier"].apply(lambda p: pathlib.Path(p).name.replace("output-full-", "").replace(".html", ""))

    print_summary(df)
    print("\n" * 10)

    print_details(df)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("input", nargs="+", type=pathlib.Path)
    parser.add_argument("--exclude", nargs="+", type=str, default=[])
    args = parser.parse_args()
    main(args)
