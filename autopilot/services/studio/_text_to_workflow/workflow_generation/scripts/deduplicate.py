import argparse
import collections
import copy
import multiprocessing
import pathlib
import shutil

import tqdm

from services.studio._text_to_workflow.utils import minhash
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

N_PROCESSES = 32


def _load_and_return(path):
    return path, yaml_load(path)


def load_datapoints(dataset_path):
    root_path = pathlib.Path(dataset_path)
    listing = list(root_path.rglob("*.yaml"))

    datapoints = {}
    with multiprocessing.Pool(N_PROCESSES) as pool:
        for path, datapoint in tqdm.tqdm(pool.imap_unordered(_load_and_return, listing), total=len(listing)):
            datapoints[path] = datapoint
    return datapoints


def _print_groups(groups):
    for i, paths in enumerate(sorted(groups, key=len, reverse=True)):
        if len(paths) < 2:
            continue
        print(f"Group {i} ({len(paths)}):")
        for path in sorted(paths):
            print(f"  {path}")


def filter_exact_matches(datapoints):
    exact_matches = collections.defaultdict(list)
    for path, datapoint in tqdm.tqdm(datapoints.items()):
        serialization = str(datapoint["process"])
        exact_matches[serialization].append(path)
    _print_groups(exact_matches.values())
    for paths in exact_matches.values():
        for path in sorted(paths)[1:]:
            del datapoints[path]


def _filter_by_hash_duplicate_mp(datapoints, hash_func):
    matches = collections.defaultdict(list)
    with multiprocessing.Pool(N_PROCESSES) as pool:
        for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(hash_func, datapoints.values()), total=len(datapoints)), strict=False):
            matches[h].append(path)
    _print_groups(matches.values())
    for paths in matches.values():
        for path in sorted(paths)[1:]:
            del datapoints[path]
    return matches


def _get_str_no_process_name(datapoint):
    obj = copy.deepcopy(datapoint["process"])
    del obj["processName"]
    return str(obj)


def filter_partial_matches_no_process_name(datapoints):
    return _filter_by_hash_duplicate_mp(datapoints, _get_str_no_process_name)


def _get_str_no_packages(datapoint):
    obj = copy.deepcopy(datapoint["process"])
    del obj["packages"]
    del obj["processName"]
    return str(obj)


def filter_partial_matches_no_packages(datapoints):
    return _filter_by_hash_duplicate_mp(datapoints, _get_str_no_packages)


def _get_str_no_thoughts(datapoint):
    obj = copy.deepcopy(datapoint["process"])
    del obj["packages"]
    del obj["processName"]

    def strip_thoughts(node):
        if isinstance(node, dict):
            if "thought" in node:
                del node["thought"]
            for v in node.values():
                strip_thoughts(v)
        if isinstance(node, list):
            for v in node:
                strip_thoughts(v)

    strip_thoughts(obj)

    return str(obj)


def filter_partial_matches_no_thoughts(datapoints):
    return _filter_by_hash_duplicate_mp(datapoints, _get_str_no_thoughts)


def _get_str_no_variable_names(datapoint):
    obj = copy.deepcopy(datapoint["process"])
    del obj["packages"]
    del obj["processName"]

    def strip_thoughts(node):
        if isinstance(node, dict):
            if "thought" in node:
                del node["thought"]
            for v in node.values():
                strip_thoughts(v)
        if isinstance(node, list):
            for v in node:
                strip_thoughts(v)

    strip_thoughts(obj)

    variables = obj.pop("variables", [])
    arguments = obj.pop("arguments", [])
    obj_as_str = str(obj)

    def find_by_index(name):
        try:
            return obj_as_str.index(name)
        except ValueError:
            return 1e9

    var_names = [str(var["name"]) for var in variables]
    var_names = sorted(var_names, key=find_by_index)

    arg_names = [str(var["name"]) for var in arguments]
    arg_names = sorted(arg_names, key=find_by_index)

    for i, var_name in enumerate(var_names):
        obj_as_str = obj_as_str.replace(var_name, f"var_{i}")
    for i, arg_name in enumerate(arg_names):
        obj_as_str = obj_as_str.replace(arg_name, f"arg_{i}")
    return obj_as_str


def filter_partial_matches_no_variable_names(datapoints):
    return _filter_by_hash_duplicate_mp(datapoints, _get_str_no_variable_names)


def flush_datapoints(root_path, datapoints, *, output_path=None, suffix=None):
    if output_path is not None:
        assert suffix is None
        new_root = pathlib.Path(output_path)
    elif suffix is not None:
        new_root = root_path.with_name(f"{root_path.name}_{suffix}")
    new_root.mkdir(exist_ok=True)
    for path, _ in datapoints.items():
        new_path = new_root / path.relative_to(root_path)
        # print(f"Copying {path} to {new_path}")
        new_path.parent.mkdir(exist_ok=True, parents=True)
        shutil.copy2(path, new_path)
    return new_root


def _get_activity_tree(datapoint):
    obj = copy.deepcopy(datapoint["process"])
    tree = []

    def traverse(node, level=0):
        if isinstance(node, dict):
            if "activity" in node:
                tree.append(f"{level * '  '}{node['activity']}")
                level += 1
            for v in node.values():
                traverse(v, level)
        if isinstance(node, list):
            for v in node:
                traverse(v, level)

    traverse(obj)
    return "\n".join(tree)


def partition_by_activity_tree(datapoints):
    activity_tree_matches = collections.defaultdict(list)
    with multiprocessing.Pool(N_PROCESSES) as pool:
        for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(_get_activity_tree, datapoints.values()), total=len(datapoints)), strict=False):
            activity_tree_matches[h].append(path)
    return activity_tree_matches


def datapoint_to_minhash_representation(datapoint):
    representation = {}
    if "trigger" in datapoint["process"]:
        representation["trigger"] = copy.deepcopy(datapoint["process"]["trigger"])
    representation["workflow"] = copy.deepcopy(datapoint["process"]["workflow"])
    return yaml_dump(representation)


def find_duplicates(args):
    _hash, datapoints_subset, n_hashes, threshold = args
    paths = list(datapoints_subset.keys())
    if len(datapoints_subset) < 2:
        return _hash, paths, {}
    dp_representation = [*map(datapoint_to_minhash_representation, datapoints_subset.values())]
    shingles = minhash.create_shingles(dp_representation, paths)
    _, dupes_to_scores = minhash.find_duplicates(dp_representation, paths, shingles, n_hashes, threshold)
    return _hash, paths, dupes_to_scores


def apply_minhash(datapoints, activity_tree_partitioning, n_hashes=100, threshold=0.9):
    def iterator():
        for _h, paths in activity_tree_partitioning.items():
            if len(paths) < 2:
                continue
            yield _h, {path: datapoints[path] for path in paths}, n_hashes, threshold

    duplicates = {}
    with (
        multiprocessing.Pool(N_PROCESSES) as pool,
        tqdm.tqdm(total=len(datapoints)) as pbar,
    ):
        for _hash, paths, dupes_to_scores in pool.imap_unordered(find_duplicates, iterator()):
            pbar.update(len(paths))
            duplicates[_hash] = dupes_to_scores
    return duplicates


def filter_by_minhash_dupes(datapoints, duplicates, activity_tree_matches):
    total_unique_dupes = {h: len(set.union(set(), *(set(paths) for paths, score in dupes.items()))) for h, dupes in duplicates.items()}

    for i, (_hash, dupes) in enumerate(sorted(duplicates.items(), key=lambda k: total_unique_dupes[k[0]], reverse=True)):
        print(f"Group {i} ({total_unique_dupes[_hash]} / {len(activity_tree_matches[_hash])}):")
        remaining_paths = set(activity_tree_matches[_hash])
        for paths, _ in sorted(dupes.items(), key=lambda k: k[1], reverse=True):
            remaining_paths -= set(sorted(paths)[1:])
        remaining_paths = sorted(remaining_paths)
        print(f"Remaining ({len(remaining_paths)}):")
        for path in remaining_paths:
            print(f"  {path}")
        print(f"Deduped ({len(activity_tree_matches[_hash]) - len(remaining_paths)}):")
        for path in sorted(set(activity_tree_matches[_hash]) - set(remaining_paths)):
            print(f"  {path}")
        for path in set(activity_tree_matches[_hash]) - set(remaining_paths):
            del datapoints[path]


def _is_uia(args):
    path, datapoint = args
    yaml_serialization = yaml_dump(datapoint)
    return path, datapoint, "activity: UiPath.UIAutomation".lower() in yaml_serialization.lower()


def split_datapoints_uia(datapoints):
    datapoints_uia = {}
    datapoints_nonuia = {}

    with multiprocessing.Pool(32) as pool:
        for path, datapoint, is_uia in tqdm.tqdm(pool.imap_unordered(_is_uia, list(datapoints.items())), total=len(datapoints)):
            if is_uia:
                datapoints_uia[path] = datapoint
            else:
                datapoints_nonuia[path] = datapoint

    print(f"Total = {len(datapoints)}")
    print(f"UIA = {len(datapoints_uia)}")
    print(f"Non-UIA = {len(datapoints_nonuia)}")
    return datapoints_uia, datapoints_nonuia


def hard_deduplicate(root_path):
    datapoints = load_datapoints(root_path)
    filter_exact_matches(datapoints)
    filter_partial_matches_no_process_name(datapoints)
    filter_partial_matches_no_packages(datapoints)
    filter_partial_matches_no_thoughts(datapoints)
    filter_partial_matches_no_variable_names(datapoints)
    return flush_datapoints(root_path, datapoints, suffix="deduplicated_part1")


def soft_deduplicate(root_path):
    datapoints = load_datapoints(root_path)
    activity_tree_partitioning = partition_by_activity_tree(datapoints)
    duplicates = apply_minhash(datapoints, activity_tree_partitioning, 25, 0.9)
    filter_by_minhash_dupes(datapoints, duplicates, activity_tree_partitioning)
    return flush_datapoints(root_path, datapoints, suffix="deduplicated_part2")


def group_uia(root_path):
    datapoints = load_datapoints(root_path)
    datapoint_uia, datapoint_api = split_datapoints_uia(datapoints)
    flush_datapoints(root_path, datapoint_uia, suffix="uia")
    flush_datapoints(root_path, datapoint_api, suffix="api")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("root_path", type=str)
    args = parser.parse_args()

    # args.root_path = "/workspace/data/workflows/windows_20240805"

    dedup1_path = hard_deduplicate(args.root_path)
    dedup2_path = soft_deduplicate(dedup1_path)
