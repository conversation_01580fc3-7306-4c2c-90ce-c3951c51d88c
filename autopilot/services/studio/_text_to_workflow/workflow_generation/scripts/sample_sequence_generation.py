import argparse
import asyncio
import copy
import pathlib

import numpy as np

from experimental.autopilot_dataset import wfgen_dataset as workflow_generation_dataset
from services.studio._text_to_workflow.common import typedefs
from services.studio._text_to_workflow.common.schema import WorkflowDict
from services.studio._text_to_workflow.common.walkers import (
    ActivitiesAndTriggersCollector,
    PlanBuilder,
    TestWorkflowParenthoodValidity,
    WorkflowPruner,
    WorkflowSequenceExtractor,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils.testing import build_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.scripts import generate_from_production_data


async def main():
    typedefs.load()
    parser = argparse.ArgumentParser(description="Process workflow generation sequences.")
    parser.add_argument("paths", type=pathlib.Path, nargs="*", help="Paths to the YAML files to process")
    parser.add_argument("--output", type=pathlib.Path, default=None, help="Output directory for the generated sequences")
    parser.add_argument("--limit", type=int, default=None, help="Limit the number of sequences to generate")
    args = parser.parse_args()

    await sample(args.paths, args.output, args.limit)


async def sample(paths: list[pathlib.Path], output_dir: pathlib.Path | None = None, limit: int | None = None):
    # dataset = workflow_generation_dataset.load_workflow_generation_dataset("Windows")
    # datapoints = dataset["train"]
    datapoints = {path: yaml_load(open(path)) for path in paths}
    if output_dir is not None:
        output_dir.mkdir(parents=True, exist_ok=True)

    n_generated = 0
    for path, datapoint in datapoints.items():
        for i, dp in enumerate(await sample_sequences(path, datapoint)):
            n_generated += 1
            if limit is not None and n_generated > limit:
                break
            if output_dir is not None:
                output_path = output_dir / f"{path.stem}-sequence-{i}.yaml"
                yaml_dump(dp, output_path)
        print("\n" * 3)
    print(f"Generated {n_generated} sequences.")


async def sample_sequences(path: pathlib.Path, datapoint: dict):
    datapoint = copy.deepcopy(datapoint)
    print("Sampling from")
    print(path)
    print()

    print("Original workflow:")
    print(datapoint["description"])
    print(datapoint["plan"])
    print()

    workflow = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])

    # prune workflow (default: remove comment out and collapse try catches)
    WorkflowPruner().prune(workflow)
    assert TestWorkflowParenthoodValidity().validate(workflow), f"Invalid workflow {path}"
    datapoint["process"] = workflow.to_dict()
    datapoint["plan"] = PlanBuilder().build(workflow)
    print("Pruned workflow:")
    print(datapoint["description"])
    print(datapoint["plan"])
    print()

    # create all possible sequences from workflow
    cutting_points = WorkflowSequenceExtractor().generate_cutting_points(workflow)
    sequences_datapoints = [workflow_generation_dataset._seqgen_get_datapoint(datapoint, start, end) for start, end in cutting_points]
    n_steps_sequence = [len(dp["plan_sequence"].split("\n")) for dp in sequences_datapoints]
    print(f"Generated {len(sequences_datapoints)} sequences of various lengths.")

    # filter by sequences that are between 1 and 10 steps long
    sequences_datapoints = [dp for dp in sequences_datapoints if 1 <= len(dp["plan_sequence"].split("\n")) <= 10]
    print(f"Filtered to {len(sequences_datapoints)} sequences between 1 and 5 steps long.")

    # filter by ratio of sequence steps to original steps between 20% and 80%
    # sequences_datapoints = [dp for dp in sequences_datapoints if 0.2 <= len(dp["plan_sequence"].split("\n")) / len(dp["plan"].split("\n")) <= 0.8]

    # filter by heuristics regarding activities inside the sequence
    filtered_sequences_datapoints = []
    for dp in sequences_datapoints:
        n_steps = len(dp["plan_sequence"].split("\n"))
        sequence_as_workflow: WorkflowDict = {"workflow": dp["process_sequence"]}  # type: ignore
        sequence_object = Workflow(dp["description"], dp["plan_sequence"], sequence_as_workflow)
        sequence_activities = ActivitiesAndTriggersCollector().collect(sequence_object)["activity"]
        if n_steps > 1 and len(set(act.activity_id for act in sequence_activities)) < 2:
            continue  # same activity
        if sum("assign" in act.activity_id.lower() for act in sequence_activities) / n_steps > 0.5:
            continue  # > 50% assigns
        if sum("logmessage" in act.activity_id.lower() for act in sequence_activities) / n_steps > 0.5:
            continue  # > 50% log messages
        if sum(any(keyword in act.activity_id.lower() for keyword in ["logmessage", "assign"]) for act in sequence_activities) / n_steps > 0.7:
            continue  # > 70% log messages or assigns
        filtered_sequences_datapoints.append(dp)
    sequences_datapoints = filtered_sequences_datapoints
    print(f"Filtered to {len(sequences_datapoints)} sequences after heuristics.")

    # sample sequences
    TO_SAMPLE = 5
    n_steps_sequence = [len(dp["plan_sequence"].split("\n")) for dp in sequences_datapoints]
    _probs = [n_steps / sum(n_steps_sequence) for n_steps in n_steps_sequence]
    _probs = [p**0.8 for p in _probs]  # flatten the distribution a little bit
    _probs = [p / sum(_probs) for p in _probs]  # normalize
    _probs[-1] = 1 - sum(_probs[:-1])  # make sure they add up to 1
    sequences_datapoints = [
        sequences_datapoints[i]
        for i in np.random.choice(  # need to use numpy here for sampling without replacement and weighting
            len(sequences_datapoints),
            min(TO_SAMPLE, len(sequences_datapoints)),
            p=_probs,
            replace=False,
        )
    ]
    print(f"Sampled {len(sequences_datapoints)} sequences.")
    print()

    # generate queries for the considered sequences
    for dp in sequences_datapoints:
        dp["description_sequence"] = await generate_query_from_sequence_datapoint(dp)
        print(dp["description_sequence"])
        print(dp["plan_sequence"])
        print()
    return sequences_datapoints


async def generate_query_from_sequence_datapoint(datapoint: dict) -> str:
    lmyaml: WorkflowDict = {
        # "processName": datapoint["process"]["processName"],
        "packages": datapoint["process"]["packages"],
        "workflow": datapoint["process_sequence"],
    }  # type: ignore
    workflow = Workflow("", datapoint["plan_sequence"], lmyaml)
    return await generate_from_production_data.prompt_llm_for_query(build_testing_request_context(), workflow)


if __name__ == "__main__":
    asyncio.run(main())
