# VLLM

run `docker_build.sh` to clone the repo and build the docker image
if you need to rerun this you can comment out the clone repo and checkout for next runs

if this is the first time you run this, it could take several hours to build the docker image

then run `docker_run.sh` - for this you might need to change some volume paths and maybe the port

## Using huggingface models

VLLM uses huggingface models. you need to download from e.g.
https://huggingface.co/meta-llama/Meta-Llama-3.1-70B-Instruct/tree/main

e.g.

```sh
git lfs install
git clone https://huggingface.co/meta-llama/Meta-Llama-3.1-70B-Instruct
```

then modify the config.json from the cloned folder to include `"type": "extended"` in `rope_scaling`

## Inside the container

from inside the docker container you can just run either of the entrypoints.
the entrypoint can be:

* vllm.entrypoints.openai.api_server: `vllm_openai.sh`
* vllm.entrypoints.api_server: `vllm_generate.sh`

first time you download a model from huggingface it is missing something vllm expects in the config.json. the config.json needs to look like this:

```json
"rope_scaling": {
    "factor": 8.0,
    "high_freq_factor": 4.0,
    "low_freq_factor": 1.0,
    "original_max_position_embeddings": 8192,
    "rope_type": "llama3",
    "type": "extended"
  },
```

the openai one allows messages with roles `system`, `user`, `assistant`
the generate entrypoint just allows regular generation

you can find client examples in:
client/curl examples are `client_openai.sh` and `client_generate.sh`
