import argparse
import copy
import csv
import json
import pathlib
import sys
import time
import traceback
from datetime import datetime

import <PERSON><PERSON>htein
import requests

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths, uipath_cloud_platform
from services.studio._text_to_workflow.utils.workflow_utils import cleanup_rpa_wf_workflow_for_compare, use_typeid_in_activities
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score
from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import ActivityTreeAccuracyCalculator

# model = "gpt-4"
# model = "gpt-35-turbo-16k"


server_urls = {
    # "alpha": f"https://aiwork-alp-svc-ne-01-g-dns.northeurope.cloudapp.azure.com/text-to-workflow/api-automation?model={model}",
    "local": f"http://localhost:{settings.PORT}",
    "alpha": "https://alpha.uipath.com/autopilot_templates/DefaultTenant/autopilotstudio_/v2",
    "stg": "https://staging.uipath.com/autopilot_templates/DefaultTenant/autopilotstudio_/v2",
    "prod": "https://cloud.uipath.com/autopilottests/DefaultTenant/autopilotstudio_/v2",
}

urls_workflow_generation = {k: f"{v}/generate-workflow" for k, v in server_urls.items()}
urls_sequence_generation = {k: f"{v}/generate-sequence" for k, v in server_urls.items()}


def get_key_tokens(response, key="completion_tokens"):
    result = 0
    if "usage" in response:
        usage = response["usage"]
        if "planning" in usage:
            planning = usage["planning"]
            if key in planning:
                result += planning[key]
        if "generation" in usage:
            generation = usage["generation"]
            if key in generation:
                result += generation[key]
    return result


if __name__ == "__main__":
    now_str = datetime.today().strftime("%Y%m%d_%H%M%S")
    ap = argparse.ArgumentParser()
    ap.add_argument("--deployment", type=str, default="workflow", choices=["local", "alpha", "stg", "prod"])
    ap.add_argument("--mode", type=str, choices=["workflow", "sequence"])
    # testset evaluation
    ap.add_argument("--framework", type=str, choices=["Portable", "Windows"])
    ap.add_argument("--subset", type=str, default="test", choices=["test"])
    # CLI query inference
    ap.add_argument("--query", type=str, default=None)
    # CLI filepath inference
    ap.add_argument("--filepath", type=pathlib.Path, default=None)
    # optional params
    ap.add_argument("--runs", type=int, default=2)
    ap.add_argument("--limit", type=int, default=None)
    ap.add_argument("--sequence-roots-only", type=bool, default=False)
    ap.add_argument("--verbose", type=bool, default=True)

    args = ap.parse_args()
    deployment = args.deployment

    token = uipath_cloud_platform.get_token_from_env()
    if args.mode == "workflow":
        dataset = workflow_generation_dataset.load_workflow_generation_subset(args.framework, args.subset)
    elif args.mode == "sequence":
        dataset = workflow_generation_dataset.load_sequence_generation_subset(args.framework, args.subset)
        if args.sequence_roots_only:
            dataset = {k: v for k, v in dataset.items() if k.name.startswith("00__")}

    examples: dict[pathlib.Path, dict] = {}
    if args.query:
        if args.mode == "workflow":
            examples.update({None: {"description": args.query}})  # type: ignore (simulate a datapoint having only the description)
        if args.mode == "sequence":
            for k, v in dataset.items():
                if v.get("description") == args.query or v.get("description_sequence") == args.query:
                    examples[k] = v
                    break
    if args.filepath:
        examples.update({args.filepath: yaml_load(args.filepath)})
    if not examples:
        examples = dataset

    connections = yaml_load(paths.get_dataset_connections_path())["connections"]
    headers = {
        "Content-type": "application/json; charset=utf-8",
        "Authorization": token,
        #    "X-Uipath-Localization": "es-ES"
    }

    metadata = {
        "query": [],
        "path": [],
    }
    metrics = {
        "plan_scores": [],
        "ted_scores": [],
        "pled_scores": [],
        "param_scores": [],
        "elapsed_times": [],
        "prompt_tokens": [],
        "completion_tokens": [],
    }
    for iteration in range(args.runs):
        print("=== ITERATION", iteration)
        for key in metrics:
            metrics[key].append([])
        for key in metadata:
            metadata[key].append([])
        for index, (path, datapoint) in enumerate(examples.items()):
            if args.limit is not None and index >= args.limit:
                break
            if args.mode == "workflow" or args.mode == "sequence" and args.sequence_roots_only:
                query = datapoint["description"]
            else:  # args.mode == "sequence"
                query = datapoint["description_sequence"]

            payload = {
                "userRequest": query,
                "targetFramework": args.framework,
                "connections": connections,
            }
            if args.mode == "sequence":
                existing_workflow = datapoint["process_existing"]
                payload["currentWorkflow"] = yaml_dump(existing_workflow)
                payload["availableVariables"] = existing_workflow.get("arguments", []) + existing_workflow.get("variables", [])
                payload["availableAdditionalTypeDefinitions"] = ""

            print(index + 1, "/", len(examples), query)

            urls = urls_workflow_generation if args.mode == "workflow" else urls_sequence_generation
            start_time = time.time()
            response_json = {}
            try:
                response = requests.post(urls[deployment], json=payload, headers=headers)
                response_json = response.json()
                response.raise_for_status()
                response = response_json
            except Exception as e:
                print("ERROR calling service", e)
                print(json.dumps(response_json, indent=2))
                response = {}

            end_time = time.time()
            elapsed_time = end_time - start_time
            print("elapsed_time", elapsed_time)
            prompt_tokens = get_key_tokens(response, "prompt_tokens")
            print("prompt_tokens", prompt_tokens)
            completion_tokens = get_key_tokens(response, "completion_tokens")
            print("completion_tokens", completion_tokens)

            try:
                # workflow_key = "workflow_result" if args.mode == "workflow" else "sequence_result"
                workflow_key = "result"
                workflow_str = response[workflow_key]
                with open("client_eval.json", "w") as outfile:
                    json.dump(response, outfile, indent=2)

                workflow_dict = yaml_load(workflow_str)
                yaml_dump(workflow_dict, pathlib.Path("client_eval_workflow.yaml"), default_flow_style=False)
                clean_workflow = cleanup_rpa_wf_workflow_for_compare(workflow_dict)

                process_key = "process" if args.mode == "workflow" else "process_sequence"
                plan_key = "plan" if args.mode == "workflow" else "plan_sequence"

                # get ground truth
                if process_key in datapoint:  # datapoint is from testset
                    gt_workflow_yaml = copy.deepcopy(datapoint[process_key])
                    if args.mode == "sequence":
                        gt_workflow_yaml = {"workflow": gt_workflow_yaml}
                    gt_workflow = yaml_dump(gt_workflow_yaml)
                    clean_gt_workflow_yaml = cleanup_rpa_wf_workflow_for_compare(gt_workflow_yaml)

                # show results
                if args.verbose:
                    if "plan" in response:
                        print("-" * 10, "response plan")
                        print(response["plan"])
                        if plan_key in datapoint:
                            print("-" * 10, "labeled plan")
                            print(datapoint[plan_key])
                    print("-" * 10, "response workflow")
                    yaml_dump(clean_workflow, sys.stdout)
                    if process_key in datapoint:
                        print("-" * 10, "labeled workflow")
                        yaml_dump(clean_gt_workflow_yaml, sys.stdout)

                # compute scores
                plan_score = 0.0
                if plan_key in datapoint:
                    if "plan" in response:
                        plan_score = Levenshtein.ratio(response["plan"], datapoint[plan_key])
                        print("PLAN SCORE", plan_score)

                ted_score = 0.0
                pled_score = 0.0
                current_params_scores = []
                if process_key in datapoint:
                    use_typeid_in_activities(clean_gt_workflow_yaml)
                    use_typeid_in_activities(clean_workflow)

                    good, total = ActivityTreeAccuracyCalculator().tree_accuracy(clean_gt_workflow_yaml, clean_workflow)
                    mapping = []
                    try:
                        ted_score, _, _, mapping = core_wf_workflow_edit_score(clean_gt_workflow_yaml, clean_workflow)
                    except Exception:
                        print("ERROR computing TED or parameter score, using 0 value")
                    print("ACTIVITY TREE EDIT DISTANCE SCORE", ted_score)

                    try:
                        pled_score, _, _, mapping = core_wf_workflow_edit_score(clean_gt_workflow_yaml, clean_workflow, param_match="exact", filter_params=None)
                    except Exception:
                        print("ERROR computing PLED score, using 0 value")

                    print("TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE", pled_score)
                    current_params_scores = [m[3] for m in mapping if m[3] >= 0]
                    current_params_score = sum(current_params_scores) / len(current_params_scores) if current_params_scores else -1.0
                    print("LEVENSHTEIN PARAMS SCORE", current_params_score)
                    if mapping:
                        for m in mapping[1:]:
                            print(m)

                if plan_key not in datapoint or process_key not in datapoint:
                    print("CAN'T FIND EXAMPLE IN DATASET")

            except Exception as e:
                print("ERROR computing scores", e)
                traceback.print_exc()
                plan_score = 0.0
                ted_score = 0.0
                pled_score = 0.0
                current_params_scores = []

            metadata["query"][-1].append(query)
            metadata["path"][-1].append(path)

            metrics["plan_scores"][-1].append(plan_score)
            metrics["ted_scores"][-1].append(ted_score)
            metrics["pled_scores"][-1].append(pled_score)
            metrics["param_scores"][-1].extend(current_params_scores)
            metrics["elapsed_times"][-1].append(elapsed_time)
            metrics["prompt_tokens"][-1].append(prompt_tokens)
            metrics["completion_tokens"][-1].append(completion_tokens)

            with open(f"table_{deployment}_{args.mode}_{args.framework}_{args.subset}_{now_str}_run{iteration}.csv", "w") as fout:
                writer = csv.writer(fout)
                columns = list(metrics.keys()) + list(metadata.keys())
                writer.writerow(columns)
                for idx in range(len(metadata["query"][-1])):
                    row = []
                    for key in metrics:
                        row.append(metrics[key][-1][idx])
                    for key in metadata:
                        row.append(metadata[key][-1][idx])
                    writer.writerow(row)

            for key in metrics:
                with open(f"{key}_{deployment}_{args.mode}_{args.framework}_{args.subset}_{now_str}.csv", "w") as fout:
                    writer = csv.writer(fout)
                    for row in metrics[key]:
                        writer.writerow(row)

        for key in metrics:
            print("average: ", key, sum(metrics[key][-1]) / len(metrics[key][-1]))
