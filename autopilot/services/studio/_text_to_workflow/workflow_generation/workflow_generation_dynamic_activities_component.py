import copy
import json
import traceback

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.activity_utils import get_input_properties, get_output_type
from services.studio._text_to_workflow.common.constants import LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST, PRODUCTIVITY_ACTIVITY2CONNECTION
from services.studio._text_to_workflow.common.schema import (
    ActivityDefinition,
    Connection,
    TargetFramework,
)
from services.studio._text_to_workflow.common.typedefs_parser import parse_typedef
from services.studio._text_to_workflow.utils import (
    base64_utils,
    dotnet_dynamic_activities_discovery,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_generation.config import constants

LOGGER = AppInsightsLogger()


class WorkflowGenerationDynamicActivitiesComponent:
    def __init__(self, activities_retriever: ActivitiesRetriever):
        self.activities_retriever = activities_retriever

    @staticmethod
    async def augment_type_definitions_for_dynamic_activities(
        activities: dict[str, ActivityDefinition],
        triggers: dict[str, ActivityDefinition],
        connections_by_key: dict[str, Connection],
        force_is_package_name: str | None = None,
        force_is_package_version: str | None = None,
    ) -> tuple[dict[str, dict], dict[str, dict]]:
        activities_replacement_map: dict[str, ActivityDefinition] = {}
        activity_definitions: dict[str, ActivityDefinition] = {}
        indexable_connections = []

        is_package_name, is_package_version = WorkflowGenerationDynamicActivitiesComponent._index_activities(
            activity_definitions, activities, connections_by_key, indexable_connections, activities_replacement_map
        )
        is_package_name, is_package_version = WorkflowGenerationDynamicActivitiesComponent._index_activities(
            activity_definitions, triggers, connections_by_key, indexable_connections, activities_replacement_map, is_package_name, is_package_version
        )

        if force_is_package_name is not None and force_is_package_version is not None:
            is_package_name, is_package_version = force_is_package_name, force_is_package_version

        jit_types, new_activities_config_map, connections_json = {}, {}, None
        if is_package_name is not None and is_package_version is not None:
            connections_json = await dotnet_dynamic_activities_discovery.get_type_definitions(is_package_name, is_package_version, indexable_connections)
        if connections_json is not None:
            for connection in connections_json["Configurations"]:
                if connection is None:
                    LOGGER.warning("Connection is None")
                    continue
                if not connection["IsSuccess"]:
                    print("Connection " + connection["OldConfiguration"] + " failed.")
                    LOGGER.warning("Connection unsuccessful.")  # is connection sensitive?
                    continue
                if "OldConfiguration" not in connection:
                    LOGGER.warning("OldConfiguration not found in connection.")
                    continue
                old_config = connection["OldConfiguration"]
                activity_definition = activity_definitions[old_config]

                # TODO: Maybe we should also have a required only class definition too in the IS Generator CLI
                activity_definition["typeDefinition"] = connection["NewClassDefinition"]

                typedef = parse_typedef(activity_definition["typeDefinition"])
                activity_definition["className"] = typedef["name"]
                activity_definition["fullClassName"] = activity_definition["namespace"] + "." + typedef["name"]
                activity_definition["params"] = typedef["params"]
                activity_definition["genericParamDefinition"] = typedef["type_params"]

                if activity_definition["additionalTypeDefinitions"] and not activity_definition["additionalTypeDefinitions"].endswith("\r\n"):
                    activity_definition["additionalTypeDefinitions"] += "\r\n"
                activity_definition["additionalTypeDefinitions"] += connection["AdditionalTypesDefinitions"]
                if activity_definition["additionalNamespaces"] and not activity_definition["additionalNamespaces"].endswith("\r\n"):
                    activity_definition["additionalNamespaces"] += "\r\n"
                activity_definition["additionalNamespaces"] += connection["AdditionalTypesNamespaces"]
                activity_definition["activityConfiguration"] = connection["NewConfiguration"]
                activity_definition["activityIsConfigured"] = connection["OldConfiguration"] != connection["NewConfiguration"]

                if "DynamicActivityDetails" in connection:
                    activity_definition["dynamicActivityDetails"] = connection["DynamicActivityDetails"]
                else:
                    activity_definition["dynamicActivityDetails"] = constants.DYNAMIC_ACTIVITY_DETAILS_DEFAULT

                # TODO: refactor/refine this once DAP CLI supports JSON Schema
                if "JsonSchema" in connection:
                    activity_definition["jsonSchema"] = connection["JsonSchema"]
                    json_schema = json.loads(activity_definition["jsonSchema"])
                    activity_definition["inputPropertiesJsonSchema"] = get_input_properties(json_schema)
                    activity_definition["outputTypeJsonSchema"] = get_output_type(json_schema)
                    activity_definition["httpMethod"] = json_schema.get("method", None)

                # configuration should be set to:
                # the activity configuration in the embedding db, if the DAP CLI returns valid DynamicActivityDetails
                # the new configuration returned by the DAP CLI, if the DynamicActivityDetails is not present
                new_activities_config_map[activity_definition["fullClassName"]] = {
                    "activityTypeId": activity_definition["activityTypeId"],
                    "configuration": (
                        activity_definitions[old_config]["activityConfiguration"]
                        if activity_definition["dynamicActivityDetails"]
                        else activity_definition["activityConfiguration"]
                    ),
                    "actualActivityName": activity_definition["fullActivityName"],
                    "connectionId": activity_definition.get("connectionId", None),
                    "connectorKey": activity_definition["connectorKey"],
                    "activityIsConfigured": activity_definition["activityIsConfigured"],
                    "dynamicActivityDetails": activity_definition["dynamicActivityDetails"],
                    "jsonSchema": activity_definition["jsonSchema"],
                    "inputPropertiesJsonSchema": activity_definition["inputPropertiesJsonSchema"],
                    "outputTypeJsonSchema": activity_definition["outputTypeJsonSchema"],
                    "httpMethod": activity_definition["httpMethod"],
                }
                jit_types[activity_definition["fullClassName"]] = connection["JitCommands"]

        # Update the activities with the new type definitions
        for activity_or_trigger_name, new_activity_or_trigger in activities_replacement_map.items():
            if activity_or_trigger_name in activities:
                activities.pop(activity_or_trigger_name)
                activities[activity_or_trigger_name] = new_activity_or_trigger
            elif activity_or_trigger_name in triggers:
                triggers.pop(activity_or_trigger_name)
                triggers[activity_or_trigger_name] = new_activity_or_trigger

        # Activities with failed or missing connection
        for activity_or_trigger in list(activities.values()) + list(triggers.values()):
            if activity_or_trigger["activityConfiguration"] is None:
                continue

            if activity_or_trigger["fullClassName"] in new_activities_config_map:
                continue

            new_activities_config_map[activity_or_trigger["fullClassName"]] = {
                "activityTypeId": activity_or_trigger["activityTypeId"],
                "configuration": activity_or_trigger["activityConfiguration"],
                "actualActivityName": activity_or_trigger["fullActivityName"],
                "connectorKey": activity_or_trigger["connectorKey"],
                "connectionId": None,
                "activityIsConfigured": False,
                "dynamicActivityDetails": constants.DYNAMIC_ACTIVITY_DETAILS_DEFAULT,
            }

        # Dynamic activities (Integration Service) are treated as follows:
        # If a connection exists for the activity, the activity is replaced with the generic activity with the new configuration and the configured expressions
        # Otherwise, the activity is replaced with the generic connector activity, but setting the initial configuration only
        # The actual configuration is set on the activity in the validation stage, once we have built the actual workflow
        return jit_types, new_activities_config_map

    @staticmethod
    def _index_activities(
        activity_definitions: dict[str, ActivityDefinition],
        activities: dict[str, ActivityDefinition],
        connections_by_key: dict[str, Connection],
        indexable_connections: list[dict],
        activities_replacement_map: dict[str, ActivityDefinition],
        is_package_name: str | None = None,
        is_package_version: str | None = None,
    ):
        for activity_or_trigger in activities.values():
            if activity_or_trigger["connectorKey"] is None:
                continue
            if activity_or_trigger["fullClassName"] in PRODUCTIVITY_ACTIVITY2CONNECTION:
                continue
            if activity_or_trigger["packageName"] in LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST:
                continue
            # What is left are only DAP activities
            is_package_name = activity_or_trigger["packageName"]
            is_package_version = activity_or_trigger["packageVersion"]

            if activity_or_trigger["connectorKey"] in connections_by_key and activity_or_trigger["activityConfiguration"] is not None:
                existing_connector = connections_by_key[activity_or_trigger["connectorKey"]]
                indexable_connections.append(
                    {
                        "ConnectionId": existing_connector["connectionId"],
                        "Configuration": activity_or_trigger["activityConfiguration"],
                        "ClassDefinition": activity_or_trigger["typeDefinition"],
                        "ActivityFullName": activity_or_trigger["fullActivityName"],
                        "UiPathActivityTypeId": activity_or_trigger["activityTypeId"],
                    }
                )

                # As the activity has a connection, we will overwrite it with a duplicate so that we can update its type definition
                activity_or_trigger_duplicate = copy.deepcopy(activity_or_trigger)
                activities_replacement_map[activity_or_trigger["fullClassName"]] = activity_or_trigger_duplicate

                activity_definitions[activity_or_trigger["activityConfiguration"]] = activity_or_trigger_duplicate
                activity_or_trigger_duplicate["connectionId"] = existing_connector["connectionId"]

        return is_package_name, is_package_version

    @staticmethod
    def configure_dynamic_connected_activity(activities_config_map: dict[str, dict], node):
        activity_config = activities_config_map[node["activity"]]
        node["isDynamic"] = True
        node["isConfigured"] = activity_config["activityIsConfigured"]
        node["activity"] = activity_config["actualActivityName"]
        node["connectorKey"] = activity_config["connectorKey"]

        node["configuration"] = base64_utils.encode_string_base64(activity_config["configuration"])
        node["uiPathActivityTypeId"] = activity_config["activityTypeId"]
        node["dynamicActivityDetails"] = base64_utils.encode_string_base64(
            activity_config["dynamicActivityDetails"]
        )  # might encode the string NotConfigured, but it doesn't matter

    async def configure_dap_notretrieved(
        self,
        node: dict,
        activity_config: dict,
        jit_types: dict[str, dict],
        activity_name: str,
        connections_by_key: dict[str, Connection],
        target_framework: TargetFramework,
    ) -> list[ActivityDefinition]:
        additional_used_activities: list[ActivityDefinition] = []
        try:
            activity = self.activities_retriever.get(activity_name, target_framework, None)
            # TODO: This is a workaround to get the activity configuration for activities from the demo that are used in generation.
            # When the model context size increases, we should simply also fetch the definition for demo DAP activities from IS.
            if activity is not None:
                # If the activity is connected, then get the proper configuration, otherwise just set it to the default
                LOGGER.warning(f"DAP activity only available from demo used in generation {activity_name}")
                if activity["connectorKey"] in connections_by_key:
                    activities = {activity["fullClassName"]: activity}

                    # This is not ideal, but to save initial context size we patch up the Configuration here.
                    # Should be refactored once we have more context size (e.g. when switching to gpt4-turbo).
                    (
                        new_jit_types,
                        new_activities_config_map,
                    ) = await WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
                        activities,
                        {},
                        connections_by_key,
                    )

                    additional_used_activities.append(activity)

                    # Update the activity config map with the new activities
                    for key, value in new_activities_config_map.items():
                        activity_config[key] = value

                    # Update the JIT types with the new JIT types
                    for key, value in new_jit_types.items():
                        jit_types[key] = value
                    if node["activity"] in activity_config:
                        WorkflowGenerationDynamicActivitiesComponent.configure_dynamic_connected_activity(activity_config, node)
                else:
                    additional_used_activities.append(activity)
                    node["params"] = node.get("params", {})
                    node["isDynamic"] = True
                    node["isConfigured"] = False
                    node["activity"] = activity["fullActivityName"]
                    node["params"].clear()
                    node["configuration"] = base64_utils.encode_string_base64(activity["activityConfiguration"] or "")
                    node["uiPathActivityTypeId"] = activity["activityTypeId"]
                    node["connectorKey"] = activity["connectorKey"]
                    node["dynamicActivityDetails"] = constants.DYNAMIC_ACTIVITY_DETAILS_DEFAULT
                    if node["connectorKey"] in connections_by_key:
                        node["params"]["ConnectionId"] = connections_by_key[node["connectorKey"]]["connectionId"]
        except Exception:
            LOGGER.warning("IS retrieval failed for DAP activity only available from demo used in generation. {activity_name}")
            traceback.print_exc()

        return additional_used_activities
