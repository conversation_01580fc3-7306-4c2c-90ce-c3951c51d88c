import json

from services.studio._text_to_workflow.common.prompt_utils import validate_workflow_generation_prompt
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import errors, paths, request_utils, telemetry_utils
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    GenerateSequenceRequest,
    GenerateSequenceRequestPydantic,
    GenerateWorkflowRequest,
    GenerateWorkflowRequestPydantic,
    WorkflowGenerationParams,
    WorkflowGenerationResponse,
    WorkflowGenerationTaskResult,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_task import WorkflowGenerationTask

TASK: WorkflowGenerationTask = WorkflowGenerationTask("prompt.yaml")
LOGGER = telemetry_utils.AppInsightsLogger()


@telemetry_utils.log_execution_time("workflow_generation_endpoint.init")
def init() -> None:
    global TASK
    TASK.load()


@telemetry_utils.log_execution_time("workflow_generation_endpoint.generate_workflow")
async def generate_workflow(request_pydantic: GenerateWorkflowRequestPydantic) -> WorkflowGenerationResponse:
    request: GenerateWorkflowRequest = request_pydantic.model_dump()  # type: ignore
    _validate_generate_workflow_request(request)

    generation_params = _get_generation_params(request)

    result = await TASK.generate_workflow(
        request["userRequest"],
        request["connections"],
        request["targetFramework"],
        model_options=generation_params["model"],
        localization=generation_params["localization"],
    )
    output: WorkflowGenerationResponse = {
        "result": result["workflow_result"]["workflow_valid"],
        "jitCommands": result["workflow_result"]["used_jit_types"],
        "connectors": result["workflow_result"]["used_connectors"],
        "usage": {
            "retrieval": result["retrieval_usage"].to_json(),
            "generation": result["generation_usage"].to_json(),
        },
        "prompt_class": result.get("prompt_class", None),
        "prompt_score": result.get("prompt_score", None),
    }
    _log_extra_info(result, output)
    return output


@telemetry_utils.log_execution_time("workflow_generation_endpoint.generate_sequence")
async def generate_sequence(request_pydantic: GenerateSequenceRequestPydantic) -> WorkflowGenerationResponse:
    request: GenerateSequenceRequest = request_pydantic.model_dump()  # type: ignore
    _validate_generate_sequence_request(request)

    generation_params = _get_generation_params(request)

    result = await TASK.generate_sequence(
        request["userRequest"],
        request["currentWorkflow"],
        request["availableVariables"],
        request["availableAdditionalTypeDefinitions"],
        request["connections"],
        request["targetFramework"],
        request.get("objects", []),
        model_options=generation_params["model"],
        localization=generation_params["localization"],
        is_test_case=request.get("isTestCase", False),
    )

    output: WorkflowGenerationResponse = {
        "result": result["sequence_result"]["workflow_valid"],
        "jitCommands": result["sequence_result"]["used_jit_types"],
        "connectors": result["sequence_result"]["used_connectors"],
        "usage": {
            "retrieval": result["retrieval_usage"].to_json(),
            "generation": result["generation_usage"].to_json(),
        },
        "prompt_class": result.get("prompt_class", None),
        "prompt_score": result.get("prompt_score", None),
    }
    _log_extra_info(result, output)
    return output


def _validate_generate_workflow_request(request: GenerateWorkflowRequest):
    for key in ["userRequest", "connections", "targetFramework"]:
        if key not in request:
            raise errors.BadRequestError(f"Field {key} is missing from the request.")
    validate_workflow_generation_prompt(request["userRequest"])
    if not _validate_target_framework(request["targetFramework"]):
        raise errors.BadRequestError("Invalid target framework.")
    request["connections"] = request.get("connections", []) or []


def _validate_generate_sequence_request(request: GenerateSequenceRequest):
    _validate_generate_workflow_request(request)
    for key in ("availableVariables", "availableAdditionalTypeDefinitions", "currentWorkflow"):
        if key not in request:
            raise errors.BadRequestError(f"Field {key} is missing from the request.")
    # mandatory fields should not be null
    request["availableVariables"] = request["availableVariables"] or []
    request["availableAdditionalTypeDefinitions"] = request["availableAdditionalTypeDefinitions"] or ""
    request["currentWorkflow"] = request["currentWorkflow"] or "workflow: []"
    # non-mandatory fields
    request["objects"] = request.get("objects", []) or []
    # Handle StudioX not being able to serialize the workflow properly
    if request["currentWorkflow"].startswith("{}"):
        request["currentWorkflow"] = "workflow: []"


def _get_generation_params(request: GenerateWorkflowRequest) -> WorkflowGenerationParams:
    request_context = request_utils.get_request_context()
    model = request.get("model", None)
    if model is not None and isinstance(model, dict):
        for _, value in model.items():
            if isinstance(value, dict) and "seed" not in value:
                value["seed"] = request_utils.get_seed(request)

    localization = getattr(request_context, "localization", None)
    return {
        "localization": localization,
        "model": model,
    }


def _validate_target_framework(target_framework: str) -> bool:
    return target_framework in ("Portable", "Windows")


def _log_extra_info(result: WorkflowGenerationTaskResult, output):
    if not settings.IS_PROD:
        logs_path = paths.get_logs_path()
        logs_path.mkdir(parents=True, exist_ok=True)
        if "plan" in result:
            print("Plan:", result["plan"].encode("unicode_escape").decode("utf-8"))
            with open(logs_path / ".plan.txt", "w", encoding="utf-8") as f:
                f.write(result["plan"])
        with open(logs_path / ".raw.txt", "w", encoding="utf-8") as f:
            f.write(result["workflow_result"]["workflow_raw"])
        with open(logs_path / ".valid.txt", "w", encoding="utf-8") as f:
            f.write(result["workflow_result"]["workflow_valid"])
        if "generation_prompt" in result:
            with open(logs_path / ".prompt.txt", "w", encoding="utf-8") as f:
                f.write(result["generation_prompt"])
        with open(logs_path / "response.json", "w", encoding="utf-8") as f:
            json.dump(output, f, indent=2)

        print("Raw:", result["workflow_result"]["workflow_raw"].encode("unicode_escape").decode("utf-8"))
        print("Valid:", result["workflow_result"]["workflow_valid"].encode("unicode_escape").decode("utf-8"))

        if "plan" in result:
            output["plan"] = result["plan"]
        output["duration"] = result["duration"]

        if "prompt_class" in result:
            output["prompt_class"] = result["prompt_class"]
            output["prompt_score"] = result["prompt_score"]

        if "retrieved_triggers" in result:
            output["retrieved_triggers"] = [activity["fullClassName"] for activity in result["retrieved_triggers"]]
        if "retrieved_activities" in result:
            output["retrieved_activities"] = [activity["fullClassName"] for activity in result["retrieved_activities"]]

        if "retrieved_triggers_after_pruning" in result:
            output["retrieved_triggers_after_pruning"] = [activity["fullClassName"] for activity in result["retrieved_triggers_after_pruning"]]

        if "retrieved_activities_after_pruning" in result:
            output["retrieved_activities_after_pruning"] = [activity["fullClassName"] for activity in result["retrieved_activities_after_pruning"]]

        if "demonstrations" in result:
            output["demonstrations"] = [
                {
                    "name": demo["name"],
                    "description": demo["description"],
                    "plan": demo["plan"],
                    "retrieved_activities": demo["retrieved_activities"],
                    "retrieved_triggers": demo.get("retrieved_triggers", []),
                }
                for demonstrations in result["demonstrations"].values()
                for demo in demonstrations
            ]
