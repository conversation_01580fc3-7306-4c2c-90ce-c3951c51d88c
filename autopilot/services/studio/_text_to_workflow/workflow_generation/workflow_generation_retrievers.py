import copy
import itertools
import os
import pathlib
import pathlib as pt
import pickle
from abc import abstractmethod
from pathlib import Path

import numpy as np
import tqdm

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ActivityType, SubsetName, TargetFramework
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import embedding_model, paths, telemetry_utils
from services.studio._text_to_workflow.utils.paths import WF_GEN_V2_DATASET_NAME
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset, workflow_generation_helpers
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score, planning_edit_score

LOGGER = telemetry_utils.AppInsightsLogger()


def get_demonstration_description(demo: dict) -> str | None:
    current_demo = copy.deepcopy(demo)
    if "query" not in current_demo.keys() and "description" not in current_demo.keys():
        current_demo = current_demo.get("input", {})
    return current_demo.get("query", current_demo.get("description", None))


# mmr: https://www.cs.cmu.edu/~jgc/publication/The_Use_MMR_Diversity_Based_LTMIR_1998.pdf
def mmr(scores, similarities, k=5, n=30, diversity=0.2):
    sorted_indices = np.argsort(-scores)
    # sorted_scores = scores[sorted_indices]
    # print(f"{sorted_indices=}")
    # print(f"{sorted_scores=}")

    candidate_indices = sorted_indices[:n]
    selected_indices, selected_scores = [candidate_indices[0]], [scores[candidate_indices[0]] + diversity]
    # print(f"{selected_indices=}")
    # print(f"{selected_scores=}")

    while len(selected_indices) < k:
        # print("=" * 10)
        remaining_indices = np.array(list(set(candidate_indices) - set(selected_indices)))
        if len(remaining_indices) == 0:
            break
        # print(f"{remaining_indices=}")
        # remaining_similarities = similarities[selected_indices, :][:, remaining_indices]
        remaining_similarities = similarities[np.ix_(selected_indices, remaining_indices)]
        # print(f"{similarities=}")
        # print(f"{similarities2=}")
        mmr_scores = scores[remaining_indices] + diversity * (1 - np.max(remaining_similarities, axis=0))
        # print(f"{mmr_scores=}")
        next_selected = remaining_indices[_max_index := np.argmax(mmr_scores)]
        next_score = mmr_scores[_max_index]
        selected_indices.append(next_selected)
        selected_scores.append(next_score)
        # print(f"{selected_indices=}")
        # print(f"{selected_scores=}")
    return selected_indices, selected_scores


class DemonstrationsRetrieverBase:
    config_path: pt.Path
    config: dict
    dataset_path: pt.Path
    retriever_path: pt.Path
    index: dict[tuple[TargetFramework, SubsetName] | str, "DemonstrationsIndexBase"]

    def __init__(self, config_name: str, dataset_path: pt.Path, retriever_path: pt.Path) -> None:
        self.dataset_path = dataset_path
        self.retriever_path = retriever_path
        self.config_path = workflow_generation_helpers.get_config_path(config_name)
        self.config = yaml_load(self.config_path)

        if self.config["demonstration_ranking"]["reranking_enable"]:
            self.rerank_model = ModelManager().get_reranking_model()

    def apply_reranking_and_mmr_transformations_on_demonstrations_similarities(
        self,
        config: dict,
        index: "DemonstrationsIndexBase",
        query: str,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        ignored_namespaces: set[str],
        ignored_identifiers: set[str],
        ignored_activities: set[str],
        verbose: bool,
    ) -> list[dict]:
        demonstrations = index.search_batch(
            query_embedding,
            connections_embedding,
            ignored_namespaces,
            ignored_identifiers,
            ignored_activities,
            k=config["embedding_retrieval_docs"],
            query=query,
        )
        for d in demonstrations:
            d["similarity"] = d["embedding_score"]
        if verbose:
            print("-" * 10, "embedding")
            for td in demonstrations:
                print(td["index"], td["name"], "embedding_score", td["embedding_score"])
        if config["reranking_enable"]:
            texts = [get_demonstration_description(d) for d in demonstrations]
            indices, rerank_scores = self.rerank_model.predict(query, texts)
            for d, score in zip(demonstrations, rerank_scores, strict=False):
                d["rerank_score"] = score
                d["similarity"] = score
            demonstrations = [demonstrations[i] for i in indices[: config["reranking_docs"]]]
            if verbose:
                print("-" * 10, "rerank")
                for td in demonstrations:
                    print(td["index"], td["name"], "embedding_score", td["embedding_score"], "rerank_score", td["rerank_score"])

        if config["mmr_enable"]:
            indices = [td["index"] for td in demonstrations]
            scores = np.array([td["similarity"] for td in demonstrations])
            similarities = index.store.state["similarities"][np.ix_(indices, indices)]
            selected_indices, selected_scores = mmr(scores, similarities, k=config["mmr_docs"], n=len(demonstrations), diversity=config["mmr_diversity"])

            mmr_demonstrations = []
            for si, sc in zip(selected_indices, selected_scores, strict=False):
                td = demonstrations[si]
                td["mmr_score"] = sc
                td["similarity"] = sc
                mmr_demonstrations.append(td)
            demonstrations = mmr_demonstrations
            if verbose:
                print("-" * 10, "mmr")
                for td in demonstrations:
                    print(td["index"], td["name"], "embedding_score", td["embedding_score"], "rerank_score", td.get("rerank_score", 0), "mmr", td["similarity"])
        return demonstrations


class DemonstrationsRetriever(DemonstrationsRetrieverBase):
    config_path: pt.Path
    config: dict
    dataset_path: pt.Path
    retriever_path: pt.Path

    def __init__(self, config_name: str, dataset_path: pt.Path, retriever_path: pt.Path, activities_retriever: ActivitiesRetriever) -> None:
        super().__init__(config_name, dataset_path, retriever_path)
        # uia and static demonstrations are always on the Portable index
        # windows is a superset of portable, so the windows train index includes both Portable and Windows
        self.index = {
            ("Portable", "uia"): DemonstrationsIndex(
                self.config,
                "Portable",
                ["uia"],
                self.dataset_path,
                self.retriever_path,
                activities_retriever,
                force_entire_sequence=True,
            ),
            ("Portable", "static"): DemonstrationsIndex(
                self.config,
                "Portable",
                ["static"],
                self.dataset_path,
                self.retriever_path,
                activities_retriever,
                force_entire_sequence=True,
            ),
            ("Portable", "train"): DemonstrationsIndex(
                self.config,
                "Portable",
                # ["train"],
                ["train", "test"],
                self.dataset_path,
                self.retriever_path,
                activities_retriever,
            ),
            ("Windows", "train"): DemonstrationsIndex(
                self.config,
                "Windows",
                # ["train"],
                ["train", "test"],
                self.dataset_path,
                self.retriever_path,
                activities_retriever,
            ),
            ("All", "train"): DemonstrationsIndex(
                self.config,
                "Windows",
                ["train", "test"],
                # ["train", "test", "prod-filtered"],
                self.dataset_path,
                self.retriever_path,
                activities_retriever,
                force_all_frameworks=True,
            ),
        }
        if dataset_path.name == "SequenceGeneration" or dataset_path.name == WF_GEN_V2_DATASET_NAME:
            self.index[("Portable", "testcase")] = DemonstrationsIndex(
                self.config, "Portable", ["testcase"], self.dataset_path, self.retriever_path, activities_retriever, True
            )

    def get_relevant(
        self,
        query,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        demonstrations_count: dict[SubsetName, int],
        target_framework: TargetFramework,
        ignored_namespaces: set[str],
        ignored_identifiers: set[str],
        ignored_activities: set[str],
        verbose: bool = False,
    ) -> dict[str, list[dict]]:
        """Retrieve demonstrations by each subset, in decreasing order of similarity with the query."""
        # non-train demonstrations are always on the Portable index
        # demonstrations_count = {'train': 5, 'static': 0, 'uia': 0, 'testcase': 0}
        config = self.config["demonstration_ranking"]
        demonstrations = {
            key: self.index[("Portable", key)].search_batch(
                query_embedding,
                connections_embedding,
                ignored_namespaces,
                ignored_identifiers,
                ignored_activities,
                k=cnt,
                query=query,
            )
            for key, cnt in demonstrations_count.items()
            if key != "train"
        }
        for dvalues in demonstrations.values():
            for d in dvalues:
                d["similarity"] = d["embedding_score"]

        # train demonstrations are dependend on the target_framework
        demonstrations_target_framework = target_framework if target_framework != "Windows" else "All"
        index = self.index[(demonstrations_target_framework, "train")]
        train_demonstrations = self.apply_reranking_and_mmr_transformations_on_demonstrations_similarities(
            config, index, query, query_embedding, connections_embedding, ignored_namespaces, ignored_identifiers, ignored_activities, verbose
        )

        # this is already too tied to the specific implementation: we retrieve for planning and then cut for generation; let's make it explicit
        if train_demonstrations is not None:
            train_demonstrations = train_demonstrations[: config["planning_demonstrations_limit"]]
        else:
            train_demonstrations = []

        if target_framework == "Windows":
            # force at least 2 windows train demonstrations
            # the implementation assumes that ordering of train_demonstrations is best first
            non_windows_indices = [i for i, d in enumerate(train_demonstrations) if d["target_framework"] != "Windows"]
            existing_windows_identifiers = {d["identifier"] for d in train_demonstrations if d["target_framework"] == "Windows"}
            if len(existing_windows_identifiers) < config["force_windows_demonstrations"]:
                n_to_add = 2 - len(existing_windows_identifiers)
                windows_forced_demonstrations = self.index[("Windows", "train")].search_batch(
                    query_embedding,
                    connections_embedding,
                    ignored_namespaces,
                    ignored_identifiers | existing_windows_identifiers,
                    ignored_activities,
                    k=n_to_add,
                    query=query,
                )
                assert len(non_windows_indices) >= n_to_add, f"Expected at least {n_to_add} non-windows demonstrations, got {len(non_windows_indices)}"
                assert len(windows_forced_demonstrations) == n_to_add, f"Expected {n_to_add} windows demonstrations, got {len(windows_forced_demonstrations)}"
                for idx in non_windows_indices[-n_to_add:]:
                    demo = windows_forced_demonstrations.pop(0)
                    demo["similarity"] = demo["embedding_score"]
                    train_demonstrations[idx] = demo

        demonstrations = demonstrations | {"train": train_demonstrations}  # type: ignore

        # ensure ordering
        demonstrations = {
            subset: sorted(demos, key=lambda demo: demo["similarity"], reverse=config["sorting_order"] == "descending")
            for subset, demos in demonstrations.items()
        }

        return demonstrations


class DemonstrationsIndexBase(StateBuilder):
    config: dict
    identifier: str
    dataset_path: pt.Path
    retriever_path: pt.Path
    model: embedding_model.EmbeddingModel
    similarities_path: pt.Path
    identifier: str

    def __init__(self, config: dict, identifier: str, dataset_path: pt.Path, retriever_path: pt.Path, state_path: pt.Path) -> None:
        self.config = config
        # identifier is the name to identify the index (ex: "Portable.static" or "SmallComplexity")
        self.identifier = identifier
        self.dataset_path = dataset_path
        self.retriever_path = retriever_path
        self.model = ModelManager().get_embeddings_model("activities_embedding_model")
        mmr_similarities = config["demonstration_ranking"]["mmr_similarities"]
        self.similarities_path = retriever_path / f"demo_similarities.{mmr_similarities}.{self.identifier}.pkl"
        self.rerank_model = ModelManager().get_reranking_model()
        self.store = StateStore(state_path.as_posix(), self, lazy_load=settings.DEBUG_MODE)

    @abstractmethod
    def get_item_description(self, item, filepath):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_used_activities_from_item(self, item):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def activity_exists_in_retriever(self, activity_name: str, activity_type: ActivityType) -> tuple[bool, bool]:
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_by_index(self, index: int) -> dict:
        raise NotImplementedError("Subclasses must implement this method")

    def get_by_filepath(self, filepath: pt.Path) -> dict:
        return yaml_load(filepath)

    @abstractmethod
    def build(self):
        raise NotImplementedError("Subclasses must implement this method")

    def _get_relevant_activities(self, used_activities: list[str]) -> list[str]:
        return used_activities

    def _process_items(
        self,
        items,
        texts,
        filepath2index,
        index2filepath,
        workflows,
        plans,
        retrieved_activity_names,
        subset_path,
    ):
        """Process items from a subset path, filtering and extracting relevant information."""
        if settings.DEBUG_MODE:
            items = tqdm.tqdm(items, desc="/".join(subset_path.parts[-2:]), dynamic_ncols=True)
        dropped, missing = set(), set()

        for filepath, item in items:
            # Apply the skip condition early
            if self.should_skip_item(filepath, item):
                continue

            all_exist = True
            used_activities = self.get_used_activities_from_item(item)
            relevant_activities = self._get_relevant_activities(used_activities)
            for trigger_or_activity in relevant_activities:
                trigger_exists = self.activity_exists_in_retriever(trigger_or_activity, "trigger")
                activity_exists = self.activity_exists_in_retriever(trigger_or_activity, "activity")
                if not any(trigger_exists + activity_exists):
                    missing.add(trigger_or_activity)
                    all_exist = False
            if not all_exist:
                dropped.add(filepath)
                continue
            index = len(texts)

            query = self.get_item_description(item, filepath)
            if not query:
                print(f"WARNING! Missing description in {filepath}")
                continue
            texts.append(query)
            filepath2index[filepath] = index
            index2filepath[index] = filepath
            plans.append(item.get("plan", {}))
            workflows.append(item.get("process", item.get("solution_workflow", {})))
            retrieved_activity_names.append(used_activities)

        if missing:
            tqdm.tqdm.write(f"WARNING! Missing {len(missing)} triggers or activities.")
            for act in sorted(missing):
                tqdm.tqdm.write(act)
        if dropped:
            tqdm.tqdm.write(f"WARNING! Dropped {len(dropped)} workflows in {subset_path}")
            for d in sorted(dropped):
                tqdm.tqdm.write(d.as_posix())

    @abstractmethod
    def gather_activities(self, workflow, strip_templates=False):
        raise NotImplementedError("Subclasses must implement this method")

    def build_state(
        self,
        texts: list[str],
        workflows: list[dict],
        plans: list[dict],
        retrieved_activity_names: list[list[str]],
        filepath2index: dict[Path, int],
        index2filepath: dict[int, Path],
    ):
        embeddings = self.model.encode_batch(texts, batch_size=256, instruction_set="icl", instruction_type="key")

        # TO DO: this should be refactored, we should split this in different classes that all inherit a common base
        # compute/load workflow demonstration similarities
        similarities = self.compute_similarities(embeddings, texts, workflows, plans, retrieved_activity_names)

        tqdm.tqdm.write(f"Built index from with {len(embeddings)} items from:")
        tqdm.tqdm.write(f"{self.dataset_path.as_posix()}")

        # if an index is completely empty(usually happens if a core activity is missing from embeddings db) raise an exception
        # raising this exception here should cancel the entire build process
        # an empty index will cause subsequent errors in workflow generation when trying to get demonstrations
        if embeddings.size == 0:
            tqdm.tqdm.write(f"Error: index {self.identifier} is empty")
            raise Exception(f"Error: index {self.identifier} is empty")

        state = {
            "similarities": similarities,
            "embeddings": embeddings,
            "filepath2index": filepath2index,
            "index2filepath": index2filepath,
        }

        state_info = {}
        state_info["filepath2index"] = {k.as_posix(): v for k, v in state["filepath2index"].items()}
        state_info["index2filepath"] = {k: v.as_posix() for k, v in state["index2filepath"].items()}

        return state, state_info

    def compute_similarities(self, embeddings, texts, workflows, plans, retrieved_activity_names) -> np.ndarray:
        # TO DO: this should be refactored, we should split this in different classes that all inherit a common base
        # compute/load workflow demonstration similarities
        similarities = []
        if self.config["demonstration_ranking"]["mmr_similarities_action"] == "build":
            comparison_mode = self.config["demonstration_ranking"]["mmr_similarities"]
            if comparison_mode == "embeddings":
                similarities = embeddings @ embeddings.T  # v1 based on embeddings
            elif comparison_mode == "reranking":
                similarities = []  # v2 based on cross encoder
                for text1 in tqdm.tqdm(texts, "similarities reranking"):
                    _, scores = self.rerank_model.predict(text1, texts)
                    scores = scores - np.min(scores)
                    scores /= np.max(scores)
                    similarities.append(scores)
                similarities = np.stack(similarities)
            elif comparison_mode == "pled":
                similarities = []
                for workflow in tqdm.tqdm(workflows, "similarities pled"):
                    current_similarities = []
                    for workflow2 in workflows:
                        score, _, _, _ = core_wf_workflow_edit_score(workflow, workflow2, param_match="levenshtein", filter_params=None)
                        current_similarities.append(score)
                    similarities.append(current_similarities)
                similarities = np.stack(similarities)
            elif comparison_mode == "plan_tree_edit_distance":
                similarities = []  # v4 based on plan tree edit score with cross encoder
                for plan1 in tqdm.tqdm(plans, "similarities plan_tree_edit_score"):
                    current_similarities = []
                    for plan2 in plans:
                        # cross_encoder_model is None by default in planning_edit_score, so use levenstein for comparison
                        plan_tree_edit_score = planning_edit_score(plan1, plan2)
                        current_similarities.append(plan_tree_edit_score)
                    similarities.append(current_similarities)
                similarities = np.stack(similarities)
            elif comparison_mode in ["iou_activities", "iou_retrieved_activities"]:
                # not all indexes support gathering iou_activities
                if comparison_mode == "iou_activities":
                    worflow_activities = [self.gather_activities(workflow) for workflow in tqdm.tqdm(workflows, "gather iou activities")]
                else:
                    worflow_activities = retrieved_activity_names

                similarities = np.zeros((len(worflow_activities), len(worflow_activities)))
                for i, activities1 in enumerate(tqdm.tqdm(worflow_activities, "similarities iou activities")):
                    for j in range(i, len(worflow_activities)):
                        activities2 = worflow_activities[j]
                        score = len(set(activities1) & set(activities2)) / len(set(activities1) | set(activities2))
                        similarities[i, j] = score
                        similarities[j, i] = score
            else:
                raise Exception("unknown mmr_similarities value in config.yaml")

            if not self.similarities_path.parent.exists():
                os.makedirs(self.similarities_path.parent.absolute())

            with open(self.similarities_path, "wb") as f:
                pickle.dump(similarities, f, protocol=pickle.HIGHEST_PROTOCOL)
        elif self.config["demonstration_ranking"]["mmr_similarities_action"] == "load":
            with open(self.similarities_path, "rb") as f:
                similarities = pickle.load(f)
        return similarities

    @abstractmethod
    def get_identifier(self, path: pathlib.Path) -> str:
        raise NotImplementedError("Subclasses must implement this method")

    def search_batch(
        self,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        ignored_namespaces: set[str],
        ignored_identifiers: set[str],
        ignored_activities: set[str],
        k: int,
        query: str | None = None,
    ) -> list[dict]:
        """Retrieves relevant demonstrations in decreasing order of similarity with the query_embedding"""
        if k == 0:
            return []

        embeddings = self.store.state["embeddings"]
        weights = np.array([0.5, 0.5])  # TODO: recheck these
        query_embedding_arr = np.array([query_embedding, connections_embedding]).transpose()
        raw_scores = embeddings @ query_embedding_arr
        scores = raw_scores.dot(weights)
        sorted_indices = np.argsort(-scores)
        sorted_scores = scores[sorted_indices]

        demonstrations_identifiers = set()
        demonstrations = []
        for i, s in zip(sorted_indices.tolist(), sorted_scores.tolist(), strict=False):
            if len(demonstrations) >= k and k >= 0:
                break
            demonstration = self.get_by_index(i)
            filepath = pt.Path(self.store.state["index2filepath"][i])
            identifier = self.get_identifier(filepath)
            if identifier in demonstrations_identifiers:
                continue  # for sequence generation, to avoid having multiple sequences from the same workflow
            if self._contains_ignored_namespaces(demonstration, ignored_namespaces) or self._contains_ignored_activities(demonstration, ignored_activities):
                continue
            if identifier in ignored_identifiers:
                continue  # usually for "leave one out"

            assert demonstration is not None
            if filepath.stem.startswith("00__"):  # this means we are dealing with an entire workflow sequence
                demonstration["description_sequence"] = get_demonstration_description(
                    demonstration
                )  # we could bake this in the dataset creation and avoid setting it here

            demonstration["embedding_score"] = s
            demonstration["embedding_raw_scores"] = raw_scores[i]
            demonstration["index"] = i
            demonstration["identifier"] = identifier

            demonstrations_identifiers.add(identifier)
            demonstrations.append(demonstration)
        return demonstrations

    @abstractmethod
    def get_activity_info(self, activity_id: str, activity_type: ActivityType) -> ActivityDefinition | None:
        raise NotImplementedError("Subclasses must implement this method")

    # logic  that allows indexes to skip items
    def should_skip_item(self, filepath, item) -> bool:
        return False

    def _contains_ignored_namespaces(self, demonstration: dict, ignored_namespaces: set[str]) -> bool:
        if "process" not in demonstration:
            return False

        workflow = Workflow(demonstration.get("description", ""), demonstration.get("plan", ""), demonstration["process"])
        workflow_activities = ActivitiesAndTriggersCollector().collect(workflow)
        for activity_type, activities in workflow_activities.items():
            for activity in activities:
                activity_info = self.get_activity_info(activity.activity_id, activity_type)
                if activity_info is None:
                    LOGGER.warning(f"Could not find activity info for: {activity.activity_id}")
                    continue
                if activity_info["namespace"] in ignored_namespaces:
                    return True
        return False

    def _contains_ignored_activities(self, demonstration: dict, ignored_activities: set[str]) -> bool:
        if "process" not in demonstration:
            return False

        for used_activity in demonstration.get("used_triggers", demonstration.get("retrieved_triggers", [])):
            if used_activity in ignored_activities:
                return True

        for used_activity in demonstration.get("used_activities", demonstration.get("retrieved_activities", [])):
            if used_activity in ignored_activities:
                return True
        return False


class DemonstrationsIndex(DemonstrationsIndexBase):
    target_framework: TargetFramework
    subsets: list[SubsetName]
    subsets_path: list[pt.Path]
    force_entire_sequence: bool
    force_all_frameworks: bool
    activities_retriever: ActivitiesRetriever

    def __init__(
        self,
        config: dict,
        target_framework: TargetFramework,
        subsets: list[SubsetName],
        dataset_path: pt.Path,
        retriever_path: pt.Path,
        activities_retriever: ActivitiesRetriever,
        force_entire_sequence: bool = False,
        force_all_frameworks: bool = False,
    ) -> None:
        self.target_framework = target_framework
        self.subsets = subsets
        self.identifier = f"{target_framework}.{'-'.join(sorted(subsets))}" + (".all" if force_all_frameworks else "")
        target_framework_dirs = ["Portable", "Windows"] if force_all_frameworks else [target_framework]
        self.subset_name = dataset_path.name
        self.subsets_path = [dataset_path / target_framework_dir / subset for target_framework_dir, subset in itertools.product(target_framework_dirs, subsets)]
        state_path = retriever_path / f"state.{self.identifier}.pkl"
        self.force_entire_sequence = force_entire_sequence
        self.activities_retriever = activities_retriever
        super().__init__(config, self.identifier, dataset_path, retriever_path, state_path)

    def should_skip_item(self, filepath, item) -> bool:
        return "description_sequence" in item and self.force_entire_sequence and not filepath.name.startswith("00__")

    def get_used_activities_from_item(self, item):
        return (
            item.get("used_triggers", []) + item.get("used_activities", [])
            if self.subset_name == WF_GEN_V2_DATASET_NAME
            else item.get("retrieved_triggers", []) + item.get("retrieved_activities", [])
        )

    def get_item_description(self, item, filepath):
        query = get_demonstration_description(item)
        if "description_sequence" in item:
            if not filepath.name.startswith("00__"):
                query = item["description_sequence"]
        return query

    def gather_activities(self, workflow, strip_templates=False):
        activities = []
        if isinstance(workflow, dict):
            if "activity" in workflow:
                activity = workflow["activity"]
                if strip_templates:
                    activity = activity.split("<", 1)[0]
                if activity in (
                    "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity",
                    "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity",
                ):
                    activity_type_id = None
                    # Some triggers/activities do not have params.
                    workflow["params"] = workflow.get("params", {})
                    if "UiPathActivityTypeId" in workflow["params"]:
                        activity_type_id = workflow["params"]["UiPathActivityTypeId"]
                    elif "uiPathActivityTypeId" in workflow["params"]:
                        activity_type_id = workflow["params"]["uiPathActivityTypeId"]
                    elif "uiPathActivityTypeId" in workflow:
                        activity_type_id = workflow["uiPathActivityTypeId"]
                    else:
                        raise ValueError(f"UiPathActivityTypeId not found in {workflow}")
                    activity = activity + "@" + activity_type_id
                # don't include "ManualTrigger" activities
                if "ManualTrigger" not in activity:
                    activities.append(activity)
            for _, v in workflow.items():
                activities.extend(self.gather_activities(v, strip_templates))
        elif isinstance(workflow, list):
            for v in workflow:
                activities.extend(self.gather_activities(v, strip_templates))
        return activities

    def get_by_index(self, index: int) -> dict:
        filepath = self.store.state["index2filepath"][index]
        return yaml_load(filepath)

    def get_identifier(self, path: pathlib.Path) -> str:
        task, target_framework, _split, name, *_ = path.relative_to(paths.get_autopilot_samples_dataset_path()).parts
        name = name.removesuffix(".yaml")  # for workflow generation
        return f"{target_framework}/{name}"

    def build(self):
        texts, filepath2index, index2filepath = [], {}, {}
        workflows, plans = [], []
        retrieved_activity_names: list[list[str]] = []

        for subset_path in self.subsets_path:
            items = workflow_generation_dataset.load_subset(subset_path).items()
            if not len(items):
                if self.target_framework == "Windows" and "prod-filtered" in subset_path.parts:
                    continue  # this is for mitigating a hack we have (injecting portable subsets)
                raise ValueError(f"No items found in subset {subset_path}.")

            self._process_items(items, texts, filepath2index, index2filepath, workflows, plans, retrieved_activity_names, subset_path)
        return self.build_state(texts, workflows, plans, retrieved_activity_names, filepath2index, index2filepath)

    def activity_exists_in_retriever(self, activity_name: str, activity_type: ActivityType) -> tuple[bool, bool]:
        return self.activities_retriever.activity_exists(activity_name, self.target_framework, activity_type)

    def get_activity_info(self, activity_id: str, activity_type: ActivityType) -> ActivityDefinition | None:
        return self.activities_retriever.get_activity_info(activity_id, self.target_framework, activity_type)
