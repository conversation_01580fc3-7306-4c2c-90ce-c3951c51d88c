import argparse
import csv
import os

from services.studio._text_to_workflow.predict_expression import predict_expression_task
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load


def read_activity_param_counts(csv_path):
    """
    Reads activity_param_request_counts.csv file and returns a dictionary mapping
    activity and parameter names to their request counts.

    Returns:
    dict: {(activity_name, param_name): count}
    """
    counts = {}
    with open(csv_path, "r", encoding="utf-8-sig") as f:
        reader = csv.DictReader(f, delimiter=";")
        # Strip BOM from fieldnames if present
        # reader.fieldnames = [name.strip('\ufeff') for name in reader.fieldnames]
        for row in reader:
            activity_id = row["ActivityType"].split("`", 1)[0]
            param_name = row["Query"].split("(", 1)[0]
            count = int(row["count_"])
            counts[(activity_id, param_name)] = count
    return counts


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--min-request-counts", default=100, type=int, help="Minimum number of requests required for a parameter to be included in evaluation")
    ap.add_argument("--outfile", default="whitelist.yaml", type=str, help="Path to output YAML file where the whitelist of top queries will be saved")

    args, _ = ap.parse_known_args()

    dataset_path = paths.get_predict_expression_dataset_path()
    predict_expression_task.init()
    demo_index = predict_expression_task.DEMO_INDEX

    # query used to generate the csv file
    # customEvents
    # | where name contains "Biz.Ai" and timestamp > ago(28d)
    # | extend Type = tostring(customDimensions["type"])
    # | extend Step = tostring(customDimensions["step"])
    # | extend Query = tostring(customDimensions["query"])
    # | extend ActivityType = tostring(customDimensions["activityType"])
    # | where Type contains "configure"
    # | where Step == "displayed"
    # | summarize count() by ActivityType, Query

    if os.path.exists("activity_param_request_counts28days.csv"):
        activity_mapping = yaml_load(dataset_path / "activity_mapping.yaml")
        queries_raw = read_activity_param_counts("activity_param_request_counts28days.csv")
        top_queries_raw = {k: v for k, v in queries_raw.items() if v > args.min_request_counts}
        top_queries = {}
        for (activity, param_name), count in top_queries_raw.items():
            if activity == "System.Activities.Statements.Assign":
                activity = "UiPath.Core.Activities.Assign"
            activity_short = activity_mapping["activity_id2short"].get(activity, "")
            demos = demo_index.get_demos(activity_short, param_name, "unknown", [], {})
            if len(demos) <= 1:
                print("Not enough demos for", activity, param_name, len(demos))
                continue

            top_queries.setdefault(activity_short, {})
            top_queries[activity_short][param_name] = count
        with open(args.outfile, "w") as fout:
            yaml_dump(top_queries, fout)
