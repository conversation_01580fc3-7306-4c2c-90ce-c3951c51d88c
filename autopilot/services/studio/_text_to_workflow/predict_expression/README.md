## Build dataset

`python3 predict_expression_dataset.py`

the dataset will be built in `/workspace/data/Autopilot.Samples/Dataset/PredictExpression/`


## Build whitelist.yaml (optional)

`python3 build_top_queries_yaml.py --min-request-counts=100`


## Run evaluation

`GOOGLE_CLOUD_PROJECT="devtest-autopilot" GOOGLE_APPLICATION_CREDENTIALS="/workspace/src/autopilot/services/studio/_text_to_workflow/predict_expression/gcp_credentials.json" USE_LLM_GATEWAY=false python3 predict_expression_evaluation.py --max-evals-per-param=20 | tee eval_geminiandcopy_20evals.txt`


## Run service

open port 5000 e.g. by adding -p 5000:5000 when running docker
then from studio folder run

`GOOGLE_CLOUD_PROJECT="devtest-autopilot" GOOGLE_APPLICATION_CREDENTIALS="/workspace/src/autopilot/services/studio/_text_to_workflow/predict_expression/gcp_credentials.json" USE_LLM_GATEWAY=false python3 service.py`


## Test with client.py

`python3 autopilot/services/studio/_text_to_workflow/predict_expression/client.py /workspace/data/Autopilot.Samples/Dataset/PredictExpression/Excel.ReadRangeWorkbook/AddHeaders/test_StudioDesktop_LocalFiles_LogFilesAndExcelRows_Excel.ReadRangeWorkbook_6_0.yaml`
