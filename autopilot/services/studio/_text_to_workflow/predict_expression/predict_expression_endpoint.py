from services.studio._text_to_workflow.predict_expression import predict_expression_task
from services.studio._text_to_workflow.predict_expression.predict_expression_schema import PredictExpressionRequest, PredictExpressionResponse
from services.studio._text_to_workflow.utils import telemetry_utils


@telemetry_utils.log_execution_time("predict_expression_endpoint.init")
def init() -> None:
    predict_expression_task.init()


@telemetry_utils.log_execution_time("predict_expression_endpoint.generate")
async def generate(request: PredictExpressionRequest) -> PredictExpressionResponse | None:
    return await predict_expression_task.run(request)
