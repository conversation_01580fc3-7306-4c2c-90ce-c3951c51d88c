import asyncio
import copy
import json
import os
import pathlib
import re

import tqdm

from services.studio._text_to_workflow.common import schema, typedefs, typedefs_parser
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.params import get_param_value_category
from services.studio._text_to_workflow.common.walkers import (
    ActivitiesAndTriggersCollector,
    CollectWorkflowVariables,
    FillMissingWorkflowActivityTypedefs,
    FillMissingWorkflowOutArguments,
    <PERSON>,
)
from services.studio._text_to_workflow.common.workflow import Activity, Workflow
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load


def remove_param(workflow, param_name):
    if isinstance(workflow, dict):
        if param_name in workflow:
            del workflow[param_name]
        for v in workflow.values():
            remove_param(v, param_name)
    if isinstance(workflow, list):
        for v in workflow:
            remove_param(v, param_name)
    return workflow


def replace_thought_with_display_name(workflow, activity_retriever):
    if isinstance(workflow, dict):
        if "thought" in workflow and "activity" in workflow:
            activity_id = workflow["activity"]
            if "<" in activity_id:
                activity_id = activity_id.split("<")[0]
            if "uiPathActivityTypeId" in workflow:
                activity_id = f"{activity_id}@{workflow['uiPathActivityTypeId']}"
            try:
                workflow["thought"] = activity_retriever.get(activity_id, "Windows")["description"]
            except Exception:
                workflow["thought"] = "Activity"
                print("unknown activity", workflow["activity"], activity_id)
        for v in workflow.values():
            replace_thought_with_display_name(v, activity_retriever)
    if isinstance(workflow, list):
        for v in workflow:
            replace_thought_with_display_name(v, activity_retriever)
    return workflow


def replace_param_on_current_activity(workflow, param_name, param_value, is_current_activity=False):
    if isinstance(workflow, dict):
        if "activity" in workflow:
            is_current_activity = "currentActivity" in workflow and workflow["currentActivity"]
        if param_name in workflow and is_current_activity:
            workflow[param_name] = param_value
        for v in workflow.values():
            replace_param_on_current_activity(v, param_name, param_value, is_current_activity)
    if isinstance(workflow, list):
        for v in workflow:
            replace_param_on_current_activity(v, param_name, param_value, is_current_activity)
    return workflow


def cleanup_workflow(workflow, param_name, activity_retriever):
    current_workflow = copy.deepcopy(workflow)
    current_workflow = replace_param_on_current_activity(current_workflow, param_name, "")
    current_workflow = replace_thought_with_display_name(current_workflow, activity_retriever)
    current_workflow = remove_param(current_workflow, "packages")
    current_workflow = remove_param(current_workflow, "processName")
    return current_workflow


class BuildPredictExpressionExamples(Walker):
    workflow_path: pathlib.Path
    workflow: Workflow
    activity_typedefs: dict[str, schema.TypeDef]
    additional_typedefs: dict[str, schema.TypeDef]
    step: int
    examples: list

    def __init__(self, workflow_generation_example: dict, workflow_path: pathlib.Path, retriever: ActivitiesRetriever) -> None:
        self.workflow_path = workflow_path
        self.workflow_generation_example = workflow_generation_example
        self.workflow = Workflow(workflow_generation_example["description"], workflow_generation_example["plan"], workflow_generation_example["process"])
        self.activity_typedefs, self.additional_typedefs = typedefs_parser.parse_workflow_conversion_typedefs(workflow_generation_example.get("typedefs", {}))
        self.step, self.examples = 0, []
        self.retriever = retriever

    def build(self) -> list:
        self.walk(self.workflow)
        return self.examples

    def process_workflow(self, workflow: Workflow) -> None:
        FillMissingWorkflowActivityTypedefs(self.workflow, self.activity_typedefs, self.additional_typedefs).fill()
        FillMissingWorkflowOutArguments(self.workflow, self.activity_typedefs, self.additional_typedefs).fill()
        collected = ActivitiesAndTriggersCollector().collect(self.workflow)
        self.all_activities = collected["trigger"] + collected["activity"]

    def process_activity(self, activity: Activity) -> None:
        self.step += 1
        # DEBUG: why UiPath.PDF.Activities.ReadPDFText is missing
        # if "UiPath.PDF.Activities.ReadPDFText" in activity.activity_id:
        #     import pdb; pdb.set_trace()
        activity_definition = self.retriever.get(activity.activity_id, "Windows")
        if activity_definition is None:
            return
        activity.is_current_activity = True
        try:
            if "mapped_category" in activity_definition and "mapped_name" in activity_definition:
                activity_name = f"{activity_definition['mapped_category']}.{activity_definition['mapped_name']}"
            else:
                activity_name = activity.activity_id

            workflow_variables = CollectWorkflowVariables(self.workflow, self.activity_typedefs, self.additional_typedefs).collect()

            non_params = {"Then", "Else", "Body", "Default", "Try", "UiPathActivityTypeId", "TableInfo"}
            activity_params = {}
            for param_name, param_value in activity.params.items():
                if param_name in non_params:
                    continue
                activity_params[param_name] = param_value

            for i, (param_name, param_value) in enumerate(activity.params.items()):
                if param_name in non_params:
                    continue
                param_typedef = self.activity_typedefs[activity.activity_id]["params"].get(param_name, {})
                if param_typedef.get("ctype", "") == "T" and activity.typename:
                    param_typedef = copy.deepcopy(param_typedef)  # so that we don't override for all activities
                    param_typedef["ctype"] = activity.typename
                workflow_id = self.workflow_path.stem
                workflow_split = self.workflow_path.parent.name
                current_workflow = self.workflow.to_dict()
                clean_workflow = cleanup_workflow(current_workflow, param_name, self.retriever)

                workflow_used_variables = {}
                if isinstance(param_value, str):
                    for vname, vtype in workflow_variables.get("in_scope", {}).items():
                        regexp_vname = re.compile(r"\b" + re.escape(vname) + r"\b", re.IGNORECASE)
                        if regexp_vname.search(param_value) and param_value.startswith("[["):
                            workflow_used_variables[vname] = vtype

                param_value_category = get_param_value_category(param_value)
                if isinstance(param_value, str) and param_value.startswith("[[") and param_value.endswith("]]"):
                    param_value = param_value[2:-2]
                if (
                    param_value_category == "primitive_property"
                    and param_typedef.get("ctype", "").lower() == "string"
                    and not (param_value.startswith('"') and param_value.endswith('"'))
                ):
                    param_value = f'"{param_value}"'

                other_params = {p: v for p, v in activity_params.items() if p != param_name}
                self.examples.append(
                    {
                        "id": f"{workflow_split}_{workflow_id}_{activity_name}_{self.step}_{i}",
                        "param_name": param_name,
                        "param_value": param_value,
                        "param_typedef": param_typedef,
                        "param_value_category": param_value_category,
                        "workflow_used_variables": workflow_used_variables,
                        "workflow_visible_variables": workflow_variables,
                        "activity_other_params": other_params,
                        "activity_param_index": i,
                        "activity_id": activity.activity_id,
                        "activity_name": activity_name,
                        "activity_fqn": activity.fqn,
                        "activity_display_name": activity.display_name,
                        "activity_full_class_name": activity_definition.get("fullClassName", ""),
                        "activity_class_name": activity_definition.get("className", ""),
                        "activity_category": activity_definition.get("category", ""),
                        "activity_typedef": self.activity_typedefs[activity.activity_id],
                        "workflow_activity_index": self.step,
                        "workflow_activities": [a.activity_id for a in self.all_activities],
                        "workflow_variables": activity.workflow_variables,
                        "workflow_arguments": activity.workflow_arguments,
                        "workflow_path": self.workflow_path.as_posix(),
                        "original_workflow": current_workflow,
                        "workflow": clean_workflow,
                        "workflow_additional_typedef": self.additional_typedefs,  # TODO: try do this per activity
                        "target_framework": self.workflow_generation_example["target_framework"],
                    }
                )
        finally:
            activity.is_current_activity = False


async def create_predict_expression_dataset() -> None:
    if not typedefs.exists():
        await typedefs.build(True)
    typedefs.load()
    retriever = ActivitiesRetriever()

    activity_id2short, activity_short2id = {}, {}

    dataset_path = paths.get_predict_expression_dataset_path()
    dataset_path.mkdir(parents=True, exist_ok=True)
    for target_framework in ("Portable", "Windows"):
        workflow_generation_dataset_path = paths.get_workflow_generation_dataset_path(target_framework)
        for workflow_split in ("train", "test", "static", "uia"):
            workflow_split_path = workflow_generation_dataset_path / workflow_split
            for workflow_generation_yaml_path in tqdm.tqdm(
                sorted(workflow_split_path.glob("*.yaml")), f"create_predict_expression_dataset {target_framework} {workflow_split}"
            ):
                workflow_generation_example = yaml_load(workflow_generation_yaml_path)
                examples = BuildPredictExpressionExamples(workflow_generation_example, workflow_generation_yaml_path, retriever).build()
                for example in examples:
                    os.makedirs(dataset_path / example["activity_name"] / example["param_name"], exist_ok=True)
                    example_path = dataset_path / example["activity_name"] / example["param_name"] / f"{example['id']}.yaml"
                    yaml_dump(example, example_path)  # JSON is much faster to dump, include this for debugging
                    with open(example_path.with_suffix(".json"), "w") as f:
                        json.dump(example, f)
                    activity_id2short[example["activity_id"]] = example["activity_name"]
                    activity_short2id[example["activity_name"]] = example["activity_id"]

    yaml_dump({"activity_id2short": activity_id2short, "activity_short2id": activity_short2id}, dataset_path / "activity_mapping.yaml")


if __name__ == "__main__":
    asyncio.run(create_predict_expression_dataset())
