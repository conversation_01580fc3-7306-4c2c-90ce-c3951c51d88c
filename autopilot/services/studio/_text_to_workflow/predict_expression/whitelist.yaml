Core.SetVariableValue:
  To: 100051
  Value: 32203
Excel.ReadRangeWorkbook:
  DataTable: 22462
  WorkbookPath: 3312
  SheetName: 1178
UiPathOrchestrator.LogMessage:
  Message: 22411
DataTable.ForEachRowInDataTable:
  DataTable: 19519
  CurrentIndex: 3286
Core.InputDialog:
  Result: 17861
ControlFlow.If:
  Condition: 17774
ControlFlow.ForEach:
  Values: 11707
  CurrentIndex: 2638
DataTable.OutputDataTableAsText:
  Text: 7487
  DataTable: 5144
DataTable.AddDataRow:
  ArrayRow: 7029
  DataTable: 6321
Excel.ReadRange:
  SaveTo: 5133
  Range: 2724
Excel.WriteDataTableToExcel:
  Source: 5035
  Destination: 3515
Excel.WriteCell:
  Value: 4869
  Cell: 3284
DataTable.BuildDataTable:
  DataTable: 4553
DataTable.FilterDataTable:
  OutputDataTable: 3951
  DataTable: 2942
File.WriteTextFile:
  Text: 3090
  FileName: 518
Web.DeserializeJson:
  JsonString: 2361
DataTable.MergeDataTable:
  Destination: 2093
  Source: 2023
Database.RunQuery:
  DataTable: 2027
  ExistingDbConnection: 449
  Sql: 136
Excel.UseExcelFile:
  WorkbookPath: 1959
DataTable.AddDataColumn:
  DataTable: 1840
  ColumnName: 159
Database.ConnectToDatabase:
  DatabaseConnection: 1836
Web.HttpRequest:
  Result: 1812
  StatusCode: 792
  ResponseHeaders: 300
  ResponseAttachment: 162
  EndPoint: 111
File.MoveFile:
  Path: 1361
  Destination: 769
DocumentUnderstanding.ExtractPdfText:
  Text: 1352
  PdfFile: 299
CSV.ReadCsv:
  DataTable: 1099
  FilePath: 114
Text.FindMatchingPatterns:
  Result: 1057
  FirstMatch: 950
  Input: 543
DataTable.GenerateDataTableFromText:
  DataTable: 978
  Input: 612
Excel.ForEachExcelRow:
  Range: 920
File.PathExists:
  Exists: 857
  Path: 606
  Resource: 606
File.CreateFolder:
  Path: 761
  Output: 380
Web.DeserializeJsonArray:
  JsonArray: 661
  JsonString: 431
Word.WordApplicationScope:
  FilePath: 578
Mail.SaveEmailAttachments:
  Message: 435
  Attachments: 413
  FolderPath: 289
  ResourceAttachments: 137
Excel.Filter:
  Range: 375
GSuite.WriteRange:
  Source: 313
GSuite.DownloadEmailAttachments:
  Email: 289
  NewResult: 236
File.CreateFile:
  Name: 272
Compression.ExtractUnzipFiles:
  DestinationFolderInfo: 271
  DestinationFolder: 119
MicrosoftOffice365.DownloadEmailAttachments:
  Email: 260
  NewResult: 201
Text.ExtractText:
  Source: 248
  Result: 215
Database.DisconnectFromDatabase:
  DatabaseConnection: 247
GSuite.SendEmail:
  Attachments: 219
Compression.CompressZipFiles:
  CompressedFileInfo: 218
  CompressedFileName: 138
MicrosoftOffice365.WriteRange:
  Source: 216
Text.IsTextMatching:
  Result: 206
ControlFlow.RepeatNumberOfTimes:
  NumberOfTimes: 180
GSuite.ReadRange:
  RangeInformation: 159
GSuite.GetNewestEmail:
  Result: 150
UiPathOrchestrator.SetTransactionStatus:
  TransactionItem: 138
DocumentUnderstanding.ClassifyDocument:
  ClassificationResults: 113
DataTable.CollectionToDataTable:
  DataTable: 111
Database.RunCommand:
  AffectedRecords: 111
Excel.InsertSheet:
  ReferenceNewSheetAs: 107
Word.InsertDataTableInDocument:
  DataTable: 107
DocumentUnderstanding.ExtractDocumentData:
  FileInput: 103
DocumentUnderstanding.GetPdfPageCount:
  PageCount: 102
