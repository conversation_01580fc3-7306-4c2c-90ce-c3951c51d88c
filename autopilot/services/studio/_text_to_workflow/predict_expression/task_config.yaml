predictions:
  use_llm: true
  use_copy_and_replace: true
llm:
  request_type: llm_gateway
  model_config: predict_expression_gemini_model
  # model_config: predict_expression_gpt4oaug_model
  # model_config: predict_expression_gpt4omini_model

  # ### Azure OpenAI
  # request_type: azure_openai
  # # model_name: gpt-4o
  # model_name: gpt-4o-mini
  # # deployment_name: gpt-4o-2024-08-06
  # # deployment_name: gpt-4o-2024-05-13
  # deployment_name: gpt-4o-mini-2024-07-18
  # endpoint: https://autopilot-openai-eastus.openai.azure.com/
  # api_version: 2024-05-01-preview

  # request_type: http_request
  # model_name: /data/models/Llama-4-Maverick-17B-128E-Instruct-FP8/
  # endpoint: http://35.193.153.193:8008/v1/chat/completions

  # request_type: http_request
  # model_name: /data/models/Mistral-Small-24B-Instruct-2501
  # endpoint: http://35.193.153.193:8008/v1/chat/completions

  # request_type: http_request
  # model_name: /data/models/Qwen2.5-72B-Instruct
  # endpoint: http://35.193.153.193:8009/v1/chat/completions
  # config:
  #   temperature: 0.9
  #   max_tokens: 200
  #   request_timeout: 60
  #   top_p: 0.95
  #   frequency_penalty: 0.0
  #   presence_penalty: 0.0
retrieval:
  enable: false
  embedding_model:
    model_type: sentence-embedding
    deployment_name: sentence-transformers/all-roberta-large-v1
  top_k: 5

