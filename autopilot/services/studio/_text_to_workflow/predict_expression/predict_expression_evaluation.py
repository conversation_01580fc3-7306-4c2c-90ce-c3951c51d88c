import argparse
import asyncio
import os
import pathlib
import random
import time
from collections import defaultdict
from pprint import pprint

from services.studio._text_to_workflow.predict_expression import client, predict_expression_task
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load


async def evaluate(max_evals_per_param, eval_activity=None, eval_param_name=None):
    random.seed(42)  # Set the seed for reproducibility
    dataset_path = paths.get_predict_expression_dataset_path()

    score_sum, score_count = 0, 0
    elapsed_times, elapsed_times_count = defaultdict(float), 0
    score_sum_by_category, score_count_by_category = defaultdict(float), defaultdict(int)
    score_sum_by_param, score_count_by_param = defaultdict(float), defaultdict(int)
    # activities = sorted(
    #     [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
    # )
    for activity in predict_expression_task.WHITELIST:
        if eval_activity is not None and activity != eval_activity:
            continue
        params = predict_expression_task.WHITELIST[activity]
        # params = sorted(os.listdir(dataset_path / activity))
        for param_name in params:
            if eval_param_name is not None and param_name != eval_param_name:
                continue
            if not (dataset_path / activity / param_name).is_dir():
                print(f"missing from dataset: activity {activity} and param {param_name}")
                continue
            files = sorted(os.listdir(dataset_path / activity / param_name))
            random.shuffle(files)
            for file_name in files[:max_evals_per_param]:
                file_path = dataset_path / activity / param_name / file_name
                result, example, info = await evaluation_result(file_path)
                for k, v in info["elapsed_times"].items():
                    elapsed_times[k] += v
                elapsed_times_count += 1
                if result is None:
                    # No demonstrations: exclude from the score
                    continue
                score_sum += result
                score_count += 1
                category = f"{example['param_typedef'].get('category', '')}.{example['param_value_category']}"

                score_sum_by_param[f"{activity}:{param_name}:{category}"] += result
                score_count_by_param[f"{activity}:{param_name}:{category}"] += 1

                score_sum_by_category[category] += result
                score_count_by_category[category] += 1
        print("partial score", score_sum / max(1, score_count), score_sum, score_count)
        print("partial score by category")
        for k in sorted(score_sum_by_category.keys()):
            print(k, score_sum_by_category[k] / max(1, score_count_by_category[k]), score_sum_by_category[k], score_count_by_category[k])

    print("final score by param")
    for k in sorted(score_sum_by_param.keys()):
        print(k, score_sum_by_param[k] / max(1, score_count_by_param[k]), score_sum_by_param[k], score_count_by_param[k])
    print("final score by category")
    for k in sorted(score_sum_by_category.keys()):
        print(k, score_sum_by_category[k] / max(1, score_count_by_category[k]), score_sum_by_category[k], score_count_by_category[k])
    print("final score", score_sum / max(1, score_count), score_sum, score_count)

    print(f"elapsed times {elapsed_times_count}")
    # average elapsed times
    elapsed_times = {k: v / max(1, elapsed_times_count) for k, v in elapsed_times.items()}
    pprint(elapsed_times)


async def evaluation_result(file_path: pathlib.Path):
    example = yaml_load(file_path)
    print("=" * 10, example["activity_name"], example["param_name"])
    print(example["workflow_path"])
    print(file_path)
    print("display_name", example["activity_display_name"])
    print("activities")
    for a in example["workflow_activities"]:
        print(a)
    print("variable types")
    pprint(example["workflow_visible_variables"]["in_scope"])

    print(f"{example['param_typedef'].get('category', '')}.{example['param_value_category']}")
    print(example["param_value"], "ground truth")

    if example["param_value_category"].startswith("list_") or example["param_value_category"].startswith("dict_"):
        return None, example, {}

    additionalTypeDefinitions = "\n".join(example["workflow_additional_typedef"][td]["text"] for td in example["workflow_additional_typedef"])

    results = await predict_expression_task.run(
        {
            "activity": example["activity_id"],
            "parameter": example["param_name"],
            "workflow": yaml_dump(example["workflow"]),
            "targetFramework": example["target_framework"],
            "activityTypeDefinition": example["activity_typedef"]["text"],
            "additionalTypeDefinitions": additionalTypeDefinitions,
            "availableVariables": example["workflow_visible_variables"],
            "workflowPath": example["workflow_path"],
        },
        ignore_demo_paths=[example["workflow_path"]],
    )
    if results is None:
        print("not in whitelist", example["activity_id"], example["param_name"])
        return None, example, {}
    predictions = results["values"]
    info = {
        "usage": results["usage"],
        "elapsed_times": results["elapsedTimes"],
        "demos": results["demos"],
        "param_type": results.get("parameterInferedType", ""),
    }
    if len(predictions) == 0:
        print("no demos", example["activity_id"], example["param_name"])
        return None, example, info

    ret = client.get_score(predictions, example["param_value"])
    print("prediction score", ret)
    print("infered param type", info["param_type"])
    print("v" * 10)
    for pred in predictions:
        print(pred)
    print("^" * 10)
    print(info["usage"])
    print("num demos", len(info["demos"]))
    for i, d in enumerate(info["demos"]):
        print("-" * 10, "demo", i + 1)
        print("demo value", d["param_value"])
        print("demo type", d["param_type"])
        print("demo visible variables in scope")
        pprint(d["used_variables"])
    pprint(info["elapsed_times"])
    return ret, example, info


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--max-evals-per-param", default=10, type=int, help="Maximum number of examples to evaluate per parameter type")
    ap.add_argument("--activity", default=None, type=str, help="Activity name to evaluate. Optional")
    ap.add_argument("--param", default=None, type=str, help="Parameter name to evaluate. Optional")

    args, _ = ap.parse_known_args()
    start_time = time.time()
    predict_expression_task.init()
    end_time = time.time()
    print("init took", end_time - start_time)

    asyncio.run(evaluate(max_evals_per_param=args.max_evals_per_param, eval_activity=args.activity, eval_param_name=args.param))
