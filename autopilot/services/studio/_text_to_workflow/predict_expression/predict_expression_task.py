import json
import os
import pathlib
import re
import time

import httpx

# from sentence_transformers import SentenceTransformer # TODO: this import is slow (check if we can improve it)
import langchain.prompts
import langchain_community.callbacks
import Levenshtein

# import vertexai
from openai import AzureOpenAI
from tqdm import tqdm

from services.studio._text_to_workflow.common import typedefs_parser
from services.studio._text_to_workflow.common.schema import Variable
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.common.walkers import (
    ActivitiesAndTriggersCollector,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.predict_expression.predict_expression_schema import PredictExpressionRequest, PredictExpressionResponse
from services.studio._text_to_workflow.utils import errors, paths, telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

DEMO_INDEX = None
ACTIVITY_MAPPING = {}
TASK_CONFIG = {}
PROMPT_CONFIG = {}
WHITELIST = {}
initialized = False
LOGGER = telemetry_utils.AppInsightsLogger()


def get_config_path(config_name: str = "prompt.yaml") -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / config_name


def init():
    global DEMO_INDEX, ACTIVITY_MAPPING, TASK_CONFIG, PROMPT_CONFIG, WHITELIST, initialized

    TASK_CONFIG = yaml_load(get_config_path("task_config.yaml"))
    PROMPT_CONFIG = yaml_load(get_config_path("prompt.yaml"))
    WHITELIST = yaml_load(get_config_path("whitelist.yaml"))

    DEMO_INDEX = PredictExpressionDemoIndex((paths.get_predict_expression_retrievers_path() / "index.demos.pkl").as_posix())

    ACTIVITY_MAPPING = yaml_load(paths.get_predict_expression_dataset_path() / "activity_mapping.yaml")

    # vertexai.init(project="devtest-autopilot") # THIS MIGHT BE NEEDED FOR GEMINI INFERRENCE

    initialized = True


async def run(request: PredictExpressionRequest, ignore_demo_paths=None) -> PredictExpressionResponse | None:
    elapsed_times = {}
    function_start_time = start_time = time.time()

    if not initialized:
        LOGGER.log_custom_event("TextToWorkflow.predict_expression_task.not_initialized")
        return {
            "values": [],
            "usage": TokenUsage(model="NoModel", prompt_tokens=0, completion_tokens=0, total_tokens=0).to_json(),
            "elapsedTimes": {},
            "demos": [],
            "parameterInferedType": "",
        }

    activity_id = request["activity"]
    parameter_name = request["parameter"]

    LOGGER.log_custom_event("TextToWorkflow.predict_expression_task.request", {"activity": activity_id, "parameter": parameter_name})

    activity_name = ACTIVITY_MAPPING["activity_id2short"].get(activity_id, None)
    is_in_whitelist = activity_name is not None and activity_name in WHITELIST and parameter_name in WHITELIST[activity_name]

    end_time = time.time()
    elapsed_times["whitelist_check"] = end_time - start_time
    start_time = end_time

    if not is_in_whitelist:
        LOGGER.log_custom_event("TextToWorkflow.predict_expression_task.whitelist_filter", {"activity": activity_id, "parameter": parameter_name})
        return None

    current_workflow_str = request["workflow"]
    workflow_visible_variables = request.get("availableVariables", {})
    activity_typedef = request["activityTypeDefinition"]
    workflow_additional_typedef = request["additionalTypeDefinitions"]

    current_workflow = yaml_load(current_workflow_str)
    activity_typedef_parsed = typedefs_parser.parse_typedef(activity_typedef)
    workflow_additional_typedef_parsed = typedefs_parser.parse_typedefs(workflow_additional_typedef)

    param_typedef = activity_typedef_parsed["params"].get(parameter_name, {})
    param_category = param_typedef["category"] if "category" in param_typedef else ""
    param_type = param_typedef["ctype"] if "ctype" in param_typedef else ""

    workflow = Workflow("", "", current_workflow)
    collected = ActivitiesAndTriggersCollector().collect(workflow)
    all_activities = collected["trigger"] + collected["activity"]
    workflow_activities = [a.activity_id for a in all_activities]
    current_activities = [a for a in all_activities if a.is_current_activity]

    end_time = time.time()
    elapsed_times["preprocess"] = end_time - start_time
    start_time = end_time

    if len(current_activities) == 0:
        LOGGER.log_custom_event("TextToWorkflow.predict_expression_task.missing_current_activity", {"activity": activity_id, "parameter": parameter_name})
        raise errors.MissingCurrentActivityError()
    elif len(current_activities) != 1:
        LOGGER.log_custom_event("TextToWorkflow.predict_expression_task.multiple_current_activities", {"activity": activity_id, "parameter": parameter_name})
        raise errors.MultipleCurrentActivitiesError()
    activity = current_activities[0]

    if param_type == "T" and activity.typename:
        param_type = activity.typename
        if "ctype" in param_typedef and param_typedef["ctype"] == "T":
            param_typedef["ctype"] = param_type

    demos = DEMO_INDEX.get_demos(  # type: ignore
        activity_name, parameter_name, param_type, workflow_activities, workflow_visible_variables, TASK_CONFIG["retrieval"]["top_k"], ignore_demo_paths
    )

    end_time = time.time()
    elapsed_times["get_demos"] = end_time - start_time
    start_time = end_time
    if len(demos) < 1:
        LOGGER.log_custom_event(
            "TextToWorkflow.predict_expression_task.missing_demos", {"activity": activity_id, "parameter": parameter_name, "parameter_type": param_type}
        )
        return {
            "values": [],
            "usage": TokenUsage(model="NoModel", prompt_tokens=0, completion_tokens=0, total_tokens=0).to_json(),
            "elapsedTimes": elapsed_times,
            "demos": [],
            "parameterInferedType": param_type,
        }

    non_params = {"Then", "Else", "Body", "Default", "Try", "UiPathActivityTypeId", "TableInfo"}
    activity_params = {}
    for other_param_name, other_param_value in activity.params.items():
        if other_param_name in non_params:
            continue
        activity_params[other_param_name] = other_param_value
    activity_other_params = {p: v for p, v in activity_params.items() if p != parameter_name}

    messages = build_chat_messages(current_workflow, demos, param_typedef, activity_other_params, activity_typedef_parsed, workflow_additional_typedef_parsed)

    end_time = time.time()
    elapsed_times["build_chat_messages"] = end_time - start_time
    start_time = end_time

    new_variables = None
    if TASK_CONFIG["predictions"]["use_llm"]:
        completions, usage = await run_inference(messages, n=1)
    else:
        completions = []
        usage = TokenUsage(model="NoModel", prompt_tokens=0, completion_tokens=0, total_tokens=0)

    end_time = time.time()
    elapsed_times["run_inference"] = end_time - start_time
    start_time = end_time

    if TASK_CONFIG["predictions"]["use_copy_and_replace"]:
        workflow_in_scope_variables = {k: v.get("type", "None") for k, v in workflow_visible_variables.get("in_scope", {}).items()}
        workflow_types2vars = {}
        for workflow_varname, workflow_vartype in workflow_in_scope_variables.items():
            workflow_types2vars.setdefault(workflow_vartype, set())
            workflow_types2vars[workflow_vartype].add(workflow_varname)

        for demo in demos:
            if not isinstance(demo["param_value"], str):
                # print("Not implemented") # TODO: implement for complex types (dict, list)
                continue
            demo_vars2types = {k: v["type"] for k, v in demo["used_variables"].items()}

            dvs = [demo["param_value"]]
            replaced_all = True
            used_workflow_vars = set()  # Track which workflow variables have been used
            for demo_varname, demo_vartype in demo_vars2types.items():
                if demo_vartype not in workflow_types2vars:
                    replaced_all = False
                    break
                # Get available workflow variables of matching type that haven't been used yet
                available_workflow_vars = [wvname for wvname in workflow_types2vars[demo_vartype] if wvname not in used_workflow_vars]
                if not available_workflow_vars:
                    replaced_all = False
                    break
                # TODO: in the future, we should use the closest matching variable name using embeddings or LLM
                # Find the closest matching variable name using Levenshtein distance
                closest_var = min(available_workflow_vars, key=lambda x: Levenshtein.distance(demo_varname.lower(), x.lower()))
                used_workflow_vars.add(closest_var)
                # variable names are case insensitive and should only match complete words
                regexp_dvname = re.compile(r"\b" + re.escape(demo_varname) + r"\b", re.IGNORECASE)  # match only complete variable names
                dvs = [regexp_dvname.sub(closest_var, dvalue) for dvalue in dvs]
            if replaced_all:
                completions.extend(dvs)

        if param_type in workflow_types2vars:
            # TODO: in the future, we should use the sorting by distance using embeddings or LLM
            # Order variables by Levenshtein distance to parameter name
            ordered_vars = sorted(workflow_types2vars[param_type], key=lambda x: Levenshtein.distance(x.lower(), parameter_name.lower()))
            completions.extend(ordered_vars)

        if param_category == "OutArgument":
            if len(parameter_name) < 4:
                prefix = "Result"
            else:
                prefix = parameter_name
            workflow_variables = {k: v.get("type", "None") for d in workflow_visible_variables.values() for k, v in d.items()}
            new_variable_name = ""
            if prefix not in workflow_variables:
                new_variable_name = prefix
            elif prefix not in completions:
                for i in range(1, 100):
                    if f"{prefix}{i}" not in workflow_variables:
                        new_variable_name = f"{prefix}_{i}"
                        break
            if new_variable_name:
                new_variable = Variable({"name": new_variable_name, "type": param_type})
                new_variables = [[]] * len(completions) + [new_variable]
                completions.append(new_variable_name)

    # postprocess completions
    # for i, c in enumerate(completions):
    #     if c.startswith("new ") or c.startswith("string.Format(") or c.startswith("Excel.Sheet"):
    #         completions[i] = c
    #     if c.startswith('"') and c.endswith('"') and '"' not in c[1:-1]:
    #         completions[i] = c[1:-1]
    # workflow_in_scope_variables = {k: v.get("type", "None") for k, v in workflow_visible_variables.get("in_scope", {}).items()}
    # for vname in workflow_in_scope_variables:
    #     regexp_vname = re.compile(r'\b' + re.escape(vname) + r'\b', re.IGNORECASE)
    #     for i, c in enumerate(completions):
    #         if regexp_vname.search(c) and not c.startswith("[["):
    #             completions[i] = c
    # TODO: ask model to maybe ignore sometimes the details of types. e.g. List types can be generic types
    # TODO: expressions can be ok even if some things change e.g. [["Meeting created" + meeding]] ~ [["Scheduled meeting" + meeting]]
    # TODO: remove PII in our dataset
    # TODO: primitive_property strings can be anything, hard to predict (maybe don't, predict only general ones)
    # TODO: sometimes types can be close and examples can work, but similarity doesn't take it into account
    # TODO: sometimes you just don't have the demos; make it easy to add demos

    completions, new_variables = cleanup_completions(completions, new_variables)

    LOGGER.log_custom_event(
        "TextToWorkflow.predict_expression_task.finished",
        {"activity": activity_id, "parameter": parameter_name, "parameter_type": param_type, "values": completions},
    )
    end_time = time.time()
    elapsed_times["run_copy_and_replace"] = end_time - start_time
    start_time = end_time

    end_time = time.time()
    elapsed_times["total"] = end_time - function_start_time
    ret = {"values": completions, "usage": usage.to_json(), "elapsedTimes": elapsed_times, "demos": demos, "parameterInferedType": param_type}
    if new_variables is not None:
        ret["newVariables"] = new_variables
    return ret  # type: ignore


def build_chat_messages(
    current_workflow, demos, param_typedef, activity_other_params, activity_typedef, workflow_additional_typedef, programming_language="C#"
):
    activity_typedef_str = activity_typedef.get("text", "")
    workflow_additional_typedef_str = "\n".join([wat["text"] for wat in workflow_additional_typedef.values()])
    messages = []
    system_message = PROMPT_CONFIG["system_template"].format(
        programming_language=programming_language,
        param_typedef=yaml_dump(param_typedef).strip(),
        activity_type_definitions=activity_typedef_str.replace("{", "{{").replace("}", "}}"),  # TODO: escape somewhere else
        # additional_type_definitions="{{}}",
        additional_type_definitions=workflow_additional_typedef_str.replace("{", "{{").replace("}", "}}"),  # TODO: check if it is worth to add these
    )
    messages.append({"role": "system", "content": system_message})
    for demo in demos:
        user_message = PROMPT_CONFIG["user_template"].format(
            workflow=yaml_dump(demo["workflow"]).strip().replace("{", "{{").replace("}", "}}"),
            activity_other_params=yaml_dump(demo["activity_other_params"]).strip().replace("{", "{{").replace("}", "}}"),
        )
        assistant_message = PROMPT_CONFIG["assistant_template"].format(
            programming_language=programming_language, param_value=demo["param_value"].replace("{", "{{").replace("}", "}}")
        )
        messages.append({"role": "user", "content": user_message})
        messages.append({"role": "assistant", "content": assistant_message})
    request_message = PROMPT_CONFIG["user_template"].format(
        workflow=yaml_dump(current_workflow).strip().replace("{", "{{").replace("}", "}}"),
        activity_other_params=yaml_dump(activity_other_params).strip().replace("{", "{{").replace("}", "}}"),
    )
    messages.append({"role": "user", "content": request_message})
    if settings.DEBUG_MODE:
        with open("predict_expression.json", "w") as fout:
            json.dump(messages, fout, indent=2)
    return messages


def cleanup_completion(c: str) -> str:
    # Remove ```language prefix and ``` suffix if present
    if isinstance(c, str):
        # Match any ```language\n prefix
        if c.startswith("```"):
            # Find the first newline after ```
            newline_pos = c.find("\n", 3)
            if newline_pos != -1:
                c = c[newline_pos + 1 :]  # Remove ```language\n
        if c.endswith("```"):
            c = c[:-3]  # Remove ```
        c = c.strip()  # Remove any extra whitespace
    return c


async def run_inference(messages: list, n: int) -> tuple[list[str], TokenUsage]:
    if TASK_CONFIG["llm"]["request_type"] == "llm_gateway":
        completions, usage = await run_llm_gateway_inference(TASK_CONFIG["llm"]["model_config"], messages)
    elif TASK_CONFIG["llm"]["request_type"] == "http_request":
        completions, usage = run_http_request_inference(messages)
    elif TASK_CONFIG["llm"]["request_type"] == "azure_openai":
        completions, usage = run_azureopenai_inference(messages, n)
    else:
        raise Exception("Can't find configured request_type")

    completions = [cleanup_completion(c) for c in completions]
    return completions, usage


async def run_llm_gateway_inference(model_config: str, messages: list) -> tuple[list[str], TokenUsage]:
    gen_messages = []
    for message in messages:
        if message["role"] == "system":
            gen_messages.append(("system", message["content"]))
        elif message["role"] == "user":
            gen_messages.append(("human", message["content"]))
        elif message["role"] == "assistant":
            gen_messages.append(("assistant", message["content"]))

    model = ModelManager().get_llm_model(model_config, ConsumingFeatureType.PREDICT_EXPRESSION)
    model_name = model.model_name if hasattr(model, "model_name") else ""  # type: ignore
    chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
    chat_chain = chat_prompt | model

    with langchain_community.callbacks.get_openai_callback() as cb:
        response = await chat_chain.ainvoke({})
        assert isinstance(response.content, str), f"Unexpected response content type: {response.content}"
        usage = TokenUsage(model=model_name, prompt_tokens=cb.prompt_tokens, completion_tokens=cb.completion_tokens, total_tokens=cb.total_tokens)
    completions = [response.content]
    return completions, usage  # type: ignore


def run_azureopenai_inference(messages: list, n: int) -> tuple[list[str], TokenUsage]:
    endpoint = TASK_CONFIG["llm"]["endpoint"]
    deployment = TASK_CONFIG["llm"]["deployment_name"]
    subscription_key = os.getenv("AZURE_OPENAI_API_KEY")

    client = AzureOpenAI(
        azure_endpoint=endpoint,
        api_key=subscription_key,
        api_version="2024-05-01-preview",
    )
    completion = client.chat.completions.create(
        model=deployment,
        messages=messages,
        max_tokens=TASK_CONFIG["llm"]["config"]["max_tokens"],
        temperature=TASK_CONFIG["llm"]["config"]["temperature"],
        top_p=TASK_CONFIG["llm"]["config"]["top_p"],
        frequency_penalty=TASK_CONFIG["llm"]["config"]["frequency_penalty"],
        presence_penalty=TASK_CONFIG["llm"]["config"]["presence_penalty"],
        stop=None,
        stream=False,
        n=n,  # Request multiple completions
    )

    # Return list of completions and usage stats
    completions = [choice.message.content for choice in completion.choices]
    return completions, completion.usage  # type: ignore


def run_http_request_inference(messages: list) -> tuple[list[str], TokenUsage]:
    endpoint = TASK_CONFIG["llm"]["endpoint"]
    data = {
        "model": TASK_CONFIG["llm"]["model_name"],
        "temperature": TASK_CONFIG["llm"]["config"]["temperature"],
        "max_tokens": TASK_CONFIG["llm"]["config"]["max_tokens"],
        "messages": [
            {
                "role": message["role"],
                "content": message["content"],
            }
            for message in messages
        ],
    }

    # Make synchronous request since this is not an async function
    response = httpx.post(url=endpoint, json=data, timeout=300)
    response_data = response.json()

    completions = [c["message"]["content"] for c in response_data["choices"]]
    usage = TokenUsage(
        model=TASK_CONFIG["llm"]["model_name"],
        prompt_tokens=response_data["usage"]["prompt_tokens"],
        completion_tokens=response_data["usage"]["completion_tokens"],
        total_tokens=response_data["usage"]["total_tokens"],
    )
    return completions, usage


def cleanup_completions(completions, new_variables=None):
    # filter some values and remove duplicates but keep the order
    completions_set = set(["", '""', '[[""]]'])  # filter out these values
    if new_variables is None:
        completions_clean, new_variables_clean = [], None
        for c in completions:
            if c not in completions_set:
                completions_set.add(c)
                completions_clean.append(c)
    else:
        completions_clean, new_variables_clean = [], []
        for c, v in zip(completions, new_variables, strict=False):
            if c not in completions_set:
                completions_set.add(c)
                completions_clean.append(c)
                new_variables_clean.append(v)
    return completions_clean, new_variables_clean


class PredictExpressionDemoIndex(StateBuilder):
    def __init__(self, state_path: str):
        self.store = StateStore(state_path, self, lazy_load=settings.DEBUG_MODE)

    def build(self):
        state = {}
        dataset_path = paths.get_predict_expression_dataset_path()
        if not dataset_path.exists() or not any(dataset_path.iterdir()):
            return state, {}
        iterator = sorted(os.listdir(dataset_path))
        if settings.DEBUG_MODE:
            iterator = tqdm(iterator, desc="Building predict_expression index")
        for activity_name in iterator:
            activity_path = dataset_path / activity_name
            if not activity_path.is_dir() or (WHITELIST is not None and activity_name not in WHITELIST):
                continue
            state[activity_name] = {}
            for param_name in sorted(os.listdir(activity_path)):
                param_path = activity_path / param_name
                if not param_path.is_dir() or (WHITELIST is not None and param_name not in WHITELIST[activity_name]):
                    continue
                state[activity_name][param_name] = {}
                for demonstration_name in sorted(os.listdir(param_path)):
                    demonstration_path = param_path / demonstration_name
                    if demonstration_path.suffix == ".json":
                        with open(demonstration_path, "r") as f:
                            demo = json.load(f)
                        state[activity_name][param_name][demonstration_name] = demo
                    # else:
                    #     demo = yaml_load(demonstration_path) # JSON is much faster to load
        state_info = {activity_name: list(params2demos.keys()) for activity_name, params2demos in state.items()}
        return state, state_info

    def get_demos(self, activity_name, param_name, param_type, workflow_activities, workflow_visible_variables, num_demos=5, ignore_demo_paths=None):
        if self.store.state is None or len(self.store.state) == 0:
            return []
        if activity_name not in self.store.state or param_name not in self.store.state[activity_name]:
            return []

        workflow_in_scope_variable_types = [v.get("type", "None") for v in workflow_visible_variables.get("in_scope", {}).values()]

        demos = []
        for _demonstration_name, demo in self.store.state[activity_name][param_name].items():
            if ignore_demo_paths is not None and demo["workflow_path"] in ignore_demo_paths:
                continue
            typedef_score = 1.0 if demo["param_typedef"].get("ctype", "") == param_type else 0.0  # TODO: improve
            demo_activities = demo["workflow_activities"]
            demo_variables = demo["workflow_visible_variables"]
            demo_in_scope_variables = {k: v.get("type", "None") for k, v in demo_variables.get("in_scope", {}).items()}
            demo_in_scope_variable_types = [v.get("type", "None") for v in demo_variables.get("in_scope", {}).values()]

            activities_intersection = set(workflow_activities) & set(demo_activities)
            activities_union = set(workflow_activities) | set(demo_activities)
            activities_iou = len(activities_intersection) / max(1, len(activities_union))

            in_scope_variable_intersection = set(workflow_in_scope_variable_types) & set(demo_in_scope_variable_types)
            in_scope_variable_union = set(workflow_in_scope_variable_types) | set(demo_in_scope_variable_types)
            in_scope_variable_iou = len(in_scope_variable_intersection) / max(1, len(in_scope_variable_union))

            demos.append(
                {
                    "similarity": typedef_score + activities_iou + in_scope_variable_iou,
                    "param_value": demo["param_value"],
                    "param_type": demo["param_typedef"].get("ctype", ""),
                    "used_variables": demo["workflow_used_variables"],
                    "activity_other_params": demo["activity_other_params"],
                    "workflow": demo["workflow"],
                    "workflow_path": demo["workflow_path"],
                    "activities_iou": activities_iou,
                    "in_scope_variable_iou": in_scope_variable_iou,
                    "demo_activities": demo_activities,
                    "demo_in_scope_variables": demo_in_scope_variables,
                }
            )
        demos = sorted(demos, key=lambda x: x["similarity"], reverse=True)
        return demos[:num_demos]
