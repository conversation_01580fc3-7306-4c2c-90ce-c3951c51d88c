import typing_extensions as t

from services.studio._text_to_workflow.common.schema import TargetFramework, Variable
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse


class PredictExpressionRequest(BaseRequest):
    activity: str
    parameter: str
    workflow: str
    targetFramework: TargetFramework
    activityTypeDefinition: str
    additionalTypeDefinitions: str
    availableVariables: dict[str, dict[str, Variable]]
    editHistory: t.NotRequired[str]
    clipboard: t.NotRequired[str]
    workflowPath: t.NotRequired[str]  # for debug, not for prod


class PredictExpressionResponse(BaseResponse):
    values: list[str]
    newVariables: t.NotRequired[list[Variable]]  # TODO: be able to do this (maybe combine with the values)
    usage: TokenUsageJson  # not on prod
    elapsedTimes: dict[str, float]  # not on prod
    demos: list  # not on prod
    parameterInferedType: str  # not on prod
