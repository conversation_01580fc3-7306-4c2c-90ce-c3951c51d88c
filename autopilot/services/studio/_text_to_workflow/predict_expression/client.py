import argparse
import json
import os
import time
from glob import glob
from pprint import pprint

import Levenshtein
import requests
import yaml
from tqdm import tqdm


def get_score(predictions, ground_truth):
    # Calculate score based on position (1.0 for first position, decreasing by 0.1 for each position)
    try:
        position = predictions.index(ground_truth)
        ret = 1.0 - (position * 0.1)
    except ValueError:
        # If no exact match, find the closest prediction by Levenshtein distance
        max_lev_score = 0
        for pred in predictions:
            pred_str = str(pred)
            lev_score = Levenshtein.ratio(ground_truth, pred_str)
            max_lev_score = max(max_lev_score, lev_score)
        # Divide the Levenshtein distance by 3 to get the final score
        ret = max_lev_score / 3
    return ret


def predict_expression(endpoint, fpath):
    # Headers
    headers = {
        "Authorization": os.environ.get("UIPATH_TOKEN"),
        "Content-Type": "application/json",
        "X-Uipath-Tenantid": os.environ.get("X_UIPATH_TENANTID"),
    }

    with open(fpath, encoding="utf-8-sig") as fd:
        datapoint = yaml.load(fd, Loader=yaml.CSafeLoader)

    activity = datapoint["activity_id"]
    parameter = datapoint["param_name"]
    workflow = datapoint["workflow"]

    target_framework = datapoint["target_framework"]
    activityTypeDefinition = datapoint["activity_typedef"]["text"]
    additionalTypeDefinitions = "\n".join(datapoint["workflow_additional_typedef"][td]["text"] for td in datapoint["workflow_additional_typedef"])
    workflow_str = yaml.dump(workflow, None, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)

    # Request payload
    payload = {
        "activity": activity,
        "parameter": parameter,
        "workflow": workflow_str,
        "targetFramework": target_framework,
        "activityTypeDefinition": activityTypeDefinition,
        "additionalTypeDefinitions": additionalTypeDefinitions,
    }
    if "workflow_visible_variables" in datapoint:
        payload["availableVariables"] = datapoint["workflow_visible_variables"]
    with open("payload.json", "w") as fd:
        json.dump(payload, fd, indent=4)
    # Make the POST request
    response = requests.post(endpoint, headers=headers, json=payload)
    if response.status_code != 200:
        print(f"request finished with status code {response.status_code}")
        pprint(response.__dict__)
        return response.status_code, 0, None, None

    ground_truth = datapoint["param_value"]
    results = response.json()
    if "errors" in results:
        print(f"request finished with errors {results['errors']}")
        score = 0
    elif "error" in results:
        print(f"request finished with errors {results['error']}")
        score = 0
    elif "values" in results:
        predictions = results["values"]
        score = get_score(predictions, ground_truth)
    else:
        raise ValueError(f"unknown response {results}")
    return response.status_code, score, ground_truth, results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Predict expression from workflow YAML file")
    parser.add_argument("--endpoint", default="http://localhost:5000/v2/predict-expression", type=str, help="endpoint to the server")
    parser.add_argument("fpath", help="Path to the YAML file containing workflow data")

    args = parser.parse_args()

    if os.path.isdir(args.fpath):
        scores, durations, predicted_durations = [], [], []
        not_predicted = 0
        yaml_files = glob(os.path.join(args.fpath, "*.yaml"), recursive=True)
        for yaml_path in tqdm(yaml_files):
            try:
                start_time = time.time()
                status_code, score, ground_truth, results = predict_expression(args.endpoint, yaml_path)
                end_time = time.time()
                durations.append(end_time - start_time)
                if results is not None and len(results["values"]):
                    scores.append(score)
                    predicted_durations.append(end_time - start_time)
                else:
                    not_predicted += 1
            except Exception as e:
                print(f"Error processing {yaml_path}: {e}")

        if scores:
            mean_score = sum(scores) / max(1, len(scores))
            mean_duration = sum(durations) / max(1, len(durations))
            mean_predicted_duration = sum(predicted_durations) / max(1, len(predicted_durations))
            print(f"\nProcessed {len(durations)} YAML files and got predictions for {len(scores)}")
            print(f"Mean score: {mean_score:.3f}")
            print(f"Mean all duration: {mean_duration:.3f}, predicted {mean_predicted_duration:.3f}")
        else:
            print("No YAML files were processed successfully")

    elif os.path.isfile(args.fpath) and args.fpath.endswith(".yaml"):
        status_code, score, ground_truth, results = predict_expression(args.endpoint, args.fpath)
        pprint(results)
        print(status_code)
        print(f"ground truth {ground_truth}, score {score}")
    else:
        print(f"unknown path {args.fpath}")
