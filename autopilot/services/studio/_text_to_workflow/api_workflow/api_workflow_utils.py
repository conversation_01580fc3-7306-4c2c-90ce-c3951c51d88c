from yaml.representer import SafeRepresenter


# Needed to serialize code literals in the workflow in a special way
class CodeLiteral(str):
    pass


def code_literal_presenter(dumper, code_literal: CodeLiteral):
    # always use the literal block style
    return dumper.represent_scalar("tag:yaml.org,2002:str", str(code_literal), style="|")


def rewrite_code_as_literal(node):
    """Recursively replace values under the key 'code' with CodeLiteral(...)"""
    if isinstance(node, dict):
        return {
            k: CodeLiteral(v) if k == "code" and isinstance(v, str) and node.get("activity", "") == "JsInvoke" else rewrite_code_as_literal(v)
            for k, v in node.items()
        }
    elif isinstance(node, list):
        return [rewrite_code_as_literal(x) for x in node]
    else:
        return node


SafeRepresenter.add_representer(CodeLiteral, code_literal_presenter)
