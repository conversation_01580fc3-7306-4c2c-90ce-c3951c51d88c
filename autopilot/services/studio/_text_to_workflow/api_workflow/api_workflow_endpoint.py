from services.studio._text_to_workflow.api_workflow.api_workflow_request_schema import APIWorkflowRequest, APIWorkflowResponse
from services.studio._text_to_workflow.api_workflow.api_workflow_task import APIWorkflowTask
from services.studio._text_to_workflow.common.prompt_utils import validate_workflow_generation_prompt
from services.studio._text_to_workflow.utils import telemetry_utils

TASK: APIWorkflowTask = APIWorkflowTask("prompt.yaml")
LOGGER = telemetry_utils.AppInsightsLogger()


@telemetry_utils.log_execution_time("api_workflow_endpoint.init")
def init() -> None:
    global TASK
    TASK.load()


@telemetry_utils.log_execution_time("api_workflow_endpoint.generate_workflow")
async def generate_workflow(request: APIWorkflowRequest) -> APIWorkflowResponse:
    validate_workflow_generation_prompt(request.userRequest)

    return await TASK.generate_workflow(request)
