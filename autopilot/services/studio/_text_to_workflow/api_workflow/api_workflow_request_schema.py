from typing import Optional

from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, Connection
from services.studio._text_to_workflow.utils.request_schema import BasePydanticRequest, BaseResponse
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowProcessingError


class APIWorkflowRequest(BasePydanticRequest):
    userRequest: str
    connections: list[Connection]
    existingWorkflow: Optional[str] = None
    expressionLanguage: API_WF_EXPRESSION_LANGUAGE
    runId: Optional[str] = None
    mode: Optional[ActivitiesGenerationMode] = None


class APIWorkflowResponse(BaseResponse):
    result: Optional[str]
    promptScore: int
    ambiguities: Optional[str]
    errors: list[WorkflowProcessingError]
    summary: Optional[str]
