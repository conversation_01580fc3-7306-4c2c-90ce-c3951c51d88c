from typing import cast

from services.studio._text_to_workflow.api_workflow.summarization.workflow_generation_summary_schema import WorkflowGenerationSummary
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import ConnectorIntegrationWithMetadata, PostGenApiWorkflow
from services.studio._text_to_workflow.common.api_workflow.schema import HttpRequest
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import list_activities_post_gen


class WorkflowGenerationSummarizer:
    """Service for generating programmatic tree summaries of API workflows"""

    def __init__(self, activities_retriever: APIActivitiesRetriever):
        self.activities_retriever = activities_retriever

    def summarize_postgen_workflow(self, workflow: PostGenApiWorkflow) -> WorkflowGenerationSummary:
        """
        Generate a summary of the the inputs and API calls used in the workflow.

        Args:
            workflow: PostGenApiWorkflow

        Returns:
            WorkflowGenerationSummary containing the inputs and API calls used in the workflow
        """
        # Extract input names
        if workflow.input is None or workflow.input.get("properties") is None:
            input_names = []
        else:
            input_names = list(workflow.input["properties"].keys())

        postgen_activities = list_activities_post_gen(workflow)
        api_calls = []

        for activity in postgen_activities:
            if isinstance(activity, ConnectorIntegrationWithMetadata):
                activity_def = self.activities_retriever.get(activity.activity)
                if activity_def is not None:
                    if activity.activity.endswith("_HTTP_Request"):
                        connector_http_activity = cast(ConnectorIntegrationWithMetadata, activity)
                        method = str(connector_http_activity.with_.bodyParameters.get("method", '${"GET"}')[3:-2]).upper()
                        url = connector_http_activity.with_.bodyParameters.get("url")
                        if url is None or url == "" or len(url) <= 5:
                            url = " and the URL is not set."
                        else:
                            url = url[3:-2]
                        api_calls.append(
                            f"{activity.thought}: Using pre-authenticated HTTP call {activity.activity} with {method} {url} (application: {activity_def['category']})"
                        )
                    else:
                        api_calls.append(f"{activity.thought}: Using curated connector {activity.activity} (application: {activity_def['category']})")

            elif isinstance(activity, HttpRequest):
                http_activity = cast(HttpRequest, activity)
                url = http_activity.with_.endpoint
                if url is not None and url != "" and len(url) > 5:
                    url = url[3:-2]
                else:
                    url = " and the URL is not set."
                api_calls.append(f"{activity.thought}: Using HTTP call with {str(http_activity.with_.method)} {url}")

        return WorkflowGenerationSummary(input_names=input_names, api_calls=api_calls)
