demonstration_ranking:
  planning_demonstrations_limit: 30
  demonstrations_limit: 4
  force_windows_demonstrations: 2
  embedding_retrieval_docs: 30
  reranking_enable: true
  reranking_docs: 30
  mmr_enable: true
  mmr_similarities: iou_retrieved_activities  # choices: embeddings, reranking, pled, plan_tree_edit_distance, iou_activities, iou_retrieved_activities
  mmr_similarities_action: build # choices: build, load, disable; only used at dataset build time
  mmr_docs: 30
  mmr_diversity: 1.0  # 1.0 if you use reranking, 0.2 if not
  sorting_order: descending  # choices: ascending, descending
post_processing:
  plan_step_activities: 1 # how many activities to try extract for each plan step
  plan_step_triggers: 1
  anticipated_inexistent_activities: 2 # how many matches for each anticipated inexistent activities to keep
  anticipated_inexistent_triggers: 2
prompt:
  demonstration_filtering:
    planing_demonstrations_limit: 6
    wf_min_trigger_count: 2 # for workflow generation mode, we want at least 2 demonstrations with triggers
  proposed_activities_selection:
    main_query_activities_percentage: 30 # the percentage of activities that should be retrieved from the whole query embedding, the rest will be retrieved from each line of the query embeddings
    query_triggers_limit: 15  # how many related triggers do we want to extract for the query
    query_activities_limit: 150  # how many related activities do we want to extract for the query
    wf_step_activites_limit: 2  # how many related activities do we want to extract for each activity in the existing workflow
    wf_step_activities_limit_for_edit: 10   # how many related activities do we want to extract for each activity when rewriting or editing the existing workflow
  prompt_entity_types: activities
  system_msg: |-
    {intro}
    You will be provided with a JSON list with all the available Integration Activities used to build UiPath Serverless Workflows. Each activity has an id, application, display name and description. They will appear as JSON objects.
    The user will provide a query along a subset of Integration Activities ids out of the full list that might be used to build an automation that solves the query.
    
    {output_format}
    
    {general_requirements}

    # Integration Activities Details:
    - Each activity has an "application" field that indicates the application that the activity will interact with.
    - The purpose of the activity is indicated by the "display_name" and "description" fields.
    - There are 3 types of Integration Activities:
    1. Specialized Integration Activities, which are used for a specific single CRUD operation on a specific entity.
      - Example: Activity with application "Salesforce" and name "Get Account" is an activity used to get an account object from Salesforce.
    2. There are activities that can perform queries in a Query Language specific to an application. They must be selected from the list of proposed activities if retrieving objects from their application is relevant to the query.
      - Example: Activity with application "Salesforce" and name "Search using SOQL" is an activity used to retrieve records by executing 'SELECT' queries in the Salesforce Object Query Language (SOQL)
      - Example: Activity with application "Oracle NetSuite" and name "Execute SuiteQL Query" is an activity used to execute SELECT queries on the Oracle NetSuite database.
    3. API Integration Activities that perform pre-authorized HTTP calls to a specific application API. These must be used only when a more specific Integration Operation for that application is not available.
      - The name of these activities follow the format: "<APPLICATIONNAME> HTTP Request". 
      - IMPORTANT: Only use these activities when an operation on an <APPLICATIONNAME> entity is mentioned in the query and no Specialized Integration Activity is available for that operation/object OR if the query specifically asks for an API call or HTTP request for that application.
        - Example: "Salesforce HTTP Request" is an activity used to perform HTTP requests to interact with Salesforce and should be used whenever a more specific Salesforce Integration Activity is not available for a specific application entity.
        - Example: "Oracle NetSuite HTTP Request" is an activity used to Perform HTTP requests to interact with Oracle NetSuite and should be used whenever a more specific Oracle NetSuite Integration Activity is not available.
      - Only include an API Integration Activity if the application name is directly mentioned in the query or heavily implied.
      - IMPORTANT: DO NOT USE MORE THAN 5 API Integration Activities (e.g. activities with names ending in "HTTP Request" like "Salesforce HTTP Request").

    # Relevant Integration Activities Requirements:
    {activity_selection_guidelines_1}
    - Attention: If you already added an activity id, you must not add it again.
    {activity_selection_guidelines_2}
    - When asked to "generate" or "summarize" something from natural language or using an LLM, make sure to include Content Generation or Chat Completion activities as well in the proposed activities list.
      - Example: Activity with application "Perplexity" and name "Chat Completion" is used to generate natural language responses using Perplexity.
      - Example: Activity with application "DeepSeek" and name "Chat Completion" is used to generate natural language responses using DeepSeek models based on a given prompt and inputs. Add this activity when DeepSeek completion is mentioned in the query.
      - Example: Activity named "Web Summary" is used to summarize information using large language models based on a given prompt and inputs. Add this activity when summarizing any type of information is mentioned in the query.
    - When asked to "create" something, make sure to include activities that are relevant to the query, like write/send/invite activities for your type of entity because you will probably be asked to fill in the details into what you created.
    - If you fail to provide activities that may be necessary for the plan, you will be heavily penalized.
    - Be very specific when retrieving activities, do not retrieve activities that are not relevant to the query, it is better to retrieve fewer relevant activities than more with a broader scope.

    - Very Important: If there seem to be multiple activities useful even for a step which seems specific (but may be achieved in multiple ways), provide all of them. Take note of these examples:
         - Example: If a plan step says "Send a notification message in Microsoft Teams", you should provide all of "Send Channel Message", "Send Group Chat Message" and "Send Individual Chat Message" to the activities list (activity names are made-up here, to exemplify the point).
    - How to get related activities:
      - If the application used by the activities is specified (e.g. Google Drive, Excel, etc.), you must only include activities from that application. Don't include e.g. Smartsheet activities if the user clearly specified that they want to use Google Sheets.
      - If the application used by the activities is not specified, then you may include related activities from any application, but try to get the same ones for each (e.g. if you get "Get File or Folders" for "OneDrive SharePoint", also get "Get File or Folder List" for "Google Drive").
    {activity_list_structuring}
    - Do not blindly increment the id of the last activity in the list if the next activity is not useful for solving the user query, e.g. this is completely wrong: [0,1,2,3,4,5,6,7,...,100].
    - You must only include activities ids that are present in the list of proposed activities. 

    {ambiguities_requirements}

    {score_requirements}

    # Inexistent Activity Type Name Requirements:
    - The inexistentActivities should be a list of fictional activity type names that you would have liked to have retrieved, but which do not exist.
    - However, if you think you did a good job and fulfilled the activities for the plan well, inexistentActivities should be empty.
    - The inexistentActivities should be lists of strings.
    - Example: 
      ```
        Query: "Download the email from Gmail and upload it to Google Drive and OneDrive."
        Retrieval Critique: "I found the OneDrive upload activity, but I did not find the Google Drive upload activity."
        Anticipated But Inexistent Activity Type Names: ["Google Drive Upload Files"]
      ```
    
    # Plan Requirements:
    - It's important to include "If" and "For each" steps where necessary in the pseudocode.
    {plan_requirements}
        Query: "Download the invoice attachment from Gmail and upload it to Salesforce as a related document."
        Bad Example: "1. Download the invoice from Gmail.\n2. Upload the document to Salesforce."
        Good Example: "1. Use Gmail API to retrieve emails with invoice attachments.\n2. Extract and download the invoice attachment from the email.\n3. Use Salesforce API to upload the document and associate it with the appropriate record in Salesforce."

    {footer}

  user_msg_template: |-
    QUERY:
    {query}
  demonstration_template: |-
    ```
    QUERY: {query}
    PLAN: {plan}
    ```