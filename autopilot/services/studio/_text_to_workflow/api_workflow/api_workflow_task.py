import dataclasses
import datetime
import json
import pathlib
import random

from services.studio._text_to_workflow.api_workflow.api_workflow_request_schema import (
    APIWorkflowRequest,
    APIWorkflowResponse,
)
from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import APIActivityRetrievalService
from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.api_workflow.summarization.workflow_generation_summarizer import WorkflowGenerationSummarizer
from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import ApiWorkflowDraftResult
from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflow
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import ApiWorkflowParser
from services.studio._text_to_workflow.common.prompt_utils import append_debug_runid_to_prompt
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import (
    embedding_model,
    paths,
    telemetry_utils,
)
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.api_workflow_generation_retrievers import (
    APIWorkflowDemonstrationsRetriever,
)
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import InvalidConnectionError

LOGGER = telemetry_utils.AppInsightsLogger()


# TODO: This is a temporary, not yet fully implemented class.
class APIWorkflowTask:
    config_path: pathlib.Path
    config: dict
    embed_model: embedding_model.EmbeddingModel
    activity_retrieval_service: APIActivityRetrievalService
    draft_service: ApiWfDraftService
    summarizer: WorkflowGenerationSummarizer

    def __init__(self, config_name: str) -> None:
        super().__init__()
        self.config_path = (pathlib.Path(__file__).parent / config_name).absolute()
        self.config = yaml_load(self.config_path)
        # embed model
        self.embed_model = ModelManager().get_embeddings_model("activities_embedding_model")

        # retriever
        self.api_activity_retriever = APIActivitiesRetriever()
        self.post_gen_processing_service = ApiWfPostGenerationProcessingService(self.api_activity_retriever)
        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "workflow_generation" / "config" / "activity_retrieval_prompt.yaml"

        self.demo_retriever = APIWorkflowDemonstrationsRetriever(
            config_path.as_posix(),
            paths.get_api_workflow_retriever_dataset_path(),
            paths.get_api_workflow_demonstrations_retriever_path(),
            self.api_activity_retriever,
        )

        self.api_workflow_parser = ApiWorkflowParser(self.api_activity_retriever)
        self.summarizer = WorkflowGenerationSummarizer(self.api_activity_retriever)
        self.connection_embeddings_retriever = ConnectionEmbeddingsRetriever(self.api_activity_retriever, self.embed_model)

    def load(self) -> "APIWorkflowTask":
        self.activity_retrieval_service = APIActivityRetrievalService(self.api_activity_retriever, self.connection_embeddings_retriever)
        self.draft_service = ApiWfDraftService(self.api_activity_retriever, self.post_gen_processing_service)
        return self

    @log_execution_time("APIWorkflowTaskGeneration")
    async def generate_workflow(self, request: APIWorkflowRequest) -> APIWorkflowResponse:
        existing_workflow: ApiWorkflow | None = None
        if request.existingWorkflow:
            existing_workflow, _, _ = self.api_workflow_parser.parse_api_workflow(json.loads(request.existingWorkflow))

        if request.runId:
            # Prepend the runId to the userRequest to prevent prompt caching for debug mode.
            request.userRequest = append_debug_runid_to_prompt(request.userRequest, request.runId)

        activity_retrieval_result: APIActivityRetrievalResult = await self.activity_retrieval_service.generate_relevant_activities(
            query=request.userRequest,
            workflow=existing_workflow,
            connections=request.connections,
            eval_mode=False,
        )

        draft_result: ApiWorkflowDraftResult | None = None
        edit_draft_result: ApiWorkflowDraftResult | None = None
        # TODO: add existing workflow as a parameter, when draft service has support for it
        if request.mode == "edit":
            # try to generate a patch for the existing workflow
            edit_draft_result = await self.draft_service.generate_workflow_draft(
                query=request.userRequest,
                workflow=existing_workflow,
                connections=request.connections,
                activity_retrieval_result=activity_retrieval_result,
                expression_language=request.expressionLanguage,
                mode=request.mode,
                retry_count=2,
            )

            # if any errors were encountered applying the patch, we should try to fully re-write the workflow
            if len(edit_draft_result.edit_patch_structure_errors) > 0:
                edit_draft_result = await self.draft_service.generate_workflow_draft(
                    query=request.userRequest,
                    workflow=existing_workflow,
                    connections=request.connections,
                    activity_retrieval_result=activity_retrieval_result,
                    expression_language=request.expressionLanguage,
                    mode="workflow",
                    retry_count=2,
                )
        else:
            draft_result = await self.draft_service.generate_workflow_draft(
                query=request.userRequest,
                workflow=existing_workflow,
                connections=request.connections,
                activity_retrieval_result=activity_retrieval_result,
                expression_language=request.expressionLanguage,
                mode=request.mode if request.mode else "workflow",
            )

        result_draft = edit_draft_result if request.mode == "edit" else draft_result
        if result_draft is None:
            raise ValueError(f"Failed to generate the {request.mode} draft")

        if result_draft.api_workflow_details.processed_workflow is not None:
            summary = self.summarizer.summarize_postgen_workflow(result_draft.api_workflow_details.processed_workflow)
        else:
            summary = None

        response = APIWorkflowResponse(
            result=result_draft.api_workflow_details.processed_workflow.model_dump_json(exclude_none=True, by_alias=True)
            if result_draft.api_workflow_details.processed_workflow
            else None,
            promptScore=activity_retrieval_result.generation.score,
            ambiguities=activity_retrieval_result.generation.ambiguities or None,
            errors=result_draft.api_workflow_details.post_generation_errors,
            summary=summary.model_dump_json(indent=0, exclude_none=True) if summary else None,
        )

        if settings.DEBUG_MODE:
            try:
                self._log_debug_info(request, response, activity_retrieval_result, result_draft)
            except Exception as e:
                LOGGER.error(f"Error logging debug info: {e}")

        return response

    @staticmethod
    def _log_debug_info(
        request: APIWorkflowRequest, response: APIWorkflowResponse, activity_retrieval_result: APIActivityRetrievalResult, draft_result: ApiWorkflowDraftResult
    ):
        logs_path = paths.get_logs_path()
        logs_path.mkdir(parents=True, exist_ok=True)

        # Create a timestamped folder
        if hasattr(request, "runId") and request.runId:
            folder_name = f"API_WF_{request.runId}"
        else:
            current_time = datetime.datetime.now().strftime("%Y_%m_%d_%H%M%S")
            folder_name = f"API_WF_{current_time}_{random.randint(10000, 99999)}"

        log_folder = logs_path / folder_name
        log_folder.mkdir(exist_ok=True)

        print(f"Debug: Saving API workflow generation info to .data/Logs/{folder_name}.")

        # Helper function to serialize objects with numpy arrays
        def json_serialize(obj):
            if hasattr(obj, "__dict__"):
                return {
                    k: json_serialize(v) for k, v in obj.__dict__.items() if not k.startswith("_") and k != "query_embedding" and k != "connections_embedding"
                }
            if isinstance(obj, (list, tuple)):
                return [json_serialize(item) for item in obj]
            if isinstance(obj, dict):
                return {k: json_serialize(v) for k, v in obj.items()}
            if isinstance(obj, set):
                return list(obj)
            if hasattr(obj, "model_dump"):
                return json_serialize(obj.model_dump())
            if hasattr(obj, "model_dump_json"):
                return json.loads(obj.model_dump_json())
            if dataclasses.is_dataclass(obj) and not isinstance(obj, type):
                return json_serialize(dataclasses.asdict(obj))
            return obj

        # Save activity retrieval result
        with open(log_folder / "activity_retrieval_result.json", "w") as f:
            serialized_result = json_serialize(activity_retrieval_result)
            json.dump(serialized_result, f, indent=2, default=str)

        # Save prompt value
        if draft_result.prompt_value:
            with open(log_folder / "generation_prompt.txt", "w") as f:
                f.write(draft_result.prompt_value.to_string())

        # Save raw workflow
        if draft_result.api_workflow_details and hasattr(draft_result.api_workflow_details, "raw_workflow"):
            with open(log_folder / "raw_workflow.yaml", "w") as f:
                f.write(draft_result.api_workflow_details.raw_workflow)

        # Save parsed raw workflow
        if draft_result.api_workflow_details and draft_result.api_workflow_details.generated_workflow:
            with open(log_folder / "parsed_raw_workflow.json", "w") as f:
                workflow_json = draft_result.api_workflow_details.generated_workflow.model_dump_json(indent=2, exclude_none=True, by_alias=True)
                f.write(workflow_json)

        # Save generated workflow
        if draft_result.api_workflow_details and draft_result.api_workflow_details.processed_workflow:
            with open(log_folder / "generated_workflow.json", "w") as f:
                workflow_json = draft_result.api_workflow_details.processed_workflow.model_dump_json(indent=2, exclude_none=True, by_alias=True)
                f.write(workflow_json)

        # Save connection errors
        connection_errors = [error for error in draft_result.api_workflow_details.post_generation_errors if isinstance(error, InvalidConnectionError)]
        if connection_errors and len(connection_errors) > 0:
            with open(log_folder / "connection_errors.json", "w") as f:
                errors = [error.model_dump() for error in connection_errors]
                f.write(json.dumps(errors, indent=2, default=str))

        # Save post processing errors
        post_generation_errors = [error for error in draft_result.api_workflow_details.post_generation_errors if not isinstance(error, InvalidConnectionError)]
        if post_generation_errors and len(post_generation_errors) > 0:
            with open(log_folder / "post_processing_errors.json", "w") as f:
                errors = [
                    error.model_dump() for error in draft_result.api_workflow_details.post_generation_errors if not isinstance(error, InvalidConnectionError)
                ]
                f.write(json.dumps(errors, indent=2, default=str))
