from dataclasses import dataclass
from enum import Enum
from typing import Any, Coroutine, Dict, Generator, List, Optional, Tuple, Union

import regex as re
import yaml  # type: ignore[import-untyped]
from langchain_core.messages import BaseMessage
from pydantic import BaseModel, ValidationInfo, model_validator
from pydantic.json_schema import SkipJsonSchema


class EventType(str, Enum):
    ADD = "adding"
    SUMMARY = "summary"
    RESPOND = "respond"


@dataclass
class Event:
    type: EventType
    content: Optional[list[BaseMessage] | list[Coroutine]]


class SemanticDiffConfig(BaseModel):
    model: dict[str, Any]
    embedding_model: dict[str, Any]
    min_chunk_size: int
    max_input_tokens: int
    max_chunking_tokens_limit: int
    max_retry_attempts: int
    retry_wait_fixed: int
    system: str
    summary: str
    examples: list[str]
    evaluation: dict[str, Any]

    def override(self, **kwargs) -> "SemanticDiffConfig":
        """
        Create a new config instance with overridden fields.

        Args:
            **kwargs: Key-value pairs of fields to override

        Returns:
            A new SemanticDiffConfig instance with overridden values

        Raises:
            ValueError: If an invalid field is provided in kwargs
        """
        # Check for invalid fields
        invalid_fields = set(kwargs.keys()) - self.model_fields_set
        if invalid_fields:
            raise ValueError(f"Invalid fields provided: {invalid_fields}")

        # Create new instance with updated values
        return self.__class__(**{**self.model_dump(), **kwargs})


class SemanticDiffRequest(BaseModel):
    diff: str
    lut: dict[str, Any]


class SemanticDiffEvalRequest(SemanticDiffRequest):
    ref: str
    name: str

    def export(self) -> SemanticDiffRequest:
        return SemanticDiffRequest(diff=self.diff, lut=self.lut)


class LineNumberRangeLUT:
    def __init__(self, ranges: Optional[List[int]] = None, text: Optional[str] = None) -> None:
        """
        Constructor for the LineNumberRangeLUT. It can be initialized either by ranges or a text.

        Args:
            ranges (Optional[List[int]]): The list of line ranges.
            text (Optional[str]): The string text from which line ranges are to be calculated.
        """

        if ranges:
            self.ranges = ranges
        elif text:
            self.ranges = self.generate_ranges_from_text(text)
        else:
            self.ranges = []

    def __bool__(self) -> bool:
        return bool(self.ranges)

    def get_line_number(self, col: int) -> int:
        """
        Function to get the line number corresponding to the column number.

        Args:
            col (int): The column number.

        Returns:
            int: The corresponding line number.
        """

        return sum(ln <= col for ln in self.ranges) + 1

    @classmethod
    def generate_ranges_from_text(cls, text: str) -> List[int]:
        """
        Class method to generate line ranges from a text.

        Args:
            text (str): The input text.

        Returns:
            List[int]: The list of line ranges.
        """

        return [match.span()[0] for match in re.finditer("\n", text)]


class SimpleStrResponse(BaseModel):
    content: str


class SourceQuote(BaseModel):
    span: str

    # Fields ignored by LLM model
    alpha: SkipJsonSchema[Optional[int]] = None
    beta: SkipJsonSchema[Optional[int]] = None
    delta: SkipJsonSchema[Optional[int]] = None


class CitedFact(BaseModel):
    fact: str
    source_quotes: List[SourceQuote]


class InternalCitedFact(CitedFact):
    @model_validator(mode="after")
    def validate_sources(self, info: ValidationInfo) -> "InternalCitedFact":
        """
        Validates the source quotes against the provided context.

        This method ensures that each source quote exists in the context and updates
        the source_quote list with the exact text found in the context.

        Args:
            info (ValidationInfo): Validation information containing the context.

        Returns:
            Citation: The validated Citation object.

        Note:
            This method modifies the source_quote attribute in-place.
        """
        if not hasattr(info, "context") or not info.context:
            raise ValueError("Validation context is missing.")
        text_chunks = info.context.get("text_chunk", None)
        lr_lut = info.context.get("lut", None)
        if text_chunks is not None and lr_lut is not None:
            spans = list(self.get_spans(text_chunks))
            _src_quotes = []
            for start, end in spans:
                _src_quotes.append(SourceQuote(span=text_chunks[start:end], delta=lr_lut.get_line_number(start)))
            self.source_quotes = _src_quotes

        return self

    def get_spans(self, context: str) -> Generator[Tuple[int, int], None, None]:
        """
        Finds the locations of all source quotes in the given context.

        Args:
            context (str): The full text in which to search for quotes.

        Yields:
            tuple: A series of (start, end) index pairs for each quote found in the context.
        """
        for quote in self.source_quotes:
            yield from self._get_span(quote.span, context)

    def _get_span(self, quote: str, context: str) -> Generator[Tuple[int, int], None, None]:
        """
        Finds all occurrences of a specific quote in the context.

        Args:
            quote (str): The quote to search for.
            context (str): The full text in which to search.

        Yields:
            tuple: A series of (start, end) index pairs for the quote in the context.
        """
        for match in re.finditer(re.escape(quote), context):  # type: ignore[attr-defined]
            yield match.span()

    def export(self) -> CitedFact:
        return CitedFact(fact=self.fact, source_quotes=self.source_quotes)


class CitedSemanticDiff(BaseModel):
    differences: List[CitedFact]
    explanation: str
    semantic_diff: bool


class InternalCitedSemanticDiff(BaseModel):
    differences: List[InternalCitedFact]
    explanation: str
    semantic_diff: bool

    def load_alpha_beta(self, lut: Dict[str, Dict[str, str]]) -> None:
        """
        Method to load alpha and beta (line numbers) into the source quotes.

        Args:
            lut (Dict[str, Dict[str, str]]): A lookup table for alpha and beta values.
        """

        for diff in self.differences:
            for sq in diff.source_quotes:
                key = str(sq.delta)
                alpha = lut.get(key, {}).get("A")
                beta = lut.get(key, {}).get("B")
                sq.alpha = int(alpha) if alpha else None
                sq.beta = int(beta) if beta else None

    def to_yaml(self, **kwargs: Any) -> str:
        """
        Method to convert the object into a YAML string.

        Args:
            **kwargs (Any): Any additional parameters to pass.

        Returns:
            str: The YAML representation of the object.
        """

        exclude_ln = kwargs.pop("exclude_ln", False)
        if exclude_ln:
            kwargs["exclude"] = {"differences": {"__all__": {"source_quotes": {"__all__": {v: ... for v in ["alpha", "beta", "delta"]}}}}}
        return yaml.dump(self.model_dump(**kwargs), allow_unicode=True)

    def __add__(self, o: "InternalCitedSemanticDiff") -> "InternalCitedSemanticDiff":
        """
        Overloaded addition operator to combine two CitedSemanticDiff objects.

        Args:
            o (CitedSemanticDiff): The other CitedSemanticDiff object.

        Returns:
            CitedSemanticDiff: New object of same type with combined values.
        """

        return type(self).model_construct(
            differences=self.differences + o.differences,
            explanation="\n\n".join([x for x in [self.explanation, o.explanation] if x]),
            semantic_diff=self.semantic_diff & o.semantic_diff,
        )

    @model_validator(mode="after")
    def validate_sources(self) -> "InternalCitedSemanticDiff":
        """
        Validates all citations in the differences list.

        This method removes any CitedFact objects that don't have valid source quotes.

        Returns:
            CitedSemanticDiff: The validated CitedSemanticDiff object.

        Note:
            This method modifies the source_quotes attribute in-place.
        """
        self.differences = [src for src in self.differences if len(src.source_quotes) > 0]
        return self

    def export(self) -> CitedSemanticDiff:
        return CitedSemanticDiff(
            differences=[d.export() for d in self.differences],
            explanation=self.explanation,
            semantic_diff=self.semantic_diff,
        )


class EvalScore(BaseModel):
    explanation: str
    accuracy_score: int
    hallucination_score: int

    def get_score(self) -> Union[float, int]:
        """
        Represents the average score of accuracy and hallucination.

        Returns:
            Union[float, int]: The average score of accuracy and hallucination.
        """
        return (self.accuracy_score + self.hallucination_score) / 2

    def get_explanation(self) -> str:
        """
        Formats the explanation and the score to a string.

        Returns:
            str: Explanation string appended with the overall score.
        """
        return f"{self.explanation} \n\n Score: {self.get_score():.2f}"
