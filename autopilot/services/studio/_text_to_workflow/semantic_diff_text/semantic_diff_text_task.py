import asyncio
import json
from pathlib import Path
from queue import Queue
from typing import Coroutine

from fastapi import HTTPException, status
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
)
from langchain_core.output_parsers import <PERSON>sonOutputParser
from langchain_core.prompts.chat import SystemMessagePromptTemplate

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_schema import (
    Event,
    EventType,
    InternalCitedSemanticDiff,
    LineNumberRangeLUT,
    SemanticDiffConfig,
    SemanticDiffRequest,
)
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.paths import get_semantic_diff_text_dataset_path
from services.studio._text_to_workflow.utils.split import split_by_character
from services.studio._text_to_workflow.utils.structured_outputs import async_generate_json_mode, async_llm_retry
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

# Logging
LOGGER = AppInsightsLogger()


class RequestEntityTooLarge(Exception):
    pass


class SemanticDiffTextsTask:
    def __init__(self, path: Path) -> None:
        self.config = SemanticDiffConfig(**yaml_load(path))
        dataset_path = get_semantic_diff_text_dataset_path()
        self.examples = [dataset_path / ex for ex in self.config.examples]

        parser = JsonOutputParser(pydantic_object=InternalCitedSemanticDiff)

        # Creating initial conversation with Few Shot examples
        self.init_prompt = [
            SystemMessagePromptTemplate.from_template(self.config.system).format(format_instructions=parser.get_format_instructions())
        ] + self.load_examples(self.examples)

        self.summary_system_prompt = SystemMessagePromptTemplate.from_template(self.config.summary).format(format_instructions=parser.get_format_instructions())

    def load_examples(self, paths: list[Path]) -> list[BaseMessage]:
        """
        Loads examples from a given list of paths.

        Args:
            self: The instance of the class the method belongs to.
            paths (list[Path]): A list of paths where the examples are located.

        Returns:
            list[BaseMessage]: A list of messages formed by reading from the files at each of the given paths -
            each set of messages include a 'HumanMessage' from 'unidiff.diff' file,
            an 'AIMessage' and a 'ToolMessage' related to 'ssot.yaml' file.
        """
        messages: list[BaseMessage] = []
        for path in paths:
            # Load User Message
            with open(path / "unidiff.diff", encoding="utf8") as file:
                diff = file.read()
            messages.append(HumanMessage(content=diff))

            # Load Assistant Messages
            messages.append(AIMessage(content=json.dumps(yaml_load(path / "ssot.yaml"))))

        return messages

    @telemetry_utils.log_execution_time("SemanticDiffTextsTask.run")
    async def run(self, request: SemanticDiffRequest) -> InternalCitedSemanticDiff:
        """
        Process semantic diff requests with chunking, retries, and error handling.

        This asynchronous function handles semantic differentiation requests by:
        1. Automatically chunking large inputs that exceed token limits
        2. Managing parallel processing of chunked requests
        3. Implementing configurable retry/reask logic

        Args:
            self : The object instance.
            request (SemanticDiffRequest): The semantic differentiation request.

        Returns:
            InternalCitedSemanticDiff: The semantic differentiation response.

        Raises:
            HTTPException: An exception that occurs when there is a server error.
        """

        if request.diff == "":
            return InternalCitedSemanticDiff(differences=[], explanation="No differences found.", semantic_diff=False)

        # Instantiate model
        model = ModelManager().get_llm_model("semantic_diff_model", ConsumingFeatureType.SEMANTIC_DIFF)
        init_prompt_token_count = model.get_num_tokens_from_messages(self.init_prompt)

        try:
            # Declaring variables
            q: Queue[Event] = Queue()
            response: InternalCitedSemanticDiff | None = None

            user_msg = HumanMessage(content=request.diff)
            q.put(Event(type=EventType.RESPOND, content=self.init_prompt + [user_msg]))

            # Set validation context
            delta_lut = LineNumberRangeLUT(text=request.diff)
            validation_context = {"text_chunk": request.diff, "lut": delta_lut}

            # Decorate async_generate_json_mode with retry decorator, using correct configuration
            generate_llm_output = async_llm_retry(max_attempts=self.config.max_retry_attempts, wait_fixed_time=self.config.retry_wait_fixed)(
                async_generate_json_mode
            )

            while not q.empty():
                event = q.get()

                # Initiate explanation summarization on next iteration
                if event.type == EventType.SUMMARY and not event.content:
                    q.put(self._create_summarize_event(response.explanation))  # type: ignore
                    continue

                # Initiate chuncking on next iteration if total token count > max_input_tokens
                if self._needs_chunking(event, model):
                    LOGGER.info("Chunking input because it's too large.")
                    reqs = []
                    if isinstance((msg := event.content[-1]), HumanMessage):  # type: ignore
                        # Calculate max size that permits adding system message
                        chunk_size = self.config.max_input_tokens - init_prompt_token_count
                        if chunk_size < 0:
                            raise ValueError(f"Possible misconfiguration, chunk size is too small: {chunk_size}")
                        # Add conversations that will produce partial responses
                        for chunk in split_by_character(
                            msg.content,  # type: ignore
                            self.config.model["deployment_name"],
                            chunk_size,
                        ):
                            _user_msg = HumanMessage(content=chunk)
                            reqs.append(
                                generate_llm_output(
                                    self.init_prompt + [_user_msg],
                                    model,
                                    validation_context=validation_context,
                                    response_model=InternalCitedSemanticDiff,
                                )
                            )
                    # Add batch requests into an event
                    q.put(Event(type=EventType.ADD, content=reqs))
                    # Add special event to trigger explanation summarization
                    q.put(Event(type=EventType.SUMMARY, content=None))
                    continue

                batch_requests: list[Coroutine] = []
                if event.type == EventType.ADD:
                    batch_requests = event.content  # type: ignore
                else:
                    batch_requests.append(
                        generate_llm_output(
                            messages=event.content,
                            model=model,
                            validation_context=validation_context,
                            response_model=InternalCitedSemanticDiff,
                        )
                    )

                # Run coroutines concurrently
                gathered_results = await asyncio.gather(*batch_requests)

                for _response, _ in gathered_results:
                    # Load Alpha Beta LUT
                    _response.load_alpha_beta(request.lut)

                    # Set or combine partial responses
                    if event.type == EventType.RESPOND:
                        response = _response
                    elif event.type == EventType.ADD:
                        response = response + _response if response else _response
                    else:
                        response.explanation = _response.explanation  # type: ignore

        except RequestEntityTooLarge as ex:
            LOGGER.error(f"Error: {ex}")
            raise HTTPException(status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE, detail=str(ex))
        except Exception as ex:
            LOGGER.error(f"Error: {ex}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal Server Error.")

        return response  # type: ignore

    def _create_summarize_event(self, explanation: str) -> Event:
        """Creates a event that will generate a summarized explanation"""
        return Event(type=EventType.SUMMARY, content=[self.summary_system_prompt, HumanMessage(content=explanation)])

    def _needs_chunking(self, event: Event, model: BaseChatModel) -> bool:
        """Determines if an event needs to be chunked and validates against maximum size limits."""
        if event.type != EventType.ADD:
            token_count = model.get_num_tokens_from_messages(event.content)  # type: ignore
            if token_count > self.config.max_input_tokens:
                if token_count > self.config.max_chunking_tokens_limit:
                    raise RequestEntityTooLarge("The request body exceeds the maximum allowed size")
                return True

        return False
