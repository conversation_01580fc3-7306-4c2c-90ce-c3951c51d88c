import argparse
import async<PERSON>
from pathlib import Path

import pandas as pd

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_eval import evaluate, load_request_set
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_task import SemanticDiffTextsTask
from services.studio._text_to_workflow.utils.paths import get_semantic_diff_text_dataset_path


async def main() -> None:
    parser = argparse.ArgumentParser(description="Async semantic diff evaluation CLI tool")
    parser.add_argument(
        "--evaluate",
        type=str,
        choices=["lmyaml", "pdf_text", "both"],
        required=True,
        help="Type of evaluation to perform (lmyaml, pdf_text, or both)",
    )
    args = parser.parse_args()

    # Declare variables
    dataset_folder = get_semantic_diff_text_dataset_path()
    task = SemanticDiffTextsTask(Path(__file__).parent / "prompt.yaml")
    requests = []
    tasks = []
    embeding_model = ModelManager().get_embeddings_model()

    # Create requests
    if args.evaluate == "lmyaml" or args.evaluate == "both":
        requests += load_request_set(dataset_folder / "lmyaml")
    if args.evaluate == "pdf_text" or args.evaluate == "both":
        requests += load_request_set(dataset_folder / "pdf_text")

    print("Sending requests:")
    for i, req in enumerate(requests):
        print(f"{i} - {req.name}")

    # Create tasks
    tasks = [evaluate(task, r, embeding_model) for r in requests]

    # Wait for results
    results = await asyncio.gather(*tasks)

    # Print results
    if not results:
        print("No results were generated")
        return
    print("Results:")
    for i, result in enumerate(results):
        print(f"{i} - {result}")
    # Print stats
    if not (data := [e for e in results if isinstance(e, dict)]):
        print("There is no data to calculate stats")
        return
    df = pd.DataFrame(data)
    stats = {
        "correlation": df["llm_score"].corr(df["similarity_score"]),
        "success_count": (success_count := len(df)),
        "err_count": len(results) - success_count,
        "llm_score_min": df["llm_score"].min(),
        "llm_score_mean": df["llm_score"].mean(),
        "llm_score_max": df["llm_score"].max(),
        "similarity_score_min": df["similarity_score"].min(),
        "similarity_score_mean": df["similarity_score"].mean(),
        "similarity_score_max": df["similarity_score"].max(),
    }
    print(f"\nStats: \n {stats}")


if __name__ == "__main__":
    asyncio.run(main())
