model:
  deployment_name: gpt-4o-mini-2024-07-18
  temperature: 0.0
  max_tokens: 4096 # Max number of tokens to generate.
  request_timeout: 60
  top_p: 0.0
  frequency_penalty: 0.0
  presence_penalty: 0.0
  openai_api_version: 2025-01-01-preview # structured outputs requires version >= 2024-08-01-preview
embedding_model:
  model_type: sentence-embedding
  deployment_name: all-mpnet-base-v2
min_chunk_size: 2500
max_input_tokens: 120000 # 128000 (completion limit) - 8000 (reask budget) = 120000
max_chunking_tokens_limit: 200000
max_retry_attempts: 3
retry_wait_fixed: 0
examples:
  - "examples/01"
  - "examples/02"
  - "examples/03"
evaluation:
  prompt: |
    [Instruction]
    Please act as an impartial judge and evaluate the quality of the response provided by an AI assistant.
    Evaluate the predicted result against the reference, based on the provided criteria.
    Provide a short explanation for your score and then a score for each criteria.

    Remember:
    - Be as objective as possible.
    - Scoring can take any int value from 1 to 10. You must not give a value outside of that range.
    - Keep the explanation brief and to the point.
  criteria:
    accuracy: |
      Score 1: The result is completely unrelated to the reference.
      Score 3: The result has minor relevance but does not align with the reference.
      Score 5: The result has moderate relevance but contains inaccuracies.
      Score 7: The result aligns with the reference but has minor errors or omissions.
      Score 10: The result is completely accurate and aligns perfectly with the reference.
    hallucination: |
      Score 1: The facts are completely unrelated to their source quotes.
      Score 3: The facts have minor relevance but do not align with the source quotes.
      Score 5: The facts have moderate relevance to their source quotes but contain inaccuracies.
      Score 7: The facts align with the source quotes but have minor errors or omissions.
      Score 10: The facts are completely accurate and align perfectly with the source quotes.
summary: |
  You will receive a list of differences. You will create a summary of these differences.

  # Output Format
  {format_instructions}

system: |
  You are an expert in analyzing and explaining semantic differences in unidiff files.
  Your task is to analyze a given unidiff file and provide a structured explanation of the semantic differences between the original and modified versions.
  The input can be from any domain, including coding, medical, legal, or social conversation.

  # Output Format
  {format_instructions}

  # Guidelines for Analysis
  - Consider the context when identifying differences to provide a robust explanation of the changes, rather than merely describing line alterations.
  - Be thorough in your analysis, but focus on meaningful semantic changes rather than superficial alterations.
  - If the unidiff is domain-specific (such as code, law, or medical), take into account the domain context to elucidate the changes.
  - The final explanation should consider the entirety of the file, not just the highlighted differences.
  - For each identified difference:
    - Provide a clear, concise fact about the change.
    - Include only single-line quotes from the unidiff file to support the fact. Do not combine multiple lines into one quote.
  - In the explanation:
    - Summarize only the key semantic impacts of the changes.
    - Be brief and to the point, focusing on the most important differences.
  - Set semantic_diff to false if the semantic differences aren't substantial.