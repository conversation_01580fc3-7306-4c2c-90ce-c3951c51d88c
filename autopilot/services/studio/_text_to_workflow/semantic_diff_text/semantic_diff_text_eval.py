import json
import os
from pathlib import Path
from typing import Any

import numpy as np
import yaml  # type: ignore[import-untyped]
from langchain_core.messages import HumanMessage, SystemMessage
from numpy.typing import NDArray

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_schema import (
    EvalScore,
    SemanticDiffConfig,
    SemanticDiffEvalRequest,
)
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_task import SemanticDiffTextsTask
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.structured_outputs import async_generate_structured, async_llm_retry


def get_cosine_similarity(reference: str, prediction: str, model: EmbeddingModel) -> NDArray:
    """
    Computes the cosine similarity between reference and prediction strings using an Embedding model.

    Args:
        reference (str): Reference string to which prediction is compared.
        prediction (str): Prediction string which is compared to the reference.
        model (EmbeddingModel): The model used for generating embeddings for the comparison.

    Returns:
        NDArray: The cosine similarity score computed between reference and prediction text.
    """
    # Generate embeddings
    reference_embedding = model.encode(reference, instruction_set="icl", instruction_type="query")
    prediction_embedding = model.encode(prediction, instruction_set="icl", instruction_type="query")

    # Compute similarity
    reference_embedding_arr = np.array(reference_embedding)
    prediction_embedding_arr = np.array(prediction_embedding)

    local_score = (reference_embedding_arr @ prediction_embedding_arr.T) / (np.linalg.norm(reference_embedding_arr) * np.linalg.norm(prediction_embedding_arr))
    return local_score


async def llm_eval(prediction: str, reference: str, config: SemanticDiffConfig) -> EvalScore:
    """
    Runs the LLM evaluation with the specified prediction, reference, and config settings.

    Args:
        prediction (str): Prediction string which is going to be evaluated.
        reference (str): Reference string to compare the prediction against.
        config (SemanticDiffConfig): The configuration settings for the SemanticDiff that includes evaluation related attributes.

    Returns:
        EvalScore: An instance of EvalScore obtained by running the agenerate_structured model function on the formed evaluation messages.
    """
    eval: dict[str, Any] = config.evaluation
    system_content = f"{eval['prompt']}\n\n [Criteria]\n {yaml.dump(eval['criteria'])}"
    prediction_content = f"[Prediction]\n{prediction}\n\n[Reference]\n{reference}"
    eval_msg = [
        SystemMessage(content=system_content),
        HumanMessage(content=prediction_content),
    ]

    fn = async_llm_retry(max_attempts=config.max_retry_attempts, wait_fixed_time=config.retry_wait_fixed)(async_generate_structured)

    model = ModelManager().get_llm_model("semantic_diff_model", ConsumingFeatureType.SEMANTIC_DIFF)

    llm_score, _ = await fn(
        messages=eval_msg,
        model=model,
        response_model=EvalScore,
    )
    return llm_score


def load_request(path: Path) -> SemanticDiffEvalRequest:
    """
    Loads a SemanticDiffEvalRequest from a specified path.

    Args:
        path (Path): The path from which the request data is loaded.

    Returns:
        SemanticDiffEvalRequest: An instance of SemanticDiffEvalRequest with data loaded from provided path location.
    """
    # Load Diffs
    with open(path / "unidiff.diff", encoding="utf8") as file:
        diff = file.read()

    # Load LUTs
    with open(path / "lut.json", "r") as file:
        lut = json.load(file)

    # Load SSOTs
    with open(path / "ssot.yaml", encoding="utf8") as file:
        ssot = file.read()

    return SemanticDiffEvalRequest(name=str(path), diff=diff, lut=lut, ref=ssot)


def load_request_set(path: Path) -> list[SemanticDiffEvalRequest]:
    """
    Loads a set of SemanticDiffEvalRequest from a specified path.

    Args:
        path (Path): The path from which the list of requests is loaded.

    Returns:
        list[SemanticDiffEvalRequest]: A list of SemanticDiffEvalRequest instances with data loaded from each subfolder
        in the provided path location.
    """
    folder_list = os.listdir(path)
    folder_list.sort()

    # Return list of Requests
    return [load_request(path / fldr) for fldr in folder_list]


async def evaluate(task: SemanticDiffTextsTask, request: SemanticDiffEvalRequest, model: EmbeddingModel) -> dict[str, Any] | str:
    """
    Evaluates the request using semantic similarity and LLM Eval.

    Args:
        task: The task instance that performs semantic difference analysis.
        request (SemanticDiffEvalRequest): The request object containing the necessary information for the evaluation.
        model (EmbeddingModel): The model used for embeddings in the evaluation.

    Returns:
        dict[str, Any] | str: A dictionary containing evaluation scores if successful, else an exception message as string.
        Returned dictionary includes 'name', 'similarity_score' derived from cosine similarity of reference and predicted data,
        and 'llm_score' as scores computed by llm_eval function.
    """
    try:
        result = await task.run(request.export())
        prediction = result.to_yaml()

        # Semantic Similarity Eval
        similarity_score = get_cosine_similarity(request.ref, prediction, model) * 100

        # LLM Eval
        llm_score = await llm_eval(prediction, request.ref, task.config)
    except Exception as ex:
        return str(ex)

    return {
        "name": request.name,
        "llm_score": llm_score.get_score(),
        "similarity_score": similarity_score,
    }
