from pathlib import Path

from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_schema import CitedSemanticDiff, SemanticDiffRequest
from services.studio._text_to_workflow.semantic_diff_text.semantic_diff_text_task import SemanticDiffTextsTask

TASK = SemanticDiffTextsTask(Path(__file__).parent / "prompt.yaml")


async def semantic_diff_texts_endpoint(request: SemanticDiffRequest) -> CitedSemanticDiff:
    response = await TASK.run(request)

    return response.export()
