# Semantic Diff Task

Based on the [Semantic Diff Spike](https://uipath.atlassian.net/wiki/x/fgIxfRQ) it processes request containing a `unidiff`. Using lang chain it returns a structured output in a async manner:

```
differences:
- fact: <a difference inferred from the diff file>
  source_quotes: 
  - span: <an actual quote from the diff file>
	alpha: <line number where the span appears in the old document>
	beta: <line number where the span appears in the current document>
	delta: <line number where the span appears in the diff document>
  . . . 
. . .
explanation: <an explanation of the semantic differences between the two files>
semantic_diff: <a bool to easily know if the files are semantically different>
```

Features:
- Automatically handles retries and Structured Output errors via reasks mechanism.
- If the input is too large it is chuncked into partial models and then merged back into a single model via completion.
- Loads few shot examples from `resources/examples` to improve results.


## Eval

Evaluation is done by calculating the semantic similarity and LLM evaluation between prediction and references. The `resource` folder contains files with requests (`unidiff.diff` & `lut.json`) and references (`ssot.yaml`).

Contains a CLI that can run evals:
```
python main.py --evaluate lmyaml  # Runs just lmyaml set
python main.py --evaluate pdf_text  # Runs just pdf_text set
python main.py --evaluate both  # Runs both
```

Evals output:
```
Results:
0 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/00', 'llm_score': 5.0, 'similarity_score': 72.36948609352112}
1 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/01', 'llm_score': 1.0, 'similarity_score': 52.15010046958923}
2 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/02', 'llm_score': 1.0, 'similarity_score': 48.28376770019531}
3 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/04', 'llm_score': 8.5, 'similarity_score': 83.8708221912384}
4 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/05', 'llm_score': 5.0, 'similarity_score': 81.7531943321228}
5 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/06', 'llm_score': 5.0, 'similarity_score': 65.45417308807373}
6 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/07', 'llm_score': 3.0, 'similarity_score': 75.38015842437744}
7 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/08', 'llm_score': 5.0, 'similarity_score': 80.85044026374817}
8 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/09', 'llm_score': 5.0, 'similarity_score': 74.43883419036865}
9 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/10', 'llm_score': 5.0, 'similarity_score': 88.69932293891907}
10 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/11', 'llm_score': 5.0, 'similarity_score': 70.81971764564514}
11 - {'name': '/workspace/src/studio/text_to_workflow/semantic_diff_text/resources/pdf_text/13', 'llm_score': 5.0, 'similarity_score': 82.80715942382812}

Stats: 
 {'correlation': 0.7801020453459091, 'success_count': 12, 'err_count': 0, 'llm_score_min': 1.0, 'llm_score_mean': 4.458333333333333, 'llm_score_max': 8.5, 'similarity_score_min': 48.28376770019531, 'similarity_score_mean': 73.07309806346893, 'similarity_score_max': 88.69932293891907}
```

## Testing
Testing is done similarly to evaluation and can be triggered by:
```
cd studio/text_to_workflow/tests
pytest integration/test_semantic_diff.py
```


Notes:
- I now think the chunking architecture should be changed. Maybe triggered by an error then the loop decided on a fixing strategy.