import typing_extensions as t

from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse

TestDataGenerationMode: t.TypeAlias = t.Literal["Creative", "Balanced", "Precise"]


class TestDataGenerationParameter(t.TypedDict):
    name: str
    type: str
    direction: str


class TestDataGenerationRequest(BaseRequest):
    parameters: list[TestDataGenerationParameter]
    additionalTypeDefinitions: str | None
    implementation: str
    implementationFormat: str  # code or lmyaml
    implementationType: str  # workflow or testcase
    currentData: str | None  # <test-data-yaml>  json with data generated previously, list of dicts
    mode: TestDataGenerationMode  # Creative|Balanced|Precise, (temp 0.25/0.2/0.1)
    userRequest: str


class TestDataGenerationResult(t.TypedDict):
    output: str
    usage: TokenUsage


class TestDataGenerationResponse(BaseResponse):
    output: str
