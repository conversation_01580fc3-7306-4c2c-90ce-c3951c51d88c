import copy
from typing import Callable, List

import langchain_community.callbacks
import langchain_openai
import yaml
from langchain.prompts.chat import Chat<PERSON>romptTemplate, HumanMessagePromptTemplate
from langchain.schema.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage

from services.studio._text_to_workflow.common.walkers import CollapseWorkflowSubsequencesPruner
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.testdata_generation.testdata_generation_helpers import (
    format_result,
    get_config_path,
    load_testdata_yaml,
    remove_duplicate_data_from_current,
    strip_lmyaml,
)
from services.studio._text_to_workflow.testdata_generation.testdata_generation_retriever import TestdataGenerationRetriever
from services.studio._text_to_workflow.testdata_generation.testdata_generation_schema import TestDataGenerationRequest, TestDataGenerationResult
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.errors import BadRequestError, MissingCurrentActivityError
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time
from services.studio._text_to_workflow.utils.workflow_utils import load_completion_yaml, replace_blobs_with_hash
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

MAX_RETRY_CALLS = 5
NUMBER_OF_TOKENS_INACCURACY_TOLERANCE = 32
MAX_GENERATION_TOKENS = 32768

LOGGER = AppInsightsLogger()


class TestdataGenerationTask:
    config: dict
    demos: list[dict]

    def __init__(self) -> None:
        # retriever
        self.retriever = TestdataGenerationRetriever()
        self.load_config()
        # embed model
        self.embed_model = ModelManager().get_embeddings_model()

    def load_config(self) -> None:
        self.config = yaml_load(get_config_path())

    def load_demos_coded(self) -> None:
        self.demos = [
            yaml_load(file_path) for file_path in paths.get_testdata_generation_dataset_path("code", "rpa_testing").glob("*.yaml") if file_path.is_file()
        ]

        self.demos.extend(
            [yaml_load(file_path) for file_path in paths.get_testdata_generation_dataset_path("code", "app_testing").glob("*.yaml") if file_path.is_file()]
        )

    def load_demos_yaml_to_code(self, request) -> None:
        all_demos = [
            yaml_load(file_path) for file_path in paths.get_testdata_generation_dataset_path("lmyaml", "lmyaml_to_code").glob("*.yaml") if file_path.is_file()
        ]

        process_name_embedding = self.embed_model.encode(self.get_name_from_lmyaml(request))
        demos_embedding = [self.embed_model.encode(demo["name"]) for demo in all_demos]
        indices = self.retriever.get_relevant_demos_indices(process_name_embedding, demos_embedding)

        self.demos = [all_demos[i] for i in indices]

    def get_name_from_lmyaml(self, request: TestDataGenerationRequest) -> str:
        lmyaml = yaml_load(request["implementation"])
        process_name = lmyaml["processName"]
        return process_name

    def load_demos_code_to_testdata(self) -> None:
        self.demos = [
            yaml_load(file_path) for file_path in paths.get_testdata_generation_dataset_path("lmyaml", "code_to_testdata").glob("*.yaml") if file_path.is_file()
        ]

    async def run(self, request: TestDataGenerationRequest) -> TestDataGenerationResult:
        llm_model = ModelManager().get_llm_model("testdata_generation_model", ConsumingFeatureType.TESTDATA_GENERATION)

        implementation_format = request["implementationFormat"].lower()
        if implementation_format == "code":
            return await self.generate_testdata_from_coded(llm_model, request, self.load_demos_coded)
        elif implementation_format == "lmyaml":
            code = await self.generate_code_from_lmyaml(llm_model, request)
            request["implementation"] = code
            return await self.generate_testdata_from_coded(llm_model, request, self.load_demos_code_to_testdata)
        else:
            raise BadRequestError(f"Implementation format {implementation_format} is not supported.")

    @log_execution_time("TestdataGenerationFromCoded")
    async def generate_testdata_from_coded(
        self,
        llm_model: langchain_openai.AzureChatOpenAI,
        request: TestDataGenerationRequest,
        load_demos_function: Callable,
    ) -> TestDataGenerationResult:
        system_msg = SystemMessage(content=self.config["testdata_generation"]["system_template_instructions"])
        messages: list[BaseMessage] = [system_msg]

        self.add_demos_to_messages(messages, load_demos_function)

        result, usage = await self.generate_testdata(messages, llm_model, request)

        current_data_yaml = request.get("currentData", "")

        # list of current data
        if current_data_yaml:
            current_data = load_completion_yaml(current_data_yaml).get("test_data", [])
        else:
            current_data = []
        result = format_result(result, request["parameters"], current_data)

        if result["requested_entries"] > len(result["test_data"]):
            extra_data = await self.generate_extra_testdata(request, result)
            result["test_data"].extend(extra_data)
        if current_data:
            remove_duplicate_data_from_current(result, current_data)
        if len(result["test_data"]) > result["requested_entries"]:
            result["test_data"] = result["test_data"][: result["requested_entries"]]
        del result["requested_entries"]
        return TestDataGenerationResult(output=yaml_dump(result), usage=usage)

    @log_execution_time("TestdataGeneration")
    async def generate_testdata(
        self,
        messages: list[BaseMessage],
        llm_model: langchain_openai.AzureChatOpenAI,
        request: TestDataGenerationRequest,
    ) -> tuple[str, TokenUsage]:
        user_msg = HumanMessagePromptTemplate.from_template(self.config["testdata_generation"]["user_template"]).format(
            user_request=request["userRequest"],
            parameters="\n" + yaml_dump(request["parameters"], default_flow_style=True),
            code=request["implementation"],
        )
        messages.append(user_msg)

        current_data = request.get("currentData", None)

        current_data_dict = None

        # current data is expected to have the same format as the output of the result
        # an yaml with 'test_data' field that contains a list of the variations
        if current_data:
            current_data_dict = load_completion_yaml(current_data)
            user_msg = HumanMessagePromptTemplate.from_template(self.config["testdata_generation"]["current_data_template"]).format(
                user_request=request["userRequest"],
                parameters="\n" + yaml.safe_dump(request["parameters"]),
                code=request["implementation"],
                current_data=yaml.safe_dump({"existing_test_data": current_data_dict["test_data"]}),
            )
        else:
            user_msg = HumanMessagePromptTemplate.from_template(self.config["testdata_generation"]["user_template"]).format(
                user_request=request["userRequest"],
                parameters="\n" + yaml.safe_dump(request["parameters"]),
                code=request["implementation"],
            )
        messages.append(user_msg)

        template = ChatPromptTemplate.from_messages(messages)

        chat_chain = template | llm_model

        with langchain_community.callbacks.get_openai_callback() as cb:
            result_yaml = (await chat_chain.ainvoke({})).content
            usage = TokenUsage(
                model=llm_model.deployment_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        try:
            result = load_testdata_yaml(result_yaml)
        except Exception as e:
            result = {"test_data": []}
            LOGGER.error(f"Error loading testdata yaml: {e}")
        # if the model fails to respond with "requested_entries" will generate 15 as default
        result["requested_entries"] = int(result.get("requested_entries", 15))
        return result, usage

    @log_execution_time("TestdataGenerationFromLmyaml")
    async def generate_code_from_lmyaml(
        self,
        llm_model: langchain_openai.AzureChatOpenAI,
        request: TestDataGenerationRequest,
    ) -> str:
        system_msg = SystemMessage(content=self.config["lmyaml_to_pseudocode_generation"]["system_template_instructions"])
        messages: list[BaseMessage] = [system_msg]

        self.add_demos_to_messages(messages, self.load_demos_yaml_to_code, request=request)

        prompt_maximum_tokens = MAX_GENERATION_TOKENS - llm_model.max_tokens - NUMBER_OF_TOKENS_INACCURACY_TOLERANCE
        user_msg_template = HumanMessagePromptTemplate.from_template(self.config["lmyaml_to_pseudocode_generation"]["user_template"])
        user_msg = self.prune_workflow(
            request["implementation"],
            messages,
            {},
            user_msg_template,
            llm_model,
            prompt_maximum_tokens,
        )

        messages.append(user_msg)

        template = ChatPromptTemplate.from_messages(messages)

        chat_chain = template | llm_model

        result = (await chat_chain.ainvoke({})).content
        return result

    def add_demos_to_messages(self, messages: List[BaseMessage], load_demos_function: Callable, **kwargs) -> None:
        load_demos_function(**kwargs)
        for demo in self.demos:
            demo_user_msg = HumanMessage(content=demo["user_message"], example=True)
            demo_ai_msg = AIMessage(content=demo["assistant_message"], example=True)
            messages.extend([demo_user_msg, demo_ai_msg])

    def get_extra_testdata_demo_messages(self) -> list[BaseMessage]:
        system_msg = SystemMessage(content=self.config["additional_data"]["system_template_instructions"])
        messages: list[BaseMessage] = [system_msg]

        all_demos = [yaml_load(file_path) for file_path in paths.get_testdata_generation_dataset_path("extra_data", "").glob("*.yaml") if file_path.is_file()]

        for demo in all_demos:
            demo_user_msg = HumanMessage(content=demo["user_message"], example=True)
            demo_ai_msg = AIMessage(content=demo["assistant_message"], example=True)
            messages.append(demo_user_msg)
            messages.append(demo_ai_msg)

        return messages

    async def generate_extra_testdata(self, request: TestDataGenerationRequest, result: dict) -> list:
        llm_model = ModelManager().get_llm_model("testdata_generation_extra_data_model", ConsumingFeatureType.TESTDATA_GENERATION)

        messages: list[BaseMessage] = self.get_extra_testdata_demo_messages()

        open_ai_calls = 1
        extra_data = []
        current_test_data = result["test_data"]
        while (len(current_test_data) < result["requested_entries"]) and open_ai_calls < MAX_RETRY_CALLS:
            open_ai_calls = open_ai_calls + 1
            extra_result = await self.request_extra_data(llm_model, request, messages, current_test_data)
            current_test_data.extend(extra_result["test_data"])
            extra_data.extend(extra_result["test_data"])
        return extra_data

    def prune_workflow(
        self,
        workflow: str,
        gen_messages: list[BaseMessage],
        inputs: dict,
        user_message_template: HumanMessagePromptTemplate,
        gen_model: langchain_openai.AzureChatOpenAI,
        prompt_maximum_tokens: int,
    ) -> BaseMessage:
        existing_workflow_dict = yaml_load(workflow)
        try:
            existing_workflow_object = Workflow("", "", existing_workflow_dict)
            for _ in CollapseWorkflowSubsequencesPruner().iteratively_prune_workflow(existing_workflow_object):
                inputs["lmyaml"] = existing_workflow_object.lmyaml()
                user_message = user_message_template.format(**inputs)
                num_tokens = gen_model.get_num_tokens_from_messages(gen_messages + [user_message])
                if num_tokens < prompt_maximum_tokens:
                    return user_message
        except MissingCurrentActivityError:
            inputs["lmyaml"], _ = replace_blobs_with_hash(strip_lmyaml(workflow), use_b64_charset=True)
            return user_message_template.format(**inputs)

    async def request_extra_data(
        self,
        llm_model: langchain_openai.AzureChatOpenAI,
        request: TestDataGenerationRequest,
        messages: list[BaseMessage],
        current_test_data: dict,
    ) -> list:
        new_messages = copy.deepcopy(messages)

        current_test_data_yaml = yaml_dump({"test_data": current_test_data}, default_flow_style=True)
        user_msg = HumanMessagePromptTemplate.from_template(self.config["additional_data"]["user_template"]).format(
            code=request["implementation"],
            parameters=yaml_dump(request["parameters"], default_flow_style=True),
            test_data=current_test_data_yaml,
        )
        new_messages.append(user_msg)

        template = ChatPromptTemplate.from_messages(new_messages)

        chat_chain = template | llm_model

        with langchain_community.callbacks.get_openai_callback():
            result_yaml = (await chat_chain.ainvoke({})).content

        result = load_testdata_yaml(result_yaml)

        curated_result = format_result(result, request["parameters"], current_test_data)
        return curated_result
