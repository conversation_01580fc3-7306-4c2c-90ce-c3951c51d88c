from services.studio._text_to_workflow.testdata_generation.testdata_generation_schema import TestDataGenerationRequest, TestDataGenerationResponse
from services.studio._text_to_workflow.testdata_generation.testdata_generation_task import TestdataGenerationTask

_testdata_generation_task = TestdataGenerationTask()


def init():
    global _testdata_generation_task


async def generate(request: TestDataGenerationRequest) -> TestDataGenerationResponse:
    result = await _testdata_generation_task.run(request)

    return {"output": result["output"], "usage": result["usage"].to_json(), "fingerprints": []}
