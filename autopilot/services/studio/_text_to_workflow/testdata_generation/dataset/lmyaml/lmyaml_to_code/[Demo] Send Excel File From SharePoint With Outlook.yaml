name: Send Excel File From SharePoint With Outlook
user_message: |-
  Generate C# pseudocode for this yaml:

  processName: "[Client] Send Excel File From SharePoint With Outlook"
  packages:
  - UiPath.IntegrationService.Activities, v1.2.0-beta.20240129.16
  - UiPath.MicrosoftOffice365.Activities, v2.5.7
  - UiPath.System.Activities, v23.10.4
  - UiPath.WebAPI.Activities, v1.18.0
  trigger:
    thought: "Manual Trigger"
    displayName: "Manual Trigger"
    activity: UiPath.Core.Activities.ManualTrigger
    id: ManualTrigger_1
    params:
      Result: "[[_out_ManualTrigger_1__Result]]"
  workflow:
  - thought: "Search For Excel File"
    displayName: "Search For Excel File"
    activity: UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
    id: GetFileListConnections_1
    params:
      BrowserItemFriendlyName: "Documents"
      BrowserSpecificUrl: "https://1drv.ms/f/s!ANZ4qoWebMFhZg"
      ConnectionId: "78df4a9d-38d4-476d-9786-847f5c69f45d"
      IncludeSubfolders: "False"
      ItemSelectionMode: "Browse"
      MaxResults: "10"
      SearchMode: "UseSimple"
      TrimDuplicates: "False"
      UseConnectionService: "True"
      WhatToReturn: "Files"
      Result: "[[FoundFiles]]"
  - thought: "If a unique file was found"
    displayName: "If a unique file was found"
    activity: System.Activities.Statements.If
    id: If_1
    params:
      Condition: "[[FoundFiles.Count() == 1]]"
      Then:
      - thought: "Send Email with Excel File"
        displayName: "Send Email with Excel File"
        activity: UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections
        id: SendMailConnections_1
        params:
          AttachmentInputMode: "Existing"
          AttachmentsBackup:
            StoredValue: "Existing"
          Body: "Please find the attached Excel file from SharePoint."
          ConnectionId: "8c4a3443-51c9-492e-ac46-def084f0e3c5"
          Importance: "Normal"
          InputType: "HTML"
          InputTypeBackup:
            StoredValue: "HTML"
          SaveAsDraft: "False"
          Subject: "Excel File from SharePoint"
          UseConnectionService: "True"
          Attachments: "[[new[] { FoundFiles.First() }]]"
          To: "[[new[] { EmailToSendExcel }]]"
      Else:
      - thought: "Could Not Find A Unique Excel File Message"
        displayName: "Could Not Find A Unique Excel File Message"
        activity: UiPath.Core.Activities.LogMessage
        id: LogMessage_1
        params:
          Level: "[[LogLevel.Warn]]"
          Message: "[[\\\"Could not find a unique Excel file based on search criteria\\\"]]"
  variables:
  - name: _out_ManualTrigger_1__Result
    type: UiPath.Core.Activities.CurrentJobInfo
  - name: FoundFiles
    type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[]
  arguments:
  - direction: In
    name: ExcelFileName
    type: System.String
  - direction: In
    name: EmailToSendExcel
    type: System.String
assistant_message: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public void Execute(string ExcelFileName, string EmailToSendExcel)
    {
      // Declare variables
      UiPath.Core.Activities.CurrentJobInfo _out_ManualTrigger_1__Result;
      UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[] FoundFiles;

      // Manually trigger the process

      // Search for the Excel file in the specified SharePoint URL path using 'ExcelFileName'
      FoundFiles = UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
      (
        BrowserItemFriendlyName: "Documents",
        BrowserSpecificUrl: "https://1drv.ms/f/s!ANZ4qoWebMFhZg",
        ConnectionId: "78df4a9d-38d4-476d-9786-847f5c69f45d",
        IncludeSubfolders: "False",
        ItemSelectionMode: "Browse",
        MaxResults: "10",
        SearchMode: "UseSimple",
        TrimDuplicates: "False",
        UseConnectionService: "True",
        WhatToReturn: "Files",
        Result: "[[FoundFiles]]",
        SimpleSearch: $"\\\"{ExcelFileName}\\\""
      );

      // If exactly one file is found in 'FoundFiles'
      if (FoundFiles.Count() == 1)
      {
        // Send an email with the Excel file attached
        UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections
        (
          AttachmentInputMode: "Existing",
          AttachmentsBackup: { StoredValue: "Existing" },
          Body: "Please find the attached Excel file from SharePoint.",
          ConnectionId: "8c4a3443-51c9-492e-ac46-def084f0e3c5",
          Importance: "Normal",
          InputType: "HTML",
          InputTypeBackup: { StoredValue: "HTML" },
          SaveAsDraft: "False",
          Subject: "Excel File from SharePoint",
          UseConnectionService: "True",
          Attachments: new[] { FoundFiles.First() },
          To: new[] { EmailToSendExcel }
        );
      }
      else
      {
        // Could not find a unique Excel file Message
        UiPath.Core.Activities.LogMessage
        (
          Level: LogLevel.Warn,
          Message: "Could not find a unique Excel file based on search criteria"
        );
      }
    }
  }
  ```