name: Check email sender in salesforce and reply with openAI
user_message: |-
  Generate C# pseudocode for this yaml:
  
  processName: "[Client] Check Email Sender In Salesforce And Reply With OpenAI"
  packages:
  - UiPath.GSuite.Activities, v2.6.0-preview
  - UiPath.IntegrationService.Activities, v1.3.0-beta.20240219.9
  - UiPath.System.Activities, v24.2.0-preview
  - UiPath.WebAPI.Activities, v1.19.0-preview
  trigger:
    thought: "New Email in Gmail"
    displayName: "New Email in Gmail"
    activity: UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
    id: NewEmailReceived_1
    params:
      BrowserFolderName: "Inbox"
      FilterExpression: "(ParentFolders[?ID=='INBOX'])"
      IncludeAttachments: "False"
      MarkAsRead: "True"
      UseConnectionService: "True"
      WithAttachmentsOnly: "False"
      Result: "[[Email]]"
  workflow:
  - thought: "Check if sender is a contact in Salesforce"
    displayName: "Check if sender is a contact in Salesforce"
    activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
    id: ConnectorActivity_1
    isDynamic: True
    isConfigured: True
    params:
      ConnectionId: "5b74038e-d54a-4b4c-b38b-9da43831e69c"
      UiPathActivityTypeId: "f4e836f2-7b5f-3770-ad25-90d460867eb3"
      curated_contactId: "[[Email.FromAddress]]"
      Jit_curated_contact: "[[Contact]]"
  - thought: "Send Message To User"
    displayName: "Send Message To User"
    activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
    id: ConnectorActivity_4
    isDynamic: True
    isConfigured: True
    params:
      ConnectionId: "917ef207-cebc-483b-beef-a2e6c41c1521"
      send_as: "bot"
      UiPathActivityTypeId: "9a76a063-8903-3101-b158-e0435e77c45e"
      channel: "[[SlackName]]"
      text: "[[string.Format(\\\"The sender of the last email, {0}, is a contact in Salesforce. Preparing to send reply to them.\\\", Email.FromAddress)]]"
  - thought: "Use Azure OpenAI to generate a reply"
    displayName: "Use Azure OpenAI to generate a reply"
    activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
    id: ConnectorActivity_3
    isDynamic: True
    isConfigured: True
    params:
      apiminus_signversion: "2023-10-01-preview"
      best_of: "1"
      ConnectionId: "ca0467aa-15e4-45d2-8040-b5c6a02bca5b"
      echo: "False"
      frequency_penalty: "0"
      max_tokens: "150"
      model: "text-ada-001"
      n: "1"
      presence_penalty: "0"
      stream: "False"
      temperature: "1"
      top_p: "1"
      UiPathActivityTypeId: "c2289d22-be3e-31c3-9293-e9ffc987b351"
      out_text: "[[Reply]]"
      prompt: "[[string.Format(\\\"Generate a reply to the following email: {0}\\\", Email.Body)]]"
  - thought: "Send the generated reply to the sender"
    displayName: "Send the generated reply to the sender"
    activity: UiPath.GSuite.Activities.ReplyToEmailConnections
    id: ReplyToEmailConnections_1
    params:
      AttachmentInputMode: "UseExisting"
      AttachmentsBackup:
        StoredValue: "UseExisting"
      Importance: "Normal"
      ReplyToAll: "False"
      SaveAsDraft: "False"
      UseConnectionService: "True"
      Body: "[[Reply]]"
      Email: "[[Email]]"
  variables:
  - name: Email
    type: UiPath.GSuite.Models.GmailMessage
  - name: Contact
    type: UiPath.IntegrationService.Activities.SWEntities.CA43831E69C_curated_contact_Retrieve.Bundle.curated_contact_Retrieve
  - name: Reply
    type: System.String
  arguments:
  - direction: In
    name: SlackName
    type: System.String
assistant_message: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public void Execute(string SlackName)
    {
      // Variables
      UiPath.GSuite.Models.GmailMessage Email;
      UiPath.IntegrationService.Activities.SWEntities.CA43831E69C_curated_contact_Retrieve.Bundle.curated_contact_Retrieve Contact;
      string Reply;

      // Trigger: New Email in Gmail
      Email = NewEmailReceived(
        BrowserFolderName = "Inbox",
        FilterExpression = "(ParentFolders[?ID=='INBOX'])",
        IncludeAttachments = "False",
        MarkAsRead = "True",
        UseConnectionService = "True",
        WithAttachmentsOnly = "False"
      )
      
      // Check if sender is a contact in Salesforce
      Contact = ConnectorActivity(
        ConnectionId: "5b74038e-d54a-4b4c-b38b-9da43831e69c",
        UiPathActivityTypeId: "f4e836f2-7b5f-3770-ad25-90d460867eb3",
        curated_contactId: "[[Email.FromAddress]]"
      );
      
      if (contact != null) 
      {
        // Send Message to User via slack 
        ConnectorActivity(
          ConnectionId = "917ef207-cebc-483b-beef-a2e6c41c1521",
          send_as = "bot",
          UiPathActivityTypeId = "9a76a063-8903-3101-b158-e0435e77c45e",
          channel = "[[SlackName]]",
          text = "[[string.Format(\\\"The sender of the last email, {0}, is a contact in Salesforce. Preparing to send reply to them.\\\", Email.FromAddress)]]"
        )

        // Use Azure OpenAI to generate a reply
        Reply = GenerateReplyUsingOpenAI(
          apiminus_signversion = "2023-10-01-preview",
          best_of = "1",
          ConnectionId = "ca0467aa-15e4-45d2-8040-b5c6a02bca5b",
          echo = "False",
          frequency_penalty = "0",
          max_tokens = "150",
          model = "text-ada-001",
          n = "1",
          presence_penalty = "0",
          stream = "False",
          temperature = "1",
          top_p = "1",
          UiPathActivityTypeId = "c2289d22-be3e-31c3-9293-e9ffc987b351",
          prompt = "[[string.Format(\\\"Generate a reply to the following email: {0}\\\", Email.Body)]]"
        )
        // Send the generated reply to the sender
        ReplyToEmailConnections(
          AttachmentInputMode = "UseExisting",
          AttachmentsBackup = { StoredValue: "UseExisting" },
          Importance = "Normal",
          ReplyToAll = "False",
          SaveAsDraft = "False",
          UseConnectionService = "True",
          Body = "[[Reply]]",
          Email = "[[Email]]"
        )
      }
    }
  }
  ```