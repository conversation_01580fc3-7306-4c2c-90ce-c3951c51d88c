name: Count emails from domain
user_message: |-
  Generate C# pseudocode for this yaml:
  
  processName: "[Client] Count Emails From Domain"
  packages:
  - UiPath.MicrosoftOffice365.Activities, v2.6.0-preview
  - UiPath.System.Activities, v24.2.0-preview
  - UiPath.WebAPI.Activities, v1.19.0-preview
  trigger:
    thought: "Every day at 12 AM"
    displayName: "Every day at 12 AM"
    activity: UiPath.Core.Activities.TimeTrigger
    id: TimeTrigger_1
    params:
      CronExpression: "0 0 0 ? * *"
      Result: "[[_out_TimeTrigger_1__Result]]"
  workflow:
  - thought: "Get Emails From Domain"
    displayName: "Get Emails From Domain"
    activity: UiPath.MicrosoftOffice365.Activities.Mail.GetEmailListConnections
    id: GetEmailListConnections_1
    params:
      BrowserFolder: "Inbox"
      ConnectionId: "8c4a3443-51c9-492e-ac46-def084f0e3c5"
      Filter:
        LogicalOperator: "And"
        Filters:
        - Criteria: "Date"
          DateOperator: "NewerThan"
          StringOperator: "Contains"
          TypeOperator: "Equals"
          TypeValue: "Email"
          DateValue: "[[DateTime.Now.AddDays(-1)]]"
        - Criteria: "From"
          DateOperator: "NewerThan"
          StringOperator: "Contains"
          TypeOperator: "Equals"
          TypeValue: "Email"
          InStringValue: "[[Domain]]"
      Importance: "Any"
      IncludeSubfolders: "False"
      MarkAsRead: "False"
      MaxResults: "1000"
      OrderByDate: "NewestFirst"
      SelectionMode: "Browse"
      UnreadOnly: "False"
      UseConnectionService: "True"
      UseSharedMailbox: "False"
      WithAttachmentsOnly: "False"
      EmailList: "[[_out_GetEmailListConnections_1__EmailList]]"
  - thought: "Set Number Of Emails From Domain"
    displayName: "Set Number Of Emails From Domain"
    activity: UiPath.Core.Activities.Assign<System.Int32>
    id: Assign_4
    params:
      To: "[[NumberOfEmailsFromDomain]]"
      Value: "[[_out_GetEmailListConnections_1__EmailList.Count]]"
  variables:
  - name: _out_TimeTrigger_1__Result
    type: UiPath.Core.Activities.CurrentJobInfo
  - name: _out_GetEmailListConnections_1__EmailList
    type: System.Collections.Generic.List<UiPath.MicrosoftOffice365.Models.Office365Message>
  arguments:
  - direction: Out
    name: NumberOfEmailsFromDomain
    type: System.Int32
  - direction: In
    name: Domain
    type: System.String
assistant_message: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public int Execute(string Domain)
    {
      // Declare variables
      UiPath.Core.Activities.CurrentJobInfo _out_TimeTrigger_1__Result;
      List<UiPath.MicrosoftOffice365.Models.Office365Message> _out_GetEmailListConnections_1__EmailList;

      // Trigger: Every day at 12 AM

      // Get emails from the specified domain received in the last 24 hours
      _out_GetEmailListConnections_1__EmailList = UiPath.MicrosoftOffice365.Activities.Mail.GetEmailListConnections
      (
        BrowserFolder = "Inbox",
        ConnectionId = "8c4a3443-51c9-492e-ac46-def084f0e3c5",
        Filter = new EmailFilter
        {
          LogicalOperator = "And",
          Filters = new List<EmailFilterCriteria>
          {
            new EmailFilterCriteria
            {
              Criteria = "Date",
              DateOperator = "NewerThan",
              StringOperator = "Contains",
              TypeOperator = "Equals",
              TypeValue = "Email",
              DateValue = DateTime.Now.AddDays(-1)
            },
            new EmailFilterCriteria
            {
              Criteria = "From",
              DateOperator = "NewerThan",
              StringOperator = "Contains",
              TypeOperator = "Equals",
              TypeValue = "Email",
              InStringValue = Domain
            }
          }
        },
        Importance = "Any",
        IncludeSubfolders = "False",
        MarkAsRead = "False",
        MaxResults = "1000",
        OrderByDate = "NewestFirst",
        SelectionMode = "Browse",
        UnreadOnly = "False",
        UseConnectionService = "True",
        UseSharedMailbox = "False",
        WithAttachmentsOnly = "False"
      );

      // Set the number of emails from the specified domain
      NumberOfEmailsFromDomain = _out_GetEmailListConnections_1__EmailList.Count;

      return NumberOfEmailsFromDomain;
    }
  }
  ```