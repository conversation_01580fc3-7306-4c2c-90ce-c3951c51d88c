name: For each file in folder
user_message: |-
  Generate C# pseudocode for this yaml:
  
  processName: \"ForEachFileInFolder\"
  packages:
  - UiPath.System.Activities, v[24.3.1]
  - UiPath.Testing.Activities, v[23.10.0]
  - UiPath.UIAutomation.Activities, v[23.10.7]
  - UiPath.WebAPI.Activities, v[1.20.0]
  workflow:
  - thought: \"For Each File in Folder\"
    displayName: \"For Each File in Folder\"
    activity: UiPath.Core.Activities.ForEachFileX
    id: ForEachFileX_1
    params:
      IncludeSubDirectories: \"False\"
      OrderBy: \"NameAscFirst\"
      SkipFolderWithoutPermission: \"False\"
      Filter: \"[[Path.ChangeExtension(path, \"pdf\")]]\"
      Folder: \"[[path]]\"
      Body:
        variables:
        - name: \"CurrentFile\"
          type: \"System.IO.FileInfo\"
        - name: \"CurrentIndex\"
          type: \"System.Int32\"
        Handler:
        - thought: \"If\"
          displayName: \"If\"
          activity: System.Activities.Statements.If
          id: If_1
          params:
            Condition: \"[[CurrentFile.Name.Contains(pattern)]]\"
            Then:
              thought: \"Then\"
              displayName: \"Then\"
              activity: System.Activities.Statements.Sequence
              id: Sequence_3
              params:
                Activities:
                - thought: \"write line to file\"
                  displayName: \"write line to file\"
                  activity: UiPath.Core.Activities.LogMessage
                  id: WriteLine_2
                  params:
                    Message: \"[[line]]\"
        DisplayName: \"Do\"
  arguments:
  - direction: In
    name: path
    type: System.String
  - direction: In
    name: pattern
    type: System.String
  - direction: In
    name: line
    type: System.String
assistant_message: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public void Execute(string path, string pattern, string line)
    {
      // For each file in the folder
      foreach (System.IO.FileInfo CurrentFile in new System.IO.DirectoryInfo(path).GetFiles().Where(f => f.Extension == ".pdf"))
      {
        // If the file name contains the specified pattern
        if (CurrentFile.Name.Contains(pattern))
        {
          // Write the line to a file
          UiPath.Core.Activities.LogMessage
          (
            Message: line
          );
        }
      }
    }
  }
  ```