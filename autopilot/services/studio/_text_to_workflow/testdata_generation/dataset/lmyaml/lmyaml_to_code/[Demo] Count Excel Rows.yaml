name: Count excel rows
user_message: |-
  Generate C# pseudocode for this yaml:

  processName: "[Client] Count Excel Rows"
  packages:
  - UiPath.MicrosoftOffice365.Activities, v2.5.7
  - UiPath.System.Activities, v24.2.0-preview
  - UiPath.Testing.Activities, v[24.4.0-preview]
  - UiPath.WebAPI.Activities, v1.19.0-preview
  trigger:
    thought: "Manual Trigger"
    displayName: "Manual Trigger"
    activity: UiPath.Core.Activities.ManualTrigger
    id: ManualTrigger_1
    params:
      Result: "[[_out_ManualTrigger_1__Result]]"
  workflow:
  - thought: "Search For Excel File"
    displayName: "Search For Excel File"
    activity: UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
    id: GetFileListConnections_1
    params:
      BrowserItemFriendlyName: "Documents"
      BrowserSpecificUrl: "https://1drv.ms/f/s!ANZ4qoWebMFhZg"
      ConnectionId: "78df4a9d-38d4-476d-9786-847f5c69f45d"
      IncludeSubfolders: "False"
      ItemSelectionMode: "Browse"
      MaxResults: "10"
      SearchMode: "UseSimple"
      TrimDuplicates: "False"
      UseConnectionService: "True"
      WhatToReturn: "Files"
      Result: "[[RetrievedFiles]]"
      SimpleSearch: "[[string.Format(\\\"{0}\\\", ExcelName)]]"
  - thought: "If a unique file was found"
    displayName: "If a unique file was found"
    activity: System.Activities.Statements.If
    id: If_1
    params:
      Condition: "[[RetrievedFiles.Count() == 1]]"
      Then:
        thought: "Then"
        displayName: "Then"
        activity: System.Activities.Statements.Sequence
        id: Sequence_5
        params:
          Activities:
          - thought: "Read Excel"
            displayName: "Read Excel"
            activity: UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections<System.Data.DataTable>
            id: ReadRangeConnections`1_1
            params:
              ConnectionId: "78df4a9d-38d4-476d-9786-847f5c69f45d"
              HasHeaders: "True"
              ItemSelectionMode: "UseExisting"
              ReadAs: "Values"
              UseConnectionService: "True"
              WorkingWithTemplateWorkbook: "False"
              Item: "[[RetrievedFiles.First()]]"
              Range: "[[SheetName]]"
              Result: "[[ReadRangeExcel]]"
          - thought: "Set Rows Count"
            displayName: "Set Rows Count"
            activity: UiPath.Core.Activities.Assign<System.Int32>
            id: Assign_2
            params:
              To: "[[ExcelRows]]"
              Value: "[[ReadRangeExcel.Rows.Count]]"
      Else:
        thought: "Else"
        displayName: "Else"
        activity: System.Activities.Statements.Sequence
        id: Sequence_4
        params:
          Activities:
          - thought: "Could not find a unique file Message"
            displayName: "Could not find a unique file Message"
            activity: UiPath.Core.Activities.LogMessage
            id: LogMessage_1
            params:
              Level: "[[LogLevel.Warn]]"
              Message: "[[\\\"Could not find a unique file to match the search criteria\\\"]]"
          - thought: "Set Default Excel Rows"
            displayName: "Set Default Excel Rows"
            activity: UiPath.Core.Activities.Assign<System.Int32>
            id: Assign_1
            params:
              To: "[[ExcelRows]]"
              Value: "[[0]]"
  variables:
  - name: _out_ManualTrigger_1__Result
    type: UiPath.Core.Activities.CurrentJobInfo
  - name: RetrievedFiles
    type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[]
  - name: ReadRangeExcel
    type: System.Data.DataTable
  arguments:
  - direction: In
    name: ExcelName
    type: System.String
  - direction: Out
    name: ExcelRows
    type: System.Int32
  - direction: In
    name: SheetName
    type: System.String
assistant_message: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public int Execute(string ExcelName, string SheetName)
    {
      // Declare variables
      UiPath.Core.Activities.CurrentJobInfo _out_ManualTrigger_1__Result;
      UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[] RetrievedFiles;
      System.Data.DataTable ReadRangeExcel;

      // Manually trigger the process

      // Search for Excel file in specified OneDrive URL path using 'ExcelName'
      RetrievedFiles = UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
      (
        BrowserItemFriendlyName = "Documents",
        BrowserSpecificUrl = "https://1drv.ms/f/s!ANZ4qoWebMFhZg",
        ConnectionId = "78df4a9d-38d4-476d-9786-847f5c69f45d",
        IncludeSubfolders ="False",
        ItemSelectionMode = "Browse",
        MaxResults = "10",
        SearchMode = "UseSimple",
        TrimDuplicates = "False",
        UseConnectionService = "True",
        WhatToReturn = "Files",
        SimpleSearch = string.Format("{0}", ExcelName)
      );

      // If exactly one file is found in 'RetrievedFiles'
      if (RetrievedFiles.Count() == 1)
      {
        // Use the Read Range connection to read this Excel file (using 'SheetName') into 'ReadRangeExcel'
        ReadRangeExcel = UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections<System.Data.DataTable>
        (
          ConnectionId = "78df4a9d-38d4-476d-9786-847f5c69f45d",
          HasHeaders = "True",
          ItemSelectionMode = "UseExisting",
          ReadAs = "Values",
          UseConnectionService = "True",
          WorkingWithTemplateWorkbook = "False",
          Item = RetrievedFiles.First(),
          Range = SheetName
        );

        // Assign 'ExcelRows' to the number of rows in 'ReadRangeExcel'
        ExcelRows = ReadRangeExcel.Rows.Count;
      }
      else
      {
        // Could not find a unique file Message
        UiPath.Core.Activities.LogMessage
        (
          Level = LogLevel.Warn,
          Message = "Could not find a unique file to match the search criteria"
        );

        // Set Default Excel Rows
        ExcelRows = 0;
      }

      return ExcelRows;
    }
  }
  ```