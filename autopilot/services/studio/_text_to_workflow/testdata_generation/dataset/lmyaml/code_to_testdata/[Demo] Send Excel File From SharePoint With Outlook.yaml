name: send_excel_file
user_message: |-
  user_request: "Generate 18 test data"
  parameters:
    - name: "ExcelFileName"
      type: "string"
      direction: "in"
    - name: "EmailToSendExcel"
      type: "string"
      direction: "in"
  code: |-
    ```csharp
    public class Workflow
    {
      // Define the entry point function with arguments
      public void Execute(string ExcelFileName, string EmailToSendExcel)
      {
        // Declare variables
        UiPath.Core.Activities.CurrentJobInfo _out_ManualTrigger_1__Result;
        UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[] FoundFiles;

        // Manually trigger the process

        // Search for the Excel file in the specified SharePoint URL path using 'ExcelFileName'
        FoundFiles = UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
        (
          BrowserItemFriendlyName: "Documents",
          BrowserSpecificUrl: "https://1drv.ms/f/s!ANZ4qoWebMFhZg",
          ConnectionId: "78df4a9d-38d4-476d-9786-847f5c69f45d",
          IncludeSubfolders: "False",
          ItemSelectionMode: "Browse",
          MaxResults: "10",
          SearchMode: "UseSimple",
          TrimDuplicates: "False",
          UseConnectionService: "True",
          WhatToReturn: "Files",
          Result: "[[FoundFiles]]",
          SimpleSearch: $"\"{ExcelFileName}\""
        );

        // If exactly one file is found in 'FoundFiles'
        if (FoundFiles.Count() == 1)
        {
          // Send an email with the Excel file attached
          UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections
          (
            AttachmentInputMode: "Existing",
            AttachmentsBackup: { StoredValue: "Existing" },
            Body: "Please find the attached Excel file from SharePoint.",
            ConnectionId: "8c4a3443-51c9-492e-ac46-def084f0e3c5",
            Importance: "Normal",
            InputType: "HTML",
            InputTypeBackup: { StoredValue: "HTML" },
            SaveAsDraft: "False",
            Subject: "Excel File from SharePoint",
            UseConnectionService: "True",
            Attachments: new[] { FoundFiles.First() },
            To: new[] { EmailToSendExcel }
          );
        }
        else
        {
          // Could not find a unique Excel file Message
          UiPath.Core.Activities.LogMessage
          (
            Level: LogLevel.Warn,
            Message: "Could not find a unique Excel file based on search criteria"
          );
        }
      }
    }
    ```
assistant_message: |-
  requested_entries: 18
  test_data:
  - _thought: covers the case where the Excel file is found and email is sent
    _idx: 1
    ExcelFileName: SalesReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file is not found
    _idx: 2
    ExcelFileName: AnnualReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name is empty
    _idx: 3
    ExcelFileName: ''
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the email to send Excel is empty
    _idx: 4
    ExcelFileName: QuarterlyReport.xlsx
    EmailToSendExcel: ''
  - _thought: covers the case where both Excel file name and email to send Excel are empty
    _idx: 5
    ExcelFileName: ''
    EmailToSendExcel: ''
  - _thought: covers the case where the Excel file name contains special characters
    _idx: 6
    ExcelFileName: <EMAIL>
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the email to send Excel contains special characters
    _idx: 7
    ExcelFileName: MonthlyReport.xlsx
    EmailToSendExcel: <EMAIL>!
  - _thought: covers the case where the Excel file name and email to send Excel are very long
    _idx: 8
    ExcelFileName: QuarterlyReportQuarterlyReportQuarterlyReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel are null
    _idx: 9
    ExcelFileName: null
    EmailToSendExcel: null
  - _thought: covers the case where the Excel file name and email to send Excel are whitespace
    _idx: 10
    ExcelFileName: '   '
    EmailToSendExcel: '   '
  - _thought: covers the case where the Excel file name and email to send Excel contain leading and trailing spaces
    _idx: 11
    ExcelFileName: '  QuarterlyReport  '
    EmailToSendExcel: '  <EMAIL>  '
  - _thought: covers the case where the Excel file name and email to send Excel are case sensitive
    _idx: 12
    ExcelFileName: monthlyreport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel are case sensitive and correct
    _idx: 13
    ExcelFileName: MonthlyReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel are case sensitive and incorrect
    _idx: 14
    ExcelFileName: AnnualReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel contain numbers
    _idx: 15
    ExcelFileName: 2022_Report.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel contain special characters and numbers
    _idx: 16
    ExcelFileName: <EMAIL>
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel are very long and contain special characters
    _idx: 17
    ExcelFileName: QuarterlyReportQuarterlyReportQuarterlyReport.xlsx
    EmailToSendExcel: <EMAIL>
  - _thought: covers the case where the Excel file name and email to send Excel are very long and contain special characters and numbers
    _idx: 18
    ExcelFileName: QuarterlyReportQuarterlyReportQuarterlyReport.xlsx
    EmailToSendExcel: <EMAIL>