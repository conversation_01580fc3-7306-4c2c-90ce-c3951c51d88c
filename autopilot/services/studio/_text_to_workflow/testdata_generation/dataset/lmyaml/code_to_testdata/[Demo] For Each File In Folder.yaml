name: for_each_file_in_folder
user_message: |-
  user_request: "Generate test data"
  parameters:
    - name: "path"
      type: "string"
      direction: "in"
    - name: "pattern"
      type: "string"
      direction: "out"
    - name: "line"
      type: "string"
      direction: "in"
  code: |-
  ```csharp
  public class Workflow
  {
    // Define the entry point function with arguments
    public void Execute(string path, string pattern, string line)
    {
      // For each file in the folder
      foreach (System.IO.FileInfo CurrentFile in new System.IO.DirectoryInfo(path).GetFiles().Where(f => f.Extension == ".pdf"))
      {
        // If the file name contains the specified pattern
        if (CurrentFile.Name.Contains(pattern))
        {
          // Write the line to a file
          UiPath.Core.Activities.LogMessage
          (
            Message: line
          );
        }
      }
    }
  }
  ```
assistant_message: |-
  requested_entries: 15
  test_data:
  - _thought: covers the case where the file name contains the specified pattern
    _idx: 1
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name does not contain the specified pattern
    _idx: 2
    path: C:\\Documents\\Folder2
    pattern: doc
    line: Processing document
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 3
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the line is empty
    _idx: 4
    path: C:\\Files\\Folder1
    pattern: file
    line: ''
  - _thought: covers the case where the pattern is empty
    _idx: 5
    path: C:\\Files\\Folder1
    pattern: ''
    line: Processing file
  - _thought: covers the case where the path is empty
    _idx: 6
    path: ''
    pattern: file
    line: Processing file
  - _thought: covers the case where the line is null
    _idx: 7
    path: C:\\Files\\Folder1
    pattern: file
    line: null
  - _thought: covers the case where the path is null
    _idx: 8
    path: null
    pattern: file
    line: Processing file
  - _thought: covers the case where the pattern is null
    _idx: 9
    path: C:\\Files\\Folder1
    pattern: null
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 10
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 11
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 12
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 13
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 14
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
  - _thought: covers the case where the file name contains the specified pattern and line is relevant
    _idx: 15
    path: C:\\Files\\Folder1
    pattern: file
    line: Processing file
