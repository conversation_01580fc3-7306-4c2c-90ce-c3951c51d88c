name: check_email_sender_in_salesforce_and_reply_with_openAI
user_message: |-
  user_request: "Generate 10 test data"
  parameters:
    - name: "SlackName"
      type: "string"
      direction: "in"
  code: |-
    ```csharp
    public class Workflow
    {
      // Define the entry point function with arguments
      public void Execute(string SlackName)
      {
        // Variables
        UiPath.GSuite.Models.GmailMessage Email;
        UiPath.IntegrationService.Activities.SWEntities.CA43831E69C_curated_contact_Retrieve.Bundle.curated_contact_Retrieve Contact;
        string Reply;

        // Trigger: New Email in Gmail
        Email = NewEmailReceived(
          BrowserFolderName = "Inbox",
          FilterExpression = "(ParentFolders[?ID=='INBOX'])",
          IncludeAttachments = "False",
          MarkAsRead = "True",
          UseConnectionService = "True",
          WithAttachmentsOnly = "False"
        )
        
        // Check if sender is a contact in Salesforce
        Contact = ConnectorActivity(
          ConnectionId: "5b74038e-d54a-4b4c-b38b-9da43831e69c",
          UiPathActivityTypeId: "f4e836f2-7b5f-3770-ad25-90d460867eb3",
          curated_contactId: "[[Email.FromAddress]]"
        );
        
        if (contact != null) 
        {
          // Send Message to User via slack 
          ConnectorActivity(
            ConnectionId = "917ef207-cebc-483b-beef-a2e6c41c1521",
            send_as = "bot",
            UiPathActivityTypeId = "9a76a063-8903-3101-b158-e0435e77c45e",
            channel = "[[SlackName]]",
            text = "[[string.Format(\"The sender of the last email, {0}, is a contact in Salesforce. Preparing to send reply to them.\", Email.FromAddress)]]"
          )

          // Use Azure OpenAI to generate a reply
          Reply = GenerateReplyUsingOpenAI(
            apiminus_signversion = "2023-10-01-preview",
            best_of = "1",
            ConnectionId = "ca0467aa-15e4-45d2-8040-b5c6a02bca5b",
            echo = "False",
            frequency_penalty = "0",
            max_tokens = "150",
            model = "text-ada-001",
            n = "1",
            presence_penalty = "0",
            stream = "False",
            temperature = "1",
            top_p = "1",
            UiPathActivityTypeId = "c2289d22-be3e-31c3-9293-e9ffc987b351",
            prompt = "[[string.Format(\"Generate a reply to the following email: {0}\", Email.Body)]]"
          )
          // Send the generated reply to the sender
          ReplyToEmailConnections(
            AttachmentInputMode = "UseExisting",
            AttachmentsBackup = { StoredValue: "UseExisting" },
            Importance = "Normal",
            ReplyToAll = "False",
            SaveAsDraft = "False",
            UseConnectionService = "True",
            Body = "[[Reply]]",
            Email = "[[Email]]"
          )
        }
      }
    }
    ```
  Please generate additional data, different from this existing one, please ensure that it does not overlap with the existing data from this YAML :
  "existing_test_data:\n- SlackName: general\n- SlackName: marketing\n- SlackName: sales\n- SlackName: ''\n- SlackName: null\n"
assistant_message: |-
  requested_entries: 10
  test_data:
  - _thought: covers the case where the Slack channel name is whitespace
    _idx: 1
    SlackName: '   '
  - _thought: covers the case where the Slack channel name contains spaces
    _idx: 2
    SlackName: general channel
  - _thought: covers the case where the Slack channel name contains special characters
    _idx: 3
    SlackName: general@channel
  - _thought: covers the case where the Slack channel name is very long
    _idx: 4
    SlackName: verylongslackchannelnameverylongslackchannelname
  - _thought: covers the case where the Slack channel name contains numbers
    _idx: 5
    SlackName: channel1
  - _thought: covers the case where the Slack channel name is case sensitive
    _idx: 6
    SlackName: Channel
  - _thought: covers the case where the Slack channel name is case sensitive
    _idx: 7
    SlackName: GENERAL
  - _thought: covers the case where the Slack channel name is case sensitive
    _idx: 8
    SlackName: generalchannel
  - _thought: covers the case where the Slack channel name is case sensitive and contains numbers
    _idx: 9
    SlackName: generalchannel123
  - _thought: covers the case where the Slack channel name is case sensitive and contains special characters and numbers
    _idx: 10
    SlackName: generalchannel@2022
