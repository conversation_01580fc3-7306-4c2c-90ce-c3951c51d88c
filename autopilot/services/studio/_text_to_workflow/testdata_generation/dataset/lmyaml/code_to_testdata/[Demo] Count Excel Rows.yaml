name: count-excel-rows
user_message: |-
  user_request: "Generate 10 test data"
  parameters:
    - name: "ExcelName"
      type: "string"
      direction: "in"
    - name: "ExcelRows"
      type: "int"
      direction: "out"
    - name: "She<PERSON><PERSON><PERSON>"
      type: "string"
      direction: "in"
  code: |-
    ```csharp
    public class Workflow
    {
      // Define the entry point function with arguments
      public void Execute(string ExcelName, out int ExcelRows, string SheetName)
      {
        // Declare variables
        UiPath.Core.Activities.CurrentJobInfo _out_ManualTrigger_1__Result;
        UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem[] RetrievedFiles;
        System.Data.DataTable ReadRangeExcel;

        // Manually trigger the process

        // Search for Excel file in specified OneDrive URL path using 'ExcelName'
        RetrievedFiles = UiPath.MicrosoftOffice365.Activities.Files.GetFileListConnections
        (
          BrowserItemFriendlyName = "Documents",
          BrowserSpecificUrl = "https://1drv.ms/f/s!ANZ4qoWebMFhZg",
          ConnectionId = "78df4a9d-38d4-476d-9786-847f5c69f45d",
          IncludeSubfolders ="False",
          ItemSelectionMode = "Browse",
          MaxResults = "10",
          SearchMode = "UseSimple",
          TrimDuplicates = "False",
          UseConnectionService = "True",
          WhatToReturn = "Files",
          SimpleSearch = string.Format("{0}", ExcelName)
        );

        // If exactly one file is found in 'RetrievedFiles'
        if (RetrievedFiles.Count() == 1)
        {
          // Use the Read Range connection to read this Excel file (using 'SheetName') into 'ReadRangeExcel'
          ReadRangeExcel = UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections<System.Data.DataTable>
          (
            ConnectionId = "78df4a9d-38d4-476d-9786-847f5c69f45d",
            HasHeaders = "True",
            ItemSelectionMode = "UseExisting",
            ReadAs = "Values",
            UseConnectionService = "True",
            WorkingWithTemplateWorkbook = "False",
            Item = RetrievedFiles.First(),
            Range = SheetName
          );

          // Assign 'ExcelRows' to the number of rows in 'ReadRangeExcel'
          ExcelRows = ReadRangeExcel.Rows.Count;
        }
        else
        {
          // Could not find a unique file Message
          UiPath.Core.Activities.LogMessage
          (
            Level = LogLevel.Warn,
            Message = "Could not find a unique file to match the search criteria"
          );

          // Set Default Excel Rows
          ExcelRows = 0;
        }
      }
    ```
assistant_message: |-
  requested_entries: 10
  test_data:
  - _thought: covers the case where the Excel file is found and has data
    _idx: 1
    ExcelName: SalesData.xlsx
    ExcelRows: 100
    SheetName: Sheet1
  - _thought: covers the case where the Excel file is found but the sheet name does not exist
    _idx: 2
    ExcelName: Financials.xlsx
    ExcelRows: 0
    SheetName: Sheet5
  - _thought: covers the case where the Excel file is found but the sheet name is empty
    _idx: 3
    ExcelName: Expenses.xlsx
    ExcelRows: 0
    SheetName: ''
  - _thought: covers the case where the Excel file is not found
    _idx: 4
    ExcelName: InvalidFile.xlsx
    ExcelRows: 0
    SheetName: Sheet1
  - _thought: covers the case where the Excel file name is null
    _idx: 5
    ExcelName: null
    ExcelRows: 0
    SheetName: Sheet1
  - _thought: covers the case where the sheet name is null
    _idx: 6
    ExcelName: Data.xlsx
    ExcelRows: 0
    SheetName: null
  - _thought: covers the case where both Excel file name and sheet name are null
    _idx: 7
    ExcelName: null
    ExcelRows: 0
    SheetName: null
  - _thought: covers the case where the Excel file name and sheet name are empty strings
    _idx: 8
    ExcelName: ''
    ExcelRows: 0
    SheetName: ''
  - _thought: covers the case where the Excel file name and sheet name contain special characters
    _idx: 9
    ExcelName: <EMAIL>
    ExcelRows: 0
    SheetName: Sheet@2022 
  - _thought: covers the case where the Excel file name and sheet name are very long
    _idx: 10
    ExcelName: LongFileNameLongFileName.xlsx
    ExcelRows: 0
    SheetName: VeryLongSheetNameVeryLongSheetName
