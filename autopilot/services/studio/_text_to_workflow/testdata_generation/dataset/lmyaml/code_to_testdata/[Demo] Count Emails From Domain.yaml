name: count_emails_from_domain
user_message: |-
  user_request: "Generate 10 test data"
  parameters:
    - name: "Domain"
      type: "string"
      direction: "in"
    - name: "NumberOfEmailsFromDomain"
      type: "int"
      direction: "out"
  code: |-
    ```csharp
    public class Workflow
    {
      // Define the entry point function with arguments
      public void Execute(out int NumberOfEmailsFromDomain, string Domain)
      {
        // Declare variables
        UiPath.Core.Activities.CurrentJobInfo _out_TimeTrigger_1__Result;
        List<UiPath.MicrosoftOffice365.Models.Office365Message> _out_GetEmailListConnections_1__EmailList;

        // Trigger: Every day at 12 AM

        // Get emails from the specified domain received in the last 24 hours
        _out_GetEmailListConnections_1__EmailList = UiPath.MicrosoftOffice365.Activities.Mail.GetEmailListConnections
        (
          BrowserFolder = "Inbox",
          ConnectionId = "8c4a3443-51c9-492e-ac46-def084f0e3c5",
          Filter = new EmailFilter
          {
            LogicalOperator = "And",
            Filters = new List<EmailFilterCriteria>
            {
              new EmailFilterCriteria
              {
                Criteria = "Date",
                DateOperator = "NewerThan",
                StringOperator = "Contains",
                TypeOperator = "Equals",
                TypeValue = "Email",
                DateValue = DateTime.Now.AddDays(-1)
              },
              new EmailFilterCriteria
              {
                Criteria = "From",
                DateOperator = "NewerThan",
                StringOperator = "Contains",
                TypeOperator = "Equals",
                TypeValue = "Email",
                InStringValue = Domain
              }
            }
          },
          Importance = "Any",
          IncludeSubfolders = "False",
          MarkAsRead = "False",
          MaxResults = "1000",
          OrderByDate = "NewestFirst",
          SelectionMode = "Browse",
          UnreadOnly = "False",
          UseConnectionService = "True",
          UseSharedMailbox = "False",
          WithAttachmentsOnly = "False"
        );

        // Set the number of emails from the specified domain
        NumberOfEmailsFromDomain = _out_GetEmailListConnections_1__EmailList.Count;
      }
    }
    ```
assistant_message: |-
  requested_entries: 10
  test_data:
  - _thought: covers the case where there are emails from the specified domain
    _idx: 1
    Domain: example.com
    NumberOfEmailsFromDomain: 25
  - _thought: covers the case where there are no emails from the specified domain
    _idx: 2
    Domain: test.com
    NumberOfEmailsFromDomain: 0
  - _thought: covers the case where there are some emails from the specified domain
    _idx: 3
    Domain: company.org
    NumberOfEmailsFromDomain: 10
  - _thought: covers the case where there are a few emails from the specified domain
    _idx: 4
    Domain: personal.net
    NumberOfEmailsFromDomain: 5
  - _thought: covers the case where the specified domain does not exist
    _idx: 5
    Domain: invalid
    NumberOfEmailsFromDomain: 0
  - _thought: covers the case where the domain is an empty string
    _idx: 6
    Domain: ''
    NumberOfEmailsFromDomain: 0
  - _thought: covers the case where the domain name is very long
    _idx: 7
    Domain: verylongdomainnameverylongdomainname
    NumberOfEmailsFromDomain: 15
  - _thought: covers the case where the domain name contains special characters
    _idx: 8
    Domain: domainwithspecialcharacters@#$%
    NumberOfEmailsFromDomain: 0
  - _thought: covers the case where the domain name contains spaces
    _idx: 9
    Domain: domain with spaces
    NumberOfEmailsFromDomain: 3
  - _thought: covers the case where the domain name is in uppercase
    _idx: 10
    Domain: capitalizedDomain
    NumberOfEmailsFromDomain: 7