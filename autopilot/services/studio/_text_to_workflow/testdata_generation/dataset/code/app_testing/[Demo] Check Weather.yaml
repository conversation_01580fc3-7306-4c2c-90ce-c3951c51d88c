name: check-weather
user_message: |-
  user_request: "Generate test data"
  parameters:
    - name: "town"
      type: "string"
      direction: "in"
    - name: "country"
      type: "string"
      direction: "in"
    - name: "minTemperature"
      type: "double"
      direction: "in"
  code: |-
    ```csharp
    using System;
    using System.Net;
    using Newtonsoft.Json;
    using UiPath.CodedWorkflows;

    namespace CodedTesting
    {
      public class CodedTestCase : CodedWorkflow
      {
        [TestCase]
        public void Execute(string town, string country, double minTemperature)
        {
          CheckTemperature(town, country, minTemperature);
        }

        public static void CheckTemperature(string city, string country, double minTemperature)
        {
          string apiKey = "b6907d289e10d714a6e88b30761fae22";
          string apiUrl = $"http://api.openweathermap.org/data/2.5/weather?q={city},{country}&appid={apiKey}&units=metric";
  
          using (WebClient client = new WebClient())
          {
            string apiResponse = client.DownloadString(apiUrl);
            WeatherResponse response = JsonConvert.DeserializeObject<WeatherResponse>(apiResponse);

            double currentTemperature = response.Main.Temp;

            if (currentTemperature > minTemperature)
            {
              Console.WriteLine($"The current temperature in {city}, {country} is higher than the minimum temperature.");
            }
            else
            {
              Console.WriteLine("The current temperature is not higher than the minimum temperature.");
            }
          }
        }
      }
    }
    ```
assistant_message: |-
  "requested_entries": 15
  "test_data":
  - "_thought": covers the case where the current temperature is higher than the minimum temperature
    "_idx": 1
    "town": London
    "country": UK
    "minTemperature": 10.0
  - "_thought": covers the case where the current temperature is not higher than the minimum temperature
    "_idx": 2
    "town": Paris
    "country": France
    "minTemperature": 25.0
  - "_thought": covers the case where the town is a single character
    "_idx": 3
    "town": A
    "country": USA
    "minTemperature": 15.0
  - "_thought": covers the case where the country is a single character
    "_idx": 4
    "town": New York
    "country": U
    "minTemperature": 20.0
  - "_thought": covers the case where the town and country are very long
    "_idx": 5
    "town": Los AngelesLos AngelesLos AngelesLos AngelesLos Angeles
    "country": United StatesUnited StatesUnited StatesUnited StatesUnited States
    "minTemperature": 18.0
  - "_thought": covers the case where the town and country contain special characters
    "_idx": 6
    "town": Rio de Janeiro
    "country": Brazil!
    "minTemperature": 22.0
  - "_thought": covers the case where the town and country are null
    "_idx": 7
    "town": null
    "country": null
    "minTemperature": 20.0
  - "_thought": covers the case where the town and country are empty strings
    "_idx": 8
    "town": ''
    "country": ''
    "minTemperature": 15.0
  - "country": Australia
    "_idx": 9
    "minTemperature": -5.0
    "_thought": covers the case where the minimum temperature is a negative value
    "town": Sydney
  - "_thought": covers the case where the minimum temperature is zero
    "_idx": 10
    "town": Tokyo
    "minTemperature": 0.0
    "country": Japan
  - "_thought": covers the case where the minimum temperature is a very high value
    "_idx": 11
    "town": Beijing
    "country": China
    "minTemperature": 100.0
  - "_thought": covers the case where the town and country are in uppercase
    "_idx": 12
    "town": MOSCOW
    "country": RUSSIA
    "minTemperature": 15.0
  - "_thought": covers the case where the town and country are in lowercase
    "_idx": 13
    "town": berlin
    "country": germany
    "minTemperature": 20.0
  - "_thought": covers the case where the town and country are in title case
    "_idx": 14
    "town": New Delhi
    "country": India
    "minTemperature": 25.0
  - "_thought": covers the case where the town and country contain leading and trailing spaces
    "_idx": 15
    "town": '  Rome  '
    "country": '  Italy  '
    "minTemperature": 18.0
testing: app