name: loanBank
user_message: |-
  user_request: "Generate 10 test data"
  parameters:
    - name: "age"
      type: "int"
      direction: "in"
    - name: "yearlyIncome"
      type: "int"
      direction: "in"
    - name: "emailAddress"
      type: "string"
      direction: "in"
    - name: "loanAmount"
      type: "int"
      direction: "in"
    - name: "loanTerm"
      type: "int"
      direction: "in"
    - name: "expectedLoanRate"
      type: "string"
      direction: "in"
  code: |-
    ```csharp
    using System;
    using UiPath.CodedWorkflows;

    namespace Demo
    {
      public class UiBankLoanApplication : CodedWorkflow
      {
        LoanApplication newLoan = new LoanApplication(); 
        public void initializeLoanClass(int age, int yearlyIncome, string emailAddress, int loanAmount, int loanTerm)
        {
          newLoan.age = age;
          newLoan.emailAddress = emailAddress;
          newLoan.loanAmount = loanAmount;
          newLoan.loanTerm = loanTerm;
          newLoan.yearlyIncome = yearlyIncome;
        }
        public void NavigateToLoanApplicationScreen()
        {
          var homeScreen = uiAutomation.Open(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank);
          homeScreen.Click(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank.Products);
          homeScreen.Click(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank.Loans);
          var app = uiAutomation.Open(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank_Loan_Screen);
          app.Click("Apply For A Loan");
        }
        public string ApplyForLoan()
        {
          string loanRate = null;
          var loanScreen = uiAutomation.Open("Chrome: UiBank New Loan");
          loanScreen.TypeInto("Age", newLoan.age.ToString());
          loanScreen.TypeInto("Email Address", newLoan.emailAddress);
          loanScreen.TypeInto("Current Yearly", newLoan.yearlyIncome.ToString());
          loanScreen.TypeInto("Loan Amount Requested", newLoan.loanAmount.ToString());
          loanScreen.TypeInto("Loan Term", newLoan.loanTerm.ToString());
          loanScreen.Click("Submit Loan Application");
          var loanRequestScreen = uiAutomation.Open("Chrome: UiBank - Loan Pass Fail");
          var successMsg = loanRequestScreen.GetText("Success Message");
          Console.WriteLine(successMsg);
          if(successMsg.Contains("approved"))
          {
            loanRate = loanRequestScreen.GetText("Loan Rate");
          }
          else
            loanRate = "0";
          return loanRate;
        }
        [TestCase]
        public void Execute(int age, int yearlyIncome, string emailAddress, int loanAmount, int loanTerm, string expectedLoanRate)
        {
          Log("Test run started for UiBankLoanApplication.");
          initializeLoanClass(age, yearlyIncome, emailAddress, loanAmount, loanTerm);
          // Navigate to Loan Application Screen
          NavigateToLoanApplicationScreen();

          //Applying the loan and getting the loan rate

          string loanRate = ApplyForLoan();

          Console.WriteLine("Loan Rate: "+ loanRate);

          //Verifying whether the loan rate is 8%.

          testing.VerifyExpression(loanRate.Contains(expectedLoanRate));
        }
      }
    }
    ```
assistant_message: |-
  requested_entries: 10
  test_data:
  - "_thought" : "covers the case where the loan is approved and the expected loan rate is found"
    "_idx": 1
    "age" : 35
    "yearlyIncome" : 60000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 25000
    "loanTerm" : 5
    "expectedLoanRate" : "3.5%"
  - "_thought" : "covers the case where the loan is approved but the expected loan rate is not found"
    "_idx": 2
    "age" : 45
    "yearlyIncome" : 80000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 30000
    "loanTerm" : 7
    "expectedLoanRate" : "4.2%"
  - "_thought" : "covers the case where the loan is not approved"
    "_idx": 3
    "age" : 25
    "yearlyIncome" : 35000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 15000
    "loanTerm" : 3
    "expectedLoanRate" : "0%"
  - "_thought" : "covers the case where the loan amount is very high"
    "_idx": 4
    "age" : 40
    "yearlyIncome" : 100000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 1000000
    "loanTerm" : 10
    "expectedLoanRate" : "0%"
  - "_thought" : "covers the case where the loan term is very short"
    "_idx": 5
    "age" : 30
    "yearlyIncome" : 45000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 20000
    "loanTerm" : 1
    "expectedLoanRate" : "2.0%"
  - "_thought" : "covers the case where the age is very young"
    "_idx": 6
    "age" : 20
    "yearlyIncome" : 25000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 10000
    "loanTerm" : 5
    "expectedLoanRate" : "2.5%"
  - "_thought" : "covers the case where the age is very old"
    "_idx": 7
    "age" : 70
    "yearlyIncome" : 60000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 30000
    "loanTerm" : 5
    "expectedLoanRate" : "3.0%"
  - "_thought" : "covers the case where the yearly income is very low"
    "_idx": 8
    "age" : 45
    "yearlyIncome" : 15000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 20000
    "loanTerm" : 5
    "expectedLoanRate" : "2.8%"
  - "_thought" : "covers the case where the email address is invalid"
    "_idx": 9
    "age" : 35
    "yearlyIncome" : 55000
    "emailAddress" : "invalid-email"
    "loanAmount" : 25000
    "loanTerm" : 5
    "expectedLoanRate" : "3.5%"
  - "_thought" : "covers the case where the expected loan rate is not a valid format"
    "_idx": 10"
    "age" : 55
    "yearlyIncome" : 70000
    "emailAddress" : "<EMAIL>"
    "loanAmount" : 35000
    "loanTerm" : 7
    "expectedLoanRate" : "invalid-rate"
testing: app