name: loginBank 
user_message: |-
  user_request: "Generate as many test data that is needed for a high coverage"
  parameters:
    - name: "username"
      type: "string"
      direction: "in"
    - name: "password"
      type: "string"
      direction: "in"
  code: |-
    ```csharp
    using System;
    using UiPath.CodedWorkflows;
    using UiPath.UIAutomationNext.API.Models;

    namespace Demo
    {
      /*This classs is responsible for log into UiBank web application based on given credentials and
      passing out parameters with key indicators for further operations*/
      public class UiBankAccountLogin : CodedWorkflow
      {
        [Workflow]
        public (bool isLoginSuccessful, string LoginMessage) Execute(string username, string password)
        {
          // Initializing objects
          UiTargetApp UiBankApplication = null;
          var Description = string.Empty;
          
          try
          {
            //Open UiBank Application on Chrome Browser
            UiBankApplication = uiAutomation.Open(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank);
            UiBankApplication.Click(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank.Login);
            
            var app = uiAutomation.Open(ObjectRepository.Descriptors.Chrome__UiBank_app.Chrome__UiBank_Login);
            
            UiBankApplication.TypeInto("username", UiBankCredentials);
            UiBankApplication.TypeInto("password", "password");
            
            UiBankApplication.Click("Sign In");
                    
            Description = UiBankApplication.GetText("Login Message");
          }
          catch(Exception ex)
          {
            Log("Exception occured while login to UiBank. Exception Message: " + ex.Message);
          }
          
          if(Description.Contains("Welcome") || Description.Contains("Accounts"))
          {
            return (isLoginSuccesful: true, "LoginMessage": "UiBank SignIn process is successful");
          }
          else
          {
            return (isLoginSuccesful: false, "LoginMessage": "UiBank SignIn process failed");
          }
        }
      }
    }
    ```
assistant_message: |-
  requested_entries: 15
  test_data:
  - "_thought": "covers the case where the login is successful"
    "_idx": 1
    "username": "john.doe"
    "password": "P@ssw0rd"
  - "_thought": "covers the case where the login is unsuccessful"
    "_idx": 2
    "username": "jane.smith"
    "password": "invalidpassword"
  - "_thought": "covers the case where an exception occurs during login"
    "_idx": 3
    "username": "sam.wilson"
    "password": "P@ssw0rd"
  - "_thought": "covers the case where the username is empty"
    "_idx": 4
    "username": ""
    "password": "P@ssw0rd"
  - "_thought": "covers the case where the password is empty"
    "_idx": 5
    "username": "emily.jones"
    "password": ""
  - "_thought": "covers the case where both username and password are empty"
    "_idx": 6
    "username": ""
    "password": ""
  - "_thought": "covers the case where the username contains special characters"
    "_idx": 7
    "username": "michael@brown"
    "password": "P@ssw0rd"
  - "_thought": "covers the case where the password contains special characters"
    "_idx": 8
    "username": "olivia.wilson"
    "password": "P@ssw0rd!"
  - "_thought": "covers the case where the username and password are very long"
    "_idx": 9
    "username": "robertjohnsonrobertjohnsonrobertjohnsonrobertjohnson"
    "password": "P@ssw0rdP@ssw0rdP@ssw0rdP@ssw0rdP@ssw0rd"
  - "_thought": "covers the case where the username and password are very short"
    "_idx": 10
    "username": "ab"
    "password": "12"
  - "_thought": "covers the case where the username and password are null"
    "_idx": 11
    "username": null
    "password": null
  - "_thought": "covers the case where the username and password are whitespace"
    "_idx": 12
    "username": "   "
    "password": "   "
  - "_thought": "covers the case where the username and password contain leading and trailing spaces"
    "_idx": 13
    "username": "  john.doe  "
    "password": "  P@ssw0rd  "
  - "_thought": "covers the case where the username and password are case sensitive"
    "_idx": 14
    "username": "JohnDoe"
    "password": "p@ssw0rd"
  - "_thought": "covers the case where the username and password are case sensitive and correct"
    "_idx": 15
    "username": "JohnDoe"
    "password": "P@ssw0rd"
testing: rpa