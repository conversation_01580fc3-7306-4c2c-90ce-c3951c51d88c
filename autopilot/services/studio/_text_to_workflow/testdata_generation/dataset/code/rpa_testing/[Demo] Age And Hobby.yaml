name: age
user_message: |
  user_request: "Generate 7 test data"
  parameters:
  - name: "age"
    type: "int"
    direction: "int"
  - name: "hobby"
    type: "string"
    direction: "in"
  code: |-
    ```csharp
    using System;
    using System.Linq;
    public class DecisionMaker
    {
      [Workflow]
      public void Execute(int age, string hobby)
      {        
        if (age < 18)
          Console.WriteLine("You are too young to make this decision.");
        else if (age >= 18 && age < 30)
        {
          if (hobby == "Gaming")
            Console.WriteLine("Consider balancing gaming with other activities at this stage of life.");
          else
            Console.WriteLine(\"Explore various hobby and interests:\");
        }
        else if (age >= 30 && age < 50)
        {
          if (hobby == "Travelling")
            Console.WriteLine("If you enjoy travelling, plan some memorable trips.");
          else
            Console.WriteLine("Explore a hobby that brings joy and relaxation.");
        }
        else
        {
          if (hobby == "Reading")
            Console.WriteLine(\"Reading is a great hobby for personal growth and relaxation.\");
          else
            Console.WriteLine("Consider incorporating reading into your routine.");
        }
      }
    }
    ```
assistant_message: |-
  requested_entries: 7
  test_data:
  - "_thought" : "covers the case age < 18"
    "_idx": 1
    "age" : 15
    "hobby" : "Gaming"
  - "_thought" : "18 <= age < 30 and hobby is Gaming"
    "_idx": 2
    "age" : 27
    "hobby" : "Gaming"
  - "_thought" : "18 <= age < 30 and hobby is not Gaming"
    "_idx": 3
    "age" : 23
    "hobby" : "Photography"
  - "_thought" : "30 <= age < 50 and hobby is Travelling"
    "_idx": 4
    "age" : 31
    "hobby" : "Travelling"
  - "_thought" : "30 <= age < 50 and hobby is not Travelling"
    "_idx": 5
    "age" : 34
    "hobby" : "Writing"
  - "_thought" : "age >= 50 and hobby is Reading"
    "_idx": 6
    "age" : 74
    "hobby" : "Reading"
  - "_thought" : "age >= 50 and hobby is not Reading"
    "_idx": 7
    "age" : 61
    "hobby" : "Shopping"
testing: rpa