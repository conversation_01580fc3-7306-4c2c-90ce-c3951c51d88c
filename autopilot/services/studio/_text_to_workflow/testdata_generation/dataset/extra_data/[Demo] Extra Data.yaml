name: extra_data
user_message: |-
  number_of_data: 30
  parameters:
    - name: "town"
      type: "string"
      direction: "in"
    - name: "country"
      type: "string"
      direction: "in"
    - name: "minTemperature"
      type: "double"
      direction: "in"
  code: |-
    ```csharp
    using System;
    using System.Net;
    using Newtonsoft.Json;
    using UiPath.CodedWorkflows;

    namespace CodedTesting
    {
      public class CodedTestCase : CodedWorkflow
      {
        [TestCase]
        public void Execute(string town, string country, double minTemperature)
        {
          CheckTemperature(town, country, minTemperature);
        }

        public static void CheckTemperature(string city, string country, double minTemperature)
        {
          string apiKey = "b6907d289e10d714a6e88b30761fae22";
          string apiUrl = $"http://api.openweathermap.org/data/2.5/weather?q={city},{country}&appid={apiKey}&units=metric";
  
          using (WebClient client = new WebClient())
          {
            string apiResponse = client.DownloadString(apiUrl);
            WeatherResponse response = JsonConvert.DeserializeObject<WeatherResponse>(apiResponse);

            double currentTemperature = response.Main.Temp;

            if (currentTemperature > minTemperature)
            {
              Console.WriteLine($"The current temperature in {city}, {country} is higher than the minimum temperature.");
            }
            else
            {
              Console.WriteLine("The current temperature is not higher than the minimum temperature.");
            }
          }
        }
      }
    }
    ```
  test_data_generated: |-
    "test_data":
    - "town": London
      "country": UK
      "minTemperature": 10.0
    - "town": Paris
      "country": France
      "minTemperature": 25.0
    - "town": A
      "country": USA
      "minTemperature": 15.0
    - "town": New York
      "country": U
      "minTemperature": 20.0
    - "town": Los AngelesLos AngelesLos AngelesLos AngelesLos Angeles
      "country": United StatesUnited StatesUnited StatesUnited StatesUnited States
      "minTemperature": 18.0
    - "town": Rio de Janeiro
      "country": Brazil!
      "minTemperature": 22.0
    - "town": null
      "country": null
      "minTemperature": 20.0
    - "town": ''
      "country": ''
      "minTemperature": 15.0
    - "town": Sydney
      "country": Australia
      "minTemperature": -5.0
    - "town": Tokyo
      "minTemperature": 0.0
      "country": Japan
    - "town": Beijing
      "country": China
      "minTemperature": 100.0
    - "town": MOSCOW
      "country": RUSSIA
      "minTemperature": 15.0
    - "town": berlin
      "country": germany
      "minTemperature": 20.0
    - "town": New Delhi
      "country": India
      "minTemperature": 25.0
    - "town": '  Rome  '
      "country": '  Italy  '
      "minTemperature": 18.0
assistant_message: |
  test_data:
  - "_thought": "covers the case where the current temperature is higher than the minimum temperature"
    "town": "London"
    "country": "UK"
    "minTemperature": 12.0
  - "_thought": "covers the case where the current temperature is not higher than the minimum temperature"
    "town": "Lyon"
    "country": "France"
    "minTemperature": 23.0
  - "_thought": "covers the case where the town is a single character"
    "town": "B"
    "country": "USA"
    "minTemperature": 18.0
  - "_thought": "covers the case where the country is a single character"
    "town": "New York"
    "country": "U"
    "minTemperature": 20.0
  - "_thought": "covers the case where the town and country are very long"
    "town": "LA LA LALA LA LALA LA LA"
    "country": "United StatesUnited StatesUnited StatesUnited StatesUnited States"
    "minTemperature": 18.0
  - "_thought": "covers the case where the town and country contain special characters"
    "town": "Bucharest%%"
    "country": "Romania!"
    "minTemperature": 22.0
  - "_thought": "covers the case where the town and country are null"
    "town": null
    "country": null
    "minTemperature": 26.0
  - "_thought": "covers the case where the town and country are empty strings"
    "town": ""
    "country": ""
    "minTemperature": 18.0
  - "_thought": "covers the case where the minimum temperature is a negative value"
    "town": "Sydney"
    "country": "Australia"
    "minTemperature": -15.0
  - "_thought": "covers the case where the minimum temperature is zero"
    "town": "Osaka"
    "country": "Japan"
    "minTemperature": 0.0
  - "_thought": "covers the case where the minimum temperature is a very high value"
    "town": "Shanghai"
    "country": "China"
    "minTemperature": 100.0
  - "_thought": "covers the case where the town and country are in uppercase"
    "town": "KAZAN"
    "country": "RUSSIA"
    "minTemperature": 18.0
  - "_thought": "covers the case where the town and country are in lowercase"
    "town": "koln"
    "country": "germany"
    "minTemperature": 20.0
  - "_thought": "covers the case where the town and country are in title case"
    "town": "New Delhi"
    "country": "India"
    "minTemperature": 55.0
  - "_thought": "covers the case where the town and country contain leading and trailing spaces"
    "town": "  Napoli  "
    "country": "  Italy  "
    "minTemperature": 15.0
  - "_thought": "covers the case where the town and country are different languages"
    "town": "東京"
    "country": "日本"
    "minTemperature": 26.0
  - "_thought": "covers the case where the town and country are mix of letters and numbers"
    "town": "S4n Fr4nc1sc0"
    "country": "C4n4d4"
    "minTemperature": 11.0
  - "_thought": "covers the case where the town and country are random characters"
    "town": "!@#$%^&*"
    "country": "123456"
    "minTemperature": 19.0
  - "_thought": "covers the case where the town and country are common names"
    "town": "John"
    "country": "Mary"
    "minTemperature": 22.0
  - "_thought": "covers the case where the town and country are random characters"
    "town": "!@#$%^&*"
    "country": "123456"
    "minTemperature": 15.0
  - "_thought": "covers the case where the town and country are common names"
    "town": "Sam"
    "country": "Mary"
    "minTemperature": 20.0
  - "_thought": "covers the case where the town and country are mix of uppercase and lowercase"
    "town": "PaRiS"
    "country": "FrAnCe"
    "minTemperature": 25.0
  - "_thought": "covers the case where the town and country contain special characters"
    "town": "N3w&Y0rK"
    "country": "Un!t3d^St4t3s"
    "minTemperature": 10.0
  - "_thought": "covers the case where the town and country are mix of uppercase and lowercase"
    "town": "LoNDoN"
    "country": "EnGLanD"
    "minTemperature": 15.0
  - "_thought": "covers the case where the town and country are random characters"
    "town": "%^&*(()"
    "country": "789456"
    "minTemperature": 18.0
  - "_thought": "covers the case where the town and country are common names"
    "town": "Alice"
    "country": "Bob"
    "minTemperature": 20.0
  - "_thought": "covers the case where the town and country are mix of letters and numbers"
    "town": "T0ky0"
    "country": "J4p4n"
    "minTemperature": 25.0
  - "_thought": "covers the case where the town and country are mix of uppercase and lowercase"
    "town": "AmStErDaM"
    "country": "NeThErLaNdS"
    "minTemperature": 18.0
  - "_thought": "covers the case where the town and country are random characters"
    "town": "()_+=-{}"
    "country": "153468"
    "minTemperature": 12.0
  - "_thought": "covers the case where the town and country are symbolic names"
    "town": "Star"
    "country": "Moon"
    "minTemperature": 20.0

