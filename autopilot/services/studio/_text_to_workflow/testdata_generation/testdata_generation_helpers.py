import pathlib
import re

from services.studio._text_to_workflow.utils.workflow_utils import load_completion_yaml
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump


def get_config_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / "prompt.yaml"


def strip_lmyaml(lmyaml: str) -> str:
    unnecessary_args = ["UiPathEvent", "UiPathEventConnector", "UiPathEventObjectId", "UiPathEventObjectType"]
    lmyaml_dict = load_completion_yaml(lmyaml)

    if "trigger" in lmyaml_dict:
        for param in list(lmyaml_dict["trigger"]["params"].keys()):
            if param in unnecessary_args:
                del lmyaml_dict["trigger"]["params"][param]

        for param in lmyaml_dict["trigger"]["params"]:
            if param in unnecessary_args:
                del lmyaml_dict["trigger"]["params"][param]

        for arg in lmyaml_dict["arguments"][:]:
            if arg["name"] in unnecessary_args:
                lmyaml_dict["arguments"].remove(arg)
    keys = list(lmyaml_dict.keys())
    for key in keys:
        if key not in ["processName", "thought", "trigger", "workflow"]:
            lmyaml_dict.pop(key, None)
    return yaml_dump(lmyaml_dict)


def load_testdata_yaml(result_yaml):
    result = load_completion_yaml(result_yaml)
    if not result:
        # removes the last line, in case the model does not finish the yaml format correctly
        result = load_completion_yaml(result_yaml.rsplit("\n", 1)[0])
    if not result:
        prefix = "```yaml\n"
        if result_yaml.startswith(prefix):
            result_yaml = result_yaml[len(prefix) :]
        suffix = "```"
        if result_yaml.endswith(suffix):
            result_yaml = result_yaml[: -(len(suffix))]
        # check if there is an appereance of ":" without space after and remove from that line to the end
        pattern = r":(?!\s)"
        match = re.search(pattern, result_yaml)
        if match:
            result_yaml = result_yaml[: match.start() + 1]
        last_newline_index = result_yaml.rindex("\n")
        fixed_yaml_content = result_yaml[:last_newline_index]
        return load_testdata_yaml(fixed_yaml_content)
    return result


def format_result(result: dict, parameters: list, current_data=[]):
    test_data = []
    for data in result.get("test_data", []):
        curated_data = validate_and_format_data(data, parameters)
        if curated_data:
            test_data.append(curated_data)

    result["test_data"] = test_data

    if current_data:
        remove_duplicate_data_from_current(result, current_data)

    remove_duplicate_data_result(result)

    return result


def remove_duplicate_data_result(result):
    final_result = []
    for data in result.get("test_data", []):
        if data not in final_result:
            final_result.append(data)
    result["test_data"] = final_result


def remove_duplicate_data_from_current(result, current_data):
    for data in result.get("test_data", [])[:]:
        if data in current_data:
            result["test_data"].remove(data)


def validate_and_format_data(data, parameters):
    if not data:
        return None
    # Check if data has all parameters
    result = {}

    # extract in result only the params received in request from data generated
    # also ensures that if the model generates keys without being case sensitive, we return the data exactly as it was requested
    for param in parameters:
        if param["name"] in data:
            if "string" in param["type"].lower() and data[param["name"]] is None:
                result[param["name"]] = ""
            else:
                result[param["name"]] = data[param["name"]]
            del data[param["name"]]
        else:
            param_name = param["name"].lower()
            data_keys = {key.lower(): key for key in data.keys()}

            if param_name not in data_keys:
                return None

            result[param["name"]] = data[data_keys[param_name]]
            del data[data_keys[param_name]]

    return result
