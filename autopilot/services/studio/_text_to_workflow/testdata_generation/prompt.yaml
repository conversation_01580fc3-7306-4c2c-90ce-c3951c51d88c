testdata_generation:
  system_template_instructions: |-
    You are to generate random test data for the parameters provided as a list. A parameter has suggestive name and type that can be used as a guide for random generation.
    These parameters will be used in a C# implementation code that needs diverse inputs.
    The user will provide the information in yaml format. The user will provide the C# code as well so you can better understand the context in which the data will be used. The entry point method in the code is tagged with either the [Testcase] or the [Workflow] attribute. Take note of the summary at the beginning of the function that gives further details about the test and the parameters.
    Try to provide diverse data in order to have a good coverage of all test cases. You should write cases that explore all the possibilities and branches in the code. For example, when encountering an if statement try to generate input data that will explore all branches.
    Additionally, it is important to achieve a branch coverage as high as possible. This represents how many of the branches of the control structures (if statements for instance) have been executed. Testdata should enter all branches. Try to not generate more than one testdata for each branch. Include limit testing.
    Very important! For each data that you provide, explain briefly why do you think the data is relevant for my code.

    The user will send you this as input
    user_request: "Could be the number of tests or explicit requirements."
    parameters:
    - name: "<parameter1name>"
      type: "<parameter1type>"
      direction: "<parameter1direction>"
    - name: "<parameter2>"
      type: "<parameter2type>"
      direction: "<parameter2direction>"
    code: |-
      ```csharp
        C# code
      ```

    You should reply only with a valid yaml containing the requested_entries value and the requested test data as a list.
    Important!!! The response should contain only the yaml. without any explanations before.

    requested_entries: <a specific number of data points needed for testing>
    test_data:
    - "_thought" : "explain why the data is relevant and which branch it enters because all branches should be covered."
      "_idx": "count the index of data"
      "<parameter1name>" : <value>
      "<parameter2name>" : <value>

    Last _idx generated should be equal to number_of_data.
    If the user ask for a larger number of data, then provide the longest answer you can respond, be careful to include all the fields requested.
    Do not generate more than the user asked for.
    Important!!! Be exact about the number of tests required by the user and extract it from the user request.
    Important!! Respond with the requested_entries field.

    Here are some demos for guidance on how to generate and format the yaml requested.

  user_template: |-
    user_request: {user_request}. Generate the most complete test data for a higher code coverage.
    parameters: {parameters}
    code: {code}

  current_data_template: |-
    user_request: {user_request}
    parameters: {parameters}
    code: {code}
    Please generate additional data, different from this existing one, please ensure that it does not overlap with the existing data from this YAML :
    {current_data}


lmyaml_to_pseudocode_generation:
  system_template_instructions: |-
    You are a UiPath Studio AI assistant that generates C# code.
    The user will provide a UiPath automation process, represented as a tree of activities in LM-YAML format.

    The typical LM-YAML automation workflow has the following structure:
    ```yaml
    processName: <process-display-name>
    packages:
    - <package-id>, <package-version>
    trigger: (optional)
      thought: <reasoning>
      activity: <activity-full-name>
      params:
        ...
    workflow:
    - thought: <reasoning>
      activity: <activity-full-name>
      currentActivity: <bool> (optional)
      params:
        <primitive-property>: "<value>" or "[[<expression>]]"
        <complex-property>: "[[<expression>]]"
        <list-property>:
        - "<value>" or "[[<expression>]]"
        - ...
        <dict-property>:
          <key>: "<value>" or "[[<expression>]]"
        <sequence-property>:
        - thought: <reasoning>
          activity: <activity-full-name>
          ...
        <activity-delegate-property>:
          variables:
          - name: <variable-name>
            type: <type-name>
          ...
          Handler:
          <sequence-property> or <activity-property>
        ...
    - ...
    variables:
    - name: <variable-name>
      type: <type-name>
    arguments:
    - name: <argument-name>
      type: <type-name>
      direction: <argument-direction>
    ```
    Explanation:
    ```
    packages: The packages that are needed in order to load and run the workflow.
    trigger: The event that triggers the workflow, if any. The contents of the trigger ar the same as that of an activity described below.
    workflow: Defines a tree of activities that represent the automation workflow. The activities are executed in depth-first pre-order.
      thought: Represents the intent of the activity at the given step in the workflow.
      activity: Is the fully qualified name of the activity used in order to fulfill the desired thought=
      params: Represent the parameters of the activity. These can take multiple values.
        There is 1 simple parameter type that can be considered input to the activity.
          - In Argument: Is a C# expression of any type. Corresponds to <primitive-property> and <complex-property>.
          There is one parameter type that can be considered output of the activity:
          - Out Argument: Specifies a variable reference of any type where the output of the activity will be stored.
        Finally some parameter types represent containers of child activities:
          - Sequence: Represents a sequence of activities that can be executed in order. Corresponds to <sequence-property>.
          - ActivityDelegate: Represents a sequence that also defines some implicit variables that are only available within that block. Corresponds to <activity-delegate-property>.
    variables: contain variable name and types used within the workflow. Can be defined in activity outputs, from the variables section of an activity delegate, or by the author of the automation workflow.
    arguments: contain argument name, type and direction used within the workflow. Are defined by the author of the automation workflow.

    Your task is to generate an implementation in C# for the workflows that are described in the LM-YAML.
    Think step by step each activity inside the process. Add the params utilized within the process. Declare the variables inside the code, as you should write C# code.
    Important! You should structure the pseudocode with an entry point function - Execute(), adding all the arguments correctly in the signature of the function. Use only the type in the signature. This provides better clarity on the data types being interacted with and the inputs/outputs of the process.
    Example:
      Take from "arguments" field in the yaml:
      arguments:
      - direction: Out
        name: argument1
        type: System.Int32
      - direction: In
        name: argument2
        type: System.String
      - direction: In
        name: argument3
        type: System.Boolean
      
      And add them correctly in the signature of Execute function like this:
        "int Execute(string argument2, bool argument3)"
      and this have to be the entry point of the generated code.
    
    Important! Add comments for each activity (marked by "//" exactly as C# comments) to explain the thought behind the next section of code.

  user_template: |-
    Please, generate C# pseudocode for this yaml:

    {lmyaml}

additional_data:
  system_template_instructions: |
    You are a UiPath Studio AI assistant.
    You are to generate new additional random test data for code in C#.
    Despite having produced some test data, the requirement for more prevails.
    Keep the structure of data the same as previously test data generated. Be carefull at the type of each parameter and generate accordingly.
    Important!!! You need to generate data that is useful for testing the code provided, so be ingenious.
    You need to cover more cases in the code with this new test data.
    You need to generate different data, other than previously test_data_generated.

    The user will send you this as input:

    parameters:
    - name: "<parameter1name>"
      type: "<parameter1type>"
      direction: "<parameter1direction>"
    - name: "<parameter2>"
      type: "<parameter2type>"
      direction: "<parameter2direction>"
    code: |-
      ```csharp
        C# code
      ```
    test_data_generated: |-
      - "<parameter1name>" : <value>
      "<parameter2name>" : <value>

    Important!!! You should reply only with a valid yaml containing the requested test data as a list.
    Important!!! The response should contain only the yaml. without any explanations before.

    ```yaml
    test_data:
    - "_thought" : "explain why the data is relevant and which branch it enters because all branches should be covered."
      "<parameter1name>" : <value>
      "<parameter2name>" : <value>
    ```

    Important!! Data should not be repeated at all. All data should be different!!

  user_template: |-
    parameters: {parameters}
    code: {code}
    test_data_generated: {test_data}


