import numpy as np
from sklearn.metrics.pairwise import cosine_similarity


class TestdataGenerationRetriever:
    def get_relevant_demos_indices(self, query_embedding: list[float], demos_embeddings: list[list[float]], k: int = 3) -> list[int]:
        # Convert data to numpy arrays
        demos_embeddings_arr = np.array(demos_embeddings)
        query_embedding_arr = np.array(query_embedding)

        # Compute the cosine similarity scores between the query and the demos
        scores = cosine_similarity(demos_embeddings_arr, query_embedding_arr.reshape(1, -1)).flatten()

        # Get the indices of the top k scores
        top_k_indices = np.argpartition(scores, -k)[-k:]

        # we sort these indices starting with the highest rated among this subset
        top_k_indices_sorted = top_k_indices[np.argsort(scores[top_k_indices])[::-1]]

        return top_k_indices_sorted.tolist()
