import json
import shutil

from tqdm import tqdm

with open("index.json", encoding="utf-8") as fin:
    data = json.load(fin)

targets = set()
for item in tqdm(data.values()):
    if "destination" not in item:
        continue
    # pprint(item)
    # print(item["TargetFramework"])
    # print(item["type"])
    # print(item["storage"])
    # print(item["destination"].split("/")[-1])
    # print(item["JobCount"])
    # print(item["SuccessRate"])
    targets.add(item["TargetFramework"])
    # print(targets)
    if not item["TargetFramework"]:
        item["TargetFramework"] = "notarget"
    src = f"{item['storage']}/{item['destination'].split('/')[-1]}"
    dst = f"useful/{item['TargetFramework']}/{item['destination'].split('/')[-1]}"
    try:
        shutil.copy(src, dst)
    except Exception as e:
        print("Error on", src)
        print(e)
    # break
