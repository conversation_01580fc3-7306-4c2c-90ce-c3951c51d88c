import pathlib as pt

from services.studio._text_to_workflow.utils import paths

raw_metadata_paths_us: tuple[pt.Path, ...] = (
    paths.get_orchestrator_production_metadata_path() / "crpa-paid-orch0-eus-sql.csv",
    paths.get_orchestrator_production_metadata_path() / "crpa-paid-orch0-eus2-sql.csv",
)
raw_metadata_paths_eu: tuple[pt.Path, ...] = (
    paths.get_orchestrator_production_metadata_path() / "crpa-paid-orch0-ne-sql.csv",
    paths.get_orchestrator_production_metadata_path() / "crpa-paid-orch0-ne2-sql.csv",
    paths.get_orchestrator_production_metadata_path() / "crpa-prod-orch0-ne-db.csv",
)
metadata_path_us: pt.Path = paths.get_orchestrator_production_metadata_path() / "mlproddataeussa.metadata.feather"
metadata_path_eu: pt.Path = paths.get_orchestrator_production_metadata_path() / "mlproddataneusa.metadata.feather"
blob_container_name = "studio"
blob_path_prefix: pt.Path = pt.Path("datasets/studio/v24.02.09")
