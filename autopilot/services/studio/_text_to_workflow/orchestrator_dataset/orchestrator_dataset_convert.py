import concurrent.futures
import json
import pathlib as pt

import pandas as pd
import tqdm

from services.studio._text_to_workflow.orchestrator_dataset import orchestrator_dataset_constants
from services.studio._text_to_workflow.orchestrator_dataset.orchestrator_dataset_schema import MetadataIndex, MetadataInfo
from services.studio._text_to_workflow.utils import dotnet_workflow_converter, paths


def convert_workflow2lmyaml(project_json_path: pt.Path, xaml_path: pt.Path, redo: bool = False) -> str:
    relative_xaml_path = xaml_path.relative_to(paths.get_orchestrator_production_rawdata_path())
    output_json_path = paths.get_orchestrator_production_converted_path() / relative_xaml_path.with_suffix(".json")
    output_yaml_path = paths.get_orchestrator_production_converted_path() / relative_xaml_path.with_suffix(".yaml")
    existing = output_json_path.exists()
    if existing and not redo:
        return "existing"
    with open(project_json_path, "r", encoding="utf-8-sig") as f:
        project = json.load(f)
    output_json_path.parent.mkdir(parents=True, exist_ok=True)
    workflow_result = dotnet_workflow_converter.convert_workflow2yaml(
        project_json_path=project_json_path.as_posix(),
        xaml_path=xaml_path.as_posix(),
        output_path=output_json_path.as_posix(),
        target_framework=project.get("targetFramework", "Portable"),
        capture_output=True,
    )
    with open(output_yaml_path, "w", encoding="utf-8-sig") as f:
        f.write(workflow_result["Model"])
    conversion_success = workflow_result["Success"]
    has_errors = len(workflow_result["Errors"]) > 0
    if not conversion_success:
        status = "error"
    elif has_errors:
        status = "warning"
    else:
        status = "success"
    return status


def convert_process2lmyaml(index: MetadataIndex, process_dir_path: pt.Path, redo: bool = False) -> tuple[MetadataIndex, list[str]]:
    raw_data_path = paths.get_orchestrator_production_rawdata_path()
    project_json_path = next(iter(process_dir_path.rglob("project.json")))
    xaml_paths = process_dir_path.rglob("*.xaml")
    statuses = []
    for xaml_path in xaml_paths:
        status = convert_workflow2lmyaml(project_json_path, xaml_path, redo)
        relative_xaml_path = xaml_path.relative_to(raw_data_path)
        statuses.append(status)
        tqdm.tqdm.write(f"{relative_xaml_path.as_posix():150s}: {status}")
    return index, statuses


def convert_processes(df: pd.DataFrame, target_framework: str, redo: bool = False) -> pd.DataFrame:
    df, existing, error, warning, success = df.copy(), 0, 0, 0, 0
    mask = (df.Exists) & (~df.Empty) & (df["TargetFramework"] == target_framework)
    df_masked = df[mask]
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for item in df_masked.itertuples():
            index = MetadataIndex(*item[0])
            row = MetadataInfo(*item[1:])
            assert row.LocalPath is not None
            process_path = paths.get_orchestrator_production_rawdata_path() / row.LocalPath
            future = executor.submit(convert_process2lmyaml, index, process_path, redo)
            futures.append(future)
        progress_bar = tqdm.tqdm(concurrent.futures.as_completed(futures), dynamic_ncols=True, total=len(futures))
        for future in progress_bar:
            index, statuses = future.result()
            existing_count = statuses.count("existing")
            error_count = statuses.count("error")
            warning_count = statuses.count("warning")
            success_count = statuses.count("success")
            df.at[index, "ConversionErrorsCount"] = error_count
            df.at[index, "ConversionWarningsCount"] = warning_count
            df.at[index, "ConversionSuccessCount"] = success_count

            existing += existing_count
            error += error_count
            warning += warning_count
            success += success_count
            progress_bar.set_postfix(existing=existing, error=error, warning=warning, success=success)
    return df


def convert(target_framework: str):
    us = pd.read_feather(orchestrator_dataset_constants.metadata_path_us)
    us = convert_processes(us, target_framework, redo=True)
    us.to_feather(orchestrator_dataset_constants.metadata_path_us)

    eu = pd.read_feather(orchestrator_dataset_constants.metadata_path_eu)
    eu = convert_processes(eu, target_framework, redo=True)
    eu.to_feather(orchestrator_dataset_constants.metadata_path_eu)
