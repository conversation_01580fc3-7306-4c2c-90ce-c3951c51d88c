import concurrent.futures
import json
import logging
import os
import pathlib
import tempfile
import time

import requests
import typing_extensions as t

from services.studio._text_to_workflow.orchestrator_dataset.converter_client.zip_converter import extract_contents_zip

# windows converter also supports portable
CONVERTER_CONFIGS = {
    "legacy": {"url": "https://wf-converter-legacy.azurewebsites.net/api/Workflow2Yaml", "key": "", "max_workers": 20},
    "windows": {"url": "https://wf-converter.azurewebsites.net/api/Workflow2Yaml", "key": "", "max_workers": 20},
}


def configure_logging():
    current_time = time.strftime("%Y-%m-%d_%H-%M-%S")
    logs_file = f"conversion_logs_{current_time}.log"
    logging.basicConfig(filename=logs_file, level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", filemode="a")
    logger = logging.getLogger("ConversionLogger")
    logger.info("Logging initialized")


def _get_request_url(target_platform: str):
    return f"{CONVERTER_CONFIGS[target_platform]['url']}?code={CONVERTER_CONFIGS[target_platform]['key']}"


def _get_payload_data(workflow_name: str):
    request_input_data = {
        "Simulate": True,
        "ReplaceWriteLine": True,
        "EnhanceSystemActivities": True,
        "SkipNull": True,
        "Truncate": True,
        "GenerateTypeDefinition": True,
        "WorkflowName": workflow_name,
    }
    payload_data = {"requestInput": json.dumps(request_input_data)}

    return payload_data


def send_conversion_request(target_platform, zip_path, workflow_name):
    """Send a request to the converter API to convert an archived XAML to a YAML workflow.

    Args:
        target_platform (str): The target platform of the process. Either "legacy" or "windows".
        zip_path (str): The path to the zip file containing the XAML files.
        workflow_name (str): The name of the workflow to convert.

    Returns:
        tuple[bool, bytes]: A tuple containing a boolean indicating the success of the conversion and the converted zip file.
    """
    with open(zip_path, "rb") as f:
        files = {"projectZip": ("data.zip", f, "application/zip")}
        response = requests.post(_get_request_url(target_platform), files=files, data=_get_payload_data(workflow_name))

        if response.status_code != 200:
            return (False, response.text)

        return (True, response.content)


def convert_workflow(input_zip_path: pathlib.Path, output_folder: pathlib.Path, target_platform: str):
    logger = logging.getLogger("ConversionLogger")
    logger.info(f"Starting conversion for {input_zip_path}")

    start_time = time.time()

    with tempfile.TemporaryDirectory() as tmp_dir:
        # use zip guid as the name of the folder
        input_file_name = input_zip_path.parts[-1][:-4]
        formatted_zip_path = os.path.join(tmp_dir, f"{input_file_name}_content.zip")
        xaml_files_paths = extract_contents_zip(input_zip_path, formatted_zip_path)

        for xaml_file_path in xaml_files_paths:
            is_success, result = send_conversion_request(target_platform, formatted_zip_path, xaml_file_path)
            if is_success:
                xaml_file = xaml_file_path.split("/")[-1][:-5]
                result_zip_name = f"processed_{input_file_name}_{xaml_file}.zip"
                result_zip_path = os.path.join(output_folder, result_zip_name)
                with open(result_zip_path, "wb") as f:
                    f.write(result)
            else:
                logger.error(f"Conversion failed for {input_zip_path} with error: {result}")

    logger.info(f"Conversion done in {time.time() - start_time} seconds for {input_zip_path}")


def convert_multiple_workflows(
    input_dir: str,
    output_folder: str,
    target_platform: t.Literal["windows", "legacy"],
    num_files: int | None = None,
):
    # execution constants
    max_workers = CONVERTER_CONFIGS[target_platform]["max_workers"]

    # get all zips from the input directory
    files = os.listdir(input_dir)
    zip_names = files[:num_files]
    zips_paths = [os.path.join(input_dir, file) for file in zip_names]

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(convert_workflow, zip_path, output_folder, target_platform): zip_path for zip_path in zips_paths}
        for future in concurrent.futures.as_completed(futures):
            file = futures[future]
            try:
                _ = future.result()
            except Exception as exc:
                print(f"{file} generated an exception: {exc}")


if __name__ == "__main__":
    configure_logging()
    input_zip_path = pathlib.Path(r"C:\Projects\StudioData\Workflows\zips_portable\0d58d516-0a4c-c0ac-d364-c932bfc953c7.zip")
    output_folder = pathlib.Path(r"C:\Projects\StudioData\Workflows\converted_portable")

    start_time = time.time()
    convert_workflow(input_zip_path, output_folder, "windows")

    zips_folder_path = pathlib.Path(r"C:\Projects\StudioData\Workflows\zips")
    # convert_multiple_workflows(zips_folder_path, output_folder, "windows")
    duration = time.time() - start_time

    print("Conversion done in", duration, "seconds")
