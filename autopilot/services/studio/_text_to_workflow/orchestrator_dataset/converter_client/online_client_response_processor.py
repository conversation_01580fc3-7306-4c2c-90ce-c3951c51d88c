import json
import os


def check_json(json_path: str):
    try:
        with open(json_path, "r") as json_file:
            json_data = json.load(json_file)
            is_success = json_data.get("Success", False)
            has_errors = len(json_data.get("Errors", [])) > 0
            return is_success, has_errors
    except Exception as e:
        print(f"Error processing {json_path}: {e}")

    return False, False


if __name__ == "__main__":
    input_dir = r"C:\Projects\StudioData\Workflows\converted_windows_unzipped"

    total_processed = 0
    total_success = 0
    total_success_with_errors = 0

    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(".yaml"):
                file_path = os.path.join(root, file)

                is_success, has_errors = check_json(file_path)
                total_processed += 1
                if is_success:
                    total_success += 1
                if is_success and has_errors:
                    total_success_with_errors += 1

    print(f"Total processed: {total_processed}")
    print(f"Total success: {total_success}")
    print(f"Total success with errors: {total_success_with_errors}")
