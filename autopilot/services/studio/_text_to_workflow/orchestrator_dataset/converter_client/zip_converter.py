import concurrent.futures
import os
import tempfile
import zipfile


def extract_contents_zip(original_zip_path, output_zip_path):
    """
    Extracts the files used for conversion from the original zip file to a new zip file.

    Args:
        original_zip_path (str): The path to the original zip file.
        output_zip_path (str): The path to the new zip file.

    Returns:
        xaml_files (list(str)): A list of the xaml files that were extracted.
    """
    with zipfile.ZipFile(original_zip_path, "r") as zip_ref:
        all_files = zip_ref.namelist()

        # find the folder path that contains all xaml files
        xaml_files = [f for f in all_files if f.endswith(".xaml")]
        xaml_files_paths = [f.split("/") for f in xaml_files]
        shortest_path = min(xaml_files_paths, key=len)
        folder_name = "/".join(shortest_path[:-1])

        # xaml paths in the temp zip which will be created
        xaml_files_to_process = [f[len(folder_name) + 1 :] for f in xaml_files if f.startswith(folder_name + "/")]

        # Filter out the files that are in the specific folder
        files_to_extract = [f for f in all_files if f.startswith(folder_name + "/")]

        with tempfile.TemporaryDirectory() as tmpdirname:
            for file in files_to_extract:
                zip_ref.extract(file, tmpdirname)

            with zipfile.ZipFile(output_zip_path, "w") as new_zip:
                for file in files_to_extract:
                    file_path = os.path.join(tmpdirname, file)
                    new_file_name = file[len(folder_name) + 1 :]
                    new_zip.write(file_path, arcname=new_file_name)

    return xaml_files_to_process


def _unzip_processed_file(input_file_path, output_dir_path):
    """
    Unarchives a single zip file to a specified directory.

    Args:
    input_file_path (str): Path to the zip file.
    output_dir_path (str): Directory where the files will be extracted.
    """

    input_file_name = os.path.basename(input_file_path)[: -len(".zip")]

    # create new folder for the extracted files
    output_dir_path = os.path.join(output_dir_path, input_file_name)
    os.makedirs(output_dir_path, exist_ok=True)

    with zipfile.ZipFile(input_file_path, "r") as zip_ref:
        zip_ref.extractall(output_dir_path)


def unzip_processed_files(input_dir_path, output_dir_path):
    """
    Processes multiple zip files in parallel by unarchiving them.

    Args:
    input_dir_path (str): Directory which contains the processed zips.
    output_dir_path (str): Base directory where the files will be extracted.
    """
    zip_files = [os.path.join(input_dir_path, f) for f in os.listdir(input_dir_path) if f.endswith(".zip")]
    with concurrent.futures.ProcessPoolExecutor(max_workers=50) as executor:
        futures = [executor.submit(_unzip_processed_file, zip_path, output_dir_path) for zip_path in zip_files]
        for future in futures:
            future.result()


if __name__ == "__main__":
    input_dir_path = r"C:\Projects\StudioData\Workflows\converted_windows_zipped"
    output_dir_path = r"C:\Projects\StudioData\Workflows\converted_windows_unzipped"

    # _unzip_processed_file(input_zip_path, output_dir_path)
    unzip_processed_files(input_dir_path, output_dir_path)
