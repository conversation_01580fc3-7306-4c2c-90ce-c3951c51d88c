import concurrent.futures
import io
import pathlib as pt
import zipfile

import azure.core.exceptions as azex
import azure.storage.blob as azblob
import pandas as pd
import tqdm

from services.studio._text_to_workflow.orchestrator_dataset import orchestrator_dataset_constants
from services.studio._text_to_workflow.orchestrator_dataset.orchestrator_dataset_schema import MetadataBlobInfo, MetadataIndex, MetadataInfo
from services.studio._text_to_workflow.utils import azure_blob_storage, paths


def _extract_blob_zip(container_client: azblob.ContainerClient, blob_path: pt.Path, local_path: pt.Path) -> bool:
    try:
        blob_client = container_client.get_blob_client(blob_path.as_posix())
        zipbytes = blob_client.download_blob().readall()
        local_path.mkdir(parents=True, exist_ok=True)
        with zipfile.ZipFile(io.BytesIO(zipbytes)) as zf:
            zf.extractall(local_path)
        found = True
    except (azex.ResourceNotFoundError, OSError):
        found = False
    return found


def _is_zip_empty(local_path: pt.Path) -> bool:
    return not any(local_path.rglob("*.xaml")) and not any(local_path.rglob("*.dll"))


def merge_metadata_csvs(paths: tuple[pt.Path, ...]) -> pd.DataFrame:
    return pd.concat((pd.read_csv(p) for p in paths))


def clean_metadata(df: pd.DataFrame) -> pd.DataFrame:
    df = df.sort_values(["Identifier", "TenantId", "Version"], ascending=[True, True, False])
    df = df.drop_duplicates(["Identifier", "TenantId"])
    df["TenantId"] = df["Path"].str.extract(r"orchestrator-([a-f0-9-]+)/")
    df["TargetFramework"] = df["TargetFramework"].fillna("Legacy")
    df = df.drop("Path", axis=1)
    df["Exists"] = None
    df["Empty"] = None
    df["BlobPath"] = None
    df["LocalPath"] = None
    df["ConversionSuccessCount"] = 0
    df["ConversionWarningsCount"] = 0
    df["ConversionErrorsCount"] = 0
    columns = [
        "AccountId",
        "TenantId",
        "Identifier",
        "Version",
        "TargetFramework",
        "JobCount",
        "SuccessRate",
        "Exists",
        "Empty",
        "BlobPath",
        "LocalPath",
        "ConversionSuccessCount",
        "ConversionWarningsCount",
        "ConversionErrorsCount",
    ]
    df = df[columns]
    df = df.set_index(["AccountId", "TenantId", "Identifier"])
    df = df.sort_values(["SuccessRate", "JobCount"], ascending=[False, False])
    return df


def download_storage_account_zip(index: MetadataIndex, row: MetadataInfo, storage_account_name, container_url: str) -> tuple[MetadataIndex, MetadataBlobInfo]:
    container_client = azblob.ContainerClient.from_container_url(container_url)
    blob_path = orchestrator_dataset_constants.blob_path_prefix / f"{index.TenantId}_{index.Identifier}.zip"
    local_path = paths.get_orchestrator_production_rawdata_path() / storage_account_name / row.TargetFramework / f"{index.TenantId}_{index.Identifier}"
    found = _extract_blob_zip(container_client, blob_path, local_path)
    empty = _is_zip_empty(local_path)
    blob_info = MetadataBlobInfo(
        Exists=found,
        Empty=empty,
        BlobPath=blob_path.as_posix(),
        LocalPath=local_path.relative_to(paths.get_orchestrator_production_rawdata_path()).as_posix(),
    )
    return index, blob_info


def download_storage_account_zips(df: pd.DataFrame, storage_account_name: str) -> pd.DataFrame:
    container_url = azure_blob_storage.create_container_sas_url(storage_account_name, orchestrator_dataset_constants.blob_container_name)
    df, found, empty = df.copy(), 0, 0
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for item in df.itertuples():
            index = MetadataIndex(*item[0])
            row = MetadataInfo(*item[1:])
            future = executor.submit(download_storage_account_zip, index, row, storage_account_name, container_url)
            futures.append(future)
        progress_bar = tqdm.tqdm(concurrent.futures.as_completed(futures), dynamic_ncols=True, total=len(futures))
        for future in progress_bar:
            index, blob_info = future.result()
            df.at[index, "BlobPath"] = blob_info.BlobPath
            df.at[index, "LocalPath"] = blob_info.LocalPath
            df.at[index, "Exists"] = blob_info.Exists
            df.at[index, "Empty"] = blob_info.Empty
            found += blob_info.Exists
            empty += blob_info.Empty
            progress_bar.set_postfix(found=found, empty=empty, total=len(futures))
    return df


def download_storage_account_json(blob_path: pt.Path, storage_account_name, container_url: str) -> None:
    blob_client = azblob.ContainerClient.from_container_url(container_url).get_blob_client(blob_path.as_posix())
    local_path = paths.get_orchestrator_production_rawdata_path() / storage_account_name / blob_path.name
    local_path.parent.mkdir(parents=True, exist_ok=True)
    filebytes = blob_client.download_blob().readall()
    with open(local_path, "wb") as f:
        f.write(filebytes)


def download_storage_account_jsons(storage_account_name: str):
    container_url = azure_blob_storage.create_container_sas_url(storage_account_name, orchestrator_dataset_constants.blob_container_name)
    container_client = azblob.ContainerClient.from_container_url(container_url)
    metadata_json_blobs = container_client.list_blobs(name_starts_with=orchestrator_dataset_constants.blob_path_prefix.as_posix())
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        progress_bar = tqdm.tqdm(metadata_json_blobs, dynamic_ncols=True, desc=f"Listing {storage_account_name} jsons")
        for blob in progress_bar:
            if not blob.name.endswith(".json"):
                continue
            blob_path = pt.Path(blob.name)
            future = executor.submit(download_storage_account_json, blob_path, storage_account_name, container_url)
            futures.append(future)
        as_completed = concurrent.futures.as_completed(futures)
        progress_bar = tqdm.tqdm(as_completed, dynamic_ncols=True, total=len(futures), desc=f"Downloading {storage_account_name} jsons")
        for future in progress_bar:
            future.result()


def download_jsons():
    download_storage_account_jsons("mlproddataneusa")
    download_storage_account_jsons("mlproddataeussa")


def download_zips():
    us = merge_metadata_csvs(orchestrator_dataset_constants.raw_metadata_paths_us)
    us = clean_metadata(us)
    us = download_storage_account_zips(us, "mlproddataeussa")
    us.to_feather(orchestrator_dataset_constants.metadata_path_us)

    eu = merge_metadata_csvs(orchestrator_dataset_constants.raw_metadata_paths_eu)
    eu = clean_metadata(eu)
    eu = download_storage_account_zips(eu, "mlproddataneusa")
    eu.to_feather(orchestrator_dataset_constants.metadata_path_eu)
