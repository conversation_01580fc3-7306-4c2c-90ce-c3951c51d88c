import csv
import json
import os

from tqdm import tqdm

index = {}
metadata = []
for fname in tqdm(os.listdir("Metadata"), "Metadata"):
    if not fname.endswith(".csv"):
        continue
    with open("Metadata/" + fname, encoding="utf-8", newline="") as csvfile:
        spamreader = csv.DictReader(csvfile, delimiter=",")
        for row in spamreader:
            row["csvfile"] = fname
            row["path0"] = "/".join(row["Path"].split("/")[:-1])
            metadata.append(row)
            index[row["path0"]] = row


d1 = {
    "AccountId": "",
    "Identifier": "",
    "JobCount": "43",
    "Path": "",
    "SuccessRate": "1.***************",
    "TargetFramework": "Windows",
    "TenantId": "",
    "Version": "1.0.7",
    "csvfile": "crpa-paid-orch0-eus-sql.csv",
    "path0": "",
}
d2 = {
    "dataset": "studio",
    "destination": "",
    "error": None,
    "full_name": "",
    "last_modified": **********,
    "name": "",
    "project": "studio",
    "region": "ent-us-east",
    "size": 206282,
    "source": "",
    "tenant_id": "",
    "type": "s",
    "version": "v24.02.09",
}

for folder in ("mlproddataeussa", "mlproddataneusa"):
    for fname in tqdm(os.listdir(folder), folder):
        if not fname.endswith(".json"):
            continue
        # print(fname)
        with open(f"{folder}/{fname}", encoding="utf-8") as fin:
            data = json.load(fin)
        # pprint(data)
        path0 = "/".join(data["source"].split("/")[:-1])
        if path0 in index:
            # print("Found")
            # pprint(index[path0])
            # print("=" * 10)
            # pprint(data)
            if "dataset" in index[path0] and index[path0]["destination"] != data["destination"]:
                print("Conflict on", path0, "(", index[path0]["destination"], "!=", data["destination"], ")")
            for k in ("dataset", "destination", "error", "last_modified", "project", "region", "size", "source", "tenant_id", "type", "version"):
                index[path0][k] = data[k]
            index[path0]["storage"] = folder

with open("index.json", "w") as fout:
    json.dump(index, fout, indent=2, sort_keys=True)

with open("index.csv", mode="w") as f:
    keys = sorted(d1.keys() | d2.keys()) + ["storage"]
    writer = csv.DictWriter(f, fieldnames=keys)
    writer.writeheader()
    for item in index.values():
        writer.writerow(item)
