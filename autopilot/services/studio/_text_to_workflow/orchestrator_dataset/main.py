import argparse

from services.studio._text_to_workflow.orchestrator_dataset import orchestrator_dataset_convert, orchestrator_dataset_download


def download_jsons():
    orchestrator_dataset_download.download_jsons()


def download_zips():
    orchestrator_dataset_download.download_zips()


def convert(target_framework: str):
    orchestrator_dataset_convert.convert(target_framework)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("job", choices=["download-jsons", "download-zips", "convert"])
    args, unknown = ap.parse_known_args()
    if args.job == "download-jsons":
        download_jsons()
    elif args.job == "download-zips":
        download_zips()
    elif args.job == "convert":
        ap.add_argument("--target-framework", type=str, choices=["Legacy", "Windows", "Portable"], required=True)
        args = ap.parse_args()
        convert(args.target_framework)
    else:
        print(f"Unkonw job: {args.job}")
