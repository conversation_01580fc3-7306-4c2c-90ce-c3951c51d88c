import typing_extensions as t


class MetadataProcessInfo(t.NamedTuple):
    Version: str
    TargetFramework: str
    JobCount: int
    SuccessRate: float


class MetadataBlobInfo(t.NamedTuple):
    Exists: bool | None
    Empty: bool | None
    BlobPath: str | None
    LocalPath: str | None


class MetadataConversionInfo(t.NamedTuple):
    ConversionSuccessCount: int
    ConversionWarningsCount: int
    ConversionErrors: int


class MetadataIndex(t.NamedTuple):
    AccountId: str
    TenantId: str
    Identifier: str


class MetadataInfo(t.NamedTuple):
    Version: str
    TargetFramework: str
    JobCount: int
    SuccessRate: float
    Exists: bool | None
    Empty: bool | None
    BlobPath: str | None
    LocalPath: str | None
    ConversionSuccessCount: int
    ConversionWarningsCount: int
    ConversionErrorsCount: int
