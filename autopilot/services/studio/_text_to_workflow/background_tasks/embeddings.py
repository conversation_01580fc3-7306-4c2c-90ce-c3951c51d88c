import asyncio
import datetime
import functools
import multiprocessing
from concurrent import futures
from typing import Any, Callable

from fastapi import FastAP<PERSON>

from services.studio._text_to_workflow.code_generation import code_generation_endpoint
from services.studio._text_to_workflow.code_generation.code_generation_task import CodeGenerationTask
from services.studio._text_to_workflow.code_generation.main import run_code_generation_build
from services.studio._text_to_workflow.utils import embeddings_db, scheduling
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_task import WorkflowGenerationTask, run_workflow_generation_build

# Set multiprocess start method to spawn instead of forked
multiprocessing.set_start_method("spawn", force=True)


# Logging
LOGGER = AppInsightsLogger()

# Constants
BG_TASK_NAME = "Recurring refresh embeddings"


async def run_in_executor(
    executor: futures.Executor,
    fn: Callable[..., Any],
    timeout: float = 600,
    **kwargs: Any,
) -> Any:
    """
    Run a blocking function in an executor with a timeout.

    This function executes a given function in the provided executor asynchronously,
    with a specified timeout. If the function execution exceeds the timeout,
    a TimeoutError is raised.

    Args:
        executor (futures.Executor): The executor to run the function in.
        fn (Callable[..., Any]): The function to be executed.
        timeout (float, optional): The maximum execution time in seconds. Defaults to 600.
        **kwargs: Additional keyword arguments to pass to the function.

    Returns:
        Any: The result of the function execution.

    Raises:
        TimeoutError: If the function execution exceeds the specified timeout.
    """
    blocking = functools.partial(fn, **kwargs)
    loop = asyncio.get_event_loop()
    try:
        return await asyncio.wait_for(loop.run_in_executor(executor, blocking), timeout=timeout)
    except asyncio.TimeoutError:
        raise TimeoutError(f"Function {fn.__name__} execution timed out after {timeout} seconds")


async def shutdown_executor(app: FastAPI, timeout: float = 5.0) -> None:
    """
    Shutdown the executor stored in the FastAPI app state.

    This function attempts to gracefully shutdown the executor with a specified timeout.
    If the shutdown exceeds the timeout, it forces the shutdown without waiting for pending tasks.

    Args:
        app (FastAPI): The FastAPI application instance.
        timeout (float, optional): The maximum time to wait for executor shutdown in seconds. Defaults to 5.0.
    """

    async def _shutdown_executor(executor: futures.ProcessPoolExecutor) -> None:
        executor.shutdown(wait=True, cancel_futures=True)

    try:
        await asyncio.wait_for(_shutdown_executor(app.state.executor), timeout=timeout)
    except asyncio.TimeoutError:
        LOGGER.warning("Executor shutdown timed out after 5 seconds")
        app.state.executor.shutdown(wait=False, cancel_futures=True)
    finally:
        app.state.executor = None


async def recurring_refresh_embeddings_task(every_m_minutes: int, offset_minutes: int, app: FastAPI) -> None:
    """
    Run a recurring task to refresh embeddings at specified intervals.

    This function sets up a recurring task that refreshes embeddings periodically.
    It calculates the next run time, sleeps until then, runs the refresh task, and
    then shuts down the executor. This process repeats indefinitely until the task
    is cancelled.

    Args:
        every_m_minutes (int): The interval between each refresh task in minutes.
        offset_minutes (int): The offset in minutes for scheduling the task.
        app (FastAPI): The FastAPI application instance.

    Raises:
        asyncio.CancelledError: If the recurring task is cancelled.
    """

    is_canceled = False
    while not is_canceled:
        try:
            now = datetime.datetime.now().replace(microsecond=0)
            next_run = scheduling.get_next_run_datetime(now, every_m_minutes, offset_minutes=offset_minutes)
            sleep_time = next_run - now
            LOGGER.info(f"Running next {BG_TASK_NAME} task at: {next_run}. Sleeping for {sleep_time}.")
            await asyncio.sleep(round(sleep_time.total_seconds()))

            await refresh_embeddings_task(app)

            now = datetime.datetime.now().replace(microsecond=0)
            LOGGER.info(f"Finished {BG_TASK_NAME} task at {now}.")
        except asyncio.CancelledError:
            LOGGER.info(f"{BG_TASK_NAME} task was cancelled.")
            is_canceled = True
        except Exception as ex:
            LOGGER.exception(f"Error in {BG_TASK_NAME} task: {ex}")


async def refresh_embeddings_task(app: FastAPI) -> None:
    """
    Refreshes embeddings if the DB hash has changed using ProcessPoolExecutor.

    """
    try:
        old_hash = embeddings_db.get_embeddings_hash()
        LOGGER.info("Downloading embeddings hash...")
        if not (await embeddings_db.download_hash()):
            LOGGER.warning("Downloading embeddings hash failed.")

        new_hash = embeddings_db.get_embeddings_hash()
        LOGGER.info("Checking embeddings hashes...")

        if new_hash == old_hash:
            LOGGER.info("Embeddings hashes are the same.")
            return

        LOGGER.info("Hash changed. Downloading embeddings...")
        if not (await embeddings_db.download_embeddings()):
            LOGGER.warning("Downloading embeddings failed.")
            return

        LOGGER.info("Initializing executor embeddings rebuild...")
        executor = futures.ProcessPoolExecutor(max_workers=1, mp_context=multiprocessing.get_context("spawn"))
        app.state.executor = executor  # Store executor in app state for potential shutdown later

        LOGGER.info(
            "Rebuilding embeddings for workflow_generation.",
            properties={
                "old_hash": old_hash,
                "new_hash": new_hash,
            },
        )
        await run_in_executor(executor, run_workflow_generation_build)
        LOGGER.info("Loading new workflow_generation embeddings...")
        workflow_generation_endpoint.TASK = WorkflowGenerationTask("prompt.yaml").load()

        LOGGER.info(
            "Rebuilding embeddings for code_generation.",
            properties={
                "old_hash": old_hash,
                "new_hash": new_hash,
            },
        )
        await run_in_executor(executor, run_code_generation_build)
        LOGGER.info("Loading new code_generation embeddings...")
        code_generation_endpoint.TASK = CodeGenerationTask("prompt.yaml")

    except Exception as ex:
        LOGGER.exception(f"Refresh Embeddings Exception: {ex}")
        raise
    finally:
        if hasattr(app.state, "executor") and app.state.executor:
            LOGGER.info("Shuting down executor...")
            await shutdown_executor(app)
