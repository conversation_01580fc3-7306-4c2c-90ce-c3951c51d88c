import collections
import copy
import itertools
import math
import pathlib
import sys
import time

import pandas as pd
import tqdm
import typing_extensions as t
import wandb
from activity_config import (
    activity_config_config,
    activity_config_constants,
    activity_config_dataset,
    activity_config_task,
)
from activity_config.activity_config_schema import (
    ActivityConfigConfig,
    ActivityConfigContext,
    ActivityConfigEvaluationExample,
    ActivityConfigEvaluationExampleParamItem,
    ActivityConfigEvaluationScore,
    ActivityConfigEvaluationScores,
    ActivityConfigExample,
    ActivityConfigResult,
    ActivityConfigRunResult,
    Statistics,
)

from services.studio._text_to_workflow.common import activity_retriever, params
from services.studio._text_to_workflow.common.constants import DELEGATE_VARIABLES_KEY
from services.studio._text_to_workflow.common.schema import Argument, ParamTypeCategory, SubsetName, TargetFramework, Variable
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsageJson
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load

_config: ActivityConfigConfig = activity_config_config.get()
_seed = 42


async def evaluate(
    name: str | None,
    group: str,
    frameworks: tuple[TargetFramework, ...],
    subsets: tuple[SubsetName, ...],
    limit: int | None,
    update_whitelist: bool,
) -> tuple[
    list[ActivityConfigEvaluationExample],
    list[ActivityConfigEvaluationExampleParamItem],
    ActivityConfigEvaluationScores,
    dict[str, Statistics],
]:
    client = wandb.init(
        project=activity_config_constants.wandb_project,
        entity=activity_config_constants.wandb_entity,
        job_type=activity_config_constants.wandb_evaluation_job,
        group=group or "default",
        dir=paths.get_logs_path(),
        name=name,
    )
    assert wandb.run is not None
    run_dir = paths.get_activity_config_runs_path() / client.group / client.job_type / client.name
    run_dir.mkdir(parents=True, exist_ok=True)
    # Log config
    config = copy.deepcopy(_config)
    for key in config["prompt"]:
        config["prompt"][key] = multiline_str(config["prompt"][key])
    yaml_dump(config, run_dir / "summary.log")
    # Run and log evaluation
    activity_eval_items, param_eval_items, activity_eval_df, param_eval_df, summary, statistics = await get_evaluation_result(
        run_dir,
        frameworks,
        subsets,
        limit,
    )
    scores = {f"{tc}.{vc}": score for tc, score_dict in summary.items() for vc, score in score_dict.items()}  # type: ignore
    with open(run_dir / "summary.log", "a+") as f:
        for file in (sys.stdout, f):
            print(f"Framework: {','.join(frameworks)}. Subsets: {','.join(subsets)}", file=file)
            print("Scores:", file=file)
            df = pd.DataFrame([{"name": score_name, **scores} for score_name, scores in scores.items()])
            print(df.to_markdown(), file=file)
            print("Statistics:", file=file)
            df = pd.DataFrame([{"name": name, **statistics} for name, statistics in statistics.items()])
            print(df.to_markdown(), file=file)
    wandb.run.summary.update(dict(summary))
    wandb.run.summary.update(dict(statistics))
    # Create and log whitelist
    activity_id_eval_df, whitelist_df = await evaluate_by_activity_id(activity_eval_df)
    with open(run_dir / "summary.log", "a+") as f:
        for file in (sys.stdout, f):
            print("Activity Whitelist:", file=file)
            print(whitelist_df.to_markdown(), file=file)
    artifact = wandb.Artifact("activity_evaluation", type="evaluation")
    artifact.add(wandb.Table(dataframe=activity_id_eval_df.reset_index()), "activity_evaluation")
    artifact.add(wandb.Table(dataframe=whitelist_df.reset_index()), "activity_whitelist")
    wandb.run.log_artifact(artifact)
    if update_whitelist:
        yaml_dump(sorted(whitelist_df.index.tolist()), pathlib.Path(__file__).parent / "whitelist.yaml")
    wandb.run.config.update({activity_config_constants.wandb_evaluation_job: dict(frameworks=frameworks, subsets=subsets, **_config)})
    return activity_eval_items, param_eval_items, summary, statistics


async def evaluate_by_activity_id(df_activity_eval: pd.DataFrame, df_usage: pd.DataFrame | None = None) -> tuple[pd.DataFrame, pd.DataFrame]:
    df_activity_id_eval = get_activities_df()
    if df_usage is not None:
        df_activity_id_eval.loc[df_activity_id_eval.index.isin(df_usage.index), "usage"] = df_usage
    df_activity_id_metrics = df_activity_eval.groupby("activity_id").agg(
        num_examples=("id", "count"),
        avg_num_inputs=("inputs_support", "mean"),
        avg_precision_inputs=("inputs_precision", "mean"),
        avg_recall_inputs=("inputs_recall", "mean"),
    )
    df_activity_id_eval.loc[df_activity_id_metrics.index, df_activity_id_metrics.columns] = df_activity_id_metrics
    num_examples_mask = df_activity_id_eval["num_examples"] >= _config["whitelist"]["min_num_examples"]
    avg_num_inputs = df_activity_id_eval["avg_num_inputs"] >= _config["whitelist"]["min_num_inputs"]
    avg_precision_mask = df_activity_id_eval["avg_precision_inputs"] >= _config["whitelist"]["min_precision"]
    avg_recall_mask = df_activity_id_eval["avg_recall_inputs"] >= _config["whitelist"]["min_recall"]
    mask = num_examples_mask & avg_num_inputs & avg_precision_mask & avg_recall_mask
    df_activity_id_eval.loc[mask, "accepted"] = True
    whitelist_df = df_activity_id_eval[df_activity_id_eval["accepted"]]
    return df_activity_id_eval, whitelist_df


async def debug(example_id: str, framework: TargetFramework, subset: SubsetName) -> None:
    example_path = paths.get_activity_config_dataset_path("EvaluationSet", framework, subset) / f"{example_id}.yaml"
    example = activity_config_dataset.load_activity_config_example(example_path)
    activity_config_result, duration = await get_run_result(example)
    activity_eval_item = get_activity_eval_item(
        prediction=activity_config_result["result"],
        context=activity_config_result["context"],
        completion=activity_config_result["completion"],
        usage=activity_config_result["usage"],
        target=example["output"],
        duration=duration,
        framework=framework,
        subset=subset,
        activity_eval_item_id=example_id,
    )
    param_eval_items = get_param_eval_items(activity_eval_item)
    activity_eval_df = pd.DataFrame([activity_eval_item])
    param_eval_df = pd.DataFrame(param_eval_items)
    summary = calculate_evaluation_scores(param_eval_df)
    statistics = calculate_evaluation_statistics(activity_eval_df)
    print(example_id)
    print("Target Configuration")
    print(activity_eval_item["target_configuration"])
    print("Predicted Configuration")
    print(activity_eval_item["pred_configuration"])
    print("Statistics")
    df = pd.DataFrame([{"name": name, **statistics} for name, statistics in statistics.items()])
    print(df.to_markdown())
    print("Scores")
    scores = {f"{tc}.{vc}": score for tc, score_dict in summary.items() for vc, score in score_dict.items()}  # type: ignore
    df = pd.DataFrame([{"name": name, **scores} for name, scores in scores.items()])
    print(df.to_markdown())


async def get_evaluation_result(
    run_dir: pathlib.Path,
    frameworks: tuple[TargetFramework, ...],
    subsets: tuple[SubsetName, ...],
    limit: int | None,
) -> tuple[
    list[ActivityConfigEvaluationExample],
    list[ActivityConfigEvaluationExampleParamItem],
    pd.DataFrame,
    pd.DataFrame,
    ActivityConfigEvaluationScores,
    dict[str, Statistics],
]:
    activity_eval_items, param_eval_items = await create_evaluation_data(run_dir, frameworks, subsets, limit)
    activity_eval_df = pd.DataFrame([{k: v for k, v in item.items() if k != "context"} for item in activity_eval_items])
    param_eval_df = pd.DataFrame([item for item in param_eval_items])
    activity_eval_df.to_feather(run_dir / "activity_eval.feather")
    param_eval_df.to_feather(run_dir / "param_eval.feather")
    summary = calculate_evaluation_scores(param_eval_df)
    statistics = calculate_evaluation_statistics(activity_eval_df)
    return activity_eval_items, param_eval_items, activity_eval_df, param_eval_df, summary, statistics


async def create_evaluation_data(
    run_dir: pathlib.Path,
    frameworks: tuple[TargetFramework, ...],
    subsets: tuple[SubsetName, ...],
    limit: int | None,
) -> tuple[list[ActivityConfigEvaluationExample], list[ActivityConfigEvaluationExampleParamItem]]:
    activity_eval_items: list[ActivityConfigEvaluationExample] = []
    param_eval_items: list[ActivityConfigEvaluationExampleParamItem] = []
    dataset_path = paths.get_activity_config_dataset_path("EvaluationSet")
    for framework, subset in itertools.product(frameworks, subsets):
        total = activity_config_dataset.total("EvaluationSet", framework, subset)
        iterator = activity_config_dataset.iterate("EvaluationSet", framework, subset, limit)
        progress_bar = tqdm.tqdm(iterator, total=total, dynamic_ncols=True, desc="Evaluation:")
        for path, example in iterator:
            activity_eval_item_id = path.relative_to(dataset_path)
            activity_eval_item_path = run_dir / activity_eval_item_id
            if activity_eval_item_path.exists():
                activity_eval_item = yaml_load(activity_eval_item_path)
            else:
                activity_config_result, duration = await get_run_result(example)
                activity_eval_item = get_activity_eval_item(
                    prediction=activity_config_result["result"],
                    context=activity_config_result["context"],
                    completion=activity_config_result["completion"],
                    usage=activity_config_result["usage"],
                    target=example["output"],
                    duration=duration,
                    framework=framework,
                    subset=subset,
                    activity_eval_item_id=activity_eval_item_id.as_posix(),
                )
            activity_param_eval_items = get_param_eval_items(activity_eval_item)
            activity_param_eval_items_df = pd.DataFrame([item for item in activity_param_eval_items])
            activity_eval_scores = calculate_evaluation_scores(activity_param_eval_items_df)
            activity_eval_item["inputs_precision"] = activity_eval_scores["inputs"]["overall"]["precision"]
            activity_eval_item["inputs_recall"] = activity_eval_scores["inputs"]["overall"]["recall"]
            activity_eval_item["inputs_support"] = activity_eval_scores["inputs"]["overall"]["support"]
            activity_eval_item["outputs_precision"] = activity_eval_scores["outputs"]["overall"]["precision"]
            activity_eval_item["outputs_recall"] = activity_eval_scores["outputs"]["overall"]["recall"]
            activity_eval_item["outputs_support"] = activity_eval_scores["outputs"]["overall"]["support"]
            for multiline_str_key in ("target_configuration", "pred_configuration", "completion"):
                activity_eval_item[multiline_str_key] = multiline_str(activity_eval_item[multiline_str_key])
            activity_eval_item_path.parent.mkdir(parents=True, exist_ok=True)
            yaml_dump(activity_eval_item, activity_eval_item_path)
            activity_eval_items.append(activity_eval_item)
            param_eval_items.extend(activity_param_eval_items)
            description = (
                f"Evaluation: {framework}/{subset} : "
                f"{activity_eval_item['activity_id'][-30:]:<30} : "
                f"{activity_eval_item['inputs_precision'] or math.nan:.4f} | {activity_eval_item['inputs_recall'] or math.nan:.4f}"
            )
            progress_bar.update(1)
            progress_bar.set_description(description)
    return activity_eval_items, param_eval_items


async def get_run_result(example: ActivityConfigExample) -> tuple[ActivityConfigRunResult, float]:
    example = copy.deepcopy(example)
    start = time.time()
    result = await activity_config_task.run(example["input"], [example["demo_id"]])
    duration = time.time() - start
    assert result is not None, "Whitelist is enabled on evaluation."
    return result, duration


def get_activity_eval_item(
    prediction: ActivityConfigResult,
    context: ActivityConfigContext,
    completion: str,
    usage: TokenUsageJson,
    duration: float,
    target: ActivityConfigResult,
    framework: TargetFramework,
    subset: SubsetName,
    activity_eval_item_id: str,
) -> ActivityConfigEvaluationExample:
    target["configuration"] = params.sanitize(target["configuration"], context["activity_typedef"])
    prediction["configuration"] = params.sanitize(prediction["configuration"], context["activity_typedef"])
    activity_eval_item = ActivityConfigEvaluationExample(
        id=activity_eval_item_id,
        activity_id=context["activity_id"],
        target_configuration=yaml_dump(target),
        pred_configuration=yaml_dump(prediction),
        completion=completion,
        inputs_precision=None,
        inputs_recall=None,
        inputs_support=0,
        outputs_precision=None,
        outputs_recall=None,
        outputs_support=0,
        duration=duration,
        prompt_tokens=usage["promptTokens"],
        completion_tokens=usage["completionTokens"],
        total_tokens=usage["totalTokens"],
        context=context,
        framework=framework,
        subset=subset,
    )
    return activity_eval_item


def get_param_eval_items(activity_eval_item: ActivityConfigEvaluationExample) -> list[ActivityConfigEvaluationExampleParamItem]:
    activity_eval_item_id = activity_eval_item["id"]
    target_params = yaml_load(str(activity_eval_item["target_configuration"]))["configuration"]
    pred_params = yaml_load(str(activity_eval_item["pred_configuration"]))["configuration"]
    activity_eval_item_duration = activity_eval_item["duration"]
    activity_id = activity_eval_item["activity_id"]
    activity_typedef = activity_eval_item["context"]["activity_typedef"]
    workflow = activity_eval_item["context"]["workflow"]
    framework = activity_eval_item["framework"]
    subset = activity_eval_item["subset"]
    variables_list = workflow.get("arguments", []) + workflow.get("variables", [])
    variables_dict = {variable["name"]: variable for variable in variables_list}
    param_names = set(target_params.keys()).union(pred_params.keys())

    items = []
    for param_name in param_names:
        param_type = activity_typedef["params"].get(param_name, {}).get("type", None)
        param_type_category = activity_typedef["params"].get(param_name, {}).get("category", None)

        param_target_value = target_params.get(param_name, None)
        param_target_value_category = params.get_param_value_category(param_target_value)
        param_predicted_value = pred_params.get(param_name, None)
        param_predicted_value_category = params.get_param_value_category(param_predicted_value)
        param_predicted_is_correct = compare_params(param_target_value, param_predicted_value, param_type_category, variables_dict)

        if param_target_value is not None and not isinstance(param_target_value, str):
            param_target_value = yaml_dump(param_target_value)
        if param_predicted_value is not None and not isinstance(param_predicted_value, str):
            param_predicted_value = yaml_dump(param_predicted_value)
        item = ActivityConfigEvaluationExampleParamItem(
            id=f"{activity_eval_item_id}/{param_name}",
            activity_eval_item_id=activity_eval_item_id,
            param_target_value=param_target_value,
            param_pred_value=param_predicted_value,
            pred_correct=param_predicted_is_correct,
            param_name=param_name,
            param_type=param_type,
            param_type_category=param_type_category,
            param_target_value_category=param_target_value_category,
            param_pred_value_category=param_predicted_value_category,
            duration=activity_eval_item_duration,
            activity_id=activity_id,
            framework=framework,
            subset=subset,
        )
        items.append(item)
    return items


def compare_params(
    target_value: t.Any,
    predicted_value: t.Any,
    param_type_category: ParamTypeCategory | None,
    variables: dict[str, Variable | Argument],
) -> bool:
    if predicted_value is None and target_value is None:
        return True
    if param_type_category == "InArgument":
        if predicted_value is None or target_value is None:
            return False
        if not isinstance(predicted_value, str):
            return False
        if params.is_variable_reference(predicted_value):
            variable_name = predicted_value.removeprefix("[[").removesuffix("]]")
            if variable_name not in variables:
                return False
        return target_value == predicted_value
    elif param_type_category == "OutArgument":
        if predicted_value is None:
            return False
        if not isinstance(predicted_value, str):
            return False
        return params.is_variable_reference(predicted_value)
    elif param_type_category == "ActivityAction":
        if not isinstance(predicted_value, dict):
            return False
        predicted_variables = predicted_value.get(DELEGATE_VARIABLES_KEY, [])
        target_variables = target_value.get(DELEGATE_VARIABLES_KEY, [])
        if len(predicted_variables) != len(target_variables):
            return False
        predicted_variable_types = collections.Counter([variable.get("type") for variable in predicted_variables])
        target_variable_types = collections.Counter([variable.get("type") for variable in target_variables])
        if predicted_variable_types != target_variable_types:
            return False
        for variable in predicted_variables:
            variable_name = variable.get("name")
            if variable_name is None:
                return False
            if not params.is_variable_reference(f"[[{variable_name}]]"):
                return False
        return True
    else:
        return target_value == predicted_value


def calculate_evaluation_scores(param_df: pd.DataFrame) -> ActivityConfigEvaluationScores:
    overall = {"overall": calculate_overall_scores(param_df), **calculate_overall_scores_by_pred_value_category(param_df)}
    inputs = {"overall": calculate_inputs_scores(param_df), **calculate_inputs_scores_by_pred_value_category(param_df)}
    outputs = {"overall": calculate_outputs_scores(param_df), **calculate_outputs_scores_by_pred_value_category(param_df)}

    return {"overall": overall, "inputs": inputs, "outputs": outputs}


def calculate_evaluation_statistics(activity_df: pd.DataFrame) -> dict[str, Statistics]:
    statistics = {}
    for column in ("duration", "prompt_tokens", "completion_tokens", "total_tokens"):
        statistics[column] = calculate_statistics(activity_df, column)
    return statistics


def calculate_overall_scores(df: pd.DataFrame) -> ActivityConfigEvaluationScore:
    return calculate_scores(df, "param_target_value", "param_pred_value")


def calculate_inputs_scores(df: pd.DataFrame) -> ActivityConfigEvaluationScore:
    df = filter_df_by_column(df, "param_type_category", ["InArgument", "InOutArgument"])
    return calculate_scores(df, "param_target_value", "param_pred_value")


def calculate_outputs_scores(df: pd.DataFrame) -> ActivityConfigEvaluationScore:
    df = filter_df_by_column(df, "param_type_category", ["OutArgument"])
    return calculate_scores(df, "param_target_value", "param_pred_value")


def calculate_overall_scores_by_pred_value_category(df) -> dict[str, ActivityConfigEvaluationScore]:
    return calculate_scores_by_column(df, "param_pred_value_category", True)


def calculate_inputs_scores_by_pred_value_category(df) -> dict[str, ActivityConfigEvaluationScore]:
    df = filter_df_by_column(df, "param_type_category", ["InArgument", "InOutArgument"])
    return calculate_scores_by_column(df, "param_pred_value_category", True)


def calculate_outputs_scores_by_pred_value_category(df) -> dict[str, ActivityConfigEvaluationScore]:
    df = filter_df_by_column(df, "param_type_category", ["OutArgument"])
    return calculate_scores_by_column(df, "param_pred_value_category", True)


def calculate_statistics(activity_df: pd.DataFrame, column: str) -> Statistics:
    if activity_df.empty:
        return {"mean": math.nan, "std": math.nan, "median": math.nan}
    return {"mean": activity_df[column].mean(), "std": activity_df[column].std(), "median": activity_df[column].median()}


def get_output_false_predictions(df: pd.DataFrame) -> pd.DataFrame:
    df = filter_df_by_column(df, "param_type_category", ["OutArgument"])
    return df[~df["pred_correct"]]


def get_input_false_predictions(df: pd.DataFrame) -> pd.DataFrame:
    df = filter_df_by_column(df, "param_type_category", ["InArgument", "InOutArgument"])
    return df[~df["pred_correct"]]


def filter_df_by_column(df: pd.DataFrame, column: str, values: list) -> pd.DataFrame:
    if df.empty:
        return df
    return df[df[column].isin(values)]


def split_df_by_column(df: pd.DataFrame, column: str, dropna: bool = False) -> dict[str, pd.DataFrame]:
    if df.empty:
        return {}
    return {str(name): group for name, group in df.groupby(column, dropna=dropna)}


def calculate_scores_by_column(df: pd.DataFrame, column: str, dropna: bool = False) -> dict[str, ActivityConfigEvaluationScore]:
    dfs = split_df_by_column(df, column, dropna)
    scores = {}
    for group_name, group_df in dfs.items():
        group_scores = calculate_scores(group_df, "param_target_value", "param_pred_value")
        scores[group_name] = group_scores
    return scores


def calculate_scores(df: pd.DataFrame, target_column: str, prediction_column: str) -> ActivityConfigEvaluationScore:
    if df.empty:
        return {"precision": None, "recall": None, "support": 0}
    # Check if all output parameters have a target value. This is a requirement for calculating recall correctly on output parameters.
    param_is_output, target_value_is_positive = df["param_type_category"] == "OutArgument", df["param_target_value"].notna()
    assert target_value_is_positive[param_is_output].all(), "Not all output parameters have a target value."
    positive_targets, positive_predictions, true_predictions = df[target_column].notna(), df[prediction_column].notna(), df["pred_correct"]
    true_positive_predictions = true_predictions & positive_predictions
    num_true_positive_predictions = true_positive_predictions.sum()
    precision, recall = None, None
    if num_positive_predictions := positive_predictions.sum():
        precision = float(num_true_positive_predictions / num_positive_predictions)
    if num_positive_targets := positive_targets.sum():
        recall = float(num_true_positive_predictions / num_positive_targets)
    num_positive_targets = int(num_positive_targets)
    return {"precision": precision, "recall": recall, "support": num_positive_targets}


def read_evaluation_data(name: str, file: str) -> pd.DataFrame:
    df_path = paths.get_activity_config_runs_path() / name / file
    return pd.read_feather(df_path)


def get_activities_df() -> pd.DataFrame:
    retriever = activity_retriever.ActivitiesRetriever()
    activities = {}
    for (framework, type), index in retriever.index.items():
        if type == "trigger":
            continue
        for activity in index.store.state["activities"]:
            activity_id, package_id = activity["fullActivityId"], activity["packageName"]
            if activity_id in activities:  # skip duplicates (e.g. Portable and Windows versions)
                continue
            activities[activity_id] = {
                "activity_id": activity_id,
                "package_id": package_id,
                "framework": framework,
                "usage": 0,
                "accepted": False,
                "num_examples": 0,
                "avg_num_inputs": 0.0,
                "avg_precision_inputs": math.nan,
                "avg_recall_outputs": math.nan,
            }
    return pd.DataFrame.from_records(list(activities.values())).set_index("activity_id").sort_index()
