import glob
import pathlib

import tqdm
import typing_extensions as t

from services.studio._text_to_workflow.activity_config import activity_config_constants, activity_config_schema
from services.studio._text_to_workflow.common import schema, typedefs, workflow
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.walkers import BuildActivityConfigDemoExamples, BuildActivityConfigEvalExamples
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load

train_subsets: dict[schema.TargetFramework, tuple[schema.SubsetName, ...]] = {"Portable": ("train", "static", "uia"), "Windows": ("train",)}
test_subsets: dict[schema.TargetFramework, tuple[schema.SubsetName, ...]] = {"Portable": ("test",), "Windows": ("test",)}
prod_subsets: dict[schema.TargetFramework, tuple[schema.SubsetName, ...]] = {"Portable": ("prod",), "Windows": ("prod",)}


_retriever: ActivitiesRetriever = ActivitiesRetriever()


async def init() -> None:
    if not typedefs.exists():
        await typedefs.build(True)
    typedefs.load()


def create_activity_config_demonstration_set(target_framework: schema.TargetFramework) -> None:
    workflow_generation_dataset_path = paths.get_workflow_generation_dataset_path(target_framework)
    activity_config_demoset_path = paths.get_activity_config_dataset_path("DemonstrationSet", target_framework)
    workflow_generation_yaml_paths = sorted(workflow_generation_dataset_path.glob("*/*.yaml"))
    created, existing, included, excluded = {}, {}, 0, 0
    for split_path in activity_config_demoset_path.iterdir():
        if not split_path.is_dir():
            continue
        existing[split_path.name] = set(split_path.glob("*/*.yaml"))
    desc = "DemonstrationSet. Framework: {0}. Included {1}. Excluded {2}"
    pbar = tqdm.tqdm(workflow_generation_yaml_paths, desc=desc.format(target_framework, included, excluded), dynamic_ncols=True, leave=True)
    for workflow_generation_yaml_path in pbar:
        workflow_name = workflow_generation_yaml_path.stem
        workflow_split = workflow_generation_yaml_path.parent.name
        if workflow_split.startswith((".errors", "__")):
            continue
        workflow_generation_example = yaml_load(workflow_generation_yaml_path)
        activity_config_demo_examples = create_activity_config_demo_examples(workflow_generation_example, workflow_name)
        activity_config_dir_path = activity_config_demoset_path / workflow_split / workflow_name
        activity_config_dir_path.mkdir(parents=True, exist_ok=True)
        if workflow_split not in created:
            created[workflow_split] = set()
        for activity_config_demo_example in activity_config_demo_examples:
            activity = _retriever.get(activity_config_demo_example["activity_id"], target_framework)
            if _should_skip_activity_example(activity_config_demo_example, activity):
                excluded += 1
                continue
            activity_config_example_name = pathlib.Path(activity_config_demo_example["id"]).name + ".yaml"
            activity_config_example_path = activity_config_dir_path / activity_config_example_name
            activity_config_demo_example["output"]["explanation"] = multiline_str("...")
            yaml_dump(activity_config_demo_example, activity_config_example_path)
            created[workflow_split].add(activity_config_example_path)
            included += 1
        pbar.set_description(desc.format(target_framework, included, excluded))
    for split in sorted(set().union(existing.keys(), created.keys())):
        created_split = created.get(split, set())
        existing_split = existing.get(split, set())
        new_split = created_split.difference(existing_split)
        updated_split = created_split.intersection(existing_split)
        deleted_split = existing_split.difference(created_split)
        tqdm.tqdm.write(f"Split {split}:")
        tqdm.tqdm.write(f"\tExisting: {len(existing_split)}")
        tqdm.tqdm.write(f"\tNew: {len(new_split)}")
        tqdm.tqdm.write(f"\tUpdated: {len(updated_split)}")
        tqdm.tqdm.write(f"\tDeleted: {len(deleted_split)}")
        if deleted_split:
            activity_ids = set()
            for p in deleted_split:
                activity_id = p.name.split("___").pop()
                activity_ids.add(activity_id)
            msg = ["Existing activity ids to be deleted:"]
            for activity_id in sorted(activity_ids):
                msg.append(f"\t{activity_id}")
            tqdm.tqdm.write("\n".join(msg))
            confirm = input("Show detailed paths? (y/n):")
            if confirm.lower() == "y":
                msg = ["Existing activity config examples to be deleted:"]
                for p in sorted(deleted_split):
                    msg.append(f"\t{p.parent.name}/{p.name}")
                tqdm.tqdm.write("\n".join(msg))
            confirm = input("Confirm delete? (y/n):")
            if confirm.lower() == "y":
                for p in deleted_split:
                    p.unlink()
            for p in existing_split:
                if p.parent.exists() and p.parent.is_dir() and not list(p.parent.iterdir()):
                    p.parent.rmdir()


def create_activity_config_evaluation_set(target_framework: schema.TargetFramework) -> None:
    activity_config_demoset_path = paths.get_activity_config_dataset_path("DemonstrationSet", target_framework)
    activity_config_evalset_path = paths.get_activity_config_dataset_path("EvaluationSet", target_framework)
    activity_config_demo_yaml_paths = list(activity_config_demoset_path.glob("*/*/*.yaml"))
    existing, created, updated = set(activity_config_evalset_path.glob("*/*/*.yaml")), set(), set()
    desc = "EvaluationSet. Framework: {0}. Created {1}. Updated: {2}"
    pbar = tqdm.tqdm(activity_config_demo_yaml_paths, desc=desc.format(target_framework, created, updated), dynamic_ncols=True, leave=True)
    for activity_config_demo_yaml_path in pbar:
        activity_config_demo_example = yaml_load(activity_config_demo_yaml_path)
        activity_config_eval_examples = create_activity_config_eval_examples(activity_config_demo_example)
        subset = activity_config_demo_yaml_path.parent.parent.name
        for activity_config_eval_example in activity_config_eval_examples:
            activity_config_eval_example_name = pathlib.Path(activity_config_eval_example["id"] + ".yaml")
            activity_config_eval_example_path = activity_config_evalset_path / subset / activity_config_eval_example_name
            activity_config_eval_example_path.parent.mkdir(parents=True, exist_ok=True)
            if activity_config_eval_example_path.exists():
                updated.add(activity_config_eval_example_path)
            else:
                created.add(activity_config_eval_example_path)
            activity_config_eval_example["output"]["explanation"] = multiline_str("...")
            yaml_dump(activity_config_eval_example, activity_config_eval_example_path)
        pbar.set_description(desc.format(target_framework, len(created), len(updated)))
    to_delete = existing.difference(created.union(updated))
    if to_delete:
        tqdm.tqdm.write(f"Existing activity config examples to be deleted: {len(to_delete)}")
        confirm = input("Show detailed paths? (y/n):")
        if confirm.lower() == "y":
            msg = ["Existing activity config examples to be deleted:"]
            for p in sorted(to_delete):
                msg.append(f"\t{p.parent.name}/{p.name}")
            tqdm.tqdm.write("\n".join(msg))
        confirm = input("Confirm delete? (y/n):")
        if confirm.lower() == "y":
            for p in to_delete:
                p.unlink()


def create_activity_config_demonstration_sets() -> None:
    for target_framework in ("Portable", "Windows"):
        create_activity_config_demonstration_set(target_framework)


def create_activity_config_evaluation_sets() -> None:
    for target_framework in ("Portable", "Windows"):
        create_activity_config_evaluation_set(target_framework)


def create_activity_config_datasets() -> None:
    create_activity_config_demonstration_sets()
    create_activity_config_evaluation_sets()


def load_workflow_from_file(workflow_path: str | pathlib.Path) -> workflow.Workflow:
    workflow_example_dict = yaml_load(workflow_path)
    return load_workflow(workflow_example_dict)


def load_workflow(workflow_example_dict: dict) -> workflow.Workflow:
    workflow_example = workflow.Workflow(workflow_example_dict["description"], workflow_example_dict["plan"], workflow_example_dict["process"])
    workflow_example.lmyaml()
    return workflow_example


def create_activity_config_demo_examples(workflow_generation_example: dict, workflow_generation_id: str) -> list[activity_config_schema.ActivityConfigExample]:
    return BuildActivityConfigDemoExamples(workflow_generation_example, workflow_generation_id).build()


def create_activity_config_eval_examples(
    activity_config_demo_example: activity_config_schema.ActivityConfigExample,
) -> list[activity_config_schema.ActivityConfigExample]:
    return BuildActivityConfigEvalExamples(activity_config_demo_example).build()


def total(type: activity_config_schema.DatasetType, framework: schema.TargetFramework | None, subset: schema.SubsetName | None) -> int:
    return len(ls(type, framework, subset, sort=False))


def ls(
    type: activity_config_schema.DatasetType,
    framework: schema.TargetFramework | None,
    subset: schema.SubsetName | None,
    sort: bool = True,
    limit: int | None = None,
) -> list[pathlib.Path]:
    examples_glob_path = paths.get_activity_config_dataset_path(type, framework, subset) / "**" / "*.yaml"
    example_paths = [pathlib.Path(p) for p in glob.glob(examples_glob_path.as_posix(), recursive=True)]
    if sort:
        example_paths = sorted(example_paths)
    if limit is not None:
        example_paths = example_paths[:limit]
    return example_paths


def iterate(
    type: activity_config_schema.DatasetType,
    framework: schema.TargetFramework,
    subset: schema.SubsetName,
    limit: int | None = None,
) -> t.Iterator[tuple[pathlib.Path, activity_config_schema.ActivityConfigExample]]:
    yaml_paths = ls(type, framework, subset, True, limit)
    for activity_config_example_path in yaml_paths:
        activity_config_example = yaml_load(activity_config_example_path)
        yield activity_config_example_path, activity_config_example


def _should_skip_activity_example(activity_config_example: activity_config_schema.ActivityConfigExample, activity: schema.ActivityDefinition | None) -> bool:
    # Skip if we cannot find the activity definition
    if not activity:
        return True
    # Skip triggers
    if activity["isTrigger"]:
        return True
    # Skip some packages
    if activity["packageName"] in activity_config_constants.excluded_packages:
        return True
    # Skip some activities
    if activity["fullActivityId"] in activity_config_constants.excluded_activities:
        return True
    return False
