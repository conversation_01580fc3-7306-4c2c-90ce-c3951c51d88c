import re

from services.studio._text_to_workflow.activity_config import activity_config_config as acc
from services.studio._text_to_workflow.activity_config import activity_config_schema as acs
from services.studio._text_to_workflow.common import schema as cs
from services.studio._text_to_workflow.common.walkers import BuildActivityConfigurationContext
from services.studio._text_to_workflow.utils import telemetry_utils as tu
from services.studio._text_to_workflow.utils import yaml_utils as yu

_config = acc.get()
_system_template = _config["prompt"]["system_template"]
_user_template = _config["prompt"]["user_template"]
_assistant_template = _config["prompt"]["assistant_template"]
_user_instructions = _config["prompt"]["user_instructions"]
_logger = tu.AppInsightsLogger()


def build_system_message(activity_type_definitions: dict[str, cs.TypeDef], additional_type_definitions: dict[str, cs.TypeDef]) -> cs.Message:
    activity_type_definitions_text = "\n".join([typedef["text"].replace("\r\n", "\n") for typedef in activity_type_definitions.values()])
    additional_type_definitions_text = "\n".join([typedef["text"].replace("\r\n", "\n") for typedef in additional_type_definitions.values()])
    content = _system_template.format(
        activity_type_definitions=activity_type_definitions_text,
        additional_type_definitions=additional_type_definitions_text,
    ).strip()
    return {"role": "system", "content": content}


def build_user_message(context: acs.ActivityConfigContext, include_user_instructions: bool) -> cs.Message:
    workflow = yu.yaml_dump(context["workflow"]).strip()
    activity_configuration = yu.yaml_dump(context["activity"]).strip()
    activity_types = yu.yaml_dump(context["activity_types"]).strip()
    query = context["user_query"]
    user_instructions = _user_instructions if include_user_instructions else ""
    content = _user_template.format(
        user_instructions=user_instructions,
        workflow=workflow,
        activity=activity_configuration,
        activity_types=activity_types,
        query=query,
    ).strip()
    return {"role": "user", "content": content}


def build_assistant_message(configuration: acs.ActivityConfigResult) -> cs.Message:
    configuration["explanation"] = yu.multiline_str(configuration["explanation"])
    configuration_yaml = yu.yaml_dump(configuration).strip()
    content = _assistant_template.format(configuration=configuration_yaml).strip()
    return {"role": "assistant", "content": content}


def build_chat_messages(
    context: acs.ActivityConfigContext,
    demos: list[acs.ActivityConfigInteraction],
    activity_typedefs: dict[str, cs.TypeDef],
    additional_typedefs: dict[str, cs.TypeDef],
) -> list[cs.Message]:
    messages: list[cs.Message] = []
    system_message = build_system_message(activity_typedefs, additional_typedefs)
    messages.append(system_message)
    for demo in demos:
        user_message = build_user_message(demo["context"], include_user_instructions=False)
        assistant_message = build_assistant_message(demo["configuration"])
        messages.append(user_message)
        messages.append(assistant_message)
    user_message = build_user_message(context, include_user_instructions=True)
    messages.append(user_message)
    return messages


def create_current_activity_context(input: acs.ActivityConfigInput, params_to_keep_on_current_activity: set[str] | None) -> acs.ActivityConfigContext:
    current_activity_context = BuildActivityConfigurationContext(input, params_to_keep_on_current_activity).build()
    return current_activity_context


def create_activity_config_interaction(
    example: acs.ActivityConfigExample, params_to_keep_on_current_activity: set[str] | None
) -> acs.ActivityConfigInteraction:
    return {
        "context": create_current_activity_context(example["input"], params_to_keep_on_current_activity),
        "configuration": example["output"],
    }


def load_completion(completion: str, context: acs.ActivityConfigContext) -> acs.ActivityConfigResult:
    loaded = _load_completion_yaml(completion)
    result: acs.ActivityConfigResult = {"explanation": loaded.get("explanation", ""), "configuration": loaded.get("configuration", {})}
    activity_id = context["activity"].get("id", None)
    if activity_id in context["original_param_values"]:
        for param in result["configuration"]:
            if param in context["original_param_values"][activity_id]:
                result["configuration"][param] = context["original_param_values"][activity_id][param]
    return result


def _load_completion_yaml(output: str) -> dict:
    loaded = {}
    if output.startswith("```yaml"):
        match = re.match(r"```yaml(.*)```", output, re.DOTALL)
    elif output.startswith("```"):
        match = re.match(r"```(.*)```", output, re.DOTALL)
    else:
        match = re.match(r"(.*)", output, re.DOTALL)
    if not match:
        return loaded
    try:
        process_str = match.group(1)
    except IndexError:
        _logger.error("IndexError")
        return loaded
    try:
        loaded = yu.yaml_load(process_str)
        if not isinstance(loaded, dict):
            loaded = {}
    except yu.YAMLError:
        _logger.error("YAMLError in output. Trying to fix single quotes.")
        lines = process_str.splitlines()
        lines_fixed = []
        for line in lines:
            parts = line.split(": ", 1)
            if len(parts) > 1:
                # if w can load the line as yaml, skip it
                try:
                    yu.yaml_load(line)
                except yu.YAMLError:
                    key, value = parts
                    if not value.strip().startswith("'"):
                        value = f"'{value}"
                    if not value.strip().endswith("'"):
                        value = f"{value}'"
                    escaped_value = value[1:-1].replace("\\'", "''")
                    escaped_value = re.sub(r"(?<!')'(?!')", "''", value[1:-1])
                    value = f"'{escaped_value}'"
                    line = f"{key}: {value}"
            lines_fixed.append(line)
        process_str = "\n".join(lines_fixed)
        try:
            loaded = yu.yaml_load(process_str)
        except yu.YAMLError:
            _logger.error("YAMLError in output. Failed to fix single quotes.")
            return loaded
    return loaded
