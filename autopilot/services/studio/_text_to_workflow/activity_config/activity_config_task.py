import langchain.prompts
import langchain_community.callbacks
import langchain_core.messages

from services.studio._text_to_workflow.activity_config import activity_config_config, activity_config_demo_retriever, activity_config_messages
from services.studio._text_to_workflow.activity_config.activity_config_schema import (
    ActivityConfigConfig,
    ActivityConfigContext,
    ActivityConfigExample,
    ActivityConfigInput,
    ActivityConfigInteraction,
    ActivityConfigRunResult,
)
from services.studio._text_to_workflow.common.schema import Message, TypeDef
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump

LOGGER = telemetry_utils.AppInsightsLogger()

_config: ActivityConfigConfig = activity_config_config.get()


@telemetry_utils.log_execution_time("activity_config_task.run")
async def run(
    input: ActivityConfigInput,
    ignore_exampleids: list[str] | None = None,
) -> ActivityConfigRunResult | None:
    if ignore_exampleids is None:
        ignore_exampleids = []
    context = activity_config_messages.create_current_activity_context(input, None)
    demos = []
    if _config["retrieval"]["enable"]:
        examples, _scores = get_top_k_examples(context, ignore_exampleids)
        demos = prepare_demos(examples, context, _config["retrieval"]["clean_configuration"])
    activity_typedefs, additional_typedefs = gather_typedefs(context, demos)
    messages = build_chat_messages(context, demos, activity_typedefs, additional_typedefs)
    completion, usage = await _run_inference(messages)
    result = activity_config_messages.load_completion(completion, context)
    activity = {"thought": context["activity"]["thought"], "activity": context["activity"]["activity"], "params": result["configuration"]}
    lmyaml = yaml_dump(activity).strip()
    return {
        "lmyaml": lmyaml,
        "result": result,
        "completion": completion,
        "messages": messages,
        "usage": usage.to_json(),
        "context": context,
    }


@telemetry_utils.log_execution_time("activity_config_task.get_top_k_examples")
def get_top_k_examples(
    context: ActivityConfigContext,
    ignore_exampleids: list[str],
) -> tuple[list[ActivityConfigExample], list[float]]:
    variables, activity_id, top_k = context["workflow"].get("variables", []), context["activity_id"], _config["retrieval"]["top_k"]
    return activity_config_demo_retriever.get_top_k_demos(activity_id, variables, top_k, ignore_exampleids)


def prepare_demos(examples: list[ActivityConfigExample], context: ActivityConfigContext, clean_configuration: bool) -> list[ActivityConfigInteraction]:
    filter_current_activity_params_to = set(context["activity"]["params"].keys())
    return [activity_config_messages.create_activity_config_interaction(example, filter_current_activity_params_to) for example in examples]


def gather_typedefs(context: ActivityConfigContext, demos: list[ActivityConfigInteraction]) -> tuple[dict[str, TypeDef], dict[str, TypeDef]]:
    activity_typedefs, additional_typedefs = {}, {}
    for demo in demos:
        demo_context = demo["context"]
        activity_typedefs.update({demo_context["activity_id"]: demo_context["activity_typedef"]})
        additional_typedefs.update(demo_context["additional_typedefs"])
    # Always prefer the current context type definitions
    activity_typedefs.update({context["activity_id"]: context["activity_typedef"]})
    additional_typedefs.update(context["additional_typedefs"])
    return activity_typedefs, additional_typedefs


@telemetry_utils.log_execution_time("activity_config_task.build_chat_messages")
def build_chat_messages(
    context: ActivityConfigContext,
    demos: list[ActivityConfigInteraction],
    activity_typedefs: dict[str, TypeDef],
    additional_typedefs: dict[str, TypeDef],
) -> list[Message]:
    return activity_config_messages.build_chat_messages(context, demos, activity_typedefs, additional_typedefs)


@telemetry_utils.log_execution_time("activity_config_task.run_openai_inference")
async def _run_inference(messages: list[Message]) -> tuple[str, TokenUsage]:
    prompt_messages = []
    for message in messages:
        if message["role"] == "system":
            prompt_messages.append(langchain_core.messages.SystemMessage(message["content"]))
        elif message["role"] == "user":
            prompt_messages.append(langchain_core.messages.HumanMessage(message["content"]))
        elif message["role"] == "assistant":
            prompt_messages.append(langchain_core.messages.AIMessage(message["content"]))
    prompt = langchain.prompts.ChatPromptTemplate.from_messages(prompt_messages)
    llm = ModelManager().get_llm_model("activity_config_model", ConsumingFeatureType.ACTIVITY_CONFIGURATION)
    chain = prompt | llm
    with langchain_community.callbacks.get_openai_callback() as cb:
        response = await chain.ainvoke({})
        assert isinstance(response.content, str), f"Unexpected response content type: {response.content}"
        completion = response.content
        usage = TokenUsage(
            model=llm.deployment_name,  # type: ignore
            prompt_tokens=cb.prompt_tokens,
            completion_tokens=cb.completion_tokens,
            total_tokens=cb.total_tokens,
        )
    return completion, usage
