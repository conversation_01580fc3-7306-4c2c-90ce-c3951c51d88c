from collections import defaultdict

import numpy as np
import tqdm
from sklearn import feature_extraction, metrics

from services.studio._text_to_workflow.activity_config import activity_config_config, activity_config_dataset, activity_config_messages
from services.studio._text_to_workflow.activity_config.activity_config_schema import (
    ActivityConfigConfig,
    ActivityConfigDemoRetrieverState,
    ActivityConfigExample,
)
from services.studio._text_to_workflow.common import typedefs
from services.studio._text_to_workflow.common.schema import Variable
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

_config: ActivityConfigConfig = activity_config_config.get()
_retriever_store: StateStore | None = None


async def init_and_load(include_production_demos: bool = False) -> None:
    global _retriever_store
    if not typedefs.exists():
        await typedefs.build()
    typedefs.load()
    _retriver_builder = ActivityConfigDemoRetrieverBuilder(include_production_demos)
    _retriever_store = StateStore((paths.get_activity_config_retriever_path() / "state.pkl").as_posix(), _retriver_builder, lazy_load=settings.DEBUG_MODE)


class ActivityConfigDemoRetrieverBuilder(StateBuilder):
    def __init__(self, include_production: bool = False):
        self.include_production = include_production

    def build(self) -> tuple[ActivityConfigDemoRetrieverState, dict]:
        assert _config is not None
        subsets = list(activity_config_dataset.train_subsets.items()) + list(activity_config_dataset.test_subsets.items())
        if self.include_production:
            subsets.extend(list(activity_config_dataset.prod_subsets.items()))
        filepath2index, index2filepath, exampleid2index, index2exampleid, index2configured_properties_count, lookup = {}, {}, {}, {}, {}, defaultdict(list)
        available_variables_corpus, excluded, included = [], 0, 0
        for target_framework, target_framework_subsets in subsets:
            for subset in target_framework_subsets:
                demonstration_paths = sorted(paths.get_activity_config_dataset_path("DemonstrationSet", target_framework, subset).glob("*/*.yaml"))
                desc = "{0}/{1}. Included: {2}. Excluded: {3}"
                pbar = tqdm.tqdm(total=len(demonstration_paths), desc=desc, dynamic_ncols=True, disable=not settings.DEBUG_MODE)

                for activity_config_demo_path in demonstration_paths:
                    activity_config_demo: ActivityConfigExample = yaml_load(activity_config_demo_path)
                    interaction = activity_config_messages.create_activity_config_interaction(activity_config_demo, None)
                    if _config["retrieval"]["exclude_empty_configuration"] and not interaction["configuration"]:
                        excluded += 1
                        pbar.update(1)
                        pbar.set_description(desc.format(target_framework, subset, included, excluded))
                        continue
                    included += 1
                    index = len(available_variables_corpus)
                    filepath2index[activity_config_demo_path] = index
                    index2filepath[index] = activity_config_demo_path
                    exampleid2index[activity_config_demo["id"]] = index
                    index2exampleid[index] = activity_config_demo["id"]
                    index2configured_properties_count[index] = len(activity_config_demo["output"]["configuration"])
                    lookup[activity_config_demo["activity_id"]].append(index)
                    demo_available_variables = get_variable_types(activity_config_demo["input"]["available_variables"])
                    available_variables_corpus.append(demo_available_variables)

                    pbar.update(1)
                    pbar.set_description(desc.format(target_framework, subset, included, excluded))
        tfidf_vectorizer = feature_extraction.text.TfidfVectorizer()
        tfidf_matrix = tfidf_vectorizer.fit_transform(available_variables_corpus).toarray()  # type: ignore

        lookup = {k: np.array(v, dtype=np.int64) for k, v in lookup.items()}
        state = ActivityConfigDemoRetrieverState(
            tfidf_vectorizer=tfidf_vectorizer,
            tfidf_matrix=tfidf_matrix,
            tfidf_corpus=available_variables_corpus,
            lookup=lookup,
            filepath2index=filepath2index,
            index2filepath=index2filepath,
            exampleid2index=exampleid2index,
            index2exampleid=index2exampleid,
            index2configured_properties_count=index2configured_properties_count,
        )
        state_info = {
            "filepath2index": {k.as_posix(): v for k, v in state["filepath2index"].items()},
            "index2filepath": {k: v.as_posix() for k, v in state["index2filepath"].items()},
            "exampleid2index": {k: v for k, v in state["exampleid2index"].items()},
            "index2exampleid": {v: k for k, v in state["index2exampleid"].items()},
        }
        return state, state_info


def get_demo_by_id(demo_id: int) -> ActivityConfigExample:
    assert _retriever_store.state is not None  # type: ignore
    demonstration_path = _retriever_store.state["index2filepath"][demo_id]  # type: ignore
    return yaml_load(demonstration_path)


def get_top_k_demos_by_most_configured_properties(activity_id: str, k: int) -> list[ActivityConfigExample]:
    """We want to retrieve the top k demos by the number of configured properties.
    This is a simple yet effective way to sort the demos by how comprehensive they are (useful when we cannot build a variable embedding).
    """
    assert _retriever_store.state is not None  # type: ignore
    index2configured_properties_count = _retriever_store.state["index2configured_properties_count"]  # type: ignore

    same_activity_id_indices = get_lookup_indices(activity_id)

    # Sort indices by number of configured properties in descending order
    sorted_indices = sorted(same_activity_id_indices, key=lambda idx: index2configured_properties_count[idx], reverse=True)
    return [get_demo_by_id(i) for i in sorted_indices[:k]]


def get_top_k_demos(activity_id: str, variables: list[Variable], k: int, ignore_exampleids: list[str]) -> tuple[list[ActivityConfigExample], list[float]]:
    assert _retriever_store.state is not None  # type: ignore

    exampleid2index, vectorizer, matrix = (
        _retriever_store.state["exampleid2index"],  # type: ignore
        _retriever_store.state["tfidf_vectorizer"],  # type: ignore
        _retriever_store.state["tfidf_matrix"],  # type: ignore
    )
    top_k_demos, top_k_scores = [], []
    # filter demos by activity_id and skip ignored examples
    ignored_indices = np.array([exampleid2index[exampleid] for exampleid in ignore_exampleids if exampleid in exampleid2index], dtype=np.int64)
    same_activity_id_indices = get_lookup_indices(activity_id)
    allowed_same_activity_indices = np.setdiff1d(same_activity_id_indices, ignored_indices, assume_unique=True)
    allowed_matrix = matrix[allowed_same_activity_indices, :]
    # calculate activity vector
    activity_available_variables = get_variable_types(variables)
    activity_vector = vectorizer.transform([activity_available_variables]).toarray()  # type: ignore
    # get top k demos
    if allowed_matrix.shape[0] == 0 or k == 0:
        return [], []
    allowed_candidates_scores = metrics.pairwise.cosine_similarity(activity_vector, allowed_matrix).flatten()
    allowed_sorted_candidate_indices = np.argsort(allowed_candidates_scores)
    allowed_top_k_indices = allowed_sorted_candidate_indices[-k:].tolist()
    top_k_scores = allowed_candidates_scores[allowed_top_k_indices].tolist()
    top_k_indices = allowed_same_activity_indices[allowed_top_k_indices].tolist()
    top_k_demos = [get_demo_by_id(i) for i in top_k_indices]
    return top_k_demos, top_k_scores


def get_filtered_top_k(scores: np.ndarray, indices: np.ndarray, k: int):
    scores = scores[indices]
    sorted_indices = np.argsort(scores)
    sorted_scores = scores[sorted_indices]
    top_k_indices = sorted_indices[-k:].tolist()
    top_k_scores = sorted_scores[-k:].tolist()
    top_k_ids = [indices[i] for i in top_k_indices]
    top_k_demos = [get_demo_by_id(i.item()) for i in top_k_ids]
    return top_k_demos, top_k_scores


def get_lookup_indices(key: str) -> np.ndarray:
    assert _retriever_store.state is not None  # type: ignore
    return _retriever_store.state["lookup"].get(key, np.array([], dtype=np.int64))  # type: ignore


def get_variable_types(variables: list[Variable]) -> str:
    return " ".join(available_variable["type"] for available_variable in variables)
