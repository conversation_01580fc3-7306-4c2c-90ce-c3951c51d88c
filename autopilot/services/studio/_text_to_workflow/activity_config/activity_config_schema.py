from pathlib import Path

import numpy as np
import typing_extensions as t
from sklearn import feature_extraction

from services.studio._text_to_workflow.common.schema import (
    ActivityDict,
    Message,
    ParamValueCategory,
    SubsetName,
    TargetFramework,
    TypeDef,
    Variable,
    WorkflowDict,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse

DatasetType: t.TypeAlias = t.Literal["DemonstrationSet", "EvaluationSet"]


class ActivityConfigInput(t.TypedDict):
    user_query: str
    target_framework: TargetFramework
    workflow: WorkflowDict
    available_variables: list[Variable]
    activity_typedef: str
    additional_typedefs: str


class ActivityConfigResult(t.TypedDict):
    explanation: str
    configuration: dict


class ActivityConfigExample(t.TypedDict):
    id: str
    demo_id: str
    workflow_id: str
    activity_id: str
    activity_fqn: str
    input: ActivityConfigInput
    output: ActivityConfigResult


class ActivityConfigContext(t.TypedDict):
    activity_id: str
    target_framework: TargetFramework
    user_query: str
    workflow: WorkflowDict
    activity: ActivityDict
    activity_types: ActivityDict
    activity_typedef: TypeDef
    additional_typedefs: dict[str, TypeDef]
    original_param_values: dict[str, dict[str, str]]


class ActivityConfigInteraction(t.TypedDict):
    context: ActivityConfigContext
    configuration: ActivityConfigResult


class ActivityConfigEvaluationExample(t.TypedDict):
    id: str
    activity_id: str
    target_configuration: str
    pred_configuration: str
    completion: str
    inputs_precision: float | None
    outputs_precision: float | None
    inputs_recall: float | None
    outputs_recall: float | None
    inputs_support: int
    outputs_support: int
    duration: float
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    context: ActivityConfigContext
    framework: TargetFramework
    subset: SubsetName


class ActivityConfigEvaluationExampleParamItem(t.TypedDict):
    id: str
    activity_eval_item_id: str
    param_target_value: str
    param_pred_value: str
    pred_correct: bool
    param_name: str
    param_type: str
    param_type_category: str
    param_target_value_category: ParamValueCategory
    param_pred_value_category: ParamValueCategory
    duration: float
    activity_id: str
    framework: TargetFramework
    subset: SubsetName


class ActivityConfigEvaluationScore(t.TypedDict):
    precision: float | None
    recall: float | None
    support: int


class Statistics(t.TypedDict):
    mean: float
    std: float
    median: float


class ActivityConfigEvaluationScores(t.TypedDict):
    overall: dict[str, ActivityConfigEvaluationScore]
    inputs: dict[str, ActivityConfigEvaluationScore]
    outputs: dict[str, ActivityConfigEvaluationScore]


class EmbeddingModelConfig(t.TypedDict):
    model_type: str
    deployment_name: str


class RetrievalConfig(t.TypedDict):
    enable: bool
    embedding_model: EmbeddingModelConfig
    top_k: int
    clean_configuration: bool
    exclude_empty_configuration: bool


class PromptConfig(t.TypedDict):
    system_template: str
    user_template: str
    assistant_template: str
    user_instructions: str


class ModelConfig(t.TypedDict):
    type: t.Literal["openai", "azure", "local"]
    name: str
    deployment_name: str
    config: dict[str, t.Any]


class WhitelistConfig(t.TypedDict):
    min_num_examples: float
    min_num_inputs: float
    min_precision: float
    min_recall: float


class ActivityConfigConfig(t.TypedDict):
    retrieval: RetrievalConfig
    prompt: PromptConfig
    model: ModelConfig
    whitelist: WhitelistConfig


class ActivityConfigRunResult(t.TypedDict):
    lmyaml: str
    result: ActivityConfigResult
    completion: str
    messages: list[Message]
    context: ActivityConfigContext
    usage: TokenUsageJson


class ActivityConfigRequest(BaseRequest):
    workflow: str
    targetFramework: TargetFramework
    activityTypeDefinition: str
    additionalTypeDefinitions: str
    availableVariables: t.NotRequired[list[Variable]]


class ActivityConfigWithQueryRequest(BaseRequest):
    userRequest: str
    workflow: str
    targetFramework: TargetFramework
    activityTypeDefinition: str
    additionalTypeDefinitions: str
    availableVariables: t.NotRequired[list[Variable]]


class ActivityConfigResponse(BaseResponse):
    result: str
    explanation: str
    newVariables: list[Variable]
    usage: TokenUsageJson


class ActivityConfigDemoRetrieverState(t.TypedDict):
    tfidf_vectorizer: feature_extraction.text.TfidfVectorizer
    tfidf_matrix: np.ndarray
    tfidf_corpus: list[str]
    filepath2index: dict[Path, int]
    index2filepath: dict[int, Path]
    exampleid2index: dict[str, int]
    index2exampleid: dict[int, str]
    lookup: dict[str, np.ndarray]
    index2configured_properties_count: dict[
        int, int
    ]  # keeps track of the number of configured properties for each demo. useful for determining the most comprehensive demos when no meaningful context data is available.
