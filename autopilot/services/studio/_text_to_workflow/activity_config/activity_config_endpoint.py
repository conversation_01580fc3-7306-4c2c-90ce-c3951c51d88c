from services.studio._text_to_workflow.activity_config import activity_config_demo_retriever, activity_config_task
from services.studio._text_to_workflow.activity_config.activity_config_schema import (
    ActivityConfigContext,
    ActivityConfigInput,
    ActivityConfigResponse,
    ActivityConfigResult,
    ActivityConfigWithQueryRequest,
)
from services.studio._text_to_workflow.common import activity_retriever
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load


@telemetry_utils.log_execution_time("activity_config_endpoint.init")
async def init() -> None:
    activity_retriever.ActivitiesRetriever()
    await activity_config_demo_retriever.init_and_load()


@telemetry_utils.log_execution_time("activity_config_endpoint.generate")
async def generate(request: ActivityConfigWithQueryRequest) -> ActivityConfigResponse | None:
    activity_config_input = create_input_from_request(request)
    run_result = await activity_config_task.run(activity_config_input)
    if run_result is None:
        return None
    lmyaml, result, completion = run_result["lmyaml"], run_result["result"], run_result["completion"]
    usage = run_result["usage"]

    if not settings.IS_PROD:
        log_extra_info(lmyaml, result, completion, run_result["context"])

    return {"result": lmyaml, "explanation": result["explanation"], "newVariables": [], "usage": usage}


def create_input_from_request(request: ActivityConfigWithQueryRequest) -> ActivityConfigInput:
    workflow = yaml_load(request["workflow"])
    # Reverse the list such that the variable closest to current activity is last.
    # TODO: Remove this once the frontend is updated to send the available variables in the correct order.
    available_variables = request.get("availableVariables", [])[::-1]
    if not available_variables:
        available_variables = workflow.get("variables", [])
    return {
        "target_framework": request["targetFramework"],
        "user_query": request["userRequest"],
        "workflow": workflow,
        "available_variables": available_variables,
        "activity_typedef": request["activityTypeDefinition"],
        "additional_typedefs": request["additionalTypeDefinitions"],
    }


def log_extra_info(lmyaml: str, result: ActivityConfigResult, completion: str, context: ActivityConfigContext) -> None:
    print("--- Type Definition")
    print(context["activity_typedef"]["text"])
    print("\n\n".join(t["text"].replace("\r\n", "\n") for t in context["additional_typedefs"].values()))
    print("--- Workflow")
    print(yaml_dump(context["workflow"]))
    print("--- Completion")
    print(completion)
    print("--- Explanation")
    print(result["explanation"])
    print("--- Configuration")
    print(yaml_dump(result["configuration"]))
    print("--- LM YAML")
    print(lmyaml)
