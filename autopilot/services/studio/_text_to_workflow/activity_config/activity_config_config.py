import pathlib

import typing_extensions as t

from services.studio._text_to_workflow.activity_config import activity_config_schema as acs
from services.studio._text_to_workflow.utils import yaml_utils as yu

_configs: dict[str, acs.ActivityConfigConfig] = {}


def get_config_path(config_name: str = "prompt.yaml") -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / config_name


def get(config_name: str = "prompt.yaml") -> acs.ActivityConfigConfig:
    if config_name not in _configs:
        _configs[config_name] = yu.yaml_load(get_config_path(config_name))
    return _configs[config_name]


def update(
    config_name: str = "prompt.yaml",
    model_type: t.Literal["openai", "azure", "local"] | None = None,
    model_name: str | None = None,
    model_deployment_name: str | None = None,
) -> None:
    _config = get(config_name)
    if model_type is not None:
        _config["model"]["type"] = model_type
    if model_name is not None:
        _config["model"]["name"] = model_name
    if model_deployment_name is not None:
        _config["model"]["deployment_name"] = model_deployment_name
