wandb_project: str = "<EMAIL>"
wandb_entity: str = "uipath"
wandb_evaluation_job: str = "evaluation"
wandb_finetuning_train_job: str = "finetuning-train"
wandb_finetuning_selection_job: str = "finetuning-select"
excluded_activities: set[str] = {
    # Skip container activities
    "System.Activities.Statements.Flowchart",
    "System.Activities.Statements.Sequence",
    "UiPath.Excel.Activities.Business.SequenceX",
    # Skip  the old assign activity. We use Uipath.Core.Activities.Assign.
    "System.Activities.Statements.Assign",
    # <PERSON><PERSON> comment out, since there's nothing to configure
    "UiPath.Core.Activities.CommentOut",
    # Skip exception handling activities for now. TODO: We should add support for these.
    "System.Activities.Statements.TryCatch",
    "System.Activities.Statements.Throw",
    "System.Activities.Statements.Rethrow",
    # Skip retry scope for now. TODO: We should add support for this.
    "UiPath.Core.Activities.RetryScope",
}
excluded_packages = {
    "UiPath.UIAutomation.Activities",
    "UiPath.IntegrationService.Activities",
}
