expression_generation:
  csharp:
    - user_message: |-
          Available types: None
          Available Variables: myStr as System.String with Id: id1 in Activity: 'activity1'
          Output type: String
          User description: Verify that myStr contains at least 3 lowercase letters.
      ai_message: |-
            explanation: 'Checks if myStr contains at least 3 lowercase letters, then casts the result to the Output type, in this case String.'
            expression: '(vars.id1.Count(c => char.<PERSON>(c)) >= 3).ToString()'
            sampleInput: '{"vars.id1": "Hello123World"}'
    - user_message: |-
            Available types: None
            Available Variables: myList as System.Collections.Generic.List<int> with id: vy6152 in Activity: 'activity1233'
            Output type: Boolean
            User description: Verify that myList contains at least an even number.
      ai_message: |-
            explanation: 'Checks if myList contains at least one even number. The result is already in Boolean format, so no need to cast it.'
            expression: 'vars.vy6152.Any(i => i % 2 == 0)'
            sampleInput: '{"vars.vy6152": [1, 3, 5, 6, 7]}'
    - user_message: |-
            Available types: None
            Available Variables: var1 as System.Double with Id: vtyat1 in Activity: 'send to task', var2 as System.Double  with Id: vtyfft1 in Activity: 'send to task2'
            Output type: Integer
            User description: Get the average between var1 and var2.
      ai_message: |-
            explanation: 'Calculates the average of var1 and var2. Because the output type is Integer, we cast the result to an integer.'
            expression: '(int)Math.Round((vars.vtyat1 + vars.vtyfft1) / 2)'
            sampleInput: '{"vars.vtyat1": 10.5, "vars.vtyfft1": 15.7}'

    - user_message: |-
        Available types: None
        Available Variables: multiplier as System.Int32 with Id: mult1 in Activity: 'Calculate_multiplier', value as System.Int32 with Id: val2 in Activity: 'Calculate_value'
        Output type: Integer
        User description: Multiply the variable specified in the activity name 'Calculate_multiplier' by 10.
      ai_message: |-
        explanation: 'Identifies the variable "multiplier" from its activity "Calculate_multiplier" and multiplies its value by 10.'
        expression: 'vars.mult1 * 10'
        sampleInput: '{"vars.mult1": 5}'

    - user_message: |-
        Available types: None
        Available Variables: discount as System.Double with Id: disc1 in Activity: 'Discount_Calculation', price as System.Double with Id: price2 in Activity: 'Price_Calculation'
        Output type: Double
        User description: Subtract the discount percentage (from the variable in activity 'Discount_Calculation') from the price.
      ai_message: |-
        explanation: 'Uses the discount value from Discount_Calculation to compute the discount amount and subtract it from the price from Price_Calculation.'
        expression: 'vars.price2 - (vars.price2 * vars.disc1 / 100)'
        sampleInput: '{"vars.disc1": 15.0, "vars.price2": 100.0}'

    - user_message: |-
        Available types: None
        Available Variables: firstName as System.String with Id: fn1 in Activity: 'User_Info', lastName as System.String with Id: ln1 in Activity: 'User_Info'
        Output type: String
        User description: Combine the variables from activity 'User_Info' to form a full name in the format "lastName, firstName".
      ai_message: |-
        explanation: 'Selects firstName and lastName from the User_Info activity and concatenates them as "lastName, firstName".'
        expression: 'vars.ln1 + ", " + vars.fn1'
        sampleInput: '{"vars.fn1": "John", "vars.ln1": "Smith"}'

    - user_message: |-
        Available types: None
        Available Variables: numbers as System.Collections.Generic.List<int> with Id: numList1 in Activity: 'Statistics', threshold as System.Int32 with Id: thresh1 in Activity: 'Threshold'
        Output type: System.Collections.Generic.List<int>
        User description: From the list in activity 'Statistics', return all numbers greater than the threshold from activity 'Threshold'.
      ai_message: |-
        explanation: 'Filters the list from Statistics to include only numbers greater than the threshold from Threshold.'
        expression: 'vars.numList1.Where(n => n > vars.thresh1).ToList()'
        sampleInput: '{"vars.numList1": [1, 5, 10, 15, 20], "vars.thresh1": 7}'

    - user_message: |-
        Available types: None
        Available Variables: baseNum as System.Int32 with Id: base1 in Activity: 'Exponentiation_Base', exponent as System.Int32 with Id: exp1 in Activity: 'Exponentiation'
        Output type: Integer
        User description: Raise the variable from activity 'Exponentiation_Base' to the power specified in activity 'Exponentiation'.
      ai_message: |-
        explanation: 'Raises the base number from the Exponentiation_Base activity to the power specified in the Exponentiation activity.'
        expression: '(int)Math.Pow(vars.base1, vars.exp1)'
        sampleInput: '{"vars.base1": 2, "vars.exp1": 3}'

    - user_message: |-
        Available types: None
        Available Variables: str1 as System.String with Id: s1 in Activity: 'TextValidation', str2 as System.String with Id: s2 in Activity: 'TextValidation'
        Output type: String
        User description: Verify that str1 appears in str2.
      ai_message: |-
        explanation: 'Checks whether str1 is a substring of str2. Since the output type is String, the Boolean result is converted to a string.'
        expression: 'vars.s2.Contains(vars.s1).ToString()'
        sampleInput: '{"vars.s1": "world", "vars.s2": "Hello world!"}'

    - user_message: |-
        Available types: Employee { int Id; string FirstName; string LastName; DateTime? StartDate; string Position; }
        Available Variables: newEmployee as Employee with Id: emp1 in Activity: 'EmployeeProcessing'
        Output type: string
        User description: Concatenate the first name and the last name of the employee.
      ai_message: |-
        explanation: 'Concatenates the FirstName and the LastName properties of the employee variable, with a space between them.'
        expression: 'string.Concat(vars.emp1.FirstName, " ", vars.emp1.LastName)'
        sampleInput: '{"vars.emp1": {"FirstName": "John", "LastName": "Doe"}}'

    - user_message: |-
        Available types: None
        Available Variables: myEmail as System.String with Id: email1 in Activity: 'EmailAnalysis'
        Output type: int
        User description: Count the number of times the @ character appears in myEmail.
      ai_message: |-
        explanation: 'Counts the number of times the @ character appears in the myEmail variable.'
        expression: 'vars.email1.Count(c => c == ''@'')'
        sampleInput: '{"vars.email1": "<EMAIL>"}'

    - user_message: |-
        Available types: None
        Available Variables: counter as System.Int32 with Id: count1 in Activity: 'CounterOperation'
        Output type: string
        User description: Compute a number two times bigger than counter.
      ai_message: |-
        explanation: 'Multiplies the counter variable by 2 and converts the result to a string.'
        expression: '(vars.count1 * 2).ToString()'
        sampleInput: '{"vars.count1": 5}'
        
    - user_message: |-
        Available types: WorkEntities { decimal? Price; string Identifier; }
        Available Variables: workRecords as System.Collections.Generic.IList<WorkEntities> with Id: recList1 in Activity: 'WorkRecordAnalysis'
        Output type: String
        User description: Retrieve the Identifier of the record with the highest Price.
      ai_message: |-
        explanation: 'Returns the Identifier property of the record with the highest Price from the workRecords list.'
        expression: 'vars.recList1.OrderByDescending(f => f.Price).FirstOrDefault()?.Identifier'
        sampleInput: '{"vars.recList1": [{"Price": 100.0, "Identifier": "A1"}, {"Price": 150.0, "Identifier": "B2"}]}'

    - user_message: |-
        Available types: None
        Available Variables: None
        Output type: Boolean
        User description: Check if tomorrow's date is the 12th of April.
      ai_message: |-
        explanation: 'Checks if tomorrow''s date is the 12th of April.'
        expression: 'DateTime.Now.AddDays(1).Date == new DateTime(DateTime.Now.Year, 4, 12)'

    - user_message: |-
        Available types: None
        Available Variables: currentEmail as Office365Message with Id: emailObj1 in Activity: 'EmailProcessing', EmailList as System.Collections.Generic.List<Office365Message> with Id: emailList1 in Activity: 'EmailProcessing'
        Output type: DataTable
        User description: Create a DataTable and add a row with the Subject property of currentEmail.
      ai_message: |-
        explanation: 'Creates a DataTable and adds a row with the Subject property of the currentEmail variable.'
        expression: 'new DataTable().Rows.Add(vars.emailObj1.Subject).Table'
        sampleInput: '{"vars.emailObj1": {"Subject": "Meeting Reminder"}}'

    - user_message: |-
        Available types: None
        Available Variables: counter as System.Int32 with Id: cnt1 in Activity: 'ActivityA', counter as System.Int32 with Id: cnt2 in Activity: 'ActivityB'
        Output type: Integer
        User description: Multiply the counter in ActivityA by 3 and add the counter in ActivityB.
      ai_message: |-
        explanation: 'Multiplies the counter from ActivityA (vars.cnt1) by 3 and then adds the counter from ActivityB (vars.cnt2) to produce the final result.'
        expression: '(vars.cnt1 * 3) + vars.cnt2'
        sampleInput: '{"vars.cnt1": 5, "vars.cnt2": 10}'

    - user_message: |-
        Available types: None
        Available Variables: counter as System.Int32 with Id: 1234c in Activity: 'ActivityA', counter as System.Int32 with Id: uus1 in Activity: 'ActivityB'
        Output type: Integer
        User description: Multiply the counter in ActivityA by 3 and add the counter in ActivityB.
      ai_message: |-
        explanation: 'Multiplies the counter from ActivityA (vars.1234c) by 3 and then adds the counter from ActivityB (vars.uus1) to produce the final result.'
        expression: '(vars.1234c * 3) + vars.uus1'
        sampleInput: '{"vars.1234c": 5, "vars.uus1": 10}'

    - user_message: |-
        Available types: custom1 {\"$schema\":\"http://json-schema.org/draft-07/schema#\",\"type\":\"object\",\"properties\":{\"message\":{\"title\":\"Message\",\"$ref\":\"#/definitions/message\"},\"saveToSentItems\":{\"title\":\"Save to sent items\",\"type\":\"boolean\",\"description\":\"Indicates if the email should be saved in the sent items folder.\"},\"internetMessageHeaders[*]\":{\"title\":\"Internet message headers\",\"type\":\"array\",\"items\":{\"$ref\":\"#/definitions/internetMessageHeaders[*]\"}},\"status\":{\"title\":\"Status\",\"type\":\"string\"}},\"definitions\":{\"message\":{\"type\":\"object\",\"properties\":{\"importance\":{\"title\":\"Importance\",\"type\":\"string\",\"description\":\"Indicates the priority level of the email message.\"},\"inferenceClassification\":{\"title\":\"Email classification\",\"type\":\"string\",\"description\":\"The classification of the email based on inferred importance.\"},\"toRecipients[*]\":{\"title\":\"To recipients\",\"type\":\"array\",\"items\":{\"$ref\":\"#/definitions/messageToRecipients[*]\"}},\"ccRecipients[*]\":{\"title\":\"Cc recipients\",\"type\":\"array\",\"items\":{\"$ref\":\"#/definitions/messageCcRecipients[*]\"}}}},\"internetMessageHeaders[*]\":{\"type\":\"object\",\"properties\":{\"value\":{\"title\":\"Internet message headers value\",\"type\":\"string\"},\"name\":{\"title\":\"Internet message headers name\",\"type\":\"string\"}}},\"messageToRecipients[*]\":{\"type\":\"object\",\"properties\":{\"emailAddress\":{\"title\":\"Email address\",\"$ref\":\"#/definitions/messageToRecipients[*]EmailAddress\"}}},\"messageToRecipients[*]EmailAddress\":{\"type\":\"object\",\"properties\":{\"address\":{\"title\":\"Message to recipients email address\",\"type\":\"string\"}}},\"messageCcRecipients[*]\":{\"type\":\"object\",\"properties\":{\"emailAddress\":{\"title\":\"Email address\",\"$ref\":\"#/definitions/messageCcRecipients[*]EmailAddress\"}}},\"messageCcRecipients[*]EmailAddress\":{\"type\":\"object\",\"properties\":{\"address\":{\"title\":\"Message cc recipients email address\",\"type\":\"string\"}}}}}
        Available Variables: email as String with Id: id145a in Activity: 'act1', response as custom1 with Id: id45b in Activity: 'ActivityB', orderIndex as Integer with Id: cia345c in Activity: 'act112'
        Output type: Integer
        User description: append email to response's importance, and subject the result to upper case, then append orderIndex + 1 as string, append 'hello word' in the end
      ai_message: |-
        explanation: 'Appends the email to the importance property of the response, converts the result to upper case, appends orderIndex + 1 as a string, and finally adds \"hello word\" at the end.'
        expression: '(vars.id45b.message.importance + vars.id145a).ToUpper() + (vars.cia345c + 1).ToString() + \"hello word\"'
        sampleInput: '{"vars.id145a": "<EMAIL>", "vars.id45b": {"message": {"importance": "High"}}, "vars.cia345c": 1}'

    - user_message: |-
        Available types: Customer {int CustomerId; string FullName; Address Address; }, Address { string Street; string City; string Country; }
        Available Variables: customer as Customer with Id: cust1 in Activity: 'CustomerProcessing', customer as Customer with Id: c2us in Activity: 'AnotherCutsomerProcessing'
        Output type: String
        User description: Convert the Country field for variable 'customer' in AnotherCutsomerProcessing Address to upper-case.
      ai_message: |-
        explanation: 'Extracts the Country field from the Address property of the customer in activity "AnotherCutsomerProcessing" and converts it to upper-case.'
        expression: 'vars.cust1.Address.Country.ToUpper()'
        sampleInput: '{"vars.cust1": {"Address": {"Country": "United States"}}}'

    - user_message: |-
        Available types: Order { int OrderId; decimal Total; Payment PaymentDetails; }, Payment { string Method; DateTime Date; }
        Available Variables: order as Order with Id: order1 in Activity: 'OrderProcessing'
        Output type: Decimal
        User description: If the Payment Method is "Credit Card", increase the Total by 15; otherwise, leave the Total unchanged.
      ai_message: |-
        explanation: 'Checks the Payment Method in the PaymentDetails of the order. If it is "Credit Card", adds 15 to the Total; otherwise, returns the original Total.'
        expression: 'vars.order1.PaymentDetails.Method == "Credit Card" ? vars.order1.Total + 15 : vars.order1.Total'
        sampleInput: '{"vars.order1": {"Total": 100.00, "PaymentDetails": {"Method": "Credit Card"}}}'

    - user_message: |-
        Available types: Product { int ProductId; string Name; PriceDetail PriceDetail; }, PriceDetail { decimal ListPrice; decimal DiscountPercentage; }
        Available Variables: product as Product with Id: p1 in Activity: 'OrderProcessing'
        Output type: Decimal
        User description: output the product final price after discount
      ai_message: |-
        explanation: 'Calculates the final price of the product by applying the discount percentage to the list price'
        expression: 'vars.p1.PriceDetail.ListPrice * (1 - vars.p1.PriceDetail.DiscountPercentage / 100)'
        sampleInput: '{"vars.p1": {"PriceDetail": {"ListPrice": 100.00, "DiscountPercentage": 20.0}}}'

    - user_message: |-
        Available types: Product { int ProductId; string Name; PriceDetail PriceDetail; }, PriceDetail { decimal ListPrice; decimal DiscountPercentage; }
        Available Variables: product as Product with Id: p1 in Activity: 'OrderProcessing', product as Product with Id: p2 in Activity: 'OrderProcessing'
        Output type: Decimal
        User description: output all products final price after discount
      ai_message: |-
        explanation: 'Calculates the final price for both products after applying the discount percentage to the list price.'
        expression: '(vars.p1.PriceDetail.ListPrice * (1 - vars.p1.PriceDetail.DiscountPercentage / 100)) + (vars.p2.PriceDetail.ListPrice * (1 - vars.p2.PriceDetail.DiscountPercentage / 100))'
        sampleInput: '{"vars.p1": {"PriceDetail": {"ListPrice": 100.00, "DiscountPercentage": 20.0}}, "vars.p2": {"PriceDetail": {"ListPrice": 200.00, "DiscountPercentage": 15.0}}}'
