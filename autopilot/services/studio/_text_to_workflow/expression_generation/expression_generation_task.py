import json
import pathlib
import re
from typing import Any, Generic, Type, TypeVar, cast

import jq
import jsbeautifier
import langchain.chains
import langchain.chat_models
import langchain.embeddings
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
import langchain_core
import langchain_core.language_models
import langchain_core.prompts
import openai
from langchain.prompts.chat import BaseMessagePromptTemplate
from overrides import override

from services.studio._text_to_workflow.common.api_workflow.js_expressions_helper import JavaScriptExpressionsHelper
from services.studio._text_to_workflow.expression_generation.expression_generation_helper import ExpressionGenerationHelper
from services.studio._text_to_workflow.expression_generation.expression_generation_jq_helper import JqExpressionsHelper
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    BaseExpressionRequest,
    ExpressionGenerationBPMNModelResponse,
    ExpressionGenerationFixRequest,
    ExpressionGenerationModelResponse,
    ExpressionGenerationRequest,
    ExpressionGenerationTaskResult,
    ExpressionLanguageType,
)
from services.studio._text_to_workflow.expression_generation.expression_generation_typedefs_filter import ExpressionGenerationTypedefsFilter
from services.studio._text_to_workflow.expression_generation.variables_parser import parse_variables, parse_variables_api_workflows, parse_variables_bpmn
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.errors import UnprocessableEntityError
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.paths import get_field_escaping_prompt_path, get_meta_variables_prompt_path
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()

TResponse = TypeVar("TResponse", bound=ExpressionGenerationModelResponse)


class ExpressionGenerationTask(Generic[TResponse]):
    # Add a class variable to store the response type
    model_response_type: Type[ExpressionGenerationModelResponse] = ExpressionGenerationModelResponse

    def __init__(self, config_name: str, demos_name: str) -> None:
        """Initialize the expression generation task.

        This class handles expression generation and fix expression tasks.
        It supports both standard and large models for different complexity levels
        of expression generation.

        Args:
            config_name: Filename for the standard expression generation model configuration
            demos_name: Filename containing demonstrations for the expression generation model

        Note:
            The class uses model configurations from model_config.yaml:
            - "expression_generation_model" for standard requests
            - "expression_generation_large_model" for complex requests
        """
        self.config = yaml_load((pathlib.Path(__file__).parent / config_name).absolute())
        self.typedefs_filter = ExpressionGenerationTypedefsFilter(ModelManager().get_embeddings_model())
        self.demos = yaml_load((pathlib.Path(__file__).parent / demos_name).absolute())
        self.model_manager_name = "expression_generation_model"
        self.model_manager_large_name = "expression_generation_large_model"

    def _filter_typedefs(
        self, model: langchain_core.language_models.BaseChatModel, target_request, typedefs_to_evaluate, typedefs_available_tokens, expression_language: str
    ) -> str:
        typedefs_with_scores, parsed_namespaces = self.typedefs_filter.get_typedefs_scores(target_request, typedefs_to_evaluate, expression_language)

        for typedef_with_score in typedefs_with_scores:
            typedef_info = typedef_with_score["typedef_info"]
            typedef_info["no_tokens"] = model.get_num_tokens(parsed_namespaces[typedef_info["namespace_name"]][typedef_info["typdef_class_name"]]["text"])

        top_typedefs = []
        for scored_typedef in typedefs_with_scores:
            typedef_info = scored_typedef["typedef_info"]
            if typedefs_available_tokens - typedef_info["no_tokens"] > 0:
                top_typedefs.append(typedef_info)
                typedefs_available_tokens -= typedef_info["no_tokens"]
            else:
                break

        filtered_typedefs = self.typedefs_filter.rebuild_typedefinition_string(top_typedefs, parsed_namespaces)

        return filtered_typedefs

    def escape_template(self, template: str) -> str:
        return template.replace("{", "{{").replace("}", "}}")

    def get_demonstrations(self, purpose: str, expression_language: ExpressionLanguageType) -> list[BaseMessagePromptTemplate]:
        demonstration_messages = []
        demos = self.demos[purpose][expression_language]

        for demo in demos:
            demonstration_messages.append(langchain.prompts.HumanMessagePromptTemplate.from_template(self.escape_template(demo["user_message"])))
            demonstration_messages.append(
                langchain.prompts.AIMessagePromptTemplate.from_template(self.escape_template(json.dumps(yaml_load(demo["ai_message"]))))
            )

        return demonstration_messages

    def limit_type_defs(
        self,
        target_request: str,
        additional_type_definitions: str,
        model: langchain_core.language_models.BaseChatModel,
        expression_language: str,
        chat_prompt: langchain.prompts.ChatPromptTemplate,
        inputs: dict,
    ) -> None:
        prompt_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
        if prompt_number_of_tokens + model.max_model_tokens > model.max_total_tokens:
            inputs["available_types"] = ""
            no_typedefs_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
            typedefs_available_tokens = model.max_total_tokens - no_typedefs_number_of_tokens - 1024
            filtered_types = self._filter_typedefs(model, target_request, additional_type_definitions, typedefs_available_tokens, expression_language)
            inputs["available_types"] = str(filtered_types) if filtered_types is not None else ""

    async def format_output(
        self,
        result: langchain.schema.AIMessage,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> TResponse:
        """
        Formats an AI message into an ExpressionGenerationModelResponse object.
        Tries to regenerate the output if it is not a valid JSON object.

        Args:
            request: The request object containing the expression generation parameters
            result: The result of the expression generation task
            gen_messages: The messages generated by the model

        """
        if isinstance(result, langchain.schema.AIMessage):
            result.content = self.validate_json_response_string(result.content)  # type: ignore
            try:
                response = json.loads(result.content)
                response = ExpressionGenerationModelResponse(expression=response["expression"], explanation=response["explanation"])
                return response  # type: ignore
            except (json.JSONDecodeError, KeyError):
                response = await self.regenerate_response_for_invalid_json(result, gen_messages, inputs)
                return response

        return result

    async def enforce_generation_constraints(
        self,
        request: ExpressionGenerationRequest | ExpressionGenerationFixRequest,
        result: TResponse,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> TResponse:
        """
        Enforces functionallity constraints on the expression.

        Args:
            request: The request object containing the expression generation parameters
            result: The result of the expression generation task
        """
        should_regenerate = False
        current_expression = result.expression
        current_explanation = result.explanation
        gen_messages.append(
            langchain.prompts.AIMessagePromptTemplate.from_template(
                self.escape_template(json.dumps({"expression": current_expression, "explanation": current_explanation}))
            )
        )
        string_to_search = "var"
        if request["expressionLanguage"] == ExpressionLanguageType.VBNET.value:
            string_to_search = "Dim"
        secure_string_regen_message = "The expression should not declare a new variable using the '{}' keyword. Try to rewrite the expression in a new way so that it is a proper expression that doesn't contain variable declarations. If the user's request is not achievable, then write \"Cannot fulfill without a variable declaration\" in the explanation".format(
            string_to_search
        )
        if string_to_search in current_expression:
            gen_messages.append(
                langchain.prompts.AIMessagePromptTemplate.from_template(
                    self.escape_template(json.dumps({"expression": current_expression, "explanation": current_explanation}))
                )
            )
            gen_messages.append(langchain.schema.HumanMessage(content=secure_string_regen_message))  # type: ignore
            should_regenerate = True

        if should_regenerate:
            chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
            structured_large_model = ModelManager().get_llm_model(self.model_manager_large_name, ConsumingFeatureType.EXPRESSION_GENERATION)
            structured_large_model = structured_large_model.with_structured_output(self.model_response_type)
            chat_chain = chat_prompt | structured_large_model
            result, _ = await self.call_open_ai_with_structured_output(chat_chain, inputs)

        return result

    async def regenerate_response_for_invalid_json(
        self,
        result: langchain.schema.AIMessage,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> TResponse:
        """
        Regenerates the response for an invalid JSON object.

        Args:
            request: The request object containing the expression generation parameters
            result: The result of the expression generation task
        """
        gen_messages.append(langchain.prompts.AIMessagePromptTemplate.from_template(self.escape_template(str(result.content))))
        invalid_json_regen_message = 'The expression is not a valid JSON object with the required fields. You MUST regenerate it and return a valid JSON object with the required fields, "expression" and "explanation"'
        gen_messages.append(langchain.prompts.HumanMessagePromptTemplate.from_template(invalid_json_regen_message))

        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
        structured_large_model = ModelManager().get_llm_model(self.model_manager_large_name, ConsumingFeatureType.EXPRESSION_GENERATION)
        structured_large_model = structured_large_model.with_structured_output(self.model_response_type)
        chat_chain = chat_prompt | structured_large_model
        structured_result, _ = await self.call_open_ai_with_structured_output(chat_chain, inputs)
        return structured_result  # type: ignore

    def get_gen_messages(self, task: str, config: dict, request: ExpressionGenerationRequest) -> list[BaseMessagePromptTemplate]:
        system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(
            config[f"{task}_prompt"][request["expressionLanguage"]]["system_msg"]
        )
        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(
            config[f"{task}_prompt"][request["expressionLanguage"]]["user_msg_template"]
        )
        demonstration_messages = self.get_demonstrations(task, request["expressionLanguage"])

        # TO DO: revisit
        gen_messages = [cast(BaseMessagePromptTemplate, system_message_template)]
        gen_messages.extend(demonstration_messages)
        gen_messages.append(cast(BaseMessagePromptTemplate, user_message_template))

        return gen_messages

    def get_inputs_generate_expression(self, request: ExpressionGenerationRequest) -> dict:
        inputs = {
            "available_types": request["additionalTypeDefinitions"],
            "available_variables": self.preprocess_available_variables(request["availableVariables"]),
            "output_type": request["expressionTypeDefinition"],
            "expression_description": request["userRequest"],
        }
        return inputs

    def get_inputs_fix_expression(self, request: ExpressionGenerationFixRequest) -> dict:
        inputs = {
            "available_types": request["additionalTypeDefinitions"],
            "available_variables": self.preprocess_available_variables(request["availableVariables"]),
            "output_type": request["expressionTypeDefinition"],
            "current_expression": request["currentExpression"],
            "current_error": request["currentError"],
            "expression_language": request["expressionLanguage"],
            "vb_net_option_strict": (
                "Note that the VB.Net Option Strict is turned on." if request["expressionLanguage"] == ExpressionLanguageType.VBNET.value else ""
            ),
        }
        return inputs

    @log_execution_time("ExpressionGenerationGen")
    async def _run(self, request: ExpressionGenerationRequest) -> tuple[dict[str, Any], TokenUsage]:
        self.preprocess_request(request)

        if ExpressionGenerationHelper.check_is_complex_request(request):
            model = ModelManager().get_llm_model(self.model_manager_large_name, ConsumingFeatureType.EXPRESSION_GENERATION)
        else:
            model = ModelManager().get_llm_model(self.model_manager_name, ConsumingFeatureType.EXPRESSION_GENERATION)

        config = self.config

        inputs = self.get_inputs_generate_expression(request)

        if request["currentExpression"]:
            current_expression = request["currentExpression"]
            inputs["current_expression_description"] = f"\nCurrent expression: {current_expression}\n"
            inputs["update_expression_prompt"] = config["update_expression_prompt"]
        else:
            inputs["current_expression_description"] = ""
            inputs["update_expression_prompt"] = ""

        gen_messages = self.get_gen_messages("expression_generation", config, request)

        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)

        model_max_tokens = model.max_tokens if hasattr(model, "max_tokens") and model.max_tokens is not None else 0
        if model_max_tokens:
            self.limit_type_defs(request["currentExpression"], request["additionalTypeDefinitions"], model, request["expressionLanguage"], chat_prompt, inputs)
            self.check_expression_size(inputs.get("current_expression", ""), model)

        chat_chain = chat_prompt | model
        result, usage = await self.call_open_ai(chat_chain, inputs)

        result = await self.format_output(result, gen_messages, inputs)

        result = await self.enforce_generation_constraints(request, result, gen_messages, inputs)
        result = result.model_dump()

        return result, usage

    @log_execution_time("ExpressionGenerationGen")
    async def run(self, request: ExpressionGenerationRequest) -> dict[str, Any]:
        result, usage = await self._run(request)
        output_type = request.get("outputType", "yaml")
        output_dict = {
            "explanation": result["explanation"],
            "expression": result["expression"],
        }
        if "sampleInput" in result:
            output_dict["sampleInput"] = result["sampleInput"]

        if output_type == "yaml":
            result = yaml_dump(output_dict)
        elif output_type == "json":
            result = json.dumps(output_dict)

        return {"result": result, "usage": usage}

    async def call_open_ai_with_structured_output(self, chat_chain, inputs) -> tuple[TResponse, TokenUsage]:
        return await self._call_open_ai(chat_chain, inputs)

    async def call_open_ai(self, chat_chain, inputs) -> tuple[langchain.schema.AIMessage, TokenUsage]:
        return await self._call_open_ai(chat_chain, inputs)

    async def _call_open_ai(self, chat_chain, inputs) -> tuple[Any, TokenUsage]:
        with langchain_community.callbacks.get_openai_callback() as cb:
            try:
                result = await chat_chain.ainvoke(inputs)
            except openai.LengthFinishReasonError:
                raise UnprocessableEntityError("The generated answer is too large to process. Please try again with a more refined prompt.")
            usage = TokenUsage(
                model="ExpressionGenModel",
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        return result, usage

    def check_expression_size(self, current_expression: str, model):
        if model.get_num_tokens(current_expression) > model.max_tokens:
            raise UnprocessableEntityError("The current input is too large for the model to handle. Please try again with a smaller input.")

    async def _run_fix_expression(self, request: ExpressionGenerationFixRequest, is_retry_step: bool = False) -> tuple[dict[str, Any], TokenUsage]:
        """
        Processes a request to fix an expression with an error and generates a corrected expression.

        Args:
            request: The fix expression request containing the current expression, error, and context
            is_retry_step: Flag indicating if this was called in an inner loop validation step - if set to False, the result will not be validated

        Returns:
            A tuple containing the result (with explanation and fixed expression) and token usage
        """
        self.preprocess_request(request)

        if ExpressionGenerationHelper.check_is_complex_request(request):
            model = ModelManager().get_llm_model(self.model_manager_large_name, ConsumingFeatureType.FIX_EXPRESSION)
        else:
            model = ModelManager().get_llm_model(self.model_manager_name, ConsumingFeatureType.FIX_EXPRESSION)

        config = self.config

        gen_messages = self.get_gen_messages("fix_expression", config, request)

        inputs = self.get_inputs_fix_expression(request)

        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
        model_max_tokens = model.max_tokens if hasattr(model, "max_tokens") and model.max_tokens is not None else 0

        if model_max_tokens:
            self.limit_type_defs(request["currentExpression"], request["additionalTypeDefinitions"], model, request["expressionLanguage"], chat_prompt, inputs)
            self.check_expression_size(inputs.get("current_expression", ""), model)

        chat_chain = chat_prompt | model

        result, usage = await self.call_open_ai(chat_chain, inputs)

        result = await self.format_output(result, gen_messages, inputs)

        if not is_retry_step:
            result = await self.enforce_generation_constraints(request, result, gen_messages, inputs)

        result = {"explanation": result.explanation, "expression": result.expression}

        return result, usage

    @log_execution_time("FixExpression")
    async def run_fix_expression(self, request: ExpressionGenerationFixRequest, is_retry_step: bool = False) -> dict[str, Any]:
        output_type = request.get("outputType", "yaml")
        result, usage = await self._run_fix_expression(request, is_retry_step)
        if output_type == "yaml":
            result = yaml_dump({"explanation": result["explanation"], "expression": result["expression"]})
        elif output_type == "json":
            result = json.dumps({"explanation": result["explanation"], "expression": result["expression"]})

        return {"result": result, "usage": usage}

    def validate_json_response_string(self, result: str) -> str:
        """
        Validates a JSON response string from an LLM for characters that are not valid at the beggining of the string.

        Args:
            result: The result of the expression generation task
        """
        try:
            json.loads(result)
        except json.JSONDecodeError:
            if result.startswith("```\n"):
                result = result[4:]
                json_end = result.index("```")
                result = result[:json_end]
            if result.startswith("```json\n"):
                result = result[8:]
                json_end = result.index("```")
                result = result[:json_end]
            lines = []
            for line in result.strip().splitlines():
                lines.append(line.replace("\\'", "''").replace('\\"', '"'))
            result = "\n".join(lines)
        return result

    def preprocess_available_variables(self, available_variables: list[dict] | None) -> list[dict] | str:
        if not available_variables:
            return "None"
        return parse_variables(available_variables)

    def preprocess_request(self, request: BaseExpressionRequest):
        request["additionalTypeDefinitions"] = (
            ExpressionGenerationHelper._prune_definitions(request["additionalTypeDefinitions"]) if request["additionalTypeDefinitions"] else ""
        )
        available_variables = request.get("availableVariables") or []
        for request_variable in available_variables:
            type_str = cast(str, request_variable["type"])
            request_variable["type"] = str(ExpressionGenerationHelper._prune_definitions(type_str))


class ApiWorkflowExpressionGenerationTask(ExpressionGenerationTask[ExpressionGenerationModelResponse]):
    model_response_type = ExpressionGenerationModelResponse
    token_conversion_factor = 1.3  # Each word is assumed to be composed of token_conversion_factor tokens

    META_VARIABLES_SCHEMA_PATTERN = r"(?s)/\* Please consider I have the following variables: (.*?) \*/"

    def __init__(self, config_name: str, demos_name: str) -> None:
        super().__init__(config_name, demos_name)
        self.meta_variables_prompt = get_meta_variables_prompt_path()
        self.field_escaping_prompt = get_field_escaping_prompt_path()

    @override
    def get_inputs_generate_expression(self, request: ExpressionGenerationRequest) -> dict:
        inputs = super().get_inputs_generate_expression(request)
        meta_variables_schema = ""
        if request.get("additionalTypeDefinitions"):
            match = re.search(self.META_VARIABLES_SCHEMA_PATTERN, request["additionalTypeDefinitions"] or "")
            if match:
                meta_variables_schema = match.group(1)

        meta_variables_prompt = yaml_load(self.meta_variables_prompt)["add_meta_variables"].format(meta_variables_schema=meta_variables_schema)
        field_escaping_prompt = yaml_load(self.field_escaping_prompt)["field_escaping_prompt"]
        inputs["meta_variables_prompt"] = meta_variables_prompt
        inputs["field_escaping_prompt"] = field_escaping_prompt
        return inputs

    @override
    def get_inputs_fix_expression(self, request: ExpressionGenerationFixRequest) -> dict:
        inputs = super().get_inputs_fix_expression(request)
        meta_variables_schema = ""
        if request.get("additionalTypeDefinitions"):
            match = re.search(self.META_VARIABLES_SCHEMA_PATTERN, request["additionalTypeDefinitions"] or "")
            if match:
                meta_variables_schema = match.group(1)

        meta_variables_prompt = yaml_load(self.meta_variables_prompt)["add_meta_variables"].format(meta_variables_schema=meta_variables_schema)
        inputs["meta_variables_prompt"] = meta_variables_prompt
        field_escaping_prompt = yaml_load(self.field_escaping_prompt)["field_escaping_prompt"]
        inputs["field_escaping_prompt"] = field_escaping_prompt
        return inputs

    @override
    def preprocess_request(self, request: BaseExpressionRequest):
        """
        The meta-variables which are workflow-invariant are separately added to the set of available variables for the validation services.
        The $workflow variable includes the input received by the workflow.
        The $input variable includes the input received by the current activity.
        The $context variable includes the outputs of all the activities present in the workflow.
        """
        request["additionalTypeDefinitions"] = (
            ExpressionGenerationHelper._prune_definitions(request["additionalTypeDefinitions"]) if request["additionalTypeDefinitions"] else ""
        )
        additional_variables = [
            {"name": "$workflow", "type": "{input : System.object}"},
            {"name": "$context", "type": "{context : System.object}"},
            {"name": "$input", "type": "{input : System.object}"},
        ]

        available_vars = request.get("availableVariables") or []
        request["availableVariables"] = additional_variables + available_vars
        for request_variable in request["availableVariables"]:
            type_str = cast(str, request_variable["type"])
            request_variable["type"] = str(ExpressionGenerationHelper._prune_definitions(type_str))

    async def retry_generation_on_validation_error(
        self, input: ExpressionGenerationRequest | ExpressionGenerationFixRequest, result_expression: str, error_message: str
    ) -> ExpressionGenerationTaskResult:
        """
        Receives an invalid generated expression and an error message.
        Runs a Fix Expression call based on the output and the error message.
        """
        base_request = {
            "expressionLanguage": input["expressionLanguage"],
            "additionalTypeDefinitions": input["additionalTypeDefinitions"],
            "availableVariables": input["availableVariables"],
            "expressionTypeDefinition": input["expressionTypeDefinition"],
            "currentExpression": result_expression,
            "currentError": error_message,
            "outputType": "json",
        }

        current_input = ExpressionGenerationFixRequest(**base_request)

        response = await self.run_fix_expression(current_input, is_retry_step=True)
        result = json.loads(response["result"])
        response["result"] = ExpressionGenerationModelResponse(explanation=result["explanation"], expression=result["expression"])

        return response

    @override
    def preprocess_available_variables(self, available_variables: list[dict] | None) -> str:
        available_variables = available_variables or []
        return parse_variables_api_workflows(available_variables)

    @override
    async def enforce_generation_constraints(
        self,
        request: ExpressionGenerationRequest | ExpressionGenerationFixRequest,
        result: ExpressionGenerationModelResponse,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> ExpressionGenerationModelResponse:
        if request["expressionLanguage"] == "jq":
            try:
                current_expression = result.expression
                to_check = JqExpressionsHelper.jq_mock_variables(current_expression, request["availableVariables"])
                jq.compile(to_check)
            except ValueError as e:
                response = await self.retry_generation_on_validation_error(request, current_expression, str(e))
                try:
                    to_check = JqExpressionsHelper.jq_mock_variables(response["result"].expression, request["availableVariables"])
                    jq.compile(to_check)
                except ValueError as e:
                    print(f"Invalid jq expression fix for {request['currentExpression']}")
                result = response["result"]

        elif request["expressionLanguage"] == "javascript":
            available_variables = [variable["name"] for variable in request["availableVariables"]]
            try:
                JavaScriptExpressionsHelper.validate_js_expression(result.expression, set(available_variables))
            except Exception as e:
                response = await self.retry_generation_on_validation_error(request, result.expression, str(e))
                try:
                    JavaScriptExpressionsHelper.validate_js_expression(response["result"].expression, set(available_variables))
                except Exception as e:
                    print(f"Invalid javascript expression fix for {request['currentExpression']}")
                    raise e
                result = response["result"]
        return result

    @override
    def limit_type_defs(
        self,
        target_request: str,
        additional_type_definitions: str,
        model: langchain_core.language_models.BaseChatModel,
        expression_language: str,
        chat_prompt: langchain.prompts.ChatPromptTemplate,
        inputs: dict,
    ) -> None:
        prompt_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
        if prompt_number_of_tokens + model.max_model_tokens > model.max_total_tokens:
            inputs["available_types"] = ""
            no_typedefs_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
            typedefs_available_tokens = model.max_total_tokens - no_typedefs_number_of_tokens - 1024  # keep a buffer of 1024 tokens
            try:
                """
                Namespaces, functions, classes, enums, types, etc. are parsed and separated from each other and grouped in namespaces.
                The most relevant typedefs are selected based on the number of tokens available.
                In the case of a parsing failure of the d.ts definitions, the input is chunked so that only a fitting prefix is used.
                Since the tokenizer used by the Gemini model cannot be used independently, an approximation is used using the default langchai tokenizer.
                """
                inputs["available_types"] = self._filter_typedefs(
                    model, target_request, additional_type_definitions, typedefs_available_tokens, expression_language
                )
            except ValueError:
                additional_typedefs_tokens = 0 if additional_type_definitions == "None" else model.get_num_tokens(additional_type_definitions)
                token_difference = additional_typedefs_tokens - typedefs_available_tokens
                discarded_words = token_difference / self.token_conversion_factor
                chunked_prefix = additional_type_definitions.split()[: -int(discarded_words)]
                inputs["available_types"] = " ".join(chunked_prefix)


class BPMNExpressionGenerationTask(ExpressionGenerationTask[ExpressionGenerationBPMNModelResponse]):
    model_response_type = ExpressionGenerationBPMNModelResponse

    def __init__(self, config_name: str, demos_name: str) -> None:
        super().__init__(config_name, demos_name)
        self.model_manager_name = "expression_generation_bpmn_model"

    @override
    async def format_output(
        self,
        result: langchain.schema.AIMessage,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> ExpressionGenerationBPMNModelResponse:
        """
        Formats an AI message into an ExpressionGenerationBPMNModelResponse object.
        Tries to regenerate the output if it is not a valid JSON object.

        Args:
            result: The result of the expression generation task
            gen_messages: The messages generated by the model
            inputs: The inputs used for generation
        """
        if isinstance(result, langchain.schema.AIMessage):
            result.content = self.validate_json_response_string(result.content)  # type: ignore
            try:
                response = json.loads(result.content)
                response = ExpressionGenerationBPMNModelResponse(
                    expression=response["expression"], explanation=response["explanation"], sampleInput=response.get("sampleInput", None)
                )
                return response
            except (json.JSONDecodeError, KeyError):
                response = await self.regenerate_response_for_invalid_json(result, gen_messages, inputs)
                return response

        return result

    @override
    def preprocess_available_variables(self, available_variables: list[dict] | None) -> str:
        available_variables = available_variables or []
        return parse_variables_bpmn(available_variables)

    @override
    async def enforce_generation_constraints(
        self,
        request: ExpressionGenerationRequest | ExpressionGenerationFixRequest,
        result: ExpressionGenerationBPMNModelResponse,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> ExpressionGenerationBPMNModelResponse:
        return result


class JsInvokeExpressionGenerationTask(ExpressionGenerationTask[ExpressionGenerationModelResponse]):
    model_response_type = ExpressionGenerationModelResponse
    token_conversion_factor = 1.3  # Each word is assumed to be composed of token_conversion_factor tokens
    META_VARIABLES_SCHEMA_PATTERN = r"(?s)/\* Please consider I have the following variables: (.*?) \*/"

    def __init__(self, config_name: str, demos_name: str) -> None:
        super().__init__(config_name, demos_name)
        self.meta_variables_prompt = get_meta_variables_prompt_path()
        self.field_escaping_prompt = get_field_escaping_prompt_path()

    def postprocess_output(self, result: dict[str, Any]) -> dict[str, Any]:
        """
        This method checks whether the generated JavaScript function body places each instruction on a new line.
        It does so by counting the maximum number of semicolons in a single line.
        If multiple semicolons are found, the jsbeautifier library is used to format the code according to JS standards.
        """
        rows = result["expression"].split("\n")
        semicolons_counts = list(map(lambda row: row.count(";"), rows))
        max_semicolons = max(semicolons_counts) if semicolons_counts else 0
        if max_semicolons > 1:
            result["expression"] = jsbeautifier.beautify(result["expression"])
        return result

    @log_execution_time("ExpressionGenerationGen")
    async def run(self, request: ExpressionGenerationRequest) -> dict[str, Any]:
        result, usage = await self._run(request)
        result = self.postprocess_output(result)
        output_type = request.get("outputType", "yaml")
        if output_type == "yaml":
            result = yaml_dump({"explanation": result["explanation"], "expression": result["expression"]})
        elif output_type == "json":
            result = json.dumps({"explanation": result["explanation"], "expression": result["expression"]})

        return {"result": result, "usage": usage}

    @log_execution_time("FixExpression")
    async def run_fix_expression(self, request: ExpressionGenerationFixRequest, is_retry_step: bool = False) -> dict[str, Any]:
        output_type = request.get("outputType", "yaml")
        result, usage = await self._run_fix_expression(request, is_retry_step)
        result = self.postprocess_output(result)
        if output_type == "yaml":
            result = yaml_dump({"explanation": result["explanation"], "expression": result["expression"]})
        elif output_type == "json":
            result = json.dumps({"explanation": result["explanation"], "expression": result["expression"]})

        return {"result": result, "usage": usage}

    @override
    def get_inputs_generate_expression(self, request: ExpressionGenerationRequest) -> dict:
        inputs = super().get_inputs_generate_expression(request)
        meta_variables_schema = ""
        if request.get("additionalTypeDefinitions"):
            match = re.search(self.META_VARIABLES_SCHEMA_PATTERN, request["additionalTypeDefinitions"] or "")
            if match:
                meta_variables_schema = match.group(1)

        meta_variables_prompt = yaml_load(self.meta_variables_prompt)["add_meta_variables"].format(meta_variables_schema=meta_variables_schema)
        inputs["meta_variables_prompt"] = meta_variables_prompt
        field_escaping_prompt = yaml_load(self.field_escaping_prompt)["field_escaping_prompt"]
        inputs["field_escaping_prompt"] = field_escaping_prompt
        return inputs

    @override
    def get_inputs_fix_expression(self, request: ExpressionGenerationFixRequest) -> dict:
        inputs = super().get_inputs_fix_expression(request)
        meta_variables_schema = ""
        if request.get("additionalTypeDefinitions"):
            match = re.search(self.META_VARIABLES_SCHEMA_PATTERN, request["additionalTypeDefinitions"] or "")
            if match:
                meta_variables_schema = match.group(1)

        meta_variables_prompt = yaml_load(self.meta_variables_prompt)["add_meta_variables"].format(meta_variables_schema=meta_variables_schema)
        inputs["meta_variables_prompt"] = meta_variables_prompt
        field_escaping_prompt = yaml_load(self.field_escaping_prompt)["field_escaping_prompt"]
        inputs["field_escaping_prompt"] = field_escaping_prompt
        return inputs

    def preprocess_available_variables(self, available_variables: list[dict] | None) -> str:
        available_variables = available_variables or []
        return parse_variables_api_workflows(available_variables)

    @override
    def preprocess_request(self, request: BaseExpressionRequest):
        """
        The meta-variables which are workflow-invariant are separately added to the set of available variables for the validation services.
        The $workflow variable includes the input received by the workflow.
        The $input variable includes the input received by the current activity.
        The $context variable includes the outputs of all the activities present in the workflow.
        """
        request["additionalTypeDefinitions"] = (
            ExpressionGenerationHelper._prune_definitions(request["additionalTypeDefinitions"]) if request["additionalTypeDefinitions"] else ""
        )
        additional_variables = [
            {"name": "$workflow", "type": "{input : System.object}"},
            {"name": "$context", "type": "{context : System.object}"},
            {"name": "$input", "type": "{input : System.object}"},
        ]
        available_vars = request.get("availableVariables") or []
        request["availableVariables"] = additional_variables + available_vars
        for request_variable in request["availableVariables"]:
            type_str = cast(str, request_variable["type"])
            request_variable["type"] = str(ExpressionGenerationHelper._prune_definitions(type_str))

    async def retry_generation_on_validation_error(
        self, input: ExpressionGenerationRequest | ExpressionGenerationFixRequest, result_expression: str, error_message: str
    ) -> ExpressionGenerationTaskResult:
        """
        Receives an invalid generated expression and an error message.
        Runs a Fix Expression call based on the output and the error message.
        """
        base_request = {
            "expressionLanguage": input["expressionLanguage"],
            "additionalTypeDefinitions": input["additionalTypeDefinitions"],
            "availableVariables": input["availableVariables"],
            "expressionTypeDefinition": input["expressionTypeDefinition"],
            "currentExpression": result_expression,
            "currentError": error_message,
            "outputType": "json",
        }

        current_input = ExpressionGenerationFixRequest(**base_request)

        response = await self.run_fix_expression(current_input, is_retry_step=True)
        result = json.loads(response["result"])
        response["result"] = ExpressionGenerationModelResponse(explanation=result["explanation"], expression=result["expression"])

        return response

    @override
    async def enforce_generation_constraints(
        self,
        request: ExpressionGenerationRequest | ExpressionGenerationFixRequest,
        result: ExpressionGenerationModelResponse,
        gen_messages: list[BaseMessagePromptTemplate],
        inputs: dict,
    ) -> ExpressionGenerationModelResponse:
        available_variables = [variable["name"] for variable in request["availableVariables"]]
        try:
            JavaScriptExpressionsHelper.validate_js_invoke(result.expression, set(available_variables))
        except Exception as e:
            response = await self.retry_generation_on_validation_error(request, result.expression, str(e))
            try:
                JavaScriptExpressionsHelper.validate_js_invoke(response["result"].expression, set(available_variables))
            except Exception as e:
                print(f"Invalid javascript expression fix for {request['currentExpression']}")
                raise e
            result = response["result"]
        return result

    @override
    def limit_type_defs(
        self,
        target_request: str,
        additional_type_definitions: str,
        model: langchain_core.language_models.BaseChatModel,
        expression_language: str,
        chat_prompt: langchain.prompts.ChatPromptTemplate,
        inputs: dict,
    ) -> None:
        prompt_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
        if prompt_number_of_tokens + model.max_model_tokens > model.max_total_tokens:
            inputs["available_types"] = ""
            no_typedefs_number_of_tokens = model.get_num_tokens(chat_prompt.format(**inputs))
            typedefs_available_tokens = model.max_total_tokens - no_typedefs_number_of_tokens - 1024  # keep a buffer of 1024 tokens
            try:
                """
                Namespaces, functions, classes, enums, types, etc. are parsed and separated from each other and grouped in namespaces.
                The most relevant typedefs are selected based on the number of tokens available.
                In the case of a parsing failure of the d.ts definitions, the input is chunked so that only a fitting prefix is used.
                Since the tokenizer used by the Gemini model cannot be used independently, an approximation is used using the default langchai tokenizer.
                """
                inputs["available_types"] = self._filter_typedefs(
                    model, target_request, additional_type_definitions, typedefs_available_tokens, expression_language
                )
            except ValueError:
                additional_typedefs_tokens = 0 if additional_type_definitions == "None" else model.get_num_tokens(additional_type_definitions)
                token_difference = additional_typedefs_tokens - typedefs_available_tokens
                discarded_words = token_difference / self.token_conversion_factor
                chunked_prefix = additional_type_definitions.split()[: -int(discarded_words)]
                inputs["available_types"] = " ".join(chunked_prefix)
