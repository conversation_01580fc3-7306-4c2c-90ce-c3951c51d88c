from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    ExpressionGenerationFixRequest,
    ExpressionGenerationRequest,
    ExpressionGenerationResponse,
    ExpressionLanguageType,
    SourceType,
)
from services.studio._text_to_workflow.expression_generation.expression_generation_task import (
    ApiWorkflowExpressionGenerationTask,
    BPMNExpressionGenerationTask,
    ExpressionGenerationTask,
    JsInvokeExpressionGenerationTask,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()

GENERATION_TASK_CONFIGURATIONS: dict[SourceType, ExpressionGenerationTask] = {
    SourceType.Workflow: ExpressionGenerationTask("prompt.yaml", "demos.yaml"),
    SourceType.BPMN: BPMNExpressionGenerationTask("bpmn/config.yaml", "bpmn/examples.yaml"),
    SourceType.ApiWorkflow: ApiWorkflowExpressionGenerationTask("prompt.yaml", "demos.yaml"),
    SourceType.JsInvoke: JsInvokeExpressionGenerationTask("js_invoke/config.yaml", "js_invoke/examples.yaml"),
}


async def generate(request: ExpressionGenerationRequest) -> ExpressionGenerationResponse:
    expression_generation_task = _get_generation_task(request)
    result = await expression_generation_task.run(request)
    expression_response = {"result": result["result"], "usage": result["usage"].to_json(), "fingerprints": []}

    LOGGER.log_custom_event(
        "TextToWorkflow.ExpressionGenerationGen.Data",
        {"source": request.get("source", SourceType.Workflow).value, "outputType": request.get("outputType", "yaml")},
    )

    return expression_response


async def fix_expression(request: ExpressionGenerationFixRequest) -> ExpressionGenerationResponse:
    # fix endpoint does not support bpmn -> default the source to workflow
    request["source"] = SourceType.Workflow
    expression_generation_task = _get_generation_task(request)
    result = await expression_generation_task.run_fix_expression(request)
    fix_response = {"result": result["result"], "usage": result["usage"].to_json(), "fingerprints": []}
    return fix_response


def _get_generation_task(request: ExpressionGenerationRequest | ExpressionGenerationFixRequest) -> ExpressionGenerationTask:
    if request.get("source", SourceType.Workflow) == SourceType.BPMN:
        return GENERATION_TASK_CONFIGURATIONS[SourceType.BPMN]
    if request.get("activityTypeName", None) == "JsInvoke":
        return GENERATION_TASK_CONFIGURATIONS[SourceType.JsInvoke]
    if request.get("expressionLanguage", None) in [ExpressionLanguageType.JavaScript.value, ExpressionLanguageType.JQ.value]:
        return GENERATION_TASK_CONFIGURATIONS[SourceType.ApiWorkflow]

    return GENERATION_TASK_CONFIGURATIONS[SourceType.Workflow]
