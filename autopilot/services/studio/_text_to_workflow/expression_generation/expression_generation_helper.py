from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionGenerationFixRequest, ExpressionGenerationRequest


class ExpressionGenerationHelper:
    @staticmethod
    def check_is_complex_request(request: ExpressionGenerationRequest | ExpressionGenerationFixRequest) -> bool:
        # This function check whether the user request should be solved using a RegEx pattern
        keywords = ["regex", "match", "regular expression"]
        for keyword in keywords:
            if keyword in request.get("userRequest", "").lower():
                return True
            name = request.get("name", "") or ""
            if keyword in name.lower():
                return True
        return False

    @staticmethod
    def _prune_definitions(type_str: str) -> str:
        ignored_defintions = ["UiPath."]
        for ignored_def in ignored_defintions:
            type_str = type_str.replace(ignored_def, "")
        return type_str
