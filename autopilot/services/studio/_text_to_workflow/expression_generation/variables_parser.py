def parse_variables(available_variables: list):
    variables_list = []
    for variable in available_variables:
        variables_list.append("{} as {}".format(variable["name"], variable["type"]))
    return "\n".join(variables_list)


def parse_variables_api_workflows(available_variables: list):
    variables_list = []
    for variable in available_variables:
        variable["type"] = variable["type"].split(", ")[0]
        variables_list.append("{} as {}".format(variable["name"], variable["type"]))
    return ", ".join(variables_list)


def parse_variables_bpmn(available_variables: list):
    variables_list = []
    for variable in available_variables:
        variable["type"] = variable["type"].split(", ")[0]
        variables_list.append("{} as {} with Id: {} in Activity: '{}'".format(variable["name"], variable["type"], variable["id"], variable["activityName"]))
    return ", ".join(variables_list)


if __name__ == "__main__":
    variable1 = {"name": "employee1", "type": "Employee"}

    variable2 = {"name": "employee2", "type": "Employee"}

    variable3 = {
        "name": "email",
        "type": "UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
    }

    parsed_variable3 = parse_variables([variable3])
    print(parsed_variable3)
    variables = []
    variables.append(variable1)
    variables.append(variable2)

    print(parse_variables(variables))
