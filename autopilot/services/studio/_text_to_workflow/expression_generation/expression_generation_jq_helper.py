import re


class JqExpressionsHelper:
    @staticmethod
    def jq_mock_variables(expression: str, available_variables: list) -> str:
        """
        Handles missing variable definitions in the context of compiling jq expressions.
        It receives an expression as input and returns a modified expression that contains mock definitions for all the undefined variables
        """
        # We do not want to mock unavailable variables
        available_variables_names = set([variable["name"].lstrip("$") for variable in available_variables])
        # The set of all variables used in the expression
        all_variables = set(re.findall(r"\$([a-zA-Z_][a-zA-Z0-9_]*)", expression))
        # The set of intermediary variables, which are declared inside the expression. They are prefixed by the keyword "as"
        declared_variables = set([match[1] for match in re.findall(r"(as)\s*\$([a-zA-Z_][a-zA-Z0-9_]*)", expression)])
        # The difference between these two sets represents the set of variables that are not declared
        # They will trigger SynthaxError when using the pyjq compiler.
        variables = all_variables - declared_variables
        variables = variables.intersection(available_variables_names)
        # Each variable is declared as null. This allows the pyjq compiler to consider the variables declared.
        variables_pipe = JqExpressionsHelper.create_variables_pipe(variables)
        if len(variables) > 0:
            return variables_pipe + " | " + expression
        # The new pipeline is returned
        return expression

    @staticmethod
    def create_variables_pipe(variables: set[str]) -> str:
        """
        Creates a pipe-separated string of variable declarations.
        """
        return "|".join(sorted([f"null as ${variable}" for variable in variables]))
