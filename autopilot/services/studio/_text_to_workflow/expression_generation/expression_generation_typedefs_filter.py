from typing import <PERSON><PERSON>

import numpy as np

from services.studio._text_to_workflow.common.typedefs_parser import parse_namespaces, parse_namespaces_api_workflows
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionLanguageType
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel


class ExpressionGenerationTypedefsFilter:
    def __init__(self, embeddings_model: EmbeddingModel) -> None:
        self.embeddings_model = embeddings_model

    def _flatten_namespaces(self, parsed_namespaces) -> list[dict]:
        flattened_namespaces = []
        for namespace_name in parsed_namespaces:
            for typdef_class_name in parsed_namespaces[namespace_name]:
                flattened_type = f"{namespace_name}   {typdef_class_name}"
                flattened_namespaces.append({"namespace_name": namespace_name, "typdef_class_name": typdef_class_name, "flattened_type": flattened_type})
        return flattened_namespaces

    def _get_prompt_typedefs_similarity(self, prompt: str, flattened_types) -> list[Tuple[dict, float]]:
        encoded_prompt = self.embeddings_model.encode(prompt)
        prompt_embeddings = np.array(encoded_prompt)
        typedef_scores = []
        for typedef in flattened_types:
            typedef_encoding = self.embeddings_model.encode(typedef["flattened_type"])

            typedef_embedding_arr = np.array(typedef_encoding).transpose()
            score = prompt_embeddings @ typedef_embedding_arr
            typedef_scores.append({"typedef_info": typedef, "score": score})

        sorted_typedef_scores = sorted(typedef_scores, key=lambda x: x["score"], reverse=True)

        return sorted_typedef_scores

    def rebuild_typedefinition_string(self, filtered_typedefs_info, namespaces_withdefs):
        res = {}
        for typedef_info in filtered_typedefs_info:
            typedef_class_name = typedef_info["typdef_class_name"]
            namespace_name = typedef_info["namespace_name"]
            if namespace_name in res:
                res[namespace_name].append(typedef_class_name)
            else:
                res[namespace_name] = [typedef_class_name]

        filtered_typedefinition_string = ""
        for namespace in res:
            filtered_typedefinition_string += f"namespace {namespace} {{"
            for typedef in res[namespace]:
                filtered_typedefinition_string += f"{namespaces_withdefs[namespace][typedef]}"
            filtered_typedefinition_string += "}"
        return filtered_typedefinition_string

    def get_typedefs_scores(self, prompt: str, activities_typedefs: str, expression_language: str):
        if expression_language in [ExpressionLanguageType.CSharp.value, ExpressionLanguageType.VBNET.value]:
            parsed_namespaces = parse_namespaces(activities_typedefs)
        else:
            parsed_namespaces = parse_namespaces_api_workflows(activities_typedefs)

        flattened_namespaces = self._flatten_namespaces(parsed_namespaces)
        typedefs_with_scores = self._get_prompt_typedefs_similarity(prompt, flattened_namespaces)

        return typedefs_with_scores, parsed_namespaces
