import json
import os
import pathlib
import re
import traceback
from typing import Generic, Type, TypeVar

from langchain_community.callbacks import get_openai_callback
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.prompts.chat import HumanMessagePromptTemplate, SystemMessagePromptTemplate

from services.studio._text_to_workflow.assistants.services.attachments_converter import AttachmentsConverter
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    APIWorkflowAssistantRequest,
    APIWorkflowAssistantRouterModelResponse,
    ChatMessage,
    RPAWorkflowAssistantRequest,
    WorkflowAssistantBaseRequest,
    WorkflowAssistantContext,
    WorkflowAssistantPromptContent,
    WorkflowAssistantRouterModelResponse,
    WorkflowAssistantRouterResponse,
    WorkflowRouterException,
)
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.models.model_manager import Model<PERSON>anager
from services.studio._text_to_workflow.models.output_parsers import build_pydantic_parser_with_fix
from services.studio._text_to_workflow.utils import request_utils, telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.workflow_utils import replace_blobs_with_hash
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema

LOGGER = AppInsightsLogger()

ReqT = TypeVar("ReqT", bound=WorkflowAssistantBaseRequest)
ResponseT = TypeVar("ResponseT", bound=WorkflowAssistantRouterModelResponse)


class WorkflowRouterBaseTask(Generic[ReqT, ResponseT]):
    # Add a class variable to store the response type
    router_response_type: Type[ResponseT]

    def __init__(self, config: dict):
        self.model_manager = ModelManager()

        self.config = config

        # prompt components shared by all router tasks
        common_config: dict = yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "common_prompt.yaml")))
        self.common_user_prompt_parts: dict[str, str] = common_config["common_user_prompt_parts"]
        self.common_system_msg_parts: dict[str, str] = common_config["common_system_msg_parts"]

        self.attachments_converter = AttachmentsConverter()

    @telemetry_utils.log_execution_time("workflow_router_task.process")
    async def process(self, request: ReqT, message_emitter: IMessageEmitter) -> WorkflowAssistantRouterResponse:
        response, context = await self._process_internal(request, message_emitter)
        return WorkflowAssistantRouterResponse.from_model_response(response, context)

    async def _process_internal(self, request: ReqT, message_emitter: IMessageEmitter) -> tuple[ResponseT, WorkflowAssistantContext]:
        router_context = self._build_context(request)
        model = self.model_manager.get_llm_model("workflow_assistant_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)

        # Use the class-level router_response_type instead of the TypeVar as TypeVar cannot be used
        parser = build_pydantic_parser_with_fix(model, self.router_response_type)

        prompt = SystemMessagePromptTemplate.from_template(
            template=self.config["systemPrompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

        gen_messages = [prompt, HumanMessagePromptTemplate.from_template(template=self.common_user_prompt_parts["base_user_prompt"])]
        chat_prompt = ChatPromptTemplate.from_messages(gen_messages)
        chain = chat_prompt | model | parser

        # Get user localization from request context
        request_context = request_utils.get_request_context()
        user_localization = "en-US"  # Default to English if no localization is found
        if request_context and hasattr(request_context, "localization") and request_context.localization:
            user_localization = request_context.localization

        prompt_content = self._build_common_prompt_content(router_context, user_localization) | await self._build_prompt_content(
            request, router_context, user_localization, model
        )

        await message_emitter.emit_debug_message(lambda: "Prompt:\n" + chat_prompt.format(**prompt_content))

        for i in range(int(self.config["maxRetries"])):
            try:
                with get_openai_callback() as cb:
                    response: ResponseT = await chain.ainvoke(prompt_content)  # type: ignore

                    usage = workflow_generation_schema.TokenUsage(
                        model=model.model_name,  # type: ignore
                        prompt_tokens=cb.prompt_tokens,
                        completion_tokens=cb.completion_tokens,
                        total_tokens=cb.total_tokens,
                    )

                await message_emitter.emit_debug_message(lambda u=usage: "Usage:\n" + json.dumps(u.to_json(), indent=2))
                await message_emitter.emit_message(json.dumps(response.model_dump(), indent=2), message_type="router")

                await message_emitter.emit_message(response.message)

                return response, router_context

            except Exception as e:
                await message_emitter.emit_debug_message(f"⚠️ Failed to generate router response, retrying... Error: {traceback.format_exc()}")
                LOGGER.exception(f"⚠️ Failed to generate router response, retrying... Error: {traceback.format_exc()}")

                if i == int(self.config["maxRetries"]) - 1:
                    if settings.IS_PROD:
                        raise e

        raise WorkflowRouterException("❌ Failed to generate router response.")

    def _build_common_prompt_content(self, router_context: WorkflowAssistantContext, user_localization: str) -> dict:
        common_prompt_placeholders = {
            "user_localization": user_localization,
            "current_time": router_context.current_time.isoformat(),
        }
        prompt_content = {}

        for key, value in self.common_system_msg_parts.items():
            if isinstance(value, str):
                # format the string with the placeholders
                prompt_content[key] = value.format(**common_prompt_placeholders)

        return prompt_content

    async def _build_prompt_content(
        self, request: ReqT, router_context: WorkflowAssistantContext, user_localization: str, model: BaseChatModel
    ) -> WorkflowAssistantPromptContent:
        return WorkflowAssistantPromptContent(
            user_query=request.userRequest,
            current_time=router_context.current_time.isoformat(),
            user_localization=user_localization,
            message_history=self._build_message_history_prompt(router_context),
            available_entities=self._build_available_entities_prompt(router_context),
            current_workflow=self._build_current_workflow_prompt(router_context),
            additional_instructions=self._build_additional_instructions_prompt(request),
        )

    def _build_message_history_prompt(self, context: WorkflowAssistantContext) -> str:
        history = ""

        # Note that python dictionaries are insertion ordered, so it is safe to iterate over the values
        for message_id, message in context.message_history_map.items():
            history += self.common_user_prompt_parts["messagesPrompt"].format(
                role=message.role, content=message.content, category=message.category, message_id=message_id, timestamp=str(message.timestamp)
            )
        return history

    def _build_additional_instructions_prompt(self, request: WorkflowAssistantBaseRequest) -> str:
        if request.additionalInstructions:
            return "\n" + self.common_user_prompt_parts["additionalInstructionsPrompt"].format(additional_instructions=request.additionalInstructions)
        else:
            return ""

    def _build_available_entities_prompt(self, context: WorkflowAssistantContext) -> str:
        available_entities_prompt = ""
        for entity_id, entity_name in context.assets.items():
            available_entities_prompt += self.common_user_prompt_parts["assetsPrompt"].format(asset_id=entity_id, name=entity_name)

        for entity_id, entity_name in context.queues.items():
            available_entities_prompt += self.common_user_prompt_parts["queuesPrompt"].format(queue_id=entity_id, name=entity_name)

        for entity_id, entity_name in context.processes.items():
            available_entities_prompt += self.common_user_prompt_parts["processesPrompt"].format(process_id=entity_id, name=entity_name)

        return available_entities_prompt

    def _build_current_workflow_prompt(self, context: WorkflowAssistantContext) -> str:
        if context.current_workflow:
            # Preprocess the current workflow to replace long blobs with hashes
            current_workflow_content, _ = replace_blobs_with_hash(context.current_workflow.content, use_b64_charset=True)

            return (
                self.config["currentWorkflowPrompt"].format(
                    current_workflow_content=current_workflow_content,
                    current_workflow_file_path=context.current_workflow.path,
                    current_workflow_description=context.current_workflow.metadata.description,
                    current_workflow_errors=self._build_errors_prompt(context),
                    current_workflow_last_modified=context.current_workflow.metadata.lastModified
                    if context.current_workflow.metadata.lastModified
                    else "Unknown",
                    available_variables=context.current_workflow_designer_state.availableVariables if context.current_workflow_designer_state else "None",
                    available_additional_type_definitions=context.current_workflow_designer_state.availableAdditionalTypeDefinitions,
                )
                if context.current_workflow_designer_state
                else "None"
            )
        else:
            return self.common_user_prompt_parts["missingCurrentWorkflowPrompt"]

    def _build_errors_prompt(self, context: WorkflowAssistantContext) -> str:
        errors_prompt = ""
        for error_id, error in context.current_workflow_errors.items():
            errors_prompt += self.common_user_prompt_parts["currentWorkflowErrorsPrompt"].format(
                error_id=error_id, activity_id=f"{error.activityId} (property: {error.property})" if error.property else error.activityId, message=error.message
            )
        return errors_prompt

    def _build_context(self, request: ReqT) -> WorkflowAssistantContext:
        context = WorkflowAssistantContext(user_request=request.userRequest)

        if request.currentWorkflow:
            context.current_workflow = request.currentWorkflow
            context.current_workflow_designer_state = request.currentWorkflowDesignerState
            if request.currentWorkflow.metadata.errors:
                context.current_workflow_errors = {idx: error for idx, error in enumerate(request.currentWorkflow.metadata.errors)}

        if request.attachments:
            context.attachments = {idx: attachment for idx, attachment in enumerate(request.attachments)}

        if request.messageHistory:
            context.message_history_map = {idx: message for idx, message in enumerate(request.messageHistory)}

        if request.projectDefinition:
            context.project_files_map = {idx: file for idx, file in enumerate(request.projectDefinition.files)}

        if request.availableEntities:
            # Use a single counter across all entity types to ensure unique IDs
            entity_id = 0
            context.assets = {}
            for asset in request.availableEntities.assets:
                context.assets[entity_id] = asset
                entity_id += 1

            context.queues = {}
            for queue in request.availableEntities.queues:
                context.queues[entity_id] = queue
                entity_id += 1

            context.processes = {}
            for process in request.availableEntities.processes:
                context.processes[entity_id] = process
                entity_id += 1

        self._parse_message_history(request.messageHistory, context)
        return context

    # TO DO: leaving this generic, we might need to change it a bit for API wfs
    def _parse_message_history(self, message_history: list[ChatMessage], context: WorkflowAssistantContext):
        history = ""

        # Starting from 1 because 0 is reserved for the current workflow
        workflow_id = 1
        for message in message_history:
            # If the message is a workflow and the content is a valid yaml, it is a workflow definition
            if message.category == "workflow":
                # Extract workflow content from yaml code block if present
                wf_match = re.search(r"```yaml(.*?)```", message.content, re.DOTALL)
                if not wf_match:
                    continue

                wf_content = wf_match.group(1).strip()

                # Check for and extract file path if present
                file_path_match = re.match(r"file_path:\s*(.*?)\n(.*)", wf_content, re.DOTALL)
                file_path = None
                if file_path_match:
                    file_path = file_path_match.group(1)
                    wf_content = file_path_match.group(2)

                    message.content = f"Workflow (id {workflow_id}, file path {file_path}): ```yaml\n{wf_content}\n```"
                else:
                    message.content = f"Workflow (id {workflow_id}): ```yaml\n{wf_content}\n```"

                # Save the mapping between the message content and the workflow id, so that we can use this in case the model selects it
                context.message_history_workflow_ids[workflow_id] = (wf_content, file_path)
                workflow_id += 1
            else:
                history += f"{message.role}: {message.content}\n"

        return history


class WorkflowRouterTask(WorkflowRouterBaseTask[RPAWorkflowAssistantRequest, WorkflowAssistantRouterModelResponse]):
    # Set the concrete response class
    router_response_type = WorkflowAssistantRouterModelResponse

    def __init__(self):
        config = yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "rpa_wf_prompt.yaml")))
        super().__init__(config)


class APIWorkflowRouterTask(WorkflowRouterBaseTask[APIWorkflowAssistantRequest, APIWorkflowAssistantRouterModelResponse]):
    # Set the concrete response class
    router_response_type = APIWorkflowAssistantRouterModelResponse

    def __init__(self):
        config = yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "api_wf_prompt.yaml")))
        super().__init__(config)
