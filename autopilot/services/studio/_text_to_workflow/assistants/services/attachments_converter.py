import asyncio
import base64
import os

from langchain_core.language_models import BaseChatModel
from markitdown import MarkItDown
from requests import Response
from requests.structures import CaseInsensitiveDict

from services.studio._text_to_workflow.common.schema import File
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class AttachmentsConverter:
    md: MarkItDown

    def __init__(self):
        self.md = MarkItDown()

    async def get_attachment_content(self, attachment: File) -> tuple[str | None, str | None]:
        try:
            # Skip image conversion as we will pass those images directly to the model
            if attachment.mime_type.startswith("image/"):
                return None, None

            # Skip .xaml files as they are handled separately
            if attachment.path.lower().endswith(".xaml"):
                return None, None

            mock_response = self._create_mock_response(attachment)
            content = await asyncio.to_thread(self.md.convert, mock_response)
        except Exception as e:
            LOGGER.error(f"Error converting attachment {attachment.path}: {e}")
            content = None

        if content is None:
            return None, None

        return content.text_content, content.title

    def _create_mock_response(self, attachment: File):
        class MockResponse(Response):
            def __init__(self, content: str, url: str, headers: CaseInsensitiveDict):
                super().__init__()
                self._content = base64.b64decode(content)
                self.url = url
                self.headers = headers

            def iter_content(self, chunk_size=None, decode_unicode=False):
                yield self._content

        return MockResponse(
            content=attachment.content,
            url=attachment.path,
            headers=CaseInsensitiveDict(
                {"content-type": attachment.mime_type, "content-disposition": f'attachment; filename="{os.path.basename(attachment.path)}"'}
            ),
        )

    def _process_content(self, content, model: BaseChatModel, max_tokens: int, current_tokens: int) -> str:
        if content.text_content is None:
            return ""

        new_tokens = model.get_num_tokens(content.text_content)
        if current_tokens + new_tokens < max_tokens:
            return content.text_content
        return ""
