import json

import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as aes
import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_task as aet
import services.studio._text_to_workflow.common.api_activity_retriever as ar
import services.studio._text_to_workflow.common.api_workflow.schema as api_wf_schema
import services.studio._text_to_workflow.common.api_workflow.workflow_parser as awp
import services.studio._text_to_workflow.utils.inference.llm_schema as llms
import services.studio._text_to_workflow.utils.telemetry_utils as tu

LOGGER = tu.AppInsightsLogger()


class ApiActivityEditEndpoint:
    """Endpoint for editing an activity in the workflow. Handles service logic, like SSE implementation."""

    def __init__(self) -> None:
        """Initialize the endpoint."""
        self.task = aet.get(aes.BASIC_MODEL)

        self.activities_retriever = ar.APIActivitiesRetriever()

    async def edit(self, request: aes.EndpointRequest) -> tuple[aes.EndpointResponse, aes.Trace]:
        """Edit an activity in the workflow."""
        task_request = self._to_task_request(request)
        response, current_activity, current_schema, usage, trace = await self.task.run(task_request)
        return self._to_endpoint_response(response, usage, current_activity, current_schema), trace

    def _to_task_request(self, request: aes.EndpointRequest) -> aes.TaskRequest:
        """Convert an EndpointRequest to a TaskRequest."""
        workflow, metadata, old2new_id = awp.ApiWorkflowParser(self.activities_retriever).parse_api_workflow(request.workflow)
        workflow = api_wf_schema.ApiWorkflowForActivityEdit(input=json.loads(workflow.input), root=workflow.root)
        if request.activityId not in old2new_id:
            raise ValueError(f"Activity ID {request.activityId} could not be mapped to internal activity ID.")
        schemas = {}
        if request.schemas is not None:
            for activity_id, schema in request.schemas.items():
                if activity_id not in old2new_id:
                    raise ValueError(f"Activity ID {activity_id} from schemas could not be mapped to internal activity ID.")
                new_activity_id = old2new_id[activity_id]
                schemas[new_activity_id] = schema
        new_activity_id = old2new_id[request.activityId]
        return aes.TaskRequest(
            messages=request.messages,
            activity_id=new_activity_id,
            expression_language=request.expressionLanguage,
            workflow=workflow,
            metadata=metadata,
            schemas=schemas,
        )

    def _to_endpoint_response(
        self,
        response: aes.TaskResponse,
        usage: llms.TokenUsage,
        current_activity: api_wf_schema.BASE_API_ACTIVITIES,
        current_schema: dict,
    ) -> aes.EndpointResponse:
        """Convert an LLMResponse to an EndpointResponse."""
        arguments_changes, properties_changes = [], []
        for operation in response.arguments_operations:
            schema = operation.schema_.model_dump()
            if schema.get("type") == "object":
                properties_str = schema.get("properties", "")
                if properties_str:
                    try:
                        properties = json.loads(properties_str)
                    except json.JSONDecodeError:
                        LOGGER.warning(f"Failed to parse properties string: {properties_str}")
                        properties = {}
                else:
                    properties = {}
                schema["properties"] = properties

            argument_change = aes.WorkflowArgumentChange.model_validate(
                {
                    "argumentLocation": operation.name,
                    "argumentOperation": operation.type,
                    "argumentDirection": operation.direction,
                    "argumentRequired": operation.required,
                    "argumentSchema": schema,
                    "explanation": operation.explanation,
                },
                strict=False,
            )
            arguments_changes.append(argument_change)
        for operation in response.properties_operations:
            # TODO: This is hackish right now. Should improve once we understand the schemas better.
            current_property_schema = current_schema.get("properties", {}) or {}
            if property_schema := current_property_schema.get(operation.target):
                property_info = {"location": property_schema.get("location", None), "direction": property_schema.get("direction", "input")}
            elif property_schema := current_property_schema.get(operation.target.split(".")[-1]):
                property_info = {"location": property_schema.get("location", None), "direction": property_schema.get("direction", "input")}
            else:
                property_info = {"location": None, "direction": "input"}
            property_change = aes.ActivityPropertyChange.model_validate(
                {
                    "propertyName": operation.target,
                    "propertyOperation": operation.type,
                    "propertyValue": operation.value,
                    "propertyInfo": property_info,
                    "explanation": operation.explanation,
                },
                strict=False,
            )
            properties_changes.append(property_change)
        arguments_change = aes.WorkflowArgumentsChange(data=arguments_changes)
        property_change = aes.ActivityPropertiesChange(activityId=current_activity.id, activityType=current_activity.activity, data=properties_changes)
        return aes.EndpointResponse(message=response.message, argumentsChange=arguments_change, propertiesChange=property_change, usage=usage)


_instance: ApiActivityEditEndpoint | None = None


def get(force_new: bool = False) -> ApiActivityEditEndpoint:
    """Get the endpoint."""
    global _instance
    if force_new or _instance is None:
        _instance = ApiActivityEditEndpoint()
    return _instance
