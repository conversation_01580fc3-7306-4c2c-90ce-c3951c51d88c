from enum import Enum
from typing import Generic, Literal, TypeVar

from langchain_core.messages import BaseMessage
from pydantic import Field

from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE, ApiWorkflowForActivityEdit, SanitizedBaseModel
from services.studio._text_to_workflow.common.schema import Message
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage

################################################################################################################################################################
# Models. The supported models for API Activity Edit.
################################################################################################################################################################

BASIC_MODEL: str = "api_activity_edit_openai_model"
REASONING_MODEL: str = "api_activity_edit_openai_reasoning_model"
MODELS: tuple[str, ...] = (BASIC_MODEL, REASONING_MODEL)

################################################################################################################################################################
# Operations. These are operations that the LLM can perform on various parts of the activity and workflow.
################################################################################################################################################################

PropertyOperationTarget = TypeVar("PropertyOperationTarget", bound=Enum)


class OperationType(str, Enum):
    """Types of operations that can be performed."""

    UPDATE = "update"
    CLEAR = "clear"


class ArgumentOperationDirection(str, Enum):
    """Directions of workflow arguments."""

    INPUT = "input"
    # NOTE: OUTPUT is not supported yet
    # OUTPUT = "output"


class ArgumentOperationPrimitiveType(str, Enum):
    """Types of workflow inputs."""

    STRING = "string"
    NUMBER = "number"
    INTEGER = "integer"
    BOOLEAN = "boolean"


class ArgumentOperationPrimitiveSchema(SanitizedBaseModel):
    """Type of a workflow argument."""

    is_primitive_type: Literal[True] = Field(..., description="Whether the argument is a primitive type", exclude=True)
    type: ArgumentOperationPrimitiveType = Field(..., description="The type of the argument")
    description: str = Field(..., description="The description of the argument")


class ArgumentOperationArraySchema(SanitizedBaseModel):
    """Type of an array workflow argument."""

    is_array_type: Literal[True] = Field(..., description="Whether the argument is an array type", exclude=True)
    type: Literal["array"] = Field(..., description="The type of the argument")
    description: str = Field(..., description="The description of the argument")
    items: "list[ArgumentOperationSchema]" = Field(..., description="The type of the items in the argument if the type is array")


class ArgumentOperationObjectSchema(SanitizedBaseModel):
    """Type of an object workflow argument."""

    is_object_type: Literal[True] = Field(..., description="Whether the argument is an object type", exclude=True)
    type: Literal["object"] = Field(..., description="The type of the argument")
    description: str = Field(..., description="The description of the argument")
    # NOTE: Properties needs to be have a circular reference to WorkflowArgumentSchema, which trips up structured output, so we generate a json string instead.
    # properties: dict[str, WorkflowArgumentSchema] = Field(..., description="The properties of the object.")
    properties: str = Field(..., description="The properties of the object. This is a JSON schema string that can be parsed into a dict.")


ArgumentOperationSchema = ArgumentOperationPrimitiveSchema | ArgumentOperationArraySchema | ArgumentOperationObjectSchema


class ActivityPropertyOperation(SanitizedBaseModel, Generic[PropertyOperationTarget]):
    """Operation to update a field value."""

    explanation: str = Field(..., description="Explanation of why this field operation is being performed")
    type: OperationType = Field(..., description="Type of operation to perform")
    target: PropertyOperationTarget = Field(..., description="The field on which to perform the operation")
    value: str = Field(..., description="Value of the field. Empty string if the operation is CLEAR")


class WorkflowArgumentOperation(SanitizedBaseModel):
    """Operation to update a workflow input."""

    explanation: str = Field(..., description="Explanation of why this workflow argument operation is being performed")
    type: OperationType = Field(..., description="Type of operation to perform")
    name: str = Field(..., description="The name of the argument on which to perform the operation. If the operation is CLEAR, the argument should exist")
    direction: ArgumentOperationDirection = Field(..., description="The direction of the argument on which to perform the operation")
    required: bool = Field(..., description="Whether the argument is required")
    schema_: ArgumentOperationSchema = Field(..., alias="schema", description="The schema of the argument.")


################################################################################################################################################################
# Task I/O. This is the full request and response of the task. The task response is used as the response schema for the LLM call as well.
################################################################################################################################################################


class TaskRequest(SanitizedBaseModel):
    """Request schema for API Activity Edit task."""

    messages: list[Message] = Field(..., description="The chat history. The last message is the user's request edit request")
    activity_id: str = Field(..., description="The key of the selected activity. This is used to identify the selected activity in the workflow.")
    expression_language: API_WF_EXPRESSION_LANGUAGE = Field(..., description="The expression language of the workflow.")
    workflow: ApiWorkflowForActivityEdit = Field(..., description="The parsed workflow.")
    metadata: dict[str, dict] = Field(..., description="The metadata for all activities in the workflow by id.")
    schemas: dict[str, dict] = Field(..., description="The schemas for all activities in the workflow by id.")


class TaskResponse(SanitizedBaseModel, Generic[PropertyOperationTarget]):
    """Response schema for API Activity Edit LLM Call."""

    message: str = Field(..., description="Natural language message to the user about the changes made")
    arguments_operations: list[WorkflowArgumentOperation] = Field(
        ...,
        description="List of distinct operations performed on the workflow arguments",
    )
    properties_operations: list[ActivityPropertyOperation[PropertyOperationTarget]] = Field(
        ...,
        description="List of distinct operations performed on the activity properties",
    )


################################################################################################################################################################
# Endpoint I/O. These are the full request and response schemas for the API Activity Edit endpoint.
################################################################################################################################################################


class ChangeType(str, Enum):
    """Types of changes that can be made."""

    WORKFLOW_ARGUMENTS_CHANGE = "workflowArgumentsChange"
    ACTIVITY_PROPERTIES_CHANGE = "activityPropertiesChange"


class WorkflowArgumentDirection(str, Enum):
    """Directions of workflow inputs."""

    INPUT = "input"
    OUTPUT = "output"


class WorkflowArgumentType(str, Enum):
    """Types of workflow arguments."""

    STRING = "string"
    NUMBER = "number"
    INTEGER = "integer"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"


class WorkflowArgumentSchema(SanitizedBaseModel):
    """Schema of a workflow argument."""

    type: WorkflowArgumentType = Field(description="The type of the argument.")
    description: str = Field(description="The description of the argument.")
    items: "list[WorkflowArgumentSchema] | None" = Field(default=None, description="The type of the items in the argument if the type is array")
    properties: dict | None = Field(default=None, description="The properties of the object if the type is object")


class WorkflowArgumentChange(SanitizedBaseModel):
    """A change made to a workflow argument."""

    argumentLocation: str = Field(description="The location where the change should be made. Right now, this is just the name of the argument.")
    argumentOperation: OperationType = Field(description="The operation that was performed on the argument.")
    argumentDirection: WorkflowArgumentDirection = Field(description="The direction of the argument on which to perform the operation.")
    argumentRequired: bool | None = Field(description="Whether the argument is required. If the operation is CLEAR, this is None.")
    argumentSchema: WorkflowArgumentSchema = Field(description="The schema of the argument.")
    explanation: str = Field(description="Explanation of why this workflow argument change is being made.")


class ActivityPropertyChange(SanitizedBaseModel):
    """A change made to a property of the activity."""

    propertyName: str = Field(description="The name of the property that was changed.")
    propertyOperation: OperationType = Field(description="The operation that was performed on the property.")
    propertyValue: str | None = Field(description="The new value of the property. If the operation is CLEAR, this is None.")
    propertyInfo: dict = Field(description="Any extra information required for the property change.")
    explanation: str = Field(description="Explanation of why this property change is being made.")


class WorkflowArgumentsChange(SanitizedBaseModel):
    """A change made to a workflow argument."""

    type: Literal[ChangeType.WORKFLOW_ARGUMENTS_CHANGE] = Field(default=ChangeType.WORKFLOW_ARGUMENTS_CHANGE, description="The type of change.")
    data: list[WorkflowArgumentChange] = Field(description="The changes made to the input.")


class ActivityPropertiesChange(SanitizedBaseModel):
    """A change made to an activity to update its properties."""

    type: Literal[ChangeType.ACTIVITY_PROPERTIES_CHANGE] = Field(default=ChangeType.ACTIVITY_PROPERTIES_CHANGE, description="The type of change.")
    activityId: str = Field(description="The ID of the activity that was edited. This is the key of the activity in the current workflow.")
    activityType: str = Field(description="The type of the activity that was edited.")
    data: list[ActivityPropertyChange] = Field(description="The changes made to the activity.")


Changes = WorkflowArgumentsChange | ActivityPropertiesChange


class EndpointRequest(SanitizedBaseModel):
    """Request schema for API Activity Edit endpoint."""

    messages: list[Message] = Field(..., description="The chat history. The last message is the user's request edit request")
    activityId: str = Field(..., description="The key of the selected activity. This is used to identify the selected activity in the workflow.")
    expressionLanguage: API_WF_EXPRESSION_LANGUAGE = Field(..., description="The expression language of the workflow.")
    workflow: dict = Field(..., description="The workflow.")
    schemas: dict[str, dict] | None = Field(None, description="The schemas for all activities in the workflow by id.")


class EndpointResponse(SanitizedBaseModel):
    """Response schema for API Activity Edit endpoint."""

    message: str = Field(..., description="Natural language message to the user about the changes made")
    argumentsChange: WorkflowArgumentsChange = Field(..., description="Change made to the workflow arguments.")
    propertiesChange: ActivityPropertiesChange = Field(..., description="Change made to the activity properties.")
    usage: TokenUsage = Field(..., description="The token usage of the LLM call(s).")


class Trace(SanitizedBaseModel):
    """Trace schema for API Activity Edit endpoint."""

    messages: list[BaseMessage] = Field(..., description="The messages exchanged between the user and the LLM.")
    response_model: type[TaskResponse] = Field(..., description="The response model of the LLM.")
    response: TaskResponse = Field(..., description="The response from the LLM.")


################################################################################################################################################################
# Datapoint. This is the schema of a datapoint in Autopilot.Samples
################################################################################################################################################################


class ApiActivityEditInput(SanitizedBaseModel):
    messages: list[Message]
    language: API_WF_EXPRESSION_LANGUAGE
    workflow: ApiWorkflowForActivityEdit
    metadata: dict
    schemas: dict
    activity_id: str


class ApiActivityEditExpected(SanitizedBaseModel):
    message: str
    activity: dict


class ApiActivityEditDataPoint(SanitizedBaseModel):
    input: ApiActivityEditInput
    expected: ApiActivityEditExpected
