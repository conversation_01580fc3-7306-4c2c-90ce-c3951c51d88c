import copy
import datetime
import enum
import json
import pathlib

import langchain.prompts as lp
import langchain_community.callbacks as lcc
import langchain_core.messages as lcm

import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as aes
import services.studio._text_to_workflow.common.api_activity_retriever as ar
import services.studio._text_to_workflow.common.api_workflow.dynamic_activities as da
import services.studio._text_to_workflow.common.api_workflow.schema as api_wf_schema
import services.studio._text_to_workflow.models.model_manager as mm
import services.studio._text_to_workflow.utils.inference.llm_schema as llms
from services.studio._text_to_workflow.common.api_workflow import workflow_parser


class ApiActivityEditTask:
    """Task for editing an activity in the workflow."""

    def __init__(self, model_name: str) -> None:
        """Initialize the task."""

        self.model_name = model_name
        with open(pathlib.Path(__file__).parent / "prompt.system.api_activity_edit.md", "r") as f:
            self.system_message_template = f.read()

        self.activities_retriever = ar.APIActivitiesRetriever()

    async def run(self, request: aes.TaskRequest) -> tuple[aes.TaskResponse, api_wf_schema.BASE_API_ACTIVITIES, dict, llms.TokenUsage, aes.Trace]:
        """Edit the activity."""
        current_activity, current_schema, context = await self._get_current(request)
        response_model = self._make_response_model(current_activity, current_schema)
        messages = self._make_messages(request, current_activity, current_schema, context)

        response, usage = await self._make_llm_call(messages, response_model)
        trace = aes.Trace(messages=messages, response_model=response_model, response=response)
        return response, current_activity, current_schema, usage, trace

    async def _get_current(self, request: aes.TaskRequest) -> tuple[api_wf_schema.BASE_API_ACTIVITIES, dict, dict]:
        """Get the current activity, schema, activities, and schemas."""
        activities = workflow_parser.list_activities(request.workflow)
        activities = {activity.id: activity for activity in activities}
        # Get the current activity
        if request.activity_id not in activities:
            raise ValueError(f"Activity with id {request.activity_id} not found")
        current_activity = activities[request.activity_id].model_copy(deep=True)
        current_activity.model_remove_children()
        # Get the schemas
        schemas = request.schemas or {}
        static_schemas, dynamic_schemas = {}, {}
        missing_schemas = set(activities.keys()) - set(schemas.keys())
        if missing_schemas:
            dynamic_activities = []
            for activity_id in missing_schemas:
                if isinstance(activities[activity_id], api_wf_schema.ConnectorIntegration):
                    dynamic_activities.append(activities[activity_id])
                else:
                    static_schema = activities[activity_id].model_configurable_json_schema()
                    static_schemas[activity_id] = static_schema
            dynamic_schemas = await da.get_dynamic_activity_schemas(dynamic_activities, request.metadata, self.activities_retriever)
            schemas = {**schemas, **static_schemas, **dynamic_schemas}
        if current_activity.id not in schemas:
            raise ValueError(f"Could not find schema for activity with id {current_activity.id}")
        current_schema = copy.deepcopy(schemas[current_activity.id])
        current_schema["properties"] = {k: v for k, v in current_schema["properties"].items() if v.get("direction") == "input"}
        if "definitions" in current_schema:
            del current_schema["definitions"]
        if "$defs" in current_schema:
            del current_schema["$defs"]
        # Get the context outputs
        context = {"definitions": {}, "properties": {}}
        for activity_id, schema in schemas.items():
            context["properties"][activity_id] = {"type": "object", "properties": {"content": {"direction": "output", "type": "object"}}}
            for property in schema.get("properties", {}).values():
                if property.get("direction") == "output":
                    output_property = property
                    context["definitions"].update(schema.get("definitions", schema.get("$defs", {})))
                    context["properties"][activity_id] = {"type": "object", "properties": {"content": output_property}}
        return current_activity, current_schema, context

    def _make_messages(self, request: aes.TaskRequest, activity: api_wf_schema.BaseWorkflowActivity, schema: dict, context: dict) -> list[lcm.BaseMessage]:
        """Make the context for the LLM."""
        workflow = request.workflow.model_dump()
        messages = [{"role": "system", "content": self.system_message_template}] + list(map(dict, request.messages))
        return lp.ChatPromptTemplate(messages).format_messages(
            today=datetime.datetime.now().strftime("%Y-%m-%d"),
            expression_language=request.expression_language.upper(),
            inputs=json.dumps(workflow["input"], indent=2),
            context=json.dumps(context, indent=2),
            schema=json.dumps(schema, indent=2),
            workflow=json.dumps(workflow["root"], indent=2),
            activity=json.dumps(activity.model_dump(), indent=2),
        )

    def _make_response_model(self, activity: api_wf_schema.BaseWorkflowActivity, schema: dict | None) -> type[aes.TaskResponse[enum.Enum]]:
        """Make the response format for the LLM."""
        if isinstance(activity, api_wf_schema.ConnectorIntegration):
            assert schema is not None
            input_properties = []
            for property_name, property_value in schema["properties"].items():
                if property_value["direction"] == "input":
                    input_properties.append(f"with.{property_name}")
            Constraint = enum.Enum("Constraint", {p.upper(): p for p in input_properties})
        else:
            activity_configurable_parameters = type(activity).model_configurable_properties()
            Constraint = enum.Enum("Constraint", {k.upper(): k for k in activity_configurable_parameters})
        return aes.TaskResponse[Constraint]

    async def _make_llm_call(self, messages: list, response_format: type[aes.TaskResponse]) -> tuple[aes.TaskResponse, llms.TokenUsage]:
        """Make a call to the LLM using LangChain."""

        model = mm.ModelManager().get_llm_model(self.model_name, llms.ConsumingFeatureType.API_ACTIVITY_EDIT)
        with lcc.get_openai_callback() as cb:
            # NOTE: Langchain does not implement streaming well. It only yields the final response, so we just get the final response with ainvoke.
            response = await model.with_structured_output(response_format).ainvoke(messages)
            usage = llms.TokenUsage(
                model=model.deployment_name,  # type: ignore
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
            return aes.TaskResponse.model_validate(response), usage


_instances: dict[str, ApiActivityEditTask] = {}


def get(model_name: str, force_new: bool = False) -> ApiActivityEditTask:
    """Get the task."""
    global _instances
    if force_new or model_name not in _instances:
        _instances[model_name] = ApiActivityEditTask(model_name)
    return _instances[model_name]
