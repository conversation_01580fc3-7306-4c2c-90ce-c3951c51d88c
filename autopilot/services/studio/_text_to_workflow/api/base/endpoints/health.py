from typing import Any, Callable, Coroutine, List, Tuple

from fastapi import APIRouter
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

router = APIRouter()

HEALTH_TESTS: List[Callable[[Any], Any]] = []


async def _get_health(
    health_tests: List[Callable[..., Coroutine[None, None, Tuple[bool, str]]]],
    verbose: bool = False,
) -> JSONResponse:
    """
    Get the health status of the service.

    Args:
        health_tests (list[Callable[[], Coroutine[None, None, Tuple[bool, str]]]]): A list of functions that test the health of the service.
        verbose (bool, optional): Whether to include detailed information in the response. Defaults to False.

    Returns:
        JSONResponse: A JSON response containing the health status of the service.

    """
    status_code = 200
    status = "Service is ready."
    details = ""
    for test in health_tests:
        healthy, detail = await test()
        if verbose:
            details += f"{detail}\n"

        if not healthy:
            status_code = 500
            status = "Service is not ready."
            break

    response = {"status": status}
    if details:
        response["details"] = details
    return JSONResponse(jsonable_encoder(response), status_code=status_code)


@router.get("/alive")
def get_alive() -> JSONResponse:
    """
    Check if the service is alive.

    Returns:
        JSONResponse: A JSON response indicating whether the service is alive.

    """
    response = {"status": "Service is alive."}
    return JSONResponse(jsonable_encoder(response), status_code=200)


@router.get("/health")
async def get_health() -> JSONResponse:
    """
    Get the health status of the service.

    Returns:
        JSONResponse: A JSON response containing the health status of the service.

    """
    return await _get_health(HEALTH_TESTS)


@router.get("/health-details")
async def get_health_details() -> JSONResponse:
    """
    Get detailed health information about the service.

    Returns:
        JSONResponse: A JSON response containing detailed health information about the service.

    """
    return await _get_health(HEALTH_TESTS, verbose=True)
