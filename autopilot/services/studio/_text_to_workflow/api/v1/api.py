from fastapi import APIRouter

from services.studio._text_to_workflow.api.v1.endpoints import (
    activity_config,
    activity_summary,
    code_generation,
    expression_generation,
    testdata_generation,
    ui_automation,
    workflow_generation,
)

api_router = APIRouter()
api_router.include_router(activity_summary.router, tags=["activity_summary"])
api_router.include_router(code_generation.router, tags=["code_generation"])
api_router.include_router(expression_generation.router, tags=["expression_generation"])
api_router.include_router(workflow_generation.router, tags=["workflow_generation"])
api_router.include_router(ui_automation.router, tags=["ui_automation"])
api_router.include_router(activity_config.router, tags=["activity_config"])
api_router.include_router(testdata_generation.router, tags=["testdata_generation"])
