from fastapi import APIRouter, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.code_generation import code_generation_endpoint
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_utils

router = APIRouter()


@router.post("/code-generation", include_in_schema=False)
async def code_generation(
    request: Request,
) -> JSONResponse:
    request_body = request_utils.convert_snake_case_keys_to_camel_case(await request.json())

    request_body["targetFramework"] = request_body.get("targetFramework", "Windows")
    response = await code_generation_endpoint.generate(request_body)

    if settings.IS_PROD:
        del response["codeUsage"]
        del response["planningUsage"]
    else:
        response["code_usage"] = request_utils.convert_camel_case_keys_to_snake_case(response.pop("codeUsage"))
        response["planning_usage"] = request_utils.convert_camel_case_keys_to_snake_case(response.pop("planningUsage"))

    return JSONResponse(jsonable_encoder(response), status_code=200)
