from fastapi import APIRouter, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowGenerationResponse

router = APIRouter()


@router.post("/api-automation", include_in_schema=False)
async def generate_workflow(request: Request) -> WorkflowGenerationResponse:
    request_body = request_utils.convert_snake_case_keys_to_camel_case(await request.json())
    # for backwards compatibility - some clients might not send connections or target framework
    request_body["connections"] = request_body.get("connections", [])
    request_body["targetFramework"] = request_body.get("targetFramework", "Portable")

    response = await workflow_generation_endpoint.generate_workflow(request_body)

    if settings.IS_PROD:
        del response["usage"]
    else:
        response["usage"]["generation"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"]["generation"])
        response["usage"]["retrieval"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"]["retrieval"])

    return JSONResponse(jsonable_encoder(response))


@router.post("/generate-sequence", include_in_schema=False)
async def generate_sequence(request: Request) -> WorkflowGenerationResponse:
    request_body = await request.json()
    request_body = request_utils.convert_snake_case_keys_to_camel_case(request_body)
    # for backwards compatibility - some clients might not send availableVariables, availableAdditionalTypeDefinitions, connections, objects
    request_body["availableVariables"] = request_body.get("availableVariables", [])
    request_body["availableAdditionalTypeDefinitions"] = request_body.get("availableAdditionalTypeDefinitions", "")
    request_body["connections"] = request_body.get("connections", []) or []
    request_body["objects"] = request_body.get("objects", [])

    response = await workflow_generation_endpoint.generate_sequence(request_body)

    if settings.IS_PROD:
        del response["usage"]
    else:
        response["usage"]["generation"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"]["generation"])
        response["usage"]["retrieval"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"]["retrieval"])

    return JSONResponse(jsonable_encoder(response))
