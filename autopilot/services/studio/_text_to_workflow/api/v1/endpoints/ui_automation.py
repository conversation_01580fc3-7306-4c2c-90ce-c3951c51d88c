from fastapi import APIRouter, Request, Response
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.ui_automation import ui_automation_endpoint

router = APIRouter()


@router.post("/ui-automation", include_in_schema=False)
async def ui_automation(
    request: Request,
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.generate(request_body)
    return JSONResponse(content=response, status_code=200)
