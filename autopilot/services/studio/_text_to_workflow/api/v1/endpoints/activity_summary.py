from fastapi import APIRouter, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryResponse
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_utils

router = APIRouter()


@router.post("/activity-summary", include_in_schema=False)
async def activity_summary(
    request: Request,
) -> ActivitySummaryResponse:
    request_body = await request.json()
    request_body = request_utils.convert_snake_case_keys_to_camel_case(request_body)

    context = request_utils.get_request_context()

    response = await activity_summary_endpoint.summarize(
        request_body,
        localization=context.localization,
        identifier=context.request_id,
    )

    if settings.IS_PROD:
        del response["usage"]
    else:
        response["usage"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"])

    return JSONResponse(content=jsonable_encoder(response), status_code=200)
