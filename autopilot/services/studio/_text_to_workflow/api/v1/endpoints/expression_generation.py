from fastapi import APIRouter, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.utils import request_utils

router = APIRouter()


@router.post("/expression-generation", include_in_schema=False)
async def expression_generation(
    request: Request,
) -> JSONResponse:
    request_body = await request.json()
    # for backwards compatibility - some clients might not send currentExpression
    if "currentExpression" not in request_body:
        request_body["currentExpression"] = None

    response = await expression_generation_endpoint.generate(request_body)

    if settings.IS_PROD:
        del response["usage"]
    else:
        response["usage"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"])

    return JSONResponse(jsonable_encoder(response), status_code=200)


@router.post("/fix-expression", include_in_schema=False)
async def fix_generation(
    request: Request,
) -> JSONResponse:
    request_body = await request.json()
    request_body = request_utils.convert_snake_case_keys_to_camel_case(request_body)
    response = await expression_generation_endpoint.fix_expression(request_body)

    if settings.IS_PROD:
        del response["usage"]
    else:
        response["usage"] = request_utils.convert_camel_case_keys_to_snake_case(response["usage"])

    return JSONResponse(jsonable_encoder(response), status_code=200)
