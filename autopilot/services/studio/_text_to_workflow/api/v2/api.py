from fastapi import APIRouter

from services.studio._text_to_workflow.api.v2.endpoints import (
    activity_config,
    activity_summary,
    api_activity_edit,
    api_workflow,
    bpmn_generation,
    code_generation,
    expression_generation,
    predict_expression,
    semantic_diff,
    testdata_generation,
    ui_automation,
    workflow_fix,
    workflow_generation,
)
from services.studio._text_to_workflow.api.v2.endpoints.assistants import workflow_assistant

api_router = APIRouter()
api_router.include_router(activity_summary.router, tags=["summarize_workflow"])
api_router.include_router(activity_config.router, tags=["activity_config"])
api_router.include_router(code_generation.router, tags=["code_generation"])
api_router.include_router(expression_generation.router, tags=["expression_generation"])
api_router.include_router(predict_expression.router, tags=["predict_expression"])
api_router.include_router(workflow_generation.router, tags=["workflow_generation"])
api_router.include_router(ui_automation.router, tags=["ui_automation"])
api_router.include_router(workflow_fix.router, tags=["workflow_fix"])
api_router.include_router(testdata_generation.router, tags=["testdata_generation"])
api_router.include_router(semantic_diff.router, tags=["semantic_diff"])
api_router.include_router(bpmn_generation.router, tags=["bpmn_generation"])
api_router.include_router(api_workflow.router, tags=["api_workflow"])
api_router.include_router(api_activity_edit.router, tags=["api_activity_edit"])
api_router.include_router(workflow_assistant.router, tags=["workflow_assistant"])
