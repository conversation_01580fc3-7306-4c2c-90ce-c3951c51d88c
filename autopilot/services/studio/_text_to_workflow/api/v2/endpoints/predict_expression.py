from fastapi import APIRouter, Response
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.predict_expression import predict_expression_endpoint
from services.studio._text_to_workflow.predict_expression.predict_expression_schema import (
    PredictExpressionRequest,
    PredictExpressionResponse,
)

router = APIRouter()


@router.post("/predict-expression", response_model=PredictExpressionResponse)
async def predict_expression(request: PredictExpressionRequest) -> Response:
    response = await predict_expression_endpoint.generate(request)
    if response is None:
        return Response("", status_code=204)

    if settings.IS_PROD:
        for key in ["usage", "elapsedTimes", "demos", "parameterInferedType"]:
            if key in response:
                del response[key]

    return JSONResponse(content=jsonable_encoder(response), status_code=200)
