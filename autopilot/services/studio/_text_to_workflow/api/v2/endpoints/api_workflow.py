from fastapi import APIRouter

from services.studio._text_to_workflow.api_workflow import api_workflow_endpoint
from services.studio._text_to_workflow.api_workflow.api_workflow_request_schema import APIWorkflowRequest, APIWorkflowResponse

router = APIRouter()


@router.post("/generate-api-workflow", response_model=APIWorkflowResponse)
async def v2_generate_api_workflow(
    request: APIWorkflowRequest,
) -> APIWorkflowResponse:
    response = await api_workflow_endpoint.generate_workflow(request)

    return response
