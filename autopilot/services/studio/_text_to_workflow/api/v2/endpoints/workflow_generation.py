from fastapi import APIRouter

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    GenerateSequenceRequestPydantic,
    GenerateWorkflowRequestPydantic,
    WorkflowGenerationResponse,
)

router = APIRouter()


@router.post("/generate-workflow", response_model=WorkflowGenerationResponse)
async def v2_generate_workflow(
    request: GenerateWorkflowRequestPydantic,
) -> WorkflowGenerationResponse:
    response = await workflow_generation_endpoint.generate_workflow(request)

    if settings.IS_PROD:
        del response["usage"]

    return response


@router.post("/generate-sequence", response_model=WorkflowGenerationResponse)
async def v2_generate_sequence(
    request: GenerateSequenceRequestPydantic,
) -> WorkflowGenerationResponse:
    response = await workflow_generation_endpoint.generate_sequence(request)

    if settings.IS_PROD:
        del response["usage"]

    return response
