from fastapi import APIRouter, Request

from services.studio._text_to_workflow.assistants.workflow_assistant.api_workflow_assistant_endpoint import APIWorkflowAssistantEndpoint
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_endpoint import WorkflowAssistantEndpoint
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    APIWorkflowAssistantRequest,
    RPAWorkflowAssistantRequest,
)
from services.studio._text_to_workflow.utils.sse_helper import SSEHelper

router = APIRouter()
wf_assistant_endpoint = WorkflowAssistantEndpoint()
api_wf_assistant_endpoint = APIWorkflowAssistantEndpoint()


@router.post("/assistants/workflow")
async def assistants_workflow(request: Request):
    helper = SSEHelper[RPAWorkflowAssistantRequest](wf_assistant_endpoint.perform_assistant_request, RPAWorkflowAssistantRequest)
    return await helper.run(request)


@router.post("/assistants/api-workflow")
async def assistants_api_workflow(request: Request):
    helper = <PERSON><PERSON>Helper[APIWorkflowAssistantRequest](api_wf_assistant_endpoint.perform_assistant_request, APIWorkflowAssistantRequest)
    return await helper.run(request)
