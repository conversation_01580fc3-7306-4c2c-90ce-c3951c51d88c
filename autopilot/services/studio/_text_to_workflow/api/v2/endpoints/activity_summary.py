from fastapi import APIRouter

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint, api_workflow_activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest, ActivitySummaryResponse
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_utils

router = APIRouter()


@router.post("/summarize-workflow", response_model=ActivitySummaryResponse)
async def v2_summarize_workflow(
    request: ActivitySummaryRequest,
) -> ActivitySummaryResponse:
    context = request_utils.get_request_context()
    response = await activity_summary_endpoint.summarize(request, localization=context.localization)

    if settings.IS_PROD:
        del response["usage"]

    return response


@router.post("/api-workflow/summarize-workflow", response_model=ActivitySummaryResponse)
async def v2_summarize_api_workflow(
    request: ActivitySummaryRequest,
) -> ActivitySummaryResponse:
    context = request_utils.get_request_context()
    response = await api_workflow_activity_summary_endpoint.summarize(request, localization=getattr(context, "localization", None))

    if settings.IS_PROD:
        del response["usage"]

    return response
