from fastapi import APIRouter

import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_endpoint as aae
import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as aes

router = APIRouter()


@router.post("/api-activity-edit", response_model=aes.EndpointResponse)
async def v2_edit_activity(request: aes.EndpointRequest) -> aes.EndpointResponse:
    endpoint = aae.get()
    response, _ = await endpoint.edit(request)
    return response
