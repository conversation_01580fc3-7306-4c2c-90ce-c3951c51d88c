from fastapi import APIRouter, Query, Request, status

from services.studio._text_to_workflow.bpmn_generation import bpmn_generation_endpoint
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnAssistantRequest,
    BpmnAssistantResponse,
    BpmnGenerationChatResponse,
    BpmnGenerationResponse,
    BpmnTelemetryRequest,
    GenerateBpmnChatRequest,
    GenerateBpmnRequest,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAssistantRequest
from services.studio._text_to_workflow.utils.sse_helper import SSEHelper

router = APIRouter()


@router.post("/generate-bpmn", response_model=BpmnGenerationResponse)
async def generate_bpmn(
    request: GenerateBpmnRequest,
) -> BpmnGenerationResponse:
    response = await bpmn_generation_endpoint.generate_bpmn(request)
    return response


@router.post("/generate-bpmn/chat", response_model=BpmnGenerationChatResponse)
async def generate_bpmn_chat(
    request: GenerateBpmnChatRequest,
    validate: bool = Query(False),
) -> BpmnGenerationChatResponse:
    response = await bpmn_generation_endpoint.generate_bpmn_chat(request, validate)
    return response


@router.post("/assistants/bpmn", response_model=BpmnAssistantResponse)
async def generate_bpmn_assistant(
    request: BpmnAssistantRequest,
    validate: bool = Query(False),
) -> BpmnAssistantResponse:
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request, validate)
    return response


@router.post("/generate-bpmn/telemetry", status_code=status.HTTP_204_NO_CONTENT)
async def generate_bpmn_telemetry(
    request: BpmnTelemetryRequest,
):
    await bpmn_generation_endpoint.generate_bpmn_business_telemetry(request)


@router.post("/assistant/bussiness-process")
async def business_process_assistant(
    request: Request,
):
    helper = SSEHelper[BusinessProcessAssistantRequest](bpmn_generation_endpoint.business_process_assistant, BusinessProcessAssistantRequest)
    return await helper.run(request)
