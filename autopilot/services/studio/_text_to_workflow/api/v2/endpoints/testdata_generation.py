from fastapi import APIRouter

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.testdata_generation import testdata_generation_endpoint
from services.studio._text_to_workflow.testdata_generation.testdata_generation_schema import TestDataGenerationRequest, TestDataGenerationResponse

router = APIRouter()


@router.post("/generate-testdata", response_model=TestDataGenerationResponse)
async def testdata_generation(
    request: TestDataGenerationRequest,
) -> TestDataGenerationResponse:
    response = await testdata_generation_endpoint.generate(request)

    if settings.IS_PROD:
        del response["usage"]

    return response
