from fastapi import APIRouter, Response
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.activity_config import activity_config_endpoint
from services.studio._text_to_workflow.activity_config.activity_config_schema import (
    ActivityConfigRequest,
    ActivityConfigResponse,
    ActivityConfigWithQueryRequest,
)
from services.studio._text_to_workflow.core import settings

router = APIRouter()


@router.post("/configure-activity", response_model=ActivityConfigResponse)
async def v2_configure_activity(request: ActivityConfigRequest) -> Response:
    # No longer supported, to be removed in the future.
    # This feature was only enabled in Community Edition.
    return Response("", status_code=204)


@router.post("/core-activity-edit", response_model=ActivityConfigResponse)
async def v2_configure_activity_with_query(request: ActivityConfigWithQueryRequest) -> Response:
    response = await activity_config_endpoint.generate(request)
    if response is None:
        return Response("", status_code=204)

    if settings.IS_PROD:
        del response["usage"]

    return JSONResponse(content=jsonable_encoder(response), status_code=200)
