from fastapi import APIRouter

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    ExpressionGenerationFixRequest,
    ExpressionGenerationRequest,
    ExpressionGenerationResponse,
)

router = APIRouter()


@router.post("/generate-expression", response_model=ExpressionGenerationResponse)
async def expression_generation(request: ExpressionGenerationRequest) -> ExpressionGenerationResponse:
    response = await expression_generation_endpoint.generate(request)

    if settings.IS_PROD:
        del response["usage"]

    return response


@router.post("/fix-expression", response_model=ExpressionGenerationResponse)
async def fix_expression(request: ExpressionGenerationFixRequest) -> ExpressionGenerationResponse:
    response = await expression_generation_endpoint.fix_expression(request)

    if settings.IS_PROD:
        del response["usage"]

    return response
