from fastapi import <PERSON><PERSON><PERSON><PERSON>, Header, Request, Response
from fastapi.responses import JSONResponse

from services.studio._text_to_workflow.ui_automation import ui_automation_endpoint
from services.studio._text_to_workflow.ui_automation.models.ui_agent.advice_model import AdviceModel

router = APIRouter()


@router.post("/generate-screen-actions")
async def v2_generate_screen_actions(
    request: Request,
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.generate(request_body)
    return JSONResponse(content=response, status_code=200)


@router.post("/agent-act-on-screen")
async def v2_agent_act_on_screen(
    request: Request,
) -> Response:
    request_body = await request.json()
    response, predict_info = await ui_automation_endpoint.agent_act_on_screen(request_body)
    headers = {"x-uipath-uiagent-model": predict_info["llm_model_name"]}
    return JSONResponse(content=response, status_code=200, headers=headers)


@router.post("/agent-screen-extract")
async def v2_agent_extract(
    request: Request,
) -> Response:
    request_body = await request.json()
    response, predict_info = await ui_automation_endpoint.extract(request_body)
    headers = {"x-uipath-uiagent-model": predict_info["llm_model_name"]}
    return JSONResponse(content=response, status_code=200, headers=headers)


@router.post("/qa-screen")
async def v2_qa_on_screen(
    request: Request,
    x_uipath_semantic_duplicates_matching: bool = Header(default=False, description="Enable semantic duplicates matching for QA screen requests."),
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.qa_screen(request_body, {"x-uipath-semantic-duplicates-matching": x_uipath_semantic_duplicates_matching})
    return JSONResponse(content=response, status_code=200)


@router.post("/qa-dom")
async def v2_qa_on_dom(
    request: Request,
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.qa_dom(request_body)
    return JSONResponse(content=response, status_code=200)


@router.post("/get-element-description")
async def v2_get_element_description(
    request: Request,
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.get_element_description(request_body)
    return JSONResponse(content=response, status_code=200)


@router.post("/close-popup")
async def v2_close_popup(
    request: Request,
) -> Response:
    request_body = await request.json()
    response = await ui_automation_endpoint.close_popup(request_body)
    return JSONResponse(content=response, status_code=200)


@router.post("/learn-from-experience")
async def v2_learn_from_experience(
    request: Request,
) -> Response:
    request_body = await request.json()
    model = AdviceModel()
    advice = await model.predict(trace_data=request_body)
    return JSONResponse(content={"advice": advice}, status_code=200)
