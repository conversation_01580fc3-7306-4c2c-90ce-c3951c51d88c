from fastapi import APIRouter

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.workflow_fix import workflow_fix_endpoint
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixRequest, WorkflowFixResponse

router = APIRouter()


@router.post("/fix-workflow", response_model=WorkflowFixResponse)
async def v2_fix_workflow(
    request: WorkflowFixRequest,
) -> WorkflowFixResponse:
    response = await workflow_fix_endpoint.fix_workflow(request)

    if settings.IS_PROD:
        del response["usage"]

    return response
