from fastapi import APIRouter

from services.studio._text_to_workflow.code_generation import code_generation_endpoint
from services.studio._text_to_workflow.code_generation.code_generation_schema import (
    CodeGenerationRequestPydantic,
    CodeGenerationResponse,
    FixCodeRequestPydantic,
    FixCodeResponse,
)
from services.studio._text_to_workflow.core import settings

router = APIRouter()


@router.post("/generate-code", response_model=CodeGenerationResponse)
async def generate_code(
    request: CodeGenerationRequestPydantic,
) -> CodeGenerationResponse:
    response = await code_generation_endpoint.generate(request)

    if settings.IS_PROD:
        del response["codeUsage"]
        del response["planningUsage"]

    return response


@router.post("/fix-code", response_model=FixCodeResponse)
async def fix_code(
    request: FixCodeRequestPydantic,
) -> FixCodeResponse:
    response = await code_generation_endpoint.generate_fix_code(request)

    if settings.IS_PROD:
        del response["usage"]

    return response
