embedding_model:
  model_type: sentence-embedding
  deployment_name: all-mpnet-base-v2
retriever:
  demonstrations: 3
  ted_sample_size: 30
model:
  # TODO: we should experiment with temperature, top_p, frequency_penalty, presence_penalty for the summarization
  deployment_name: gpt-4o-mini-2024-07-18
  temperature: 0.0
  max_tokens: 1000
  request_timeout: 60
  top_p: 0.0
  frequency_penalty: 0.0
  presence_penalty: 0.0
prompt:
  # TODO: The system message could actually be a template in the properties part based on the exact activities in the workflow
  system_msg: |-
    You are a UiPath Studio AI assistant.
    The user will provide a UiPath automation workflow, represented as a tree of activities in YAML format.
    The activities have type names, activity display names and their parameters. Multiple activities can be nested inside another activity, forming a tree.
    There are four types of workflows:
    - Workflows which include a trigger, which means that the workflow will be started when a certain event occurs
    - Workflows which do not have a trigger and have arguments instead.
    - Workflows which have neither a trigger nor arguments.
    - Single activity workflows, which have a single instance of `activity` within the tree, meaning that we must summarize only that activity.
    {format_instructions}
  user_msg_template: |-
    {activity}
  assistant_msg_template: |-
    {description}
  response_size_instructions:
    activity-description: "Try to use at most 6 words."
    activity-summary: "Try to use at most 12 words."
  
