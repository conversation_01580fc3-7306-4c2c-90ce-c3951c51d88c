import pathlib
from enum import Enum
from typing import Any

import numpy as np
import typing_extensions as t

from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE
from services.studio._text_to_workflow.common.schema import Variable
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage, TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse


class ActivitySummarySourceType(str, Enum):
    Workflow = "Workflow"
    ApiWorkflow = "ApiWorkflow"


class ActivityDescription(t.TypedDict):
    id: str
    description: str


class ActivitySummary(t.TypedDict):
    id: str
    summary: str


class ActivitySummaryRequest(BaseRequest):
    activity: str
    language: t.NotRequired[API_WF_EXPRESSION_LANGUAGE | None]


class ActivitySummaryResult(t.TypedDict):
    activityDescriptions: list[ActivityDescription]
    activitySummaries: list[ActivitySummary]
    workflowSummary: str
    workflowFileName: str
    workflowShortDescription: str
    originalWorkflow: t.NotRequired[dict[str, Any] | None]


class ActivitySummaryTaskResult(t.TypedDict):
    result: ActivitySummaryResult
    usage: TokenUsage


class ActivitySummaryResponse(BaseResponse):
    result: ActivitySummaryResult
    usage: t.NotRequired[TokenUsageJson]


class ActivitySummaryRetrieverState(t.TypedDict):
    paths: list[pathlib.Path]
    documents: list
    embeddings: np.ndarray
    keys: list[set[str]]


class _ActivitySummaryDemoInput(t.TypedDict):
    description: str
    workflow: dict
    variables: list[Variable]


class _ActivitySummaryDemoOutput(t.TypedDict):
    activityDescriptions: list[ActivityDescription]
    activitySummaries: list[ActivitySummary]
    workflowSummary: str
    workflowFileName: str


class ActivitySummaryDemo(t.TypedDict):
    input: _ActivitySummaryDemoInput
    output: _ActivitySummaryDemoOutput
