# Activity/Workflow Summarization

Summarizes an activity tree and returns the summarized activities.

## Use cases

- summarize/explain workflow/summarize activity
- improve activity display names

## TODO

- implement request and request chunking
- support workflow summary only in request. This will save up on output tokens but produce worse summarization.
- support other locales (e.g. JP)
- LM-YAML: UIA may have to do special serialization for object targets (?)

## Notes

If workflow too large, find cutoff point as close to max token (16k) * 2/3 as possible.
Create sub-workflow from that part, find all used variables in that part, include them. include all used args as variables too. Summarize  the sub-workflow.

In the main workflow, replace sub-workflow activities with Sequence having displayName of summarization. Rinse and repeat if too large.

Max Tokens is variable up to max input size. 2/3 tokens should be input, 1/3 should be output.
