import argparse
import asyncio
import pathlib

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint, activity_summary_evaluation, api_workflow_activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_dataset import create_workflow_summarization_datasets
from services.studio._text_to_workflow.common.state_store import clear_retrievers_cache
from services.studio._text_to_workflow.utils import paths

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate the activity summary model.")
    parser.add_argument("job", choices=["test", "build", "bt", "dataset"], help="Job to run.")
    args, _unknown = parser.parse_known_args()

    if args.job == "dataset":
        create_workflow_summarization_datasets()

    if args.job in {"bt", "build"}:
        clear_retrievers_cache()
        activity_summary_endpoint.init()
        api_workflow_activity_summary_endpoint.init()
    if args.job in {"bt", "test"}:
        parser.add_argument(
            "--dataset-path",
            "--dataset_path",
            type=pathlib.Path,
            default=None,
            help="Path to the dataset to evaluate.",
        )
        parser.add_argument(
            "--target-framework",
            "--target_framework",
            "--framework",
            type=str,
            default="Portable",
            choices=["Portable", "Windows", "Api"],
            help="Target framework to generate for.",
        )
        parser.add_argument(
            "--subset",
            type=str,
            default="test",
            choices=["train", "test", "static", "Issues"],
            help="Subset of the dataset to process. If not provided, both train and test subsets will be processed.",
        )

        parser.add_argument(
            "--language",
            type=str,
            default="js",
            choices=["jq", "js"],
            help="Language to generate for.",
        )
        args = parser.parse_args()

        if args.dataset_path is None:
            args.dataset_path = paths.get_workflow_summarization_dataset_path(args.target_framework, args.subset)

        asyncio.run(activity_summary_evaluation.evaluate(args.dataset_path, args.target_framework, args.language))
