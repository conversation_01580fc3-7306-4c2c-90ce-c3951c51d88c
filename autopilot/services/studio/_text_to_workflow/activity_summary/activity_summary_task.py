import copy
import json
import pathlib
from typing import Any, Dict, List, Protocol, cast

import langchain.cache
import langchain.chains
import langchain.chains.llm
import langchain.embeddings
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
import langchain_core
import langchain_core.language_models
from langchain.schema.messages import AIMessage, BaseMessage, HumanMessage
from pydantic import TypeAdapter, ValidationError

from services.studio._text_to_workflow.activity_summary.activity_summary_helpers import get_unique_activity_names
from services.studio._text_to_workflow.activity_summary.activity_summary_retriever import ActivitySummaryRetriever
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest, ActivitySummaryResult, ActivitySummaryTaskResult
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE, ApiWorkflow
from services.studio._text_to_workflow.common.api_workflow.walkers import ApiWorkflowActivityIdReplace
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import (
    ApiWorkflowParser,
    get_workflow_activity_types,
    list_activities,
)
from services.studio._text_to_workflow.common.schema import TargetFramework, WorkflowDict
from services.studio._text_to_workflow.common.walkers import CollapseWorkflowSubsequencesPruner, DAPClassNameTranslator
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.common.workflow_parser import remove_fields
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import errors, paths
from services.studio._text_to_workflow.utils.errors import BadRequestError, UnprocessableEntityError
from services.studio._text_to_workflow.utils.inference.llm_gateway_model import LLMGatewayModel
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask
from services.studio._text_to_workflow.utils.workflow_utils import replace_blobs_with_hash
from services.studio._text_to_workflow.utils.yaml_utils import escape_quotes, yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.api_workflow_generation_retrievers import APIWorkflowDemonstrationsRetriever

LOGGER = AppInsightsLogger()
TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")

NUMBER_OF_TOKENS_INACCURACY_TOLERANCE = 32
TOKENS_TOLERANCE = 2000
summary_model = ModelManager().get_llm_model("activity_summary_model", ConsumingFeatureType.WORKFLOW_SUMMARIZATION)


class RetrieverProtocol(Protocol):
    def get_relevant(self, *args: Any, **kwargs: Any) -> Any: ...


class BaseActivitySummaryTask:
    def __init__(self, config_name: str) -> None:
        self.config_path = (pathlib.Path(__file__).parent / config_name).absolute()
        self.dataset_path = paths.get_workflow_summarization_dataset_path("Portable")
        self.config = yaml_load(self.config_path)
        self.activity_summary_result_validator = TypeAdapter(ActivitySummaryResult)
        self.retriever: RetrieverProtocol
        self.format_config = paths.get_activity_summary_format_config_path()

    def _prepare_demonstration(self, demonstrations, summary_model, activity_ids):
        demonstration_messages: List[Any] = []
        for demonstration in demonstrations[::-1]:
            # generation should not fail if demonstration generation fails
            try:
                generation_demonstration = self._build_demonstration_messages(
                    demonstration,
                    self.config["prompt"]["user_msg_template"],
                    self.config["prompt"]["assistant_msg_template"],
                    activity_ids,
                )
                demonstration_messages[:0] = generation_demonstration
            except Exception as e:
                LOGGER.exception(f"Demonstration generation error: {e}")

            text = langchain.prompts.ChatPromptTemplate.from_messages(demonstration_messages).format()
            num_tokens = summary_model.get_num_tokens(text)

            # if prompt gets longer than 4000 tokens, stop adding demonstrations
            if num_tokens > self._get_prompt_tokens_limit(summary_model) - TOKENS_TOLERANCE:
                # if the prompt is too long, remove the last demonstration
                if num_tokens > self._get_prompt_tokens_limit(summary_model) - TOKENS_TOLERANCE:
                    # each demonstration has 2 messages now (this is why we pop twice)
                    demonstration_messages.pop()
                    demonstration_messages.pop()
                # this warning might come too often
                LOGGER.warning(f"Demonstrations too long: {num_tokens}. using only top {len(demonstration_messages) // 2} demonstration(s)")
                break
        return demonstration_messages

    def _build_demonstration_messages(
        self,
        data: Dict[str, Dict[Any, Any]],
        user_msg_template: str,
        ai_msg_template: str,
        activity_ids: list[str] | None = None,
    ) -> list[BaseMessage]:
        demonstration_messages: list[BaseMessage] = []
        user_message = user_msg_template.format(activity=yaml_dump(data["input"]), activity_ids=yaml_dump(activity_ids))
        assistant_message = ai_msg_template.format(description=yaml_dump(data["output"]))

        demonstration_messages.append(HumanMessage(content=user_message))
        demonstration_messages.append(AIMessage(content=assistant_message))

        return demonstration_messages

    def _get_prompt_tokens_limit(self, summary_model: langchain_core.language_models.BaseChatModel) -> int:  # separate method to be able to mock it in a test
        return summary_model.max_total_tokens - summary_model.max_model_tokens - NUMBER_OF_TOKENS_INACCURACY_TOLERANCE

    def _ensure_prompt_fits_limit(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        prompt_template: langchain.prompts.ChatPromptTemplate,
        workflow: WorkflowDict,
        activity_ids: list[str] | None = None,
    ) -> WorkflowDict | None:
        """Returns the pruned workflow or None if it cannot fit or be pruned."""
        tokens_limit = self._get_prompt_tokens_limit(summary_model)

        if "activity_ids" in prompt_template.input_variables:
            num_tokens = summary_model.get_num_tokens(prompt_template.format(activity=yaml_dump(workflow), activity_ids=yaml_dump(activity_ids)))
        else:
            num_tokens = summary_model.get_num_tokens(prompt_template.format(activity=yaml_dump(workflow)))
        if num_tokens < tokens_limit:
            return workflow
        LOGGER.warning(f"Prompt too long: {num_tokens}. Limit: {tokens_limit}.")

        try:
            workflow_object = Workflow("", "", workflow)
        except Exception as e:
            LOGGER.exception(f"Failed to create workflow object. Error: {e}")
            return

        for _pruning_message in CollapseWorkflowSubsequencesPruner(collapse_along_deepest_activity=True).iteratively_prune_workflow(workflow_object):
            num_tokens = summary_model.get_num_tokens(prompt_template.format(activity=workflow_object.lmyaml()))
            if num_tokens < tokens_limit:
                return workflow_object.to_dict()

        LOGGER.warning("Cannot prune workflow to fit prompt.")
        return

    def validate_model_result(self, result: str) -> str:
        if result.startswith("```yaml"):
            result = result[len("```yaml") :]
        if result.endswith("```"):
            result = result[: -len("```")]
        return result

    async def generate_summary_result(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        chat_chain: langchain.chains.LLMChain,
        inputs: dict[str, Any],
    ) -> tuple[ActivitySummaryResult, TokenUsage]:
        """Generate the summary result using the chat chain.

        Args:
            summary_model: The LLM model used for summarization
            chat_chain: The chat chain to use for generation
            inputs: The inputs to pass to the chat chain

        Returns:
            Tuple containing:
            - The parsed activity summary result
            - Token usage information
        """
        for _ in range(3):
            with langchain_community.callbacks.get_openai_callback() as cb:
                result = str((await chat_chain.ainvoke(inputs)).content)
                result = self.validate_model_result(result)
                usage = TokenUsage(
                    model=cast(LLMGatewayModel, summary_model).deployment_name or "",
                    prompt_tokens=cb.prompt_tokens,
                    completion_tokens=cb.completion_tokens,
                    total_tokens=cb.total_tokens,
                )

            # ensure that the initial result is a valid yaml
            try:
                parsed_result: ActivitySummaryResult = cast(ActivitySummaryResult, yaml_load(result))
            except Exception as e:
                LOGGER.warning(f"Failed to parse result as YAML: {e}")
                continue

            if self._validate_result(parsed_result):
                return parsed_result, usage

        # if we never performed a break it means that we don't have a valid result
        raise UnprocessableEntityError("Could not generate a valid activity summary.")

    def _validate_result(self, result: ActivitySummaryResult) -> bool:
        try:
            self.activity_summary_result_validator.validate_python(result)
            return True
        except ValidationError:
            LOGGER.warning(f"Invalid result from the model, found keys: {', '.join(result.keys())}")
            return False

    async def _process_summary_result(
        self,
        parsed_result: ActivitySummaryResult,
        usage: TokenUsage,
        localization: str | None,
        feature_type: ConsumingFeatureType,
    ) -> ActivitySummaryTaskResult:
        """Process the summary result by handling translations and escaping descriptions.

        Args:
            parsed_result: The parsed activity summary result
            usage: Token usage information
            localization: Optional localization string
            feature_type: The feature type for translation

        Returns:
            The processed activity summary task result
        """
        # change response to english
        if localization and not localization.lower().startswith("en"):
            try:
                strings = [desc["description"] for desc in parsed_result["activityDescriptions"]] + [
                    summary["summary"] for summary in parsed_result["activitySummaries"]
                ]
                strings.append(parsed_result["workflowShortDescription"])
                strings.append(parsed_result["workflowFileName"])
                strings.append(parsed_result["workflowSummary"])

                translated_strings = await TRANSLATE_TASK.translate_multi_str(
                    {
                        "target_language": localization,
                        "input_strings": strings,
                        "feature": feature_type,
                    },
                )

                if len(translated_strings) != len(strings):
                    LOGGER.error(
                        f"Failed to translate activity summary to {localization}. Error: Length mismatch, expected {len(strings)} but got {len(translated_strings)}.",  # noqa
                    )
                    return {"result": parsed_result, "usage": usage}  # type: ignore[return-value]

                # First, update the translated activity descriptions
                for i, translated_string in enumerate(translated_strings[: len(parsed_result["activityDescriptions"])]):
                    parsed_result["activityDescriptions"][i]["description"] = translated_string

                # Then, update the translated activity summaries
                for i, translated_string in enumerate(translated_strings[len(parsed_result["activityDescriptions"]) : -3]):
                    parsed_result["activitySummaries"][i]["summary"] = translated_string

                # Finally, update the translated workflow summaries
                parsed_result["workflowShortDescription"] = translated_strings[-3]
                parsed_result["workflowFileName"] = translated_strings[-2]
                parsed_result["workflowSummary"] = translated_strings[-1]

            except Exception as e:
                LOGGER.exception(f"Failed to translate activity summary to {localization}. {e}")

        for desc in parsed_result.get("activityDescriptions", []):
            if "description" not in desc:
                LOGGER.warning(f"Invalid result. Not having description. {desc.keys()}")
                continue
            desc["description"] = escape_quotes(desc["description"])

        return {"result": parsed_result, "usage": usage}


class ActivitySummaryTask(BaseActivitySummaryTask):
    def __init__(self, config_name: str) -> None:
        super().__init__(config_name)

        self.retriever = ActivitySummaryRetriever(self.config, ModelManager().get_embeddings_model())

    def _retrieve_demonstrations(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        workflow: WorkflowDict,
        query: set[str],
        identifier: str | None = None,
        activity_ids: list[str] | None = None,
    ) -> List[Any]:
        demonstrations = self.retriever.get_relevant(workflow, query, identifier=identifier)

        return self._prepare_demonstration(demonstrations, summary_model, activity_ids)

    @log_execution_time("ActivitySummaryTask.run")
    async def run(
        self,
        request: ActivitySummaryRequest,
        localization: str | None = None,
        identifier: str | None = None,
        exclude_existing_thoughts: bool = False,
        target_framework: TargetFramework = "Windows",  # we could change the contract of the request to include this
    ) -> ActivitySummaryTaskResult:
        original_workflow_dict = yaml_load(request["activity"])
        if "workflow" not in original_workflow_dict:
            raise BadRequestError("Missing workflow.")
        original_workflow_object = Workflow(original_workflow_dict.get("description", ""), "", original_workflow_dict)

        workflow_object = DAPClassNameTranslator().translate(original_workflow_object)
        workflow_dict = copy.deepcopy(original_workflow_dict)
        workflow_dict["workflow"] = workflow_object.to_dict(
            include_ids=True,
            include_thoughts=not exclude_existing_thoughts,
            # include_packages=False,
            # include_name=False,  # should we exclude process name?
        )["workflow"]
        activity_names = get_unique_activity_names(workflow_object, target_framework)

        summary_model = ModelManager().get_llm_model("activity_summary_model", ConsumingFeatureType.WORKFLOW_SUMMARIZATION)
        demonstration_messages = self._retrieve_demonstrations(
            summary_model,
            workflow_dict,
            activity_names,
            identifier=identifier,
        )
        result = await self.run_generate(summary_model, workflow_dict, demonstration_messages, localization)

        return result

    def _clear_request_workflow(self, workflow: WorkflowDict) -> WorkflowDict:
        workflow_cleaned = remove_fields(workflow, ["Configuration", "configuration", "dynamicActivityDetails"])
        workflow_cleaned_as_str, _ = replace_blobs_with_hash(yaml_dump(workflow_cleaned), use_b64_charset=True)
        try:
            workflow_cleaned = yaml_load(workflow_cleaned_as_str)
        except Exception as e:
            LOGGER.exception(f"Blob replacement failed. Cannot load workflow. Error: {e}")
        return workflow_cleaned

    async def run_generate(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        workflow: WorkflowDict,
        demonstrations: list[langchain.schema.BaseMessage],
        localization: str | None = None,
    ) -> ActivitySummaryTaskResult:
        chat_chain, pruned_workflow = self._prepare_summarization_chain(summary_model, workflow, demonstrations)
        inputs = {"activity": yaml_dump(pruned_workflow)}

        parsed_result, usage = await self.generate_summary_result(summary_model, chat_chain, inputs)
        return await self._process_summary_result(parsed_result, usage, localization, ConsumingFeatureType.WORKFLOW_SUMMARIZATION)

    def _prepare_summarization_chain(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        workflow: WorkflowDict,
        demonstrations: list[langchain.schema.BaseMessage],
        activity_ids: list[str] | None = None,
        language: API_WF_EXPRESSION_LANGUAGE | None = None,
    ) -> tuple[langchain.chains.LLMChain, WorkflowDict]:
        """Prepare the summarization chain with messages, chat chain and pruned workflow.

        Args:
            summary_model: The LLM model to use for summarization
            workflow: The workflow to summarize
            demonstrations: List of demonstration messages
            activity_ids: Optional list of activity IDs for API workflows

        Returns:
            Tuple containing:
            - Chat chain for summarization
            - Pruned workflow that fits within token limits
        """
        format_instructions = yaml_load(self.format_config)["format_prompt"]
        size_instructions = self.config["prompt"]["response_size_instructions"]
        system_prompt_template = self.config["prompt"]["system_msg"]
        format_instructions = format_instructions.format(**size_instructions)

        cleaned_workflow: WorkflowDict = self._clear_request_workflow(workflow)
        input_dict = {"format_instructions": format_instructions}
        messages: list[Any] = [langchain.prompts.SystemMessagePromptTemplate.from_template(system_prompt_template).format(**input_dict)]

        for demonstration in demonstrations:
            messages.append(demonstration)

        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["user_msg_template"])
        messages.append(user_message_template)
        summarization_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(messages)

        chat_chain = summarization_prompt_template | summary_model

        pruned_workflow = self._ensure_prompt_fits_limit(summary_model, summarization_prompt_template, cleaned_workflow, activity_ids)
        if pruned_workflow is None:
            raise errors.PromptOverflowError()

        return chat_chain, pruned_workflow


class ActivitySummaryTaskApiWorkflows(BaseActivitySummaryTask):
    """
    A specialized version of ActivitySummaryTask for handling API workflows.
    This class is designed to work with JS and JQ target frameworks.
    """

    def __init__(self, config_name: str) -> None:
        super().__init__(config_name)

        self.api_activities_retriever = APIActivitiesRetriever()
        self.retriever = APIWorkflowDemonstrationsRetriever(
            config_name=str(paths.get_activity_retrieval_prompt_path()),
            dataset_path=paths.get_api_workflows_dataset_path(),
            retriever_path=paths.get_workflow_summarization_retriever_path(),
            activities_retriever=self.api_activities_retriever,
        )
        self.workflow_parser = ApiWorkflowParser(self.api_activities_retriever)

    def _retrieve_demonstrations(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        workflow: WorkflowDict,
        query: set[str],
        identifier: str | None = None,
        activity_ids: list[str] | None = None,
        eval_mode: bool = False,
    ) -> List[Any]:
        embeddings_model = ModelManager().get_embeddings_model("activities_embedding_model")
        query_embedding = embeddings_model.encode(str(query), instruction_set="icl", instruction_type="key")
        connections_embedding = embeddings_model.encode(yaml_dump(workflow), instruction_set="icl", instruction_type="key")
        demonstrations = self.retriever.get_relevant(query=str(query), query_embedding=query_embedding, connections_embedding=connections_embedding)

        all_demonstrations = []
        for demo_list in demonstrations.values():
            all_demonstrations.extend(demo_list)

        if eval_mode:
            demonstrations = [demo for demo in all_demonstrations if demo.get("identifier", "").split(".")[1].split(".")[0] != identifier]
        else:
            demonstrations = copy.deepcopy(all_demonstrations)
        return self._prepare_demonstration(demonstrations, summary_model, activity_ids)

    def _prepare_summarization_chain(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        workflow: WorkflowDict,
        demonstrations: list[langchain.schema.BaseMessage],
        activity_ids: list[str] | None = None,
        language: API_WF_EXPRESSION_LANGUAGE | None = None,
    ) -> tuple[langchain.chains.LLMChain, WorkflowDict]:
        """Prepare the summarization chain with messages, chat chain and pruned workflow.

        Args:
            summary_model: The LLM model to use for summarization
            workflow: The workflow to summarize
            demonstrations: List of demonstration messages
            activity_ids: Optional list of activity IDs for API workflows

        Returns:
            Tuple containing:
            - Chat chain for summarization
            - Pruned workflow that fits within token limits
        """

        api_workflow_schema = ApiWorkflow.model_json_schema()

        complete_schema = {"ApiWorkflow": api_workflow_schema}

        format_instructions = yaml_load(self.format_config)["format_prompt"]
        size_instructions = self.config["prompt"]["response_size_instructions"]
        system_prompt_template = self.config["prompt"]["system_msg"]
        format_instructions = format_instructions.format(**size_instructions)

        input_dict = {"language_name": language, "api_wf_json_schema": json.dumps(complete_schema), "format_instructions": format_instructions}

        messages: list[Any] = [langchain.prompts.SystemMessagePromptTemplate.from_template(system_prompt_template).format(**input_dict)]

        for demonstration in demonstrations:
            messages.append(demonstration)

        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["user_msg_template"])
        messages.append(user_message_template)
        summarization_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(messages)

        chat_chain = summarization_prompt_template | summary_model

        pruned_workflow = self._ensure_prompt_fits_limit(summary_model, summarization_prompt_template, workflow, activity_ids)
        if pruned_workflow is None:
            raise errors.PromptOverflowError()

        return chat_chain, pruned_workflow

    @log_execution_time("ActivitySummaryTask.run")
    async def run(
        self,
        request: ActivitySummaryRequest,
        localization: str | None = None,
        identifier: str | None = None,
        eval_mode: bool = False,
        language: API_WF_EXPRESSION_LANGUAGE | None = None,
    ) -> ActivitySummaryTaskResult:
        original_workflow_dict = yaml_load(request["activity"])
        workflow_dict_to_parse = copy.deepcopy(original_workflow_dict)
        if "solution_workflow" in workflow_dict_to_parse.keys():
            workflow_dict_to_parse = workflow_dict_to_parse["solution_workflow"]
        api_workflow, _, activity_id_map = self.workflow_parser.parse_api_workflow(workflow_dict_to_parse)

        if eval_mode:
            ApiWorkflowActivityIdReplace().walk(api_workflow)

        reverse_activity_id_map = {v: k for k, v in activity_id_map.items()}

        extracted_activity_ids = [
            activity.id for activity in list_activities(ApiWorkflow(root=workflow_dict_to_parse["root"], input=workflow_dict_to_parse["input"]))
        ]
        workflow_dict = copy.deepcopy(workflow_dict_to_parse)
        workflow_dict["workflow"] = api_workflow.root.dict(exclude_none=True, exclude_unset=True)

        retriever_query = set(get_workflow_activity_types(api_workflow.root))

        summary_model = ModelManager().get_llm_model("activity_summary_model", ConsumingFeatureType.API_WORKFLOW_SUMMARIZATION)
        demonstration_messages = self._retrieve_demonstrations(
            summary_model, workflow_dict, retriever_query, activity_ids=extracted_activity_ids, identifier=identifier, eval_mode=eval_mode
        )
        result = await self.run_generate_api_workflows_summary(
            summary_model, extracted_activity_ids, workflow_dict, demonstration_messages, localization, language
        )

        for activity in result["result"]["activityDescriptions"]:
            if activity["id"] in reverse_activity_id_map.keys():
                activity["id"] = reverse_activity_id_map[activity["id"]]

        for activity in result["result"]["activitySummaries"]:
            if activity["id"] in reverse_activity_id_map.keys():
                activity["id"] = reverse_activity_id_map[activity["id"]]

        if eval_mode:
            result["result"]["originalWorkflow"] = dict(original_workflow_dict)

        return result

    async def run_generate_api_workflows_summary(
        self,
        summary_model: langchain_core.language_models.BaseChatModel,
        activity_ids: list[str],
        workflow: WorkflowDict,
        demonstrations: list[langchain.schema.BaseMessage],
        localization: str | None = None,
        language: API_WF_EXPRESSION_LANGUAGE | None = None,
    ) -> ActivitySummaryTaskResult:
        chat_chain, pruned_workflow = self._prepare_summarization_chain(summary_model, workflow, demonstrations, activity_ids, language)

        inputs = {"activity": yaml_dump(pruned_workflow), "activity_ids": activity_ids}

        parsed_result, usage = await self.generate_summary_result(summary_model, chat_chain, inputs)
        return await self._process_summary_result(parsed_result, usage, localization, ConsumingFeatureType.API_WORKFLOW_SUMMARIZATION)
