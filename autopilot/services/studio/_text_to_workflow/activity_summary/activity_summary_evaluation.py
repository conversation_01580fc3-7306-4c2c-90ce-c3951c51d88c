import json
import os
import pathlib
from typing import Any, Dict

import tqdm
from numpy.typing import NDArray

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint, api_workflow_activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest, ActivitySummaryResult
from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

activity_summary_endpoint.init()
api_workflow_activity_summary_endpoint.init()


async def evaluate(dataset_path: pathlib.Path, target_framework: TargetFramework, language: API_WF_EXPRESSION_LANGUAGE | None = None) -> None:
    (
        total_activity_desc_score,
        total_activity_sum_score,
        total_workflow_score,
        total_workflow_filename_score,
        total_workflow_short_description_score,
        total_files,
    ) = (0, 0, 0, 0, 0, 0)

    save_path = paths.get_workflow_summarization_result_path()
    model_results = []

    for file_path in tqdm.tqdm(sorted(dataset_path.glob("*.yaml")), desc="Evaluating", dynamic_ncols=True):
        data = yaml_load(file_path)

        total_files += 1
        tqdm.tqdm.write(f"\n🏢 Processing {file_path.name}...")
        summary_request = ActivitySummaryRequest(activity=yaml_dump(data["input"]))

        if target_framework == "Api":
            summary_request["language"] = language
            summary_response = await api_workflow_activity_summary_endpoint.summarize(
                summary_request,
                identifier=file_path.stem,
                eval_mode=True,
            )
        else:
            summary_response = await activity_summary_endpoint.summarize(
                summary_request,
                identifier=file_path.stem,
            )

        model_result = summary_response["result"]

        model_results.append(model_result)

        # Evaluate cosine similarity between the generated description and the ground truth description
        activity_desc_score = get_composite_score(data, "activityDescriptions", "description", model_result)
        activity_sum_score = get_composite_score(data, "activitySummaries", "summary", model_result)
        workflow_score = _get_cosine_similarity(data["output"]["workflowSummary"], model_result["workflowSummary"])
        workflow_filename_score = _get_cosine_similarity(data["output"]["workflowFileName"], model_result["workflowFileName"])
        workflow_short_description_score = _get_cosine_similarity(data["output"]["workflowShortDescription"], model_result["workflowShortDescription"])
        total_activity_desc_score += activity_desc_score
        total_activity_sum_score += activity_sum_score
        total_workflow_score += workflow_score
        total_workflow_filename_score += workflow_filename_score
        total_workflow_short_description_score += workflow_short_description_score

        print(f"   🔃 {activity_desc_score} (Avg. Activity Description Cosine-Similarity)")
        print(f"   🔃 {activity_sum_score} (Avg. Activity Summary Cosine-Similarity)")
        print(f"   ➡️  {workflow_score} (Avg. Workflow Description Cosine-Similarity)")
        print(f"   📝 {workflow_filename_score} (Avg. Workflow File Name Cosine-Similarity)")
        print(f"   📝 {workflow_short_description_score} (Avg. Workflow Short Description Cosine-Similarity)")
    print("\n💯 Scores on entire dataset:\n")
    print(f"   🔃 {total_activity_desc_score / total_files} (Avg. Activity Description Cosine-Similarity)")
    print(f"   🔃 {total_activity_sum_score / total_files} (Avg. Activity Summary Cosine-Similarity)")
    print(f"   ➡️  {total_workflow_score / total_files} (Avg. Workflow Description Cosine-Similarity)")
    print(f"   📝 {total_workflow_filename_score / total_files} (Avg. Workflow File Name Cosine-Similarity)")
    print(f"   📝 {total_workflow_short_description_score / total_files} (Avg. Workflow Short Description Cosine-Similarity)")

    os.makedirs(save_path.parent, exist_ok=True)
    with open(save_path, "w") as f:
        json.dump(model_results, f)


def get_composite_score(
    data: Dict[Any, Any],
    list_param_name: str,
    field_param_name: str,
    model_result: ActivitySummaryResult,
) -> NDArray:
    generated_activity_descriptions = dict[str, str]()
    for desc in model_result[list_param_name]:
        generated_activity_descriptions[desc["id"]] = desc[field_param_name]

    activity_score = 0
    for activity_description in data["output"][list_param_name]:
        if activity_description["id"] not in generated_activity_descriptions:
            print(f"WARNING: activity {field_param_name} {activity_description['id']} not found in generated content, skipping...")
            continue
        else:
            # Ground truth description
            ground_truth_description = activity_description[field_param_name]
            generated_description = generated_activity_descriptions[activity_description["id"]]

            local_score = _get_cosine_similarity(ground_truth_description, generated_description)
            activity_score += local_score

    activity_score /= len(data["output"][list_param_name])  # type: ignore[assignment]
    return activity_score


def _get_cosine_similarity(ground_truth_description: str, generated_description: str) -> NDArray:
    embedding_model = ModelManager().get_embeddings_model()

    ground_truth_embedding, generated_embedding = embedding_model.encode_batch(
        [ground_truth_description, generated_description], batch_size=2, instruction_set="icl", instruction_type="query"
    )

    similarity = ground_truth_embedding @ generated_embedding.T
    return similarity
