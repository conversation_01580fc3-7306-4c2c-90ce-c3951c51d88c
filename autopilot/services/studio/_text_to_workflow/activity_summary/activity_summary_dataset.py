import asyncio
import pathlib
from typing import List

import langchain
import langchain.prompts
import langchain.schema
import langchain_openai
import openai
import tqdm
from langchain.schema.messages import AIMessage, BaseMessage, HumanMessage

from services.studio._text_to_workflow.activity_summary.activity_summary_helpers import get_config_path
from services.studio._text_to_workflow.activity_summary.activity_summary_task import ActivitySummaryTask
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.walkers import ActivityIdCollector
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

ACTIVITY_SUMMARY_TASK = ActivitySummaryTask("prompt.yaml")


def _build_static_demonstration_messages(
    dataset_path: pathlib.Path,
    user_msg_template: langchain.prompts.HumanMessagePromptTemplate,
    ai_msg_template: langchain.prompts.AIMessagePromptTemplate,
) -> List[BaseMessage]:
    demonstration_messages = []
    for file_path in dataset_path.iterdir():
        data = yaml_load(file_path)
        process = yaml_dump(data["input"])

        user_message = user_msg_template.format(activity=process)
        assistant_message = ai_msg_template.format(description=yaml_dump(data["output"]))

        demonstration_messages.append(HumanMessage(content=user_message))
        demonstration_messages.append(AIMessage(content=assistant_message))

    return demonstration_messages


async def build_dataset(
    model: langchain_openai.AzureChatOpenAI,
    source_dataset_path: pathlib.Path,
    target_framework: TargetFramework,
    demos: List[langchain.schema.BaseMessage],
) -> None:
    subset = source_dataset_path.name
    target_dataset_path = paths.get_workflow_summarization_dataset_path(target_framework, subset)
    target_dataset_path.mkdir(exist_ok=True)

    for file_path in tqdm.tqdm(
        sorted(source_dataset_path.rglob("*.yaml")),
        desc=f"Building from {subset}",
        dynamic_ncols=True,
    ):
        datapoint = yaml_load(file_path)

        print(f"\n🏢 Processing {file_path.name}...")
        workflow_object = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])
        workflow_dict = workflow_object.to_dict(include_ids=True, include_packages=False, include_name=False)
        for _retry in range(3):
            try:
                summary_response = await ACTIVITY_SUMMARY_TASK.run_generate(model, workflow_dict, demos)
                parsed_result = summary_response["result"]

                expected_keys = {
                    "activitySummaries",
                    "activityDescriptions",
                    "workflowSummary",
                    "workflowFileName",
                    "workflowShortDescription",
                }
                response_keys = set(parsed_result.keys())
                if expected_keys - response_keys:
                    print(f"   🚫 Missing keys in response {expected_keys - response_keys}")
                    continue

                all_ids = sorted(ActivityIdCollector().collect(workflow_object))
                summaries_ids = sorted([x["id"] for x in parsed_result["activitySummaries"]])
                descriptions_ids = sorted([x["id"] for x in parsed_result["activityDescriptions"]])
                if all_ids != summaries_ids:
                    print(f"   🚫 Missing summaries {set(all_ids) - set(summaries_ids)}")
                    continue
                if all_ids != descriptions_ids:
                    print(f"   🚫 Missing descriptions {set(all_ids) - set(descriptions_ids)}")
                    continue

                # prev_description = datapoint["description"]
                # print("   🔃 {prev_description}".format(prev_description=prev_description))
                # print("   ➡️  {new_description}".format(new_description=parsed_result["workflowSummary"]))
                # print("   📝 {file_name}".format(file_name=parsed_result["workflowFileName"]))

                target_file_path = target_dataset_path / file_path.name
                yaml_dump({"input": workflow_dict, "output": parsed_result}, target_file_path)
                break
            except openai.APIError as e:
                print(f"   🚫 {e}")
            except Exception as e:
                print(f"   🚫 {e}")
        else:
            print(f"   🚫 Could not process {file_path.name}")


def create_workflow_summarization_dataset(request_context: RequestContext, target_framework: TargetFramework) -> None:
    config = yaml_load(get_config_path("prompt.yaml"))
    model = ModelManager().get_llm_model("activity_summary_model", ConsumingFeatureType.WORKFLOW_SUMMARIZATION, forced_context=request_context)

    static_dataset_path = paths.get_workflow_summarization_dataset_path(target_framework, "static")
    demos = _build_static_demonstration_messages(
        static_dataset_path,
        config["prompt"]["user_msg_template"],
        config["prompt"]["assistant_msg_template"],
    )
    workflow_generation_dataset_path = paths.get_workflow_generation_dataset_path(target_framework)
    for subset_path in workflow_generation_dataset_path.iterdir():
        if not subset_path.is_dir():
            continue
        if subset_path.name.startswith((".", "_")):
            continue
        if subset_path.name in {"prod", "static"}:
            continue
        asyncio.run(build_dataset(model, subset_path, target_framework, demos))


def create_workflow_summarization_datasets() -> None:
    request_context = get_testing_request_context()
    for target_framework in ("Portable", "Windows"):
        create_workflow_summarization_dataset(request_context, target_framework)
