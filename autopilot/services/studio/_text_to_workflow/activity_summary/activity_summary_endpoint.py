from services.studio._text_to_workflow.activity_summary.activity_summary_schema import (
    ActivitySummaryRequest,
    ActivitySummaryResponse,
)
from services.studio._text_to_workflow.activity_summary.activity_summary_task import ActivitySummaryTask

TASK: ActivitySummaryTask = ActivitySummaryTask("prompt.yaml")


def init() -> None:
    pass


async def summarize(
    request: ActivitySummaryRequest,
    localization: str | None = None,
    identifier: str | None = None,
) -> ActivitySummaryResponse:
    result = await TASK.run(request, localization, identifier=identifier, target_framework=request.get("target_framework", "Portable"))

    return {"result": result["result"], "usage": result["usage"].to_json()}
