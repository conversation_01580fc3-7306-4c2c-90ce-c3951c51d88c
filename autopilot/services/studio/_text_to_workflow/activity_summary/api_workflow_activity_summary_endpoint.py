from services.studio._text_to_workflow.activity_summary.activity_summary_schema import (
    ActivitySummaryRequest,
    ActivitySummaryResponse,
)
from services.studio._text_to_workflow.activity_summary.activity_summary_task import ActivitySummaryTaskApiWorkflows
from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework

TASK: ActivitySummaryTaskApiWorkflows

train_subsets_api_workflows: dict[TargetFramework, tuple[SubsetName, ...]] = {
    "Api": ("static",),
}


def init() -> None:
    global TASK
    TASK = ActivitySummaryTaskApiWorkflows("prompt_api_workflows.yaml")


async def summarize(
    request: ActivitySummaryRequest,
    localization: str | None = None,
    identifier: str | None = None,
    eval_mode: bool = False,
) -> ActivitySummaryResponse:
    result = await TASK.run(request, localization, identifier=identifier, eval_mode=eval_mode)

    return {"result": result["result"], "usage": result["usage"].to_json()}
