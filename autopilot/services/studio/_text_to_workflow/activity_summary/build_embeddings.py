from typing import Any, Dict, List, Set, <PERSON><PERSON>

import tqdm
from numpy.typing import NDArray

from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager

from . import activity_summary_helpers


def run(
    config: Dict[str, Any],
    target_framework: TargetFramework,
    subset: SubsetName | None = None,
    show_progress_bar: bool = False,
) -> Tuple[List[Dict[Any, Any]], NDArray, Set[str], str]:
    items = activity_summary_helpers.get_dataset(target_framework, subset).items()
    model = ModelManager().get_embeddings_model()
    documents, document_representation, document_keys, paths = [], [], [], []
    if show_progress_bar:
        items = tqdm.tqdm(items, dynamic_ncols=True)
    for path, document in items:
        activities_repr = activity_summary_helpers.get_unique_activity_names(Workflow("", "", document["input"]), target_framework)

        paths.append(path)
        document_keys.append({activity_summary_helpers.get_key_representation_for_activity(act) for act in activities_repr})
        document_representation.append(activity_summary_helpers.get_aggregate_representation_activity_names(activities_repr))
        documents.append(document)
    # TODO: Something is fishy about these descriptions.
    # Example description: UiPath.MicrosoftOffice365.Activities.Excel.WriteRowConnections+UiPath.MicrosoftOffice365.Activities.Mail.Triggers.NewEmailReceived
    embeddings = model.encode_batch(document_representation, 64)
    return documents, embeddings, document_keys, paths  # type: ignore[return-value]
