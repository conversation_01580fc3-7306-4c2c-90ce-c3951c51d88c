import pathlib
from typing import Any, Dict

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.embeddingsdb import get_activity_embedding_representation
from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.utils import paths, telemetry_utils
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

ACTIVITIES_RETRIEVER: ActivitiesRetriever | None = None  # for DAP translation
LOGGER = telemetry_utils.AppInsightsLogger()


def init_retriever() -> None:
    global ACTIVITIES_RETRIEVER
    ACTIVITIES_RETRIEVER = ActivitiesRetriever()


def get_config_path(config_name: str = "prompt.yaml") -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / config_name


def get_dataset(target_framework: TargetFramework | None, subset: SubsetName | None) -> Dict[pathlib.Path, Dict[Any, Any]]:
    dataset_path, ds = (
        paths.get_workflow_summarization_dataset_path(target_framework, subset),
        {},
    )
    for file_path in sorted(dataset_path.rglob("**/*.yaml")):
        ds[file_path] = yaml_load(file_path)
    return ds


def get_unique_activity_names(
    workflow: Workflow,
    target_framework: TargetFramework,
    context: RequestContext | None = None,
) -> set[str]:
    if ACTIVITIES_RETRIEVER is None:
        init_retriever()
    assert ACTIVITIES_RETRIEVER is not None, "Activities retriever is not initialized"
    activities_and_trigger = ActivitiesAndTriggersCollector().collect(workflow)
    activities = activities_and_trigger["trigger"] + activities_and_trigger["activity"]
    activities_details = [act_det for act in activities if (act_det := ACTIVITIES_RETRIEVER.get(act.activity_id, target_framework)) is not None]

    activities_missing = {act.activity_id for act, act_det in zip(activities, activities_details, strict=False) if act_det is None}
    if activities_missing:
        LOGGER.info(f"Missing activity info for {sorted(activities_missing)}")
    activities_formatted = {
        get_activity_embedding_representation(act_details) if act_details is not None else act_details for act_details in activities_details
    }
    return activities_missing | activities_formatted

    # # alternative implementation
    # activity_details = {act.activity_id: details for act in activities if (details := ACTIVITIES_RETRIEVER.get(act.activity_id, "Portable")) is not None}
    # return {_format_activity(activity_details[act.activity_id]) if act.activity_id in activity_details else act.activity_id for act in activities}


def get_aggregate_representation_activity_names(
    activities_representations: set[str],
) -> str:
    # TODO: test ablation for changing "+" to ", "
    return ", ".join(sorted(activities_representations))


def get_key_representation_for_activity(activity_representation: str) -> str:
    # # v1: identity function
    # return activity_representation
    # v2: either formatted activities or activity ids, take tha last token after "."
    return activity_representation.rsplit(".", 1)[-1]
