embedding_model:
  model_type: sentence-embedding
  deployment_name: all-mpnet-base-v2
retriever:
  demonstrations: 10
  ted_sample_size: 30
prompt:
  system_msg: |-
    You are a UiPath Studio AI assistant.
    The user will provide a UiPath automation API workflow, represented as a tree of activities in YAML format. 
    The activities have type names, activity display names and their parameters. Multiple activities can be nested inside another activity, forming a tree.
    API Workflows and their activities will always follow the JSON Schema below:
    {api_wf_json_schema}
    All activity ids will be found under an "activity" field in the workflow YAML file. A set of extracted activity ids will be provided to you. You MUST output an answer for every one of them.
    The $workflow object contains the input arguments of the workflow.
    The $context object contains the output of all the previously executed activities.
    The id of each activity is represented by the root key of the activity in the input YAML.
    {format_instructions}
  user_msg_template: |-
    The workflow to analyze is:
    {activity}
    The activity ids for the workflow are:
    {activity_ids}

  assistant_msg_template: |-
    {description}

  response_size_instructions:
    activity-description: "Try to use at most 6 words for non-Sequence activities and at most 12 words for Sequence activities."
    activity-summary: "Try to use at most 12 words for non-Sequence activities and at most 24 words for Sequence activities."