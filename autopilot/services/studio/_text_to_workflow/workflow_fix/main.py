import argparse
import asyncio

from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask


async def build() -> None:
    await WorkflowFixTask().load()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix activities")
    parser.add_argument("job", choices=["build"], help="Job type to run")
    args, _ = parser.parse_known_args()

    if args.job == "build":
        # parser.add_argument("--shallow", action="store_true", help="Shallow build. Only for wffix retriever")
        # args, _ = parser.parse_known_args()
        asyncio.run(build())
