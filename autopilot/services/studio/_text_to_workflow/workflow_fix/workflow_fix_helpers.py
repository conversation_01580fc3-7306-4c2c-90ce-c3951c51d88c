import pathlib

from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


def get_config_path(config_name: str) -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / config_name


def load_subset(subset_path: pathlib.Path) -> dict[pathlib.Path, dict]:
    ds = {}
    for file_path in sorted(subset_path.rglob("*.yaml")):
        if file_path.is_file():
            ds[file_path] = yaml_load(file_path)
    return ds
