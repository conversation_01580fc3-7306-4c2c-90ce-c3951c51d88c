import collections
import csv
import pathlib
import random
import re

import streamlit as st
from st_diff_viewer import diff_viewer

# from streamlit_ace import st_ace
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

AVAILABLE_LABELS = [
    "Very Good",
    "Good",
    "Bad",
    # "Incorrect"
    "Needs Review",
    "Other language",
    # "Missing validation error",
    "Unlabelled",
]

st.set_page_config(layout="wide")


def get_error_category(error: str) -> str:
    error_code_re = re.compile(r"(BC\d{5}|CS\d{4})")
    if match_object := error_code_re.match(error):
        return match_object.group()
    return re.sub(r"\'(.*)\'", "'x'", error)


@st.cache_data
def get_prod_freq_categories():
    def load_errors():
        errors_list_path = pathlib.Path("/workspace/data/workdir/fix_workflow/csv_of_errors.csv")
        # format is RFC 4180-compliant CSV: items are enclosed in " and " inside is escaped by double ""
        prod_errors = [line[0] for line in csv.reader(errors_list_path.open())][1:]
        return prod_errors

    prod_errors = load_errors()
    categories = [get_error_category(error) for error in prod_errors]
    return collections.Counter(categories)


labelling_data_filepath = pathlib.Path("/workspace/data/workdir/fix_workflow/playground-cummulative-tagging-results.yaml")


def get_loaded_labels_uncached():
    if not labelling_data_filepath.exists():
        yaml_dump({}, labelling_data_filepath)
    return yaml_load(labelling_data_filepath)


@st.cache_data
def get_loaded_labels():
    return get_loaded_labels_uncached()


labelling_data = get_loaded_labels()
good_labels = {path: data for path, data in labelling_data.items() if data["label"] in ["Very Good", "Good"]}

root_path = pathlib.Path("/workspace/data/workdir/fix_workflow/playground-cummulative")


@st.cache_data
def get_all_datapoints():
    datapoints = {file: yaml_load(file) for file in root_path.rglob("*.yaml")}
    # datapoints = {file: data for file, data in datapoints.items() if data["validation"]["errors"]}  # take only datapoints with validation errors as well
    return datapoints


@st.cache_data
def get_filepaths():
    return sorted(get_all_datapoints().keys())


datapoints = get_all_datapoints()
# file_paths = sorted(datapoints.keys())
file_paths = get_filepaths()

# WARNING! Changing the file_paths will result in the selected category being reset
review_filter = st.sidebar.radio("Filter by review status:", ("Show All", "Show Reviewed", "Show Unreviewed"), index=2)
if review_filter == "Show Reviewed":
    file_paths = [file for file in file_paths if file.name in labelling_data]
    label_filter = st.sidebar.selectbox("Filter by label:", ["All"] + AVAILABLE_LABELS, key="selected_label_filter")
    if label_filter != "All":
        file_paths = [file for file in file_paths if labelling_data.get(file.name, {}).get("label") == label_filter]
elif review_filter == "Show Unreviewed":
    file_paths = [file for file in file_paths if file.name not in labelling_data]
# file_paths = [file for file in file_paths if file.name not in labelling_data]

error_categories = {get_error_category(datapoints[path]["error_introduced"]) for path in file_paths}
prod_categories_frequency = get_prod_freq_categories()
existing_error_categories = collections.Counter(get_error_category(datapoints[path]["error_introduced"]) for path in file_paths)
# if st.session_state["selected_error_category"] != "All":

selected_error_category = st.sidebar.selectbox(
    "Filter by error category:",
    ["All"] + sorted(error_categories, key=lambda cat: prod_categories_frequency.get(cat, 0), reverse=True),
    # format_func=lambda x: f"{existing_error_categories.get(x, 0)} / {prod_categories_frequency.get(x, 0)} {x}" if x != "All" else "All",
    key="selected_error_category",
)
if selected_error_category != "All":
    file_paths = [file for file in file_paths if get_error_category(datapoints[file]["error_introduced"]) == selected_error_category]

select_random_on_rerun = st.sidebar.checkbox("Select Random on Submit?", value=False)
labelled_error_category_counts = {category: 0 for category in error_categories}
labelled_error_category_counts.update(collections.Counter(get_error_category(data["error"]) for data in labelling_data.values()))
min_count = min(labelled_error_category_counts.values())

# Add a button to the sidebar to randomly select an example with a path from the lowest represented category in the labelled data
if st.sidebar.button("Select Random Example from Lowest Represented Category") or (select_random_on_rerun and "rerun" in st.session_state):
    if "rerun" in st.session_state:
        del st.session_state["rerun"]
    # Find the lowest represented category
    lowest_categories = [category for category, count in labelled_error_category_counts.items() if count == min_count]

    unlabelled_examples = [
        path for path in file_paths if path.name not in labelling_data and get_error_category(datapoints[path]["error_introduced"]) in lowest_categories
    ]

    if unlabelled_examples:
        st.session_state.file_select = random.choice(unlabelled_examples)
        st.session_state.label = "Unlabelled"
        st.sidebar.write("Randomly selected file path")
        st.sidebar.code(st.session_state.file_select)
        st.sidebar.code(get_error_category(datapoints[st.session_state.file_select]["error_introduced"]))
        st.sidebar.write(f"with {min_count} labelled examples.")
    else:
        st.sidebar.write("No unlabelled examples available in the lowest represented category.")

if not file_paths:
    st.error("No files available.")
    st.stop()

selected_path = st.sidebar.selectbox(
    "Choose a file to view the diff:", file_paths, format_func=lambda x: x.stem[-36:], key="file_select", help="Type to search for a file"
)
if not selected_path:
    st.error("No files available.")

error_category_of_selected_file = get_error_category(datapoints[selected_path]["error_introduced"])
st.sidebar.write("Coverage of prod for the item's error category", prod_categories_frequency.get(error_category_of_selected_file, 0))
st.sidebar.write("Coverage of selected item's error category: ", existing_error_categories.get(error_category_of_selected_file, 0))
st.sidebar.write("Labelled examples of item's error category: ", labelled_error_category_counts.get(error_category_of_selected_file, 0))
st.sidebar.write(
    "Labelled examples of item's error category that are good: ",
    len(
        [
            data
            for data in labelling_data.values()
            if data["label"] in {"Very Good", "Good"} and get_error_category(data["error"]) == error_category_of_selected_file
        ]
    ),
)
st.sidebar.divider()
st.sidebar.write(f"Total labelled examples: {len(labelling_data)}")
st.sidebar.write(f"Total unlabelled examples: {len(file_paths)}")
st.sidebar.divider()
st.sidebar.write(f"Very good: {len([data for data in labelling_data.values() if data['label'] == 'Very Good'])}")
st.sidebar.write(f"Good: {len([data for data in labelling_data.values() if data['label'] == 'Good'])}")
st.sidebar.write(f"Bad: {len([data for data in labelling_data.values() if data['label'] == 'Bad'])}")
st.sidebar.write(f"Needs Review: {len([data for data in labelling_data.values() if data['label'] == 'Needs Review'])}")
st.sidebar.write(f"Other language: {len([data for data in labelling_data.values() if data['label'] == 'Other language'])}")
st.sidebar.write(f"Unlabelled: {len([data for data in labelling_data.values() if data['label'] == 'Unlabelled'])}")

content = yaml_load(selected_path)

original_workflow = Workflow("", "", content["workflow"]).lmyaml(include_name=False)
altered_workflow = Workflow("", "", content["fixedWorkflow"]).lmyaml(include_name=False)
error_used = content["error_introduced"]
reasoning = content["reasoning"]

st.code(selected_path)
st.markdown(f"### {selected_path.name}")
st.code("\n".join(content["validation"]["errors"]))
with st.container():
    st.code(error_used)
    # st.divider()
    st.text(reasoning)
    st.write("""<div class='fixed-header'/>""", unsafe_allow_html=True)
    st.markdown(
        """
    <style>
        div[data-testid="stVerticalBlock"] div:has(div.fixed-header) {
            position: sticky;
            top: 2.875rem;
            background-color: black;
            z-index: 999;
        }
        .fixed-header {
            border-bottom: 1px solid black;
        }
    </style>
        """,
        unsafe_allow_html=True,
    )
    # st.divider()

# Distribution of labelled examples by error category
# st.sidebar.markdown("### Labelled Examples by Error Category")
# for category, count in labelled_error_category_counts.items():
#     st.sidebar.write(f"{category}: {count}")

# Distribution of unlabelled examples by error category
# st.sidebar.markdown("### Unlabelled Examples by Error Category")
# unlabelled_error_category_counts = {category: 0 for category in error_categories}
# for file in file_paths:
#     if file.name not in labelling_data:
#         category = get_error_category(yaml_load(file)["error_introduced"])
#         unlabelled_error_category_counts[category] += 1
# for category, count in sorted(unlabelled_error_category_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
#     st.sidebar.write(f"{category}: {count}")

split_view = st.checkbox("Split View", value=True)
diff_viewer(
    original_workflow,
    altered_workflow,
    left_title="Original",
    right_title="Altered",
    use_dark_theme=True,
    split_view=split_view,
    disabled_word_diff=True,
    # height=-1,
)
# updated_altered_workflow = st.text_area("Altered Workflow", altered_workflow, height=400)
# updated_altered_workflow = st_ace(altered_workflow, language="yaml", key="altered_workflow", theme="monokai", auto_update=True)
# print(updated_altered_workflow)


# Labeling interface
# st.session_state["label"] = "Unlabelled"
# if selected_path.name in labelling_data:
#     st.code(selected_path.name)
#     st.write(f"Already labelled as: {labelling_data[selected_path.name]['label']}")
#     st.session_state["label"] = labelling_data.get[selected_path.name]["label"]
label = st.radio(
    "Select the label for the diff:",
    AVAILABLE_LABELS,
    index=AVAILABLE_LABELS.index(labelling_data[selected_path.name]["label"]) if selected_path.name in labelling_data else None,
    # index=_label_list.index("Unlabelled"),
    key="label",
)

if st.button("Submit"):
    # try:
    #     yaml_load(updated_altered_workflow)
    # except:
    #     st.error("Invalid YAML provided.")
    #     st.stop()

    st.write(f"Label submitted: {label}")
    # if updated_altered_workflow == altered_workflow:
    #     labelling_data[selected_path.name]["updatedWorkflow"] = updated_altered_workflow
    persistent_labels = get_loaded_labels_uncached()
    persistent_labels[selected_path.name] = {
        "path": selected_path.as_posix(),
        "label": label,
        "error": error_used,
    }
    yaml_dump(persistent_labels, labelling_data_filepath)
    get_loaded_labels.clear()  # noqa

    # if st.session_state[""]

    if select_random_on_rerun:
        st.session_state["rerun"] = True
        # del st.session_state["label"]
    st.rerun()
