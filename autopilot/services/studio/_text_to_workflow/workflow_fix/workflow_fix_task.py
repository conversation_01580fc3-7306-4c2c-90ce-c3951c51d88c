import copy
import functools
import itertools
import re
from typing import Optional

import langchain
import langchain.prompts
import langchain_community.callbacks
import langchain_core
import langchain_core.language_models
import langchain_core.messages
import numpy as np
import openai

from services.studio._text_to_workflow.activity_config import activity_config_demo_retriever
from services.studio._text_to_workflow.activity_config.activity_config_schema import ActivityConfigExample
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.constants import LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST, PRODUCTIVITY_ACTIVITY2CONNECTION
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ActivitySearchOptions, Connection, PlanStep, TargetFramework
from services.studio._text_to_workflow.common.typedefs_parser import parse_typedef
from services.studio._text_to_workflow.common.walkers import (
    ActivityThoughts<PERSON>ollector,
    ActivityTypeCollector,
    IntegrationServiceActivityReplacer,
    IntegrationServiceActivityRestorer,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import MODEL_MAX_OUTPUT_TOKENS_CEILING, ModelManager
from services.studio._text_to_workflow.utils import base64_utils, dotnet_dynamic_activities_discovery, paths, workflow_utils
from services.studio._text_to_workflow.utils.activity_utils import build_activity_type_definitions, build_additional_type_definitions
from services.studio._text_to_workflow.utils.errors import BadRequestError, ExistingWorkflowYAMLDeserializationError
from services.studio._text_to_workflow.utils.inference import llm_gateway_model
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.utils.request_utils import get_request_context
from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask
from services.studio._text_to_workflow.utils.unidiff_utils import (
    DiffingException,
    get_merge_conflict,
    get_skip_to_the_good_bit,
    get_soft_unidiff,
    get_unidiff,
    prepend_line_numbers,
    replace_diff_with_yaml,
    replace_patch_with_yaml,
    replace_skip_to_the_good_bit_with_yaml,
)
from services.studio._text_to_workflow.utils.workflow_utils import load_workflow_instance
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_fix import workflow_fix_helpers
from services.studio._text_to_workflow.workflow_fix.workflow_fix_retriever import WorkflowFixDemonstrationsRetriever
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixDemoType, WorkflowFixFlairType, WorkflowFixResponse
from services.studio._text_to_workflow.workflow_generation.config.constants import DYNAMIC_ACTIVITY_DETAILS_DEFAULT
from services.studio._text_to_workflow.workflow_generation.fixyaml_task import FixWorkflowYamlTask

DEMONSTRATIONS_BY_TYPE = {
    WorkflowFixDemoType.HumanInTheLoop: 0,
    WorkflowFixDemoType.Autofix: 2,
}
LOGGER = AppInsightsLogger()
TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")
FIXYAML_TASK = FixWorkflowYamlTask("fixyaml_prompt.yaml")

WORKFLOW_FIX_MODEL_NAME = "workflow_fix_gemini_model"
WORKFLOW_FIX_FULL_REWRITE_MODEL_NAME = "workflow_fix_gemini_model"


class WorkflowFixTask:
    def __init__(self) -> None:
        self.config = yaml_load(workflow_fix_helpers.get_config_path("prompt.yaml"))
        self.embedding_model = ModelManager().get_embeddings_model()
        self.activities_embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

        self.activity_definitions_retriever = ActivitiesRetriever()
        self.demo_retriever = WorkflowFixDemonstrationsRetriever(
            paths.get_workflow_fix_retriever_path(),
            self.activity_definitions_retriever,
        )

    async def load(self) -> "WorkflowFixTask":
        await activity_config_demo_retriever.init_and_load()
        return self

    async def _get_model(
        self,
        model_options: ModelOptions | None,
        existing_workflow: str,
        message_emitter: IMessageEmitter | None = None,
        flair: WorkflowFixFlairType = WorkflowFixFlairType.Default,
    ) -> tuple[langchain_core.language_models.BaseChatModel, bool]:
        """This function reconciles config and options and dynamically sets max_tokens based on the existing workflow."""
        model = ModelManager().get_llm_model(
            WORKFLOW_FIX_MODEL_NAME if model_options is None or ("model_name" in model_options and model_options["model_name"] is None) else model_options,
            ConsumingFeatureType.WORKFLOW_FIX,
        )
        model_name = getattr(model, "model_name", "unknown")
        if model_name == "o3-mini-2025-01-31":
            return model, False
        existing_workflow_tokens = model.get_num_tokens(existing_workflow)
        proposed_max_tokens = int(200 + existing_workflow_tokens * 1.25)
        proposed_max_tokens = min(proposed_max_tokens, MODEL_MAX_OUTPUT_TOKENS_CEILING.get(model_name, 8192))
        if flair != WorkflowFixFlairType.Default:
            proposed_max_tokens = int(proposed_max_tokens * 0.5)

        if model.max_model_tokens is None and message_emitter is not None:  # type: ignore
            await message_emitter.emit_message("Thinking longer about this workflow...")

        if model.max_model_tokens is None or model.max_model_tokens > proposed_max_tokens:  # type: ignore
            if flair == WorkflowFixFlairType.Default:
                model.max_model_tokens = proposed_max_tokens  # type: ignore
            return model, False
        else:
            model.max_model_tokens = 500  # type: ignore
            return model, True

    async def _preprocess_existing_workflow(self, existing_workflow: str) -> tuple[str, dict[str, str]]:
        altered_existing_workflow, mapping = workflow_utils.replace_blobs_with_hash(existing_workflow, use_b64_charset=True)
        return altered_existing_workflow, mapping

    async def _postprocess_fixed_workflow(self, fixed_workflow: str, replacements: dict[str, str]) -> str:
        return workflow_utils.revert_blobs_from_hash(fixed_workflow, replacements)

    async def run_fix_workflow(
        self,
        existing_workflow: str,
        errors: list[str],
        additional_type_defs: str,
        connections: list[Connection] | None,
        target_framework: TargetFramework,
        model_options: ModelOptions | None = None,
        flair: WorkflowFixFlairType = WorkflowFixFlairType.Default,
        message_emitter: IMessageEmitter | None = None,
        identifier: str | None = None,
        fixed_workflow_gt: str | None = None,
    ) -> WorkflowFixResponse:
        _original_existing_workflow = existing_workflow
        existing_workflow, blob_replacement_mapping = await self._preprocess_existing_workflow(existing_workflow)
        model, analysis_only = await self._get_model(model_options, existing_workflow, message_emitter, flair)
        if analysis_only:
            flair = WorkflowFixFlairType.AnalysisOnly
            # TODO: maybe we should inform the user about this - via a message emitter?
            if message_emitter is not None:
                await message_emitter.emit_message("Workflow might be too large to handle. Analyzing...")
            LOGGER.info("Model token limit reached. Running analysis only.")

        existing_workflow_object, result, usage, dap_restore_info, workflow_as_seen_by_model = await self._execute_fix_prompt(
            existing_workflow,
            connections,
            errors,
            additional_type_defs,
            target_framework,
            flair,
            model,
            None,
            identifier=identifier,
            message_emitter=message_emitter,
            fixed_workflow_gt=fixed_workflow_gt,
        )

        response: WorkflowFixResponse = {"usage": usage}
        # response["model_raw_prediction"] = result  # TODO: remove this after experimentation is done
        try:
            if flair in {
                WorkflowFixFlairType.UniDiff,
                WorkflowFixFlairType.SoftUniDiff,
            }:
                replacement_func = functools.partial(replace_diff_with_yaml, workflow_as_seen_by_model=workflow_as_seen_by_model, flair=flair)
                result = re.sub(r"```diff\n(.*)```", replacement_func, result, flags=re.DOTALL)
            elif flair in {
                WorkflowFixFlairType.MergeConflict,
                WorkflowFixFlairType.MergeConflictWithLines,
                WorkflowFixFlairType.MergeHybrid,
            }:
                replacement_func = functools.partial(replace_patch_with_yaml, workflow_as_seen_by_model=workflow_as_seen_by_model, flair=flair)
                if result.count("```patch") > 1:
                    LOGGER.warning("More than one patch block found in the model output. Using the first one.")
                result = re.sub(r"```patch\n(.*)```", replacement_func, result, flags=re.DOTALL)
            elif flair == WorkflowFixFlairType.SkipToTheGoodBit:
                replacement_func = functools.partial(replace_skip_to_the_good_bit_with_yaml, workflow_as_seen_by_model=workflow_as_seen_by_model, flair=flair)
                result = re.sub(r"```patch\n(.*)```", replacement_func, result, flags=re.DOTALL)
        except DiffingException as e:
            LOGGER.error(f"Could not apply diff. {e}")
            if self.config.get("retry_full_rewrite_on_partial_generation_failure", False):
                if message_emitter is not None:
                    await message_emitter.emit_message("Couldn't find a quick fix. Attempting more thorough methods.")
                full_rewrite_model_options = copy.deepcopy(model_options) or {}
                full_rewrite_model_options["model_name"] = WORKFLOW_FIX_FULL_REWRITE_MODEL_NAME
                return await self.run_fix_workflow(
                    existing_workflow,
                    errors,
                    additional_type_defs,
                    connections,
                    target_framework,
                    full_rewrite_model_options,
                    WorkflowFixFlairType.Default,
                    message_emitter,
                    identifier,
                )

        yaml_idx = result.index("```yaml") if "```yaml" in result else -1
        if yaml_idx == -1 or flair == WorkflowFixFlairType.AnalysisOnly or flair == WorkflowFixFlairType.GenerateReasoning:
            response["reasoning"] = result.removeprefix("Analysis:").strip()
        else:
            # Try to parse the fixed workflow and ensure that it is parse-able, otherwise skip returning it
            response["reasoning"] = result[:yaml_idx].removeprefix("Analysis:").strip()

            try:
                match_object = re.match(r"```yaml(.*)```", result[yaml_idx:], re.DOTALL)
                if match_object is None:
                    LOGGER.error("Could not find yaml inside response.")
                    # import pathlib
                    # from uuid import uuid4

                    # yaml_dump([existing_workflow, errors, result], pathlib.Path(f"troubleshoot-no-yaml-{uuid4()}.yaml"))
                else:
                    fixed_workflow_response = match_object.group(1)
                    # Ensure fixed workflow is parse-able and re-format it
                    try:
                        fixed_workflow_object = workflow_utils.load_workflow_instance("", fixed_workflow_response)
                    except ExistingWorkflowYAMLDeserializationError as e:
                        # Try to fix the YAML if it's not parse-able using LLM
                        if message_emitter is not None:
                            await message_emitter.emit_debug_message("Attempting to fix broken YAML from model output using LLM")

                        try:
                            result = await FIXYAML_TASK.run(model, {"workflow": fixed_workflow_response, "errors": str(e)})
                            fixed_workflow_response = yaml_dump(result["fixed_workflow"])
                            fixed_workflow_object = workflow_utils.load_workflow_instance("", fixed_workflow_response)
                        except Exception as fix_error:
                            LOGGER.exception(f"Could not fix YAML using LLM. Original error: {e}, Fix error: {fix_error}")
                            raise e  # Re-raise the original error if we couldn't fix it

                    # Post-process the workflow for DAP reverse mapping (restoring the initial structure).
                    IntegrationServiceActivityRestorer(dap_restore_info).walk(fixed_workflow_object)
                    # Since the namespace imports are not sent to the model, we have to restore them
                    # from the initial workflow.
                    fixed_workflow_object.namespace_imports = existing_workflow_object.namespace_imports

                    fixed_workflow_lmyaml = fixed_workflow_object.lmyaml(include_namespaces=True, include_dap_properties=True)
                    response["fixedWorkflow"] = await self._postprocess_fixed_workflow(fixed_workflow_lmyaml, blob_replacement_mapping)
            except BadRequestError as e:
                LOGGER.exception(f"Could not parse fixed workflow. {e}")

            # If we got a fixed workflow, but the reasoning is empty, we need to run the model again to get the reasoning
            if "fixedWorkflow" in response and response["fixedWorkflow"] is not None and response["reasoning"] == "":
                _, result, usage, _, _ = await self._execute_fix_prompt(
                    existing_workflow,
                    connections,
                    errors,
                    additional_type_defs,
                    target_framework,
                    flair,
                    model,
                    identifier=identifier,
                    fixed_workflow_gt=fixed_workflow_gt,
                )
                response["reasoning"] = result.removeprefix("Analysis:").strip()

        context = get_request_context()
        if context is not None and context.localization is not None and not context.localization.lower().startswith("en"):
            LOGGER.info(f"Translating analysis to {context.localization}")
            response["reasoning"] = await TRANSLATE_TASK.translate_single_str(
                {"target_language": context.localization, "input_string": response["reasoning"], "feature": ConsumingFeatureType.WORKFLOW_FIX}
            )

        return response

    async def _execute_fix_prompt(
        self,
        existing_workflow: str,
        connections: list[Connection] | None,
        errors: list[str],
        additional_type_defs: str,
        target_framework: TargetFramework,
        flair: WorkflowFixFlairType,
        model: llm_gateway_model.LLMGatewayModel,
        speculative_decoding: bool | None = None,
        identifier: str | None = None,
        message_emitter: IMessageEmitter | None = None,
        fixed_workflow_gt: str | None = None,
    ):
        speculative_decoding = speculative_decoding if speculative_decoding is not None else self.config.get("speculative_decoding", False)
        existing_workflow_object = load_workflow_instance("", existing_workflow)

        # TODO: these parts up to augment_dynamic_activities_type_definitions could be moved to a separate function
        activity_defs, collector, existing_workflow_object = await self._get_activity_defs(existing_workflow_object, errors, target_framework, message_emitter)
        # Used for replacing the DAP/IS activities with their virtual class name.
        # The ID is required for identifying multiple DAP activities that have the same activityTypeId.
        dap_uuid_class_name: dict[str, str] = {
            type_def["uuid"]: type_def["fullClassName"] for type_def in activity_defs if type_def["activityTypeId"] and "uuid" in type_def and type_def["uuid"]
        }

        augmented_type_defs, dap_restore_info = activity_defs, {}
        if connections:
            connections_by_connector: dict[str, Connection] = {}
            for connection in connections:
                if not (connector := connection["connector"]):
                    LOGGER.error("Faulty connector found in connection.")
                    continue

                if connector not in connections_by_connector or connection.get("isDefault", True):
                    connections_by_connector[connector] = connection

            augmented_type_defs, dap_restore_info = await self.augment_dynamic_activities_type_definitions(activity_defs, connections_by_connector)

        fix_messages = await self._build_system_prompt(augmented_type_defs, collector, additional_type_defs, target_framework, flair)
        if flair == WorkflowFixFlairType.GenerateReasoning:
            fix_user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["user_template_generate_reasoning"])
            fix_assistant_message_template = langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["ai_template_generate_reasoning"])
        else:
            fix_user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["user_template"])
            fix_assistant_message_template = langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["ai_template"])

        # TODO: Errors may also come in non-English languages, we should translate them internally for higher response quality
        error_messages = "\n".join(errors)
        fix_chat_prompt_template = self._build_prompt_template(
            target_framework,
            fix_messages,
            fix_user_message_template,
            fix_assistant_message_template,
            error_messages,
            flair,
            identifier,
        )

        preprocessed_workflow = existing_workflow_object
        if connections:  # should we issue a warning in case DAP activities are used, but no connections are provided?
            # We should use virtual activity names only if the connections were provided. Otherwise, we cannot augment the DAP type definitions.
            IntegrationServiceActivityReplacer(dap_uuid_class_name).walk(preprocessed_workflow)

        workflow_as_seen_by_the_model = preprocessed_workflow.lmyaml(include_ids=True, include_namespaces=False)
        if flair in {
            WorkflowFixFlairType.UniDiff,
            WorkflowFixFlairType.MergeConflictWithLines,
            WorkflowFixFlairType.MergeHybrid,
            WorkflowFixFlairType.SkipToTheGoodBit,
        }:
            workflow_annotated_with_line_numbers = prepend_line_numbers(workflow_as_seen_by_the_model)
            inputs = {"workflow": workflow_annotated_with_line_numbers, "errors": error_messages}
        elif flair == WorkflowFixFlairType.GenerateReasoning:
            if fixed_workflow_gt is None:
                raise ValueError("fixed_workflow_gt is required for GenerateReasoning")
            fixed_workflow_diff = get_soft_unidiff(existing_workflow, fixed_workflow_gt)
            fixed_workflow_diff = f"```diff\n{fixed_workflow_diff}\n```"
            inputs = {"workflow": workflow_as_seen_by_the_model, "errors": error_messages, "fixed_workflow": fixed_workflow_diff}
        else:
            inputs = {"workflow": workflow_as_seen_by_the_model, "errors": error_messages}

        # TODO: the model might generate new DAP activities. We should restore them to their original form as well. `dap_restore_info` lacks such information.
        model_runnable = model

        # Speculative decoding is not supported for reasoning models
        if speculative_decoding and model.max_model_tokens is not None:
            model_runnable = model.bind(
                prediction={
                    "type": "content",
                    "content": workflow_as_seen_by_the_model,
                },
                # response_validation=False,
            )
        chat_chain = fix_chat_prompt_template | model_runnable
        with langchain_community.callbacks.get_openai_callback() as cb:
            n_attempts = 2
            for attempt_idx in range(n_attempts):
                try:
                    result: str = (
                        await chat_chain.ainvoke(
                            inputs,
                            # response_validation=False,
                        )
                    ).content  # type: ignore
                    break
                except openai.BadRequestError as e:
                    if e.code == 400 and "Finish reason: 2" in e.message and attempt_idx < n_attempts - 1:
                        LOGGER.warning(f"Content filtering got triggered. Retrying... {attempt_idx}")
                        model.max_model_tokens = min(model.max_model_tokens * 2, MODEL_MAX_OUTPUT_TOKENS_CEILING.get(model.model_name, 8192))
                        result: str = await chat_chain.ainvoke(inputs)
                    else:
                        raise e
            usage = TokenUsage(
                model=model.model_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            ).to_json()

        return existing_workflow_object, result, usage, dap_restore_info, workflow_as_seen_by_the_model

    async def _get_activity_defs(
        self,
        workflow_object: Workflow,
        errors: list[str],
        target_framework: TargetFramework,
        message_emitter: IMessageEmitter | None = None,
    ) -> tuple[list[ActivityDefinition], ActivityTypeCollector, Workflow]:
        """Will build the activity type defs taking into account the already
        configured DAP activities. In case there are multiple DAP activities with
        the same type, each one will get a different type def with separate configs.
        """
        collector = ActivityTypeCollector().walk(workflow_object)

        activity_defs: list[ActivityDefinition] = []
        existing_activity_types = set(collector.get_activity_types())

        for activity_type in existing_activity_types:
            activity_def = self.activity_definitions_retriever.get(activity_type, target_framework, None)
            if not activity_def:
                LOGGER.warning(f"{activity_type} doesn't have a stored definition counterpart.")
                continue

            type_activities = collector.typed_activity_instances[activity_type]
            for activity in type_activities:
                curr_activity_def = copy.deepcopy(activity_def)

                if activity.is_dynamic:
                    curr_activity_def["uuid"] = activity.id
                    curr_activity_def["defaultConfiguration"] = curr_activity_def["activityConfiguration"]
                    curr_activity_def["activityIsConfigured"] = False
                    if activity.dap_is_configured:
                        # Override the type def only for configured activities,
                        # also keeping the default embedding config string.
                        curr_activity_def["activityConfiguration"] = activity.dap_config
                        curr_activity_def["activityIsConfigured"] = True
                        curr_activity_def["dynamicActivityDetails"] = activity.dynamic_activity_details

                activity_defs.append(curr_activity_def)

        # Fetch additional activity definitions based on errors and activity thoughts
        activity_thoughts_collector = ActivityThoughtsCollector().walk(workflow_object)

        # Concatenate errors and activity thoughts into a single string
        error_messages = "\n".join(errors)
        activity_thoughts = "\n".join([activity.display_name for activity in activity_thoughts_collector.activities])
        message = f"{error_messages}\n{activity_thoughts}"

        # Embed the combined error message and activity thoughts
        embedding = self.activities_embedding_model.encode(f"{message}", instruction_set="icl", instruction_type="query")

        # Create PlanSteps with the error message for searching activities and triggers
        activity_step: PlanStep = {"thought": message, "embedding": embedding, "type": "activity"}
        trigger_step: PlanStep = {"thought": message, "embedding": embedding, "type": "trigger"}

        # Search for relevant activities and triggers based on these
        candidate_activities = self.activity_definitions_retriever.search(
            activity_step,
            [],
            ActivitySearchOptions(
                mode=None,
                target_framework=target_framework,
                ignored_namespaces=set(),
                ignored_activities=set(),
            ),
            "activity",
            25,
        )

        candidate_triggers = self.activity_definitions_retriever.search(
            trigger_step,
            [],
            ActivitySearchOptions(
                mode=None,
                target_framework=target_framework,
                ignored_namespaces=set(),
                ignored_activities=set(),
            ),
            "trigger",
            3,
        )

        # Combine activities and triggers
        candidate_activities.extend(candidate_triggers)

        # Add the additional activities, avoiding duplicates
        added_activities = []
        for activity_def in candidate_activities:
            activity_type = activity_def.get("fullActivityId") or activity_def.get("fullClassName")
            if activity_type and activity_type not in existing_activity_types:
                activity_defs.append(activity_def)
                existing_activity_types.add(activity_type)
                added_activities.append(activity_type)

        # Emit debug messages only for newly added activities
        if message_emitter is not None and added_activities:
            added_activities_str = "\n".join(added_activities)
            await message_emitter.emit_debug_message(f"Added {len(added_activities)} additional activities based on error analysis:\n{added_activities_str}")

        return activity_defs, collector, workflow_object

    def _build_prompt_template(
        self,
        target_framework: TargetFramework,
        fix_messages: list[langchain_core.messages.BaseMessage],
        fix_user_message_template: langchain.prompts.HumanMessagePromptTemplate,
        fix_assistant_message_template: langchain.prompts.AIMessagePromptTemplate,
        error_messages: str,
        flair: WorkflowFixFlairType,
        identifier: str | None = None,
    ):
        query_embedding = self.embedding_model.encode(error_messages, instruction_set="icl", instruction_type="query")
        demos = self.demo_retriever.get_relevant(np.array(query_embedding), DEMONSTRATIONS_BY_TYPE, target_framework, identifier)
        for demonstration in itertools.chain(*demos.values()):
            self._add_demonstration(
                demonstration,
                fix_user_message_template,
                fix_assistant_message_template,
                fix_messages,
                flair,
            )
        fix_messages.append(fix_user_message_template)  # type: ignore
        fix_chat_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(fix_messages)
        return fix_chat_prompt_template

    def _add_demonstration(
        self,
        demo: dict,
        fix_user_message_template: langchain.prompts.HumanMessagePromptTemplate,
        fix_assistant_message_template: langchain.prompts.AIMessagePromptTemplate,
        fix_messages: list[langchain_core.messages.BaseMessage],
        flair: WorkflowFixFlairType,
    ) -> None:
        if flair == WorkflowFixFlairType.WorkflowBreaker:
            return  # no demonstrations for workflow breaking

        existing_workflow_yaml = yaml_dump(demo["process_existing"])
        fixed_workflow_yaml = yaml_dump(demo["process_fixed"]) if "process_fixed" in demo else ""

        existing_workflow_presentation = existing_workflow_yaml
        if flair in {
            WorkflowFixFlairType.UniDiff,
            WorkflowFixFlairType.MergeConflictWithLines,
            WorkflowFixFlairType.MergeHybrid,
            WorkflowFixFlairType.SkipToTheGoodBit,
        }:
            existing_workflow_presentation = prepend_line_numbers(existing_workflow_yaml)

        fixed_workflow_presentation = ""
        if flair == WorkflowFixFlairType.AnalysisOnly:
            fixed_workflow_presentation = ""
        elif "process_fixed" not in demo:  # following blocks require "process_fixed" in demo
            fixed_workflow_presentation = ""
        elif flair in {
            WorkflowFixFlairType.UniDiff,
            WorkflowFixFlairType.SoftUniDiff,
            WorkflowFixFlairType.GenerateReasoning,
        }:
            if flair == WorkflowFixFlairType.UniDiff:
                unified_diff = get_unidiff(existing_workflow_yaml, fixed_workflow_yaml, span=0)
            elif flair == WorkflowFixFlairType.SoftUniDiff or flair == WorkflowFixFlairType.GenerateReasoning:
                unified_diff = get_soft_unidiff(existing_workflow_yaml, fixed_workflow_yaml, span=1)
            if not unified_diff:
                LOGGER.warning(f"No unified diff found for {flair}!")
                return  # skip this demonstration
            fixed_workflow_presentation = f"```diff\n{unified_diff}\n```"
        elif flair in {
            WorkflowFixFlairType.MergeConflict,
            WorkflowFixFlairType.MergeConflictWithLines,
            WorkflowFixFlairType.MergeHybrid,
        }:
            span = 0 if flair == WorkflowFixFlairType.MergeConflictWithLines else 1
            merge_patch = get_merge_conflict(existing_workflow_yaml, fixed_workflow_yaml, flair=flair, span=span, use_ellipsis=True)
            if not merge_patch:
                LOGGER.warning(f"No merge patch found for {flair}!")
                return  # skip this demonstration
            fixed_workflow_presentation = f"```patch\n{merge_patch}\n```"
        elif flair == WorkflowFixFlairType.SkipToTheGoodBit:
            patch = get_skip_to_the_good_bit(existing_workflow_yaml, fixed_workflow_yaml)
            if not patch:
                LOGGER.warning(f"No skip to the good bit found for {flair}!")
                return  # skip this demonstration
            fixed_workflow_presentation = f"```patch\n{patch}\n```"
        else:  # for other flairs, use the full workflow
            fixed_workflow_presentation = f"```yaml\n{fixed_workflow_yaml}\n```"

        errors = ("\n".join(demo["errors"])) if demo["errors"] != [] else "Errors are provided inline."

        if flair == WorkflowFixFlairType.GenerateReasoning:
            fix_messages.append(
                fix_user_message_template.format(workflow=existing_workflow_presentation, errors=errors, fixed_workflow=fixed_workflow_presentation)
            )
            fix_messages.append(fix_assistant_message_template.format(fix_reasoning=demo["reasoning"]))
        else:
            fix_messages.append(fix_user_message_template.format(workflow=existing_workflow_presentation, errors=errors))
            fix_messages.append(fix_assistant_message_template.format(fixed_workflow=fixed_workflow_presentation, fix_reasoning=demo["reasoning"]))

    # Will also process the DAP activities and provide a lookup dict to be used when post-processing
    # the model response in order to revert the DAP structure to its initial/compressed form.
    async def _build_system_prompt(
        self,
        activity_defs: list[ActivityDefinition],
        collector: ActivityTypeCollector,
        additional_type_defs: str,
        target_framework: TargetFramework,
        flair: WorkflowFixFlairType,
    ) -> list:
        system_prompt_params = {
            "activity_type_definitions": build_activity_type_definitions(activity_defs),
            "additional_type_definitions": build_additional_type_definitions(activity_defs, additional_type_defs),
            "config_examples": self.build_activity_config_demonstrations(collector),
        }

        system_prompt_params.update(self.config["prompt"]["system_prompt_sections"][flair.value])

        fix_messages = [langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["system_template"]).format(**system_prompt_params)]
        return fix_messages

    async def augment_dynamic_activities_type_definitions(
        self,
        type_defs: list[ActivityDefinition],
        connections_by_key: dict[str, Connection],
    ) -> tuple[list[ActivityDefinition], dict[str, dict]]:
        type_defs = copy.deepcopy(type_defs)

        type_defs_by_config_str: dict[str, ActivityDefinition] = {}
        is_package_name = is_package_version = None
        indexable_connections = []
        for activity_or_trigger in type_defs:
            if activity_or_trigger["connectorKey"] is None:
                continue
            if activity_or_trigger["fullClassName"] in PRODUCTIVITY_ACTIVITY2CONNECTION:
                continue
            if activity_or_trigger["packageName"] in LEGACY_INTEGRATION_SERVICE_PACKAGE_WHITELIST:
                continue

            is_package_name = activity_or_trigger["packageName"]
            is_package_version = activity_or_trigger["packageVersion"]

            self._add_indexable_connection(activity_or_trigger, connections_by_key, type_defs_by_config_str, indexable_connections)

        dap_restore_info, connections_json = {}, None
        if is_package_name is not None and is_package_version is not None:
            connections_json = await dotnet_dynamic_activities_discovery.get_type_definitions(is_package_name, is_package_version, indexable_connections)
        if connections_json is not None:
            for connection in connections_json["Configurations"]:
                self._add_discovery_result_to_type_def(connection, dap_restore_info, type_defs_by_config_str)

        # Add all the activities with missing/failed connections.
        for activity_or_trigger in type_defs:
            if activity_or_trigger["activityConfiguration"] is None:
                continue
            if "uuid" not in activity_or_trigger or activity_or_trigger["uuid"] not in dap_restore_info:
                continue

            # Unconfigured activities cannot be fixed anyway.
            dap_restore_info[activity_or_trigger["uuid"]] = {
                "activityTypeId": activity_or_trigger["activityTypeId"],
                "configuration": base64_utils.encode_string_base64(activity_or_trigger["activityConfiguration"]),
                "actualActivityName": activity_or_trigger["fullActivityName"],
                "connectorKey": activity_or_trigger["connectorKey"],
                "connectionId": None,
                "activityIsConfigured": False,
                "dynamicActivityDetails": base64_utils.encode_string_base64(DYNAMIC_ACTIVITY_DETAILS_DEFAULT),
                # "className": activity_or_trigger["className"],  # used for debugging / fixing dataset issues
            }

        return type_defs, dap_restore_info

    def _add_indexable_connection(self, activity_def: ActivityDefinition, connections_by_key: dict[str, Connection], type_defs: dict, result: list) -> None:
        if (
            activity_def["connectorKey"] in connections_by_key
            and activity_def["activityConfiguration"] is not None
            and activity_def.get("defaultConfiguration") is not None
        ):
            existing_connector = connections_by_key[activity_def["connectorKey"]]
            result.append(
                {
                    "ConnectionId": existing_connector["connectionId"],
                    "Configuration": activity_def["defaultConfiguration"],
                    "ClassDefinition": activity_def["typeDefinition"],
                    "ActivityFullName": activity_def["fullActivityName"],
                    "UiPathActivityTypeId": activity_def["activityTypeId"],
                }
            )

            type_defs[activity_def["defaultConfiguration"]] = activity_def
            activity_def["connectionId"] = existing_connector["connectionId"]

    def _add_discovery_result_to_type_def(self, connection: Optional[dict], dap_restore_info: dict, type_defs_by_config: dict[str, ActivityDefinition]) -> None:
        if connection is None:
            LOGGER.warning("Connection is None")
            return
        if not connection["IsSuccess"]:
            LOGGER.warning(f"Connection unsuccessful: {connection}")
            return
        if "OldConfiguration" not in connection:
            LOGGER.warning("OldConfiguration not found in connection.")
            return

        old_config = connection["OldConfiguration"]
        activity_definition = type_defs_by_config[old_config]

        activity_definition["typeDefinition"] = connection["NewClassDefinition"]

        parsed_type_def = parse_typedef(activity_definition["typeDefinition"])
        activity_definition["className"] = parsed_type_def["name"]
        activity_definition["fullClassName"] = activity_definition["namespace"] + "." + parsed_type_def["name"]
        activity_definition["params"] = parsed_type_def["params"]
        activity_definition["genericParamDefinition"] = parsed_type_def["type_params"]

        if activity_definition["additionalTypeDefinitions"] and not activity_definition["additionalTypeDefinitions"].endswith("\r\n"):
            activity_definition["additionalTypeDefinitions"] += "\r\n"
        activity_definition["additionalTypeDefinitions"] += connection["AdditionalTypesDefinitions"]
        if activity_definition["additionalNamespaces"] and not activity_definition["additionalNamespaces"].endswith("\r\n"):
            activity_definition["additionalNamespaces"] += "\r\n"
        activity_definition["additionalNamespaces"] += connection["AdditionalTypesNamespaces"]

        if not activity_definition["activityIsConfigured"]:
            # Populate the configuration details only for unconfigured activities.
            if "DynamicActivityDetails" in connection:
                activity_definition["dynamicActivityDetails"] = connection["DynamicActivityDetails"]
            else:
                activity_definition["dynamicActivityDetails"] = DYNAMIC_ACTIVITY_DETAILS_DEFAULT

            activity_definition["activityConfiguration"] = connection["NewConfiguration"]
            activity_definition["activityIsConfigured"] = connection["OldConfiguration"] != connection["NewConfiguration"]

        # TODO: this uuid based dap restore_info assumes that the model would output the id; maybe we should benchmark this
        dap_restore_info[activity_definition["uuid"]] = {
            "activityTypeId": activity_definition["activityTypeId"],
            "configuration": activity_definition["activityConfiguration"],
            "actualActivityName": activity_definition["fullActivityName"],
            "connectionId": activity_definition.get("connectionId", None),
            "connectorKey": activity_definition["connectorKey"],
            "activityIsConfigured": activity_definition["activityIsConfigured"],
            "dynamicActivityDetails": activity_definition["dynamicActivityDetails"],
            # "className": activity_definition["className"],  # used for debugging / fixing dataset issues
        }

    def build_activity_config_demonstrations(self, collector: ActivityTypeCollector):
        activity_demos: list[ActivityConfigExample] = []
        for activity in collector.iterate_activities_once_per_type():
            retrieved_activity_demos, _ = activity_config_demo_retriever.get_top_k_demos(activity.activity_id, variables=[], k=2, ignore_exampleids=[])
            activity_demos = activity_demos + retrieved_activity_demos

        return "\n".join(
            yaml_dump({"thought": demo["input"]["user_query"], "activity": demo["activity_fqn"], "params": demo["output"]["configuration"]})
            for demo in activity_demos
        )
