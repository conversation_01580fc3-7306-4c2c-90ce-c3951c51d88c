from enum import Enum

import typing_extensions as t

from services.studio._text_to_workflow.common.schema import Connection, TargetFramework, WorkflowDict
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse


class WorkflowFixDemoType(Enum):
    HumanInTheLoop = "train_hil"
    Autofix = "train"


# TODO: change these to StrEnum once we move to python3.11
class WorkflowFixFlairType(Enum):
    Default = "default"
    AnalysisOnly = "analysis_only"
    WorkflowBreaker = "workflow_breaker"
    UniDiff = "unidiff"
    SoftUniDiff = "softunidiff"
    GenerateReasoning = "generate_reasoning"
    MergeConflict = "merge_conflict"
    MergeConflictWithLines = "merge_conflict_with_lines"
    SkipToTheGoodBit = "skip_to_the_good_bit"
    MergeHybrid = "merge_hybrid"


class WorkflowFixDatapoint(t.TypedDict):
    name: str
    target_framework: TargetFramework
    errors: list[str]
    reasoning: str
    process_existing: WorkflowDict
    process_fixed: WorkflowDict
    # reasoning:


class WorkflowFixPrediction(t.TypedDict):
    usage: TokenUsageJson
    latency: float
    reasoning: str
    fixedWorkflow: WorkflowDict


class WorkflowFixRequest(BaseRequest):
    currentWorkflow: str
    errors: list[str]
    targetFramework: TargetFramework
    availableAdditionalTypeDefinitions: str | None
    connections: t.NotRequired[list[Connection] | None]


class WorkflowFixResponse(BaseResponse, total=False):
    usage: t.NotRequired[TokenUsageJson]
    reasoning: str
    fixedWorkflow: t.NotRequired[str]
