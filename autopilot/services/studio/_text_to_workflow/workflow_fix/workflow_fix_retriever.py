import pathlib as pt

import numpy as np
import tqdm

from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import embedding_model, paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_fix import workflow_fix_helpers
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixDemoType
from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesRetriever


class WorkflowFixDemonstrationsRetriever:
    retriever_path: pt.Path
    index: dict[tuple[TargetFramework, WorkflowFixDemoType], "DemonstrationsIndex"]

    def __init__(self, retriever_path: pt.Path, activities_retriever: ActivitiesRetriever) -> None:
        self.retriever_path = retriever_path
        self.index = {
            ("Portable", WorkflowFixDemoType.HumanInTheLoop): DemonstrationsIndex(
                self.retriever_path,
                "Portable",
                WorkflowFixDemoType.HumanInTheLoop,
                activities_retriever,
            ),
            ("Portable", WorkflowFixDemoType.Autofix): DemonstrationsIndex(
                self.retriever_path,
                "Portable",
                WorkflowFixDemoType.Autofix,
                activities_retriever,
            ),
            ("Windows", WorkflowFixDemoType.HumanInTheLoop): DemonstrationsIndex(
                self.retriever_path,
                "Windows",
                WorkflowFixDemoType.HumanInTheLoop,
                activities_retriever,
            ),
            ("Windows", WorkflowFixDemoType.Autofix): DemonstrationsIndex(
                self.retriever_path,
                "Windows",
                WorkflowFixDemoType.Autofix,
                activities_retriever,
            ),
        }

    def get_relevant(
        self,
        query_embedding: np.ndarray,
        demonstrations_count: dict[WorkflowFixDemoType, int],
        target_framework: TargetFramework,
        identifier: str | None = None,
    ) -> dict[WorkflowFixDemoType, list[dict]]:
        return {
            demo: self.index[(target_framework, demo)].search_batch(query_embedding, demo_cnt, identifier) for demo, demo_cnt in demonstrations_count.items()
        }


class DemonstrationsIndex(StateBuilder):
    model: embedding_model.EmbeddingModel
    subset: WorkflowFixDemoType

    def __init__(
        self,
        retriever_path: pt.Path,
        target_framework: TargetFramework,
        subset: WorkflowFixDemoType,
        activities_retriever: ActivitiesRetriever,
    ) -> None:
        self.target_framework = target_framework
        self.model = ModelManager().get_embeddings_model()
        self.subset = subset
        self.subset_path: pt.Path = paths.get_workflow_fix_dataset_path(subset.value)
        self.activities_retriever = activities_retriever
        self.store = StateStore((retriever_path / f"state.{subset}.pkl").as_posix(), self, lazy_load=settings.DEBUG_MODE)

    def build(self):
        texts, filepath2index, index2filepath = [], {}, {}
        items = workflow_fix_helpers.load_subset(self.subset_path).items()
        if settings.DEBUG_MODE:
            items = tqdm.tqdm(items, desc=self.subset.value, dynamic_ncols=True)
        if not len(items):
            raise ValueError(f"No items found in subset {self.subset_path}.")
        missing, dropped = set(), set()
        for filepath, item in items:
            # if filepath.parent.name == "handcrafted":
            #     continue  # skip the handcrafted demonstrations
            all_exist = True
            for trigger_or_activity in item.get("retrieved_triggers", []) + item.get("retrieved_activities", []):
                trigger_exists = self.activities_retriever.activity_exists(trigger_or_activity, self.target_framework, "trigger")
                activity_exists = self.activities_retriever.activity_exists(trigger_or_activity, self.target_framework, "activity")
                if not any(trigger_exists + activity_exists):
                    missing.add(trigger_or_activity)
                    tqdm.tqdm.write(f"WARNING! Missing triggers or activities {trigger_or_activity}")
                    all_exist = False
            if not all_exist:
                dropped.add(filepath)
                continue
            index = len(texts)
            texts.append("\n".join(item["errors"]))
            filepath2index[filepath] = index
            index2filepath[index] = filepath
        embeddings = self.model.encode_batch(texts, batch_size=256, instruction_set="icl", instruction_type="key")
        if missing:
            tqdm.tqdm.write(f"WARNING! Missing triggers or activities in {self.subset_path}")
            for m in sorted(missing):
                tqdm.tqdm.write(m)
        if dropped:
            tqdm.tqdm.write(f"WARNING! Dropped workflows in {self.subset_path}")
            for d in sorted(dropped):
                tqdm.tqdm.write(d.as_posix())
        tqdm.tqdm.write(f"Built index from {self.subset_path.as_posix()} with {len(embeddings)} items.")
        state = {
            "embeddings": embeddings,
            "filepath2index": filepath2index,
            "index2filepath": index2filepath,
        }
        state_info = {}
        state_info["filepath2index"] = {k.as_posix(): v for k, v in state["filepath2index"].items()}
        state_info["index2filepath"] = {k: v.as_posix() for k, v in state["index2filepath"].items()}
        return state, state_info

    def get_by_index(self, index: int) -> dict:
        filepath = self.store.state["index2filepath"][index]
        return yaml_load(filepath)

    def get_by_filepath(self, filepath: pt.Path) -> dict:
        return yaml_load(filepath)

    def search_batch(self, query_embedding: np.ndarray, k: int, identifier: str | None = None) -> list[dict]:
        if k == 0:
            return []

        embeddings = self.store.state["embeddings"]
        scores = embeddings @ query_embedding
        sorted_indices = np.argsort(-scores)
        sorted_scores = scores[sorted_indices]

        demonstrations_identifiers = {identifier} if identifier is not None else set()
        demonstrations = []
        for i, s in zip(sorted_indices.tolist(), sorted_scores.tolist(), strict=False):
            if len(demonstrations) >= k:
                break
            demonstration = self.get_by_index(i)
            filepath = pt.Path(self.store.state["index2filepath"][i])
            workflow_unique_identifier = filepath.stem
            if workflow_unique_identifier in demonstrations_identifiers:
                continue
            # TODO: test unique identifier (LOO evaluation)

            assert demonstration is not None
            demonstration["similarity"] = s
            demonstration["raw_scores"] = scores[i]

            demonstrations_identifiers.add(workflow_unique_identifier)
            demonstrations.append(demonstration)
        return demonstrations[::-1]
