from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import request_schema, request_utils, telemetry_utils
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixRequest, WorkflowFixResponse
from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask

TASK = WorkflowFixTask()


@telemetry_utils.log_execution_time("workflow_fix_endpoint.init")
async def init():
    pass


@telemetry_utils.log_execution_time("workflow_fix_endpoint.run")
async def fix_workflow(request: WorkflowFixRequest) -> WorkflowFixResponse:
    context = request_utils.get_request_context()

    model_options: request_schema.ModelOptions = {
        "model_name": context.model if not settings.IS_PROD else None,
        "seed": request_utils.get_seed(request),
    }

    if request["availableAdditionalTypeDefinitions"] is None:
        request["availableAdditionalTypeDefinitions"] = ""

    result = await TASK.run_fix_workflow(
        request["currentWorkflow"],
        request["errors"],
        request["availableAdditionalTypeDefinitions"],
        request.get("connections"),
        request["targetFramework"],
        model_options=model_options,
    )

    return result
