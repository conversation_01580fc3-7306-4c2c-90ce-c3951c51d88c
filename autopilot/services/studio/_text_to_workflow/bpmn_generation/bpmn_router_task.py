import asyncio
import time
import traceback
from itertools import groupby

from langchain_community.callbacks import get_openai_callback
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.prompts.chat import HumanMessagePromptTemplate, SystemMessagePromptTemplate
from openai import APIStatusError

from services.studio._text_to_workflow.bpmn_generation.agent.expression_generation_agent import ExpressionGenerationAgent
from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_extension_task import BpmnExtensionTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_flow_task import BpmnGenerationFlowTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_document_task import (
    BpmnGenerationFromDocumentTask,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_image_task import (
    BpmnGenerationFromImageTask,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    Activity,
    BpmnAssistantRequest,
    BpmnAssistantResponse,
    BpmnRequestContext,
    BpmnRouterException,
    BpmnRouterResult,
    ChatHistory,
    Connection,
    Connector,
    ExtensionDataOverride,
    ModelType,
    Process,
    TelemetryType,
    TelemetryUserAction,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_task import BpmnGenerationTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task import BpmnQATask
from services.studio._text_to_workflow.bpmn_generation.bpmn_telemetry import (
    BusinessMetrics,
    create_performance_metrics,
    detailed_performance_tracking,
    llm_timing_context,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_telemetry_task import BpmnTelemetryTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_flow_task import BpmnExtensionFlowTask
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.errors import ServiceError
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.request_utils import get_request_context
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()

BPMN_TELEMETRY_TASK: BpmnTelemetryTask = BpmnTelemetryTask()

generate_bpmn_task = BpmnGenerationTask()
generate_bpmn_multi_agent_task = BpmnGenerationFlowTask()
image_task = BpmnGenerationFromImageTask()
document_task = BpmnGenerationFromDocumentTask()
extension_multi_agent_task = BpmnExtensionFlowTask()
expression_generation_agent = ExpressionGenerationAgent()

TOOL_TASK_MAPPINGS: dict[Tool, BpmnBaseTask] = {
    Tool.GENERATE_BPMN: generate_bpmn_task,
    Tool.EDIT_BPMN: generate_bpmn_task,
    Tool.QA: BpmnQATask(),
    Tool.EXTENSION: BpmnExtensionTask(),
    Tool.CONVERT_IMAGE: image_task,
    Tool.CONVERT_DOCUMENT: document_task,
    Tool.GENERATE_EXP: expression_generation_agent,
}
TOO_MANY_REQUESTS = 429
INNER_SERVER_ERROR = 500
USE_MULTI_AGENT = "useMultiAgent"
UNKNOWN = "unknown"


class BpmnRouterTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("router_prompt.yaml")

    @detailed_performance_tracking("RouterProcess")
    async def process(self, request: BpmnAssistantRequest, support_validation: bool = False, external_perf=None) -> BpmnAssistantResponse:
        # Ensure perf is never None to satisfy the linter
        perf = external_perf or create_performance_metrics("RouterProcess", request.get("requestId", UNKNOWN))
        perf.add_property("solution_id", request.get("solutionId", UNKNOWN))
        perf.add_property("project_key", request.get("projectKey", UNKNOWN))

        # Get user request for logging
        user_request = request.get("userRequest", "")
        LOGGER.info(f"Processing BPMN assistant request: {user_request[:50]}...")

        # Find tools - most important step with LLM usage
        perf.start_segment("find_tools")
        result, context = await self._find_tools(request, perf)
        perf.end_segment()

        # Record tools detected
        if result.tools:
            for tool in result.tools:
                tool_name = str(tool.tool or UNKNOWN)
                perf.add_tool_info(tool_name)

        # Set validation mode
        context.support_validation = support_validation

        # Prepare response
        perf.start_segment("prepare_response")
        output: BpmnAssistantResponse = BpmnAssistantResponse(valid=result.valid, explanation=result.explanation)

        asyncio.create_task(
            BPMN_TELEMETRY_TASK.generate_telemetry(
                {
                    "sourceType": TelemetryType.BPMN,
                    "userAction": [TelemetryUserAction.USER_REQUEST],
                    "request": request["userRequest"],
                    "tools": result.tools or [],
                }
            )
        )

        # Handle invalid response
        if not result.valid:
            LOGGER.warning(message=f"Router response is invalid, due to [{result.explanation}]")
            output["explanation"] = result.explanation
            perf.add_property("response_valid", False)
            perf.add_property("error", "invalid_router_response")
            perf.record()
            return output

        # Handle no tools case
        tools = result.tools or []
        if len(tools) == 0:
            LOGGER.error(f"No tools detected in the router response, due to [{result.explanation}]")
            output["explanation"] = "Sorry, I cannot process your request."
            output["valid"] = False

            perf.add_property("response_valid", False)
            perf.add_property("error", "no_tools_detected")
            perf.record()
            return output

        # Handle multiple tools case
        if len(tools) > 1:
            bpmn_model_tools = [tool for tool in tools if (tool.tool == Tool.GENERATE_BPMN or tool.tool == Tool.EDIT_BPMN)]
            if len(bpmn_model_tools) > 0:
                # Route to BPMN model generation for multiple tools
                tools = bpmn_model_tools
                perf.add_property("filtered_to_bpmn_model_tools", True)
            else:
                LOGGER.error(f"Multiple tools [{tools}] detected in the router response, due to [{result.explanation}]")
                output["explanation"] = (
                    "Sorry, updating BPMN model along with other request is not supported yet and we're working on it. Please try again with only one request."
                )
                output["valid"] = False

                perf.add_property("response_valid", False)
                perf.add_property("error", "multiple_tools_unsupported")
                perf.record()
                return output

        del output["explanation"]
        perf.end_segment()

        # Process tools - LLM intensive step
        perf.start_segment("process_tools")
        results = []

        # Track total tool execution time
        tool_execution_start = time.time()

        for tool in tools:
            selected_tool = tool.tool or Tool.GENERATE_BPMN
            tool_task = TOOL_TASK_MAPPINGS.get(selected_tool)

            # Track individual tool
            tool_start = time.time()

            # Multi-agent check
            use_edit_multi_agent = selected_tool in (Tool.GENERATE_BPMN, Tool.EDIT_BPMN, Tool.CONVERT_IMAGE, Tool.CONVERT_DOCUMENT) and request.get(
                USE_MULTI_AGENT
            )
            if use_edit_multi_agent:
                tool_task = generate_bpmn_multi_agent_task
                LOGGER.info(f"Using multi-agent BPMN generation task for tool [{selected_tool}]")
                perf.add_property("using_multi_agent", True)
            elif selected_tool == Tool.EXTENSION and request.get("solutionId") and request.get("projectKey"):
                tool_task = extension_multi_agent_task
                LOGGER.info(f"Using multi-agent extension task for tool [{selected_tool}]")
                perf.add_property("using_extension_multi_agent", True)

            if tool_task is None:
                LOGGER.warning(f"Tool [{tool}] is not supported yet, skipping for now")
                results.append(ToolResult(tool=selected_tool, explanation=tool.request or context.user_request))
                continue

            LOGGER.info(f"Processing tool: {selected_tool}")

            # Get any model override
            context.override_model_type = self._get_override_model(selected_tool, request)
            if context.override_model_type:
                perf.add_property(f"tool_{selected_tool}_model_override", str(context.override_model_type))

            # Execute the tool
            try:
                tool_result = await tool_task.generate(context)
                perf.add_property(f"tool_{selected_tool}_success", True)
                results.append(tool_result)
            except Exception as ex:
                error_type = type(ex).__name__
                error_msg = str(ex)

                LOGGER.exception(f"Error processing tool {selected_tool}: {error_msg[:200]}")
                perf.add_property(f"tool_{selected_tool}_success", False)
                perf.add_property(f"tool_{selected_tool}_error", error_type)

                # Add fallback result
                output["valid"] = False
                if isinstance(ex, ServiceError) and ex.code < 500:
                    results.append(ToolResult(tool=selected_tool, explanation=ex.message))
                else:
                    results.append(ToolResult(tool=selected_tool, explanation=f"An error occurred while processing your request: {error_type}"))

            # Record individual tool timing
            tool_duration = time.time() - tool_start
            perf.add_property(f"tool_{selected_tool}_duration", round(tool_duration, 3))

        # Record total tool execution time
        tool_execution_time = time.time() - tool_execution_start
        perf.add_property("total_tool_execution_time", round(tool_execution_time, 3))
        perf.end_segment()

        # Prepare final response
        perf.start_segment("finalize_response")
        output["results"] = results

        # Final metrics
        perf.add_property("response_valid", True)
        perf.add_property("result_count", len(results))
        perf.add_property("tool_count", len(tools))
        perf.end_segment()

        # Record all performance metrics
        perf.record()

        return output

    def _get_override_model(self, tool: Tool, request: BpmnAssistantRequest) -> ModelType | None:
        selected_tool = tool
        if tool == Tool.GENERATE_BPMN:
            # merge 2 tools into 1
            selected_tool = Tool.EDIT_BPMN

        model_override = request.get("modelTypeOverride")
        if model_override and (model_override.get("tool") == selected_tool or (not model_override.get("tool"))) and model_override.get("model_type"):
            return model_override["model_type"]
        return None

    @telemetry_utils.log_execution_time(tracked_section="bpmn_router_task.find_tools")
    async def _find_tools(self, request: BpmnAssistantRequest, parent_perf=None) -> tuple[BpmnRouterResult, BpmnRequestContext]:
        # Initialize performance metrics for tool finding
        request_id = request.get("requestId", "unknown")
        performance = create_performance_metrics("FindTools", request_id)
        performance.start_segment("initialization")

        router_model: ModelType = request.get("routerModel") or ModelType.Google
        performance.add_property("router_model", router_model.value)

        # Get LLM model
        performance.start_segment("get_llm_model")
        model = self.model_manager.get_llm_model(f"bpmn_router_model_{router_model.value}", ConsumingFeatureType.BPMN_GENERATION)
        performance.end_segment()

        # Process current BPMN
        current_bpmn = request.get("currentBpmn") or ""
        if current_bpmn is not None:
            performance.start_segment("parse_bpmn")
            current_bpmn = self._remove_bpmn_diagram(current_bpmn.strip())
            performance.add_property("bpmn_size", len(current_bpmn))
            performance.end_segment()

            request["currentBpmn"] = current_bpmn

        # Prepare the prompt
        performance.start_segment("prepare_prompt")
        parser = PydanticOutputParser(pydantic_object=BpmnRouterResult)
        prompts = self.config["prompt"]

        system_prompt = SystemMessagePromptTemplate.from_template(
            template=prompts["system"],
            partial_variables={"schema": parser.get_format_instructions()},
        )

        # Process chat history
        chat_history = request.get("chatHistory") or []
        performance.add_property("chat_history_count", len(chat_history))

        user_prompt = HumanMessagePromptTemplate.from_template(
            template=prompts["user"], partial_variables={"chat_history": self._build_router_chat_history_prompt(chat_history)}
        )
        gen_messages = [system_prompt, user_prompt]
        chat_prompt = ChatPromptTemplate.from_messages(gen_messages)
        chain = chat_prompt | model | parser
        prompt_content = {
            "user_query": request["userRequest"],
            "document_attached": request.get("image") is not None or request.get("document") is not None,
            "current_bpmn": current_bpmn,
        }
        # LOGGER.info("Router prompt:\n" + chat_prompt.format(**prompt_content))

        performance.end_segment()

        # Execute the LLM call
        performance.start_segment("llm_call")
        async with llm_timing_context("router_llm_call", {"model": router_model.value, "request_type": "find_tools", "request_id": request_id}):
            response: BpmnRouterResult = await self._execute_with_retry(chain, prompt_content)
        performance.end_segment()

        # Process the response
        performance.start_segment("process_response")
        performance.add_property("valid_response", response.valid)
        performance.add_property("tool_count", len(response.tools or []))
        performance.add_property("has_explanation", bool(response.explanation))

        # Log tools detected
        if response.tools:
            for tool in response.tools:
                tool_name = str(tool.tool or "unknown")
                performance.add_property(f"detected_tool.{tool_name}", True)
        performance.end_segment()

        # Build context
        performance.start_segment("build_context")
        context = self._build_context(request, response, parent_perf)
        performance.end_segment()

        # Record final performance metrics
        performance.record()

        return response, context

    async def _execute_with_retry(self, chain, prompt_content) -> BpmnRouterResult:
        # Initialize performance metrics for retry execution
        performance = create_performance_metrics("ExecuteWithRetry", None)
        performance.start_segment("initialization")

        lastError: Exception | None = None
        max_retries = int(self.config["maxRetries"])
        performance.add_property("max_retries", max_retries)

        for retry_count in range(max_retries):
            performance.start_segment(f"attempt_{retry_count}")

            try:
                retry_telemetry = {"retry_count": retry_count, "max_retries": max_retries}

                # LLM execution
                performance.start_segment("llm_execution")
                async with llm_timing_context("router_llm_execution", retry_telemetry):
                    with get_openai_callback() as cb:
                        response: BpmnRouterResult = await chain.ainvoke(prompt_content)  # type: ignore

                        # Record token usage metrics
                        performance.add_property("total_tokens", cb.total_tokens)
                        performance.add_property("prompt_tokens", cb.prompt_tokens)
                        performance.add_property("completion_tokens", cb.completion_tokens)
                        performance.add_property("total_cost", str(cb.total_cost))

                        # Log OpenAI callback metrics
                        LOGGER.log_custom_event(
                            "BpmnRouter.LLMMetrics",
                            {
                                "total_tokens": cb.total_tokens,
                                "prompt_tokens": cb.prompt_tokens,
                                "completion_tokens": cb.completion_tokens,
                                "total_cost": cb.total_cost,
                                "successful_requests": cb.successful_requests,
                                "retry_count": retry_count,
                            },
                        )
                performance.end_segment()

                # Success metrics
                performance.add_property("success", True)
                performance.add_property("final_retry_count", retry_count)
                performance.record()

                return response

            except APIStatusError as error:
                performance.end_segment()  # End llm_execution segment if it was started

                lastError = error
                error_type = type(error).__name__
                error_msg = str(error)
                status_code = error.status_code

                # Add error metrics
                performance.add_property("error_type", error_type)
                performance.add_property("error", error_msg[:200])
                performance.add_property("status_code", status_code)

                if status_code == TOO_MANY_REQUESTS or status_code >= INNER_SERVER_ERROR:
                    LOGGER.warning(f"Failed to generate router response, retrying... Error: {traceback.format_exc()} with request_id: {error.request_id}")

                    # Retryable error
                    performance.add_property("retryable_error", True)

                    LOGGER.log_custom_event(
                        "BpmnRouter.APIStatusError",
                        {"status_code": status_code, "request_id": error.request_id, "retry_count": retry_count, "error": error_msg[:200]},
                    )
                else:
                    # Non-retryable error
                    performance.add_property("retryable_error", False)
                    performance.add_property("success", False)
                    performance.add_property("final_retry_count", retry_count)
                    performance.record()

                    LOGGER.log_custom_event("BpmnRouter.FatalAPIError", {"status_code": status_code, "request_id": error.request_id, "error": error_msg[:200]})
                    raise error
            except Exception as error:
                performance.end_segment()  # End llm_execution segment if it was started

                lastError = error
                error_type = type(error).__name__
                error_msg = str(error)

                # Add error metrics
                performance.add_property("error_type", error_type)
                performance.add_property("error", error_msg[:200])
                performance.add_property("retryable_error", True)

                LOGGER.warning(f"Failed to generate router response, retrying... Error: {traceback.format_exc()}")

                LOGGER.log_custom_event("BpmnRouter.GeneralError", {"error_type": error_type, "retry_count": retry_count, "error": error_msg[:200]})

            # End attempt segment
            performance.end_segment()

        # If we got here, all retries failed
        performance.add_property("success", False)
        performance.add_property("final_retry_count", max_retries)
        performance.add_property("max_retries_exceeded", True)
        performance.record()

        if not lastError:
            raise BpmnRouterException("Failed to generate router response due to unknown error")

        LOGGER.log_custom_event("BpmnRouter.MaxRetriesExceeded", {"error_type": type(lastError).__name__, "error": str(lastError)[:200]})

        raise lastError

    def _build_context(self, request: BpmnAssistantRequest, response: BpmnRouterResult, parent_perf=None) -> BpmnRequestContext:
        request_context = get_request_context()
        history_by_tool = {}
        if response.tools:
            all_histories = request.get("chatHistory") or []
            all_histories.sort(key=lambda h: h["tool"])
            for tool_name, tool_histories in groupby(all_histories, key=lambda ch: ch["tool"]):
                tool_histories = list(tool_histories)
                history_by_tool[tool_name] = tool_histories

        context = BpmnRequestContext(
            user_request=request["userRequest"],
            current_bpmn=request.get("currentBpmn") or "",
            request_context=request_context,
            image_data=request.get("image"),
            document_data=request.get("document"),
            chat_history=history_by_tool,
            solution_id=request.get("solutionId"),
            project_key=request.get("projectKey"),
            extension_data_override=self._build_extension_data_override(request),
            performance_tracker=parent_perf,
        )

        return context

    def _build_router_chat_history_prompt(self, chatHistories: list[ChatHistory]) -> str:
        history = ""
        for chatHistory in chatHistories:
            history += self.config["prompt"]["router_messages"].format(
                request=chatHistory["request"], response="The tool is " + chatHistory["tool"], timestamp=str(chatHistory["timestamp"])
            )
            history += "\n"
        return history

    def _analyze_user_request(self, user_request: str, business: BusinessMetrics) -> None:
        if not user_request:
            return

        # Basic length metrics
        word_count = len(user_request.split())
        char_length = len(user_request)

        business.add_metric("request_word_count", word_count)
        business.add_metric("request_char_length", char_length)

        # Simple complexity classification based on length
        complexity = "low"
        if word_count > 30:
            complexity = "high"
        elif word_count > 15:
            complexity = "medium"

        business.add_metric("request_complexity", complexity)

    def _build_extension_data_override(self, request: BpmnAssistantRequest) -> ExtensionDataOverride | None:
        if not request.get("extensionDataOverride"):
            return None

        extension_data = request.get("extensionDataOverride") or {}
        extension_data_override = ExtensionDataOverride()
        extension_data_override.processes = [
            Process(
                name=process.get("name"),
                fakeId=None,
                type=process.get("type"),
                score=0,
                id=process.get("id"),
                description=process.get("description"),
                connection=None,
                activity=None,
            )
            for process in extension_data.get("processes") or []
        ]
        extension_data_override.actionApps = [
            ActionApp(
                fakeId=None,
                id=action_app.get("id"),
                name=action_app.get("name"),
                systemName=action_app.get("systemName"),
                deployVersion=action_app.get("deployVersion"),
                type="Actions.HITL",
                score=0,
            )
            for action_app in extension_data.get("actionApps") or []
        ]
        extension_data_override.connections = [
            Connector(
                fakeId=None,
                key=connector.get("key"),
                name=connector.get("name"),
                connection=Connection(
                    name=connector.get("connection").get("name"), id=connector.get("connection").get("id"), isDefault=True, state="Active", instanceId=0
                ),
                activity=None,
                trigger=None,
                description=connector.get("description"),
                type="",
                score=0,
                isExternalAgent=connector.get("isExternalAgent", False),
                inputJson=None,
                resourceType=None,
            )
            for connector in extension_data.get("connections") or []
        ]
        extension_data_override.activities = {
            connector: [
                Activity(
                    name=activity.get("name"),
                    displayName=activity.get("displayName"),
                    description=activity.get("description"),
                    objectName=activity.get("objectName"),
                    eventOperation=activity.get("eventOperation", None),
                )
                for activity in activities
            ]
            for connector, activities in (extension_data.get("activities") or {}).items()
        }

        extension_data_override.triggers = {
            connector: [
                Activity(
                    name=activity.get("name"),
                    displayName=activity.get("displayName"),
                    description=activity.get("description"),
                    objectName=activity.get("objectName"),
                    eventOperation=activity.get("eventOperation"),
                )
                for activity in activities
            ]
            for connector, activities in (extension_data.get("triggers") or {}).items()
        }

        return extension_data_override
