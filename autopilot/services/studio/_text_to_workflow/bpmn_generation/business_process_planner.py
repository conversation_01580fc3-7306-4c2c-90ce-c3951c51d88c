import traceback
from itertools import groupby

from langchain_community.callbacks import get_openai_callback
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.prompts.chat import HumanMessagePromptTemplate, SystemMessagePromptTemplate
from langchain_core.runnables.config import RunnableConfig
from openai import APIStatusError

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ResourceKinds
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    Activity,
    BpmnRequestContext,
    ChatHistory,
    Connection,
    Connector,
    ExtensionDataOverride,
    ModelType,
    Process,
    SolutionResource,
    Tool,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import (
    BusinessProcessAgentState,
    BusinessProcessAssistantPlannerException,
    BusinessProcessAssistantPlannerResponse,
    BusinessProcessAssistantPlannerResult,
    BusinessProcessAssistantRequest,
    ProcessType,
)
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()

TOO_MANY_REQUESTS = 429
INNER_SERVER_ERROR = 500

TOOLS_SORT_BY_PRIORITY = [Tool.GENERATE_BPMN, Tool.EDIT_BPMN, Tool.EXTENSION, Tool.GENERATE_EXP, Tool.QA]


class BusinessProcessAgentPlanner(BpmnBaseTask):
    def __init__(self):
        super().__init__("planner_prompt.yaml")

    @telemetry_utils.log_execution_time(tracked_section="business_process_agent_planner.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        context: BpmnRequestContext = state["context"]
        result = await self._find_tools(state["request"], context)
        state["request"] = None

        message_emitter = config.get("configurable", {})["message_emitter"]

        error_message = ""
        if result is None:
            error_message = "Planner result is None, cannot find tools."
            LOGGER.error(error_message)
            await message_emitter.emit_message(error_message, end_stream=True)
            return {"planner_result": None}

        output: BusinessProcessAssistantPlannerResponse = BusinessProcessAssistantPlannerResponse(valid=result.valid, reasoning=result.reasoning)

        if not result.valid:
            error_message = f"Planner response is invalid, due to [{result.reasoning}]. Please try again."
            LOGGER.error(error_message)
            await message_emitter.emit_message(error_message, end_stream=True)
            return {"planner_result": output}

        result_tools = result.tools or []
        if len(result_tools) == 0:
            error_message = f"No tools detected in the Planner response, due to [{result.reasoning}]"
            LOGGER.error(error_message)
            await message_emitter.emit_message(error_message, end_stream=True)
            output["reasoning"] = f"Sorry, I cannot process your request due to {result.reasoning}. Please try again."
            output["valid"] = False
            return {"planner_result": output}

        result.tools.sort(key=lambda tool: TOOLS_SORT_BY_PRIORITY.index(tool.tool))

        await message_emitter.emit_message(result.reasoning)

        return {"planner_result": result, "context": context}

    @telemetry_utils.log_execution_time(tracked_section="business_process_agent_planner.find_tools")
    async def _find_tools(self, request: BusinessProcessAssistantRequest, context: BpmnRequestContext) -> BusinessProcessAssistantPlannerResult:
        planner_model: ModelType = request.modelProvider or ModelType.Google
        model = self.model_manager.get_llm_model(f"bpmn_planner_model_{planner_model.value}", ConsumingFeatureType.BPMN_GENERATION)
        current_process = request.currentProcess or ""
        if current_process is not None and request.processType == ProcessType.BPMN:
            current_process = self._remove_bpmn_diagram(current_process.strip())
            request.currentProcess = current_process

        parser = PydanticOutputParser(pydantic_object=BusinessProcessAssistantPlannerResult)
        prompts = self.config["prompt"]
        system_prompt = SystemMessagePromptTemplate.from_template(
            template=prompts["system"],
            partial_variables={"schema": parser.get_format_instructions()},
        )

        user_prompt = HumanMessagePromptTemplate.from_template(
            template=prompts["user"], partial_variables={"chat_history": self._build_planner_chat_history_prompt(request.chatHistory or [])}
        )
        gen_messages = [system_prompt, user_prompt]
        chat_prompt = ChatPromptTemplate.from_messages(gen_messages)
        chain = chat_prompt | model | parser
        prompt_content = {
            "user_query": request.userRequest,
            "document_attached": request.image is not None or request.document is not None,
            "current_process": current_process,
        }
        # LOGGER.info("Prompt:\n" + chat_prompt.format(**prompt_content))

        response: BusinessProcessAssistantPlannerResult = await self._execute_with_retry(chain, prompt_content)
        self._build_context(request, response, context)
        return response

    async def _execute_with_retry(self, chain, prompt_content) -> BusinessProcessAssistantPlannerResult:
        lastError: Exception | None = None
        for _ in range(int(self.config["maxRetries"])):
            try:
                with get_openai_callback():
                    response: BusinessProcessAssistantPlannerResult = await chain.ainvoke(prompt_content)  # type: ignore
                return response

            except APIStatusError as error:
                lastError = error
                if error.status_code == TOO_MANY_REQUESTS or error.status_code >= INNER_SERVER_ERROR:
                    LOGGER.warning(f"Failed to generate planner response, retrying... Error: {traceback.format_exc()} with request_id: {error.request_id}")
                else:
                    raise error
            except Exception as error:
                lastError = error
                LOGGER.warning(f"Failed to generate planner response, retrying... Error: {traceback.format_exc()}")

        if not lastError:
            raise BusinessProcessAssistantPlannerException("Failed to generate planner response due to unknown error")
        raise lastError

    def _build_context(self, request: BusinessProcessAssistantRequest, response: BusinessProcessAssistantPlannerResult, context: BpmnRequestContext):
        history_by_tool = {}
        if response.tools:
            all_histories = request.chatHistory or []
            all_histories.sort(key=lambda h: h["tool"])
            for tool_name, tool_histories in groupby(all_histories, key=lambda ch: ch["tool"]):
                tool_histories = list(tool_histories)
                history_by_tool[tool_name] = tool_histories

        context.user_request = request.userRequest
        context.current_bpmn = request.currentProcess or ""
        context.solution_id = request.solutionId
        context.project_key = request.projectKey
        context.image_data = request.image
        context.chat_history = history_by_tool
        context.override_model_type = request.modelProvider
        # Build extension eval data override if provided
        context.extension_data_override = self._build_extension_data_override(request)
        context.support_validation = False

    def _build_planner_chat_history_prompt(self, chatHistories: list[ChatHistory]) -> str:
        history = ""
        for chatHistory in chatHistories:
            history += self.config["prompt"]["planner_messages"].format(
                request=chatHistory["request"], response="The tool is " + chatHistory["tool"], timestamp=str(chatHistory["timestamp"])
            )
            history += "\n"
        return history

    def _build_extension_data_override(self, request: BusinessProcessAssistantRequest) -> ExtensionDataOverride | None:
        if not request.extensionDataOverride:
            return None

        extension_data = request.extensionDataOverride or {}
        extension_data_override = ExtensionDataOverride()
        extension_data_override.processes = [
            Process(
                name=process.get("name"),
                fakeId=None,
                type=process.get("type"),
                score=0,
                id=process.get("id"),
                description=process.get("description"),
            )
            for process in extension_data.get("processes") or []
        ]
        extension_data_override.actionApps = [
            ActionApp(
                fakeId=None,
                id=action_app.get("id"),
                name=action_app.get("name"),
                systemName=action_app.get("systemName"),
                deployVersion=action_app.get("deployVersion"),
                type="Actions.HITL",
                score=0,
            )
            for action_app in extension_data.get("actionApps") or []
        ]
        extension_data_override.connections = [
            Connector(
                fakeId=None,
                key=connector.get("key"),
                name=connector.get("name"),
                connection=Connection(
                    name=connector.get("connection").get("name"), id=connector.get("connection").get("id"), isDefault=True, state="Active", instanceId=0
                ),
                activity=None,
                trigger=None,
                description=connector.get("description"),
                type="",
                score=0,
                inputJson=None,
                resourceType=None,
            )
            for connector in extension_data.get("connections") or []
        ]
        extension_data_override.activities = {
            connector: [
                Activity(
                    name=activity.get("name"),
                    displayName=activity.get("displayName"),
                    description=activity.get("description"),
                    objectName=activity.get("objectName"),
                    eventOperation=activity.get("eventOperation"),
                )
                for activity in activities
            ]
            for connector, activities in (extension_data.get("activities") or {}).items()
        }

        extension_data_override.triggers = {
            connector: [
                Activity(
                    name=activity.get("name"),
                    displayName=activity.get("displayName"),
                    description=activity.get("description"),
                    objectName=activity.get("objectName"),
                    eventOperation=activity.get("eventOperation"),
                )
                for activity in activities
            ]
            for connector, activities in (extension_data.get("triggers") or {}).items()
        }

        extension_data_override.queues = [
            SolutionResource(
                fakeId=None,
                id=queue.get("id"),
                name=queue.get("name"),
                description=queue.get("description"),
                type="",
                score=0,
                kind=ResourceKinds.QUEUE,
                folder=None,
                inputJson=None,
                resourceType=None,
            )
            for queue in extension_data.get("queues") or []
        ]

        return extension_data_override
