from typing_extensions import override

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    BpmnElementTypes,
    ExtensionTypes,
    ImplementationTypes,
    ResourceKinds,
    ResourceTypes,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    ExtensionDataOverride,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_solution_resource_task import BpmnExtensionSolutionResourceTask
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class BpmnExtensionAgenticProcessTask(BpmnExtensionSolutionResourceTask):
    def __init__(self):
        super().__init__(ImplementationTypes.AGENTIC_PROCESS, ResourceKinds.PROCESS, [ResourceTypes.PROCESS_ORCHESTRATION])

    @override
    def _get_task_type(self, plan: BpmnExtensionPlan) -> str:
        return BpmnElementTypes.CALL_ACTIVITY

    @override
    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        return ExtensionTypes.WAIT_AGENTIC_PROCESS if plan.wait_for_completion else ExtensionTypes.AGENTIC_PROCESS

    @override
    def _get_eval_dataset(self, extension_data_override: ExtensionDataOverride) -> list[SolutionResource]:
        return []
