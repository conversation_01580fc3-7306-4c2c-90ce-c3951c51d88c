from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    BpmnRequestContext,
    Connector,
    ElementExtension,
    Process,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()


class BpmnExtensionBaseTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("extension/suggestion_prompt.yaml")

    @log_execution_time("bpmn_extension_base_task.generate")
    async def generate(
        self,
        context: BpmnRequestContext,
        plans: list[BpmnExtensionPlan],
        connectors: list[Connector],
        external_agents: list[Process],
        model_name: str,
        chat_history: str,
    ) -> list[ElementExtension]:
        raise NotImplementedError
