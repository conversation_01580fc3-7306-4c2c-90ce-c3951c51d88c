from typing_extensions import override

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    BpmnElementTypes,
    ExtensionTypes,
    ImplementationTypes,
    ResourceKinds,
    ResourceTypes,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    ExtensionDataOverride,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_solution_resource_task import BpmnExtensionSolutionResourceTask
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class BpmnExtensionActionTask(BpmnExtensionSolutionResourceTask):
    def __init__(self):
        super().__init__(ImplementationTypes.HITL, ResourceKinds.APP, [ResourceTypes.WORKFLOW_ACTION, ResourceTypes.VB_ACTION])

    @override
    def _get_task_type(self, plan: BpmnExtensionPlan) -> str:
        return BpmnElementTypes.USER

    @override
    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        return ExtensionTypes.ACTION

    @override
    def _get_eval_dataset(self, extension_data_override: ExtensionDataOverride) -> list[SolutionResource]:
        return [
            SolutionResource(
                fakeId=action_app.fakeId,
                name=action_app.name,
                score=action_app.score,
                description="",
                id=action_app.id,
                type=action_app.type,
                kind=ResourceKinds.APP,
                folder=None,
                inputJson=None,
            )
            for action_app in extension_data_override.actionApps or []
        ]
