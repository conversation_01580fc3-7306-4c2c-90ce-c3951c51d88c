<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" targetNamespace="http://www.omg.org/spec/BPMN/20100524/DI"  elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xsd:import namespace="http://www.omg.org/spec/DD/20100524/DC" schemaLocation="DC.xsd" />
	<xsd:import namespace="http://www.omg.org/spec/DD/20100524/DI" schemaLocation="DI.xsd" />
	
	<xsd:element name="BPMNDiagram" type="bpmndi:BPMNDiagram" />
	<xsd:element name="BPMNPlane" type="bpmndi:BPMNPlane" />
	<xsd:element name="BPMNLabelStyle" type="bpmndi:BPMNLabelStyle" />
	<xsd:element name="BPMNShape" type="bpmndi:BPMNShape" substitutionGroup="di:DiagramElement" />
	<xsd:element name="BPMNLabel" type="bpmndi:BPMNLabel" />
	<xsd:element name="BPMNEdge" type="bpmndi:BPMNEdge" substitutionGroup="di:DiagramElement" />
	
	<xsd:complexType name="BPMNDiagram">
		<xsd:complexContent>
			<xsd:extension base="di:Diagram">
				<xsd:sequence>
					<xsd:element ref="bpmndi:BPMNPlane" />
					<xsd:element ref="bpmndi:BPMNLabelStyle" maxOccurs="unbounded" minOccurs="0" />
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:complexType name="BPMNPlane">
		<xsd:complexContent>
			<xsd:extension base="di:Plane">
				<xsd:attribute name="bpmnElement" type="xsd:QName" />
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:complexType name="BPMNEdge">
		<xsd:complexContent>
			<xsd:extension base="di:LabeledEdge">
				<xsd:sequence>
					<xsd:element ref="bpmndi:BPMNLabel" minOccurs="0" />
				</xsd:sequence>
				<xsd:attribute name="bpmnElement" type="xsd:QName" />
				<xsd:attribute name="sourceElement" type="xsd:QName" />
				<xsd:attribute name="targetElement" type="xsd:QName" />
				<xsd:attribute name="messageVisibleKind" type="bpmndi:MessageVisibleKind" />
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:complexType name="BPMNShape">
		<xsd:complexContent>
			<xsd:extension base="di:LabeledShape">
				<xsd:sequence>
					<xsd:element ref="bpmndi:BPMNLabel" minOccurs="0" />
				</xsd:sequence>
				<xsd:attribute name="bpmnElement" type="xsd:QName" />
				<xsd:attribute name="isHorizontal" type="xsd:boolean" />
				<xsd:attribute name="isExpanded" type="xsd:boolean" />
				<xsd:attribute name="isMarkerVisible" type="xsd:boolean" />
				<xsd:attribute name="isMessageVisible" type="xsd:boolean" />
				<xsd:attribute name="participantBandKind" type="bpmndi:ParticipantBandKind" />
        		<xsd:attribute name="choreographyActivityShape" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:complexType name="BPMNLabel">
		<xsd:complexContent>
			<xsd:extension base="di:Label">
				<xsd:attribute name="labelStyle" type="xsd:QName" />
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:complexType name="BPMNLabelStyle">
		<xsd:complexContent>
			<xsd:extension base="di:Style">
				<xsd:sequence>
					<xsd:element ref="dc:Font" />
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="ParticipantBandKind">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="top_initiating" />
			<xsd:enumeration value="middle_initiating" />
			<xsd:enumeration value="bottom_initiating" />
			<xsd:enumeration value="top_non_initiating" />
			<xsd:enumeration value="middle_non_initiating" />
			<xsd:enumeration value="bottom_non_initiating" />
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:simpleType name="MessageVisibleKind">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="initiating" />
			<xsd:enumeration value="non_initiating" />
		</xsd:restriction>
	</xsd:simpleType>

</xsd:schema>
