<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema elementFormDefault="qualified" attributeFormDefault="unqualified"
	xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" 
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
	targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL">
	
	<xsd:element name="activity" type="tActivity"/>
	<xsd:complexType name="tActivity" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tFlowNode">
				<xsd:sequence>
					<xsd:element ref="ioSpecification" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="property" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="dataInputAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="dataOutputAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="resourceRole" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="loopCharacteristics" minOccurs="0"/>
				</xsd:sequence>
				<xsd:attribute name="isForCompensation" type="xsd:boolean" default="false"/>
				<xsd:attribute name="startQuantity" type="xsd:integer" default="1"/>
				<xsd:attribute name="completionQuantity" type="xsd:integer" default="1"/>
				<xsd:attribute name="default" type="xsd:IDREF" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="adHocSubProcess" type="tAdHocSubProcess" substitutionGroup="flowElement"/>
	<xsd:complexType name="tAdHocSubProcess">
		<xsd:complexContent>
			<xsd:extension base="tSubProcess">
				<xsd:sequence>
					<xsd:element name="completionCondition" type="tExpression" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="cancelRemainingInstances" type="xsd:boolean" default="true"/>
				<xsd:attribute name="ordering" type="tAdHocOrdering"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="tAdHocOrdering">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Parallel"/>
			<xsd:enumeration value="Sequential"/>
		</xsd:restriction>
	</xsd:simpleType>	

	<xsd:element name="artifact" type="tArtifact"/>
	<xsd:complexType name="tArtifact" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="assignment" type="tAssignment" />
	<xsd:complexType name="tAssignment">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="from" type="tExpression" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="to" type="tExpression" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="association" type="tAssociation" substitutionGroup="artifact"/>
	<xsd:complexType name="tAssociation">
		<xsd:complexContent>
			<xsd:extension base="tArtifact">
				<xsd:attribute name="sourceRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="targetRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="associationDirection" type="tAssociationDirection" default="None"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="tAssociationDirection">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="One"/>
			<xsd:enumeration value="Both"/>
		</xsd:restriction>
	</xsd:simpleType>

	<xsd:element name="auditing" type="tAuditing"/>
	<xsd:complexType name="tAuditing">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="baseElement" type="tBaseElement"/>
	<xsd:complexType name="tBaseElement" abstract="true">
		<xsd:sequence>
			<xsd:element ref="documentation" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element ref="extensionElements" minOccurs="0" maxOccurs="1" /> 
		</xsd:sequence>
		<xsd:attribute name="id" type="xsd:ID" use="optional"/>
		<xsd:anyAttribute namespace="##other" processContents="lax"/>
	</xsd:complexType>
	
	<xsd:element name="baseElementWithMixedContent" type="tBaseElementWithMixedContent"/>
	<xsd:complexType name="tBaseElementWithMixedContent" abstract="true" mixed="true">
		<xsd:sequence>
			<xsd:element ref="documentation" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element ref="extensionElements" minOccurs="0" maxOccurs="1" /> 
		</xsd:sequence>
		<xsd:attribute name="id" type="xsd:ID" use="optional"/>
		<xsd:anyAttribute namespace="##other" processContents="lax"/>
	</xsd:complexType>

	<xsd:element name="boundaryEvent" type="tBoundaryEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tBoundaryEvent">
		<xsd:complexContent>
			<xsd:extension base="tCatchEvent">
				<xsd:attribute name="cancelActivity" type="xsd:boolean" default="true"/>
				<xsd:attribute name="attachedToRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>			

	<xsd:element name="businessRuleTask" type="tBusinessRuleTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tBusinessRuleTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:attribute name="implementation" type="tImplementation" default="##unspecified"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="callableElement" type="tCallableElement"/>
	<xsd:complexType name="tCallableElement">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element name="supportedInterfaceRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="ioSpecification" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="ioBinding" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="callActivity" type="tCallActivity" substitutionGroup="flowElement"/>
	<xsd:complexType name="tCallActivity">
		<xsd:complexContent>
			<xsd:extension base="tActivity">
				<xsd:attribute name="calledElement" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="callChoreography" type="tCallChoreography" substitutionGroup="flowElement"/>
	<xsd:complexType name="tCallChoreography">
		<xsd:complexContent>
			<xsd:extension base="tChoreographyActivity">
				<xsd:sequence>
					<xsd:element ref="participantAssociation" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="calledChoreographyRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="callConversation" type="tCallConversation" substitutionGroup="conversationNode"/>
	<xsd:complexType name="tCallConversation">
		<xsd:complexContent>
			<xsd:extension base="tConversationNode">
				<xsd:sequence>
					<xsd:element ref="participantAssociation" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="calledCollaborationRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="cancelEventDefinition" type="tCancelEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tCancelEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="catchEvent" type="tCatchEvent"/>
	<xsd:complexType name="tCatchEvent" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tEvent">
				<xsd:sequence>
					<xsd:element ref="dataOutput" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="dataOutputAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="outputSet" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="eventDefinition" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="eventDefinitionRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="parallelMultiple" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="category" type="tCategory" substitutionGroup="rootElement"/>
	<xsd:complexType name="tCategory">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="categoryValue" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="categoryValue" type="tCategoryValue"/>
	<xsd:complexType name="tCategoryValue">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="value" type="xsd:string" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="choreography" type="tChoreography" substitutionGroup="collaboration"/>
	<xsd:complexType name="tChoreography">
		<xsd:complexContent>
			<xsd:extension base="tCollaboration">
				<xsd:sequence>
					<xsd:element ref="flowElement" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="choreographyActivity" type="tChoreographyActivity"/>
	<xsd:complexType name="tChoreographyActivity" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tFlowNode">
				<xsd:sequence>
					<xsd:element name="participantRef" type="xsd:QName" minOccurs="2" maxOccurs="unbounded"/>
					<xsd:element ref="correlationKey" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="initiatingParticipantRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="loopType" type="tChoreographyLoopType" default="None"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="tChoreographyLoopType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="Standard"/>
			<xsd:enumeration value="MultiInstanceSequential"/>
			<xsd:enumeration value="MultiInstanceParallel"/>
		</xsd:restriction>
	</xsd:simpleType>	
	
	<xsd:element name="choreographyTask" type="tChoreographyTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tChoreographyTask">
		<xsd:complexContent>
			<xsd:extension base="tChoreographyActivity">
				<xsd:sequence>
					<xsd:element name="messageFlowRef" type="xsd:QName" minOccurs="1" maxOccurs="2"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="collaboration" type="tCollaboration" substitutionGroup="rootElement"/>
	<xsd:complexType name="tCollaboration">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="participant" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="messageFlow" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="artifact" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="conversationNode" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="conversationAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="participantAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="messageFlowAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="correlationKey" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="choreographyRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="conversationLink" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="isClosed" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="compensateEventDefinition" type="tCompensateEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tCompensateEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:attribute name="waitForCompletion" type="xsd:boolean"/>
				<xsd:attribute name="activityRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="complexBehaviorDefinition" type="tComplexBehaviorDefinition"/>
	<xsd:complexType name="tComplexBehaviorDefinition">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="condition" type="tFormalExpression" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="event" type="tImplicitThrowEvent" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="complexGateway" type="tComplexGateway" substitutionGroup="flowElement"/>
	<xsd:complexType name="tComplexGateway">
		<xsd:complexContent>
			<xsd:extension base="tGateway">
				<xsd:sequence>
					<xsd:element name="activationCondition" type="tExpression" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="default" type="xsd:IDREF"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="conditionalEventDefinition" type="tConditionalEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tConditionalEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:sequence>
					<xsd:element name="condition" type="tExpression"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="conversation" type="tConversation" substitutionGroup="conversationNode"/>
	<xsd:complexType name="tConversation">
		<xsd:complexContent>
			<xsd:extension base="tConversationNode"/>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="conversationAssociation" type="tConversationAssociation"/>
	<xsd:complexType name="tConversationAssociation">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="innerConversationNodeRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="outerConversationNodeRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="conversationLink" type="tConversationLink"/> 
	<xsd:complexType name="tConversationLink"> 
		<xsd:complexContent> 
			<xsd:extension base="tBaseElement"> 
				<xsd:attribute name="name" type="xsd:string" use="optional"/> 
				<xsd:attribute name="sourceRef" type="xsd:QName" use="required"/> 
				<xsd:attribute name="targetRef" type="xsd:QName" use="required"/> 
			</xsd:extension> 
		</xsd:complexContent> 
	</xsd:complexType>
  
	<xsd:element name="conversationNode" type="tConversationNode"/>
	<xsd:complexType name="tConversationNode" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="participantRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="messageFlowRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="correlationKey" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="correlationKey" type="tCorrelationKey"/>
	<xsd:complexType name="tCorrelationKey">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="correlationPropertyRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="correlationProperty" type="tCorrelationProperty" substitutionGroup="rootElement"/>
	<xsd:complexType name="tCorrelationProperty">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="correlationPropertyRetrievalExpression" minOccurs="1" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="optional"/>
				<xsd:attribute name="type" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="correlationPropertyBinding" type="tCorrelationPropertyBinding"/>
	<xsd:complexType name="tCorrelationPropertyBinding">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="dataPath" type="tFormalExpression" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="correlationPropertyRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="correlationPropertyRetrievalExpression" type="tCorrelationPropertyRetrievalExpression"/>
	<xsd:complexType name="tCorrelationPropertyRetrievalExpression">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="messagePath" type="tFormalExpression" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="messageRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="correlationSubscription" type="tCorrelationSubscription"/>
	<xsd:complexType name="tCorrelationSubscription">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="correlationPropertyBinding" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="correlationKeyRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="dataAssociation" type="tDataAssociation" />
	<xsd:complexType name="tDataAssociation">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="sourceRef" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="targetRef" type="xsd:IDREF" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="transformation" type="tFormalExpression" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="assignment" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="dataInput" type="tDataInput" />
	<xsd:complexType name="tDataInput">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="optional"/>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName" />
				<xsd:attribute name="isCollection" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataInputAssociation" type="tDataInputAssociation" />
	<xsd:complexType name="tDataInputAssociation">
		<xsd:complexContent>
			<xsd:extension base="tDataAssociation"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataObject" type="tDataObject" substitutionGroup="flowElement"/>
	<xsd:complexType name="tDataObject">
		<xsd:complexContent>
			<xsd:extension base="tFlowElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
				<xsd:attribute name="isCollection" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataObjectReference" type="tDataObjectReference" substitutionGroup="flowElement"/>
	<xsd:complexType name="tDataObjectReference">
		<xsd:complexContent>
			<xsd:extension base="tFlowElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
				<xsd:attribute name="dataObjectRef" type="xsd:IDREF"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataOutput" type="tDataOutput" />
	<xsd:complexType name="tDataOutput">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="optional" />
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
				<xsd:attribute name="isCollection" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataOutputAssociation" type="tDataOutputAssociation" />
	<xsd:complexType name="tDataOutputAssociation">
		<xsd:complexContent>
			<xsd:extension base="tDataAssociation"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataState" type="tDataState" />
	<xsd:complexType name="tDataState">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataStore" type="tDataStore" substitutionGroup="rootElement"/>
	<xsd:complexType name="tDataStore">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="capacity" type="xsd:integer"/>
				<xsd:attribute name="isUnlimited" type="xsd:boolean" default="true"/>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="dataStoreReference" type="tDataStoreReference" substitutionGroup="flowElement"/>
	<xsd:complexType name="tDataStoreReference">
		<xsd:complexContent>
			<xsd:extension base="tFlowElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
				<xsd:attribute name="dataStoreRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="documentation" type="tDocumentation"/>
	<xsd:complexType name="tDocumentation" mixed="true">
		<xsd:sequence>
			<xsd:any namespace="##any" processContents="lax" minOccurs="0"/>
		</xsd:sequence>
		<xsd:attribute name="id" type="xsd:ID" use="optional"/>
		<xsd:attribute name="textFormat" type="xsd:string" default="text/plain"/>
	</xsd:complexType>
	
	<xsd:element name="endEvent" type="tEndEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tEndEvent">
		<xsd:complexContent>
			<xsd:extension base="tThrowEvent"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="endPoint" type="tEndPoint" substitutionGroup="rootElement"/>
	<xsd:complexType name="tEndPoint">
		<xsd:complexContent>
			<xsd:extension base="tRootElement"/>
		</xsd:complexContent>
	</xsd:complexType>		
	
	<xsd:element name="error" type="tError" substitutionGroup="rootElement"/>
	<xsd:complexType name="tError">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="errorCode" type="xsd:string"/>
				<xsd:attribute name="structureRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="errorEventDefinition" type="tErrorEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tErrorEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:attribute name="errorRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="escalation" type="tEscalation" substitutionGroup="rootElement"/>	
	<xsd:complexType name="tEscalation">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="escalationCode" type="xsd:string"/>
				<xsd:attribute name="structureRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="escalationEventDefinition" type="tEscalationEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tEscalationEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:attribute name="escalationRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="event" type="tEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tEvent" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tFlowNode">
				<xsd:sequence>
					<xsd:element ref="property" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="eventBasedGateway" type="tEventBasedGateway" substitutionGroup="flowElement"/>
	<xsd:complexType name="tEventBasedGateway">
		<xsd:complexContent>
			<xsd:extension base="tGateway">
				<xsd:attribute name="instantiate" type="xsd:boolean" default="false"/>
				<xsd:attribute name="eventGatewayType" type="tEventBasedGatewayType" default="Exclusive"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:simpleType name="tEventBasedGatewayType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Exclusive"/>
			<xsd:enumeration value="Parallel"/>
		</xsd:restriction>
	</xsd:simpleType>	

	<xsd:element name="eventDefinition" type="tEventDefinition" substitutionGroup="rootElement"/>
	<xsd:complexType name="tEventDefinition" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tRootElement"/>
		</xsd:complexContent>
	</xsd:complexType>			

	<xsd:element name="exclusiveGateway" type="tExclusiveGateway" substitutionGroup="flowElement"/>
	<xsd:complexType name="tExclusiveGateway">
		<xsd:complexContent>
			<xsd:extension base="tGateway">
				<xsd:attribute name="default" type="xsd:IDREF" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="expression" type="tExpression"/>
	<xsd:complexType name="tExpression">
		<xsd:complexContent>
			<xsd:extension base="tBaseElementWithMixedContent"/>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="extension" type="tExtension"/>
	<xsd:complexType name="tExtension">
		<xsd:sequence>
			<xsd:element ref="documentation" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
		<xsd:attribute name="definition" type="xsd:QName"/>
		<xsd:attribute name="mustUnderstand" type="xsd:boolean" use="optional" default="false"/>
	</xsd:complexType>
	
	<xsd:element name="extensionElements" type="tExtensionElements" /> 
	<xsd:complexType name="tExtensionElements">
		<xsd:sequence>
			<xsd:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded" /> 
		</xsd:sequence>
	</xsd:complexType>

	<xsd:element name="flowElement" type="tFlowElement"/>
	<xsd:complexType name="tFlowElement" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="auditing" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="monitoring" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="categoryValueRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="flowNode" type="tFlowNode"/>
	<xsd:complexType name="tFlowNode" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tFlowElement">
				<xsd:sequence>
					<xsd:element name="incoming" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="outgoing" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="formalExpression" type="tFormalExpression" substitutionGroup="expression"/>
	<xsd:complexType name="tFormalExpression">
		<xsd:complexContent>
			<xsd:extension base="tExpression">
				<xsd:attribute name="language" type="xsd:anyURI" use="optional"/>
				<xsd:attribute name="evaluatesToTypeRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="gateway" type="tGateway" abstract="true"/>
	<xsd:complexType name="tGateway">
		<xsd:complexContent>
			<xsd:extension base="tFlowNode">
				<xsd:attribute name="gatewayDirection" type="tGatewayDirection" default="Unspecified"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:simpleType name="tGatewayDirection">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Unspecified"/>
			<xsd:enumeration value="Converging"/>
			<xsd:enumeration value="Diverging"/>
			<xsd:enumeration value="Mixed"/>
		</xsd:restriction>
	</xsd:simpleType>	
	
	<xsd:element name="globalBusinessRuleTask" type="tGlobalBusinessRuleTask" substitutionGroup="rootElement"/>
	<xsd:complexType name="tGlobalBusinessRuleTask">
		<xsd:complexContent>
			<xsd:extension base="tGlobalTask">
				<xsd:attribute name="implementation" type="tImplementation" default="##unspecified"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="globalChoreographyTask" type="tGlobalChoreographyTask" substitutionGroup="choreography"/>
	<xsd:complexType name="tGlobalChoreographyTask">
		<xsd:complexContent>
			<xsd:extension base="tChoreography">
				<xsd:attribute name="initiatingParticipantRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:element name="globalConversation" type="tGlobalConversation" substitutionGroup="collaboration"/>
	<xsd:complexType name="tGlobalConversation">
		<xsd:complexContent>
			<xsd:extension base="tCollaboration"/>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="globalManualTask" type="tGlobalManualTask" substitutionGroup="rootElement"/>
	<xsd:complexType name="tGlobalManualTask">
		<xsd:complexContent>
			<xsd:extension base="tGlobalTask"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="globalScriptTask" type="tGlobalScriptTask"  substitutionGroup="rootElement"/>
	<xsd:complexType name="tGlobalScriptTask">
		<xsd:complexContent>
			<xsd:extension base="tGlobalTask">
				<xsd:sequence>
					<xsd:element ref="script" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="scriptLanguage" type="xsd:anyURI"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="globalTask" type="tGlobalTask" substitutionGroup="rootElement"/>
	<xsd:complexType name="tGlobalTask">
		<xsd:complexContent>
			<xsd:extension base="tCallableElement">
				<xsd:sequence>
					<xsd:element ref="resourceRole" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="globalUserTask" type="tGlobalUserTask" substitutionGroup="rootElement"/>
	<xsd:complexType name="tGlobalUserTask">
		<xsd:complexContent>
			<xsd:extension base="tGlobalTask">
				<xsd:sequence>
					<xsd:element ref="rendering" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="implementation" type="tImplementation" default="##unspecified"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="group" type="tGroup" substitutionGroup="artifact"/>
	<xsd:complexType name="tGroup">
		<xsd:complexContent>
			<xsd:extension base="tArtifact">
				<xsd:attribute name="categoryValueRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="humanPerformer" type="tHumanPerformer" substitutionGroup="performer"/>
	<xsd:complexType name="tHumanPerformer">
		<xsd:complexContent>
			<xsd:extension base="tPerformer"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:simpleType name="tImplementation">
		<xsd:union memberTypes="xsd:anyURI">
			<xsd:simpleType>
				<xsd:restriction base="xsd:token">
					<xsd:enumeration value="##unspecified" /> 
					<xsd:enumeration value="##WebService" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:union>
	</xsd:simpleType>

	<xsd:element name="implicitThrowEvent" type="tImplicitThrowEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tImplicitThrowEvent">
		<xsd:complexContent>
			<xsd:extension base="tThrowEvent"/>
		</xsd:complexContent>
	</xsd:complexType>				
	
	<xsd:element name="inclusiveGateway" type="tInclusiveGateway" substitutionGroup="flowElement"/>
	<xsd:complexType name="tInclusiveGateway">
		<xsd:complexContent>
			<xsd:extension base="tGateway">
				<xsd:attribute name="default" type="xsd:IDREF" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="inputSet" type="tInputSet" />
	<xsd:complexType name="tInputSet">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="dataInputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="optionalInputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="whileExecutingInputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="outputSetRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" />
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="interface" type="tInterface" substitutionGroup="rootElement"/>
	<xsd:complexType name="tInterface">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="operation" minOccurs="1" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="required"/>
				<xsd:attribute name="implementationRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="intermediateCatchEvent" type="tIntermediateCatchEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tIntermediateCatchEvent">
		<xsd:complexContent>
			<xsd:extension base="tCatchEvent"/>
		</xsd:complexContent>
	</xsd:complexType>			

	<xsd:element name="intermediateThrowEvent" type="tIntermediateThrowEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tIntermediateThrowEvent">
		<xsd:complexContent>
			<xsd:extension base="tThrowEvent"/>
		</xsd:complexContent>
	</xsd:complexType>				

	<xsd:element name="ioBinding" type="tInputOutputBinding" />
	<xsd:complexType name="tInputOutputBinding">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="operationRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="inputDataRef" type="xsd:IDREF" use="required"/>
				<xsd:attribute name="outputDataRef" type="xsd:IDREF" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="ioSpecification" type="tInputOutputSpecification" />
	<xsd:complexType name="tInputOutputSpecification">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="dataInput" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="dataOutput" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="inputSet" minOccurs="1" maxOccurs="unbounded"/>
					<xsd:element ref="outputSet" minOccurs="1" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="itemDefinition" type="tItemDefinition" substitutionGroup="rootElement"/>	
	<xsd:complexType name="tItemDefinition">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:attribute name="structureRef" type="xsd:QName"/>
				<xsd:attribute name="isCollection" type="xsd:boolean" default="false"/>
				<xsd:attribute name="itemKind" type="tItemKind" default="Information"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>		
	
	<xsd:simpleType name="tItemKind">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Information"/>
			<xsd:enumeration value="Physical"/>
		</xsd:restriction>
	</xsd:simpleType>

	<xsd:element name="lane" type="tLane"/>
	<xsd:complexType name="tLane">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="partitionElement" type="tBaseElement" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="flowNodeRef" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="childLaneSet" type="tLaneSet" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="partitionElementRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="laneSet" type="tLaneSet"/>
	<xsd:complexType name="tLaneSet">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="lane" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="linkEventDefinition" type="tLinkEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tLinkEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:sequence>
					<xsd:element name="source" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="target" type="xsd:QName" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="loopCharacteristics" type="tLoopCharacteristics"/>
	<xsd:complexType name="tLoopCharacteristics" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="manualTask" type="tManualTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tManualTask">
		<xsd:complexContent>
			<xsd:extension base="tTask"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="message" type="tMessage" substitutionGroup="rootElement"/>	
	<xsd:complexType name="tMessage">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="itemRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>		
	
	<xsd:element name="messageEventDefinition" type="tMessageEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tMessageEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:sequence>
					<xsd:element name="operationRef" type="xsd:QName" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="messageRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="messageFlow" type="tMessageFlow"/>
	<xsd:complexType name="tMessageFlow">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="name" type="xsd:string" use="optional"/>
				<xsd:attribute name="sourceRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="targetRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="messageRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="messageFlowAssociation" type="tMessageFlowAssociation"/>
	<xsd:complexType name="tMessageFlowAssociation">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="innerMessageFlowRef" type="xsd:QName" use="required"/>
				<xsd:attribute name="outerMessageFlowRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="monitoring" type="tMonitoring"/>
	<xsd:complexType name="tMonitoring">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="multiInstanceLoopCharacteristics" type="tMultiInstanceLoopCharacteristics"  substitutionGroup="loopCharacteristics"/>
	<xsd:complexType name="tMultiInstanceLoopCharacteristics">
		<xsd:complexContent>
			<xsd:extension base="tLoopCharacteristics">
				<xsd:sequence>
					<xsd:element name="loopCardinality" type="tExpression" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="loopDataInputRef" type="xsd:QName" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="loopDataOutputRef" type="xsd:QName" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="inputDataItem" type="tDataInput" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="outputDataItem" type="tDataOutput" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="complexBehaviorDefinition" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="completionCondition" type="tExpression" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="isSequential" type="xsd:boolean" default="false"/>
				<xsd:attribute name="behavior" type="tMultiInstanceFlowCondition" default="All"/>
				<xsd:attribute name="oneBehaviorEventRef" type="xsd:QName" use="optional"/>
				<xsd:attribute name="noneBehaviorEventRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:simpleType name="tMultiInstanceFlowCondition">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="One"/>
			<xsd:enumeration value="All"/>
			<xsd:enumeration value="Complex"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:element name="operation" type="tOperation"/>
	<xsd:complexType name="tOperation">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="inMessageRef" type="xsd:QName" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="outMessageRef" type="xsd:QName" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="errorRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="required"/>
				<xsd:attribute name="implementationRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="outputSet" type="tOutputSet" />
	<xsd:complexType name="tOutputSet">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="dataOutputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="optionalOutputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="whileExecutingOutputRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="inputSetRefs" type="xsd:IDREF" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="parallelGateway" type="tParallelGateway" substitutionGroup="flowElement"/>
	<xsd:complexType name="tParallelGateway">
		<xsd:complexContent>
			<xsd:extension base="tGateway"/>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="participant" type="tParticipant"/>
	<xsd:complexType name="tParticipant">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="interfaceRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="endPointRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="participantMultiplicity" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="processRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="participantAssociation" type="tParticipantAssociation"/>
	<xsd:complexType name="tParticipantAssociation">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="innerParticipantRef" type="xsd:QName" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="outerParticipantRef" type="xsd:QName" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="participantMultiplicity" type="tParticipantMultiplicity"/>
	<xsd:complexType name="tParticipantMultiplicity">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="minimum" type="xsd:int" default="0"/>
				<xsd:attribute name="maximum" type="xsd:int" default="1"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="partnerEntity" type="tPartnerEntity" substitutionGroup="rootElement"/>
	<xsd:complexType name="tPartnerEntity">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element name="participantRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="partnerRole" type="tPartnerRole" substitutionGroup="rootElement"/>
	<xsd:complexType name="tPartnerRole">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element name="participantRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="performer" type="tPerformer" substitutionGroup="resourceRole"/>
	<xsd:complexType name="tPerformer">
		<xsd:complexContent>
			<xsd:extension base="tResourceRole"/>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="potentialOwner" type="tPotentialOwner" substitutionGroup="performer"/>
	<xsd:complexType name="tPotentialOwner">
		<xsd:complexContent>
			<xsd:extension base="tHumanPerformer"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="process" type="tProcess" substitutionGroup="rootElement"/>
	<xsd:complexType name="tProcess">
		<xsd:complexContent>
			<xsd:extension base="tCallableElement">
				<xsd:sequence>
					<xsd:element ref="auditing" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="monitoring" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="property" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="laneSet" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="flowElement" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="artifact" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="resourceRole" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="correlationSubscription" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="supports" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="processType" type="tProcessType" default="None"/>
				<xsd:attribute name="isClosed" type="xsd:boolean" default="false"/>
				<xsd:attribute name="isExecutable" type="xsd:boolean"/>
				<xsd:attribute name="definitionalCollaborationRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="tProcessType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="Public"/>
			<xsd:enumeration value="Private"/>
		</xsd:restriction>
	</xsd:simpleType>

	<xsd:element name="property" type="tProperty" />
	<xsd:complexType name="tProperty">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="dataState" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="itemSubjectRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="receiveTask" type="tReceiveTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tReceiveTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:attribute name="implementation" type="tImplementation" default="##WebService"/>
				<xsd:attribute name="instantiate" type="xsd:boolean" default="false"/>
				<xsd:attribute name="messageRef" type="xsd:QName" use="optional"/>
				<xsd:attribute name="operationRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="relationship" type="tRelationship"/>
	<xsd:complexType name="tRelationship">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element name="source" type="xsd:QName" minOccurs="1" maxOccurs="unbounded"/>
					<xsd:element name="target" type="xsd:QName" minOccurs="1" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="type" type="xsd:string" use="required"/>
				<xsd:attribute name="direction" type="tRelationshipDirection"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:simpleType name="tRelationshipDirection">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="Forward"/>
			<xsd:enumeration value="Backward"/>
			<xsd:enumeration value="Both"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:element name="rendering" type="tRendering"/>
	<xsd:complexType name="tRendering">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="resource" type="tResource" substitutionGroup="rootElement"/>
	<xsd:complexType name="tResource">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:sequence>
					<xsd:element ref="resourceParameter" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="name" type="xsd:string" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="resourceAssignmentExpression" type="tResourceAssignmentExpression"/>
	<xsd:complexType name="tResourceAssignmentExpression">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="expression" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="resourceParameter" type="tResourceParameter"/>
	<xsd:complexType name="tResourceParameter">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="type" type="xsd:QName"/>
				<xsd:attribute name="isRequired" type="xsd:boolean"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="resourceParameterBinding" type="tResourceParameterBinding"/>
	<xsd:complexType name="tResourceParameterBinding">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement">
				<xsd:sequence>
					<xsd:element ref="expression" minOccurs="1" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="parameterRef" type="xsd:QName" use="required"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="resourceRole" type="tResourceRole"/>
	<xsd:complexType name="tResourceRole"> 
		<xsd:complexContent> 
			<xsd:extension base="tBaseElement"> 
				<xsd:choice> 
					<xsd:sequence> 
						<xsd:element name="resourceRef" type="xsd:QName"/> 
						<xsd:element ref="resourceParameterBinding" minOccurs="0" maxOccurs="unbounded"/> 
					</xsd:sequence> 
					<xsd:element ref="resourceAssignmentExpression" minOccurs="0" maxOccurs="1"/> 
				</xsd:choice>
				<xsd:attribute name="name" type="xsd:string"/>
			</xsd:extension> 
		</xsd:complexContent> 
	</xsd:complexType>

	<xsd:element name="rootElement" type="tRootElement"/>
	<xsd:complexType name="tRootElement" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tBaseElement"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="scriptTask" type="tScriptTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tScriptTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:sequence>
					<xsd:element ref="script" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="scriptFormat" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>		
	
	<xsd:element name="script" type="tScript"/>
	<xsd:complexType name="tScript" mixed="true">
		<xsd:sequence>
			<xsd:any namespace="##any" processContents="lax" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>	

	<xsd:element name="sendTask" type="tSendTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tSendTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:attribute name="implementation" type="tImplementation" default="##WebService"/>
				<xsd:attribute name="messageRef" type="xsd:QName" use="optional"/>
				<xsd:attribute name="operationRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="sequenceFlow" type="tSequenceFlow" substitutionGroup="flowElement"/>
	<xsd:complexType name="tSequenceFlow">
		<xsd:complexContent>
			<xsd:extension base="tFlowElement">
				<xsd:sequence>
					<xsd:element name="conditionExpression"  type="tExpression" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="sourceRef" type="xsd:IDREF" use="required"/>
				<xsd:attribute name="targetRef" type="xsd:IDREF" use="required"/>
				<xsd:attribute name="isImmediate" type="xsd:boolean" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="serviceTask" type="tServiceTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tServiceTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:attribute name="implementation" type="tImplementation" default="##WebService"/>
				<xsd:attribute name="operationRef" type="xsd:QName" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	
	
	<xsd:element name="signal" type="tSignal" substitutionGroup="rootElement"/>	
	<xsd:complexType name="tSignal">
		<xsd:complexContent>
			<xsd:extension base="tRootElement">
				<xsd:attribute name="name" type="xsd:string"/>
				<xsd:attribute name="structureRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="signalEventDefinition" type="tSignalEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tSignalEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:attribute name="signalRef" type="xsd:QName"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="standardLoopCharacteristics" type="tStandardLoopCharacteristics"  substitutionGroup="loopCharacteristics"/>
	<xsd:complexType name="tStandardLoopCharacteristics">
		<xsd:complexContent>
			<xsd:extension base="tLoopCharacteristics">
				<xsd:sequence>
					<xsd:element name="loopCondition" type="tExpression" minOccurs="0"/>
				</xsd:sequence>
				<xsd:attribute name="testBefore" type="xsd:boolean" default="false"/>
				<xsd:attribute name="loopMaximum" type="xsd:integer" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>	

	<xsd:element name="startEvent" type="tStartEvent" substitutionGroup="flowElement"/>
	<xsd:complexType name="tStartEvent">
		<xsd:complexContent>
			<xsd:extension base="tCatchEvent">
				<xsd:attribute name="isInterrupting" type="xsd:boolean" default="true"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>			
	
	<xsd:element name="subChoreography" type="tSubChoreography" substitutionGroup="flowElement"/>
	<xsd:complexType name="tSubChoreography">
		<xsd:complexContent>
			<xsd:extension base="tChoreographyActivity">
				<xsd:sequence>
					<xsd:element ref="flowElement" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="artifact" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="subConversation" type="tSubConversation" substitutionGroup="conversationNode"/>
	<xsd:complexType name="tSubConversation">
		<xsd:complexContent>
			<xsd:extension base="tConversationNode">
				<xsd:sequence>
					<xsd:element ref="conversationNode" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="subProcess" type="tSubProcess" substitutionGroup="flowElement"/>
	<xsd:complexType name="tSubProcess">
		<xsd:complexContent>
			<xsd:extension base="tActivity">
				<xsd:sequence>
					<xsd:element ref="laneSet" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="flowElement" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="artifact" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="triggeredByEvent" type="xsd:boolean" default="false"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="task" type="tTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tTask">
		<xsd:complexContent>
			<xsd:extension base="tActivity"/>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:element name="terminateEventDefinition" type="tTerminateEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tTerminateEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition"/>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="textAnnotation" type="tTextAnnotation" substitutionGroup="artifact"/>
	<xsd:complexType name="tTextAnnotation">
		<xsd:complexContent>
			<xsd:extension base="tArtifact">
				<xsd:sequence>
					<xsd:element ref="text" minOccurs="0" maxOccurs="1"/>
				</xsd:sequence>
				<xsd:attribute name="textFormat" type="xsd:string" default="text/plain"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="text" type="tText"/>
	<xsd:complexType name="tText" mixed="true">
		<xsd:sequence>
			<xsd:any namespace="##any" processContents="lax" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>

	<xsd:element name="throwEvent" type="tThrowEvent"/>
	<xsd:complexType name="tThrowEvent" abstract="true">
		<xsd:complexContent>
			<xsd:extension base="tEvent">
				<xsd:sequence>
					<xsd:element ref="dataInput" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="dataInputAssociation" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element ref="inputSet" minOccurs="0" maxOccurs="1"/>
					<xsd:element ref="eventDefinition" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="eventDefinitionRef" type="xsd:QName" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>			
	
	<xsd:element name="timerEventDefinition" type="tTimerEventDefinition" substitutionGroup="eventDefinition"/>
	<xsd:complexType name="tTimerEventDefinition">
		<xsd:complexContent>
			<xsd:extension base="tEventDefinition">
				<xsd:choice>
					<xsd:element name="timeDate" type="tExpression" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="timeDuration" type="tExpression" minOccurs="0" maxOccurs="1"/>
					<xsd:element name="timeCycle" type="tExpression" minOccurs="0" maxOccurs="1"/>
				</xsd:choice>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

	<xsd:element name="transaction" type="tTransaction" substitutionGroup="flowElement"/>
	<xsd:complexType name="tTransaction">
		<xsd:complexContent>
			<xsd:extension base="tSubProcess">
				<xsd:attribute name="method" type="tTransactionMethod" default="##Compensate"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	
	<xsd:simpleType name="tTransactionMethod">
		<xsd:union memberTypes="xsd:anyURI">
			<xsd:simpleType>
				<xsd:restriction base="xsd:token">
					<xsd:enumeration value="##Compensate" /> 
					<xsd:enumeration value="##Image" />
					<xsd:enumeration value="##Store" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:union>
	</xsd:simpleType>

	<xsd:element name="userTask" type="tUserTask" substitutionGroup="flowElement"/>
	<xsd:complexType name="tUserTask">
		<xsd:complexContent>
			<xsd:extension base="tTask">
				<xsd:sequence>
					<xsd:element ref="rendering" minOccurs="0" maxOccurs="unbounded"/>
				</xsd:sequence>
				<xsd:attribute name="implementation" type="tImplementation" default="##unspecified"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>

</xsd:schema>