import json
import os
import pathlib

import langchain.schema
from langchain.prompts.chat import MessageLikeRepresentation
from langchain_core.runnables import RunnableConfig

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnGenerationToolResult,
    ChatHistory,
    ModelType,
    Tool,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

LOGGER = AppInsightsLogger()

# Mapping of extension types
FILE_PATH_MAPPING = {
    "edit_bpmn_examples": "edit_bpmn_position_examples.md",
    "supported_element_examples": "supported_element_examples.md",
}


class BpmnGenerationPositionTask(BpmnBaseTask):
    def __init__(self) -> None:
        self.edit_bpmn_examples = None
        self.supported_element_examples = None
        self.history_dependencies = [Tool.EDIT_BPMN, Tool.QA, Tool.EXTENSION]
        super().__init__("generation_position_prompt.yaml")

    def _init_config(self, config_file: str):
        super()._init_config(config_file)
        config_path = os.path.join(os.path.dirname(__file__), "config")
        for key, value in FILE_PATH_MAPPING.items():
            setattr(self, key, BpmnBaseTask.read_content(pathlib.Path(os.path.join(config_path, value))))

    def _get_model_name(self, model_type: ModelType) -> str:
        return f"bpmn_generation_chat_model_{model_type.value}"

    async def _generate(
        self,
        query: str,
        current_bpmn: str,
        model_name: str,
        edit_sys_template: str = "edit",
        chat_history: dict[Tool, list[ChatHistory]] | None = None,
        message_emitter: MessageEmitter | None = None,
    ):
        system_message: MessageLikeRepresentation
        user_message: langchain.schema.BaseMessage

        chat_history_str = await self._get_related_chat_history(chat_history, self.history_dependencies, model_name)

        if current_bpmn is not None and current_bpmn != "":
            system_message = self._system_template(self.config["prompt"]["system_template"][edit_sys_template]).format(
                supported_element_examples=self.supported_element_examples,
                edit_bpmn_examples=self.edit_bpmn_examples,
            )

            if "User: pending" in chat_history_str.strip():
                query = f"{query}. Also integrate the latest pending change for edit after the last accepted or declined one."
            user_message = self._human_template(self.config["prompt"]["user_template"]["edit"]).format(
                current_bpmn=current_bpmn, query=query, chat_history=chat_history_str
            )
        else:
            system_message = self._system_template(self.config["prompt"]["system_template"]["create"])
            user_message = self._human_template(self.config["prompt"]["user_template"]["create"]).format(query=query)

        responseBuffer = []
        async for chunk in self._call_llm_stream(system_message, user_message, model_name):
            if chunk.strip() != "":
                responseBuffer.append(chunk)
                if message_emitter:
                    await message_emitter.emit_message_part(chunk, 0)
        response = "".join(responseBuffer)
        return response

    @log_execution_time("bpmn_generation_position_task_flow.agent_run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)

        context = state["context"]
        # await message_emitter.emit_message(jsonable_encoder(result), "result")
        result = await self.generate_bpmn_chat_stream(
            user_request,
            context.current_bpmn,
            model_type=context.override_model_type or ModelType.Anthropic,
            chat_history=context.chat_history,
            message_emitter=message_emitter,
        )

        ret = BpmnGenerationToolResult(
            tool=Tool.EDIT_BPMN,
            title=result.get("title"),
            explanation=result["explanation"],
            add=result["add"],
            update=result["update"],
            delete=result["delete"],
        )
        return {"tool_result": ret, "current_tool_index": state["current_tool_index"] + 1}

    async def generate_bpmn_chat_stream(
        self,
        query: str,
        current_bpmn: str,
        model_type: ModelType = ModelType.OpenAI,
        chat_history: dict[Tool, list[ChatHistory]] | None = None,
        message_emitter: MessageEmitter | None = None,
    ) -> BpmnGenerationChatTaskResult:
        # self._load_conf()  # local test to load the config file in case of changes
        current_bpmn = current_bpmn.strip() if current_bpmn is not None else ""
        # provide a default bpmn xml if current_bpmn is empty, then LLM can always generate patch
        current_bpmn = self.common_config["default_bpmn_xml"] if current_bpmn == "" else current_bpmn

        model_name = self._get_model_name(model_type) + "_streaming"
        result = await self._generate(
            query,
            current_bpmn,
            model_name=model_name,
            edit_sys_template="edit_patch",
            chat_history=chat_history,
            message_emitter=message_emitter,
        )

        result_json = result.strip("```json\n").strip("\n```")
        return json.loads(result_json)
