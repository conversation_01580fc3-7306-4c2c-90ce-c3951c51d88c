import asyncio
import functools
import time
from typing import Any, Callable, Dict, Optional

from opentelemetry import trace

from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

TRACER_NAME = "AppInsights"


def detailed_log_execution_time(tracked_section: str, include_args: bool = False) -> Callable[..., Any]:
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        async def awrapper(*args: Any, **kwargs: Any) -> Any:
            logger = AppInsightsLogger()
            tracer = trace.get_tracer(TRACER_NAME)
            func_name = func.__name__
            start_time = time.time()

            # Minimal data to track
            telemetry_data = {"function": func_name, "timestamp": time.time()}

            # Only include primitive args if requested
            if include_args:
                arg_data = {}
                for i, arg in enumerate(args[1:], 1):  # Skip 'self' for instance methods
                    if isinstance(arg, (str, int, float, bool)) or arg is None:
                        arg_data[f"arg{i}"] = arg
                    else:
                        arg_data[f"arg{i}_type"] = type(arg).__name__

                for key, value in kwargs.items():
                    if isinstance(value, (str, int, float, bool)) or value is None:
                        arg_data[key] = value
                    else:
                        arg_data[f"{key}_type"] = type(value).__name__

                telemetry_data["args"] = arg_data

            with tracer.start_as_current_span(f"{tracked_section}.{func_name}", attributes=telemetry_data) as span:
                try:
                    # Execute the function
                    result = await func(*args, **kwargs)

                    # Record timing information
                    duration = time.time() - start_time
                    span.set_attribute("duration_ms", round(duration * 1000, 2))

                    # Log basic result info
                    if result and isinstance(result, dict):
                        result_keys = list(result.keys())
                        span.set_attribute("result_keys", str(result_keys))

                    logger.log_custom_event(f"BPMN.Perf.{tracked_section}.{func_name}", {"duration": round(duration, 3), "success": True})

                    return result

                except Exception as e:
                    duration = time.time() - start_time
                    error_type = type(e).__name__

                    # Record error info
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    span.set_attribute("error_type", error_type)
                    span.set_attribute("duration_ms", round(duration * 1000, 2))

                    logger.log_custom_event(
                        f"BPMN.Perf.{tracked_section}.{func_name}.Error", {"duration": round(duration, 3), "error_type": error_type, "success": False}
                    )

                    # Re-raise to maintain original behavior
                    raise

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            logger = AppInsightsLogger()
            tracer = trace.get_tracer(TRACER_NAME)
            func_name = func.__name__
            start_time = time.time()

            # Minimal data to track
            telemetry_data = {"function": func_name, "timestamp": time.time()}

            if include_args:
                # Same arg processing as async version
                arg_data = {}
                for i, arg in enumerate(args[1:], 1):
                    if isinstance(arg, (str, int, float, bool)) or arg is None:
                        arg_data[f"arg{i}"] = arg
                    else:
                        arg_data[f"arg{i}_type"] = type(arg).__name__

                for key, value in kwargs.items():
                    if isinstance(value, (str, int, float, bool)) or value is None:
                        arg_data[key] = value
                    else:
                        arg_data[f"{key}_type"] = type(value).__name__

                telemetry_data["args"] = arg_data

            with tracer.start_as_current_span(f"{tracked_section}.{func_name}", attributes=telemetry_data) as span:
                try:
                    result = func(*args, **kwargs)

                    # Record timing information
                    duration = time.time() - start_time
                    span.set_attribute("duration_ms", round(duration * 1000, 2))

                    # Log basic result info
                    if result and isinstance(result, dict):
                        result_keys = list(result.keys())
                        span.set_attribute("result_keys", str(result_keys))

                    logger.log_custom_event(f"BPMN.Perf.{tracked_section}.{func_name}", {"duration": round(duration, 3), "success": True})

                    return result

                except Exception as e:
                    duration = time.time() - start_time
                    error_type = type(e).__name__

                    # Record error info
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    span.set_attribute("error_type", error_type)
                    span.set_attribute("duration_ms", round(duration * 1000, 2))

                    logger.log_custom_event(
                        f"BPMN.Perf.{tracked_section}.{func_name}.Error", {"duration": round(duration, 3), "error_type": error_type, "success": False}
                    )

                    # Re-raise to maintain original behavior
                    raise

        return awrapper if asyncio.iscoroutinefunction(func) else wrapper

    return decorator


class LLMTimingContext:
    def __init__(self, operation_name: str, additional_properties: Optional[Dict[str, Any]] = None):
        self.operation_name = operation_name
        self.properties = additional_properties or {}
        self.logger = AppInsightsLogger()
        self.tracer = trace.get_tracer(TRACER_NAME)
        self.start_time = 0.0
        self.span = None

    async def __aenter__(self):
        self.start_time = time.time()
        self.span = self.tracer.start_span(f"LLM.{self.operation_name}", attributes=self.properties)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.span is None:
            return

        duration = time.time() - self.start_time
        self.properties["duration"] = round(duration, 3)
        self.properties["duration_ms"] = round(duration * 1000, 2)

        if exc_type:
            self.properties["success"] = False
            self.properties["error_type"] = exc_type.__name__
            self.span.record_exception(exc_val)
            self.span.set_status(trace.Status(trace.StatusCode.ERROR, str(exc_val)))
        else:
            self.properties["success"] = True
            self.span.set_status(trace.StatusCode.OK)

        self.span.set_attribute("duration_ms", self.properties["duration_ms"])
        self.span.end()

        # Add clear category for filtering
        self.properties["category"] = "LLM"

        self.logger.log_custom_event(f"BPMN.LLM.{self.operation_name}", self.properties)

    def __enter__(self):
        self.start_time = time.time()
        self.span = self.tracer.start_span(f"LLM.{self.operation_name}", attributes=self.properties)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.span is None:
            return

        duration = time.time() - self.start_time
        self.properties["duration"] = round(duration, 3)
        self.properties["duration_ms"] = round(duration * 1000, 2)

        if exc_type:
            self.properties["success"] = False
            self.properties["error_type"] = exc_type.__name__
            self.span.record_exception(exc_val)
            self.span.set_status(trace.Status(trace.StatusCode.ERROR, str(exc_val)))
        else:
            self.properties["success"] = True
            self.span.set_status(trace.StatusCode.OK)

        self.span.set_attribute("duration_ms", self.properties["duration_ms"])
        self.span.end()

        # Add clear category for filtering
        self.properties["category"] = "LLM"

        self.logger.log_custom_event(f"BPMN.LLM.{self.operation_name}", self.properties)

    def add_property(self, key: str, value: Any):
        self.properties[key] = value
        if self.span:
            self.span.set_attribute(key, value)


def llm_timing_context(name: str, additional_properties: Optional[Dict[str, Any]] = None) -> LLMTimingContext:
    return LLMTimingContext(name, additional_properties)


class PerformanceTracker:
    def __init__(self, component_name: str, request_id: Optional[str] = None):
        self.component_name = component_name
        self.request_id = request_id or "unknown"
        self.start_time = time.time()
        self.logger = AppInsightsLogger()
        self.segment_times: Dict[str, Dict[str, float]] = {}
        self.current_segment: Optional[str] = None
        self.segment_start_time = 0.0
        self.properties: Dict[str, Any] = {"request_id": self.request_id, "component": component_name}

    def start_segment(self, name: str) -> None:
        if self.current_segment:
            self.end_segment()

        self.current_segment = name
        self.segment_start_time = time.time()

    def end_segment(self) -> None:
        """End the current segment"""
        if not self.current_segment:
            return

        duration = time.time() - self.segment_start_time
        segment_name = self.current_segment

        if segment_name not in self.segment_times:
            self.segment_times[segment_name] = {"count": 0, "total_duration": 0.0}

        self.segment_times[segment_name]["count"] += 1
        self.segment_times[segment_name]["total_duration"] += duration

        # Add segment to properties
        self.properties[f"segment.{segment_name}.duration"] = round(duration, 3)
        self.properties[f"segment.{segment_name}.count"] = self.segment_times[segment_name]["count"]

        self.current_segment = None

    def add_property(self, key: str, value: Any) -> None:
        self.properties[key] = value

    def add_model_info(self, model_name: str, tokens: Optional[int] = None) -> None:
        self.properties["model_name"] = model_name
        if tokens is not None:
            self.properties["token_count"] = tokens

    def add_tool_info(self, tool_name: str, success: bool = True) -> None:
        self.add_property(f"tool.{tool_name}", True)
        self.add_property(f"tool.{tool_name}.success", success)

    def record(self) -> None:
        # End any active segment
        if self.current_segment:
            self.end_segment()

        # Calculate total duration
        total_duration = time.time() - self.start_time
        self.properties["total_duration"] = round(total_duration, 3)

        # Add segment summary
        self.properties["segment_count"] = len(self.segment_times)

        # Log the event
        self.logger.log_custom_event(f"BPMN.Performance.{self.component_name}", self.properties)


class BusinessMetrics:
    def __init__(self, request_id: Optional[str] = None):
        self.logger = AppInsightsLogger()
        self.metrics: Dict[str, Any] = {"request_id": request_id or "unknown", "timestamp": time.time()}

    def add_metric(self, name: str, value: Any) -> None:
        self.metrics[name] = value

    def add_request_info(self, request_type: str, word_count: int, content_type: Optional[str] = None) -> None:
        self.metrics["request_type"] = request_type
        self.metrics["word_count"] = word_count
        if content_type:
            self.metrics["content_type"] = content_type

    def add_tool_usage(self, tool_name: str, successful: bool = True) -> None:
        self.metrics[f"tool.{tool_name}"] = True
        self.metrics[f"tool.{tool_name}.successful"] = successful

        # Calculate total tools count
        tool_count = sum(1 for key in self.metrics if key.startswith("tool.") and not key.endswith(".successful"))
        self.metrics["total_tools_count"] = tool_count

    def record(self) -> None:
        self.logger.log_custom_event("BPMN.Business", self.metrics)


def create_performance_metrics(component_name: str, request_id: Optional[str] = None) -> PerformanceTracker:
    return PerformanceTracker(component_name, request_id)


def create_business_metrics(request_id: Optional[str] = None) -> BusinessMetrics:
    return BusinessMetrics(request_id)


def detailed_performance_tracking(component_name: str, capture_request_info: bool = True) -> Callable[..., Any]:
    """
    Detailed performance tracking decorator that creates or accepts a performance tracker
    and handles segments, request analysis, and context passing.
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        async def wrapper(self, request, *args, **kwargs) -> Any:
            # Extract the external performance tracker if provided
            external_perf = kwargs.pop("external_perf", None)
            request_id = request.get("requestId", "unknown")

            # Create or use provided performance tracker
            perf = external_perf or create_performance_metrics(component_name, request_id)

            # Start initialization segment
            perf.start_segment("initialization")

            # Capture basic request info if enabled
            if capture_request_info and hasattr(request, "get"):
                user_request = request.get("userRequest", "")
                perf.add_property("has_image", request.get("image") is not None)
                perf.add_property("has_bpmn", bool(request.get("currentBpmn", "")))
                perf.add_property("has_chat_history", bool(request.get("chatHistory", "")))

                # Capture validation flag if present in args
                if len(args) > 0 and isinstance(args[0], bool):
                    perf.add_property("validation_enabled", args[0])

                # Simple request analysis
                if user_request:
                    word_count = len(user_request.split())
                    perf.add_property("request_word_count", word_count)

                    # Create business metrics for this request
                    business = create_business_metrics(request_id)
                    business.add_request_info(
                        request_type=component_name, word_count=word_count, content_type="text_with_image" if request.get("image") else "text_only"
                    )

                    # Add simple complexity metric
                    complexity = "low"
                    if word_count > 30:
                        complexity = "high"
                    elif word_count > 15:
                        complexity = "medium"
                    business.add_metric("request_complexity", complexity)
                    business.record()

            perf.end_segment()

            try:
                # Always add the performance tracker to kwargs
                kwargs["external_perf"] = perf

                # Execute the function
                result = await func(self, request, *args, **kwargs)

                # Record basic result info
                if result and isinstance(result, dict):
                    perf.add_property("response_valid", result.get("valid", False))
                    results = result.get("results", [])
                    perf.add_property("result_count", len(results))

                perf.record()
                return result

            except Exception as e:
                # Record error info
                error_type = type(e).__name__
                error_msg = str(e)

                perf.add_property("error", error_type)
                perf.add_property("error_message", error_msg[:200])
                perf.record()

                # Re-raise the exception
                raise

        return wrapper

    return decorator
