import json
import os
import pathlib

from fastapi.encoders import jsonable_encoder
from langchain_core.runnables.config import RunnableConfig

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import CANNOT_PROCESS_REQUEST_EXPLANATION
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnRequestContext,
    ChatHistory,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()


class BpmnQATask(BpmnBaseTask):
    def __init__(self) -> None:
        super().__init__("qa_prompt.yaml")
        self.bpmn_qa_examples = BpmnBaseTask.read_content(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "bpmn_qa_examples.md")))
        self.history_dependencies = [Tool.QA, Tool.EDIT_BPMN, Tool.EXTENSION]

    @log_execution_time("bpmn_qa_task.generate")
    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        model_type = context.override_model_type or ModelType.OpenAI
        result: BpmnGenerationChatTaskResult = await self._generate_bpmn_qa(context.user_request, context.current_bpmn, context.chat_history, model_type)
        return ToolResult(tool=Tool.QA, explanation=result["explanation"])

    @log_execution_time("bpmn_qa_node.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)

        context = state["context"]

        result: BpmnGenerationChatTaskResult = await self._generate_bpmn_qa(user_request, context.current_bpmn, context.chat_history)

        tool_result = ToolResult(tool=Tool.QA, explanation=result["explanation"])
        await message_emitter.emit_message(jsonable_encoder(tool_result), "result")
        tool_results = state.get("tool_results") or []
        tool_results.append(tool_result)

        return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

    async def _generate_bpmn_qa(
        self, query: str, current_bpmn: str, chat_history: dict[Tool, list[ChatHistory]] | None, model_type: ModelType = ModelType.OpenAI
    ) -> BpmnGenerationChatTaskResult:
        current_bpmn = current_bpmn.strip() if current_bpmn is not None else ""
        # provide a default bpmn xml if current_bpmn is empty, then LLM can always generate patch
        current_bpmn = self.common_config["default_bpmn_xml"] if current_bpmn == "" else current_bpmn

        system_message = self._system_template(self.config["prompt"]["system_template"]["q&a"]).format(bpmn_qa_examples=self.bpmn_qa_examples)

        model_name = self._get_model_name(model_type)
        chat_history_str = await self._get_related_chat_history(chat_history, self.history_dependencies, model_name)
        user_message = self._human_template(self.config["prompt"]["user_template"]["q&a"]).format(
            current_bpmn=current_bpmn, query=query, chat_history=chat_history_str
        )
        result, usage = await self._call_llm(system_message, user_message, model_name)
        result_json = result.strip("```json\n").strip("\n```")
        qa_output: BpmnGenerationChatTaskResult = json.loads(result_json)
        qa_output["usage"] = usage

        validation_result = VALIDATOR.validate_qa_json(result_json)
        if not validation_result["valid"]:
            LOGGER.error(f"JSON isn't valid: {validation_result['error_message']}. Failed to generate valid BPMN Q&A answer. An exception occurred.")
            return BpmnGenerationChatTaskResult(add=None, update=None, delete=None, explanation=CANNOT_PROCESS_REQUEST_EXPLANATION, title=None, usage=usage)

        return qa_output
