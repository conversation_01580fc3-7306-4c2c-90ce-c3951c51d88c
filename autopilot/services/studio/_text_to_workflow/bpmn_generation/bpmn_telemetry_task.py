import json
import os
import pathlib

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import TelemetryConstants
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnRequestContext,
    BpmnSummarizationTelemetryResult,
    BpmnTelemetryRequest,
    ModelType,
    TelemetryUserAction,
    ToolResult,
    UserActionExplanationTelemetryResult,
    UserRequestSummarizationTelemetryResult,
    UserRequestTelemetryRequest,
)
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

LOGGER = AppInsightsLogger()


class BpmnTelemetryTask(BpmnBaseTask):
    def __init__(self) -> None:
        super().__init__("telemetry_prompt.yaml")
        self.bpmn_telemetry_examples = BpmnBaseTask.read_content(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "bpmn_telemetry_examples.md")))

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        raise NotImplementedError

    @log_execution_time("bpmn_telemetry_task.generate_telemetry")
    async def generate_telemetry(self, request: BpmnTelemetryRequest | UserRequestTelemetryRequest) -> None:
        router_model: ModelType = ModelType.Google

        if self._is_summarization_action(request["userAction"]):
            telemetry_dict = await self._bpmn_summarization_telemetry(request, router_model)
        elif TelemetryUserAction.USER_REQUEST in request["userAction"]:
            if "tools" in request and "sourceType" in request:
                telemetry_dict = await self._user_request_summarization_telemetry(request, router_model)
            else:
                raise TypeError("Expected request to be of type UserRequestTelemetryRequest")
        else:
            telemetry_dict = await self._user_action_explanation_telemetry(request, router_model)

        LOGGER.log_custom_event("bpmn_telemetry", telemetry_dict)

    async def _user_action_explanation_telemetry(self, request: BpmnTelemetryRequest, router_model: ModelType) -> dict:
        system_message = self._system_template(self.config["prompt"]["system_template"]["user_actions_explanation_telemetry"]).format(
            bpmn_telemetry_examples=self.bpmn_telemetry_examples
        )
        user_message = self._human_template(self.config["prompt"]["user_template"]["user_actions_explanation_telemetry"]).format(
            current_bpmn=self._get_bpmn_model(request), user_action=request["userAction"], user_request=request["request"], ai_response=request["response"]
        )

        result, usage = await self._call_llm(system_message, user_message, f"bpmn_telemetry_model_{router_model.value}")
        result_json = result.strip("```json\n").strip("\n```")
        telemetry_output: UserActionExplanationTelemetryResult = json.loads(result_json)
        ai_response_json = json.loads(request.get("response") or "{}")
        tool = ai_response_json.get("tool", "")

        return {
            "type": request["sourceType"].value,
            "tool": tool,
            "userAction": json.dumps([action.value for action in request["userAction"]]),  # Save as JSON string
            "userRequest": telemetry_output.get("userRequest", ""),
            "aiResponse": telemetry_output.get("aiResponse", ""),
            "explanation": telemetry_output.get("explanation", ""),
        }

    async def _bpmn_summarization_telemetry(self, request: BpmnTelemetryRequest, router_model: ModelType) -> dict:
        system_message = self._system_template(self.config["prompt"]["system_template"]["bpmn_summarization_telemetry"])

        user_message = self._human_template(self.config["prompt"]["user_template"]["bpmn_summarization_telemetry"]).format(
            current_bpmn=self._get_bpmn_model(request)
        )

        result, usage = await self._call_llm(system_message, user_message, f"bpmn_telemetry_model_{router_model.value}")
        result_json = result.strip("```json\n").strip("\n```")
        bpmn_summarization_output: BpmnSummarizationTelemetryResult = json.loads(result_json)

        return {
            TelemetryConstants.TYPE: request["sourceType"].value,
            TelemetryConstants.USER_ACTION: json.dumps([action.value for action in request["userAction"]]),
            TelemetryConstants.BPMN_DESCRIPTION: bpmn_summarization_output.get("bpmnDescription", ""),
            TelemetryConstants.DOMAIN: bpmn_summarization_output.get("domain", "generic").lower(),
            TelemetryConstants.FILE_ID: request.get("fileId", ""),
        }

    async def _user_request_summarization_telemetry(self, request: UserRequestTelemetryRequest, router_model: ModelType) -> dict:
        system_message = self._system_template(self.config["prompt"]["system_template"]["user_request_summarization_telemetry"])

        user_message = self._human_template(self.config["prompt"]["user_template"]["user_request_summarization_telemetry"]).format(
            user_request=request.get("request", ""),
        )

        result, usage = await self._call_llm(system_message, user_message, f"bpmn_telemetry_model_{router_model.value}")
        result_json = result.strip("```json\n").strip("\n```")
        user_request_summarization_output: UserRequestSummarizationTelemetryResult = json.loads(result_json)
        tools = ""
        if request.get("tools"):
            tools = json.dumps([tool_request.tool.value for tool_request in request["tools"] if tool_request.tool is not None])

        return {
            "type": request["sourceType"].value,
            "userAction": json.dumps([action.value for action in request["userAction"]]),
            "userRequest": user_request_summarization_output.get("userRequest", ""),
            "tools": tools,
        }

    def _get_bpmn_model(self, request: BpmnTelemetryRequest) -> str:
        current_bpmn = request.get("currentBpmn", "").strip()
        return self._remove_bpmn_diagram(current_bpmn) if current_bpmn else ""

    def _is_summarization_action(self, user_action: list[TelemetryUserAction]) -> bool:
        return any(action.can_summarize for action in user_action)
