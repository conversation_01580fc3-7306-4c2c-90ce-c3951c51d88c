import os
import pathlib

import langchain.schema
from langchain.prompts.chat import MessageLikeRepresentation

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnGenerationTaskResult,
    BpmnGenerationToolResult,
    BpmnRequestContext,
    ChatHistory,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()

# Mapping of extension types
FILE_PATH_MAPPING = {
    "bpmn_patch_schema": "bpmn_patch_schema.json",
    "edit_bpmn_examples": "edit_bpmn_examples.md",
    "supported_element_examples": "supported_element_examples.md",
}


class BpmnGenerationTask(BpmnBaseTask):
    def __init__(self) -> None:
        self.bpmn_patch_schema = None
        self.edit_bpmn_examples = None
        self.supported_element_examples = None
        self.history_dependencies = [Tool.EDIT_BPMN, Tool.QA, Tool.EXTENSION]
        super().__init__("generation_prompt.yaml")

    def _init_config(self, config_file: str):
        super()._init_config(config_file)
        config_path = os.path.join(os.path.dirname(__file__), "config")
        for key, value in FILE_PATH_MAPPING.items():
            setattr(self, key, BpmnBaseTask.read_content(pathlib.Path(os.path.join(config_path, value))))

    def _get_model_name(self, model_type: ModelType) -> str:
        return f"bpmn_generation_chat_model_{model_type.value}"

    async def _generate(
        self,
        query: str,
        current_bpmn: str,
        model_name: str,
        edit_sys_template: str = "edit",
        chat_history: dict[Tool, list[ChatHistory]] | None = None,
    ) -> tuple[str, TokenUsage]:
        system_message: MessageLikeRepresentation
        user_message: langchain.schema.BaseMessage

        chat_history_str = await self._get_related_chat_history(chat_history, self.history_dependencies, model_name)

        if current_bpmn is not None and current_bpmn != "":
            system_message = self._system_template(self.config["prompt"]["system_template"][edit_sys_template]).format(
                bpmn_patch_schema=self.bpmn_patch_schema,
                supported_element_examples=self.supported_element_examples,
                edit_bpmn_examples=self.edit_bpmn_examples,
            )

            if "User: pending" in chat_history_str.strip():
                query = f"{query}. Also integrate the latest pending change for edit after the last accepted or declined one."
            user_message = self._human_template(self.config["prompt"]["user_template"]["edit"]).format(
                current_bpmn=current_bpmn, query=query, chat_history=chat_history_str
            )
        else:
            system_message = self._system_template(self.config["prompt"]["system_template"]["create"])
            user_message = self._human_template(self.config["prompt"]["user_template"]["create"]).format(query=query)

        return await self._call_llm(system_message, user_message, model_name)

    async def generate_bpmn(
        self,
        query: str,
        current_bpmn: str,
    ) -> BpmnGenerationTaskResult:
        model_name = self._get_model_name(ModelType.OpenAI)
        result, usage = await self._generate(query, current_bpmn, model_name)
        result_bpmn = result.strip("```xml\n").strip("\n```")
        output: BpmnGenerationTaskResult = {
            "result": result_bpmn,
            "usage": usage,
            # If the BPMN result is empty, return the result as is because user's request could be not related to BPMN generation, joke, offensiveness, etc.
            "prompt_class": "JUNK" if not result_bpmn else None,
        }

        if output["prompt_class"] == "JUNK":
            return output

        validation_result = VALIDATOR.validate_xml(output["result"])
        if not validation_result["valid"]:
            error_message = validation_result["error_message"] if validation_result["error_message"] is not None else "XML error"
            fixed_bpmn = await self._fix_bpmn("xml", output["result"], error_message, model_name)
            fixed_bpmn = fixed_bpmn.strip("```xml\n").strip("\n```")
            fix_validation_result = VALIDATOR.validate_xml(fixed_bpmn)
            if fix_validation_result["valid"]:
                output["result"] = fixed_bpmn
            else:
                LOGGER.error(
                    f"Failed to generate valid BPMN 2.0 XML. An exception occurred after multiple creation attempts: {fix_validation_result['error_message']}"
                )
                raise errors.ServiceError(500, "Failed to generate valid BPMN 2.0 XML. An exception occurred after multiple creation attempts.")

        return output

    @log_execution_time("bpmn_generation_task.generate")
    async def generate(
        self,
        context: BpmnRequestContext,
    ) -> ToolResult:
        result = await self.generate_bpmn_chat(
            context.user_request,
            context.current_bpmn,
            validate=context.support_validation,
            model_type=context.override_model_type or ModelType.OpenAI,
            chat_history=context.chat_history,
        )
        return BpmnGenerationToolResult(
            tool=Tool.EDIT_BPMN,
            title=result.get("title"),
            explanation=result["explanation"],
            add=result["add"],
            update=result["update"],
            delete=result["delete"],
        )

    async def generate_bpmn_chat(
        self,
        query: str,
        current_bpmn: str,
        validate: bool = False,
        model_type: ModelType = ModelType.OpenAI,
        chat_history: dict[Tool, list[ChatHistory]] | None = None,
    ) -> BpmnGenerationChatTaskResult:
        # self._load_conf()  # local test to load the config file in case of changes
        current_bpmn = current_bpmn.strip() if current_bpmn is not None else ""
        # provide a default bpmn xml if current_bpmn is empty, then LLM can always generate patch
        current_bpmn = self.common_config["default_bpmn_xml"] if current_bpmn == "" else current_bpmn

        model_name = self._get_model_name(model_type)
        result, usage = await self._generate(query, current_bpmn, model_name=model_name, edit_sys_template="edit_patch", chat_history=chat_history)

        # LOGGER.info(f"Token usage of generate BPMN is : [{usage}]")
        result_json = result.strip("```json\n").strip("\n```")
        return await self._validate_output(validate, current_bpmn, result_json, usage, model_name)
