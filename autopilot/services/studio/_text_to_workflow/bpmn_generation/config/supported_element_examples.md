### **Supported BPMN 2.0 Elements**

#### **Parent Objects**
**Collaboration** (`<bpmn:collaboration>`), format:
  {
      "id": String,
      "name": String,
      "type": "bpmn:collaboration"
  }
- **Participant** (`<bpmn:participant>`), format:
  {
      "id": String,
      "name": String,
      "type": "bpmn:participant",
      "parentId": String, // immediate collaboration id
      "data":
      {
          "processRef": String, // reffered process id
          "label": String,
          "key": "value" // or "key": {}
      }
  }
- **Process** (`<bpmn:process>`), format:
  {
      "id": String,
      "name": String,
      "type": "bpmn:process",
      "data":
      {
          "label": String,
          "key": "value" // or "key": {}
      }
  }
- **SubProcess** (`<bpmn:subProcess>`), format:
  {
      "id": String,
      "name": String,
      "type": "bpmn:subProcess",
      "parentId": String, // immediate parent process or subprocess id
      "data":
      {
          "label": String,
          "isExpanded": Boolean, // optional
          "triggeredByEvent": Boolean, // optional
          "laneId": String // optional, avialble only when under a Lane
      }
  }
- **Call Activity** (`bpmn:callActivity`), format:
  {
    "id": String,
      "name": String,
      "type": "bpmn:callActivity",
      "parentId": String, // immediate parent process or subprocess id
      "data":
      {
          "label": String,
          "calledElement": String, // the element id intend to call
          "laneId": String, // optional, avialble only when under a Lane
          "variablesMapping": {"key": "value"} // optional
      }
  }
- **LaneSet** (`<bpmn:laneSet>`), format:
  {
      "id": String,
      "type": "bpmn:laneSet",
      "parentId": String // immediate parent process or subprocess id
  }
- **ChildLaneSet** (`<bpmn:childLaneSet>`), format:
  {
      "id": String,
      "type": "bpmn:childLaneSet",
      "parentId": String // immediate parent process or subprocess id
  }
- **Lane** (`<bpmn:lane>`), format:
  {
      "id": String,
      "type": "bpmn:lane",
      "parentId": String // immediate parent process or subprocess id
  }

#### **Flow Objects**
- **Start Event** (`<bpmn:startEvent>`), format:
  {
    "type": "bpmn:startEvent",  // BPMN element type
    "id": String,        // Required for modify & remove, auto-generated for add
    "parentId": String,  // Required for subprocesses & boundary events
    "name": String,             // Element name (optional)
    "data": {
      "label": String,           // Good to have, but most of the times it exists
      "laneId": String, // optional, avialble only when under a Lane
      "eventDefinition": // Optional
        {
            "type": String, // allowed types: Timer, Message, Signal, Error, Escalation, Compensation, Conditional
            "key": "value" // Optional additional attributes specific to the element type
        }
    }
  }

  - **Examples**
    Assume elements examples below are in process 'Process_112358'

    - Start event Type: None
      Soure xml, 
      ```xml
      <bpmn:startEvent id="Event_start" name="goodStart">
        <bpmn:documentation>Start of a process 111111</bpmn:documentation>
        <bpmn:outgoing>edge_Event_start-Activity_E3LAVC</bpmn:outgoing>
      </bpmn:startEvent>
      ```
      
      Target json 
      ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "Start of a process 111111"
          }
      }
      ```
    - Start event Type: Timer
      Soure xml
      ```xml
      <bpmn:startEvent id="Event_start_timer" name="goodStart">
      <bpmn:documentation>A timer start event</bpmn:documentation>
      <bpmn:outgoing>edge_Event_start-Activity_E3LAVC</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">PT1H/T2</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
      </bpmn:startEvent>
      ```
      
      Target json 
      ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_timer",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "A timer start event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "timeCycle": "PT1H/T2"
              }
          }
      }
      ```

    - Start event Type: Message
      Soure xml
      ```xml
      <bpmn:startEvent id="Event_start_msg">
        <bpmn:documentation>A message start event</bpmn:documentation>
        <bpmn:outgoing>SequenceFlow_1cg4qub</bpmn:outgoing>
        <bpmn:messageEventDefinition messageRef="Message_1enp267" />
      </bpmn:startEvent>
      ```
      
      Target json 
      ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_msg",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "A message start event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_1enp267"
              }
          }
      }
      ```
    - Start event Type: Signal
      Soure xml
      ```xml
      <bpmn:startEvent id="Event_start_sig" name="goodStart">
        <bpmn:documentation>A signal start event </bpmn:documentation>
        <bpmn:outgoing>edge_Event_start-Activity_E3LAVC</bpmn:outgoing>
        <bpmn:signalEventDefinition signalRef="Signal_GCXfZu"/>
      </bpmn:startEvent>
      ```
      
      Target json 
      ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_sig",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "A signal start event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_GCXfZu" 
              }
          }
      }
      ```
    - Start Event Type: Error
      Source XML:
      ```xml
      <bpmn:startEvent id="Event_start_error" name="errorStart">
        <bpmn:documentation>An error start event</bpmn:documentation>
        <bpmn:outgoing>edge_Event_start_error-Activity_E3LAVC</bpmn:outgoing>
        <bpmn:errorEventDefinition errorRef="Error_9876AB" />
      </bpmn:startEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_error",
          "parentId": "Process_112358",
          "name": "errorStart",
          "data": {
              "label": "An error start event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_9876AB"
              }
          }
      }
      ```

    - Start Event Type: Escalation
        Source XML:
        ```xml
        <bpmn:startEvent id="Event_start_escalation" name="escalationStart">
          <bpmn:documentation>An escalation start event</bpmn:documentation>
          <bpmn:outgoing>edge_Event_start_escalation-Activity_E3LAVC</bpmn:outgoing>
          <bpmn:escalationEventDefinition escalationRef="Escalation_1234CD" />
        </bpmn:startEvent>
        ```

        Target JSON:
        ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_escalation",
            "parentId": "Process_112358",
            "name": "escalationStart",
            "data": {
                "label": "An escalation start event",
                "eventDefinition": {
                    "type": "bpmn:escalationEventDefinition",
                    "escalationRef": "Escalation_1234CD"
                }
            }
        }
        ```

    - Start Event Type: Compensation
        Source XML:
        ```xml
        <bpmn:startEvent id="Event_start_compensation" name="compensationStart">
          <bpmn:documentation>A compensation start event</bpmn:documentation>
          <bpmn:outgoing>edge_Event_start_compensation-Activity_E3LAVC</bpmn:outgoing>
          <bpmn:compensateEventDefinition />
        </bpmn:startEvent>
        ```

        Target JSON:
        ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_compensation",
            "parentId": "Process_112358",
            "name": "compensationStart",
            "data": {
                "label": "A compensation start event",
                "eventDefinition": {
                    "type": "bpmn:compensateEventDefinition"
                }
            }
        }
        ```

    - Start Event Type: Conditional
        Source XML:
        ```xml
        <bpmn:startEvent id="Event_start_conditional" name="conditionalStart">
          <bpmn:documentation>A conditional start event</bpmn:documentation>
          <bpmn:outgoing>edge_Event_start_conditional-Activity_E3LAVC</bpmn:outgoing>
          <bpmn:conditionalEventDefinition>
            <bpmn:condition xsi:type="bpmn:tFormalExpression">${temperature > 30}</bpmn:condition>
          </bpmn:conditionalEventDefinition>
        </bpmn:startEvent>
        ```

        Target JSON:
        ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_conditional",
            "parentId": "Process_112358",
            "name": "conditionalStart",
            "data": {
                "label": "A conditional start event",
                "eventDefinition": {
                    "type": "bpmn:conditionalEventDefinition",
                    "conditionExpression": "${temperature > 30}"
                }
            }
        }
        ```

- **End Event** (`<bpmn:endEvent>`), format:
  {
    "type": "bpmn:endEvent",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,           // Good to have, but most of the times it exists
      "laneId": String, // optional, avialble only when under a Lane
      "eventDefinition": // Optional
        {
            "type": String, // allowed types: Message, Signal, Error, Escalation, Compensation, Terminate
            "key": "value" // Optional additional attributes specific to the element type
        }
    }
  }

  - **Examples**
      Assume elements examples below are in process 'Process_45678'

    - End Event Type: None
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end" name="processEnd">
        <bpmn:documentation>End of the process</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end</bpmn:incoming>
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end",
          "parentId": "Process_45678",
          "name": "processEnd",
          "data": {
              "label": "End of the process"
          }
      }
      ```

    - End Event Type: Message
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end_message" name="messageEnd">
        <bpmn:documentation>A message end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_message</bpmn:incoming>
        <bpmn:messageEventDefinition messageRef="Message_5678EF" />
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_message",
          "parentId": "Process_45678",
          "name": "messageEnd",
          "data": {
              "label": "A message end event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_5678EF"
              }
          }
      }
      ```
    - End Event Type: Signal
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end_signal" name="signalEnd">
        <bpmn:documentation>A signal end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_signal</bpmn:incoming>
        <bpmn:signalEventDefinition signalRef="Signal_9012GH" />
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_signal",
          "parentId": "Process_45678",
          "name": "signalEnd",
          "data": {
              "label": "A signal end event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_9012GH"
              }
          }
      }
      ```

    - End Event Type: Error
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end_error" name="errorEnd">
        <bpmn:documentation>An error end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_error</bpmn:incoming>
        <bpmn:errorEventDefinition errorRef="Error_3456IJ" />
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_error",
          "parentId": "Process_45678",
          "name": "errorEnd",
          "data": {
              "label": "An error end event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_3456IJ"
              }
          }
      }
      ```

    - End Event Type: Escalation
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end_escalation" name="escalationEnd">
        <bpmn:documentation>An escalation end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_escalation</bpmn:incoming>
        <bpmn:escalationEventDefinition escalationRef="Escalation_7890KL" />
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_escalation",
          "parentId": "Process_45678",
          "name": "escalationEnd",
          "data": {
              "label": "An escalation end event",
              "eventDefinition": {
                  "type": "bpmn:escalationEventDefinition",
                  "escalationRef": "Escalation_7890KL"
              }
          }
      }
      ```

    - End Event Type: Compensation
      Source XML:
      ```xml 
      <bpmn:endEvent id="Event_end_compensation" name="compensationEnd">
        <bpmn:documentation>A compensation end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_compensation</bpmn:incoming>
        <bpmn:compensateEventDefinition />
      </bpmn:endEvent>
      ```
      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_compensation",
          "parentId": "Process_45678",
          "name": "compensationEnd",
          "data": {
              "label": "A compensation end event",
              "eventDefinition": {
                  "type": "bpmn:compensateEventDefinition"
              }
          }
      }
      ```

    - End Event Type: Terminate
      Source XML:
      ```xml
      <bpmn:endEvent id="Event_end_terminate" name="terminateEnd">
        <bpmn:documentation>A terminate end event</bpmn:documentation>
        <bpmn:incoming>edge_Activity_E3LAVC-Event_end_terminate</bpmn:incoming>
        <bpmn:terminateEventDefinition />
      </bpmn:endEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_terminate",
          "parentId": "Process_45678",
          "name": "terminateEnd",
          "data": {
              "label": "A terminate end event",
              "eventDefinition": {
                  "type": "bpmn:terminateEventDefinition"
              }
          }
      }
      ```

- **Intermediate Catch Event** (`<bpmn:intermediateCatchEvent>`), format:
  {
    "type": "bpmn:intermediateCatchEvent",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,           // Good to have, but most of the times it exists
      "laneId": String, // optional, avialble only when under a Lane
      "eventDefinition":
        {
            "type": String, // allowed types: Timer, Message, Signal, Conditional
            "key": "value" // Optional additional attributes specific to the element type
        }
    }
  }

  - **Examples**
      Assume elements examples below are in process 'Process_45678'

    - Intermediate Catch Event Type: Timer
      Source XML:
      ```xml
      <bpmn:intermediateCatchEvent id="Event_timer_catch" name="timerCatch">
        <bpmn:documentation>A timer intermediate catch event</bpmn:documentation>
        <bpmn:incoming>edge_Task_1234-Event_timer_catch</bpmn:incoming>
        <bpmn:timerEventDefinition>
            <bpmn:timeDuration>PT5M</bpmn:timeDuration>
        </bpmn:timerEventDefinition>
      </bpmn:intermediateCatchEvent>
      ```

      Target JSON:
      ```json
      {
        "type": "bpmn:intermediateCatchEvent",
        "id": "Event_timer_catch",
        "parentId": "Process_45678",
        "name": "timerCatch",
        "data": {
            "label": "A timer intermediate catch event",
            "eventDefinition": {
                "type": "bpmn:timerEventDefinition",
                "timeDuration": "PT5M"
            }
        }
      }
      ```

    - Intermediate Catch Event Type: Message 
      Source XML:
      ```xml
      <bpmn:intermediateCatchEvent id="Event_message_catch" name="messageCatch">
        <bpmn:documentation>A message intermediate catch event</bpmn:documentation>
        <bpmn:incoming>edge_Task_5678-Event_message_catch</bpmn:incoming>
        <bpmn:messageEventDefinition messageRef="Message_ABC123" />
      </bpmn:intermediateCatchEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_message_catch",
          "parentId": "Process_45678",
          "name": "messageCatch",
          "data": {
              "label": "A message intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
      ```
    
    - Intermediate Catch Event Type: Signal  
      Source XML:
      ```xml
      <bpmn:intermediateCatchEvent id="Event_signal_catch" name="signalCatch">
          <bpmn:documentation>A signal intermediate catch event</bpmn:documentation>
          <bpmn:incoming>edge_Task_9876-Event_signal_catch</bpmn:incoming>
          <bpmn:signalEventDefinition signalRef="Signal_789XYZ" />
      </bpmn:intermediateCatchEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_signal_catch",
          "parentId": "Process_45678",
          "name": "signalCatch",
          "data": {
              "label": "A signal intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_789XYZ"
              }
          }
      }
      ```

  - Intermediate Catch Event Type: Conditional
      Source XML:
      ```xml
      <bpmn:intermediateCatchEvent id="Event_conditional_catch" name="conditionalCatch">
          <bpmn:documentation>A conditional intermediate catch event</bpmn:documentation>
          <bpmn:incoming>edge_Task_3210-Event_conditional_catch</bpmn:incoming>
          <bpmn:conditionalEventDefinition>
              <bpmn:conditionExpression><![CDATA[${orderAmount > 1000}]]></bpmn:conditionExpression>
          </bpmn:conditionalEventDefinition>
      </bpmn:intermediateCatchEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_conditional_catch",
          "parentId": "Process_45678",
          "name": "conditionalCatch",
          "data": {
              "label": "A conditional intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:conditionalEventDefinition",
                  "conditionExpression": "${orderAmount > 1000}"
              }
          }
      }
      ```

- **Intermediate Throw Event** (`<bpmn:intermediateThrowEvent>`), format:
  {
    "type": "bpmn:intermediateThrowEvent",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,           // Good to have, but most of the times it exists
      "laneId": String, // optional, avialble only when under a Lane
      "eventDefinition": // Optional
        {
            "type": String, // allowed types: Message, Signal, Escalation, Compensation
            "key": "value" // Optional additional attributes specific to the element type
        }
    }
  }

  - Examples
    - Intermediate Throw Event: None
      Source XML:
      ```xml
      <bpmn:intermediateThrowEvent id="Event_none_throw" name="noneThrow">
        <bpmn:documentation>A generic intermediate throw event with no specific definition</bpmn:documentation>
        <bpmn:incoming>edge_Task_4321-Event_none_throw</bpmn:incoming>
      </bpmn:intermediateThrowEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_none_throw",
          "parentId": "Process_45678",
          "name": "noneThrow",
          "data": {
              "label": "A generic intermediate throw event with no specific definition"
          }
      }
      ```
    
    - Intermediate Throw Event: Message
      Source XML:
      ```xml
      <bpmn:intermediateThrowEvent id="Event_message_throw" name="messageThrow">
          <bpmn:documentation>A message intermediate throw event</bpmn:documentation>
          <bpmn:incoming>edge_Task_1234-Event_message_throw</bpmn:incoming>
          <bpmn:messageEventDefinition messageRef="Message_ABC123" />
      </bpmn:intermediateThrowEvent>

      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_message_throw",
          "parentId": "Process_45678",
          "name": "messageThrow",
          "data": {
              "label": "A message intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
      ```
    - Intermediate Throw Event: Signal
      Source XML:
      ```xml
      <bpmn:intermediateThrowEvent id="Event_signal_throw" name="signalThrow">
          <bpmn:documentation>A signal intermediate throw event</bpmn:documentation>
          <bpmn:incoming>edge_Task_5678-Event_signal_throw</bpmn:incoming>
          <bpmn:signalEventDefinition signalRef="Signal_789XYZ" />
      </bpmn:intermediateThrowEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_signal_throw",
          "parentId": "Process_45678",
          "name": "signalThrow",
          "data": {
              "label": "A signal intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_789XYZ"
              }
          }
      }
      ```

    - Intermediate Throw Event: Signal
      Source XML:
      ```xml
      <bpmn:intermediateThrowEvent id="Event_escalation_throw" name="escalationThrow">
          <bpmn:documentation>An escalation intermediate throw event</bpmn:documentation>
          <bpmn:escalationEventDefinition escalationRef="Escalation_456XYZ" />
      </bpmn:intermediateThrowEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_escalation_throw",
          "parentId": "Process_45678",
          "name": "escalationThrow",
          "data": {
              "label": "An escalation intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:escalationEventDefinition",
                  "escalationRef": "Escalation_456XYZ"
              }
          }
      }
      ```
    - Intermediate Throw Event: Compensation
      Source XML:
      ```xml
      <bpmn:intermediateThrowEvent id="Event_compensation_throw" name="compensationThrow">
          <bpmn:documentation>A compensation intermediate throw event</bpmn:documentation>
          <bpmn:incoming>edge_Task_9876-Event_compensation_throw</bpmn:incoming>
          <bpmn:compensateEventDefinition activityRef="Task_CompensateXYZ" />
      </bpmn:intermediateThrowEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_compensation_throw",
          "parentId": "Process_45678",
          "name": "compensationThrow",
          "data": {
              "label": "A compensation intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:compensateEventDefinition",
                  "activityRef": "Task_CompensateXYZ"
              }
          }
      }
      ```

- **Boundry Event** (`<bpmn:boundaryEvent>`), format:
  {
    "type": "bpmn:boundaryEvent",
    "id": String,
    "parentId": String, // the element this boundry event is attched to
    "name": String, // Optional
    "data": {
      "label": String,           // Good to have, but most of the times it exists
      "laneId": String, // optional, avialble only when under a Lane
      "eventDefinition":
        {
            "type": String, // allowed types: Message, Timer, Error, Signal, Escalation, Compensation, Conditional
            "isInterrupting": Boolean, // Optional, indicates if the bounday event interrupt the flow, if original xml contains `cancelActivity="false"` then it is false
            "key": "value" // Optional additional attributes specific to the element type
        }
    }
  }

  - Examples

    - Boundry Event: Message
      Source XML:
      ```xml
      <bpmn:boundaryEvent id="Event_message_boundary" name="messageBoundary" attachedToRef="Task_1234">
          <bpmn:documentation>A message boundary event</bpmn:documentation>
          <bpmn:messageEventDefinition messageRef="Message_ABC123" />
      </bpmn:boundaryEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_message_boundary",
          "parentId": "Task_1234",
          "name": "messageBoundary",
          "data": {
              "label": "A message boundary event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
      ```

    - Boundry Event: Timer
      Source XML:
      ```xml
      <bpmn:boundaryEvent id="Event_timer_boundary" name="timerBoundary" attachedToRef="Task_5678">
          <bpmn:documentation>A timer boundary event</bpmn:documentation>
          <bpmn:timerEventDefinition>
              <bpmn:timeDuration>PT10M</bpmn:timeDuration>
          </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_timer_boundary",
          "parentId": "Task_5678",
          "name": "timerBoundary",
          "data": {
              "label": "A timer boundary event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "timeDuration": "PT10M"
              }
          }
      }
      ```

    - Boundry Event: Timer (Non-interruptive)
      Source XML:
      ```xml
      <bpmn:boundaryEvent id="Event_timer_boundary" name="timerBoundary" cancelActivity="false" attachedToRef="Task_5678">
          <bpmn:documentation>A timer boundary event</bpmn:documentation>
          <bpmn:timerEventDefinition>
              <bpmn:timeDuration>PT10M</bpmn:timeDuration>
          </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_timer_boundary",
          "parentId": "Task_5678",
          "name": "timerBoundary",
          "data": {
              "label": "A timer boundary event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "isInterrupting": false,
                  "timeDuration": "PT10M"
              }
          }
      }
      ```

    - Boundry Event: Error
      Source XML:
      ```xml
      <bpmn:boundaryEvent id="Event_error_boundary" name="errorBoundary" attachedToRef="Task_9876">
          <bpmn:documentation>An error boundary event</bpmn:documentation>
          <bpmn:errorEventDefinition errorRef="Error_567XYZ" />
      </bpmn:boundaryEvent>

      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_error_boundary",
          "parentId": "Task_9876",
          "name": "errorBoundary",
          "data": {
              "label": "An error boundary event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_567XYZ"
              }
          }
      }
      ```

    - Boundry Event: Signal
          Source XML:
          ```xml
          <bpmn:boundaryEvent id="Event_signal_boundary" name="signalBoundary" attachedToRef="Task_6543">
              <bpmn:documentation>A signal boundary event</bpmn:documentation>
              <bpmn:signalEventDefinition signalRef="Signal_ABC987" />
          </bpmn:boundaryEvent>

          ```

          Target JSON:
          ```json
          {
              "type": "bpmn:boundaryEvent",
              "id": "Event_signal_boundary",
              "parentId": "Task_6543",
              "name": "signalBoundary",
              "data": {
                  "label": "A signal boundary event",
                  "eventDefinition": {
                      "type": "bpmn:signalEventDefinition",
                      "signalRef": "Signal_ABC987"
                  }
              }
          }
          ```

    - Boundry Event: Signal
          Source XML:
          ```xml
          <bpmn:boundaryEvent id="Event_signal_boundary" name="signalBoundary" attachedToRef="Task_6543">
              <bpmn:documentation>A signal boundary event</bpmn:documentation>
              <bpmn:signalEventDefinition signalRef="Signal_ABC987" />
          </bpmn:boundaryEvent>

          ```

          Target JSON:
          ```json
          {
              "type": "bpmn:boundaryEvent",
              "id": "Event_signal_boundary",
              "parentId": "Task_6543",
              "name": "signalBoundary",
              "data": {
                  "label": "A signal boundary event",
                  "eventDefinition": {
                      "type": "bpmn:signalEventDefinition",
                      "signalRef": "Signal_ABC987"
                  }
              }
          }
          ```

    - Boundry Event: Escalation 
          Source XML:
          ```xml
          <bpmn:boundaryEvent id="Event_escalation_boundary" name="escalationBoundary" attachedToRef="Task_2468">
              <bpmn:documentation>An escalation boundary event</bpmn:documentation>
              <bpmn:escalationEventDefinition escalationRef="Escalation_999XYZ" />
          </bpmn:boundaryEvent>
          ```

          Target JSON:
          ```json
          {
              "type": "bpmn:boundaryEvent",
              "id": "Event_escalation_boundary",
              "parentId": "Task_2468",
              "name": "escalationBoundary",
              "data": {
                  "label": "An escalation boundary event",
                  "eventDefinition": {
                      "type": "bpmn:escalationEventDefinition",
                      "escalationRef": "Escalation_999XYZ"
                  }
              }
          }
          ```

    - Boundry Event: Compensation  
          Source XML:
          ```xml
          <bpmn:boundaryEvent id="Event_compensation_boundary" name="compensationBoundary" attachedToRef="Task_1357">
              <bpmn:documentation>A compensation boundary event</bpmn:documentation>
              <bpmn:compensateEventDefinition activityRef="Task_CompensateXYZ" />
          </bpmn:boundaryEvent>
          ```

          Target JSON:
          ```json
          {
              "type": "bpmn:boundaryEvent",
              "id": "Event_compensation_boundary",
              "parentId": "Task_1357",
              "name": "compensationBoundary",
              "data": {
                  "label": "A compensation boundary event",
                  "eventDefinition": {
                      "type": "bpmn:compensateEventDefinition",
                      "activityRef": "Task_CompensateXYZ"
                  }
              }
          }
          ```

    - Boundry Event: Conditional   
          Source XML:
          ```xml
          <bpmn:boundaryEvent id="Event_conditional_boundary" name="conditionalBoundary" attachedToRef="Task_7890">
              <bpmn:documentation>A conditional boundary event</bpmn:documentation>
              <bpmn:conditionalEventDefinition>
                  <bpmn:conditionExpression><![CDATA[${orderAmount > 500}]]></bpmn:conditionExpression>
              </bpmn:conditionalEventDefinition>
          </bpmn:boundaryEvent>
          ```

          Target JSON:
          ```json
          {
              "type": "bpmn:boundaryEvent",
              "id": "Event_conditional_boundary",
              "parentId": "Task_7890",
              "name": "conditionalBoundary",
              "data": {
                  "label": "A conditional boundary event",
                  "eventDefinition": {
                      "type": "bpmn:conditionalEventDefinition",
                      "conditionExpression": "${orderAmount > 500}"
                  }
              }
          }
          ```

- **Signal Event** (`<bpmn:signal>`), format:
  {
    "type": "bpmn:signal",
    "id": String,
    "name": String, // Optional
    "data": {
      "label": String
    }
  }

- **Error Event** (`<bpmn:error>`), format:
  {
    "type": "bpmn:error",
    "id": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "errorCode": String
    }
  }

- **Escalation Event** (`<bpmn:escalation>`), format:
  {
    "type": "bpmn:escalation",
    "id": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "escalationCode": String
    }
  }

#### **Activities**
- **User Task** (`<bpmn:userTask>`), format:
  {
    "type": "bpmn:userTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
    }
  }
- **Send Task** (`<bpmn:sendTask>`), format:
  {
    "type": "bpmn:sendTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
      "messageRef": String // Optional
    }
  }
- **Receive Task** (`<bpmn:receiveTask>`), format:
  {
    "type": "bpmn:receiveTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
      "messageRef": String // Optional
    }
  }
- **Manual Task** (`<bpmn:manualTask>`), format:
  {
    "type": "bpmn:manualTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
    }
  }
- **Script Task** (`<bpmn:scriptTask>`), format:
  {
    "type": "bpmn:scriptTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
      "scriptFormat ": String, // Optional
      "script": String // Optional
    }
  }
- **Business rule task** (`<bpmn:businessRuleTask>`), format:
  {
    "type": "bpmn:businessRuleTask",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String,
      "laneId": String, // optional, avialble only when under a Lane
      "ruleSet": String
    }
  }

#### **Gateways**
- **Exclusive Gateway** (`<bpmn:exclusiveGateway>`), format:
  {
    "type": "bpmn:exclusiveGateway",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String, // Optional
      "laneId": String // optional, avialble only when under a Lane
    }
  }
- **Parallel Gateway** (`<bpmn:parallelGateway>`), format:
  {
    "type": "bpmn:parallelGateway",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String, // Optional
      "laneId": String // optional, avialble only when under a Lane
    }
  }
- **Inclusive Gateway** (`<bpmn:inclusiveGateway>`), format:
  {
    "type": "bpmn:inclusiveGateway",
    "id": String,
    "parentId": String,
    "name": String, // Optional
    "data": {
      "label": String, // Optional
      "laneId": String, // optional, avialble only when under a Lane
      "conditionExpressions": String
    }
  }



  - Examples

    - Exclusive Gateway  

      Source XML:
      ```xml
      <bpmn:exclusiveGateway id="Gateway_1234" name="Approval Decision">
        <bpmn:incoming>Flow_from_review</bpmn:incoming>
        <bpmn:outgoing>Flow_to_approve</bpmn:outgoing>
        <bpmn:outgoing>Flow_to_reject</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <!-- Incoming flow without name -->
      <bpmn:sequenceFlow id="Flow_from_review" sourceRef="Task_Review" targetRef="Gateway_1234" />
      <!-- Outgoing flows with names -->
      <bpmn:sequenceFlow id="Flow_to_approve" name="Approved?" sourceRef="Gateway_1234" targetRef="Task_Approve" />
      <bpmn:sequenceFlow id="Flow_to_reject" name="Rejected?" sourceRef="Gateway_1234" targetRef="Task_Reject" />
      ```

      Target JSON:
      ```json
      {
        "type": "bpmn:exclusiveGateway",
        "id": "Gateway_1234",
        "parentId": "process_1",
        "name": "Approval?"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_from_review",
        "source": "Task_Review",
        "target": "Gateway_1234",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_to_approve",
        "source": "Gateway_1234", 
        "target": "Task_Approve",
        "parentId": "process_1",
        "name": "Application Approved"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_to_reject",
        "source": "Gateway_1234",
        "target": "Task_Reject", 
        "parentId": "process_1",
        "name": "Rejected?"
      }
      ```

    - Parallel Gateway  

      Source XML:
      ```xml
      <bpmn:process id="process_1">
        <bpmn:startEvent id="startEvent" name="Start Document Processing">
          <bpmn:outgoing>seq-0</bpmn:outgoing>
        </bpmn:startEvent>
        
        <bpmn:serviceTask id="collectDocuments" name="Collect Documents">
          <bpmn:incoming>seq-0</bpmn:incoming>
          <bpmn:outgoing>seq-1</bpmn:outgoing>
        </bpmn:serviceTask>
        
        <bpmn:parallelGateway id="parallelGatewayStart" name="Split Document Processing">
          <bpmn:incoming>seq-1</bpmn:incoming>
          <bpmn:outgoing>seq-2</bpmn:outgoing>
          <bpmn:outgoing>seq-3</bpmn:outgoing>
        </bpmn:parallelGateway>
        
        <bpmn:serviceTask id="processDocumentA" name="Process Document A">
          <bpmn:incoming>seq-2</bpmn:incoming>
          <bpmn:outgoing>seq-4</bpmn:outgoing>
        </bpmn:serviceTask>
        
        <bpmn:serviceTask id="processDocumentB" name="Process Document B">
          <bpmn:incoming>seq-3</bpmn:incoming>
          <bpmn:outgoing>seq-5</bpmn:outgoing>
        </bpmn:serviceTask>
        
        <bpmn:parallelGateway id="parallelGatewayEnd" name="Join Document Processing">
          <bpmn:incoming>seq-4</bpmn:incoming>
          <bpmn:incoming>seq-5</bpmn:incoming>
          <bpmn:outgoing>seq-6</bpmn:outgoing>
        </bpmn:parallelGateway>
        
        <bpmn:serviceTask id="consolidateResults" name="Consolidate Results">
          <bpmn:incoming>seq-6</bpmn:incoming>
          <bpmn:outgoing>seq-7</bpmn:outgoing>
        </bpmn:serviceTask>
        
        <bpmn:endEvent id="endEvent" name="Document Processing Complete">
          <bpmn:incoming>seq-7</bpmn:incoming>
        </bpmn:endEvent>
        
        <!-- Sequence Flows -->
        <bpmn:sequenceFlow id="seq-0" sourceRef="startEvent" targetRef="collectDocuments" />
        <bpmn:sequenceFlow id="seq-1" sourceRef="collectDocuments" targetRef="parallelGatewayStart" />
        <bpmn:sequenceFlow id="seq-2" sourceRef="parallelGatewayStart" targetRef="processDocumentA" />
        <bpmn:sequenceFlow id="seq-3" sourceRef="parallelGatewayStart" targetRef="processDocumentB" />
        <bpmn:sequenceFlow id="seq-4" sourceRef="processDocumentA" targetRef="parallelGatewayEnd" />
        <bpmn:sequenceFlow id="seq-5" sourceRef="processDocumentB" targetRef="parallelGatewayEnd" />
        <bpmn:sequenceFlow id="seq-6" sourceRef="parallelGatewayEnd" targetRef="consolidateResults" />
        <bpmn:sequenceFlow id="seq-7" sourceRef="consolidateResults" targetRef="endEvent" />
      </bpmn:process>
      ```

      Target JSON:
      ```json
      {
        "type": "bpmn:parallelGateway",
        "id": "parallelGatewayStart",
        "parentId": "process_1",
        "name": "Split Document Processing"
      },
      {
        "type": "bpmn:parallelGateway",
        "id": "parallelGatewayEnd",
        "parentId": "process_1",
        "name": "Join Document Processing"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-1",
        "source": "collectDocuments",
        "target": "parallelGatewayStart",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-2",
        "source": "parallelGatewayStart",
        "target": "processDocumentA",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-3",
        "source": "parallelGatewayStart",
        "target": "processDocumentB",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-4",
        "source": "processDocumentA",
        "target": "parallelGatewayEnd",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-5",
        "source": "processDocumentB",
        "target": "parallelGatewayEnd",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "seq-6",
        "source": "parallelGatewayEnd",
        "target": "consolidateResults",
        "parentId": "process_1"
      }
      ```

    - Inclusive Gateway  

      Source XML:
      ```xml
      <bpmn:inclusiveGateway id="Gateway_release_review" name="Release Review Requirements">
        <bpmn:incoming>Flow_from_code_changes</bpmn:incoming>
        <bpmn:outgoing>Flow_to_security_review</bpmn:outgoing>
        <bpmn:outgoing>Flow_to_performance_review</bpmn:outgoing>
        <bpmn:outgoing>Flow_to_ui_review</bpmn:outgoing>
      </bpmn:inclusiveGateway>
      <!-- Incoming flow without name -->
      <bpmn:sequenceFlow id="Flow_from_code_changes" sourceRef="Task_CodeChanges" targetRef="Gateway_release_review" />
      <!-- Outgoing flows with names -->
      <bpmn:sequenceFlow id="Flow_to_security_review" name="Security Review Required" sourceRef="Gateway_release_review" targetRef="Task_SecurityReview" />
      <bpmn:sequenceFlow id="Flow_to_performance_review" name="Performance Review Required" sourceRef="Gateway_release_review" targetRef="Task_PerformanceReview" />
      <bpmn:sequenceFlow id="Flow_to_ui_review" name="UI Review Required" sourceRef="Gateway_release_review" targetRef="Task_UIReview" />
      ```

      Target JSON:
      ```json
      {
        "type": "bpmn:inclusiveGateway",
        "id": "Gateway_release_review",
        "parentId": "process_1",
        "name": "Release Review Requirements"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_from_code_changes",
        "source": "Task_CodeChanges",
        "target": "Gateway_release_review",
        "parentId": "process_1"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_to_security_review",
        "source": "Gateway_release_review",
        "target": "Task_SecurityReview",
        "parentId": "process_1",
        "name": "Security Review Required"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_to_performance_review",
        "source": "Gateway_release_review",
        "target": "Task_PerformanceReview",
        "parentId": "process_1",
        "name": "Performance Review Required"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "Flow_to_ui_review",
        "source": "Gateway_release_review",
        "target": "Task_UIReview",
        "parentId": "process_1",
        "name": "UI Review Required"
      }
      ```
      
- **Event Based Gateway** (`<bpmn:eventBasedGateway>`), format:
  {
    "type": "bpmn:eventBasedGateway",
    "id": String,
    "parentId": String,
    "name": String,        // Short descriptive name for the split point
    "data":{
      "laneId": String // optional, avialble only when under a Lane
    }
  }
  
#### **Connecting Objects**
- **Sequence Flow** (`<bpmn:sequenceFlow>`), format:
  {
    "type": "bpmn:sequenceFlow",
    "id": "String",
    "parentId": String,
    "source": "String", // ID of source node
    "target": "String", // ID of target node
    "data":
    {
        "label": "String", //optional, description 
        "conditionExpression": "String"
    }
}
- **MessageFlow Flow** (`<bpmn:messageFlow>`), format:
  {
    "type": "bpmn:messageFlow",
    "id": String,
    "parentId": String, // Id of collaboration
    "source": String,   // ID of source node
    "target": String,   // ID of target node
    "data": {
      "label": String,
      "messageRef": String // Optional
    }
  }

- **Association** (`<bpmn:association>`), format:
  {
    "type": "bpmn:association",
    "id": String,
    "parentId": String,
    "source": String,   // ID of source node
    "target": String,   // ID of target node
    "data": {
      "label": String
    }
  }
- **Data Input Association** (`<bpmn:dataInputAssociation>`), format:
  {
    "type": "bpmn:dataInputAssociation",
    "id": String,
    "parentId": String,
    "source": String,   // ID of source node
    "target": String,   // ID of target node
  }
- **Data Output Association** (`<bpmn:dataOutputAssociation>`), format:
  {
    "type": "bpmn:dataOutputAssociation",
    "id": String,
    "parentId": String,
    "source": String,   // ID of source node
    "target": String,   // ID of target node
  }

#### **Artifacts**

- **DataObjectReference** (`bpmn:dataObjectReference`), format:
  {
    "type": "bpmn:dataObjectReference",
    "id": String,
    "parentId": String,
    "name": String,
    "data": {
      "dataState": String, // optional
      "laneId": String // optional, avialble only when under a Lane
    }
  }
- **DataStoreReference** (`bpmn:dataStoreReference`), format:
  {
    "type": "bpmn:dataStoreReference",
    "id": String,
    "parentId": String,
    "name": String,
    "data": {
      "storageType ": String, // optional, value can be database or file
      "laneId": String // optional, avialble only when under a Lane
    }
  }
- **Text Annotation** (`<bpmn:textAnnotation>`), format:
  {
    "type": "bpmn:textAnnotation",
    "id": String,
    "parentId": String,
    "data": {
      "label": String,
      "laneId": String // optional, avialble only when under a Lane
      "textContent": String
    }
  }

  - Example
      - Text Annotation
        Assume in process: process_11567
        Source XML:

        ```xml
        <bpmn:textAnnotation id="Text_SYbFoN">
          <bpmn:documentation>a text description of the element</bpmn:documentation>
          <bpmn:text>This is a start</bpmn:text>
        </bpmn:textAnnotation>
        ```

        Target JSON:

        ```json
        {
          "type": "bpmn:textAnnotation",
          "id": "Text_SYbFoN",
          "parentId": "process_11567",
          "data": {
            "label": "a text description of the element",
            "textContent": "This is a start"
          }
        }
        ```

#### **Group** (`<bpmn:group>`), format:
  {
    "type": "bpmn:group",
    "id": String,
    "parentId": String,
    "name": String, // Optional but recommended
    "data": {
      "label": String, // Optional description of the group
      "nodes": [String] // Array of node IDs that belong to this group
    }
  }

  - **Example**
      Source XML:
      ```xml
      <bpmn:group id="Group_Intake" name="Data Intake Process">
        <bpmn:documentation>Group for data intake activities</bpmn:documentation>
      </bpmn:group>
      ```

      Target JSON:
      ```json
      {
          "type": "bpmn:group",
          "id": "Group_Intake",
          "parentId": "Process_12345",
          "name": "Data Intake Process",
          "data": {
              "label": "Group for data intake activities",
              "nodes": ["Task_ReceiveData", "Task_ValidateData", "Gateway_DataValid"]
          }
      }
      ```
        