prompt:
  input_schema: |-
      "userRequest":str, // user request, that contains the intention of picking a specific extension, and what inputs are expected in the extension
      "currentBpmn": str, // current bpmn xml
      "inputSchema": str, // the json schema that defines what fields are available in the extension, the type of the fields etc.
    

  output_schema: |-
      "inputJson": "Serialized JSON object string with configured values for the extension",
      "explanation": "Detailed explanation of how the JSON was generated based on user request"
    

  system: |-
    You are an expert system that helps users configure BPMN process extensions by generating valid JSON configurations based on user requests. Your task is to analyze the user's request, understand the available input schema, and produce a properly formatted JSON object that satisfies both the schema requirements and the user's intentions.

    ## Input Format:
    {input_schema}

    ## Your Process:

    1. Analyze the provided input which includes:
      - User's request describing what they want to accomplish
      - Current BPMN XML content for context
      - JSON schema defining the available fields and their types
      - Locate the task in the BPMN XML that the user wants to configure the extension for, and the extension that the user wants to configure for that task

    2. Extract the user's intentions regarding:
      - Which extension they want to use
      - What values they want to set for the extension's parameters

    3. Generate a valid JSON object that:
      - Conforms to the provided input schema
      - Matches types and constraints defined in the json schema
      - Includes appropriate values based on the user's request
      - Uses sensible defaults for any required fields not explicitly mentioned by the user

    4. Provide a clear explanation of how you interpreted the user's request and how the JSON was constructed

    Always ensure your output JSON strictly adheres to the schema provided in the input. Parse the schema carefully to understand required fields, data types, and constraints. 
    If the user request is ambiguous, make reasonable assumptions and explain them in your explanation.
    If the user request does not contain any input instruction for the extension, return an empty string in inputJson field, and explain why in the explanation field.

    Important: The "inputJson" field must contain a properly JSON object as a string, not a raw object. Ensure all quotes and special characters are properly escaped according to JSON serialization rules.

    ## Output Format:
    {output_schema}

    ## Examples:
    {examples}
   

  user: |-
    userRequest:
      {userRequest}

    currentBpmn:
    ```xml
      {currentBpmn}
    ```
    
    inputSchema:
    ```json
      {inputSchema}
    ```

  examples: |-
    ### Example 1:
      Input:
      ```json
      {{
        "userRequest": "Send an <NAME_EMAIL>, with subject of 'hello from Jack' and body asking about how it's going recently in the task 'send email to user'"
        "currentBpmn": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<bpmn:definitions xmlns:bpmn=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/MODEL\" xmlns:uipath=\"http:\/\/uipath.org\/schema\/bpmn\" xmlns:bpmndi=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/DI\" xmlns:dc=\"http:\/\/www.omg.org\/spec\/DD\/20100524\/DC\" xmlns:di=\"http:\/\/www.omg.org\/spec\/DD\/20100524\/DI\" id=\"sample-diagram\" targetNamespace=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/MODEL\" exporter=\"UiPath Studio Web (https:\/\/uipath.com)\" exporterVersion=\"f65d24af\">\r\n  <bpmn:process id=\"Proce\u2026ce=\"Connection\" resourceKey=\"4e89a57b-1cb3-459d-a9c7-2c32d18e8806\" propertyAttribute=\"ConnectionId\" \/>\r\n        <uipath:binding id=\"bZrNwfCqF\" name=\"FolderKey\" type=\"string\" default=\"13290d42-0f3e-4a55-a6f6-a6873b4c4f2b\" resource=\"Connection\" resourceKey=\"4e89a57b-1cb3-459d-a9c7-2c32d18e8806\" propertyAttribute=\"folderKey\" \/>\r\n\u2026        <di:waypoint x=\"446\" y=\"218\" \/>\r\n        <di:waypoint x=\"496\" y=\"218\" \/>\r\n      <\/bpmndi:BPMNEdge>\r\n    <\/bpmndi:BPMNPlane>\r\n  <\/bpmndi:BPMNDiagram>\r\n<\/bpmn:definitions>\r\n",
        "inputSchema": "{\"name\":\"send-mail-v2\",\"path\":\"\/send-mail-v2\",\"type\":\"standard\",\"subType\":\"standard\",\"elementKey\":\"uipath-microsoft-outlook365\",\"displayName\":\"Send Mail\",\"custom\":\"no\",\"metadata\":{\"hasCustomFieldDiscovery\":false,\"method\":{\"POST\":{\"operation\":\"Create\",\"description\":\"Send the message specified in the request body using either JSON.\",\"method\":\"POST\",\"parameters\":[{\"name\":\"saveAsDraft\",\"description\":\"Send an email message. By default, the email will be saved as draft.\",\"type\":\"query\",\"dataType\":\"boolean\",\"required\":false,\"defaultValue\":true,\"displayName\":\"Save as draft\",\"curated\":true,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"sortOrder\":1},{\"name\":\"file\",\"description\":\"The file to attach to the email\",\"type\":\"multipart\",\"dataType\":\"file\",\"required\":false,\"displayName\":\"Attachment\",\"curated\":true,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"sortOrder\":5},{\"name\":\"body\",\"description\":\"The object body\",\"type\":\"multipart\",\"dataType\":\"string\",\"required\":false,\"displayName\":\"Body\",\"curated\":false,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false},\"sortOrder\":13}],\"path\":\"\/hubs\/productivity\/send-mail-v2\",\"operationId\":\"createHubProductivitySendMailV2\",\"curated\":{\"name\":\"SendEmailV2\",\"displayName\":\"Send Email\",\"description\":\"Send an email message based on the input parameters\",\"isHidden\":false}}},\"hasSearchables\":false},\"fields\":{\"message.importance\":{\"name\":\"message.importance\",\"type\":\"string\",\"nativeType\":\"string\",\"enum\":[{\"name\":\"High\",\"value\":\"high\"},{\"name\":\"Normal\",\"value\":\"normal\"},{\"name\":\"Low\",\"value\":\"low\"}],\"displayName\":\"Importance\",\"description\":\"The importance of the email\",\"sampleValue\":\"high\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"secondary\"},\"isPriority\":false,\"sortOrder\":7,\"isCuratedEventField\":false},\"message.ccRecipients\":{\"name\":\"message.ccRecipients\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"CC\",\"description\":\"The secondary recipients of the email, separated by comma (,)\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"secondary\"},\"isPriority\":false,\"sortOrder\":8,\"isCuratedEventField\":false},\"message.body.contentType\":{\"name\":\"message.body.contentType\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"Message body content type\",\"description\":\"Specifies the format of the email body, such as plain text or HTML.\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"isPriority\":false,\"sortOrder\":11,\"isCuratedEventField\":false},\"message.replyTo\":{\"name\":\"message.replyTo\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"Reply to\",\"description\":\"The email addresses to use when replying, separated by comma (,)\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":true,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"secondary\"},\"isPriority\":false,\"sortOrder\":6,\"isCuratedEventField\":false},\"message.bccRecipients\":{\"name\":\"message.bccRecipients\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"BCC\",\"description\":\"The hidden recipients of the email, separated by comma (,)\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"secondary\"},\"isPriority\":false,\"sortOrder\":9,\"isCuratedEventField\":false},\"message.body.content\":{\"name\":\"message.body.content\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"Body\",\"description\":\"The body of the email\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":true,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"component\":\"RichTextEditorHTML\",\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"isPriority\":false,\"sortOrder\":4,\"isCuratedEventField\":false},\"saveToSentItems\":{\"name\":\"saveToSentItems\",\"type\":\"boolean\",\"nativeType\":\"string\",\"displayName\":\"Save to sent items\",\"description\":\"Indicates if the email should be saved in the sent items folder.\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":false,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false},\"isPriority\":false,\"sortOrder\":12,\"defaultValue\":true,\"isCuratedEventField\":false},\"message.toRecipients\":{\"name\":\"message.toRecipients\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"To\",\"description\":\"The main recipients of the email, separated by comma(,)\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":true,\"responseCurated\":false,\"request\":true,\"required\":true}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"isPriority\":false,\"sortOrder\":2,\"isCuratedEventField\":false},\"status\":{\"name\":\"status\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"Status\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":true,\"requestCurated\":false,\"responseCurated\":false,\"request\":false,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"isPriority\":false,\"isCuratedEventField\":false},\"message.subject\":{\"name\":\"message.subject\",\"type\":\"string\",\"nativeType\":\"string\",\"displayName\":\"Subject\",\"description\":\"The subject of the email\",\"sampleValue\":\"\",\"primaryKey\":false,\"method\":{\"POST\":{\"name\":\"POST\",\"response\":false,\"requestCurated\":true,\"responseCurated\":false,\"request\":true,\"required\":false}},\"searchable\":false,\"custom\":\"no\",\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"isPriority\":false,\"sortOrder\":3,\"isCuratedEventField\":false}},\"isPriority\":false,\"isHidden\":false}",
       }}
      ```

      Output:
      ```json
      {{
        "explanation": "Based on the user request, I've configured the extension to send an <NAME_EMAIL> with the subject 'hello from Jack' and a body asking about how it's going recently.",
        "inputJson": "{\"message\":{\"toRecipients\":\"<EMAIL>\",\"subject\":\"hello from Jack\",\"body\":{\"contentType\":\"html\",\"content\":\"<p>Hi John,<br><br>Just wanted to check in \u2014 how's it going recently?<\/p>\"}},\"saveToSentItems\":true}"
      }}
      ```

    ### Example 2:
      Input:
      ```json
      {{
        "userRequest": "Analyze the stock for AAPL",
        "inputSchema": "{\"type\":\"object\",\"properties\":{\"stock_symbol\":{\"type\":\"string\",\"description\":\"The stock symbol to analyze\"}},\"required\":[\"stock_symbol\"]}",
        "currentBpmn": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<bpmn:definitions xmlns:bpmn=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/MODEL\" xmlns:uipath=\"http:\/\/uipath.org\/schema\/bpmn\" xmlns:bpmndi=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/DI\" xmlns:dc=\"http:\/\/www.omg.org\/spec\/DD\/20100524\/DC\" xmlns:di=\"http:\/\/www.omg.org\/spec\/DD\/20100524\/DI\" id=\"sample-diagram\" targetNamespace=\"http:\/\/www.omg.org\/spec\/BPMN\/20100524\/MODEL\" exporter=\"UiPath Studio Web (https:\/\/uipath.com)\" exporterVersion=\"81217a21\">\r\n  <bpmn:process id=\"Process_1\" isExecutable=\"false\">\r\n    <bpmn:extensionElements>\r\n      <uipath:variables version=\"v1\">\r\n        <uipath:inputOutput id=\"vJhIGKdkt\" name=\"Error\" type=\"any\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"vBufamBee\" name=\"stock_overview\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"v9ii9EvfT\" name=\"current_market_data\" type=\"object\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"vU9q2wnJb\" name=\"financial_analysis\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"vb15dQBCF\" name=\"market_sentiment\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"vdzDh74Vn\" name=\"recommendation\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"v8IXypyR8\" name=\"justification\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n        <uipath:inputOutput id=\"vE7R8TgMu\" name=\"option_strategy\" type=\"string\" elementId=\"Activity_F6jrCC\" \/>\r\n      <\/uipath:variables>\r\n      <uipath:bindings version=\"v1\">\r\n        <uipath:binding id=\"bPPD0bXJi\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"app\" resourceKey=\"Solution Folder.AOHITL-UPv1\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"bTVyr3WTd\" name=\"name\" type=\"string\" default=\"AOHITL-UPv1\" resource=\"app\" resourceKey=\"Solution Folder.AOHITL-UPv1\" propertyAttribute=\"name\" \/>\r\n        <uipath:binding id=\"b5Jd4Kuek\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"process\" resourceKey=\"Solution Folder.all-hands-demo-01\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"bz7oElfHQ\" name=\"name\" type=\"string\" default=\"all-hands-demo-01\" resource=\"process\" resourceKey=\"Solution Folder.all-hands-demo-01\" propertyAttribute=\"name\" \/>\r\n        <uipath:binding id=\"bY7b0vNiN\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"process\" resourceKey=\"Solution Folder.deposit\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"bZTsK4Umm\" name=\"name\" type=\"string\" default=\"deposit\" resource=\"process\" resourceKey=\"Solution Folder.deposit\" propertyAttribute=\"name\" \/>\r\n        <uipath:binding id=\"bs9JIoctx\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"process\" resourceKey=\"Solution Folder.EchoRobot\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"bqE2exrYg\" name=\"name\" type=\"string\" default=\"EchoRobot\" resource=\"process\" resourceKey=\"Solution Folder.EchoRobot\" propertyAttribute=\"name\" \/>\r\n        <uipath:binding id=\"bM47cfEfZ\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"process\" resourceSubType=\"Agent\" resourceKey=\"Solution Folder.Completion_agent\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"b0U8fqGLX\" name=\"name\" type=\"string\" default=\"Completion_agent\" resource=\"process\" resourceSubType=\"Agent\" resourceKey=\"Solution Folder.Completion_agent\" propertyAttribute=\"name\" \/>\r\n        <uipath:binding id=\"bZePGSYbH\" name=\"folderPath\" type=\"string\" default=\"Solution Folder\" resource=\"process\" resourceSubType=\"Agent\" resourceKey=\"Solution Folder.Stock-analyst\" propertyAttribute=\"folderPath\" \/>\r\n        <uipath:binding id=\"bXDRPNkK5\" name=\"name\" type=\"string\" default=\"Stock-analyst\" resource=\"process\" resourceSubType=\"Agent\" resourceKey=\"Solution Folder.Stock-analyst\" propertyAttribute=\"name\" \/>\r\n      <\/uipath:bindings>\r\n    <\/bpmn:extensionElements>\r\n    <bpmn:startEvent id=\"Event_start\">\r\n      <bpmn:extensionElements>\r\n        <uipath:entryPointId value=\"ea649ecb-b8e0-4097-afde-3d999e73bb74\" \/>\r\n      <\/bpmn:extensionElements>\r\n      <bpmn:outgoing>edge_Event_start-Activity_F6jrCC<\/bpmn:outgoing>\r\n    <\/bpmn:startEvent>\r\n    <bpmn:serviceTask id=\"Activity_F6jrCC\" name=\"analyze_stock\">\r\n      <bpmn:extensionElements>\r\n        <uipath:activity version=\"v1\">\r\n          <uipath:type value=\"Orchestrator.StartAgentJob\" version=\"v2\" \/>\r\n          <uipath:context>\r\n            <uipath:input name=\"name\" type=\"string\" value=\"=bindings.bXDRPNkK5\" \/>\r\n            <uipath:input name=\"folderPath\" type=\"string\" value=\"=bindings.bZePGSYbH\" \/>\r\n            <uipath:input name=\"_label\" type=\"string\" value=\"Stock-analyst\" \/>\r\n          <\/uipath:context>\r\n          <uipath:input name=\"JobArguments\" type=\"json\" target=\"bodyField\"><![CDATA[{\"stock_symbol\":\"\"}]]><\/uipath:input>\r\n          <uipath:output name=\"Error\" type=\"any\" source=\"=Error\" var=\"vJhIGKdkt\" \/>\r\n          <uipath:output name=\"stock_overview\" type=\"string\" source=\"=stock_overview\" var=\"vBufamBee\" \/>\r\n          <uipath:output name=\"current_market_data\" type=\"object\" source=\"=current_market_data\" var=\"v9ii9EvfT\" \/>\r\n          <uipath:output name=\"financial_analysis\" type=\"string\" source=\"=financial_analysis\" var=\"vU9q2wnJb\" \/>\r\n          <uipath:output name=\"market_sentiment\" type=\"string\" source=\"=market_sentiment\" var=\"vb15dQBCF\" \/>\r\n          <uipath:output name=\"recommendation\" type=\"string\" source=\"=recommendation\" var=\"vdzDh74Vn\" \/>\r\n          <uipath:output name=\"justification\" type=\"string\" source=\"=justification\" var=\"v8IXypyR8\" \/>\r\n          <uipath:output name=\"option_strategy\" type=\"string\" source=\"=option_strategy\" var=\"vE7R8TgMu\" \/>\r\n        <\/uipath:activity>\r\n      <\/bpmn:extensionElements>\r\n      <bpmn:incoming>edge_Event_start-Activity_F6jrCC<\/bpmn:incoming>\r\n    <\/bpmn:serviceTask>\r\n    <bpmn:sequenceFlow id=\"edge_Event_start-Activity_F6jrCC\" sourceRef=\"Event_start\" targetRef=\"Activity_F6jrCC\" \/>\r\n  <\/bpmn:process>\r\n  <bpmndi:BPMNDiagram id=\"BPMNDiagram_1\">\r\n    <bpmndi:BPMNPlane id=\"BPMNPlane_1\" bpmnElement=\"Process_1\">\r\n      <bpmndi:BPMNShape id=\"S_Event_start\" bpmnElement=\"Event_start\">\r\n        <dc:Bounds x=\"260\" y=\"200\" width=\"36\" height=\"36\" \/>\r\n        <bpmndi:BPMNLabel>\r\n          <dc:Bounds x=\"260\" y=\"241\" width=\"36\" height=\"14\" \/>\r\n        <\/bpmndi:BPMNLabel>\r\n      <\/bpmndi:BPMNShape>\r\n      <bpmndi:BPMNShape id=\"S_Activity_F6jrCC\" bpmnElement=\"Activity_F6jrCC\">\r\n        <dc:Bounds x=\"346\" y=\"178\" width=\"100\" height=\"80\" \/>\r\n        <bpmndi:BPMNLabel>\r\n          <dc:Bounds x=\"346\" y=\"263\" width=\"100\" height=\"14\" \/>\r\n        <\/bpmndi:BPMNLabel>\r\n      <\/bpmndi:BPMNShape>\r\n      <bpmndi:BPMNEdge id=\"BPMNEdge_edge_Event_start-Activity_F6jrCC\" bpmnElement=\"edge_Event_start-Activity_F6jrCC\">\r\n        <di:waypoint x=\"296\" y=\"218\" \/>\r\n        <di:waypoint x=\"346\" y=\"218\" \/>\r\n      <\/bpmndi:BPMNEdge>\r\n    <\/bpmndi:BPMNPlane>\r\n  <\/bpmndi:BPMNDiagram>\r\n<\/bpmn:definitions>\r\n"
      }}      
      ```

      Output:
      ```json
      {{
        "explanation": "Based on the user request, I've configured the extension to analyze the stock for AAPL.",
        "inputJson": "{\"stock_symbol\":\"AAPL\"}"
      }}
      ```
    
