# Process examples
Here are some examples for process:
##Textual description:
"Conver the following image to BPMN process"
##Current Image attached:
"Base 64 converted image of an image showing bpmn process for bank credit card approval process"
##Expected BPMN model in JSON format:
```json
{
    "explanation": "Here's the BPMN for the bank credit card approval process. It covers submitting and reviewing the application, followed by either scheduling an interview or sending a rejection email based on the decision.",
    "title": "Generate credit card approve process",
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:startEvent",
            "id": "startEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "Start Event",
            "data": {
                "label": "Start Credit Card Application"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "submitApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Submit Application",
            "data": {
                "label": "Submit Application"
            }
        },
        {
            "type": "bpmn:serviceTask",
            "id": "reviewApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Review Application",
            "data": {
                "label": "Review the submitted application"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval",
            "name": "Suitability Gateway",
            "data": {
                "label": "Approved?"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "name": "Schedule Interview",
            "data": {
                "label": "Schedule Interview"
            }
        },
        {
            "type": "bpmn:sendTask",
            "id": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "name": "Send Rejection Email",
            "data": {
                "label": "Send Rejection Email"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "End Event",
            "data": {
                "label": "End Event"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-0",
            "source": "startEvent",
            "target": "submitApplication",
            "parentId": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-1",
            "source": "submitApplication",
            "target": "reviewApplication",
            "parentId": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-2",
            "source": "reviewApplication",
            "target": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-3",
            "source": "suitabilityGateway",
            "target": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "Yes"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-4",
            "source": "suitabilityGateway",
            "target": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "No"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-5",
            "source": "scheduleInterview",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-6",
            "source": "sendRejectionEmail",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval"
        }
    ],
    "update": [],
    "delete": []
}
```
