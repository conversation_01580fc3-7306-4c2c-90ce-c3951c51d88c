Here are some examples for Business Telemetry:
# Example 1
## Current BPMN Model (XML):
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="b9d58e12">
  <bpmn:process id="Process_PurchaseToPay">
    <bpmn:startEvent id="startEvent" name="Start Purchase to Pay Process">
      <bpmn:extensionElements>
        <uipath:entryPointId value="8027ee2f-bdf6-4d7e-b62b-3a37a9fea60a"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>seq-0</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="createRequisition" name="Create Purchase Requisition">
      <bpmn:incoming>seq-0</bpmn:incoming>
      <bpmn:outgoing>seq-1</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="approveRequisition" name="Approve Purchase Requisition">
      <bpmn:incoming>seq-1</bpmn:incoming>
      <bpmn:outgoing>seq-2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="createPurchaseOrder" name="Create Purchase Order">
      <bpmn:incoming>seq-2</bpmn:incoming>
      <bpmn:outgoing>seq-3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sendTask id="sendOrderToSupplier" name="Send Purchase Order to Supplier">
      <bpmn:incoming>seq-3</bpmn:incoming>
      <bpmn:outgoing>seq-4</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:receiveTask id="receiveGoods" name="Receive Goods">
      <bpmn:incoming>seq-4</bpmn:incoming>
      <bpmn:outgoing>seq-5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:userTask id="performQualityCheck" name="Perform Quality Check">
      <bpmn:incoming>seq-5</bpmn:incoming>
      <bpmn:outgoing>seq-6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="qualityCheckGateway" name="Quality Check Passed?">
      <bpmn:incoming>seq-6</bpmn:incoming>
      <bpmn:outgoing>seq-7</bpmn:outgoing>
      <bpmn:outgoing>seq-8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="returnGoods" name="Return Goods to Supplier">
      <bpmn:incoming>seq-7</bpmn:incoming>
    </bpmn:userTask>
    <bpmn:userTask id="processInvoice" name="Process Supplier Invoice">
      <bpmn:incoming>seq-8</bpmn:incoming>
      <bpmn:outgoing>seq-9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="makePayment" name="Make Payment to Supplier">
      <bpmn:incoming>seq-9</bpmn:incoming>
      <bpmn:outgoing>seq-10</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="endEvent" name="End Purchase to Pay Process">
      <bpmn:incoming>seq-10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="seq-0" sourceRef="startEvent" targetRef="createRequisition"/>
    <bpmn:sequenceFlow id="seq-1" sourceRef="createRequisition" targetRef="approveRequisition"/>
    <bpmn:sequenceFlow id="seq-2" sourceRef="approveRequisition" targetRef="createPurchaseOrder"/>
    <bpmn:sequenceFlow id="seq-3" sourceRef="createPurchaseOrder" targetRef="sendOrderToSupplier"/>
    <bpmn:sequenceFlow id="seq-4" sourceRef="sendOrderToSupplier" targetRef="receiveGoods"/>
    <bpmn:sequenceFlow id="seq-5" sourceRef="receiveGoods" targetRef="performQualityCheck"/>
    <bpmn:sequenceFlow id="seq-6" sourceRef="performQualityCheck" targetRef="qualityCheckGateway"/>
    <bpmn:sequenceFlow id="seq-7" name="No" sourceRef="qualityCheckGateway" targetRef="returnGoods"/>
    <bpmn:sequenceFlow id="seq-8" name="Yes" sourceRef="qualityCheckGateway" targetRef="processInvoice"/>
    <bpmn:sequenceFlow id="seq-9" sourceRef="processInvoice" targetRef="makePayment"/>
    <bpmn:sequenceFlow id="seq-10" sourceRef="makePayment" targetRef="endEvent"/>
  </bpmn:process>
</bpmn:definitions>
```
## User request:
If the quality check fails more than once, route to a task 'Supervisor Review'. Add a counter (as data condition) to track attempts and an exclusive gateway after 'Return Goods to Supplier' to either repeat the quality check or escalate.
## AI response:
```json
{
    "valid": true,
    "results": [
        {
            "tool": "edit-bpmn",
            "explanation": "I've added a counter to track quality check attempts and a new exclusive gateway after 'Return Goods to Supplier' that routes either back to quality check or to a new 'Supervisor Review' task when attempts exceed 1.",
            "title": "Add quality check counter",
            "add": [
                {
                    "type": "bpmn:exclusiveGateway",
                    "id": "Gateway_RetryOrEscalate",
                    "parentId": "Process_PurchaseToPay",
                    "name": "Check Attempts"
                },
                {
                    "type": "bpmn:userTask",
                    "id": "Task_SupervisorReview",
                    "parentId": "Process_PurchaseToPay",
                    "name": "Supervisor Review"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "seq-11",
                    "source": "returnGoods",
                    "target": "Gateway_RetryOrEscalate",
                    "parentId": "Process_PurchaseToPay"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "seq-12",
                    "source": "Gateway_RetryOrEscalate",
                    "target": "performQualityCheck",
                    "parentId": "Process_PurchaseToPay",
                    "data": {
                        "label": "First attempt (attempts <= 1)",
                        "conditionExpression": "${attempts <= 1}"
                    }
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "seq-13",
                    "source": "Gateway_RetryOrEscalate",
                    "target": "Task_SupervisorReview",
                    "parentId": "Process_PurchaseToPay",
                    "data": {
                        "label": "Multiple attempts (attempts > 1)",
                        "conditionExpression": "${attempts > 1}"
                    }
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "seq-14",
                    "source": "Task_SupervisorReview",
                    "target": "processInvoice",
                    "parentId": "Process_PurchaseToPay"
                },
                {
                    "type": "bpmn:dataObjectReference",
                    "id": "DataObject_QualityAttempts",
                    "parentId": "Process_PurchaseToPay",
                    "name": "Quality Check Attempts"
                },
                {
                    "type": "bpmn:association",
                    "id": "Association_1",
                    "parentId": "Process_PurchaseToPay",
                    "source": "DataObject_QualityAttempts",
                    "target": "Gateway_RetryOrEscalate",
                    "data": {
                        "label": "Tracks number of attempts"
                    }
                }
            ],
            "update": [],
            "delete": []
        }
    ]
}
```
## User Reaction:
["accept"]
## Expected result
```json
{
    "userRequest": "The user intends to implement a conditional escalation mechanism based on the number of previous attempts. After a specific activity, a decision point should evaluate a retry counter. If the number of attempts is below a defined limit, the process should repeat a verification step. If the limit is exceeded, the process should escalate to a supervisory review step",
    "aiResponse": "The system modifies the process to introduce a conditional branching mechanism based on the number of times a certain activity has been repeated. It adds a decision gateway that evaluates a data object representing attempt count. If the number of attempts is within an acceptable limit, the flow continues back to the verification step. If the limit is exceeded, the flow is directed to an escalation step involving human intervention. The data object is associated with the decision point to indicate it is used for evaluating the condition.",
    "explanation": "The user likely selected “Accept” because the system’s modification fully and accurately implements the requested logic:
    - All specified logic was implemented exactly
    - The structure introduced retry control and escalation as requested
    - No unintended changes or deletions occurred"
}
```
# Example 2
## Current BPMN Model (XML):
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="505a97d2">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:startEvent id="Event_start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="2afffd27-bee2-4e72-a8ab-940f78467914"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_start_requisition</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_create_requisition" name="Create Purchase Requisition">
      <bpmn:incoming>Flow_start_requisition</bpmn:incoming>
      <bpmn:outgoing>Flow_requisition_approval</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_approval_decision" name="Requisition Approved?">
      <bpmn:incoming>Flow_requisition_approval</bpmn:incoming>
      <bpmn:outgoing>Flow_approval_yes</bpmn:outgoing>
      <bpmn:outgoing>Flow_approval_no</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_create_po" name="Create Purchase Order">
      <bpmn:incoming>Flow_approval_yes</bpmn:incoming>
      <bpmn:outgoing>Flow_po_send</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sendTask id="Task_send_po" name="Send PO to Supplier">
      <bpmn:incoming>Flow_po_send</bpmn:incoming>
      <bpmn:outgoing>Flow_send_receive</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:userTask id="Task_receive_goods" name="Receive Goods">
      <bpmn:incoming>Flow_send_receive</bpmn:incoming>
      <bpmn:outgoing>Flow_receive_quality</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_quality_check" name="Perform Quality Check">
      <bpmn:incoming>Flow_receive_quality</bpmn:incoming>
      <bpmn:outgoing>Flow_quality_decision</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_quality_decision" name="Quality Check Passed?">
      <bpmn:incoming>Flow_quality_decision</bpmn:incoming>
      <bpmn:outgoing>Flow_quality_fail</bpmn:outgoing>
      <bpmn:outgoing>Flow_quality_pass</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_return_goods" name="Return Goods to Supplier">
      <bpmn:incoming>Flow_quality_fail</bpmn:incoming>
      <bpmn:outgoing>Flow_return_end</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_return_end" name="Goods Returned">
      <bpmn:incoming>Flow_return_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:userTask id="Task_notify_rejection" name="Notify Requisition Rejection">
      <bpmn:incoming>Flow_approval_no</bpmn:incoming>
      <bpmn:outgoing>Flow_rejection_end</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_rejection_end" name="Requisition Rejected">
      <bpmn:incoming>Flow_rejection_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:receiveTask id="Task_receive_invoice" name="Receive Invoice">
      <bpmn:incoming>Flow_quality_pass</bpmn:incoming>
      <bpmn:incoming>Flow_dispute_receive</bpmn:incoming>
      <bpmn:outgoing>Flow_invoice_verify</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:userTask id="Task_verify_invoice" name="Verify Invoice">
      <bpmn:incoming>Flow_invoice_verify</bpmn:incoming>
      <bpmn:outgoing>Flow_verify_decision</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_invoice_decision" name="Invoice Correct?">
      <bpmn:incoming>Flow_verify_decision</bpmn:incoming>
      <bpmn:outgoing>Flow_invoice_no</bpmn:outgoing>
      <bpmn:outgoing>Flow_invoice_yes</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_dispute_invoice" name="Dispute Invoice with Supplier">
      <bpmn:incoming>Flow_invoice_no</bpmn:incoming>
      <bpmn:outgoing>Flow_dispute_receive</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_process_payment" name="Process Payment">
      <bpmn:incoming>Flow_invoice_yes</bpmn:incoming>
      <bpmn:outgoing>Flow_payment_end</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_process_end" name="Payment Complete">
      <bpmn:incoming>Flow_payment_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_start_requisition" sourceRef="Event_start" targetRef="Task_create_requisition"/>
    <bpmn:sequenceFlow id="Flow_requisition_approval" sourceRef="Task_create_requisition" targetRef="Gateway_approval_decision"/>
    <bpmn:sequenceFlow id="Flow_approval_yes" name="Yes" sourceRef="Gateway_approval_decision" targetRef="Task_create_po"/>
    <bpmn:sequenceFlow id="Flow_approval_no" name="No" sourceRef="Gateway_approval_decision" targetRef="Task_notify_rejection"/>
    <bpmn:sequenceFlow id="Flow_po_send" sourceRef="Task_create_po" targetRef="Task_send_po"/>
    <bpmn:sequenceFlow id="Flow_send_receive" sourceRef="Task_send_po" targetRef="Task_receive_goods"/>
    <bpmn:sequenceFlow id="Flow_receive_quality" sourceRef="Task_receive_goods" targetRef="Task_quality_check"/>
    <bpmn:sequenceFlow id="Flow_quality_decision" sourceRef="Task_quality_check" targetRef="Gateway_quality_decision"/>
    <bpmn:sequenceFlow id="Flow_quality_fail" name="No" sourceRef="Gateway_quality_decision" targetRef="Task_return_goods"/>
    <bpmn:sequenceFlow id="Flow_quality_pass" name="Yes" sourceRef="Gateway_quality_decision" targetRef="Task_receive_invoice"/>
    <bpmn:sequenceFlow id="Flow_return_end" sourceRef="Task_return_goods" targetRef="Event_return_end"/>
    <bpmn:sequenceFlow id="Flow_rejection_end" sourceRef="Task_notify_rejection" targetRef="Event_rejection_end"/>
    <bpmn:sequenceFlow id="Flow_invoice_verify" sourceRef="Task_receive_invoice" targetRef="Task_verify_invoice"/>
    <bpmn:sequenceFlow id="Flow_verify_decision" sourceRef="Task_verify_invoice" targetRef="Gateway_invoice_decision"/>
    <bpmn:sequenceFlow id="Flow_invoice_no" name="No" sourceRef="Gateway_invoice_decision" targetRef="Task_dispute_invoice"/>
    <bpmn:sequenceFlow id="Flow_invoice_yes" name="Yes" sourceRef="Gateway_invoice_decision" targetRef="Task_process_payment"/>
    <bpmn:sequenceFlow id="Flow_dispute_receive" sourceRef="Task_dispute_invoice" targetRef="Task_receive_invoice"/>
    <bpmn:sequenceFlow id="Flow_payment_end" sourceRef="Task_process_payment" targetRef="Event_process_end"/>
  </bpmn:process>
</bpmn:definitions>
```
## User request:
Update the process to replace the 'Perform Quality Check' with two user tasks: 'Visual Review (assigned to Maria R.)' followed by 'Technical Inspection (handled by QA specialist Daniel Liu)
## AI response:
```json
{
    "valid": true,
    "results": [
        {
            "tool": "edit-bpmn",
            "explanation": "I've replaced the 'Perform Quality Check' task with two sequential user tasks: 'Visual Review' assigned to Maria R. followed by 'Technical Inspection' handled by Daniel Liu from QA.",
            "title": "Replace Quality Check Task",
            "add": [
                {
                    "type": "bpmn:serviceTask",
                    "id": "Task_visual_review",
                    "parentId": "Process_1",
                    "name": "Visual Review"
                },
                {
                    "type": "bpmn:serviceTask",
                    "id": "Task_technical_inspection",
                    "parentId": "Process_1",
                    "name": "Technical Inspection"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "Flow_receive_visual",
                    "source": "Task_receive_goods",
                    "target": "Task_visual_review",
                    "parentId": "Process_1"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "Flow_visual_technical",
                    "source": "Task_visual_review",
                    "target": "Task_technical_inspection",
                    "parentId": "Process_1"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "Flow_technical_decision",
                    "source": "Task_technical_inspection",
                    "target": "Gateway_quality_decision",
                    "parentId": "Process_1"
                }
            ],
            "update": [],
            "delete": [
                {
                    "type": "bpmn:userTask",
                    "id": "Task_quality_check"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "Flow_receive_quality"
                },
                {
                    "type": "bpmn:sequenceFlow",
                    "id": "Flow_quality_decision"
                }
            ]
        }
    ]
}
```
## User Reaction:
["reject"]
## Expected result
```json
{
    "userRequest": "The user intends to decompose an existing verification task into two sequential user tasks, each representing a different level of inspection. The updated structure should reflect both a preliminary visual assessment and a detailed technical review, with clear responsibility separation.",
    "aiResponse": "The system restructures the process by replacing a single user task with two sequential service tasks, each representing a different stage of an inspection. The new tasks include labels containing personally identifiable information, indicating role-based responsibility. To support this change, the system adds new sequence flows to maintain process continuity and removes the original task and its related connections to avoid duplication and ensure correctness.",
    "explanation": "The user chose to reject the response because, although the structure and flow aligned with the request, the task types were incorrectly set as serviceTasks instead of the requested userTasks, which changed the intended execution of the process"
}
```