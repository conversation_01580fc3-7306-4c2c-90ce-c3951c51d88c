{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "BPMN patch schema", "type": "object", "properties": {"explanation": {"type": "string"}, "title": {"type": "string"}, "add": {"type": "array", "items": {"oneOf": [{"$ref": "#/$defs/Node"}, {"$ref": "#/$defs/Edge"}]}}, "update": {"type": "array", "items": {"oneOf": [{"$ref": "#/$defs/Node"}, {"$ref": "#/$defs/Edge"}]}}, "delete": {"type": "array", "items": {"$ref": "#/$defs/BaseElement"}}}, "$defs": {"BaseElement": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["bpmn:Task", "bpmn:UserTask", "bpmn:ServiceTask", "bpmn:ScriptTask", "bpmn:BusinessRuleTask", "bpmn:SendTask", "bpmn:ReceiveTask", "bpmn:ManualTask", "bpmn:ExclusiveGateway", "bpmn:ParallelGateway", "bpmn:InclusiveGateway", "bpmn:EventBasedGateway", "bpmn:Process", "bpmn:SubProcess", "bpmn:LaneSet", "bpmn:ChildLaneSet", "bpmn:Lane", "bpmn:Collaboration", "bpmn:Participant", "bpmn:StartEvent", "bpmn:IntermediateEvent", "bpmn:MessageEvent", "bpmn:EndEvent", "bpmn:SequenceFlow", "bpmn:MessageFlow", "bpmn:Group"]}}, "required": ["id", "type"]}, "Node": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "parentId": {"type": "string", "description": "Id of the parent element"}, "type": {"enum": ["bpmn:Task", "bpmn:UserTask", "bpmn:ServiceTask", "bpmn:ScriptTask", "bpmn:BusinessRuleTask", "bpmn:SendTask", "bpmn:ReceiveTask", "bpmn:ManualTask", "bpmn:ExclusiveGateway", "bpmn:ParallelGateway", "bpmn:InclusiveGateway", "bpmn:EventBasedGateway", "bpmn:Process", "bpmn:SubProcess", "bpmn:LaneSet", "bpmn:ChildLaneSet", "bpmn:Lane", "bpmn:Collaboration", "bpmn:Participant", "bpmn:StartEvent", "bpmn:IntermediateEvent", "bpmn:EndEvent", "bpmn:boundaryEvent", "bpmn:Group"]}, "data": {"if": {"properties": {"type": {"enum": ["bpmn:StartEvent", "bpmn:IntermediateEvent", "bpmn:EndEvent", "bpmn:boundaryEvent"]}}}, "then": {"$ref": "#/$defs/EventData"}, "else": {"if": {"properties": {"type": {"enum": ["bpmn:Group"]}}}, "then": {"$ref": "#/$defs/GroupData"}, "else": {"$ref": "#/$defs/Data"}}}}, "if": {"properties": {"type": {"enum": ["bpmn:Process"]}}, "required": ["id", "type"]}, "then": {"required": ["id", "type", "name"]}}, "Edge": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "source": {"type": "string", "description": "Source element id of the edge"}, "target": {"type": "string", "description": "Target element id of the edge"}, "type": {"enum": ["bpmn:SequenceFlow", "bpmn:MessageFlow"]}, "data": {"$ref": "#/$defs/Data"}}, "required": ["id", "type", "source", "target"]}, "Data": {"type": "object", "properties": {"label": {"type": "string", "description": "Description of the element."}, "processRef": {"type": "string"}, "errorCode": {"type": "string"}, "escalationCode": {"type": "string"}, "conditionExpression": {"type": "string"}}}, "GroupData": {"type": "object", "properties": {"label": {"type": "string", "description": "Description of the group."}, "nodes": {"type": "array", "description": "Array of node IDs that belong to this group.", "items": {"type": "string"}}}}, "EventData": {"type": "object", "properties": {"label": {"type": "string"}, "eventDefinition": {"$ref": "#/$defs/EventDefinition", "description": "Event definition of the element."}}}, "EventDefinition": {"type": "object", "properties": {"type": {"type": "string", "enum": ["Timer", "Message", "Signal", "Error", "Escalation", "Conditional", "Compensation", "Conditional", "Terminate"]}, "timeDuration": {"type": "string"}, "timeCycle": {"type": "string"}, "timeDate": {"type": "string"}, "signalRef": {"type": "string"}, "messageRef": {"type": "string"}, "errorCode": {"type": "string"}, "escalationCode": {"type": "string"}, "conditionExpression": {"type": "string"}, "activityRef": {"type": "string"}, "isInterrupting": {"type": "boolean"}}, "required": ["type"]}}}