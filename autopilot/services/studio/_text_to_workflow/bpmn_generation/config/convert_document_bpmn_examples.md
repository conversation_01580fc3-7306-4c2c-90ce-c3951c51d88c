# BPMN Process Examples for Document Processing
Here are examples showing proper BPMN structure with decision points and comprehensive business logic for document-based content:

## Example 1: Inventory Restocking Process

**Textual Description:**
"Create BPMN from inventory restocking process"

**Extracted Content:**
```
Inventory Restock Process
1. Open inventory status sheet in Google Sheets
2. Check stock levels against reorder thresholds
3. Identify products that need restocking
4. Calculate quantity differences
5. Open email client and compose new email
6. Enter recipient and subject line
7. Copy product information from sheets
8. Find supplier information from supplier names sheet
9. Access Coupa system to get supplier contact details
10. Copy contact information into email
11. Review email content for completeness
12. Send email if complete, otherwise revise
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN with detailed task breakdown for inventory restocking process. The process includes granular steps for accessing systems, copying data, and a single decision gateway for email completion review.",
    "title": "Inventory Restock Process",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_InventoryRestock",
            "name": "Inventory Restock Process"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_1",
            "parentId": "Process_InventoryRestock",
            "name": "Start Inventory Process",
            "data": {
                "label": "Begin inventory restocking workflow"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_OpenInventorySheet",
            "parentId": "Process_InventoryRestock",
            "name": "Open Inventory Status Sheet",
            "data": {
                "label": "Access Google Sheets document containing inventory data"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CheckStockLevels",
            "parentId": "Process_InventoryRestock",
            "name": "Check Stock Levels",
            "data": {
                "label": "Review current stock quantities against reorder thresholds"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_IdentifyRestockNeeds",
            "parentId": "Process_InventoryRestock",
            "name": "Identify Restock Needs",
            "data": {
                "label": "Determine which products require restocking"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CalculateQuantities",
            "parentId": "Process_InventoryRestock",
            "name": "Calculate Quantity Differences",
            "data": {
                "label": "Calculate required order quantities"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_OpenEmailClient",
            "parentId": "Process_InventoryRestock",
            "name": "Open Email Client",
            "data": {
                "label": "Launch Outlook email application"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ComposeNewEmail",
            "parentId": "Process_InventoryRestock",
            "name": "Compose New Email",
            "data": {
                "label": "Click new mail button to start email composition"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_EnterRecipientSubject",
            "parentId": "Process_InventoryRestock",
            "name": "Enter Recipient and Subject",
            "data": {
                "label": "Add recipient email and subject line"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CopyProductInfo",
            "parentId": "Process_InventoryRestock",
            "name": "Copy Product Information",
            "data": {
                "label": "Copy product names and quantities from inventory sheet"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_PasteProductInfo",
            "parentId": "Process_InventoryRestock",
            "name": "Paste Product Information",
            "data": {
                "label": "Paste product details into email body"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_FindSupplierInfo",
            "parentId": "Process_InventoryRestock",
            "name": "Find Supplier Information",
            "data": {
                "label": "Navigate to supplier names sheet and locate supplier"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CopySupplierName",
            "parentId": "Process_InventoryRestock",
            "name": "Copy Supplier Name",
            "data": {
                "label": "Copy supplier name from Google Sheets"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_PasteSupplierName",
            "parentId": "Process_InventoryRestock",
            "name": "Paste Supplier Name",
            "data": {
                "label": "Paste supplier name into email"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_AccessCoupaSystem",
            "parentId": "Process_InventoryRestock",
            "name": "Access Coupa System",
            "data": {
                "label": "Log into Coupa procurement system"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_SearchSupplier",
            "parentId": "Process_InventoryRestock",
            "name": "Search for Supplier",
            "data": {
                "label": "Search for supplier in Coupa database"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ViewContactDetails",
            "parentId": "Process_InventoryRestock",
            "name": "View Supplier Contact Details",
            "data": {
                "label": "Access detailed supplier contact information"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CopyContactInfo",
            "parentId": "Process_InventoryRestock",
            "name": "Copy Contact Information",
            "data": {
                "label": "Copy supplier email and phone number"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_PasteContactInfo",
            "parentId": "Process_InventoryRestock",
            "name": "Paste Contact Information",
            "data": {
                "label": "Paste contact details into email"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewEmailContent",
            "parentId": "Process_InventoryRestock",
            "name": "Review Email Content",
            "data": {
                "label": "Check email for completeness and accuracy"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_EmailComplete",
            "parentId": "Process_InventoryRestock",
            "name": "Email Complete?",
            "data": {
                "label": "Is the email complete and ready to send?"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_SendEmail",
            "parentId": "Process_InventoryRestock",
            "name": "Send Email",
            "data": {
                "label": "Send email to supplier"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_1",
            "parentId": "Process_InventoryRestock",
            "name": "Restock Request Complete",
            "data": {
                "label": "Inventory restock request process completed"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_1",
            "source": "StartEvent_1",
            "target": "Task_OpenInventorySheet",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_2",
            "source": "Task_OpenInventorySheet",
            "target": "Task_CheckStockLevels",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_3",
            "source": "Task_CheckStockLevels",
            "target": "Task_IdentifyRestockNeeds",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_4",
            "source": "Task_IdentifyRestockNeeds",
            "target": "Task_CalculateQuantities",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_5",
            "source": "Task_CalculateQuantities",
            "target": "Task_OpenEmailClient",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_6",
            "source": "Task_OpenEmailClient",
            "target": "Task_ComposeNewEmail",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_7",
            "source": "Task_ComposeNewEmail",
            "target": "Task_EnterRecipientSubject",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_8",
            "source": "Task_EnterRecipientSubject",
            "target": "Task_CopyProductInfo",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_9",
            "source": "Task_CopyProductInfo",
            "target": "Task_PasteProductInfo",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_10",
            "source": "Task_PasteProductInfo",
            "target": "Task_FindSupplierInfo",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_11",
            "source": "Task_FindSupplierInfo",
            "target": "Task_CopySupplierName",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_12",
            "source": "Task_CopySupplierName",
            "target": "Task_PasteSupplierName",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_13",
            "source": "Task_PasteSupplierName",
            "target": "Task_AccessCoupaSystem",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_14",
            "source": "Task_AccessCoupaSystem",
            "target": "Task_SearchSupplier",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_15",
            "source": "Task_SearchSupplier",
            "target": "Task_ViewContactDetails",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_16",
            "source": "Task_ViewContactDetails",
            "target": "Task_CopyContactInfo",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_17",
            "source": "Task_CopyContactInfo",
            "target": "Task_PasteContactInfo",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_18",
            "source": "Task_PasteContactInfo",
            "target": "Task_ReviewEmailContent",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_19",
            "source": "Task_ReviewEmailContent",
            "target": "Gateway_EmailComplete",
            "parentId": "Process_InventoryRestock"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_20",
            "source": "Gateway_EmailComplete",
            "target": "Task_SendEmail",
            "parentId": "Process_InventoryRestock",
            "name": "Yes, email is complete",
            "data": {
                "label": "Email is complete and ready to send"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_21",
            "source": "Gateway_EmailComplete",
            "target": "Task_EnterRecipientSubject",
            "parentId": "Process_InventoryRestock",
            "name": "No, needs revision",
            "data": {
                "label": "Email needs further editing"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_22",
            "source": "Task_SendEmail",
            "target": "EndEvent_1",
            "parentId": "Process_InventoryRestock"
        }
    ]
}
```

## Example 2: Employee Document Review Process

**Textual Description:**
"Create BPMN from employee document review process"

**Extracted Content:**
```
Employee Document Review Process
1. Download document attachments from employee email
2. Open document management system
3. Create new employee record in system
4. Upload identity documents (passport, driver's license, ID card)
5. Upload tax forms (W-4, state tax forms)
6. Upload emergency contact forms
7. Upload bank details for direct deposit
8. Scan all documents for completeness
9. Review identity documents for authenticity
10. Review tax forms for accuracy
11. Review emergency contact information
12. Review bank details for correctness
13. Generate compliance checklist
14. Mark checklist items as complete or incomplete
15. Cross-reference with employment policy requirements
16. Review checklist for any missing items
17. Send approval notification if complete, otherwise request missing documents
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN with detailed document processing workflow. The process includes granular steps for document handling, system interactions, and a single decision gateway for compliance review.",
    "title": "Employee Document Review Process",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_DocumentReview",
            "name": "Employee Document Review Process"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_1",
            "parentId": "Process_DocumentReview",
            "name": "Documents Received",
            "data": {
                "label": "Employee documents received via email"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_DownloadAttachments",
            "parentId": "Process_DocumentReview",
            "name": "Download Attachments",
            "data": {
                "label": "Download all document attachments from email"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_OpenDocumentSystem",
            "parentId": "Process_DocumentReview",
            "name": "Open Document Management System",
            "data": {
                "label": "Access HR document management platform"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_CreateEmployeeRecord",
            "parentId": "Process_DocumentReview",
            "name": "Create New Employee Record",
            "data": {
                "label": "Establish new employee profile in system"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_UploadIdentityDocs",
            "parentId": "Process_DocumentReview",
            "name": "Upload Identity Documents",
            "data": {
                "label": "Upload passport, driver's license, or ID card"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_UploadTaxForms",
            "parentId": "Process_DocumentReview",
            "name": "Upload Tax Forms",
            "data": {
                "label": "Upload W-4 and state tax forms"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_UploadEmergencyContacts",
            "parentId": "Process_DocumentReview",
            "name": "Upload Emergency Contact Forms",
            "data": {
                "label": "Upload emergency contact information forms"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_UploadBankDetails",
            "parentId": "Process_DocumentReview",
            "name": "Upload Bank Details",
            "data": {
                "label": "Upload direct deposit authorization forms"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ScanCompleteness",
            "parentId": "Process_DocumentReview",
            "name": "Scan Documents for Completeness",
            "data": {
                "label": "Check that all required document types are present"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewIdentityDocs",
            "parentId": "Process_DocumentReview",
            "name": "Review Identity Documents",
            "data": {
                "label": "Verify identity document authenticity and validity"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewTaxForms",
            "parentId": "Process_DocumentReview",
            "name": "Review Tax Forms",
            "data": {
                "label": "Check tax form completion and accuracy"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewEmergencyContacts",
            "parentId": "Process_DocumentReview",
            "name": "Review Emergency Contacts",
            "data": {
                "label": "Validate emergency contact information"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewBankDetails",
            "parentId": "Process_DocumentReview",
            "name": "Review Bank Details",
            "data": {
                "label": "Confirm bank account information accuracy"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_GenerateChecklist",
            "parentId": "Process_DocumentReview",
            "name": "Generate Compliance Checklist",
            "data": {
                "label": "Create compliance verification checklist"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewChecklist",
            "parentId": "Process_DocumentReview",
            "name": "Review Checklist",
            "data": {
                "label": "Review compliance checklist for any issues"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_DocumentsComplete",
            "parentId": "Process_DocumentReview",
            "name": "All Documents Complete?",
            "data": {
                "label": "Are all required documents present and valid?"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_SendApproval",
            "parentId": "Process_DocumentReview",
            "name": "Send Approval Notification",
            "data": {
                "label": "Send document approval notification to employee"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_RequestMissingDocs",
            "parentId": "Process_DocumentReview",
            "name": "Request Missing Documents",
            "data": {
                "label": "Send request for missing or invalid documents"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Approved",
            "parentId": "Process_DocumentReview",
            "name": "Documents Approved",
            "data": {
                "label": "All documents approved and process complete"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Pending",
            "parentId": "Process_DocumentReview",
            "name": "Pending Additional Documents",
            "data": {
                "label": "Waiting for additional documents from employee"
            }
        }
    ]
}
```

# Image Processing Examples

## Example 3: Simple Linear Workflow from Flowchart Image

**Image Description:**
A simple flowchart showing: Start → Task A → Task B → Decision → End

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN from a simple linear flowchart image with a single decision point.",
    "title": "Simple Linear Workflow",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_SimpleLinear",
            "name": "Simple Linear Workflow"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_1",
            "parentId": "Process_SimpleLinear",
            "name": "Start",
            "data": {
                "label": "Process initiation"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_A",
            "parentId": "Process_SimpleLinear",
            "name": "Task A",
            "data": {
                "label": "First task in sequence"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_B",
            "parentId": "Process_SimpleLinear",
            "name": "Task B",
            "data": {
                "label": "Second task in sequence"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_Decision",
            "parentId": "Process_SimpleLinear",
            "name": "Decision Point",
            "data": {
                "label": "Decision gateway from flowchart"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_1",
            "parentId": "Process_SimpleLinear",
            "name": "End",
            "data": {
                "label": "Process completion"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_1",
            "source": "StartEvent_1",
            "target": "Task_A",
            "parentId": "Process_SimpleLinear"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_2",
            "source": "Task_A",
            "target": "Task_B",
            "parentId": "Process_SimpleLinear"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_3",
            "source": "Task_B",
            "target": "Gateway_Decision",
            "parentId": "Process_SimpleLinear"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_4",
            "source": "Gateway_Decision",
            "target": "EndEvent_1",
            "parentId": "Process_SimpleLinear"
        }
    ]
}
```

## Example 4: Parallel Process from Flowchart Image

**Image Description:**
A flowchart showing: Start → Fork → (Parallel Task 1, Parallel Task 2) → Join → End

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN from flowchart image showing parallel processing with fork and join gateways.",
    "title": "Parallel Process Workflow",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_Parallel",
            "name": "Parallel Process Workflow"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_1",
            "parentId": "Process_Parallel",
            "name": "Start",
            "data": {
                "label": "Process initiation"
            }
        },
        {
            "type": "bpmn:ParallelGateway",
            "id": "Gateway_Fork",
            "parentId": "Process_Parallel",
            "name": "Fork",
            "data": {
                "label": "Split into parallel paths"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_Parallel1",
            "parentId": "Process_Parallel",
            "name": "Parallel Task 1",
            "data": {
                "label": "First parallel task"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_Parallel2",
            "parentId": "Process_Parallel",
            "name": "Parallel Task 2",
            "data": {
                "label": "Second parallel task"
            }
        },
        {
            "type": "bpmn:ParallelGateway",
            "id": "Gateway_Join",
            "parentId": "Process_Parallel",
            "name": "Join",
            "data": {
                "label": "Merge parallel paths"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_1",
            "parentId": "Process_Parallel",
            "name": "End",
            "data": {
                "label": "Process completion"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_1",
            "source": "StartEvent_1",
            "target": "Gateway_Fork",
            "parentId": "Process_Parallel"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_2",
            "source": "Gateway_Fork",
            "target": "Task_Parallel1",
            "parentId": "Process_Parallel"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_3",
            "source": "Gateway_Fork",
            "target": "Task_Parallel2",
            "parentId": "Process_Parallel"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_4",
            "source": "Task_Parallel1",
            "target": "Gateway_Join",
            "parentId": "Process_Parallel"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_5",
            "source": "Task_Parallel2",
            "target": "Gateway_Join",
            "parentId": "Process_Parallel"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_6",
            "source": "Gateway_Join",
            "target": "EndEvent_1",
            "parentId": "Process_Parallel"
        }
    ]
}
```

## Example 5: Complex Decision Tree from Flowchart Image

**Image Description:**
A flowchart with multiple decision points and conditional branches showing approval workflow

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN from complex flowchart image with multiple decision points and conditional branches for approval workflow.",
    "title": "Approval Decision Workflow",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_ApprovalDecision",
            "name": "Approval Decision Workflow"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_1",
            "parentId": "Process_ApprovalDecision",
            "name": "Request Submitted",
            "data": {
                "label": "Start of approval process"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ReviewRequest",
            "parentId": "Process_ApprovalDecision",
            "name": "Review Request",
            "data": {
                "label": "Initial review of submitted request"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_AmountCheck",
            "parentId": "Process_ApprovalDecision",
            "name": "Amount > $1000?",
            "data": {
                "label": "Check if amount exceeds threshold"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ManagerApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "Manager Approval",
            "data": {
                "label": "Manager review for high-value requests"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_AutoApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "Auto Approval",
            "data": {
                "label": "Automatic approval for low-value requests"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_ManagerDecision",
            "parentId": "Process_ApprovalDecision",
            "name": "Manager Approved?",
            "data": {
                "label": "Manager approval decision"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_ProcessApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "Process Approval",
            "data": {
                "label": "Process the approved request"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_SendRejection",
            "parentId": "Process_ApprovalDecision",
            "name": "Send Rejection",
            "data": {
                "label": "Send rejection notification"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Approved",
            "parentId": "Process_ApprovalDecision",
            "name": "Request Approved",
            "data": {
                "label": "Successful approval completion"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Rejected",
            "parentId": "Process_ApprovalDecision",
            "name": "Request Rejected",
            "data": {
                "label": "Request rejection completion"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_1",
            "source": "StartEvent_1",
            "target": "Task_ReviewRequest",
            "parentId": "Process_ApprovalDecision"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_2",
            "source": "Task_ReviewRequest",
            "target": "Gateway_AmountCheck",
            "parentId": "Process_ApprovalDecision"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_3",
            "source": "Gateway_AmountCheck",
            "target": "Task_ManagerApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "Yes",
            "data": {
                "label": "Amount exceeds $1000"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_4",
            "source": "Gateway_AmountCheck",
            "target": "Task_AutoApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "No",
            "data": {
                "label": "Amount is under $1000"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_5",
            "source": "Task_ManagerApproval",
            "target": "Gateway_ManagerDecision",
            "parentId": "Process_ApprovalDecision"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_6",
            "source": "Gateway_ManagerDecision",
            "target": "Task_ProcessApproval",
            "parentId": "Process_ApprovalDecision",
            "name": "Approved",
            "data": {
                "label": "Manager approved the request"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_7",
            "source": "Gateway_ManagerDecision",
            "target": "Task_SendRejection",
            "parentId": "Process_ApprovalDecision",
            "name": "Rejected",
            "data": {
                "label": "Manager rejected the request"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_8",
            "source": "Task_AutoApproval",
            "target": "Task_ProcessApproval",
            "parentId": "Process_ApprovalDecision"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_9",
            "source": "Task_ProcessApproval",
            "target": "EndEvent_Approved",
            "parentId": "Process_ApprovalDecision"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_10",
            "source": "Task_SendRejection",
            "target": "EndEvent_Rejected",
            "parentId": "Process_ApprovalDecision"
        }
    ]
}
```

## Example 6: Customer Onboarding Process from Visio Diagram

**Textual Description:**
"Create BPMN from customer onboarding process Visio diagram"

**Extracted Content:**
```
# VISIO DIAGRAM CONTENT (VSDX)

This content was extracted from a VSDX Visio diagram file. The extracted information includes shape names, text annotations, connectors, and other diagram elements that describe the workflow or process.

=== PAGE: visio/pages/page1.xml ===
Shape 'StartEvent_1': New Customer Registration
Shape 'StartEvent_1' User.Description=Customer initiates onboarding process
Shape 'Task_KYCVerification': Verify Customer Identity
Shape 'Task_KYCVerification': Perform KYC checks and document validation
Shape 'Gateway_RiskAssessment': Risk Level Assessment
Shape 'Gateway_RiskAssessment' User.Process_Type=ExclusiveGateway
Shape 'Task_EnhancedDueDiligence': Enhanced Due Diligence
Shape 'Task_EnhancedDueDiligence': Additional verification for high-risk customers
Shape 'Task_StandardVerification': Standard Verification
Shape 'Task_StandardVerification': Basic verification process
Shape 'Gateway_FinalApproval': Approval Decision
Shape 'Task_WelcomePackage': Send Welcome Materials
Shape 'Task_RejectionNotice': Send Rejection Notice
Shape 'EndEvent_Approved': Customer Successfully Onboarded
Shape 'EndEvent_Rejected': Application Rejected

=== MASTER: visio/masters/master1.xml ===
Master Shape Template: Process Task
Template Text: Standard Business Process Task

=== MASTER: visio/masters/master2.xml ===
Master Shape Template: Decision Gateway
Template Text: Decision Point with Multiple Outcomes
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Generated BPMN from Visio customer onboarding process with risk assessment and approval workflow including parallel verification paths and decision gateways.",
    "title": "Customer Onboarding Process",
    "add": [
        {
            "type": "bpmn:Process",
            "id": "Process_CustomerOnboarding",
            "name": "Customer Onboarding Process"
        },
        {
            "type": "bpmn:StartEvent",
            "id": "StartEvent_Registration",
            "parentId": "Process_CustomerOnboarding",
            "name": "New Customer Registration",
            "data": {
                "label": "Customer initiates registration process"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_KYCVerification",
            "parentId": "Process_CustomerOnboarding",
            "name": "KYC Verification",
            "data": {
                "label": "Know Your Customer identity verification"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_RiskAssessment",
            "parentId": "Process_CustomerOnboarding",
            "name": "Risk Assessment",
            "data": {
                "label": "Evaluate customer risk profile"
            }
        },
        {
            "type": "bpmn:ServiceTask",
            "id": "Task_EnhancedDueDiligence",
            "parentId": "Process_CustomerOnboarding",
            "name": "Enhanced Due Diligence",
            "data": {
                "label": "Additional verification for high-risk customers"
            }
        },
        {
            "type": "bpmn:ServiceTask",
            "id": "Task_StandardVerification",
            "parentId": "Process_CustomerOnboarding",
            "name": "Standard Verification",
            "data": {
                "label": "Standard verification process for low-risk customers"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_MergeVerification",
            "parentId": "Process_CustomerOnboarding",
            "name": "Merge Verification",
            "data": {
                "label": "Merge parallel verification paths"
            }
        },
        {
            "type": "bpmn:ExclusiveGateway",
            "id": "Gateway_FinalApproval",
            "parentId": "Process_CustomerOnboarding",
            "name": "Final Approval",
            "data": {
                "label": "Final approval decision based on verification results"
            }
        },
        {
            "type": "bpmn:UserTask",
            "id": "Task_WelcomePackage",
            "parentId": "Process_CustomerOnboarding",
            "name": "Send Welcome Package",
            "data": {
                "label": "Send welcome materials and account information"
            }
        },
        {
            "type": "bpmn:SendTask",
            "id": "Task_RejectionNotice",
            "parentId": "Process_CustomerOnboarding",
            "name": "Send Rejection Notice",
            "data": {
                "label": "Send rejection notification with reason"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Onboarded",
            "parentId": "Process_CustomerOnboarding",
            "name": "Customer Onboarded",
            "data": {
                "label": "Successful customer onboarding completion"
            }
        },
        {
            "type": "bpmn:EndEvent",
            "id": "EndEvent_Rejected",
            "parentId": "Process_CustomerOnboarding",
            "name": "Application Rejected",
            "data": {
                "label": "Customer application rejected"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_StartToKYC",
            "source": "StartEvent_Registration",
            "target": "Task_KYCVerification",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_KYCToRisk",
            "source": "Task_KYCVerification",
            "target": "Gateway_RiskAssessment",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_HighRisk",
            "source": "Gateway_RiskAssessment",
            "target": "Task_EnhancedDueDiligence",
            "parentId": "Process_CustomerOnboarding",
            "name": "High Risk",
            "data": {
                "label": "Customer assessed as high risk"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_LowRisk",
            "source": "Gateway_RiskAssessment",
            "target": "Task_StandardVerification",
            "parentId": "Process_CustomerOnboarding",
            "name": "Low Risk",
            "data": {
                "label": "Customer assessed as low risk"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_EnhancedToMerge",
            "source": "Task_EnhancedDueDiligence",
            "target": "Gateway_MergeVerification",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_StandardToMerge",
            "source": "Task_StandardVerification",
            "target": "Gateway_MergeVerification",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_MergeToApproval",
            "source": "Gateway_MergeVerification",
            "target": "Gateway_FinalApproval",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_Approved",
            "source": "Gateway_FinalApproval",
            "target": "Task_WelcomePackage",
            "parentId": "Process_CustomerOnboarding",
            "name": "Approved",
            "data": {
                "label": "Customer application approved"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_Rejected",
            "source": "Gateway_FinalApproval",
            "target": "Task_RejectionNotice",
            "parentId": "Process_CustomerOnboarding",
            "name": "Rejected",
            "data": {
                "label": "Customer application rejected"
            }
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_WelcomeToEnd",
            "source": "Task_WelcomePackage",
            "target": "EndEvent_Onboarded",
            "parentId": "Process_CustomerOnboarding"
        },
        {
            "type": "bpmn:SequenceFlow",
            "id": "Flow_RejectionToEnd",
            "source": "Task_RejectionNotice",
            "target": "EndEvent_Rejected",
            "parentId": "Process_CustomerOnboarding"
        }
    ]
}
``` 