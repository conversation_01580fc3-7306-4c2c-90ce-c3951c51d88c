## **Examples of Correct Responses:**
**BPMN XML example shared by all the examples:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="40741f9c">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
      <uipath:bindings version="v1">
        <uipath:binding id="bi4AhtGRd" name="releaseKey" type="string" default="E175C1D7-94EE-41A5-9322-5D55D56416B6" resource="process" resourceKey="E175C1D7-94EE-41A5-9322-5D55D56416B6" propertyAttribute="Key"/>
      </uipath:bindings>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="00899dd7-2daf-4a4f-b13a-26847babe320"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>edge___Event_start-Activity_f9kTJJ</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sendTask id="send_email" name="send email">
      <bpmn:incoming>edge___Event_start-Activity_f9kTJJ</bpmn:incoming>
      <bpmn:outgoing>edge___Activity_f9kTJJ-Activity_OiscgS</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:receiveTask id="receive_lead" name="find lead in salesforce">
      <bpmn:incoming>edge___Activity_f9kTJJ-Activity_OiscgS</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_OiscgS-Activity_r6QEdk</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:userTask id="user_finance" name="finance">
      <bpmn:incoming>edge_Activity_OiscgS-Activity_r6QEdk</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_LpGyL5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="service_ticket" name="ticket">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_LpGyL5</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_LpGyL6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:task id="task_report" name="Submit report">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_LpGyL6</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_report_email</bpmn:outgoing>
    </bpmn:task>
    <bpmn:serviceTask id="report_email" name="Send report email">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_report_email</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="edge___Event_start-Activity_f9kTJJ" sourceRef="Event_start" targetRef="send_email"/>
    <bpmn:sequenceFlow id="edge___Activity_f9kTJJ-Activity_OiscgS" sourceRef="send_email" targetRef="receive_lead"/>
    <bpmn:sequenceFlow id="edge_Activity_OiscgS-Activity_r6QEdk" sourceRef="receive_lead" targetRef="user_finance"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_LpGyL5" sourceRef="user_finance" targetRef="service_ticket"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_LpGyL6" sourceRef="service_ticket" targetRef="task_report"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_report_email" sourceRef="task_report" targetRef="report_email"/>
  </bpmn:process>  
</bpmn:definitions>
```

### **Example 1:**
**User Query:** Change task ticket to a process

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"},{"id":7,"name":"Finance","description":"Finance"},{"id":8,"name":"Buy ticket","description":"Buy movie ticket"},{"id":9,"name":"Flight ticket","description":"Book flight ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output:**
```json
{"title":"Add Finance Process","explanation":"I'll update the 'ticket' service task by adding the Finance ticket process. This process will handle ticket-related financial operations and integrate with your existing workflow","update":[{"id":"service_ticket","type":"bpmn:serviceTask","data":{"extensions":[{"id":1,"score":0.9},{"id":8,"score":0.85},{"id":9,"score":0.85}]}}]}
```

### **Example 2:**
**User Query:** Add an agent to task ticket

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"},{"id":7,"name":"Create Incident"},{"id":8,"name":"Incident"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output (JSON):**
```json
{"title":"Add Agent to Ticket","explanation":"I'll add an agent to the 'ticket' service task. Since this is a service task, we can apply an agent from the agents list. I'm selecting the 'Create.Incident.Task' agent which appears most relevant to ticket handling.","update":[{"id":"service_ticket","type":"bpmn:serviceTask","data":{"extensions":[{"id":2,"score":0.9},{"id":3,"score":0.8},{"id":7,"score":0.7}]}}]}
```

### **Example 3:**
**User Query:** Add human in the loop for task finance

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"},{"id":7,"name":"Finance ticket"},{"id":8,"name":"Finance help"}]}
```

**Expected Output:**
```json
{"title":"Add HITL to task finance","explanation":"I'll add a human-in-the-loop to the 'finance' user task. This will incorporate human approval into the workflow, ensuring proper review of finance-related items before proceeding to the ticket creation step.","update":[{"id":"user_finance","type":"bpmn:userTask","data":{"extensions":[{"id":5,"score":0.9},{"id":7,"score":0.9},{"id":8,"score":0.8}]}}]}
```


### **Example 4:**
**User Query:** Add human in the loop for task submit application

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output (JSON):**
```json
{
  "explanation": "None of tasks match the 'submit application' task you're looking for. Please verify the task name and try again."
}
```


### **Example 5:**
**User Query:** Add human in the loop for task ticket

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output:**
```json
{"title":"Add HITL to task ticket","explanation":"I'll add a human-in-the-loop to the 'ticket' user task. This will incorporate human approval into the workflow, ensuring proper review of ticket-related items before proceeding to the ticket creation step.","update":[{"id":"service_ticket","type":"bpmn:userTask","data":{"extensions":[{"id":5,"score":0.9}]}}]}
```


### **Example 6:**
**User Query:** Add human in the loop for task finance

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}]}
```

**Expected Output:**
```json
{
    "explanation": "There is no human in loop for task 'finance'. To add a human in the loop for the 'finance' task, Please add action app in action center."
}
```

### **Example 7:**
**User Query:** Add activity for task ticket

**Extensions (JSON):**
```json
{"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output:**
```json
{
    "explanation": "There is no orchestrator activity for task 'ticket'. To add an activity for the 'ticket' task, Please create one in Orchestrator."
}
```

### **Example 8:**
**User Query:** Add agent for task ticket

**Extensions (JSON):**
```json
{"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Approve fin ticket"},{"id":6,"name":"AOInvoicePODiscrepancies"}]}
```

**Expected Output:**
```json
{
    "explanation": "There is no agent for task 'ticket'. To add an agent for the 'ticket' task, Please create one in agent builder."
}
```


### **Example 9:**
**User Query:** Add human in the loop for task Submit report

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[{"id":7,"name":"Microsoft Outlook 365","description":"A cloud-based version of Microsoft's email, calendar, and contact management program. Automate repetitive or time-intensive tasks like email sorting/funneling, report generation, and event creation. (.NET 5.0)"},{"id":8,"name":"HTTP Webhook","description":"Use this connector to capture events (webhooks) sent by any application to trigger your automation"},{"id":9,"name":"Salesforce","description":"A cloud-based CRM and sales platform used to manage opportunities, relationships, progress, and more. Automate parts of the sales cycle, from lead management to report generation."},{"id":10,"name":"Gmail","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."}]}
```

**Expected Output:**
```json
{"title":"Add HITL to Report Task","explanation":"I'll add a human-in-the-loop to the 'Submit report' task. Since this is currently a generic task, I'll convert it to a user task and link it with the 'Submit expense report' HITL, which closely matches the task's purpose.","update":[{"id":"task_report","type":"bpmn:userTask","data":{"extensions":[{"id":5,"score":0.9}]}}]}
```


### **Example 10:**
**User Query:** Add outlook connector event for task Submit report

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[{"id":7,"name":"Microsoft Outlook 365","description":"A cloud-based version of Microsoft's email, calendar, and contact management program. Automate repetitive or time-intensive tasks like email sorting/funneling, report generation, and event creation. (.NET 5.0)"},{"id":8,"name":"HTTP Webhook","description":"Use this connector to capture events (webhooks) sent by any application to trigger your automation"},{"id":9,"name":"Salesforce","description":"A cloud-based CRM and sales platform used to manage opportunities, relationships, progress, and more. Automate parts of the sales cycle, from lead management to report generation."},{"id":10,"name":"Gmail","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."}]}
```

**Expected Output:**
```json
{"title":"Add Outlook Connector","explanation":"I'll add the Microsoft Outlook 365 connector to the 'Revise Request' task. Since this is currently a regular task, I'll convert it to a send task to enable email communication functionality, allowing users to send revision details via Outlook.","update":[{"id":"task_report","type":"bpmn:receiveTask","name":"Submit report","purpose":"Send revision details via Outlook","data":{"extensions":[{"id":7,"score":0.9}]}}]}
```


### **Example 11:**
**User Query:** Add email connector event for task Submit report

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[{"id":7,"name":"Microsoft Outlook 365","description":"A cloud-based version of Microsoft's email, calendar, and contact management program. Automate repetitive or time-intensive tasks like email sorting/funneling, report generation, and event creation. (.NET 5.0)"},{"id":8,"name":"HTTP Webhook","description":"Use this connector to capture events (webhooks) sent by any application to trigger your automation"},{"id":9,"name":"Salesforce","description":"A cloud-based CRM and sales platform used to manage opportunities, relationships, progress, and more. Automate parts of the sales cycle, from lead management to report generation."},{"id":10,"name":"Gmail","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."},{"id":11,"name":"Yahoo email","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."}]}
```

**Expected Output:**
```json
{"title":"Add Outlook Connector","explanation":"I'll add the Microsoft Outlook 365 connector to the 'Revise Request' task. Since this is currently a regular task, I'll convert it to a send task to enable email communication functionality, allowing users to send revision details via Outlook.","update":[{"id":"task_report","type":"bpmn:receiveTask","name":"Submit report","purpose":"Send revision details via Outlook","data":{"extensions":[{"id":7,"score":0.9},{"id":10,"score":0.85},{"id":11,"score":0.8}]}}]}
```


### **Example 12:**
**User Query:** Add email connector event for task Submit report

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[]}
```

**Expected Output:**
```json
{
    "explanation": "There is no connection for 'email connector'."
}
```

### **Example 13:**
**User Query:** Add outlook connection event for task finance

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"Finance ticket","description":"Finance ticket"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[{"id":7,"name":"Microsoft Outlook 365","description":"A cloud-based version of Microsoft's email, calendar, and contact management program. Automate repetitive or time-intensive tasks like email sorting/funneling, report generation, and event creation. (.NET 5.0)"},{"id":8,"name":"HTTP Webhook","description":"Use this connector to capture events (webhooks) sent by any application to trigger your automation"},{"id":9,"name":"Salesforce","description":"A cloud-based CRM and sales platform used to manage opportunities, relationships, progress, and more. Automate parts of the sales cycle, from lead management to report generation."},{"id":10,"name":"Gmail","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."},{"id":11,"name":"Yahoo email","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."}]}
```

**Expected Output:**
```json
{"title":"Add Outlook Connection to finance","explanation":"I'll add the Microsoft Outlook 365 connector to the 'finance' task. Since this is currently a user task, I'll convert it to a send task to enable email communication functionality, allowing users to send finance details via Outlook.","update":[{"id":"user_finance","type":"bpmn:sendTask","name":"finance","purpose":"Send finance details via Outlook","data":{"extensions":[{"id":7,"score":0.9}]}}]}
```

### **Example 14:**
**User Query:** Add rpa automation to Send report email

**Extensions (JSON):**
```json
{"processes":[{"id":1,"name":"SendEmail","description":"Report process"}],"agents":[{"id":2,"name":"Invoice-checking-agent"},{"id":3,"name":"Create.Incident.Task"}],"human-in-the-loop":[{"id":4,"name":"Invoice Processing - Finance Approval"},{"id":5,"name":"Submit expense report"},{"id":6,"name":"AOInvoicePODiscrepancies"}],"connectors":[{"id":7,"name":"Microsoft Outlook 365","description":"A cloud-based version of Microsoft's email, calendar, and contact management program. Automate repetitive or time-intensive tasks like email sorting/funneling, report generation, and event creation. (.NET 5.0)"},{"id":8,"name":"HTTP Webhook","description":"Use this connector to capture events (webhooks) sent by any application to trigger your automation"},{"id":9,"name":"Salesforce","description":"A cloud-based CRM and sales platform used to manage opportunities, relationships, progress, and more. Automate parts of the sales cycle, from lead management to report generation."},{"id":10,"name":"Gmail","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."},{"id":11,"name":"Yahoo email","description":"A web-based email service. After connecting, automate your mail--prioritize important new emails, send regular responses to similar messages, or manage file attachments."}]}
```

**Expected Output:**
```json
{"title":"Add RPA Workflow for Send report email", "explanation":"I'll add an RPA workflow to the 'Send report email' service task. Based on your available processes, I'm recommending the 'SendEmail' process which is designed specifically for email automation. This will enable your workflow to automatically send report via email.","update":[{"id":"report_email","type":"bpmn:serviceTask","data":{"extensions":[{"id":1,"score":0.9}]}}]}
```