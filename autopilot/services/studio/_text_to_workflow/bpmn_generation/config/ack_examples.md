
# Process examples
Here are some examples for Ack:
##Textual description:
"Please add a new task A and add an agent B to the task, also generate a variable called C which extract the field A1 in <PERSON>'s result variable"
##Expected result in JSON format:
```
{
    "explanation": "I understand you want to add a new task A and add an agent B to it, also generate a variable called C which extract the field A1 in task A's result variable. Sure, let me help you on it."
}
```

##Textual description:
"create a send task AA between gateway A and task B and explain its purpose"
##Expected result in JSON format:
```
{
    "explanation": "OK, I understand you'd like to create a send task AA between gateway A and task B in the current process, and get the explanation on the purpose of AA."
}
```

