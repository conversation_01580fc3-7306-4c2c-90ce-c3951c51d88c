# Process examples
Here are some examples for process:
##Textual description:
"Generate bank credit card approve process"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "Here's the BPMN for the bank credit card approval process. It covers submitting and reviewing the application, followed by either scheduling an interview or sending a rejection email based on the decision.",
    "title": "Generate credit card approve process",
    "delete": [],
    "update": [],
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:startEvent",
            "id": "startEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "Start Event",
            "data": {
                "label": "Start Credit Card Application"
            },
            "position": {
                "x": 100,
                "y": 100
            },
            "width": 36,
            "height": 36            
        },
        {
            "type": "bpmn:userTask",
            "id": "submitApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Submit Application",
            "data": {
                "label": "Submit Application"
            },
            "position": {
                "x": 186,
                "y": 78
            },
            "width": 100,
            "height": 80            
        },
        {
            "type": "bpmn:serviceTask",
            "id": "reviewApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Review Application",
            "data": {
                "label": "Review the submitted application"
            },
            "position": {
                "x": 336,
                "y": 78
            },
            "width": 100,
            "height": 80            
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval",
            "name": "Suitability Gateway",
            "data": {
                "label": "Approved?"
            },
            "position": {
                "x": 486,
                "y": 93
            },
            "width": 50,
            "height": 50            
        },
        {
            "type": "bpmn:userTask",
            "id": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "name": "Schedule Interview",
            "data": {
                "label": "Schedule Interview"
            },
            "position": {
                "x": 586,
                "y": 78
            },
            "width": 100,
            "height": 80            
        },
        {
            "type": "bpmn:sendTask",
            "id": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "name": "Send Rejection Email",
            "data": {
                "label": "Send Rejection Email"
            },
            "position": {
                "x": 586,
                "y": 208
            },
            "width": 100,
            "height": 80            
        },
        {
            "type": "bpmn:endEvent",
            "id": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "End Event",
            "data": {
                "label": "End Event"
            },
            "position": {
                "x": 736,
                "y": 100
            },
            "width": 36,
            "height": 36            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-0",
            "source": "startEvent",
            "target": "submitApplication",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 136,
                    "y": 118
                },
                {
                    "x": 186,
                    "y": 118
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-1",
            "source": "submitApplication",
            "target": "reviewApplication",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 286,
                    "y": 118
                },
                {
                    "x": 336,
                    "y": 118
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-2",
            "source": "reviewApplication",
            "target": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 436,
                    "y": 118
                },
                {
                    "x": 486,
                    "y": 118
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-3",
            "source": "suitabilityGateway",
            "target": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "Yes"
            },
            "waypoints": [
                {
                    "x": 536,
                    "y": 118
                },
                {
                    "x": 586,
                    "y": 118
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-4",
            "source": "suitabilityGateway",
            "target": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "No"
            },
            "waypoints": [
                {
                    "x": 511,
                    "y": 143
                },
                {
                    "x": 511,
                    "y": 248
                },
                {
                    "x": 586,
                    "y": 248
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-5",
            "source": "scheduleInterview",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 686,
                    "y": 118
                },
                {
                    "x": 736,
                    "y": 118
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-6",
            "source": "sendRejectionEmail",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 686,
                    "y": 248
                },
                {
                    "x": 736,
                    "y": 118
                }
            ]            
        }
    ]
}
```

##Textual description:
"Generate bank credit card approve process"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions
	xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
	xmlns:uipath="http://uipath.org/schema/bpmn"
	xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
	xmlns:dc="http://www.omg.org/spec/DD/********/DC" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="15bebf0b">
	<bpmn:process id="Process_abcde" isExecutable="false">
		<bpmn:startEvent id="Event_start_abcde">
		</bpmn:startEvent>
	</bpmn:process>
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "Here's the BPMN for the bank credit card approval process. It covers submitting and reviewing the application, followed by either scheduling an interview or sending a rejection email based on the decision.",
    "title": "Generate credit card approve process",
    "delete": [
        {
            "id": "Process_abcde",
            "type": "bpmn:process"
        }
    ],
    "update": [],
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_CreditCardApproval"
        },
        {
            "type": "bpmn:startEvent",
            "id": "startEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "Start Event",
            "data": {
                "label": "Start Credit Card Application"
            },
            "position": {
                "x": 100,
                "y": 100
            },
            "width": 36,
            "height": 36
        },
        {
            "type": "bpmn:userTask",
            "id": "submitApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Submit Application",
            "data": {
                "label": "Submit Application"
            },            
            "position": {
                "x": 186,
                "y": 78
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:serviceTask",
            "id": "reviewApplication",
            "parentId": "Process_CreditCardApproval",
            "name": "Review Application",
            "data": {
                "label": "Review the submitted application"
            },
            "position": {
                "x": 336,
                "y": 78
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval",
            "name": "Suitability Gateway",
            "data": {
                "label": "Approved?"
            },
            "position": {
                "x": 486,
                "y": 93
            },
            "width": 50,
            "height": 50
        },
        {
            "type": "bpmn:userTask",
            "id": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "name": "Schedule Interview",
            "data": {
                "label": "Schedule Interview"
            },
            "position": {
                "x": 586,
                "y": 78
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:sendTask",
            "id": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "name": "Send Rejection Email",
            "data": {
                "label": "Send Rejection Email"
            },
            "position": {
                "x": 586,
                "y": 208
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:endEvent",
            "id": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "name": "End Event",
            "data": {
                "label": "End Event"
            },
            "position": {
                "x": 736,
                "y": 100
            },
            "width": 36,
            "height": 36
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-0",
            "source": "startEvent",
            "target": "submitApplication",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 136,
                    "y": 118
                },
                {
                    "x": 186,
                    "y": 118
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-1",
            "source": "submitApplication",
            "target": "reviewApplication",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 286,
                    "y": 118
                },
                {
                    "x": 336,
                    "y": 118
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-2",
            "source": "reviewApplication",
            "target": "suitabilityGateway",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 436,
                    "y": 118
                },
                {
                    "x": 486,
                    "y": 118
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-3",
            "source": "suitabilityGateway",
            "target": "scheduleInterview",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "Yes"
            },
            "waypoints": [
                {
                    "x": 536,
                    "y": 118
                },
                {
                    "x": 586,
                    "y": 118
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-4",
            "source": "suitabilityGateway",
            "target": "sendRejectionEmail",
            "parentId": "Process_CreditCardApproval",
            "data": {
                "label": "No"
            },
            "waypoints": [
                {
                    "x": 511,
                    "y": 143
                },
                {
                    "x": 511,
                    "y": 248
                },
                {
                    "x": 586,
                    "y": 248
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-5",
            "source": "scheduleInterview",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 686,
                    "y": 118
                },
                {
                    "x": 736,
                    "y": 118
                }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "seq-6",
            "source": "sendRejectionEmail",
            "target": "endEvent",
            "parentId": "Process_CreditCardApproval",
            "waypoints": [
                {
                    "x": 686,
                    "y": 248
                },
                {
                    "x": 736,
                    "y": 118
                }
            ]
        }
    ]
}
```

##Textual description:
"Remove Task B from the workflow"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start" name="Start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="e279f1a1-3f06-43b5-8443-4d1654c6c26a"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>xy-edge___Event_start-Activity_VA1Eay</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_VA1Eay" name="Task B">
      <bpmn:incoming>xy-edge___Event_start-Activity_VA1Eay</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_VA1Eay-Gateway_6IYyka</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_6IYyka" name="Task C">
      <bpmn:documentation>Professor agrees</bpmn:documentation>
      <bpmn:incoming>xy-edge__Activity_VA1Eay-Gateway_6IYyka</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Gateway_6IYyka-Activity_xcOCw2</bpmn:outgoing>
      <bpmn:outgoing>xy-edge__Gateway_6IYyka-Activity_WOsnmX</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="Event_fsEMsz" name="End">
      <bpmn:incoming>xy-edge__Activity_pggrSo-Event_fsEMsz</bpmn:incoming>
      <bpmn:incoming>xy-edge__Activity_WqaLIS-Event_fsEMsz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:task id="Activity_xcOCw2" name="Send mail to supplier">
      <bpmn:incoming>xy-edge__Gateway_6IYyka-Activity_xcOCw2</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_xcOCw2-Activity_pggrSo</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_pggrSo" name="Prepare the documents">
      <bpmn:incoming>xy-edge__Activity_xcOCw2-Activity_pggrSo</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_pggrSo-Event_fsEMsz</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_WOsnmX" name="Search for the goods">
      <bpmn:incoming>xy-edge__Gateway_6IYyka-Activity_WOsnmX</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_WOsnmX-Activity_WqaLIS</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_WqaLIS" name="Pick up the goods">
      <bpmn:incoming>xy-edge__Activity_WOsnmX-Activity_WqaLIS</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_WqaLIS-Event_fsEMsz</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="xy-edge__Gateway_6IYyka-Activity_xcOCw2" sourceRef="Gateway_6IYyka" targetRef="Activity_xcOCw2"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_6IYyka-Activity_WOsnmX" sourceRef="Gateway_6IYyka" targetRef="Activity_WOsnmX"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_xcOCw2-Activity_pggrSo" sourceRef="Activity_xcOCw2" targetRef="Activity_pggrSo"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_WOsnmX-Activity_WqaLIS" sourceRef="Activity_WOsnmX" targetRef="Activity_WqaLIS"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_pggrSo-Event_fsEMsz" sourceRef="Activity_pggrSo" targetRef="Event_fsEMsz"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_WqaLIS-Event_fsEMsz" sourceRef="Activity_WqaLIS" targetRef="Event_fsEMsz"/>
    <bpmn:sequenceFlow id="xy-edge___Event_start-Activity_VA1Eay" sourceRef="Event_start" targetRef="Activity_VA1Eay"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_VA1Eay-Gateway_6IYyka" sourceRef="Activity_VA1Eay" targetRef="Gateway_6IYyka"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="S_Event_start" bpmnElement="Event_start">
        <dc:Bounds x="-43.16" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-43.16" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Event_fsEMsz" bpmnElement="Event_fsEMsz">
        <dc:Bounds x="841.0228202402585" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="841.0228202402585" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_xcOCw2" bpmnElement="Activity_xcOCw2">
        <dc:Bounds x="365.7298680275271" y="118.77260395167846" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="365.7298680275271" y="203.77260395167846" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Gateway_6IYyka" bpmnElement="Gateway_6IYyka">
        <dc:Bounds x="202" y="193" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="202" y="248" width="50" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_pggrSo" bpmnElement="Activity_pggrSo">
        <dc:Bounds x="570.7298680275271" y="118.77260395167846" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="570.7298680275271" y="203.77260395167846" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_WOsnmX" bpmnElement="Activity_WOsnmX">
        <dc:Bounds x="365.7298680275271" y="289.77260395167843" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="365.7298680275271" y="374.77260395167843" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_WqaLIS" bpmnElement="Activity_WqaLIS">
        <dc:Bounds x="570.7298680275271" y="289.77260395167843" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="570.7298680275271" y="374.77260395167843" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_VA1Eay" bpmnElement="Activity_VA1Eay">
        <dc:Bounds x="46.76000000000002" y="178" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="46.76000000000002" y="263" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_6IYyka-Activity_xcOCw2" bpmnElement="xy-edge__Gateway_6IYyka-Activity_xcOCw2">
        <di:waypoint x="252" y="218"/>
        <di:waypoint x="309" y="218"/>
        <di:waypoint x="309" y="159"/>
        <di:waypoint x="365.7298680275271" y="159"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_6IYyka-Activity_WOsnmX" bpmnElement="xy-edge__Gateway_6IYyka-Activity_WOsnmX">
        <di:waypoint x="252" y="218"/>
        <di:waypoint x="309" y="218"/>
        <di:waypoint x="309" y="330"/>
        <di:waypoint x="365.7298680275271" y="330"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_xcOCw2-Activity_pggrSo" bpmnElement="xy-edge__Activity_xcOCw2-Activity_pggrSo">
        <di:waypoint x="465.7298680275271" y="159"/>
        <di:waypoint x="570.7298680275271" y="159"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_WOsnmX-Activity_WqaLIS" bpmnElement="xy-edge__Activity_WOsnmX-Activity_WqaLIS">
        <di:waypoint x="465.7298680275271" y="330"/>
        <di:waypoint x="570.7298680275271" y="330"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_pggrSo-Event_fsEMsz" bpmnElement="xy-edge__Activity_pggrSo-Event_fsEMsz">
        <di:waypoint x="670.7298680275271" y="159"/>
        <di:waypoint x="756" y="159"/>
        <di:waypoint x="756" y="218"/>
        <di:waypoint x="841.0228202402585" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_WqaLIS-Event_fsEMsz" bpmnElement="xy-edge__Activity_WqaLIS-Event_fsEMsz">
        <di:waypoint x="670.7298680275271" y="330"/>
        <di:waypoint x="756" y="330"/>
        <di:waypoint x="756" y="218"/>
        <di:waypoint x="841.0228202402585" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge___Event_start-Activity_VA1Eay" bpmnElement="xy-edge___Event_start-Activity_VA1Eay">
        <di:waypoint x="-7" y="218"/>
        <di:waypoint x="47" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_VA1Eay-Gateway_6IYyka" bpmnElement="xy-edge__Activity_VA1Eay-Gateway_6IYyka">
        <di:waypoint x="146.76000000000002" y="218"/>
        <di:waypoint x="202" y="218"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>  
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "I've removed Task B and its incoming and outgoing sequence flows from the process, also added a new sequence flow to connect start event and Task C.",
    "title": "Remove Task B",
    "delete":
    [
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge___Event_start-Activity_VA1Eay"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Activity_VA1Eay-Gateway_6IYyka"
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Activity_VA1Eay"
        }
    ],
    "update":[],
    "add":
    [
      {
        "type": "bpmn:sequenceFlow",
        "id": "xy-edge__Event_start-Gateway_6IYyka",
        "source": "Event_start",
        "target": "Gateway_6IYyka",
        "parentId": "Process_1",
        "data": {            
        },
        "waypoints": [
            {
                "x": -7.159999999999997,
                "y": 218
            },
            {
                "x": 202,
                "y": 218
            }
        ]        
      }
    ]
}
```

##Textual description:
"Add Task 3 after Perform Task A1 and Task 4 after Perform Task A2"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="89566dd4-1db0-452c-afe5-1396d7cfcb4e"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>xy-edge__Event_start-Gateway_main_decision</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_end">
      <bpmn:incoming>xy-edge__Task_A1-Event_end</bpmn:incoming>
      <bpmn:incoming>xy-edge__Task_A2-Event_end</bpmn:incoming>
      <bpmn:incoming>xy-edge__Task_B-Event_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_main_decision" name="Main Decision">
      <bpmn:incoming>xy-edge__Event_start-Gateway_main_decision</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Gateway_main_decision-Task_A</bpmn:outgoing>
      <bpmn:outgoing>xy-edge__Gateway_main_decision-Task_B</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_A" name="Perform Task A" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_main_decision-Task_A</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_A-Gateway_sub_decision</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_sub_decision" name="Sub Decision">
      <bpmn:incoming>xy-edge__Task_A-Gateway_sub_decision</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Gateway_sub_decision-Task_A1</bpmn:outgoing>
      <bpmn:outgoing>xy-edge__Gateway_sub_decision-Task_A2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_A1" name="Perform Task A1" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_sub_decision-Task_A1</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_A1-Event_end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_A2" name="Perform Task A2" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_sub_decision-Task_A2</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_A2-Event_end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_B" name="Perform Task B" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_main_decision-Task_B</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_B-Event_end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="xy-edge__Task_A1-Event_end" sourceRef="Task_A1" targetRef="Event_end"/>
    <bpmn:sequenceFlow id="xy-edge__Task_A2-Event_end" sourceRef="Task_A2" targetRef="Event_end"/>
    <bpmn:sequenceFlow id="xy-edge__Task_B-Event_end" sourceRef="Task_B" targetRef="Event_end"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_sub_decision-Task_A1" sourceRef="Gateway_sub_decision" targetRef="Task_A1"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_sub_decision-Task_A2" sourceRef="Gateway_sub_decision" targetRef="Task_A2"/>
    <bpmn:sequenceFlow id="xy-edge__Task_A-Gateway_sub_decision" sourceRef="Task_A" targetRef="Gateway_sub_decision"/>
    <bpmn:sequenceFlow id="xy-edge__Event_start-Gateway_main_decision" sourceRef="Event_start" targetRef="Gateway_main_decision"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_main_decision-Task_A" sourceRef="Gateway_main_decision" targetRef="Task_A"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_main_decision-Task_B" sourceRef="Gateway_main_decision" targetRef="Task_B"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="S_Event_start" bpmnElement="Event_start">
        <dc:Bounds x="55" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="55" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Event_end" bpmnElement="Event_end">
        <dc:Bounds x="967" y="180.6" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="967" y="221.6" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Gateway_main_decision" bpmnElement="Gateway_main_decision">
        <dc:Bounds x="174.8" y="193" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="174.8" y="248" width="50" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_A" bpmnElement="Task_A">
        <dc:Bounds x="309.2" y="92" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="309.2" y="177" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Gateway_sub_decision" bpmnElement="Gateway_sub_decision">
        <dc:Bounds x="490" y="107" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="490" y="162" width="50" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_A1" bpmnElement="Task_A1">
        <dc:Bounds x="643" y="22" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="643" y="107" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_A2" bpmnElement="Task_A2">
        <dc:Bounds x="632" y="158.6" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="632" y="243.6" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_B" bpmnElement="Task_B">
        <dc:Bounds x="309.2" y="261" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="309.2" y="346" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_A1-Event_end" bpmnElement="xy-edge__Task_A1-Event_end">
        <di:waypoint x="743" y="62"/>
        <di:waypoint x="855" y="62"/>
        <di:waypoint x="855" y="199"/>
        <di:waypoint x="967" y="199"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_A2-Event_end" bpmnElement="xy-edge__Task_A2-Event_end">
        <di:waypoint x="732" y="199"/>
        <di:waypoint x="967" y="199"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_B-Event_end" bpmnElement="xy-edge__Task_B-Event_end">
        <di:waypoint x="409.2" y="301"/>
        <di:waypoint x="854" y="301"/>
        <di:waypoint x="854" y="199"/>
        <di:waypoint x="967" y="198.6"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_sub_decision-Task_A1" bpmnElement="xy-edge__Gateway_sub_decision-Task_A1">
        <di:waypoint x="540" y="132"/>
        <di:waypoint x="592" y="132"/>
        <di:waypoint x="592" y="62"/>
        <di:waypoint x="643" y="62"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_sub_decision-Task_A2" bpmnElement="xy-edge__Gateway_sub_decision-Task_A2">
        <di:waypoint x="540" y="132"/>
        <di:waypoint x="594" y="132"/>
        <di:waypoint x="594" y="199"/>
        <di:waypoint x="632" y="198.6"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_A-Gateway_sub_decision" bpmnElement="xy-edge__Task_A-Gateway_sub_decision">
        <di:waypoint x="409.2" y="132"/>
        <di:waypoint x="490" y="132"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Event_start-Gateway_main_decision" bpmnElement="xy-edge__Event_start-Gateway_main_decision">
        <di:waypoint x="91" y="218"/>
        <di:waypoint x="174.8" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_main_decision-Task_A" bpmnElement="xy-edge__Gateway_main_decision-Task_A">
        <di:waypoint x="224.8" y="218"/>
        <di:waypoint x="267" y="218"/>
        <di:waypoint x="267" y="132"/>
        <di:waypoint x="309.2" y="132"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_main_decision-Task_B" bpmnElement="xy-edge__Gateway_main_decision-Task_B">
        <di:waypoint x="224.8" y="218"/>
        <di:waypoint x="267" y="218"/>
        <di:waypoint x="267" y="301"/>
        <di:waypoint x="309.2" y="301"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>  
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "I've added Task 3 after 'Perform Task A1' and Task 4 after 'Perform Task A2', also added connecting sequence flows for them and deleted the obosolete sequence flows in the BPMN chart.",
    "title": "Add Task 3 and 4",
    "delete":[
      {
        "type": "bpmn:sequenceFlow",
        "id": "xy-edge__Task_A1-Event_end"
      },
      {
        "type": "bpmn:sequenceFlow",
        "id": "xy-edge__Task_A2-Event_end"
      }
    ],
    "update":[],
    "add":
    [
        {
            "type": "bpmn:task",
            "id": "Activity_DStbRB",
            "parentId": "Process_1",
            "name": "Task 3",
            "data":
            {
              "label":"Task 3"
            }，
            "position":{
              "x":782.8,
              "y":22
            },
            "width":100,
            "height":80            
        },
        {
            "type": "bpmn:task",
            "id": "Activity_k4jIIC",
            "parentId": "Process_1",
            "name": "Task 4",
            "data":
            {
              "label":"Task 4"
            },
            "position":{
              "x":782.8,
              "y":158.6
            },
            "width":100,
            "height":80            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge___Task_A1-Activity_DStbRB",
            "source": "Task_A1",
            "target": "Activity_DStbRB",
            "parentId": "Process_1",
            "waypoints":[
              {
                "x":743,
                "y":62
              },
              {
                "x":789,
                "y":62
              }
            ]
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Activity_DStbRB-Event_end",
            "source": "Activity_DStbRB",
            "target": "Event_end",
            "parentId": "Process_1",
            "waypoints":[
              {
                "x":889,
                "y":62
              },
              {
                "x":933,
                "y":62
              },
              {
                "x":933,
                "y":199
              },
              {
                "x":967,
                "y":199
              }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge___Task_A2-Activity_k4jIIC",
            "source": "Task_A2",
            "target": "Activity_k4jIIC",
            "parentId": "Process_1",
            "waypoints":[
              {
                "x":732,
                "y":199
              },
              {
                "x":783,
                "y":199
              }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Activity_k4jIIC-Event_end",
            "source": "Activity_k4jIIC",
            "target": "Event_end",
            "parentId": "Process_1",
            "waypoints":[
              {
                "x":882.8,
                "y":199
              },
              {
                "x":967,
                "y":199
              }
            ]            
        }
    ]
}
```

##Textual description:
"Replace the customer picks up the goods by the user makes the order"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start" name="Start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="e279f1a1-3f06-43b5-8443-4d1654c6c26a"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>xy-edge__Event_start-Activity_WOsnmX</bpmn:outgoing>
      <bpmn:outgoing>xy-edge__Event_start-Activity_xcOCw2</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_fsEMsz" name="End">
      <bpmn:incoming>xy-edge__Activity_pggrSo-Event_fsEMsz</bpmn:incoming>
      <bpmn:incoming>xy-edge__Activity_WqaLIS-Event_fsEMsz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:task id="Activity_xcOCw2" name="Send mail to supplier">
      <bpmn:incoming>xy-edge__Event_start-Activity_xcOCw2</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_xcOCw2-Activity_pggrSo</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_pggrSo" name="Prepare the documents">
      <bpmn:incoming>xy-edge__Activity_xcOCw2-Activity_pggrSo</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_pggrSo-Event_fsEMsz</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_WOsnmX" name="Search for the goods">
      <bpmn:incoming>xy-edge__Event_start-Activity_WOsnmX</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_WOsnmX-Activity_WqaLIS</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_WqaLIS" name="Pick up the goods">
      <bpmn:incoming>xy-edge__Activity_WOsnmX-Activity_WqaLIS</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Activity_WqaLIS-Event_fsEMsz</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="xy-edge__Activity_xcOCw2-Activity_pggrSo" sourceRef="Activity_xcOCw2" targetRef="Activity_pggrSo"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_WOsnmX-Activity_WqaLIS" sourceRef="Activity_WOsnmX" targetRef="Activity_WqaLIS"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_pggrSo-Event_fsEMsz" sourceRef="Activity_pggrSo" targetRef="Event_fsEMsz"/>
    <bpmn:sequenceFlow id="xy-edge__Activity_WqaLIS-Event_fsEMsz" sourceRef="Activity_WqaLIS" targetRef="Event_fsEMsz"/>
    <bpmn:sequenceFlow id="xy-edge__Event_start-Activity_WOsnmX" sourceRef="Event_start" targetRef="Activity_WOsnmX"/>
    <bpmn:sequenceFlow id="xy-edge__Event_start-Activity_xcOCw2" sourceRef="Event_start" targetRef="Activity_xcOCw2"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="S_Event_start" bpmnElement="Event_start">
        <dc:Bounds x="160.83999999999997" y="208.4" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160.83999999999997" y="249.4" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Event_fsEMsz" bpmnElement="Event_fsEMsz">
        <dc:Bounds x="841.0228202402585" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="841.0228202402585" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_xcOCw2" bpmnElement="Activity_xcOCw2">
        <dc:Bounds x="365.7298680275271" y="118.77260395167846" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="365.7298680275271" y="203.77260395167846" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_pggrSo" bpmnElement="Activity_pggrSo">
        <dc:Bounds x="570.7298680275271" y="118.77260395167846" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="570.7298680275271" y="203.77260395167846" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_WOsnmX" bpmnElement="Activity_WOsnmX">
        <dc:Bounds x="365.7298680275271" y="289.77260395167843" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="365.7298680275271" y="374.77260395167843" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_WqaLIS" bpmnElement="Activity_WqaLIS">
        <dc:Bounds x="570.7298680275271" y="289.77260395167843" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="570.7298680275271" y="374.77260395167843" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_xcOCw2-Activity_pggrSo" bpmnElement="xy-edge__Activity_xcOCw2-Activity_pggrSo">
        <di:waypoint x="465.7298680275271" y="159"/>
        <di:waypoint x="570.7298680275271" y="159"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_WOsnmX-Activity_WqaLIS" bpmnElement="xy-edge__Activity_WOsnmX-Activity_WqaLIS">
        <di:waypoint x="465.7298680275271" y="330"/>
        <di:waypoint x="570.7298680275271" y="330"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_pggrSo-Event_fsEMsz" bpmnElement="xy-edge__Activity_pggrSo-Event_fsEMsz">
        <di:waypoint x="670.7298680275271" y="159"/>
        <di:waypoint x="756" y="159"/>
        <di:waypoint x="756" y="218"/>
        <di:waypoint x="841.0228202402585" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Activity_WqaLIS-Event_fsEMsz" bpmnElement="xy-edge__Activity_WqaLIS-Event_fsEMsz">
        <di:waypoint x="670.7298680275271" y="330"/>
        <di:waypoint x="756" y="330"/>
        <di:waypoint x="756" y="218"/>
        <di:waypoint x="841.0228202402585" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Event_start-Activity_WOsnmX" bpmnElement="xy-edge__Event_start-Activity_WOsnmX">
        <di:waypoint x="196.83999999999997" y="226"/>
        <di:waypoint x="281" y="226"/>
        <di:waypoint x="281" y="330"/>
        <di:waypoint x="365.7298680275271" y="330"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Event_start-Activity_xcOCw2" bpmnElement="xy-edge__Event_start-Activity_xcOCw2">
        <di:waypoint x="196.83999999999997" y="226"/>
        <di:waypoint x="281" y="226"/>
        <di:waypoint x="281" y="159"/>
        <di:waypoint x="365.7298680275271" y="159"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>  
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "I've replaced 'the customer picks up the goods' by 'the user makes the order', it's only a name change of that task, all other components and connections in the BPMN remain the same.",
    "title": "Change some task name",
    "delete":[],
    "update":
    [
        {
            "type": "bpmn:serviceTask",
            "id": "Activity_WqaLIS",
            "name": "User makes the order"
        }
    ],
    "add":[]
}
```

##Textual description:
"Remove Decision from the process"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="89566dd4-1db0-452c-afe5-1396d7cfcb4e"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>xy-edge__Event_start-Gateway_sub_decision</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_end">
      <bpmn:incoming>xy-edge__Task_A1-Event_end</bpmn:incoming>
      <bpmn:incoming>xy-edge__Task_A2-Event_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_sub_decision" name="Decision">
      <bpmn:incoming>xy-edge__Event_start-Gateway_sub_decision</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Gateway_sub_decision-Task_A1</bpmn:outgoing>
      <bpmn:outgoing>xy-edge__Gateway_sub_decision-Task_A2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_A1" name="Perform Task A1" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_sub_decision-Task_A1</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_A1-Event_end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_A2" name="Perform Task A2" implementation="service-implementation">
      <bpmn:incoming>xy-edge__Gateway_sub_decision-Task_A2</bpmn:incoming>
      <bpmn:outgoing>xy-edge__Task_A2-Event_end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="xy-edge__Gateway_sub_decision-Task_A1" sourceRef="Gateway_sub_decision" targetRef="Task_A1"/>
    <bpmn:sequenceFlow id="xy-edge__Gateway_sub_decision-Task_A2" sourceRef="Gateway_sub_decision" targetRef="Task_A2"/>
    <bpmn:sequenceFlow id="xy-edge__Task_A1-Event_end" sourceRef="Task_A1" targetRef="Event_end"/>
    <bpmn:sequenceFlow id="xy-edge__Task_A2-Event_end" sourceRef="Task_A2" targetRef="Event_end"/>
    <bpmn:sequenceFlow id="xy-edge__Event_start-Gateway_sub_decision" sourceRef="Event_start" targetRef="Gateway_sub_decision"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="S_Event_start" bpmnElement="Event_start">
        <dc:Bounds x="346.59999999999997" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="346.59999999999997" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Event_end" bpmnElement="Event_end">
        <dc:Bounds x="831.4370056152343" y="208.8" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="831.4370056152343" y="249.8" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Gateway_sub_decision" bpmnElement="Gateway_sub_decision">
        <dc:Bounds x="473.2" y="193" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="473.2" y="248" width="50" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_A1" bpmnElement="Task_A1">
        <dc:Bounds x="635.5999999999999" y="92" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635.5999999999999" y="177" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Task_A2" bpmnElement="Task_A2">
        <dc:Bounds x="635.5999999999999" y="283.4" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635.5999999999999" y="368.4" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_sub_decision-Task_A1" bpmnElement="xy-edge__Gateway_sub_decision-Task_A1">
        <di:waypoint x="523" y="218"/>
        <di:waypoint x="592" y="218"/>
        <di:waypoint x="592" y="132"/>
        <di:waypoint x="633" y="132"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Gateway_sub_decision-Task_A2" bpmnElement="xy-edge__Gateway_sub_decision-Task_A2">
        <di:waypoint x="523" y="218"/>
        <di:waypoint x="586" y="218"/>
        <di:waypoint x="586" y="323"/>
        <di:waypoint x="636" y="323"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_A1-Event_end" bpmnElement="xy-edge__Task_A1-Event_end">
        <di:waypoint x="736" y="132"/>
        <di:waypoint x="784" y="132"/>
        <di:waypoint x="784" y="227"/>
        <di:waypoint x="831" y="227"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Task_A2-Event_end" bpmnElement="xy-edge__Task_A2-Event_end">
        <di:waypoint x="736" y="323"/>
        <di:waypoint x="783" y="323"/>
        <di:waypoint x="783" y="227"/>
        <di:waypoint x="831" y="227"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_xy-edge__Event_start-Gateway_sub_decision" bpmnElement="xy-edge__Event_start-Gateway_sub_decision">
        <di:waypoint x="383" y="218"/>
        <di:waypoint x="473" y="218"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>  
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "I've removed the Decision gateway along with its connecting sequence flows from the process.",
    "title": "Remove Decision",
    "delete":
    [
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Gateway_sub_decision-Task_A1"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Gateway_sub_decision-Task_A2"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "xy-edge__Event_start-Gateway_sub_decision"
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_sub_decision"
        }
    ],
    "update":[],
    "add":[]
}
```

##Textual description:
"Add a lane to include current process"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/********/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="cd63ed2b">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:startEvent id="Event_start" name="Start doing">
      <bpmn:extensionElements>
        <uipath:entryPointId value="b648edfc-c256-48d2-bd89-d2b7546b641b"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>edge_Event_start-Activity_MTuIbS</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_MTuIbS" name="Do sth">
      <bpmn:incoming>edge_Event_start-Activity_MTuIbS</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_MTuIbS-Event_iyzjOE</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_iyzjOE" name="End doing">
      <bpmn:incoming>edge_Activity_MTuIbS-Event_iyzjOE</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="edge_Event_start-Activity_MTuIbS" sourceRef="Event_start" targetRef="Activity_MTuIbS"/>
    <bpmn:sequenceFlow id="edge_Activity_MTuIbS-Event_iyzjOE" sourceRef="Activity_MTuIbS" targetRef="Event_iyzjOE"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="S_Event_start" bpmnElement="Event_start">
        <dc:Bounds x="260" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="260" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Activity_MTuIbS" bpmnElement="Activity_MTuIbS">
        <dc:Bounds x="396" y="178" width="100" height="80"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="396" y="263" width="100" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="S_Event_iyzjOE" bpmnElement="Event_iyzjOE">
        <dc:Bounds x="596" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="596" y="241" width="36" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_edge_Event_start-Activity_MTuIbS" bpmnElement="edge_Event_start-Activity_MTuIbS">
        <di:waypoint x="296" y="218"/>
        <di:waypoint x="396" y="218"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_edge_Activity_MTuIbS-Event_iyzjOE" bpmnElement="edge_Activity_MTuIbS-Event_iyzjOE">
        <di:waypoint x="496" y="218"/>
        <di:waypoint x="596" y="218"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
  {
    "explanation": "I've added a lane to include the existing process, ensuring the process is organized within a specific lane.",
    "title": "Add Lane to Process",
    "delete": [],
    "update": [
      {
        "type": "bpmn:startEvent",
        "id": "Event_start",
        "parentId": "Process_1",
        "name": "Start doing",
        "data": {
          "label": "Start doing",
          "laneId": "Lane_1"
        }
      },
      {
        "type": "bpmn:task",
        "id": "Activity_MTuIbS",
        "parentId": "Process_1",
        "name": "Do sth",
        "data": {
          "label": "Do sth",
          "laneId": "Lane_1"
        }
      },
      {
        "type": "bpmn:endEvent",
        "id": "Event_iyzjOE",
        "parentId": "Process_1",
        "name": "End doing",
        "data": {
          "label": "End doing",
          "laneId": "Lane_1"
        }
      }
    ],
    "add": [
      {
        "type": "bpmn:laneSet",
        "id": "LaneSet_1",
        "parentId": "Process_1"  
      },
      {
        "type": "bpmn:lane",
        "id": "Lane_1",
        "parentId": "LaneSet_1",
        "position": {
            "x": 200,
            "y": 100
        },
        "width": 500,
        "height": 180  
      }
    ]
  }
```

##Textual description:
"Create a process with two groups: Intake and Processing, with tasks properly organized in each group"
##Current BPMN model in XML format:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
</bpmn:definitions>
```
##Expected BPMN model in JSON format:
```json
{
    "explanation": "I've created a workflow with two distinct groups that organize the tasks in the process.",
    "title": "Workflow with Grouped Tasks",
    "delete": [],
    "update": [],
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_Workflow",
            "name": "Grouped Tasks Workflow"
        },
        {
            "type": "bpmn:startEvent",
            "id": "Event_start",
            "parentId": "Process_Workflow",
            "name": "Start Process",
            "data": {
                "label": "Start Process"
            },
            "position": {
                "x": 260,
                "y": 200
            },
            "width": 36,
            "height": 36  
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ReceiveData",
            "parentId": "Process_Workflow",
            "name": "Receive Data",
            "data": {
                "label": "Receive Data"
            },
            "position": {
                "x": 378,
                "y": 178
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ValidateData",
            "parentId": "Process_Workflow",
            "name": "Validate Data",
            "data": {
                "label": "Validate Data"
            },
            "position": {
                "x": 528,
                "y": 178
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_ProcessData",
            "parentId": "Process_Workflow",
            "name": "Process Data",
            "data": {
                "label": "Process Data"
            },
            "position": {
                "x": 678,
                "y": 178
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_GenerateOutput",
            "parentId": "Process_Workflow",
            "name": "Generate Output",
            "data": {
                "label": "Generate Output"
            },
            "position": {
                "x": 828,
                "y": 178
            },
            "width": 100,
            "height": 80
        },
        {
            "type": "bpmn:endEvent",
            "id": "Event_end",
            "parentId": "Process_Workflow",
            "name": "End Process",
            "data": {
                "label": "End Process"
            },
            "position": {
                "x": 1010,
                "y": 200
            },
            "width": 36,
            "height": 36
        },
        {
            "type": "bpmn:group",
            "id": "Group_Intake",
            "parentId": "Process_Workflow",
            "name": "Data Intake",
            "data": {
                "label": "Data Intake",
                "nodes": ["Task_ReceiveData", "Task_ValidateData"]
            },
            "position": {
                "x": 0,
                "y": 0
            },
            "width": 4,
            "height": 9
        },
        {
            "type": "bpmn:group",
            "id": "Group_Processing",
            "parentId": "Process_Workflow",
            "name": "Data Processing",
            "data": {
                "label": "Data Processing",
                "nodes": ["Task_ProcessData", "Task_GenerateOutput"]
            },
            "position": {
                "x": 0,
                "y": 0
            },
            "width": 4,
            "height": 9
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_start_receive",
            "source": "Event_start",
            "target": "Task_ReceiveData",
            "parentId": "Process_Workflow",
            "waypoints": [
                {
                    "x": 296,
                    "y": 218
                },
                {
                    "x": 378,
                    "y": 218
                }
            ]            
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_receive_validate",
            "source": "Task_ReceiveData",
            "target": "Task_ValidateData",
            "parentId": "Process_Workflow",
            "waypoints": [
                {
                    "x": 478,
                    "y": 218
                },
                {
                    "x": 528,
                    "y": 218
                }
            ]  
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_validate_process",
            "source": "Task_ValidateData",
            "target": "Task_ProcessData",
            "parentId": "Process_Workflow",
            "waypoints": [
                {
                    "x": 628,
                    "y": 218
                },
                {
                    "x": 678,
                    "y": 218
                }
            ]  
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_process_generate",
            "source": "Task_ProcessData",
            "target": "Task_GenerateOutput",
            "parentId": "Process_Workflow",
            "waypoints": [
                {
                    "x": 778,
                    "y": 218
                },
                {
                    "x": 828,
                    "y": 218
                }
            ]  
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_generate_end",
            "source": "Task_GenerateOutput",
            "target": "Event_end",
            "parentId": "Process_Workflow",
            "waypoints": [
                {
                    "x": 928,
                    "y": 218
                },
                {
                    "x": 1010,
                    "y": 218
                }
            ]  
        }
    ]
}
```
