limitOfHistories: 10
default_bpmn_xml: |-
  <?xml version="1.0" encoding="UTF-8"?>
  <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="uipath-bpmn-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL">
  </bpmn:definitions>
default_dmn: |-
  ""
###########
# PROMPTS #
###########

prompt:
  system_template:      
    summarize_history: |-
      You are an expert specializing summarizing provided historical messages into a single history message.
      The number of histories might be 0 or many. The format of each single history message is as below:
      --- MESSAGE at <timestamp> ---
      User: <request>
      AI: <response>
      --- <PERSON><PERSON> MESSAGE ---
      ```
      You need to return the summarized history message in a single record represented in JSON format:
      ```json
      {{
        "request": "string",
        "response": "string"
      }}
      ```

  user_template:
    messages_with_decision: |-
      --- MESSAGE at {timestamp} ---
      User: {request}
      AI: {response}
      User: {decision}

    messages_without_decision: |-
      --- MESSAGE at {timestamp} ---
      User: {request}
      AI: {response}

    summarize_history: |-
      Here are the provided histories:
      {chat_history}
