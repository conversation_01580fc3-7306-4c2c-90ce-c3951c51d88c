import json
import pathlib

from lxml import etree

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import FLOW_ELEMENTS, SUPPORTED_ELEMENTS
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import BpmnValidationResult


class BpmnValidator:
    @staticmethod
    def load_xml_schema(xsd_file: pathlib.Path) -> etree.XMLSchema:
        with open(xsd_file, encoding="utf-8") as xsd:
            schema = etree.parse(xsd, parser=etree.XMLParser(remove_blank_text=True, encoding="utf-8", resolve_entities=False, no_network=True))
        return etree.XMLSchema(schema)

    SCHEMA = load_xml_schema((pathlib.Path(__file__).parent / "resources/BPMN20.xsd").absolute())

    def validate_xml(self, bpmn_result: str) -> BpmnValidationResult:
        result = BpmnValidationResult(valid=True, error_message=None)
        try:
            xml_obj = self.create_xml(bpmn_result)

            if not self.SCHEMA.validate(xml_obj):
                result["valid"] = False
                result["error_message"] = self.create_error_message(self.SCHEMA.error_log)

        except etree.XMLSyntaxError as e:
            result["valid"] = False
            result["error_message"] = str(e)
        finally:
            self.SCHEMA._clear_error_log()

        return result

    def create_xml(self, xml_string: str):
        parser = etree.XMLParser(recover=False, encoding="utf-8", resolve_entities=False, no_network=True)
        return etree.fromstring(xml_string.encode(encoding="utf-8"), parser=parser)

    def create_error_message(self, error_log) -> str:
        return "\n".join(str(error) for error in error_log)

    def validate_json(self, bpmn_result: str, json_string: str) -> BpmnValidationResult:
        result = BpmnValidationResult(valid=True, error_message=None)
        errors = []
        try:
            xml_obj = self.create_xml(bpmn_result)
            json_obj = json.loads(json_string)

            if "explanation" not in json_obj:
                errors.append("Explanation is missing in JSON.")

            existing_actions = list(set(json_obj.keys()).intersection({"add", "update", "delete"}))

            # validation of elements in JSON by baseElement properties - id and type
            # if the element doesn't contain id or type, we can't validate it by next steps
            # if the element type is not supported, we add an error message
            for action in existing_actions:
                for element in json_obj[action]:
                    self.validate_necessary_fields(element, errors)
            if errors:
                return BpmnValidationResult(valid=False, error_message=self.create_error_message(errors))

            if "delete" in json_obj:
                self._validate_deleted_objects(xml_obj, json_obj["delete"], errors)
            # get all elements with id from the XML as key and the element type as value
            existing_ids_in_xml = {
                element.get("id"): (element.prefix or "") + ":" + (etree.QName(element).localname or "") for element in xml_obj.iter() if element.get("id")
            }

            # check if the elements is duplicated in the add elements of JSON
            add_elements_dict = {}

            if "add" in json_obj:
                for element in json_obj["add"]:
                    if element["id"] in add_elements_dict:
                        errors.append(f"Duplicated element id {element['id']} in JSON for type: {element['type']}.")
                    else:
                        add_elements_dict[element["id"]] = element

            # for add and update elements, check if the required fields are present in the JSON object and Ids check in the XML and JSON objects
            if "add" in json_obj:
                for element in json_obj["add"]:
                    if element["id"] in existing_ids_in_xml:
                        errors.append(f"Element with id {element['id']} is duplicated in the XML.")

                    # if element definition is not supported, we skip the validation for now
                    if element["type"].lower() not in SUPPORTED_ELEMENTS:
                        continue
                    else:
                        # Check if the required fields for the element are present in the element. If not we add an error
                        self.validate_required_fields(element, errors)

                    # Check if the parent element exists in the XML or JSON and if the parent is a valid parent for the element. If not we add an error
                    if "parentId" in element:
                        self.validate_parent(element, errors, existing_ids_in_xml, add_elements_dict)

                    self.validate_pipeline(element, errors, existing_ids_in_xml, add_elements_dict)

            # for update elements, check if the element exists in the XML and if the source and target elements exist in the XML for edges
            if "update" in json_obj:
                for element in json_obj["update"]:
                    if element["id"] not in existing_ids_in_xml:
                        errors.append(f"Element {element['type']} with id {element['id']} does not exist in XML.")

                    # if element definition is not supported, we skip the validation for now
                    if element["type"].lower() not in SUPPORTED_ELEMENTS:
                        continue

                    self.validate_pipeline(element, errors, existing_ids_in_xml, add_elements_dict)

        except etree.XMLSyntaxError as e:
            errors.append(f"Error parsing BPMN XML: {e}")
        except Exception as e:
            errors.append(f"Error parsing BPMN JSON : {e}")

        result["valid"] = not errors
        result["error_message"] = self.create_error_message(errors)
        return result

    def validate_qa_json(self, json_string: str) -> BpmnValidationResult:
        result = BpmnValidationResult(valid=True, error_message=None)
        errors = []
        try:
            json_obj = json.loads(json_string)

            if "explanation" not in json_obj:
                errors.append("Explanation is missing in JSON.")
                return BpmnValidationResult(valid=False, error_message=self.create_error_message(errors))

        except Exception as e:
            errors.append(f"Error parsing BPMN JSON : {e}")

        result["valid"] = not errors
        result["error_message"] = self.create_error_message(errors)
        return result

    def _validate_deleted_objects(self, xml_obj, deletedElements: list, errors: list):
        existing_ids_in_xml = {
            element.get("id"): (element.prefix or "") + ":" + (etree.QName(element).localname or "") for element in xml_obj.iter() if element.get("id")
        }
        # we do this validation as first step to avoid problem when LLM delete element and add new element with the same id
        # for delete elements, check if the element exists in the XML and if the source and target elements exist in the XML for edges
        for element in deletedElements:
            if element["id"] not in existing_ids_in_xml:
                errors.append(f"Element with id {element['id']} does not exist in XML.")

        # Find and remove element by "process" id using XPath
        for element in deletedElements:
            if element["id"] in existing_ids_in_xml:
                xml_elements = xml_obj.xpath(f"//*[@id='{element['id']}']")
                if xml_elements:
                    for xml_element in xml_elements:
                        parent = xml_element.getparent()
                        if parent is not None:
                            parent.remove(xml_element)

    def validate_pipeline(self, element: dict, errors: list, existing_ids_in_xml: dict, add_elements_dict: dict):
        # Flow elements validation
        if element["type"].lower() in FLOW_ELEMENTS:
            self.validate_flows(element, errors, existing_ids_in_xml, add_elements_dict)

        # validate logic of complex that related of other elements
        self.validate_complex_elements(element, errors, existing_ids_in_xml, add_elements_dict)

    def validate_necessary_fields(self, element: dict, errors: list):
        for field in ["id", "type"]:
            if field not in element:
                errors.append(f"Element {element} is missing nessesary field {field}.")

    def validate_required_fields(self, element: dict, errors: list):
        # Check if the required fields for the element are present in the JSON. If not we add an error message
        lower_element_type = element["type"].lower()
        if "required" in SUPPORTED_ELEMENTS[lower_element_type]:
            for field in SUPPORTED_ELEMENTS[lower_element_type]["required"]:
                if field not in element:
                    errors.append(f"Element {element['type']} with id {element['id']} is missing required field {field}.")
        if "dataRequired" in SUPPORTED_ELEMENTS[lower_element_type]:
            for field in SUPPORTED_ELEMENTS[lower_element_type]["dataRequired"]:
                if "data" in element and field not in element["data"]:
                    errors.append(f"Element {element['type']} with id {element['id']} is missing required field {field} in data.")

    def validate_flows(self, element: dict, errors: list, existing_ids_in_xml: dict, add_elements_dict: dict):
        # Check if the source and target elements exist by id in the XML and JSON. If not we add an error message
        if not (element["source"] in existing_ids_in_xml or (element["source"] in add_elements_dict and add_elements_dict[element["source"]] is not element)):
            errors.append(f"Source element with id {element['source']} for {element['type']} element with id {element['id']} does not exist.")
        if not (element["target"] in existing_ids_in_xml or (element["target"] in add_elements_dict and add_elements_dict[element["target"]] is not element)):
            errors.append(f"Target element with id {element['target']} for {element['type']} element with id {element['id']} does not exist.")

    def validate_parent(self, element: dict, errors: list, existing_ids_in_xml: dict, add_elements_dict: dict):
        # if the parent element does not exist in the XML or JSON, we add an error message
        if not (element["parentId"] in existing_ids_in_xml or element["parentId"] in add_elements_dict):
            errors.append(f"Parent element with id {element['parentId']} for {element['type']} element with id {element['id']} does not exist.")
        # check if the parent element exists in the XML and if the parent is a valid parent for the element. If not we add an error message
        elif element["parentId"] in existing_ids_in_xml and not self.is_valid_parent(element["type"], existing_ids_in_xml[element["parentId"]]):
            errors.append(f"Element with id {element['id']} has a bad parent element with id {element['parentId']}.")
        # check if the parent element exists in the JSON and if the parent is a valid parent for the element. If not we add an error message
        elif element["parentId"] in add_elements_dict and not self.is_valid_parent(element["type"], add_elements_dict[element["parentId"]]["type"]):
            errors.append(f"Element with id {element['id']} has a bad parent element with id {element['parentId']}.")

    # Check if element can be a child of parent by elements type
    def is_valid_parent(self, element_type: str, elements_parent_type: str) -> bool:
        return elements_parent_type.lower() in SUPPORTED_ELEMENTS[element_type.lower()]["parent"]

    def validate_complex_elements(self, element: dict, errors: list, existing_ids_in_xml: dict, add_elements_dict: dict):
        # Participant element should have processRef and it must be reference an existing bpmn:Process ID
        if element["type"].lower() == "bpmn:participant" and "data" in element and "processRef" in element["data"]:
            processRef = element["data"]["processRef"]
            if processRef in existing_ids_in_xml:
                if existing_ids_in_xml[processRef].lower() != "bpmn:process":
                    errors.append(f"Participant element with id {element['id']} has a bad processRef element with id {processRef}.")
            elif processRef in add_elements_dict:
                if add_elements_dict[processRef]["type"].lower() != "bpmn:process":
                    errors.append(f"Participant element with id {element['id']} has a bad processRef element with id {processRef}.")
            else:
                errors.append(f"Participant element with id {element['id']} has a bad processRef element with id {processRef}.")
