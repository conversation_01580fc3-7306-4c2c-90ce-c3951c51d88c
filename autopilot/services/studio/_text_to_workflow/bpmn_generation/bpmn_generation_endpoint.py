from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnAssistantRequest,
    BpmnAssistantResponse,
    BpmnGenerationChatResponse,
    BpmnGenerationResponse,
    BpmnTelemetryRequest,
    GenerateBpmnChatRequest,
    GenerateBpmnRequest,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_task import (
    BpmnGenerationTask,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_router_task import BpmnRouterTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_telemetry import create_performance_metrics
from services.studio._text_to_workflow.bpmn_generation.bpmn_telemetry_task import BpmnTelemetryTask
from services.studio._text_to_workflow.bpmn_generation.business_process_agent import BusinessProcessAgent
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAssistantRequest
from services.studio._text_to_workflow.utils import errors, telemetry_utils
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

TASK: BpmnGenerationTask = BpmnGenerationTask()
BPMN_ROUTER_TASK: BpmnRouterTask = BpmnRouterTask()
BPMN_TELEMETRY_TASK: BpmnTelemetryTask = BpmnTelemetryTask()
LOGGER = AppInsightsLogger()
BUSINESS_PROCESS_AGENT = BusinessProcessAgent()


@telemetry_utils.log_execution_time("bpmn_generation_endpoint.generate_bpmn")
async def generate_bpmn(
    request: GenerateBpmnRequest,
) -> BpmnGenerationResponse:
    _validate_generate_bpmn_request(request)

    result = await TASK.generate_bpmn(request["userRequest"], request["currentBpmn"])

    output: BpmnGenerationResponse = {
        "result": result["result"],
        "prompt_class": result["prompt_class"],
    }
    return output


@telemetry_utils.log_execution_time("bpmn_generation_endpoint.generate_bpmn_chat")
async def generate_bpmn_chat(
    request: GenerateBpmnChatRequest,
    validate: bool = False,
) -> BpmnGenerationChatResponse:
    _validate_generate_bpmn_request(request)

    result = await TASK.generate_bpmn_chat(request["userRequest"], request["currentBpmn"], validate)

    output: BpmnGenerationChatResponse = {
        "explanation": result["explanation"],
        "title": result.get("title", None),
        "add": result.get("add", []),
        "update": result.get("update", []),
        "delete": result.get("delete", []),
    }
    return output


@telemetry_utils.log_execution_time("bpmn_generation_endpoint.generate_bpmn_assistant")
async def generate_bpmn_assistant(
    request: BpmnAssistantRequest,
    validate: bool = False,
) -> BpmnAssistantResponse:
    # Setup performance tracking
    request_id = request.get("requestId", None)
    perf = create_performance_metrics("BpmnEndpoint", request_id)
    perf.start_segment("validation")

    # Track basic request properties
    perf.add_property("validation_mode", validate)
    perf.add_property("chat_history_count", len(request.get("chatHistory") or []))
    perf.add_property("router_model", str(request.get("routerModel", "default")))

    # Validate the request
    try:
        has_file_data = request.get("image") is not None or request.get("document") is not None

        if not request.get("userRequest") and not has_file_data:
            message = "User request is empty"
            perf.add_property("error", "empty_user_request")
            return BpmnAssistantResponse(valid=False, explanation=message)

        # Track whether there is an image, document, or current BPMN
        perf.add_property("has_image", request.get("image") is not None)
        perf.add_property("has_document", request.get("document") is not None)
        perf.add_property("has_any_file", has_file_data)
        perf.add_property("has_current_bpmn", request.get("currentBpmn") is not None)

    finally:
        perf.end_segment()

    # Process with router
    perf.start_segment("router_processing")
    try:
        # Pass the performance tracker to the router task
        response = await BPMN_ROUTER_TASK.process(request, validate, external_perf=perf)
        return response
    except Exception as ex:
        error_type = type(ex).__name__
        error_message = str(ex)

        # Record error metrics
        perf.add_property("error", error_type)
        perf.add_property("error_message", error_message[:200])

        # Return error response
        return BpmnAssistantResponse(valid=False, explanation=f"Error processing request: {error_type}")
    finally:
        perf.end_segment()
        perf.record()


@telemetry_utils.log_execution_time("bpmn_generation_endpoint.generate_bpmn_telemetry")
async def generate_bpmn_business_telemetry(
    request: BpmnTelemetryRequest,
):
    return await BPMN_TELEMETRY_TASK.generate_telemetry(request)


@telemetry_utils.log_execution_time("bpmn_generation_endpoint.bpmn_agent_start")
async def business_process_assistant(request: BusinessProcessAssistantRequest, message_emitter: MessageEmitter):
    _validate_business_process_request(request)

    if request.chatHistory is not None:
        request.chatHistory = list(filter(None, request.chatHistory or []))

    await BUSINESS_PROCESS_AGENT.start(request, message_emitter)


def _validate_generate_bpmn_request(request: GenerateBpmnRequest):
    no_image = request.get("image") is None
    if no_image and not _validate_user_prompt(request["userRequest"]):
        raise errors.UnprocessableEntityError("User request is too short or does not contain any letters.")

    request["currentBpmn"] = request.get("currentBpmn", "")


def _validate_user_prompt(user_prompt: str) -> bool:
    # min length
    length_requirement = len(user_prompt) >= 5

    # contains at least one letter
    letters_requirement = bool(any(letter.isalpha() for letter in user_prompt))

    return length_requirement and letters_requirement


def _validate_business_process_request(request: BusinessProcessAssistantRequest):
    no_image = request.image is None
    if no_image and not _validate_user_prompt(request.userRequest):
        raise errors.UnprocessableEntityError("User request is too short or does not contain any letters.")

    request.currentProcess = request.currentProcess or ""
