import json
import os
import pathlib
from typing import (
    Any,
    Optional,
)

import langchain.prompts
import langchain.schema
import langchain_community.callbacks
from langchain.prompts.chat import MessageLikeRepresentation
from langchain_core.prompts.chat import HumanMessagePromptTemplate
from lxml import etree

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import CANNOT_PROCESS_REQUEST_EXPLANATION
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnRequestContext,
    ChatHistory,
    LightModelType,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()


class BpmnBaseTask:
    def __init__(self, config_file: str):
        self.model_manager = ModelManager()
        self._init_config(config_file)

    def _init_config(self, config_file: str):
        self.config = yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", config_file)))
        self.common_config = yaml_load(path_or_stream=pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "common_prompt.yaml")))
        self.bpmn_patch_schema = BpmnBaseTask.read_content((pathlib.Path(__file__).parent / "config/bpmn_patch_schema.json").absolute())

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        raise NotImplementedError

    async def _call_llm(
        self,
        system_message: MessageLikeRepresentation,
        user_message: langchain.schema.BaseMessage,
        use_case: str,
    ) -> tuple[str, TokenUsage]:
        gen_template = langchain.prompts.ChatPromptTemplate.from_messages([system_message, user_message])
        inputs = {}
        gen_model = self.model_manager.get_llm_model(use_case, ConsumingFeatureType.BPMN_GENERATION)
        # LOGGER.info("Prompt:\n" + gen_template.format(**inputs))
        chat_chain = gen_template | gen_model
        with langchain_community.callbacks.get_openai_callback() as cb:
            response = await chat_chain.ainvoke(inputs)
            result = response.content if isinstance(response.content, str) else str(response.content)
            usage = TokenUsage(
                model=gen_model.deployment_name,  # type: ignore
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        return result, usage

    async def _call_llm_stream(
        self,
        system_message: MessageLikeRepresentation,
        user_message: langchain.schema.BaseMessage,
        use_case: str,
    ):
        gen_template = langchain.prompts.ChatPromptTemplate.from_messages([system_message, user_message])
        inputs = {}
        gen_model = self.model_manager.get_llm_model(use_case, ConsumingFeatureType.BPMN_GENERATION)
        chat_chain = gen_template | gen_model

        with langchain_community.callbacks.get_openai_callback():
            async for chunk in chat_chain.astream(inputs):
                yield chunk.content if isinstance(chunk.content, str) else str(chunk.content)

    def _remove_bpmn_diagram(self, xml_string):
        if xml_string is None or xml_string == "":
            return xml_string
        try:
            root = etree.fromstring(
                xml_string.encode("utf-8"), parser=etree.XMLParser(recover=False, encoding="utf-8", resolve_entities=False, no_network=True)
            )
            self._remove_diagram_elements(root)
            # Convert back to string
            return etree.tostring(root, encoding="utf-8", xml_declaration=True).decode("utf-8")
        except etree.XMLSyntaxError as e:
            LOGGER.error(f"Failed to parse BPMN 2.0 XML: {e}")
            raise errors.BadRequestError("Invalid BPMN 2.0 XML")

    def _remove_diagram_elements(self, root: etree._Element):
        """
        Remove BPMNDiagram elements from the BPMN XML.
        """
        for bpmndiagram in root.xpath("//bpmndi:BPMNDiagram", namespaces={"bpmndi": "http://www.omg.org/spec/BPMN/20100524/DI"}):
            bpmndiagram.getparent().remove(bpmndiagram)

    def _system_template(self, template: str, partial_variables: Optional[dict[str, Any]] = None) -> langchain.prompts.SystemMessagePromptTemplate:
        return langchain.prompts.SystemMessagePromptTemplate.from_template(template, partial_variables=partial_variables)

    def _human_template(self, template: str) -> HumanMessagePromptTemplate:
        return langchain.prompts.HumanMessagePromptTemplate.from_template(template)

    def _get_model_name(self, model_type: ModelType) -> str:
        return f"bpmn_generation_chat_model_{model_type.value}"

    def _get_light_model_name(self, model_type: LightModelType) -> str:
        return f"bpmn_generation_chat_model_{model_type.value}"

    @staticmethod
    def read_content(file_path) -> str:
        with open(file_path, "r", encoding="utf-8") as file:
            content = file.read()
        return content

    async def custom_stream(self, state: BusinessProcessAgentState, message_emitter: MessageEmitter):
        current_tool_index = state["current_tool_index"] + 1
        planner_result = state["planner_result"]
        request = ""
        if planner_result and planner_result.tools:
            if planner_result.tools[current_tool_index].tool is not None:
                request = planner_result.tools[current_tool_index].request or ""
                if planner_result.tools[current_tool_index].tool != Tool.QA:
                    msg = {
                        "contentType": "title and reasoning",
                        "title": planner_result.tools[current_tool_index].title,
                        "reasoning": planner_result.tools[current_tool_index].reasoning,
                    }
                    await message_emitter.emit_message(msg)
        return request

    def _build_chat_history_prompt(self, chatHistories: list[ChatHistory]) -> str:
        history = ""
        for chatHistory in chatHistories:
            decision = chatHistory.get("properties", {}).get("decision", "")  # type: ignore
            if decision:
                history += self.common_config["prompt"]["user_template"]["messages_with_decision"].format(
                    request=chatHistory["request"],
                    response=chatHistory["response"],
                    decision=decision,
                    timestamp=str(chatHistory["timestamp"]),
                )
            else:
                history += self.common_config["prompt"]["user_template"]["messages_without_decision"].format(
                    request=chatHistory["request"], response=chatHistory["response"], timestamp=str(chatHistory["timestamp"])
                )
            history += "\n"
        return history

    async def _get_related_chat_history(self, chat_history: dict[Tool, list[ChatHistory]] | None, related_tools: list[Tool], model_name: str) -> str:
        if not chat_history:
            return ""

        related_chat_history = []
        for tool in related_tools:
            if tool in chat_history:
                related_chat_history.extend(chat_history[tool])
        related_chat_history.sort(key=lambda h: h["timestamp"])
        llm_config = self.model_manager.get_llm_config(model_name)
        max_total_tokens = llm_config.pop("max_total_tokens", 65536)
        max_tokens = llm_config.pop("max_tokens", 12800)
        trimmed_histories = await self._trim_chat_history(related_chat_history, max_total_tokens - max_tokens - 8000, model_name)
        return self._build_chat_history_prompt(trimmed_histories)

    async def _trim_chat_history(self, chat_history: list[ChatHistory], tokens_limit: int, model_name: str) -> list[ChatHistory]:
        compressed_history = []
        pending_history = []
        for entry in chat_history:
            decision = entry.get("properties", {}).get("decision", "")  # type: ignore
            if decision:
                if decision == "pending":
                    pending_history.append(entry)
                elif decision == "accept" or decision == "decline":
                    pending_history.append(entry)
                    pending_requests_str = ";".join([h["request"] for h in pending_history])
                    pending_history.clear()
                    entry["request"] = pending_requests_str
                    compressed_history.append(entry)
                else:
                    compressed_history.append(entry)
            else:
                compressed_history.append(entry)
        chat_history = compressed_history + pending_history
        chat_history.sort(key=lambda h: h["timestamp"])

        tokens_sum = 0
        i = 0
        for ch in reversed(chat_history):
            tokens_sum += len(ch["request"]) + len(ch["response"])
            if tokens_sum >= tokens_limit:
                break
            i += 1

        limit_of_histories = self.common_config["limitOfHistories"]
        limit = min(i, limit_of_histories)
        if len(chat_history) > limit:
            summarized_history = [await self._summarize_chat_history(chat_history[: -(limit - 1)], model_name)]
            return summarized_history + chat_history[-(limit - 1) :]
        return chat_history

    async def _summarize_chat_history(self, chatHistories: list[ChatHistory], model_name: str) -> ChatHistory:
        system_message = langchain.prompts.SystemMessagePromptTemplate.from_template(self.common_config["prompt"]["system_template"]["summarize_history"])
        user_message = langchain.prompts.HumanMessagePromptTemplate.from_template(self.common_config["prompt"]["user_template"]["summarize_history"]).format(
            chat_history=self._build_chat_history_prompt(chatHistories)
        )

        result, _ = await self._call_llm(system_message, user_message, model_name)

        result_json = result.strip("```json\n").strip("\n```")
        chat_history: ChatHistory = json.loads(result_json)
        chat_history["timestamp"] = chatHistories[-1]["timestamp"]
        chat_history["tool"] = chatHistories[-1]["tool"]
        if "properties" in chatHistories[-1]:
            chat_history["properties"] = chatHistories[-1]["properties"]

        return chat_history

    @log_execution_time("bpmn_base_task.validate_output")
    async def _validate_output(self, validate: bool, current_bpmn: str, result_json: str, usage: TokenUsage, model_name: str) -> BpmnGenerationChatTaskResult:
        validation_result = VALIDATOR.validate_json(current_bpmn, result_json) if validate else {"valid": True, "error_message": None}
        if validation_result["valid"]:
            result_output: BpmnGenerationChatTaskResult = json.loads(result_json)
            result_output["usage"] = usage
            return result_output

        LOGGER.warning(f"JSON isn't valid: {validation_result['error_message']}. Attempting to fix the JSON.")
        error_message = validation_result["error_message"] if validation_result["error_message"] is not None else "JSON error"

        fixed_result = await self._fix_bpmn("json", result_json, error_message, current_bpmn=current_bpmn, model_name=model_name)
        fixed_result_json = fixed_result.strip("```json\n").strip("\n```")
        fix_validation_result = VALIDATOR.validate_json(current_bpmn, fixed_result_json)

        if fix_validation_result["valid"]:
            fixed_output: BpmnGenerationChatTaskResult = json.loads(fixed_result_json)
            fixed_output["usage"] = usage
            return fixed_output

        LOGGER.error(
            f"Failed to generate valid BPMN 2.0 JSON. An exception occurred after multiple creation attempts: {fix_validation_result['error_message']}"
        )
        return BpmnGenerationChatTaskResult(
            add=[],
            update=[],
            delete=[],
            explanation=CANNOT_PROCESS_REQUEST_EXPLANATION,
            usage=usage,
        )

    @log_execution_time("bpmn_base_task.fix_bpmn")
    async def _fix_bpmn(
        self,
        type: str,
        bpmn: str,
        error_message: str,
        model_name: str,
        current_bpmn: str = "",
    ) -> str:
        system_message: MessageLikeRepresentation
        user_message: langchain.schema.BaseMessage
        if type == "json":
            system_message = self._system_template(self.config["prompt"]["system_template"]["fix_bpmn_json"]).format(bpmn_patch_schema=self.bpmn_patch_schema)
            user_message = self._human_template(self.config["prompt"]["user_template"]["fix_bpmn_json"]).format(
                bpmnJson=bpmn, errors=error_message, bpmnXml=current_bpmn
            )
        else:
            system_message = self._system_template(self.config["prompt"]["system_template"]["fix_bpmn_xml"])
            user_message = self._human_template(self.config["prompt"]["user_template"]["fix_bpmn_xml"]).format(bpmnXml=bpmn, errors=error_message)

        result, _ = await self._call_llm(system_message, user_message, model_name)
        return result

    def extract_process_id(self, bpmn_xml: str) -> Optional[str]:
        if not bpmn_xml:
            return None
        try:
            root = etree.fromstring(bpmn_xml.encode("utf-8"), parser=etree.XMLParser(recover=False, encoding="utf-8", resolve_entities=False, no_network=True))
            # Find process element using BPMN namespace
            namespaces = {"bpmn": "http://www.omg.org/spec/BPMN/20100524/MODEL"}
            process = root.find(".//bpmn:process", namespaces=namespaces)
            if process is not None:
                return process.attrib.get("id")
            return None
        except etree.XMLSyntaxError as e:
            LOGGER.error(f"Failed to parse BPMN XML: {e}")
            return None
