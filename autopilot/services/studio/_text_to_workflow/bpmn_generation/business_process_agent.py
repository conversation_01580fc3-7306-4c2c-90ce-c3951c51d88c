import uuid

from langchain_core.runnables.config import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.graph import CompiledGraph

from services.studio._text_to_workflow.bpmn_generation.agent.expression_generation_agent import ExpressionGenerationAgent
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_position_task import BpmnGenerationPositionTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnRequestContext,
    Tool,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task import BpmnQATask
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_ack import BusinessProcessAgentAck
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState, BusinessProcessAssistantRequest
from services.studio._text_to_workflow.bpmn_generation.business_process_planner import BusinessProcessAgentPlanner
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_flow_task import BpmnExtensionFlowTask
from services.studio._text_to_workflow.utils.request_utils import get_request_context
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

LOGGER = AppInsightsLogger()
TOOL_NODE_MAPPING = {
    Tool.ACK: "ack",
    Tool.GENERATE_BPMN: "bpmn-editor",
    Tool.EDIT_BPMN: "bpmn-editor",
    Tool.EXTENSION: "extension-editor",
    Tool.GENERATE_EXP: "expression-editor",
    Tool.QA: "qa",
}


class BusinessProcessAgent:
    def __init__(self) -> None:
        self.ack = BusinessProcessAgentAck()
        self.planner = BusinessProcessAgentPlanner()
        self.qa = BpmnQATask()
        self.bpmn_editor = BpmnGenerationPositionTask()
        self.extension_editor = BpmnExtensionFlowTask()
        self.expression_editor = ExpressionGenerationAgent()
        self.graph = self._build_agent_graph()
        self.sessions = {}

    async def dummy(self, state: BusinessProcessAgentState, config: RunnableConfig):
        pass

    def find_next_node(self, state: BusinessProcessAgentState):
        next_tool_index = state["current_tool_index"] + 1
        tool_results = state.get("tool_results") or []
        if next_tool_index > 0 and (len(tool_results) == 0 or tool_results[-1] is None):
            return "dummy"

        if state["planner_result"] and state["planner_result"].tools:
            tools = state["planner_result"].tools
            if len(tools) > next_tool_index:
                return TOOL_NODE_MAPPING.get(tools[next_tool_index].tool or Tool.UNSUPPORTED, "dummy")

        return "dummy"

    def _build_agent_graph(self) -> CompiledGraph:
        memory = MemorySaver()
        builder = StateGraph(BusinessProcessAgentState)

        builder.add_node("ack", self.ack.run)
        builder.add_node("planner", self.planner.run)
        builder.add_node("bpmn-editor", self.bpmn_editor.run)
        builder.add_node("extension-editor", self.extension_editor.run)
        builder.add_node("expression-editor", self.expression_editor.run)
        builder.add_node("qa", self.qa.run)
        builder.add_node("dummy", self.dummy)

        builder.add_edge(START, "ack")
        builder.add_edge(START, "planner")

        builder.add_conditional_edges("planner", self.find_next_node)
        builder.add_conditional_edges("bpmn-editor", self.find_next_node)
        builder.add_conditional_edges("extension-editor", self.find_next_node)
        builder.add_conditional_edges("expression-editor", self.find_next_node)

        builder.add_edge("qa", "dummy")
        builder.add_edge(["ack", "dummy"], END)

        return builder.compile(checkpointer=memory)

    @log_execution_time("bpmn_agent.start")
    async def start(self, request: BusinessProcessAssistantRequest, message_emitter: MessageEmitter):
        session_id = request.sessionId or str(uuid.uuid4())
        LOGGER.info(f"Processing BPMN assistant request with sessionId={session_id},solutionId={request.solutionId} and projectKey={request.projectKey}")
        config = RunnableConfig(configurable={"thread_id": session_id, "message_emitter": message_emitter})
        agent_state = BusinessProcessAgentState(
            request=request,
            planner_result=None,
            context=BpmnRequestContext(
                user_request="",
                current_bpmn="",
                request_context=get_request_context(),
                image_data=None,
                chat_history=None,
                extension_data_override=None,
                support_validation=False,
            ),
            current_tool_index=-1,
            tool_results=None,
            usage_token=[],
        )

        await self.graph.ainvoke(agent_state, config, stream_mode="updates")
