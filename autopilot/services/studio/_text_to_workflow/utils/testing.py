import functools
import uuid

import jwt

from services.studio._text_to_workflow.api.deps import get_context
from services.studio._text_to_workflow.common import connections_loader
from services.studio._text_to_workflow.core import constants
from services.studio._text_to_workflow.utils import uipath_cloud_platform


def build_testing_request_context():
    return get_testing_request_context(None, get_tenant())


def get_testing_request_context(localization, tenant_id, client_name="Evaluation", token=None):
    token = token or uipath_cloud_platform.get_token_from_env()
    ctx = get_context(token, tenant_id, client_name=client_name, client_version="1.0.0", localization=localization)
    return ctx


@functools.cache
def get_tenant():
    tenant_id, _ = connections_loader.get_connections_data()
    return tenant_id


@functools.cache
def get_testing_client_jwt_token():
    payload = {
        constants.USER_ID_CLAIM: str(uuid.uuid4()),
        constants.ORGANIZATION_ID_CLAIM: str(uuid.uuid4()),
        constants.SUB_TYPE_CLAIM: constants.SUB_TYPE_SERVICE,
    }

    return "Bearer " + jwt.encode(payload, "test_secret", algorithm="HS256")
