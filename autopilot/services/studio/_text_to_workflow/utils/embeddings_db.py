import pathlib
import shutil
import tempfile

from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.embeddings_client import EmbeddingsClient


async def download_hash() -> bool:
    """
    Downloads embeddings hash in a temporary folder then moves in embeddings folder
    and returns a bool with the result.

    Returns:
        bool: True if successful, otherwise False.
    """
    with tempfile.TemporaryDirectory() as tempdir:
        tempdir = pathlib.Path(tempdir)
        success = await EmbeddingsClient().download_hash(tempdir)
        if not success:
            return False
        embeddings_hash_src = tempdir / "embeddingsHash.txt"
        embeddings_hash_dst = paths.get_embeddings_path() / "embeddingsHash.txt"
        shutil.move(embeddings_hash_src, embeddings_hash_dst)
    return True


async def download_embeddings() -> bool:
    """
    Downloads embeddings db in a temporary folder then moves in embeddings folder
    and returns a bool with the result.

    Returns:
        bool: True if successful, otherwise False.
    """
    with tempfile.TemporaryDirectory() as tempdir:
        tempdir = pathlib.Path(tempdir)
        success = await EmbeddingsClient().download_embeddings(tempdir)
        if not success:
            return False
        embeddings_db_src = tempdir / "embeddings.db"
        embedding_db_dst = paths.get_embeddings_path() / "embeddings.db"
        shutil.move(embeddings_db_src, embedding_db_dst)
    return True


def get_embeddings_hash() -> str:
    """
    Get embeddings hash from file in embeddings folder.

    Returns:
        str: Embeddings hash value.
    """
    with open(paths.get_embeddings_path() / "embeddingsHash.txt", "r") as f:
        return f.read().strip()
