import asyncio
import copy
import functools
import inspect
import logging
import time

from azure.monitor.events.extension import track_event
from azure.monitor.opentelemetry import configure_azure_monitor
from fastapi import FastAP<PERSON>, Request
from opentelemetry import trace
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace.sampling import TraceIdRatioBased
from opentelemetry.semconv.trace import SpanAttributes
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from typing_extensions import Any, Callable, Dict, Optional, Type

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.utils.errors import extract_exception_summary
from services.studio._text_to_workflow.utils.request_utils import get_request_context

tracer_name = "AppInsights"


class CustomEventFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        output = super().format(record)
        if "custom_dimensions" in record.__dict__:
            output += " " + str(record.__dict__["custom_dimensions"])
        return output


class AppInsightsLogger:
    _instance = None
    logger = logging.getLogger("AppInsightsLogger")

    def __new__(cls: Type["AppInsightsLogger"]) -> "AppInsightsLogger":
        if cls._instance is None:
            # azure monitor should be set up initially for the logger
            # there are methods that have a hard dependency on azure monitor being intialized - see log_custom_event
            configure_azure_monitor_fastapi()
            cls._instance = super(AppInsightsLogger, cls).__new__(cls)
            cls.tracer = trace.get_tracer(tracer_name)
            if settings.ENABLE_TELEMETRY:
                cls._instance.logger.setLevel(logging.INFO)
                cls._instance.logger.addHandler(logging.StreamHandler())

                events_stream_logger = logging.StreamHandler()
                events_stream_logger.formatter = CustomEventFormatter()
            else:
                cls._instance.logger.propagate = False
                for handler in cls._instance.logger.handlers:
                    cls._instance.logger.removeHandler(handler)
                cls._instance.logger.addHandler(logging.NullHandler())
        return cls._instance

    def _get_additional_info(
        self,
        properties: Optional[Dict[str, Any]] = None,
        context: RequestContext | None = None,
    ) -> dict[str, Any]:
        properties = {} if properties is None else copy.deepcopy(properties)
        additional_info = _get_request_additional_info(context)
        properties.update(additional_info)
        return properties

    def info(self, message: str, properties: Dict[str, Any] | None = None) -> None:
        properties = self._get_additional_info(properties, get_request_context())
        # get the function name that calls the logger
        caller_function = inspect.stack()[1][3]
        with self.tracer.start_as_current_span("Info: " + caller_function):
            self.logger.info(message, extra=properties)

    def warning(self, message: str, properties: Dict[str, Any] | None = None) -> None:
        properties = self._get_additional_info(properties, get_request_context())
        # get the function name that calls the logger
        caller_function = inspect.stack()[1][3]
        with self.tracer.start_as_current_span("Warning: " + caller_function):
            self.logger.warning(message, extra=properties)

    def error(self, message: str, properties: Dict[str, Any] | None = None) -> None:
        """
        Will log to application insights, in the traces table, with a severity of error.
        """
        properties = self._get_additional_info(properties, get_request_context())
        # get the function name that calls the logger
        caller_function = inspect.stack()[1][3]
        with self.tracer.start_as_current_span("Error: " + caller_function):
            self.logger.error(message, extra=properties)

    def exception(self, message: str, properties: Dict[str, Any] | None = None) -> None:
        """
        Will log to application insights, in the exceptions table. Also, includes the stack trace.
        """
        properties = self._get_additional_info(properties, get_request_context())
        # get the function name that calls the logger
        caller_function = inspect.stack()[1][3]
        with self.tracer.start_as_current_span("Exception: " + caller_function):
            self.logger.exception(message, extra=properties)

    def log_custom_event(self, name: str, properties: Dict[str, Any] | None = None) -> None:
        # TODO: This one doesn't use self._get_additional_info and it has properties. Why? Is it because it needs to update the existing properties?
        if properties is None:
            properties = {}

        additional_info = _get_request_additional_info(get_request_context())
        properties.update(additional_info)
        if settings.APP_INSIGHTS_INSTRUMENTATION_KEY:
            track_event(name, properties)


class OpenTelemetryMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, excluded_paths: list[str] | None = None):
        super().__init__(app)
        self.excluded_paths = excluded_paths if excluded_paths is not None else []

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        # If path is excluded, skip span creation and proceed with request as normal
        request_path = request.url.path.rstrip("/")
        if request_path in self.excluded_paths:
            return await call_next(request)

        # New context with span
        tracer = trace.get_tracer(tracer_name)

        context = get_request_context()
        additional_info = _get_request_additional_info(context)
        url = str(request.url)

        operation_name = f"{request.method} {request_path}"

        attributes = {
            SpanAttributes.HTTP_METHOD: request.method,
            SpanAttributes.HTTP_URL: url,
        }
        for key, value in additional_info.items():
            if isinstance(value, dict):
                value = str(value)
            attributes[key] = value

        with tracer.start_as_current_span(operation_name, kind=trace.SpanKind.SERVER, attributes=attributes) as span:
            try:
                response = await call_next(request)
                if response:
                    span.set_attributes(
                        {
                            SpanAttributes.HTTP_STATUS_CODE: int(response.status_code),
                            "http.result_code": str(response.status_code),
                        }
                    )
                return response
            except Exception as e:
                span.record_exception(e)
                _, status_code = extract_exception_summary(e)

                span.set_attributes(
                    {
                        SpanAttributes.HTTP_STATUS_CODE: int(status_code),
                        "http.result_code": str(status_code),
                    }
                )

                if status_code >= 400:
                    span.set_status(trace.Status(trace.StatusCode.ERROR))
                    span.set_attribute("error", True)
                else:
                    span.set_status(trace.StatusCode.OK)
                    span.set_attribute("error", False)
                # re-raise the exception, the response body should be built by the exception handler middleware in service.py
                raise e


def add_azure_request_tracking_middleware(fastapi_app: FastAPI, excluded_urls: list[str] | None = None) -> None:
    if settings.APP_INSIGHTS_INSTRUMENTATION_KEY:
        fastapi_app.add_middleware(OpenTelemetryMiddleware, excluded_paths=excluded_urls)


def configure_azure_monitor_fastapi() -> None:
    """
    Configures Azure Monitor for a FastAPI application.

    This function sets up Azure Monitor telemetry for logging
    using the Application Insights instrumentation key specified in the settings.
    It configures the resource with the service name and deployment region,
    sets the sampling rate to 100%, and enables instrumentation for FastAPI
    and HTTP requests.

    Returns:
        None
    """
    if settings.APP_INSIGHTS_INSTRUMENTATION_KEY:
        resource = Resource.create({"service.name": "autopilot-studio-" + settings.DEPLOYMENT_REGION})
        sampler = TraceIdRatioBased(1.0)
        instrumentation_options = {
            "fastapi": {"enabled": True},
            "requests": {"enabled": True},
        }
        configure_azure_monitor(
            connection_string=f"InstrumentationKey={settings.APP_INSIGHTS_INSTRUMENTATION_KEY}",
            resource=resource,
            sampler=sampler,
            instrumentation_options=instrumentation_options,
        )
        HTTPXClientInstrumentor().instrument()


# organization id, correlation id and request id should be added to all logs
def _get_request_additional_info(
    context: RequestContext | None = None,
) -> Dict[str, Any]:
    if not context:
        return {}
    additional_info = {}

    if context.organization_id:
        additional_info["x-uipath-organization-id"] = context.organization_id

    if context.user_id:
        additional_info["x-uipath-user-id"] = context.user_id

    if context.correlation_id:
        additional_info["x-uipath-correlation-id"] = context.correlation_id

    if context.request_id:
        additional_info["request-id"] = context.request_id

    if context.client_name:
        additional_info["x-uipath-client-name"] = context.client_name

    if context.client_version:
        additional_info["x-uipath-client-version"] = context.client_version

    if context.license_type:
        additional_info["x-uipath-license"] = context.license_type

    if context.studio_project_id:
        additional_info["x-uipath-studio-project-id"] = context.studio_project_id

    if context.tenant_id:
        additional_info["x-uipath-tenant-id"] = context.tenant_id

    if settings.API_BUILD_VERSION:
        additional_info["api-build-version"] = settings.API_BUILD_VERSION

    return additional_info


def log_execution_time(tracked_section: str) -> Callable[..., Any]:
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            logger = AppInsightsLogger()
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.log_custom_event(f"TextToWorkflow.{tracked_section}", {"duration": round(duration, 3)})

            return result

        @functools.wraps(func)
        async def awrapper(*args: Any, **kwargs: Any) -> Any:
            logger = AppInsightsLogger()
            start_time = time.time()
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.log_custom_event(f"TextToWorkflow.{tracked_section}", {"duration": round(duration, 3)})

            return result

        return awrapper if asyncio.iscoroutinefunction(func) else wrapper

    return decorator
