import os
import threading

import numpy as np
import torch
from openai import AzureOpenAI
from sentence_transformers import CrossEncoder, SentenceTransformer

from services.studio._text_to_workflow.utils import paths, telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()

INSTRUCTIONS: dict[str, dict[str, str]] = {
    "qa": {
        "query": "Represent this query for retrieving relevant documents: ",
        "key": "Represent this document for retrieval: ",
    },
    "icl": {
        "query": "Convert this example into vector to look for useful examples: ",
        "key": "Convert this example into vector for retrieval: ",
    },
    "chat": {
        "query": "Embed this dialogue to find useful historical dialogues: ",
        "key": "Embed this historical dialogue for retrieval: ",
    },
    "lrlm": {
        "query": "Embed this text chunk for finding useful historical chunks: ",
        "key": "Embed this historical text chunk for retrieval: ",
    },
    "tool": {
        "query": "Transform this user request for fetching helpful tool descriptions: ",
        "key": "Transform this tool description for retrieval: ",
    },
    "convsearch": {
        "query": "Encode this query and context for searching relevant passages: ",
        "key": "Encode this passage for retrieval: ",
    },
}

_INSTANCE: dict[str, SentenceTransformer | CrossEncoder] = {}
_AZURE_INSTANCE: dict[str, AzureOpenAI] = {}
_LOCK = threading.Lock()


class RerankingModel(object):
    model: CrossEncoder

    def __init__(self, config: dict):
        self.deployment_name = config["deployment_name"]
        self._initialized = False

    def _initialize(self):
        with _LOCK:
            # Check again after acquiring lock to prevent double initialization
            if self._initialized:
                return

            if self.deployment_name not in _INSTANCE:
                cross_encoder_model = CrossEncoder(str(paths.get_models_path() / self.deployment_name))
                if torch.cuda.is_available():
                    cross_encoder_model.model.cuda()
                elif torch.backends.mps.is_available():
                    cross_encoder_model.model.to("mps")
                _INSTANCE[self.deployment_name] = cross_encoder_model
                LOGGER.info(f"Loaded {self.deployment_name} reranking model on device {cross_encoder_model.model.device}")

            self.model = _INSTANCE[self.deployment_name]
            self._initialized = True

    @telemetry_utils.log_execution_time("RerankingModel.predict")
    def predict(self, query, texts):
        """Rerank the texts based on the query. Indices and scores will be in decreasing order of relevance."""
        if not self._initialized:
            self._initialize()

        x = [[query, text] for text in texts]
        scores = self.model.predict(x)
        indices = np.argsort(-scores)
        return indices, scores


class EmbeddingModel:
    model: SentenceTransformer
    deployment: str

    def __init__(self, config):
        self.model_type = config["model_type"]
        self.deployment_name = config["deployment_name"]
        self.config = config
        self._initialized = False

    def _initialize(self):
        with _LOCK:
            # Check again after acquiring lock to prevent double initialization
            if self._initialized:
                return

            if self.model_type == "sentence-embedding":
                split = self.deployment_name.split("/", 1)
                if len(split) == 2:
                    org, model_name = split
                else:
                    org, model_name = "", self.deployment_name

                if self.deployment_name not in _INSTANCE:
                    print(f"Using {self.deployment_name} embedding model")
                    if org == "uipath":
                        embedding_model = SentenceTransformer(str(paths.get_models_path() / model_name), trust_remote_code=True)
                    else:
                        embedding_model = SentenceTransformer(self.deployment_name)
                    if torch.cuda.is_available():
                        embedding_model.cuda()
                    _INSTANCE[self.deployment_name] = embedding_model
                    LOGGER.info(f"Loaded {self.deployment_name} embedding model on device {embedding_model.device}")

                self.model = _INSTANCE[self.deployment_name]
                self.deployment = "sentence-embedding"

            elif self.model_type == "azure-openai-embedding":
                if self.deployment_name not in _AZURE_INSTANCE:
                    print(f"Using {self.deployment_name} embedding model")
                    _AZURE_INSTANCE[self.deployment_name] = AzureOpenAI(
                        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                        api_version=os.getenv("OPENAI_API_VERSION"),
                        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                    )
                self.client = _AZURE_INSTANCE[self.deployment_name]
                self.deployment = "azure-openai-embedding"
            else:
                raise Exception("unknown embedding model type")

            self._initialized = True

    def encode(self, text, instruction_set: str | None = None, instruction_type: str | None = None) -> np.ndarray:
        if not self._initialized:
            self._initialize()

        if self.model_type == "sentence-embedding":
            if self.deployment_name == "BAAI/llm-embedder":
                assert instruction_set is not None
                assert instruction_type is not None
                instruction = INSTRUCTIONS[instruction_set][instruction_type]
                return self.model.encode(instruction + text, normalize_embeddings=True, convert_to_numpy=True)
            return self.model.encode(text, normalize_embeddings=True, convert_to_numpy=True)
        elif self.model_type == "azure-openai-embedding":
            response = self.client.embeddings.create(input=[text], model=self.deployment_name)
            result = np.array(response.data[0].embedding)
            return result / np.linalg.norm(result)
        else:
            raise Exception("unknown embedding model type")

    def encode_batch(self, texts: list[str], batch_size: int, instruction_set: str | None = None, instruction_type: str | None = None, **kwargs) -> np.ndarray:
        if not self._initialized:
            self._initialize()

        if self.model_type == "sentence-embedding":
            if self.deployment_name == "BAAI/llm-embedder":
                assert instruction_set is not None
                assert instruction_type is not None
                instruction = INSTRUCTIONS[instruction_set][instruction_type]
                texts_with_instruction = [instruction + text for text in texts]
                return self.model.encode(texts_with_instruction, batch_size=batch_size, normalize_embeddings=True, convert_to_numpy=True, **kwargs)
            return self.model.encode(texts, batch_size=batch_size, normalize_embeddings=True, convert_to_numpy=True, **kwargs)
        elif self.model_type == "azure-openai-embedding":
            all_results = []
            for i in range(0, len(texts), batch_size):
                response = self.client.embeddings.create(input=texts[i : i + batch_size], model=self.deployment_name)
                results = [np.array(d.embedding) for d in response.data]
                results = [result / np.linalg.norm(result) for result in results]
                all_results.extend(results)
            return np.array(all_results)
        else:
            raise Exception("unknown embedding model type")


if __name__ == "__main__":
    model = EmbeddingModel({"model_type": "sentence-embedding", "deployment_name": "uipath/stella-trained"})
    print(model.encode("Hello, world!"))
