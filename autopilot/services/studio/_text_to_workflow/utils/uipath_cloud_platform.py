import os
from datetime import datetime, timezone

import jwt

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.schemas.common import RequestContext

USER_ID_CLAIM = "sub"
ORGANIZATION_ID_CLAIM = "prt_id"

S2S_IPRANGE_HEADER = "X-UiPath-S2S-IpRange"


def is_token_valid(token: str | None) -> bool:
    if token is None:
        return False

    token_no_bearer = token.split(" ", 1)[-1] if token.startswith("Bearer ") else token
    if not token_no_bearer:
        return False

    # verify token
    decoded_token = jwt.decode(token_no_bearer, options={"verify_signature": False})

    # check issuer
    if decoded_token.get("iss") != f"{settings.CLOUD_URL_BASE}/identity_":
        print("Existing token does not have the correct issuer")
        return False

    # check expiration
    exp = decoded_token.get("exp")
    if exp is None or datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(tz=timezone.utc):
        print("Existing token is expired")
        return False
    return True


def get_token_from_env() -> str:
    token = settings.UIPATH_TOKEN

    if not settings.USE_LLM_GATEWAY and settings.UIPATH_TOKEN:
        return settings.UIPATH_TOKEN  # not using the token

    # TODO: do this; requires converting the function to async
    # if token is None and os.getenv("UIPATH_CLIENT_ID") is not None:
    #     for _ in range(5):
    #         token = await studio_token_helper.get_studio_token()
    #         if token is not None and token != "":
    #             break
    #         print("Retrying to get the token...", file=sys.stdout)

    if token is None or not is_token_valid(token):
        token = get_token_from_console()
        settings.UIPATH_TOKEN = token
    return token


def get_token_from_console():
    print("Tip: If you don't have a token, you can get it via the lower-left thumbprint icon in StudioWeb after adding ?dev=copyTokenButton to the URL.")
    token = input("Token: ")
    os.environ["UIPATH_TOKEN"] = token
    return token


def get_token_from_context(request_context: RequestContext | None):
    # checks context for request details
    if request_context is None:
        return None
    return request_context.raw_jwt


def get_tenant_id_from_context(request_context: RequestContext | None):
    # checks context for request details
    if request_context is None:
        return None

    return request_context.tenant_id


def get_client_name_from_context(request_context: RequestContext | None):
    if request_context is None:
        return None
    return request_context.client_name


def get_client_version_from_context(request_context: RequestContext | None):
    if request_context is None:
        return None
    return request_context.client_version


def get_s2s_iprange_from_context(request_context: RequestContext | None):
    if request_context is None:
        return None
    return request_context.s2s_iprange


def get_organization_id_from_context(request_context: RequestContext | None):
    # get organization id from token
    if request_context is None:
        return None

    token = request_context.raw_jwt
    if not token:
        return None

    _, organization_id = decode_token(token)
    return organization_id


def get_user_id_from_context(request_context: RequestContext | None):
    # get user id from token
    if request_context is None:
        return None

    token = request_context.raw_jwt
    if not token:
        return None

    user_id, _ = decode_token(token)
    return user_id


def get_correlation_id_from_context(request_context: RequestContext | None):
    if request_context is None:
        return None

    return request_context.correlation_id


def get_request_id_from_context(request_context: RequestContext | None):
    if request_context is None:
        return None

    return request_context.request_id


def decode_token(access_token: str):
    token = access_token.split(" ")[1]  # Extract the token part after "Bearer"
    decoded = jwt.decode(token, options={"verify_signature": False})
    if USER_ID_CLAIM not in decoded or ORGANIZATION_ID_CLAIM not in decoded:
        raise Exception("Invalid user token")
    return decoded[USER_ID_CLAIM], decoded[ORGANIZATION_ID_CLAIM]
