model:
  deployment_name: gpt-4o-mini-2024-07-18
  temperature: 0.0
  max_tokens: 1000
  request_timeout: 20
  top_p: 0.0
  frequency_penalty: 0.0
  presence_penalty: 0.0
prompt_single_str:
  system_msg: |-
    You are an assistant used for translating a string to another language. The input you will receive is a string and the target language code.
    The output should be just the translated string. No additional formatting is required.
  user_msg_template: |-
    Translate the following string the language identified by the following language code {target_language}.
    {input_string}
prompt_multi_str:
  system_msg: |-
    You are an assistant used for translating a list of strings to another language, outputing a YAML list of translations (make sure to escape single quotes, output '' and not '). The input you will receive is a list of strings and the target language.
  user_msg_template: |-
    Translate the following strings into the language identified by the following language code: {target_language}.
    {input_strings}
  demo_request:
    target_language: ro
    input_strings:
    - Write a given first name, last name and age
    - When I receive an email, I want to send a response
  demo_response:
    - <PERSON><PERSON> un prenume dat, un nume de familie și o vârstă
    - Când primesc un e-mail, vreau să trimit un răspuns
