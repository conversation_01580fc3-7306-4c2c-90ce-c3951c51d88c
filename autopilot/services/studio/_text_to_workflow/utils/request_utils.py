import uuid
from contextvars import <PERSON>textVar

import typing_extensions as t

from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.request_schema import BaseRequest

context: ContextVar[RequestContext | None] = ContextVar("request_context", default=None)


def get_seed(request: BaseRequest) -> int | None:
    if "seed" in request:
        seed = request["seed"]
        del request["seed"]
        return seed
    return None


def set_request_context(request_context: RequestContext) -> None:
    context.set(request_context)


def get_request_context() -> RequestContext | None:
    return context.get()


def get_prompt_values_from_request_context() -> dict:
    """Return user context values for the prompt or default values if the context is not set"""
    request_context = context.get()
    return {
        "email": request_context.email if request_context and request_context.email else "<EMAIL>",
        "first_name": request_context.first_name if request_context and request_context.first_name else "AutopilotUser123",
        "last_name": request_context.last_name if request_context and request_context.last_name else "StudioUser123",
    }


def camel_case_to_snake_case(s: str) -> str:
    return "".join(["_" + c.lower() if c.isupper() else c for c in s]).lstrip("_")


def snake_case_to_camel_case(s: str) -> str:
    words = s.split("_")
    return words[0] + "".join(word.capitalize() for word in words[1:])


def convert_snake_case_keys_to_camel_case(obj: t.Any) -> t.Any:
    if isinstance(obj, dict):
        return {snake_case_to_camel_case(k): convert_snake_case_keys_to_camel_case(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [convert_snake_case_keys_to_camel_case(e) for e in obj]
    return obj


def convert_camel_case_keys_to_snake_case(obj: t.Any) -> t.Any:
    if isinstance(obj, dict):
        return {camel_case_to_snake_case(k): convert_camel_case_keys_to_snake_case(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [convert_camel_case_keys_to_snake_case(e) for e in obj]
    return obj


def create_request_context_id() -> str:
    return str(uuid.uuid4())
