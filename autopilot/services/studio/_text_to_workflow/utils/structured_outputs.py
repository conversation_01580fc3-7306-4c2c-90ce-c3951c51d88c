import copy
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Tuple, Type, Union

import langchain_community.callbacks
from langchain.callbacks.base import BaseCallbackHandler
from langchain.prompts import ChatPromptTemplate
from langchain.schema import <PERSON><PERSON><PERSON>ult
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, ToolMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.utils.function_calling import convert_to_openai_function
from langchain_openai import AzureChatOpenAI
from pydantic import BaseModel, ValidationError
from tenacity import AsyncRetrying, retry_if_exception_type, stop_after_attempt, wait_fixed

from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage, TokenUsageJson
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

# Logging
LOGGER = AppInsightsLogger()


class LLMRecoverableError(Exception):
    """Base class for errors that can be fixed by having the LLM try again with feedback."""

    def __init__(self, *args, **kwargs) -> None:
        self.error_description = kwargs.pop("error_description", None)
        self.last_message = kwargs.pop("last_message", None)
        self.usage = kwargs.pop("usage", None)
        super().__init__(*args, **kwargs)


class GetOAIMesageHandler(BaseCallbackHandler):
    """
    A langchain callback handler thet gets original OpenAI message.
    See: https://python.langchain.com/docs/how_to/#callbacks
    """

    def __init__(self):
        self.message = None

    def on_llm_end(self, response: LLMResult, **kwargs) -> None:
        """
        Stores original LLM response message from LLMResult object

        Args:
            response (LLMResult): The result object returned from an-end-to-end LLM.
            **kwargs: Additional keyword arguments.
        """
        try:
            self.message = response.generations[0][0].message  # type:ignore
        except Exception as ex:
            LOGGER.error(f"Callback {self.__class__.__name__} couldn't store original OpenAi message: {ex}")


def async_llm_retry(
    max_attempts: int = 3,
    wait_fixed_time: float = 1,
    reask_exceptions: Union[Type[LLMRecoverableError], Tuple[Type[LLMRecoverableError], ...]] = LLMRecoverableError,
):
    """
    A decorator that adds retry/reask functionality to async function that call LLM for completion.
    When errors are raised it will either retry simply or reask if the error of LLMRecoverableError type.

    Implements "ReAsk" retries where the LLM is informed about the previous error and asked to correct its approach.
    Simple retries for regular exceptions (e.g., network issues) are already preformed by the client.


    Args:
        max_attempts (int, optional): Maximum number of retry attempts. Defaults to 3.
        wait_fixed_time (float, optional): Time in seconds to wait between retry attempts. Defaults to 1.
        reask_exceptions (Union[Type[Exception], Tuple[Type[Exception], ...]], optional):
            Exceptions that trigger a "ReAsk" retry, where error information is passed back to the LLM.
            Defaults to RetryError.

    Returns:
        Callable: A decorated async function with retry functionality. The function maintains its
                 original return type while adding retry behavior.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            _exc = None
            _usage = None
            async for attempt in AsyncRetrying(
                stop=stop_after_attempt(max_attempts), wait=wait_fixed(wait_fixed_time), retry=retry_if_exception_type(reask_exceptions)
            ):
                with attempt:
                    # Make a copy to avoid adding extra messages over multiple attempts
                    _kwargs = copy.copy(kwargs)
                    if _exc:  # Check if last attempt had error that can be fixed via ReAsk
                        try:
                            # Add correction messages to kwargs
                            retry_msgs = [
                                _exc.last_message,
                                ToolMessage(
                                    name=_exc.last_message.tool_calls[0]["name"],
                                    tool_call_id=_exc.last_message.tool_calls[0]["id"],
                                    content=f"Validation Error found:\n{_exc.error_description}\nRecall the function correctly, fix the errors.",
                                ),
                            ]
                            messages = _kwargs.get("messages", args[0] if args else None)
                            messages.extend(retry_msgs)
                        except Exception as ex:
                            LOGGER.error(f"Couldn't load ReAsk messages because of error: {ex}")

                    try:
                        if attempt.retry_state.attempt_number > 1:
                            LOGGER.info(f"Attempt {attempt.retry_state.attempt_number - 1} failed. Retrying completion (max attempts: {max_attempts})...")
                        so, usage = await func(*args, **_kwargs)
                        return so, add_completion_usage(usage, _usage)

                    # Trigger ReAsk
                    except reask_exceptions as ex:
                        _exc = ex
                        _usage = add_completion_usage(ex.usage, _usage)
                        raise ex

        return wrapper

    return decorator


async def async_generate_json_mode(
    messages: List[BaseMessage],
    model: BaseChatModel,
    response_model: BaseModel,
    validation_context: Optional[Dict[str, Any]] = None,
) -> Tuple[Any, TokenUsageJson]:
    """
    Asynchronous function to generate a completion for given messages using OpenAI. This function makes use of langchain's
    with_structured_output feature for generating a pydantic model.

    Args:
        messages (Union[Messages, SegmentedMessages]): The messages for which completion should be created.
        model (langchain_openai.AzureChatOpenAI): The chat model integration.
        response_model (BaseModel): The pydantic model used.
        validation_context (Optional[Dict[str, Any]]): Optional context to be used in modle validation.


    Returns:
        Tuple[Any, TokenUsageJson]: A tuple containing the pydantic object, and completion usage.
    """

    msg_handler = GetOAIMesageHandler()
    model.callbacks = model.callbacks.append(msg_handler) if model.callbacks else [msg_handler]  # type: ignore
    lang_chain_messages = ChatPromptTemplate.from_messages(messages)
    # Lang chain doesn't support pydantic's validation context, so call using JSON schema
    # See: https://github.com/langchain-ai/langchain/issues/10034
    chain = lang_chain_messages | model | JsonOutputParser(pydantic_object=response_model)

    with langchain_community.callbacks.get_openai_callback() as cb:
        try:
            # Call LLM and wait for response
            response: dict[str, Any] = await chain.ainvoke({})
            # Validate and create a pydantic model
            structured_output = response_model.model_validate(response, context=validation_context)
            # Create completion data object
            comp_usage = TokenUsageJson(
                model=model.deployment_name,  # type: ignore
                promptTokens=cb.prompt_tokens,
                completionTokens=cb.completion_tokens,
                totalTokens=cb.total_tokens,  # type: ignore
            )

        except ValidationError as ex:
            LOGGER.error(f"Error while attempting to generate structured output: {ex}")
            comp_usage = TokenUsageJson(
                model=model.deployment_name,  # type: ignore
                promptTokens=cb.prompt_tokens,
                completionTokens=cb.completion_tokens,
                totalTokens=cb.total_tokens,  # type: ignore
            )
            raise LLMRecoverableError(last_message=msg_handler.message, error_description=ex) from ex
        except Exception as ex:
            LOGGER.error(f"Error while attempting to generate structured output: {ex}")
            raise

    return structured_output, comp_usage


async def async_generate_structured(
    messages: List[BaseMessage],
    model: AzureChatOpenAI,
    response_model: BaseModel,
    validation_context: Optional[Dict[str, Any]] = None,
) -> Tuple[Any, TokenUsageJson]:
    """
    Asynchronous function to generate a completion for given messages using OpenAI. This function makes use of langchain's
    with_structured_output feature for generating a pydantic model.

    Args:
        messages (Union[Messages, SegmentedMessages]): The messages for which completion should be created.
        model (langchain_openai.AzureChatOpenAI): The chat model integration.
        response_model (BaseModel): The pydantic model used.
        validation_context (Optional[Dict[str, Any]]): Optional context to be used in modle validation.


    Returns:
        Tuple[Any, TokenUsageJson]: A tuple containing the pydantic object, and completion usage.
    """

    msg_handler = GetOAIMesageHandler()
    model.callbacks = model.callbacks.append(msg_handler) if model.callbacks else [msg_handler]  # type: ignore
    lang_chain_messages = ChatPromptTemplate.from_messages(messages)
    # Lang chain doesn't support pydantic's validation context, so call using JSON schema
    # See: https://github.com/langchain-ai/langchain/issues/10034
    json_schema = convert_to_openai_function(response_model)  # type: ignore
    chain = lang_chain_messages | model.with_structured_output(json_schema)

    with langchain_community.callbacks.get_openai_callback() as cb:
        try:
            # Call LLM and wait for response
            response: dict[str, Any] = await chain.ainvoke({})
            # Validate and create a pydantic model
            structured_output = response_model.model_validate(response, context=validation_context)
            # Create completion data object
            comp_usage = TokenUsageJson(
                model=model.deployment_name,  # type: ignore
                promptTokens=cb.prompt_tokens,
                completionTokens=cb.completion_tokens,
                totalTokens=cb.total_tokens,  # type: ignore
            )

        except ValidationError as ex:
            LOGGER.error(f"Error while attempting to generate structured output: {ex}")
            comp_usage = TokenUsageJson(
                model=model.deployment_name,  # type: ignore
                promptTokens=cb.prompt_tokens,
                completionTokens=cb.completion_tokens,
                totalTokens=cb.total_tokens,  # type: ignore
            )
            raise LLMRecoverableError(last_message=msg_handler.message, error_description=ex) from ex
        except Exception as ex:
            LOGGER.error(f"Error while attempting to generate structured output: {ex}")
            raise

    return structured_output, comp_usage


def add_completion_usage(left: TokenUsageJson | None, right: TokenUsageJson | None) -> TokenUsageJson:
    """
    Function to add two instances of CompletionUsage. This function adds the tokens from the instances to produce a new instance.

    Args:
        left (CompletionUsage): The first CompletionUsage instance.
        right (CompletionUsage): The second CompletionUsage instance.

    Returns:
        CompletionUsage: A new instance of CompletionUsage with combined tokens.
    """
    left = left or TokenUsage().to_json()
    right = right or TokenUsage().to_json()

    return TokenUsageJson(
        model=left["model"],
        promptTokens=left["promptTokens"] + right["promptTokens"],
        completionTokens=left["completionTokens"] + right["completionTokens"],
        totalTokens=left["totalTokens"] + right["totalTokens"],
    )
