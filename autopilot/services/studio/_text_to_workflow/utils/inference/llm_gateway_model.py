from typing import Any, Dict

import langchain_aws
import langchain_google_vertexai
import langchain_openai
import tempenv
from google.cloud.aiplatform_v1beta1.types import HarmCategory, SafetySetting
from pydantic import Field

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.core.constants import S2S_IPRANGE_HEADER
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.inference import llm_schema

PRODUCT_NAME = "Wingman"
UIA_PRODUCT_NAME = "UIA"
HEALING_AGENT_PRODUCT_NAME = "healing-agent"


def _get_feature_specific_headers(
    consuming_feature: llm_schema.ConsumingFeatureType,
    context: RequestContext | None = None,
    additional_headers: dict | None = None,
    **kwargs: Any,
) -> Dict[str, Any]:
    request_timeout = str(kwargs.get("request_timeout", 60))
    if not context or context.request_action_id is None:
        request_action_id = request_utils.create_request_context_id()
        if context is not None:
            context.request_action_id = request_action_id  # might need to refactor this
    else:
        request_action_id = context.request_action_id

    # setup product name
    product_name = PRODUCT_NAME

    # for QA DOM endpoints, either set up the product name to be UIA or the requesting product header
    # the requesting product header should be either UIA or healing-agent
    # for screen agent endpoints, set the product name to UIA
    if consuming_feature in [
        llm_schema.ConsumingFeatureType.QA_ELEMENT,
        llm_schema.ConsumingFeatureType.QA_SCREEN,
        llm_schema.ConsumingFeatureType.GET_SEMANTIC_DESCRIPTION,
        llm_schema.ConsumingFeatureType.CLOSE_POPUP,
        llm_schema.ConsumingFeatureType.SCREEN_AGENT,
        llm_schema.ConsumingFeatureType.SCREEN_AGENT_INFO_EXTRACTION,
    ]:
        product_name = UIA_PRODUCT_NAME
        if context and context.requesting_product_header:
            requesting_product_name = context.requesting_product_header
            if requesting_product_name == HEALING_AGENT_PRODUCT_NAME:
                product_name = context.requesting_product_header

    model_specific_headers = {}

    if consuming_feature == llm_schema.ConsumingFeatureType.SCREEN_AGENT:
        if kwargs.get("deployment_name", "").startswith("anthropic"):
            if kwargs.get("anthropic_computer_use_tool"):
                model_specific_headers["X-UiPath-LlmGateway-Anthropic-Beta"] = kwargs["anthropic_computer_use_tool"]

    config = {
        "X-UiPath-LlmGateway-TimeoutSeconds": request_timeout,
        "X-UiPath-LlmGateway-RequestingProduct": product_name,
        "X-UiPath-LlmGateway-RequestingFeature": consuming_feature.value,
        "X-UiPath-LlmGateway-ActionId": request_action_id,
        "X-UiPath-LlmGateway-ActionName": llm_schema.CONSUMING_TYPE_FRIENDLY_ACTION_NAME[consuming_feature],
        "X-UIPATH-STREAMING-ENABLED": "false",
        **model_specific_headers,
    }

    if additional_headers:
        config.update(additional_headers)

    return config


def _get_telemetry_headers(context: RequestContext | None = None) -> Dict[str, Any]:
    telemetry_headers = {}

    correlation_id = context.correlation_id if context else ""
    if correlation_id:
        telemetry_headers["x-uipath-correlation-id"] = correlation_id

    return telemetry_headers


class LLMGatewayModel(langchain_openai.AzureChatOpenAI):
    def __init__(
        self,
        feature_type: llm_schema.ConsumingFeatureType = llm_schema.ConsumingFeatureType.DEFAULT,
        context: RequestContext | None = None,
        additional_headers: dict | None = None,
        *,
        azure_endpoint: str | None = None,
        azure_key: str | None = None,
        **kwargs: Any,
    ):
        if context is None:
            context = request_utils.get_request_context()

        kwargs["model_name"] = kwargs.get("deployment_name")  # the "model_name" is used in the base class for tokenizer lookup, etc.

        open_ai_version = kwargs.get("openai_api_version", settings.OPENAI_API_VERSION)
        if settings.USE_LLM_GATEWAY:
            llm_gateway_url, additional_kwargs = _get_llmgw_details(feature_type, additional_headers, context, kwargs)

            super().__init__(
                api_version=open_ai_version,
                # api_key is a mandatory field, even if it's not used in LLM Gateway
                # removing this field will cause a validation error
                api_key="none",  # type: ignore  # pragma: allowlist secret
                azure_endpoint=llm_gateway_url,
                model_kwargs=additional_kwargs,
                **kwargs,
            )
        else:
            azure_endpoint = settings.AZURE_OPENAI_ENDPOINT if azure_endpoint is None else azure_endpoint
            azure_key = settings.AZURE_OPENAI_API_KEY if azure_key is None else azure_key
            additional_kwargs = kwargs.pop("additional_kwargs", {})

            with tempenv.TemporaryEnvironment(
                dict(
                    # these will be used by the AzureChatOpenAI __init__
                    AZURE_OPENAI_ENDPOINT=azure_endpoint,
                    AZURE_OPENAI_API_KEY=azure_key,
                )
            ):
                super().__init__(
                    api_version=open_ai_version,
                    model_kwargs=additional_kwargs,
                    **kwargs,
                )

        # o3 reasoning models do not have a default tokenizer set, so we need to force the tokenizer to the same one as gpt-4o
        # TODO: remove once langchain-openai is updated for o3 reasoning models
        if kwargs["model_name"] is not None and kwargs["model_name"].startswith("o"):
            self.tiktoken_model_name = "gpt-4o"


class LLMGatewayNormalizedModel(langchain_openai.ChatOpenAI):
    deployment_name: str = ""

    def __init__(
        self,
        feature_type: llm_schema.ConsumingFeatureType = llm_schema.ConsumingFeatureType.DEFAULT,
        additional_headers: dict | None = None,
        context: RequestContext | None = None,
        **kwargs: Any,
    ):
        # Required context, no default value
        deployment_name = kwargs.pop("deployment_name", "")
        kwargs["model_name"] = deployment_name  # the "model_name" is used in the base class for tokenizer lookup, etc.

        if not settings.USE_LLM_GATEWAY:
            raise ValueError("LLMGatewayNormalizedModel requires USE_LLM_GATEWAY to be True")

        if context is None:
            context = request_utils.get_request_context()

        llm_gateway_url, additional_kwargs = _get_llmgw_details(feature_type, additional_headers, context, kwargs, use_normalized_api=True)

        # OpenAI does not support response_mime_type, so we need to add it to the extra_body
        response_format = kwargs.pop("response_mime_type", None)

        # Initialize the parent class first
        super().__init__(
            base_url=llm_gateway_url,
            api_key="none",
            model_kwargs=additional_kwargs,
            **kwargs,
        )

        # Set deployment_name after parent initialization
        self.deployment_name = deployment_name

        if response_format:
            if self.extra_body is None:
                self.extra_body = {}
            self.extra_body["response_mime_type"] = response_format  # type: ignore

        # Pretend to use the same tokenizer as gpt-4o
        if kwargs["model_name"] is not None and not kwargs["model_name"].startswith("gpt"):
            self.tiktoken_model_name = "gpt-4o"

    def get_num_tokens(self, text: str) -> int:
        return len(self.get_token_ids(text))


class LLMGeminiModel(langchain_google_vertexai.ChatVertexAI):
    deployment_name: str = Field(default="", description="The name of the model deployment")

    def __init__(
        self,
        feature_type: llm_schema.ConsumingFeatureType = llm_schema.ConsumingFeatureType.DEFAULT,
        additional_headers: dict | None = None,
        context: RequestContext | None = None,
        **kwargs: Any,
    ):
        # Not used right now, will be used in the future when LLM Gateway supports Gemini on vendor-specific API
        deployment_name = None

        if "deployment_name" in kwargs:
            deployment_name = kwargs.pop("deployment_name")
            kwargs["model_name"] = deployment_name
        if context is None:
            context = request_utils.get_request_context()

        if settings.USE_LLM_GATEWAY:
            llm_gateway_url, additional_kwargs = _get_llmgw_details(feature_type, additional_headers, context, kwargs)

            super().__init__(
                base_url=llm_gateway_url,
                model_kwargs=additional_kwargs,
                request_parallelism=8,
                **kwargs,
            )
        else:
            super().__init__(
                request_parallelism=8,
                model=kwargs.get("model_name", "gemini-2.0-flash-001"),
                safety_settings={
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: SafetySetting.HarmBlockThreshold.BLOCK_ONLY_HIGH,
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: SafetySetting.HarmBlockThreshold.BLOCK_ONLY_HIGH,
                    HarmCategory.HARM_CATEGORY_HARASSMENT: SafetySetting.HarmBlockThreshold.BLOCK_ONLY_HIGH,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: SafetySetting.HarmBlockThreshold.BLOCK_ONLY_HIGH,
                },
                **kwargs,
            )

        # Set deployment_name after parent initialization
        if deployment_name:
            self.deployment_name = deployment_name


class LLMBedrockModel(langchain_aws.ChatBedrockConverse):
    model_name: str = Field(description="The name of the model to use for the request")

    def __init__(
        self,
        feature_type: llm_schema.ConsumingFeatureType = llm_schema.ConsumingFeatureType.DEFAULT,
        additional_headers: dict | None = None,
        context: RequestContext | None = None,
        **kwargs: Any,
    ):
        # Not used right now, will be used in the future when LLM Gateway supports AWS Bedrock (incl. Claude) on vendor-specific API
        if "deployment_name" in kwargs:
            kwargs["model_name"] = kwargs.pop("deployment_name")

        if context is None:
            context = request_utils.get_request_context()

        if settings.USE_LLM_GATEWAY:
            llm_gateway_url, additional_kwargs = _get_llmgw_details(feature_type, additional_headers, context, kwargs)

            super().__init__(
                base_url=llm_gateway_url,
                model_kwargs=additional_kwargs,
                model_name=self.model_name,
                **kwargs,
            )
        else:
            model_name = kwargs.pop("model_name")

            # Not supported by Bedrock Converse
            kwargs.pop("request_timeout", None)
            kwargs.pop("frequency_penalty", None)
            kwargs.pop("presence_penalty", None)

            # Add the region to the model name if it's not already there
            # This is required for Bedrock Converse to work.
            # This mode should never be used in production
            if not model_name.startswith("us."):
                model_name = "us." + model_name

            super().__init__(
                model=model_name,
                model_name=model_name,
                max_tokens=kwargs.pop("max_tokens", None),
                **kwargs,
            )


def _get_llmgw_details(
    feature_type: llm_schema.ConsumingFeatureType,
    additional_headers: dict | None,
    context: RequestContext | None,
    kwargs: dict,
    use_normalized_api: bool = False,
):
    if context:
        organization_id = context.organization_id
        tenant_id = context.tenant_id
        raw_jwt = context.raw_jwt
        s2s_iprange = context.s2s_iprange
    else:
        organization_id = ""
        tenant_id = ""
        raw_jwt = ""
        s2s_iprange = ""

    model_headers = _get_feature_specific_headers(feature_type, context=context, additional_headers=additional_headers, **kwargs)

    # add correlation id to request so that we can track it across services
    telemetry_headers = _get_telemetry_headers()
    model_headers.update(telemetry_headers)

    llm_gateway_url = ""
    if tenant_id:
        llm_gateway_url = "{}/{}/{}/llmgateway_/{}".format(settings.CLOUD_URL_BASE, organization_id, tenant_id, "api/" if use_normalized_api else "")
    else:
        llm_gateway_url = "{}/{}/llmgatewayorg_/{}".format(settings.CLOUD_URL_BASE, organization_id, "api/" if use_normalized_api else "")
    additional_kwargs = kwargs.pop("additional_kwargs", {})
    additional_kwargs["extra_headers"] = {
        "Authorization": raw_jwt,
        "X-UiPath-LlmGateway-NormalizedApi-ModelName": kwargs.get("model_name", ""),
        **model_headers,
    }

    if s2s_iprange:
        additional_kwargs["extra_headers"][S2S_IPRANGE_HEADER] = s2s_iprange
    return llm_gateway_url, additional_kwargs
