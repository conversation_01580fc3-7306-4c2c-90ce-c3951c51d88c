from typing import Any, Dict, List

import httpx
from langchain_core.messages import BaseMessage

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.core.constants import S2S_IPRANGE_HEADER
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.inference import llm_schema
from services.studio._text_to_workflow.utils.inference.llm_gateway_model import _get_feature_specific_headers, _get_telemetry_headers


# This is a client that does not use langchain, but instead uses httpx to directly call the LLM Gateway API.
# Only used for UI Automation, avoid using this for other purposes.
class LLMGatewayMultiModel:
    feature_type: llm_schema.ConsumingFeatureType
    model_kwargs: Dict[str, Any]
    token: str
    s2s_iprange: str | None
    llm_gateawy_url: str

    def __init__(
        self,
        feature_type: llm_schema.ConsumingFeatureType = llm_schema.ConsumingFeatureType.DEFAULT,
        context: RequestContext | None = None,
        **kwargs,
    ):
        self.feature_type = feature_type
        self.model_kwargs = kwargs
        if context is None:
            context = request_utils.get_request_context()
        self.token = context.raw_jwt or ""
        self.s2s_iprange = context.s2s_iprange or ""
        self.llm_gateawy_url = "{}/{}/{}/llmgateway_".format(settings.CLOUD_URL_BASE, context.organization_id, context.tenant_id)

    def standardize_messages(self, messages: List[BaseMessage]):
        standardized_messages = []
        for msg in messages:
            msg_role = msg.type
            if msg_role == "human":
                msg_role = "user"
            if msg_role == "ai":
                msg_role = "assistant"
            standardized_messages.append({"role": msg_role, "content": msg.content})
        return standardized_messages

    async def send_llm_request(self, base_messages: List[BaseMessage], **kwargs) -> dict:
        deployment_id: str = self.model_kwargs.get("deployment_name", "")

        model_headers = _get_feature_specific_headers(self.feature_type, **self.model_kwargs, **kwargs)
        model_headers.update(_get_telemetry_headers())

        if self.s2s_iprange:
            model_headers[S2S_IPRANGE_HEADER] = self.s2s_iprange

        if self.model_kwargs.get("standardize_messages", True):
            base_messages = self.standardize_messages(base_messages)

        body: dict = {
            "messages": base_messages,
        }

        for keys in ["tools", "reasoning", "truncation", "tool_choice", "max_tokens", "thinking", "top_p", "temperature"]:
            if keys in self.model_kwargs:
                body[keys] = self.model_kwargs[keys]

        if kwargs.get("previous_response_id"):
            body["previous_response_id"] = kwargs.get("previous_response_id")  # type: ignore

        async with httpx.AsyncClient(timeout=None) as client:
            response = await client.post(
                f"{self.llm_gateawy_url}/api/chat/completions",
                headers={
                    "Authorization": self.token,
                    "X-UiPath-LlmGateway-NormalizedApi-ModelName": deployment_id,
                    **model_headers,
                },
                json=body,
            )
        response.raise_for_status()
        return response.json()
