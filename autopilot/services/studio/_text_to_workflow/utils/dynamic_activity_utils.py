import json

GENERIC_DYNAMIC_ACTIVITY_SUFFIXES = [
    "Update_Record",
    "Create_Record",
    "Delete_Record",
    "Get_Record",
    "Insert_Record",
    "Replace_Record",
    "List_All_Records",
    "Search_Records",
]


def is_generic_dynamic_activity(activity_full_class_name: str, activity_configuration: str | None) -> bool:
    # use the ending of the fullClassName to determine if the activity is a generic dynamic activity
    if any(activity_full_class_name.endswith(suffix) for suffix in GENERIC_DYNAMIC_ACTIVITY_SUFFIXES):
        return True

    # as a fallback, deserialize the activity definition and check if the activity has an "objectName" - generic activities should not have an "objectName"
    if activity_configuration:
        activity_config = json.loads(activity_configuration)
        object_name = activity_config.get("objectName")
        return object_name is None or object_name == ""

    return False
