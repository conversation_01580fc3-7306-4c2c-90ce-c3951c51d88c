from services.studio._text_to_workflow.common.constants import DAP_NAMESPACE
from services.studio._text_to_workflow.common.schema import ActivityDefinition
from services.studio._text_to_workflow.common.typedefs_parser import parse_namespaces
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.workflow_generation.config import constants
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ProposedActivity

_logger = telemetry_utils.AppInsightsLogger()


def build_additional_type_definitions(
    activities: list[ActivityDefinition],
    extra_additional_type_definitions: str,
) -> str:
    additional_type_definitions = {}
    for activity in activities:
        try:
            typedefs_by_namespace = parse_namespaces(activity.get("additionalTypeDefinitions", ""))
            for _, typedefs in typedefs_by_namespace.items():
                for key, value in typedefs.items():
                    if key not in additional_type_definitions:
                        additional_type_definitions[key] = value["text"].replace("\r\n", "")
        except ValueError:
            _logger.exception(f"Failed to parse additional type definitions for activity:\n{activity}")
    try:
        extra_typedefs_by_namespace = parse_namespaces(extra_additional_type_definitions)
        for _, typedefs in extra_typedefs_by_namespace.items():
            for key, value in typedefs.items():
                if key not in additional_type_definitions:
                    additional_type_definitions[key] = value["text"].replace("\r\n", "")
    except ValueError:
        _logger.exception(f"Failed to parse extra additional type definitions:\n{extra_additional_type_definitions}")
    return "\n".join(additional_type_definitions.values())


def build_activity_type_definitions(
    activities: list[ActivityDefinition],
) -> str:
    activity_type_definitions = {}
    for activity in activities:
        activity_namespace = activity["namespace"]
        activity_type_definition = activity["typeDefinition"]
        activity_type_definition = _add_typedef_description(activity, activity_type_definition)
        if activity["connectorKey"] is not None and activity["connectionId"] is not None:
            typedef_lines = activity_type_definition.splitlines()
            typedef_lines.insert(3, "const bool HasConnection = true;")
            activity_type_definition = "\r\n".join(typedef_lines)
        if activity_namespace not in activity_type_definitions:
            activity_type_definitions[activity_namespace] = []
        activity_type_definitions[activity_namespace].append(activity_type_definition)
    return _organize_by_namespace(activity_type_definitions)


def build_trigger_type_definitions(triggers: list[ActivityDefinition]) -> str:
    trigger_type_definitions = {}
    for trigger in triggers:
        trigger_namespace = trigger["namespace"]
        trigger_type_definition = trigger["typeDefinition"]
        trigger_type_definition = _add_typedef_description(trigger, trigger_type_definition)
        if trigger["connectorKey"] is not None and trigger["connectionId"] is not None:
            typedef_lines = trigger_type_definition.splitlines()
            typedef_lines.insert(3, "const bool HasConnection = true;")
            trigger_type_definition = "\r\n".join(typedef_lines)
        if trigger_namespace not in trigger_type_definitions:
            trigger_type_definitions[trigger_namespace] = []
        trigger_type_definitions[trigger_namespace].append(trigger_type_definition)
    return _organize_by_namespace(trigger_type_definitions)


def _add_typedef_description(activity: ActivityDefinition, typedef: str) -> str:
    if activity["description"]:
        return f"// {activity['description']}\n{typedef}"
    return typedef


def _organize_by_namespace(type_definitions: dict[str, list[str]]) -> str:
    organized = []
    # move important namespaces at the end to bias the model towards using those.
    for namespace in constants.PREFERRED_NAMESPACES:
        if namespace in type_definitions:
            type_definitions[namespace] = type_definitions.pop(namespace)
    for namespace, namespace_type_definitions in type_definitions.items():
        type_definitions_string = "\n".join(namespace_type_definitions)
        organized.append(f"""namespace {namespace} {{\n{type_definitions_string}\n}}""")
    return "\n".join(organized)


def parse_proposed_activities(activities: list[ActivityDefinition]) -> list[ProposedActivity]:
    """Retrieves list of distinct activities and coverts them to a list of ProposedActivity objects."""
    proposed_activities = [parse_proposed_activity(activity) for activity in activities]

    return remove_duplicate_proposed_activities(proposed_activities)


def parse_proposed_activity(activity: ActivityDefinition) -> ProposedActivity:
    return ProposedActivity(
        type_full_name=activity["fullClassName"],
        display_name=activity["displayName"],
        description=activity["description"],
        id=activity["id"],
        category=activity.get("mapped_category", activity["category"]),
        connectorKey=activity["connectorKey"],
        namespace=activity["namespace"],
    )


def remove_duplicate_proposed_activities(activities_list: list[ProposedActivity]) -> list[ProposedActivity]:
    merged_activities: dict[str, ProposedActivity] = {activity["type_full_name"]: activity for activity in activities_list}
    return list(merged_activities.values())


def remove_dynamic_activity_prefix_from_name(activity_name: str) -> str:
    """
    Removes the dynamic activity namespace from the activity name.
    """
    return activity_name.removeprefix(DAP_NAMESPACE + ".")


def add_dynamic_activity_prefix_to_name(activity_name: str) -> str:
    """
    Adds the dynamic activity namespace to the activity name.
    """
    if activity_name.startswith(DAP_NAMESPACE + "."):
        return activity_name
    return DAP_NAMESPACE + "." + activity_name
