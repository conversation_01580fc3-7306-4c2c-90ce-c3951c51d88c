import asyncio
import json
import os
import subprocess
import tempfile
from functools import partial
from typing import List

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths, request_utils, telemetry_utils, uipath_cloud_platform
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time

DOTNET_CLI_PATH = paths.get_dotnet_path() / "dynamic.activities.discovery" / "UiPath.DynamicActivities.Discovery.dll"
LOGGER = telemetry_utils.AppInsightsLogger()
CACHED_CONNECTIONS = None


def load_cached_connections(cached_connections_path):
    with open(cached_connections_path, mode="r", encoding="utf-8") as json_data:
        global CACHED_CONNECTIONS
        CACHED_CONNECTIONS = json.load(json_data)


@log_execution_time("DAPCLI")
async def get_type_definitions(is_package_name, is_package_version, indexable_connections, capture_output=True, disable_cache=False):
    global CACHED_CONNECTIONS
    if not disable_cache and settings.USE_CACHED_CONNECTIONS and CACHED_CONNECTIONS is None:
        load_cached_connections(paths.get_dataset_connections_cache_path())

    if not disable_cache and settings.USE_CACHED_CONNECTIONS and CACHED_CONNECTIONS is not None:
        out_connections = []
        not_matched_indexable_connections = [item for item in indexable_connections]
        configurations: List = CACHED_CONNECTIONS["Configurations"]
        if configurations is not None:
            for index in range(len(indexable_connections) - 1, -1, -1):
                activity_connection_configuration = indexable_connections[index]["Configuration"]
                matched = False
                for generated_connection in configurations:
                    # for generated_connection in configurations:
                    if not generated_connection["IsSuccess"]:
                        continue

                    generated_connection_configuration = generated_connection["OldConfiguration"]
                    if activity_connection_configuration == generated_connection_configuration and generated_connection not in out_connections:
                        out_connections.append(generated_connection)
                        matched = True

                if matched:
                    del not_matched_indexable_connections[index]

        return {"Configurations": out_connections}

    request_context = request_utils.get_request_context()
    token = uipath_cloud_platform.get_token_from_context(request_context)

    if not token:
        return None

    tenant_id = request_context.tenant_id
    organization_id = request_context.organization_id
    s2s_iprange = request_context.s2s_iprange

    if token and token.startswith("Bearer "):
        token = token[len("Bearer ") :]

    input_json = {
        "Token": token,
        "Host": settings.CLOUD_URL_BASE,
        "OrganizationId": organization_id,
        "TenantId": tenant_id,
        "Configurations": indexable_connections,
        "PackageName": is_package_name,
        "PackageVersion": is_package_version,
        "TelemetryConnectionString": settings.APP_INSIGHTS_CONNECTION_STRING,
        "CorrelationId": request_context.correlation_id,
        "S2SIpRange": s2s_iprange,
    }

    # Create a temporary file and write JSON data to it
    connections_json = None
    input_file_path = None
    output_file_path = None
    with tempfile.NamedTemporaryFile(mode="w+", delete=False, suffix=".json") as input_temp_file:
        json.dump(input_json, input_temp_file.file)
        input_file_path = input_temp_file.name

    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as output_temp_file:
        output_file_path = output_temp_file.name

    try:
        cmd = ["dotnet", DOTNET_CLI_PATH.as_posix(), "-i", input_file_path, "-o", output_temp_file.name]
        run_subprocess = partial(subprocess.run, cmd, check=True, capture_output=capture_output)

        # Run the subprocess in a separate thread
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, run_subprocess)

        with open(output_file_path, mode="r", encoding="utf-8") as f:
            connections_json = json.load(f)

        os.remove(input_file_path)
        os.remove(output_file_path)
    except Exception as e:
        LOGGER.exception(f"Failed to fetch type definitions from the Integration Service. Error: {e}")
        print("Failed to fetch connections from the Integration Service, skipping...")
        connections_json = None
    return connections_json
