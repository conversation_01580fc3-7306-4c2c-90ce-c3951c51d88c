import asyncio
import os
import pathlib
import tempfile
from contextlib import asynccontextmanager

import aiofiles
import aiohttp
from azure.core.pipeline.transport import AioHttpTransport
from azure.storage.blob.aio import BlobServiceClient, StorageStreamDownloader
from azure.storage.blob.aio._blob_client_async import BlobClient
from azure.storage.blob.aio._container_client_async import ContainerClient

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class EmbeddingsClient:
    """
    A singleton client for managing embeddings storage and retrieval using Azure Blob Storage.

    This class provides methods to download embeddings and their associated hash from
    Azure Blob Storage. It uses a shared TCP connector to manage connections efficiently.

    Attributes:
        connector (aiohttp.TCPConnector): Shared TCP connector for managing connections.
        container_name (str): Name of the Azure Blob Storage container.
        embeddings_blob (str): Name of the blob containing embeddings.
        embeddings_hash_blob (str): Name of the blob containing the embeddings hash.
    """

    _instance = None
    connector: aiohttp.TCPConnector | None = None
    container_name = "embeddings"
    embeddings_blob = "embeddings.db"
    embeddings_hash_blob = "embeddingsHash.txt"

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EmbeddingsClient, cls).__new__(cls)
            cls._instance.connector = aiohttp.TCPConnector(limit=20)
        return cls._instance

    async def download_hash(self, download_path: pathlib.Path | str) -> bool:
        """
        Downloads the embeddings hash file from Azure Blob Storage.

        Args:
            download_path (pathlib.Path | str): The path where the hash file will be saved.

        Returns:
            bool: True if the download was successful, False otherwise.
        """
        download_path = pathlib.Path(download_path)
        return await self._download_blob(self.embeddings_hash_blob, download_path)

    async def download_embeddings(self, download_path: pathlib.Path | str) -> bool:
        """
        Downloads the embeddings file from Azure Blob Storage.

        Args:
            download_path (pathlib.Path | str): The path where the embeddings file will be saved.

        Returns:
            bool: True if the download was successful, False otherwise.
        """
        download_path = pathlib.Path(download_path)
        return await self._download_blob(self.embeddings_blob, download_path)

    @asynccontextmanager
    async def _create_shared_transport(self):
        """
        Creates a shared transport using the class connector.

        This context manager creates an aiohttp ClientSession and AioHttpTransport
        using the shared connector. It ensures that the session is properly closed
        after use.

        Yields:
            AioHttpTransport: The shared transport object.
        """
        # Setting owners to false so connector won't be closed by session, transport or clients
        session = aiohttp.ClientSession(connector=self.connector, connector_owner=False)
        transport = AioHttpTransport(session=session, session_owner=False)

        try:
            yield transport
        finally:
            await session.close()

    async def _write_blob_chunks(self, stream: StorageStreamDownloader[bytes], file_path: pathlib.Path) -> None:
        """
        Writes the chunks of a blob to a file.

        Args:
            stream (StorageStreamDownloader[bytes]): The stream of blob chunks.
            file_path (pathlib.Path): The path where the file will be written.
        """
        async with aiofiles.open(file_path, mode="wb") as file:
            async for chunk in stream.chunks():
                await file.write(chunk)

    async def _download_blob(self, blob_name: str, download_path: pathlib.Path) -> bool:
        """
        Downloads a blob from Azure Blob Storage.

        Args:
            blob_name (str): The name of the blob to download.
            download_path (pathlib.Path): The path where the blob will be saved.

        Returns:
            bool: True if the download was successful, False otherwise.
        """
        try:
            if not self.connector:
                LOGGER.exception("Connector is not initialized. Embeddings will not be downloaded.")
                return False

            async with self._create_shared_transport() as shared_transport:
                blob_service_client: BlobServiceClient = BlobServiceClient.from_connection_string(
                    settings.EMBEDDINGS_STORAGE_ACCOUNT_CONNECTION_STRING, transport=shared_transport
                )
                container_client: ContainerClient = blob_service_client.get_container_client(self.container_name)
                blob_client: BlobClient = container_client.get_blob_client(blob_name)
                file_path: pathlib.Path = download_path / blob_name

                download_stream: StorageStreamDownloader[bytes] = await blob_client.download_blob()
                await self._write_blob_chunks(download_stream, file_path)

        except Exception as e:
            LOGGER.exception(f"Failed to download {blob_name} from Azure Blob Storage. Error: {e}")
            return False

        return True


if __name__ == "__main__":
    if not settings.EMBEDDINGS_STORAGE_ACCOUNT_CONNECTION_STRING:
        print("Environment variable isn't set. Using hardcoded default.")
        settings.EMBEDDINGS_STORAGE_ACCOUNT_CONNECTION_STRING = "#test-key-here#"

    async def _test():
        embeddings_client = EmbeddingsClient()
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                print("Temporary directory created at:", temp_dir)

                await embeddings_client.download_hash(pathlib.Path(temp_dir))
                await embeddings_client.download_embeddings(pathlib.Path(temp_dir))

                print("Contents of the temp directory:", os.listdir(temp_dir))
        except Exception as ex:
            print(f"Exception: {ex}")

    asyncio.run(_test())
