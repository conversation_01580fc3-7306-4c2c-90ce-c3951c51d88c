import argparse
import json
import os
import zipfile

test_account_ids = ["0383908f-666b-4750-a6e4-f32940f2b4e5", "822742ea-9e6a-4023-a460-1c9f9e63fe32"]


def unzip_file(zip_path, output_dir):
    if zip_path and os.path.exists(zip_path):
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            zip_ref.extractall(output_dir)
    else:
        print(f"Zip file {zip_path} not found.")


def process_metadata(metadata_path, input_dir, feature_type):
    with open(metadata_path, "r") as file:
        metadata = json.load(file)

        # skip test accounts
        if metadata["account_id"] in test_account_ids:
            return

        # only extract the desired feature
        # the feature type is in the source field of the metadata
        if feature_type in metadata["source"]:
            # destination is the full storage account destination path
            # we only need the zip file name for the downloaded data
            zip_file = metadata["destination"].split("/")[-1]
            if zip_file:
                zip_path = os.path.join(input_dir, zip_file)
                return zip_path
    return None


def main(input_dir, output_dir, feature_type):
    print("Reading all metadata files...")
    feature_specific_zip_files = []
    for file in os.listdir(input_dir):
        if file.endswith(".json"):
            metadata_path = os.path.join(input_dir, file)
            metadata_result = process_metadata(metadata_path, input_dir, feature_type)
            if metadata_result:
                feature_specific_zip_files.append(metadata_result)

    print(f"Extracting {len(feature_specific_zip_files)} files")
    for zip_file in feature_specific_zip_files:
        unzip_file(zip_file, output_dir)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Unzip files based on metadata.")
    parser.add_argument("input_dir", type=str, help="Input directory containing zip files and metadata.")
    parser.add_argument("output_dir", type=str, help="Output directory to unzip files.")
    parser.add_argument("feature_type", type=str, help="The feature for which the data needs to be extracted.")
    args = parser.parse_args()

    input_dir = args.input_dir
    output_dir = args.output_dir
    feature_type = args.feature_type

    main(input_dir, output_dir, feature_type)
