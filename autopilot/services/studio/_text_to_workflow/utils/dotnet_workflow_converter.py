import asyncio
import json
import os
import subprocess
import tempfile
import time
from functools import partial

from services.studio._text_to_workflow.common import schema
from services.studio._text_to_workflow.utils import paths

DOTNET_CLI_PATH = {
    "Portable": paths.get_dotnet_path() / "workflow.converter.net8" / "UiPath.LanguageModel.WorkflowConverter.dll",
    "Windows": paths.get_dotnet_path() / "workflow.converter.net8.windows" / "UiPath.LanguageModel.WorkflowConverter.dll",
    "Legacy": paths.get_dotnet_path() / "workflow.converter.net461" / "UiPath.LanguageModel.WorkflowConverter.dll",
}


async def convert_yaml2workflow(
    input_path: str,
    output_path: str | None = None,
    target_framework: schema.TargetFramework = "Portable",
    capture_output: bool = True,
) -> dict:
    dotnet_cli_path = DOTNET_CLI_PATH[target_framework].as_posix()
    output_file_path = output_path
    if output_file_path is None:
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as output_temp_file:
            output_file_path = output_temp_file.name
    cmd = ["dotnet", dotnet_cli_path, "yaml2workflow", "-m", "-v", "-i", input_path, "-o", output_file_path]
    run_subprocess = partial(subprocess.run, cmd, check=True, capture_output=capture_output)

    # Run the subprocess in a separate thread
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, run_subprocess)
    with open(output_file_path, mode="r", encoding="utf-8-sig") as f:
        output = json.load(f)
    if output_path is None:
        os.remove(output_file_path)
    return output


def convert_workflow2yaml(
    project_json_path: str,
    xaml_path: str,
    output_path: str | None = None,
    typedef_output_path: str | None = None,
    target_framework: schema.TargetFramework = "Portable",
    capture_output: bool = True,
) -> dict:
    start_time = time.time()
    print(f"Starting conversion for {xaml_path}")
    dotnet_cli_path = DOTNET_CLI_PATH[target_framework].as_posix()
    output_file_path = output_path
    if output_file_path is None:
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as output_temp_file:
            output_file_path = output_temp_file.name
    typedef_output_file_path = typedef_output_path
    if typedef_output_file_path is None:
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as output_temp_file:
            typedef_output_file_path = output_temp_file.name
    cmd = ["dotnet", dotnet_cli_path, "workflow2yaml", "-s", "-p", project_json_path, "-i", xaml_path, "-o", output_file_path, "-d", typedef_output_file_path]
    # print(" ".join(cmd))
    try:
        subprocess.run(cmd, check=True, capture_output=capture_output)
        with open(output_file_path, mode="r", encoding="utf-8-sig") as f:
            output = json.load(f)
        if output_path is None:
            os.remove(output_file_path)
    except subprocess.CalledProcessError as e:
        stdout = ""
        if e.stdout is not None:
            stdout = e.stdout.decode("utf-8-sig")
        stderr = ""
        if e.stderr is not None:
            stderr = e.stderr.decode("utf-8-sig")
        output = {"Success": False, "Model": "", "Errors": [stdout, stderr]}
        if output_path is not None:
            with open(output_file_path, mode="w", encoding="utf-8-sig") as f:
                json.dump(output, f, indent=2)
    print(f"Conversion done in {time.time() - start_time} seconds for {xaml_path}")
    return output
