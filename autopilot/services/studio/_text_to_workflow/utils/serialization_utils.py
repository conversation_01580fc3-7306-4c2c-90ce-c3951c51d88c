import dataclasses
import enum
import pathlib
import typing as t
from dataclasses import asdict

import numpy as np
import pydantic

from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import APTEDNode


def sanitize_object_for_yaml_dump(obj: t.Any):
    """Sanitizes an object for YAML dump."""
    if isinstance(obj, (list, tuple, set, frozenset)):
        return [sanitize_object_for_yaml_dump(item) for item in obj]
    if isinstance(obj, dict):
        return {key: sanitize_object_for_yaml_dump(value) for key, value in obj.items()}
    if isinstance(obj, APTEDNode):
        return repr(obj)
    if isinstance(obj, enum.Enum):
        return obj.value
    if isinstance(obj, (np.ndarray, np.integer, np.floating, np.bool_)):
        return obj.tolist()
    if isinstance(obj, pathlib.Path):
        return obj.as_posix()
    if dataclasses.is_dataclass(obj):
        return sanitize_object_for_yaml_dump(asdict(obj))  # type: ignore
    if isinstance(obj, pydantic.BaseModel):
        return sanitize_object_for_yaml_dump(obj.model_dump())
    return obj
