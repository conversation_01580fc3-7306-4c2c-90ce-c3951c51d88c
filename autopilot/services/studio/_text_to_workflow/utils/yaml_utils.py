import collections
import pathlib
import re

import typing_extensions as t
import yaml
from yaml import YAM<PERSON><PERSON>r  # noqa: F401 - exposing this

from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

NON_ESCAPABLE_YAML_SYMBOLS = ["{}", "[]", "null"]
MULTILINE_SYMBOLS = ["|", "|-"]

# matches any single quote that is not doubled, not escaped and not at the end of the string
REGEX_UNESCAPED_SINGLE_QUOTE_INSIDE_VALUE = re.compile(r"(?<!^)(?<!\\)(?<!')\'(?!')(?!$)")
# matches any single quote that is not escaped and not at the end of the string
REGEX_UNESCAPED_QUOTE_INSIDE_VALUE = re.compile(r"(?<!^)(?<!\\)'(?!$)")


LOGGER = AppInsightsLogger()


class multiline_str(str):
    pass


def multiline_str_presenter(dumper, data):
    return dumper.represent_scalar("tag:yaml.org,2002:str", str(data), style="|")


yaml.add_representer(multiline_str, multiline_str_presenter)
yaml.representer.SafeRepresenter.add_representer(multiline_str, multiline_str_presenter)


@t.overload
def yaml_dump(data: t.Any, path_or_stream: pathlib.Path | t.TextIO, **kwargs) -> None: ...


@t.overload
def yaml_dump(data: t.Any, **kwargs) -> str: ...


def yaml_dump(data: t.Any, path_or_stream: pathlib.Path | t.TextIO | None = None, **kwargs) -> str | None:
    if isinstance(path_or_stream, pathlib.Path):
        with path_or_stream.open("w", encoding="utf-8-sig") as fd:
            return yaml_dump(data, fd, **kwargs)
    return yaml.dump(data, path_or_stream, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper, **kwargs)


def yaml_load(path_or_stream: pathlib.Path | str | t.TextIO | multiline_str, load_multiline=False, merge_conflicting_keys=False, **kwargs) -> t.Any:
    if isinstance(path_or_stream, pathlib.Path):
        with path_or_stream.open(encoding="utf-8-sig") as fd:
            return yaml_load(fd, load_multiline=load_multiline, merge_conflicting_keys=merge_conflicting_keys, **kwargs)
    if isinstance(path_or_stream, multiline_str):
        path_or_stream = str(path_or_stream)

    match (load_multiline, merge_conflicting_keys):
        case (True, True):
            loader = MultilineListMergingLoader
        case (True, False):
            loader = MultilineLoader
        case (False, True):
            loader = ListMergingLoader
        case (False, False):
            loader = ConflictAwareLoader

    return yaml.load(path_or_stream, Loader=loader, **kwargs)


def enforce_single_quotes_on_yaml_values(raw_yaml: str, allow_double_quoted_values=False) -> str:
    """
    Enforces single quotes on the values of the YAML string, which helps parse the YAML successfully.
    Preserves multiline strings marked with "|" by tracking indentation levels.
    """
    lines = raw_yaml.splitlines()
    lines_fixed = []

    # Track multiline state
    in_multiline = False
    base_indent = 0

    for line in lines:
        # Calculate current line indentation
        current_indent = len(line) - len(line.lstrip())

        # If we're in a multiline block and indentation drops below or equal to the base level, exit multiline mode
        if in_multiline and current_indent <= base_indent:
            in_multiline = False

        # If we're in a multiline block, just add the line unchanged
        if in_multiline:
            lines_fixed.append(line)
            continue

        parts = line.split(": ", 1)
        if len(parts) <= 1:
            # skip any lines that don't have a key (e.g. multiline strings)
            lines_fixed.append(line)
            continue

        value = parts[1].strip()
        # Check if this is the start of a multiline string with pipe character
        if value in MULTILINE_SYMBOLS:
            in_multiline = True
            base_indent = current_indent
            lines_fixed.append(line)
            continue

        if (value in NON_ESCAPABLE_YAML_SYMBOLS) or (allow_double_quoted_values and value.startswith('"') and value.endswith('"')):
            # if the value is a constant
            # OR if the value part is a double quoted constant value, we can safely add it to the lines_fixed list(this does not cover all possible edge cases, but it's good enough atm)
            lines_fixed.append(line)
            continue

        lines_fixed.append(_build_escaped_yaml_line(parts[0], value))
    process_str = "\n".join(lines_fixed)
    return process_str


def _build_escaped_yaml_line(key: str, value: str) -> str:
    has_start_quote = value.startswith("'")
    has_end_quote = value.endswith("'")

    if has_start_quote and has_end_quote:
        # double any quote that is not doubled, not escaped and inside the string to keep the value valid
        # since the model surrounded value with single quotes, we assume doubled quotes are on purpose
        # from '\'aa'a''a' => '\'aa''a''a'
        escaped_value = REGEX_UNESCAPED_SINGLE_QUOTE_INSIDE_VALUE.sub("''", value)

        escaped_value = replace_escaped_single_quotes(escaped_value)

        # value part is already valid, so we can return the line as is
        return f"{key}: {escaped_value}"

    # double any quote that is not escaped and inside the string to keep the value valid
    # from '\'aa'aa' => '\'aa''aa'
    escaped_value = REGEX_UNESCAPED_QUOTE_INSIDE_VALUE.sub("''", value)

    escaped_value = replace_escaped_single_quotes(escaped_value)

    if not has_start_quote:
        escaped_value = "'" + escaped_value

    if not has_end_quote:
        escaped_value += "'"

    # reconstruct the line with the fixed value part
    line = f"{key}: {escaped_value}"
    return line


def replace_escaped_single_quotes(value: str) -> str:
    """
    Replace escaped single quotes with 2 single quotes to keep the value valid
    from '\'aa''aa' => '''aa''aa'
    """
    return value.replace("\\'", "''")


def escape_quotes(s):
    s = re.sub(r"(?<!')'(?!')", "''", s)
    s = re.sub(r'(?<!")"(?!")', '""', s)
    return s.strip()


def unescape_quotes(s):
    s = re.sub(r"(?<!')''(?!')", "'", s)
    s = re.sub(r'(?<!")""(?!")', '"', s)
    return s.strip()


def convert_to_multiline_str(data, path):
    """
    Recursively converts the value at the specified path in a nested data structure to a MultilineStr.

    Args:
        data (dict or list): The nested data structure (dictionary or list) to traverse.
        path (str): The dot-separated path to the value that needs to be converted.
                    wildcard "*" can be used to match any key. missing index means apply to all indexes.

    Returns:
        None: The function modifies the input data structure in place.

    Example:
        data = {
            "key1": "value1",
            "key2": {
                "subkey1": "subvalue1",
                "subkey2": ["item1", "item2"]
            }
        }
        path = "key2.subkey2[1]"
        convert_to_multiline_str(data, path)
        # data will be modified to:
        # {
        #     "key1": "value1",
        #     "key2": {
        #         "subkey1": "subvalue1",
        #         "subkey2": ["item1", multiline_str("item2")]
        #     }
        # }
    """

    def startswith(path_parts, current_parts):
        for ppath, pcurrent in zip(path_parts, current_parts, strict=False):
            if ppath != pcurrent and ppath != "*":
                return False
        return True

    def matches(path_parts, current_parts):
        return len(path_parts) == len(current_parts) and startswith(path_parts, current_parts)

    def convert(data, path_parts, current_parts):
        if not startswith(path_parts, current_parts):
            return
        if isinstance(data, dict):
            for key, value in data.items():
                if not current_parts:
                    new_current_parts = [key]
                else:
                    new_current_parts = current_parts + [".", key]
                if matches(path_parts, new_current_parts) and isinstance(value, str):
                    data[key] = multiline_str(value)
                else:
                    convert(value, path_parts, new_current_parts)
        elif isinstance(data, list):
            new_current_parts2 = current_parts + ["[", "]"]
            for i, item in enumerate(data):
                new_current_parts1 = current_parts + ["[", str(i), "]"]
                if matches(path_parts, new_current_parts1) and isinstance(item, str):
                    data[i] = multiline_str(item)
                elif matches(path_parts, new_current_parts2) and isinstance(item, str):
                    data[i] = multiline_str(item)
                else:
                    if startswith(path_parts, new_current_parts1):
                        convert(item, path_parts, new_current_parts1)
                    elif startswith(path_parts, new_current_parts2):
                        convert(item, path_parts, new_current_parts2)

    if not path:
        return

    path_parts = re.split(r"(\.|\[|\])", path)
    path_parts = [part for part in path_parts if part]
    convert(data, path_parts, [])


class ConflictAwareLoader(yaml.CSafeLoader):
    def construct_mapping(self, node, deep=False):
        mapping = super().construct_mapping(node, deep=deep)
        used_keys = set()
        for key_node, _ in reversed(node.value):
            key = self.construct_object(key_node, deep=deep)  # cached, no overhead for reconstruction
            if key in used_keys:
                LOGGER.error(f'yaml_error: Duplicate keys found for: "{key}"')
        return mapping


class ListMergingLoader(ConflictAwareLoader):
    """
    A custom YAML loader that merges lists when duplicate keys are encountered.

    For example, the following YAML:
    a:
    - 1
    - 2
    a:
    - 3
    - 4

    will be loaded as:
    {
        "a": [1, 2, 3, 4]
    }

    This version reuses the original mapping construction, but adds the discarded values to the final mapping.
    """

    def construct_mapping(self, node, deep=False):
        # we use deep=True here to ensure that the value is eagerly constructed and we are not getting only a reference to the node
        mapping = super().construct_mapping(node, deep=True)

        discarded_lists_mapping = collections.defaultdict(list)
        used_keys = set()
        for key_node, value_node in reversed(node.value):
            # these seem to be cached; no overhead for reconstruction
            key = self.construct_object(key_node, deep=deep)
            if key not in used_keys:
                used_keys.add(key)
                continue

            # this value got discarded during the original mapping construction
            value = self.construct_object(value_node, deep=True)

            if not isinstance(value, list) or not isinstance(mapping[key], list):
                LOGGER.error(f'yaml_error: Duplicate keys found for: "{key}"')

            # lazy add the prior values to the final mapping
            # we do this to avoid quadratic complexity on concatenating lists)
            discarded_lists_mapping[key].append(value)

        # apply the discarded values to the final mapping
        for key, discarded_lists in discarded_lists_mapping.items():
            final_value = []
            for discarded_list in reversed(discarded_lists):
                final_value.extend(discarded_list)
            final_value.extend(mapping[key])
            mapping[key] = final_value
        return mapping


class MultilineLoader(ConflictAwareLoader):
    def construct_scalar(self, node):
        value = super().construct_scalar(node)
        return multiline_str(value) if node.style == "|" else value


class MultilineListMergingLoader(MultilineLoader, ListMergingLoader):
    pass
