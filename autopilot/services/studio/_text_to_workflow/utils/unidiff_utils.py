import difflib
import io
import re

import <PERSON><PERSON>htein
import unidiff
import unidiff.errors

from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixFlairType
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import EditPatchStructureError

LOGGER = AppInsightsLogger()

LINE_NR_ANNOTATION_RE = re.compile(r"^\s*\d{1,3}\|")  # update this according to the line number annotation format below
LINE_NR_ANNOTATION_AND_CONTENT_RE = re.compile(r"^\s*(\d{1,3})\|(.*)", flags=re.DOTALL)
SEARCH_AND_REPLACE_HUNK_RE_STRICT_MATCHING = re.compile(r"<<<<<<< SEARCH\n(?P<search>.*?)=======\n(?P<replace>.*?)>>>>>>> REPLACE", flags=re.DOTALL)
SEARCH_AND_REPLACE_HUNK_RE_SOFT_MATCHING = re.compile(
    r"<{5,9}\s*SEARCH[\t ]*\n"
    r"(?P<search>.*?)"
    r"[\t ]*={5,9}[\t ]*\n"
    r"(?P<replace>.*?)"
    r">{5,9}\s*REPLACE",
    flags=re.DOTALL | re.IGNORECASE,
)


class LineType:
    ADDED = "+"
    REMOVED = "-"
    CONTEXT = "|"


class Line:
    content: str
    type: str

    def __init__(self, content: str, _type: str):
        self.content = content
        self.type = _type


class Hunk:
    lines: list[Line]

    def __init__(self, content: str):
        # lines = content.splitlines(keepends=True)
        lines = content.splitlines()
        self.lines = [Line(content=line[1:], _type=self._get_type(line)) for line in lines]

    def _get_type(self, line: str):
        if line.startswith(" "):
            return LineType.CONTEXT
        return line[0]

    def get_matching_lines(self):
        # return [line.content or "\n" for line in self.lines if line.type in (LineType.CONTEXT, LineType.REMOVED)]
        return [line.content for line in self.lines if line.type in (LineType.CONTEXT, LineType.REMOVED)]

    def get_added_lines(self):
        # return [line.content or "\n" for line in self.lines if line.type in (LineType.CONTEXT, LineType.ADDED)]
        return [line.content for line in self.lines if line.type in (LineType.CONTEXT, LineType.ADDED)]

    def get_hunk(self):
        return "\n".join(line.type + line.content for line in self.lines)


class SoftUniDiff:
    """
    Handles soft unidiff patches of the format:
    ```patch
    @@ ... @@
    |context line before hunk
    -removed line
    +added line
    |context line after hunk
    ```
    """

    hunks: list[Hunk]

    def __init__(self, content: str):
        self._parse(content)

    def _parse(self, content: str):
        _header, *hunks = re.split(r"(^@@ .* @@\s*\n)", content, flags=re.MULTILINE)
        # print("-" * 30)
        # print(_header)
        # print("-" * 30)
        # for i in range(0, len(hunks), 2):
        #     print(hunks[i])
        #     print(hunks[i + 1])
        #     print("-" * 30)
        _hunk_headers, hunks = hunks[0::2], hunks[1::2]
        self.hunks = [Hunk(hunk) for hunk in hunks]
        # for hunk in self.hunks:
        #     print("-" * 30)
        #     print("".join(hunk.get_matching_lines()))
        #     print("-" * 30)
        #     for line in hunk.get_added_lines():
        #         print(repr(line))
        #     print("-" * 30)

    def patch(self, content: str):
        # original_lines = content.splitlines(True)
        original_lines = content.splitlines()
        if content.endswith("\n"):  # .splitlines does not make a difference between "a\n" and "a"
            original_lines.append("")

        remaining_hunks = []
        for i, hunk in enumerate(self.hunks):
            matching_lines = hunk.get_matching_lines()
            for start in range(len(original_lines) - len(matching_lines) + 1):
                if original_lines[start : start + len(matching_lines)] == matching_lines:
                    break  # TODO: instead of breaking, should we check for ambiguities? (i.e. multiple locations being matched by the hunk)
            else:
                LOGGER.warning(f"Could not find exact matching lines from hunk {i} in content")
                # print(hunk.get_hunk())
                remaining_hunks.append(hunk)
                continue
            # TODO: maybe we should defer the replacement to the end, to avoid altering the original_lines list
            # this might cause issues when a hunk might modify the context of another hunk (if that's the case, I expect hunks to be already merged by the diff engine)
            original_lines[start : start + len(matching_lines)] = hunk.get_added_lines()

        _remaining_hunks = remaining_hunks
        remaining_hunks = []
        for hunk in _remaining_hunks:
            # print("Attempting indentationless matching")
            matching_lines = hunk.get_matching_lines()
            matching_lines_lstripped = [line.lstrip() for line in matching_lines]
            for start in range(len(original_lines) - len(matching_lines_lstripped) + 1):
                if [line.lstrip() for line in original_lines[start : start + len(matching_lines)]] == matching_lines_lstripped:
                    break
            else:
                LOGGER.warning("Could not find matching lines from hunk in content")
                # print(hunk.get_hunk())
                remaining_hunks.append(hunk)
                continue

            # stabilize indentation for added lines to be "in line" with original lines
            lines_to_add = hunk.get_added_lines()
            to_indent_by = len(original_lines[start]) - len(matching_lines[0])
            if to_indent_by > 0:
                lines_to_add = [" " * to_indent_by + line for line in lines_to_add]
            elif to_indent_by < 0:
                lines_to_add = [line[-to_indent_by:] for line in lines_to_add]

            original_lines[start : start + len(matching_lines)] = lines_to_add

        for hunk in remaining_hunks:
            # print("Attempting fuzzy levenshtein matching")
            matching_lines = hunk.get_matching_lines()
            diff_key = "\n".join([line.lstrip() for line in matching_lines])
            if len(diff_key) > 1e6:  # upper bound due to quadratic levenshtein issues
                LOGGER.warning("Diff too big to process with levenshtein")
                continue

            for start in range(len(original_lines) - len(matching_lines) + 1):
                original_key = "\n".join([line.lstrip() for line in original_lines[start : start + len(matching_lines)]])
                levenshtein_tolerance = 1 if len(diff_key) < 10 else len(diff_key) // 10
                if Levenshtein.distance(diff_key, original_key) <= levenshtein_tolerance:
                    break  # tolerance 10%
            else:
                LOGGER.warning("Could not find matching lines from hunk in content with fuzzy matching")
                # print(hunk.get_hunk())
                continue

            lines_to_add = hunk.get_added_lines()
            to_indent_by = len(original_lines[start]) - len(matching_lines[0])  # assume first line is representative of the indentation
            if to_indent_by > 0:
                lines_to_add = [" " * to_indent_by + line for line in lines_to_add]
            elif to_indent_by < 0:
                lines_to_add = [line[-to_indent_by:] for line in lines_to_add]

            original_lines[start : start + len(matching_lines)] = lines_to_add
        # return "".join(original_lines)
        return "\n".join(original_lines)

    def get_diff(self):
        ret = []
        for hunk in self.hunks:
            ret.append("@@ ... @@")
            ret.extend(f"{line.type}{line.content}" for line in hunk.lines)
        return "\n".join(ret)


def apply_soft_unidiff(content: str, diff: str) -> str:
    return SoftUniDiff(diff).patch(content)


def get_unidiff(a: str, b: str, span: int | None = None, **kwargs) -> str:
    kwargs = {"n": span} if span is not None else {}
    return "\n".join(s for s in difflib.unified_diff(a.splitlines(), b.splitlines(), lineterm="", fromfile="diff.txt", tofile="diff.txt", **kwargs))


def get_soft_unidiff(a: str, b: str, span: int = 1) -> str:
    diff = get_unidiff(a, b, span)
    return SoftUniDiff(diff).get_diff()


def apply_standard_unidiff(content: str, diff: str) -> str:
    original_lines = content.splitlines(True)
    patch = unidiff.PatchSet(io.StringIO(diff))
    offset = 0
    for patched_file in patch:
        for hunk in patched_file:
            start_index = offset + hunk.source_start - 1 + (hunk.source_length == 0)
            end_index = start_index + hunk.source_length
            # line.value seems to come with trailing newlines, except when there's a last line added in the unidiff
            _altered_chunk = [line.value if line.value else "\n" for line in hunk if line.is_context or line.is_added]
            # print(f"{start_index=}, {end_index=} {offset=}")
            # print(f"Altering lines {hunk.source_start} to {hunk.source_start + hunk.source_length}")
            # print("-" * 30)
            # print("".join(original_lines[start_index:end_index]))
            # print("-" * 30)
            # print("".join(_altered_chunk))
            # print("-" * 30)
            # print(_altered_chunk)
            # print("-" * 30)
            # TODO: can include validity check on the source to match the removed lines or the context lines
            original_lines[start_index:end_index] = _altered_chunk
            offset += len(_altered_chunk) - (end_index - start_index)
    return "".join(original_lines)


class MergeConflict:
    """Handles merge conflict style patches of the format:
    <<<<<<< SEARCH
    <original_lines>
    =======
    <replacement_lines>
    >>>>>>> REPLACE

    `original_lines` can be either a line range, content, or line annotated content, depending on the flair.
    `replacement_lines` is always content.
    """

    def __init__(self, content: str, flair: WorkflowFixFlairType = WorkflowFixFlairType.MergeConflict):
        self.flair = flair
        self.raw_patch = content
        self.blocks = self._parse(content)

    def _parse(self, content: str):
        hunks = SEARCH_AND_REPLACE_HUNK_RE_SOFT_MATCHING.findall(content)
        if not hunks:
            LOGGER.warning("No merge conflict blocks found in content")
            return []

        if remaining_content := SEARCH_AND_REPLACE_HUNK_RE_SOFT_MATCHING.sub("", content).strip():
            LOGGER.warning(f"Remaining content after parsing merge conflict blocks:\n{remaining_content}")

        blocks = []
        for search_block, replace_block in hunks:
            original_lines, replacement_lines = search_block.splitlines(), replace_block.splitlines()
            if self.flair == WorkflowFixFlairType.MergeConflictWithLines:
                original_lines = "\n".join(original_lines).strip().split("-")
                if len(original_lines) != 2:
                    LOGGER.warning("Malformed line range in block:\n{}".format(search_block))
                    continue
                start, end = map(int, map(str.strip, original_lines))
                original_lines = [start, end]

            blocks.append({"original": original_lines, "replacement": replacement_lines})

        return blocks

    def patch(self, content: str) -> tuple[str, list[EditPatchStructureError]]:
        original_lines = content.splitlines()
        if content.endswith("\n"):  # no difference between "a" and "a\n"
            original_lines.append("")

        replacements = []
        errors = []

        for block in self.blocks:
            original_block = block["original"]
            replacement_block = block["replacement"]

            # if we are using line numbers, we can directly replace the lines
            if self.flair == WorkflowFixFlairType.MergeConflictWithLines:
                start, end = original_block
                if start - 1 > len(original_lines) or end > len(original_lines):
                    error_msg = f"Line range out of bounds: {start}-{end} for lines: {len(original_lines)}"
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                    continue
                replacements.append((start - 1, end, replacement_block))  # 1-indexed line numbers (inclusive, inclusive)
                continue
            # from here on we assume the original block is a list of lines

            # remove any prepended line numbers from the replacement block
            replacement_block = [LINE_NR_ANNOTATION_RE.sub("", line) for line in replacement_block]

            if not original_block or original_block == [""]:  # in case of empty original block, append content as is
                replacements.append((len(original_lines), len(original_lines), replacement_block))
            elif self.flair == WorkflowFixFlairType.MergeHybrid and any(line == "..." for line in original_block):
                if original_block.count("...") != 1:
                    error_msg = "Found multiple ellipses in block:\n{}".format("\n".join(original_block))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                    continue
                _elipsis_positions = [i for i, line in enumerate(original_block) if line == "..."]
                _index_of_first_ellipsis = _elipsis_positions[0]
                _index_of_last_ellipsis = _elipsis_positions[-1]
                prefix_lines = original_block[:_index_of_first_ellipsis]
                suffix_lines = original_block[_index_of_last_ellipsis + 1 :]

                prefix_matches = [LINE_NR_ANNOTATION_AND_CONTENT_RE.match(line) for line in prefix_lines]
                suffix_matches = [LINE_NR_ANNOTATION_AND_CONTENT_RE.match(line) for line in suffix_lines]
                if any(match is None for match in prefix_matches):
                    error_msg = "Malformed line number annotation in prefix block:\n{}".format("\n".join(prefix_lines))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                if any(match is None for match in suffix_matches):
                    error_msg = "Malformed line number annotation in suffix block:\n{}".format("\n".join(suffix_lines))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))

                prefix_line_numbers = [int(match.group(1)) for match in prefix_matches]
                suffix_line_numbers = [int(match.group(1)) for match in suffix_matches]
                prefix_lines = [match.group(2) for match in prefix_matches]
                suffix_lines = [match.group(2) for match in suffix_matches]

                # Find the location in the prefix content
                prefix_candidates = []
                for start in range(len(original_lines) - len(prefix_lines) + 1):
                    if original_lines[start : start + len(prefix_lines)] == prefix_lines:
                        prefix_candidates.append(start)

                if not prefix_candidates:
                    error_msg = "Could not find exact match for prefix block:\n{}".format("\n".join(prefix_lines))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                    continue

                prefix_candidate = min(prefix_candidates, key=lambda c: abs(c - min(prefix_line_numbers)))

                # Find the location in the postfix content
                suffix_candidates = []
                for start in range(prefix_candidate + len(prefix_lines), len(original_lines) - len(suffix_lines) + 1):
                    if original_lines[start : start + len(suffix_lines)] == suffix_lines:
                        suffix_candidates.append(start)

                if not suffix_candidates:
                    error_msg = "Could not find exact match for suffix block:\n{}".format("\n".join(suffix_lines))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                    continue

                suffix_candidate = min(suffix_candidates, key=lambda c: abs(c - min(suffix_line_numbers)))

                replacements.append((prefix_candidate, suffix_candidate + len(suffix_lines), replacement_block))
            else:
                original_block_line_numbers = []
                if self.flair == WorkflowFixFlairType.MergeHybrid:
                    matches = [LINE_NR_ANNOTATION_AND_CONTENT_RE.match(line) for line in original_block]
                    if any(match is None for match in matches):
                        error_msg = "Malformed line number annotation in block:\n{}".format("\n".join(original_block))
                        LOGGER.warning(error_msg)
                        errors.append(EditPatchStructureError(error=error_msg))
                        continue
                    original_block_line_numbers = [int(match.group(1)) for match in matches]
                    original_block = [match.group(2) for match in matches]

                # Find the location in the original content
                candidates = []
                for start in range(len(original_lines) - len(original_block) + 1):
                    if original_lines[start : start + len(original_block)] == original_block:
                        candidates.append(start)

                if not candidates:
                    error_msg = "Could not find exact match for block:\n{}".format("\n".join(original_block))
                    LOGGER.warning(error_msg)
                    errors.append(EditPatchStructureError(error=error_msg))
                    continue

                if self.flair == WorkflowFixFlairType.MergeHybrid:
                    best_start = min(candidates, key=lambda c: abs(c - min(original_block_line_numbers)))
                    replacements.append((best_start, best_start + len(original_block), replacement_block))
                else:
                    if len(candidates) > 1:
                        error_msg = "Multiple candidates found for block:\n{} {}".format("\n".join(original_block), candidates)
                        LOGGER.warning(error_msg)
                        errors.append(EditPatchStructureError(error=error_msg))
                        continue
                    replacements.append((candidates[0], candidates[0] + len(original_block), replacement_block))

        # ensure hunks are applied first to last
        replacements.sort()

        # Verify overlaps
        trailing_end = -1
        for begin_replacement, end_replacement, _ in sorted(replacements):
            if begin_replacement < trailing_end:
                error_msg = f"Overlapping replacements block {begin_replacement}-{end_replacement} and ?-{trailing_end}"
                LOGGER.warning(error_msg)
                errors.append(EditPatchStructureError(error=error_msg))
            trailing_end = end_replacement

        # After all the blocks have been identified, apply the replacements
        offset = 0
        for begin_replacement, end_replacement, replacement in replacements:
            original_lines[begin_replacement + offset : end_replacement + offset] = replacement
            offset += len(replacement) - (end_replacement - begin_replacement)

        return "\n".join(original_lines), errors


def apply_merge_conflict(content: str, patch: str, flair: WorkflowFixFlairType = WorkflowFixFlairType.MergeHybrid) -> str:
    """Apply a merge conflict style patch to content."""
    patched_content, _ = MergeConflict(patch, flair).patch(content)
    return patched_content


def get_merge_conflict(a: str, b: str, span: int = 1, flair: WorkflowFixFlairType = WorkflowFixFlairType.MergeHybrid, use_ellipsis: bool = False) -> str:
    """Generate a merge conflict style patch from two strings."""
    a_lines = a.splitlines()

    # Use unified diff as a starting point to identify changes
    diff = get_unidiff(a, b, span=span)
    patch_set = unidiff.PatchSet(io.StringIO(diff))

    blocks = []
    for patch in patch_set:
        for hunk in patch:
            start_index = hunk.source_start - 1 + (hunk.source_length == 0)
            end_index = start_index + hunk.source_length

            # Extract original and replacement lines
            original_lines = a_lines[start_index:end_index]
            replacement_lines = [line.value.rstrip("\n") for line in hunk if line.is_added or line.is_context]

            # Only create blocks for actual changes
            if original_lines != replacement_lines:
                if flair == WorkflowFixFlairType.MergeConflictWithLines:
                    original_block = f"{start_index + 1}-{end_index}"
                elif flair == WorkflowFixFlairType.MergeHybrid:
                    lines_to_find = prepend_line_numbers(a).splitlines()[start_index:end_index]
                    if use_ellipsis and len(lines_to_find) > 4:
                        lines_to_find = lines_to_find[:1] + ["..."] + lines_to_find[-1:]
                    original_block = "\n".join(lines_to_find)
                else:
                    original_block = "\n".join(original_lines)
                block = "\n".join(
                    [
                        "<<<<<<< SEARCH",
                        original_block,
                        "=======",
                        "\n".join(replacement_lines),
                        ">>>>>>> REPLACE",
                    ]
                )
                blocks.append(block)

    return "\n".join(blocks)


def prepend_line_numbers(workflow: str) -> str:
    """Adds line numbers to each line of the workflow string.
    We pre-indent the line numbers with spaces in such a way that the line numbers are alligned to the right. Currently, we support the line numbers 0-999.
      1|<line 1>
    ...
     10|<line 10>
    ...
    100|<line 100>

    Args:
        workflow: The input workflow string to add line numbers to

    Returns:
        A new string with each line prefixed with its line number in the format "XXX|" where XXX is the line number
    """

    return "\n".join(f"{i:3}|{line}" for i, line in enumerate(workflow.split("\n"), 1))


class SkipToTheGoodBit:
    """
    Handles custom patch format for the skip-to-the-good-bit section with the format:
    ```patch
    <starting_line_number_from_original_for_replacement>-<ending_line_number_from_original_for_replacement>
    -------
    <line_before_change_1>
    <line_before_change_2>
    -------
    <content_of_new_lines>
    -------
    <line_after_change_1>
    <line_after_change_2>
    ```

    Multiple patches can be separated by =======
    """

    def __init__(self, content: str):
        self.blocks = self._parse(content)

    def _parse(self, content: str):
        if content.endswith("======="):  # just in case someone might have stripped the content before this function
            content += "\n"
        blocks_content = content.split("=======\n")

        blocks = []
        for block in blocks_content:
            if not block.strip():
                continue

            # Split each block into three parts: prev_context, replacement, next_context
            parts = block.split("-------\n")
            if len(parts) != 4:
                LOGGER.warning(f"Malformed skip-to-the-good-bit block (expected 3 sections): {block}")
                continue

            line_range, prev_context_lines, replacement_lines, next_context_lines = map(str.splitlines, parts)
            estimate_start_index, estimate_end_index = map(int, map(str.strip, "\n".join(line_range).split("-")))

            # Ensure we have exactly 2 lines of context (or 1 special keyword)
            if len(prev_context_lines) < 2 and "WF_START" not in prev_context_lines:
                LOGGER.warning(f"Expected 2 lines of previous context or WF_START in block: {block}")

            if len(next_context_lines) < 2 and "WF_END" not in next_context_lines:
                LOGGER.warning(f"Expected 2 lines of next context or WF_END in block: {block}")

            blocks.append(
                {
                    "prev_context": prev_context_lines,
                    "replacement": replacement_lines,
                    "next_context": next_context_lines,
                    "estimate_start_index": estimate_start_index,
                    "estimate_end_index": estimate_end_index,
                }
            )

        return blocks

    def patch(self, content: str):
        if content.endswith("\n"):  # no difference between "content" and "content\n"
            content += "\n"
        original_lines = content.splitlines()
        original_lines = ["WF_START"] + original_lines + ["WF_END"]

        replacements = []

        # Process all blocks; assumes blocks are not interleaving
        for block in self.blocks:
            prev_context, replacement, next_context = block["prev_context"], block["replacement"], block["next_context"]
            estimate_start_index, estimate_end_index = block["estimate_start_index"], block["estimate_end_index"]

            prev_context = [LINE_NR_ANNOTATION_RE.sub("", line) for line in prev_context]  # just in case, remove line numbers
            next_context = [LINE_NR_ANNOTATION_RE.sub("", line) for line in next_context]  # just in case, remove line numbers
            # TODO: should we warn here? the model is instructed to not use line numbers for context lines

            # Process regular case - find a region in the original content
            # where prev_context is followed by next_context
            starting_context_candidates = []
            for start in range(len(original_lines) - len(prev_context) - len(next_context) + 1):
                # Check if previous context matches
                if original_lines[start : start + len(prev_context)] == prev_context:
                    starting_context_candidates.append(start)

            if not starting_context_candidates:
                LOGGER.warning(f"Could not find match for previous context: prev={prev_context}, next={next_context}")
                continue

            # Find closest candidate to the estimate_start_index
            closest_starting_candidate = min(starting_context_candidates, key=lambda c: abs(c - (estimate_start_index - len(prev_context))))

            # Find where next context follows, allowing for content in between
            ending_context_candidates = []
            for next_start in range(closest_starting_candidate + len(prev_context), len(original_lines) - len(next_context) + 1):
                if original_lines[next_start : next_start + len(next_context)] == next_context:
                    ending_context_candidates.append(next_start)

            if not ending_context_candidates:
                LOGGER.warning(f"Could not find match for next context: prev={prev_context}, next={next_context}")
                continue

            closest_ending_candidate = min(ending_context_candidates, key=lambda c: abs(c - estimate_end_index))

            # Replace everything between prev_context and next_context
            begin_replacement = closest_starting_candidate + len(prev_context)
            end_replacement = closest_ending_candidate
            replacements.append((begin_replacement, end_replacement, replacement))

            # TODO: beware that any attempt at fuzzy matching should treat the WF_START and WF_END as special cases

        # Verify overlaps
        trailing_end = -1
        for begin_replacement, end_replacement, _ in sorted(replacements):
            if begin_replacement < trailing_end:
                LOGGER.warning(f"Overlapping replacements block {begin_replacement}-{end_replacement} and ?-{trailing_end}")
            trailing_end = end_replacement

        # After all the blocks have been identified, apply the replacements
        offset = 0
        for begin_replacement, end_replacement, replacement in replacements:
            original_lines[begin_replacement + offset : end_replacement + offset] = replacement
            offset += len(replacement) - (end_replacement - begin_replacement)

        original_lines = original_lines[1:-1]  # remove WF_START and WF_END
        return "\n".join(original_lines)


def apply_skip_to_the_good_bit(content: str, patch: str) -> str:
    """Apply a skip-to-the-good-bit style patch to content."""
    return SkipToTheGoodBit(patch).patch(content)


def get_skip_to_the_good_bit(a: str, b: str) -> str:
    """
    Generate a skip-to-the-good-bit style patch from two strings.

    This is similar to get_merge_conflict but uses a different format.
    """
    if a.endswith("\n"):  # no difference between "a" and "a\n" - limitation of .splitlines()
        a += "\n"
    if b.endswith("\n"):  # no difference between "b" and "b\n" - limitation of .splitlines()
        b += "\n"
    a_lines = a.splitlines()

    # Use unified diff as a starting point to identify changes
    diff = get_unidiff(a, b, span=2)
    # the span's purpose here is to avoid atomic hunks; should not be used for the before and after context lines; those are handled below
    patch_set = unidiff.PatchSet(io.StringIO(diff))

    blocks = []
    for patch in patch_set:
        for hunk in patch:
            start_index = hunk.source_start - 1 + (hunk.source_length == 0)
            end_index = start_index + hunk.source_length

            # Extract original and replacement lines
            original_lines = a_lines[start_index:end_index]

            # Trim context lines from the beginning and end of the replacement lines
            lines_to_consider = [line for line in hunk if line.is_added or line.is_context or line.is_removed]
            while lines_to_consider and lines_to_consider[0].is_context:
                lines_to_consider.pop(0)
                start_index += 1
            while lines_to_consider and lines_to_consider[-1].is_context:
                lines_to_consider.pop(-1)
                end_index -= 1
            replacement_lines = [line.value.rstrip("\n") for line in lines_to_consider if not line.is_removed]

            # Only create blocks for actual changes
            if original_lines == replacement_lines:
                continue

            # Get context lines (2 before and 2 after)
            prev_context_lines = a_lines[max(0, start_index - 2) : start_index]
            next_context_lines = a_lines[end_index : min(len(a_lines), end_index + 2)]

            # Handle special cases for beginning and end
            if start_index < 2:
                prev_context_lines = ["WF_START"] + a_lines[:start_index]
            if end_index + 2 > len(a_lines):
                next_context_lines = a_lines[end_index:] + ["WF_END"]

            # Create the block
            block = "\n".join(
                filter(
                    None,
                    [
                        "=======",
                        f"{1 + start_index}-{end_index}",  # 1-indexed line numbers
                        "-------",
                        "\n".join(prev_context_lines),
                        "-------",
                        "\n".join(replacement_lines) or None,  # None if empty to not include a newline as the replacement block
                        "-------",
                        "\n".join(next_context_lines),
                        "=======",
                    ],
                )
            )
            blocks.append(block)

    return "\n".join(blocks)


class DiffingException(Exception):
    pass


def replace_diff_with_yaml(diff_match, workflow_as_seen_by_model: str, flair: WorkflowFixFlairType):
    """
    Replaces a diff code block with a YAML code block containing the fixed workflow.

    Takes a diff match object containing ```diff\n<diff>``` format, applies the diff to the
    original workflow, and returns a ```yaml\n<fixed_workflow>``` string with the resulting
    workflow after applying the changes.

    Args:
        diff_match: The regex match object containing the diff content
        workflow_as_seen_by_model: The original workflow YAML string
        flair: The workflow fix flair type that determines how the diff is applied

    Returns:
        A string containing the fixed workflow in YAML format wrapped in code block
    """
    diff = diff_match.group(1)
    if "@" not in diff:
        raise DiffingException("No diff found in diff code block.")

    if flair == WorkflowFixFlairType.UniDiff:
        diff = diff[diff.index("@") :]  # find first hunk, truncate anything up to that point to manipulate the diff headers next
        diff = """--- diff.txt\n+++ diff.txt\n""" + diff  # mandatory header for unidiff parser...

    try:
        if flair == WorkflowFixFlairType.UniDiff:
            stitched_workflow = apply_standard_unidiff(workflow_as_seen_by_model, diff)
        elif flair == WorkflowFixFlairType.SoftUniDiff:
            stitched_workflow = apply_soft_unidiff(workflow_as_seen_by_model, diff)
        else:
            raise DiffingException(f"Unsupported flair: {flair}")
    except unidiff.errors.UnidiffParseError as e:
        # import traceback

        # traceback.print_exc()
        # print(e)
        # print("-" * 80)
        # print(workflow_as_seen_by_model)
        # print("-" * 80)
        # print(diff)
        # print("-" * 80)
        raise DiffingException("Could not apply diff. UnidiffParseErro received.") from e
    except Exception as e:
        raise DiffingException("Could not apply diff.") from e
    return f"```yaml\n{stitched_workflow}\n```"


def replace_patch_with_yaml(patch_match, workflow_as_seen_by_model: str, flair: WorkflowFixFlairType):
    """
    Replaces a patch code block with a YAML code block containing the fixed workflow.
    To be used in conjunction with the merge conflict patch.

    Takes a patch match object containing ```patch\n<patch>``` format, applies the patch to the
    original workflow, and returns a ```yaml\n<fixed_workflow>``` string with the resulting
    workflow after applying the changes.
    """
    patch = patch_match.group(1)
    if "=======" not in patch:
        LOGGER.warning("No merge conflict marker found in patch.")
        raise DiffingException("No merge conflict marker found in patch.")

    try:
        stitched_workflow = apply_merge_conflict(workflow_as_seen_by_model, patch, flair=flair)
    except Exception as e:
        # import traceback

        # traceback.print_exc()
        # print(e)
        # print("-" * 80)
        # print(workflow_as_seen_by_model)
        # print("-" * 80)
        # print(patch)
        # print("-" * 80)
        raise DiffingException("Could not apply merge conflict patch.") from e
    return f"```yaml\n{stitched_workflow}\n```"


def replace_skip_to_the_good_bit_with_yaml(diff_match, workflow_as_seen_by_model: str, flair: WorkflowFixFlairType):
    """
    Replaces a diff code block with a YAML code block containing the fixed workflow.

    Takes a diff match object containing ```diff\n<diff>``` format, applies the "skip to the good bit"
    transformation to the original workflow, and returns a ```yaml\n<fixed_workflow>``` string with
    the resulting workflow after applying the changes.
    """
    diff = diff_match.group(1)
    try:
        stitched_workflow = apply_skip_to_the_good_bit(workflow_as_seen_by_model, diff)
    except Exception as e:
        # import traceback

        # traceback.print_exc()
        # print(e)
        # print("-" * 80)
        # print(workflow_as_seen_by_model)
        # print("-" * 80)
        # print(diff)
        # print("-" * 80)
        raise DiffingException("Could not apply skip to the good bit diff.") from e
    return f"```yaml\n{stitched_workflow}\n```"
