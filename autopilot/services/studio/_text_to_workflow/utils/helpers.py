import asyncio
from typing import Any, Awaitable, Callable, TypeVar

T = TypeVar("T")


def singleton(cls: type) -> Callable[..., Awaitable[T]]:
    instances = {}
    lock = asyncio.Lock()

    async def get_instance(*args: Any, **kwargs: Any) -> T:
        nonlocal instances
        async with lock:
            if cls not in instances:
                instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance
