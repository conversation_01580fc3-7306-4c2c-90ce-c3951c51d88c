from typing import Any, Optional

import httpx

from services.studio._text_to_workflow.utils.errors import FailedDependencyError, ServiceError


async def http_get_json(url: str, headers: dict, params: Optional[dict[str, Any] | list[tuple[str, Any]]] = None) -> dict[str, Any] | list[dict[str, Any]]:
    """Makes an HTTP GET request and returns the JSON response.

    Args:
        url: The URL to make the request to
        headers: HTTP headers to include in the request
        params: Optional query parameters or list of parameter tuples

    Returns:
        The JSON response as either a JSON object or a list of JSON objects

    Raises:
        FailedDependencyError: If the connection times out
        ServiceError: If the request fails with a non-200 status code
    """
    async with httpx.AsyncClient(timeout=None) as client:
        try:
            response = await client.get(
                url,
                headers=headers,
                params=params,
            )
        except httpx.ConnectTimeout:
            raise FailedDependencyError("Timeout connecting to the service")

        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as error:
            raise ServiceError(code=error.response.status_code, message=error.response.text)

        return response.json()


async def http_post_json(
    url: str, headers: dict, json_obj: Any = None, params: Optional[dict[str, Any] | list[tuple[str, Any]]] = None
) -> dict[str, Any] | list[dict[str, Any]]:
    """Makes an HTTP POST request with JSON body and returns the JSON response.

    Args:
        url: The URL to make the request to
        headers: HTTP headers to include in the request
        json: Optional JSON body to send with the request
        params: Optional query parameters or list of parameter tuples

    Returns:
        The JSON response as either a JSON object or a list of JSON objects

    Raises:
        FailedDependencyError: If the connection times out
        ServiceError: If the request fails with a non-200 status code
    """
    async with httpx.AsyncClient(timeout=None) as client:
        try:
            response = await client.post(
                url,
                headers=headers,
                json=json_obj,
                params=params,
            )
        except httpx.ConnectTimeout:
            raise FailedDependencyError("Timeout connecting to the service")

        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as error:
            raise ServiceError(code=error.response.status_code, message=error.response.text)

        return response.json()
