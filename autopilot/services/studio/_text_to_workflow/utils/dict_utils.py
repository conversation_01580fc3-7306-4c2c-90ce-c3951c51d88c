import typing as t


def get_sole_item_from_dict(dictionary: dict[t.Any, t.Any]) -> tuple[t.Any, t.Any]:
    """
    Checks if a dictionary has exactly one item and returns the key/value pair.
    """
    if not isinstance(dictionary, dict):
        raise ValueError(f"Expected a dictionary, got {type(dictionary).__name__}")

    if len(dictionary) != 1:
        raise ValueError(f"Expected exactly one item in dictionary, got {len(dictionary)}")

    return next(iter(dictionary.items()))
