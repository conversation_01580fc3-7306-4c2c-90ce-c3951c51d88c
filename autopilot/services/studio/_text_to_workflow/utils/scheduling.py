import datetime


def check_schdeule(every_m_minutes: int, offset_minutes: int) -> None:
    if not 0 <= offset_minutes <= every_m_minutes:
        raise ValueError(f"The `offset_minutes` ({offset_minutes}) must be between 0 and `every_m_minutes` ({every_m_minutes}).")


def get_next_run_datetime(now: datetime.datetime, every_m_minutes: int, offset_minutes: int) -> datetime.datetime:
    check_schdeule(every_m_minutes, offset_minutes)
    # Calculate the total minutes for the next run
    total_minutes = (now.hour * 60 + now.minute) // every_m_minutes * every_m_minutes + every_m_minutes + offset_minutes
    # Calculate the hour and minute for the next run
    next_hour = total_minutes // 60
    next_minute = total_minutes % 60
    # Handle the case where the next run is on the next day
    if next_hour < 24:
        return now.replace(hour=next_hour, minute=next_minute, second=0, microsecond=0)
    elif 24 <= next_hour < 48:
        next_hour -= 24
        return now.replace(hour=next_hour, minute=next_minute, second=0, microsecond=0) + datetime.timedelta(days=1)
    else:
        raise ValueError("The next run is 48 hours or more away .")


def test_midnight():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    assert get_next_run_datetime(now, 240, 0) == datetime.datetime(2023, 1, 1, 4, 0, 0)


def test_four_am():
    now = datetime.datetime(2023, 1, 1, 4, 0, 0)
    assert get_next_run_datetime(now, 240, 0) == datetime.datetime(2023, 1, 1, 8, 0, 0)


def test_noon():
    now = datetime.datetime(2023, 1, 1, 12, 0, 0)
    assert get_next_run_datetime(now, 240, 0) == datetime.datetime(2023, 1, 1, 16, 0, 0)


def test_before_midnight():
    now = datetime.datetime(2023, 1, 1, 23, 59, 59)
    assert get_next_run_datetime(now, 240, 0) == datetime.datetime(2023, 1, 2, 0, 0, 0)


def test_random_hour():
    now = datetime.datetime(2023, 1, 1, 13, 23, 0)
    assert get_next_run_datetime(now, 240, 0) == datetime.datetime(2023, 1, 1, 16, 0, 0)


def test_random_hour_every_5_minutes():
    now = datetime.datetime(2023, 1, 1, 13, 23, 0)
    assert get_next_run_datetime(now, 5, 0) == datetime.datetime(2023, 1, 1, 13, 25, 0)


def test_every_day():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    assert get_next_run_datetime(now, 1440, 0) == datetime.datetime(2023, 1, 2, 0, 0, 0)


def test_every_2_days():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    try:
        get_next_run_datetime(now, 2880, 0)
        raise AssertionError("Should have raised a ValueError")
    except ValueError:
        assert True


def test_no_offset():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    assert get_next_run_datetime(now, 60, 0) == datetime.datetime(2023, 1, 1, 1, 0, 0)


def test_small_offset():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    assert get_next_run_datetime(now, 60, 5) == datetime.datetime(2023, 1, 1, 1, 5, 0)


def test_large_offset():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    assert get_next_run_datetime(now, 60, 30) == datetime.datetime(2023, 1, 1, 1, 30, 0)


def test_random_hour_every_5_minutes_with_offset_1():
    now = datetime.datetime(2023, 1, 1, 13, 23, 0)
    assert get_next_run_datetime(now, 5, 1) == datetime.datetime(2023, 1, 1, 13, 26, 0)


def test_around_midnight():
    now = datetime.datetime(2023, 1, 1, 23, 55, 0)
    assert get_next_run_datetime(now, 60, 5) == datetime.datetime(2023, 1, 2, 0, 5, 0)


def test_invalid_offset_negative():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    try:
        get_next_run_datetime(now, 60, -5)
        raise AssertionError("Should have raised a ValueError")
    except ValueError:
        assert True


def test_invalid_offset_too_large():
    now = datetime.datetime(2023, 1, 1, 0, 0, 0)
    try:
        get_next_run_datetime(now, 60, 61)
        raise AssertionError("Should have raised a ValueError")
    except ValueError:
        assert True


if __name__ == "__main__":
    test_midnight()
    test_four_am()
    test_noon()
    test_before_midnight()
    test_random_hour()
    test_every_day()
    test_every_2_days()
    print("No offset tests passed")
    test_no_offset()
    test_small_offset()
    test_large_offset()
    test_random_hour_every_5_minutes_with_offset_1()
    test_around_midnight()
    test_invalid_offset_negative()
    test_invalid_offset_too_large()
    print("Offset tests passed")
