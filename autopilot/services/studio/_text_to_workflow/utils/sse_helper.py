import asyncio
import traceback
from abc import ABC, abstractmethod
from typing import Any, Async<PERSON><PERSON>ator, Callable, Coroutine, Generic, Optional, Type, TypeVar

from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, ValidationError
from sse_starlette import EventSourceResponse
from starlette.requests import Request

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

T = TypeVar("T", bound=BaseModel)
LOGGER = AppInsightsLogger()


class IMessageEmitter(ABC):
    """
    Interface defining the contract for message emitters that handle SSE messages.
    """

    @abstractmethod
    async def emit_message_part(self, content: Any, message_id_delta: int) -> int:
        """Emit a message intended for user display with a delta id"""
        pass

    @abstractmethod
    async def emit_message(self, content: Any, message_type: str = "message") -> int:
        """Emit a message intended for user display"""
        pass

    @abstractmethod
    async def emit_debug_message(self, content: Any | Callable[[], Any]) -> int | None:
        """Emit a message for system-level information"""
        pass

    @abstractmethod
    async def emit_error(self, content: Any, end_stream: bool = True) -> int:
        """Emit an error message"""
        pass

    @abstractmethod
    async def emit_completion(self, content: Optional[Any] = None) -> int:
        """Emit a completion message that ends the stream early"""
        pass


class SSEMessage:
    """
    A generic message class for use with SSEHelper.
    """

    def __init__(self, id: int, content: Any, message_type: str = "message", end_stream: bool = False, delta_id: int | None = None):
        self.id = id
        self.content = content
        self.message_type = message_type
        self.end_stream = end_stream
        self.delta_id = delta_id


class MessageEmitter(IMessageEmitter):
    """
    A context class that allows emitting different types of SSE messages.
    This can be passed down to services that need to emit messages.
    """

    def __init__(self, queue: asyncio.Queue[SSEMessage]):
        self._queue = queue
        self.message_id = 0

    async def emit_message_part(self, content: Any, message_id_delta: int) -> int:
        """Emit a message intended for user display"""
        return await self._emit_message(content, "message_delta", message_id_delta)

    async def emit_message(self, content: Any, message_type: str = "message") -> int:
        """Emit a message intended for user display"""
        return await self._emit_message(content, message_type)

    async def emit_debug_message(self, content: Any | Callable[[], Any]) -> int | None:
        """Emit a message for system-level information"""
        if not settings.IS_PROD:
            actual_content = content() if callable(content) else content
            return await self._emit_message(actual_content, "debug")
        return None

    async def emit_error(self, content: Any, end_stream: bool = True) -> int:
        """Emit an error message"""
        return await self._emit_message(content, "error", end_stream=end_stream)

    async def emit_completion(self, content: Optional[Any] = None) -> int:
        """Emit a completion message that ends the stream early"""
        msg_content = content if content is not None else "Operation completed."
        return await self._emit_message(msg_content, "done", end_stream=True)

    async def _emit_message(self, content: Any, message_type: str, message_id_delta: int = 0, end_stream: bool = False) -> int:
        message_id = self.message_id + 1
        self.message_id = message_id

        # Check if content is a Pydantic model and convert to a JSON
        if isinstance(content, BaseModel):
            content = content.model_dump_json(indent=2 if not settings.IS_PROD else None)

        await self._queue.put(SSEMessage(message_id, content, message_type=message_type, delta_id=message_id_delta, end_stream=end_stream))
        return message_id


class SSEHelper(Generic[T]):
    """
    A helper class to encapsulate the complexity of SSE endpoints using a message queue.

    The process_function is an asynchronous callable that takes:
        - input_data: a Pydantic model of type T
        - emitter: a MessageEmitter instance for emitting different types of messages
    """

    def __init__(self, process_function: Callable[[T, MessageEmitter], Coroutine[Any, Any, None]], model_class: Type[T]):
        self.process_function = process_function
        self.model_class = model_class

    async def _stream(self, input_data: T, request: Request) -> AsyncGenerator[dict, None]:
        message_queue: asyncio.Queue[SSEMessage] = asyncio.Queue()
        emitter = MessageEmitter(message_queue)
        task = asyncio.create_task(self.process_function(input_data, emitter))

        def done_callback(future):
            e = future.exception()
            if e is not None:
                error_message = f"Encountered an error for request {request.url}: {str(e)}\n{''.join(traceback.format_exception(type(e), e, e.__traceback__))}"
                LOGGER.error(error_message)
                if settings.DEBUG_MODE:
                    print(error_message)

                if not settings.IS_PROD:
                    asyncio.create_task(emitter.emit_error(str(e)))
                else:
                    asyncio.create_task(emitter.emit_error("An internal error occurred while processing the request."))
            else:
                asyncio.create_task(emitter.emit_completion(future.result()))

        task.add_done_callback(done_callback)

        try:
            while True:
                if await request.is_disconnected():
                    task.cancel()
                    break
                msg: SSEMessage = await message_queue.get()
                yield {
                    "event": msg.message_type if not msg.end_stream else "done",
                    "data": msg.content,
                    "id": msg.id if msg.delta_id is None else f"{msg.delta_id}.{msg.id}",
                }
                if msg.end_stream:
                    break
        except Exception:
            LOGGER.error(f"SSE stream encountered an error for request {request.url}: {traceback.format_exc()}")
            if not settings.IS_PROD:
                yield {"event": "error", "data": traceback.format_exc()}
            else:
                yield {"event": "error", "data": "An internal error occurred while processing the request."}
        finally:
            if not task.done():
                task.cancel()

    async def run(self, request: Request):
        """
        Parses the request JSON, validates it against the model_class, and starts the SSE stream.
        Returns either a validation error response or an EventSourceResponse.
        """
        input_data = await request.json()
        try:
            validated_data = self.model_class(**input_data)
        except ValidationError as e:
            raise RequestValidationError(errors=e.errors()) from e
        return EventSourceResponse(self._stream(validated_data, request), ping=999999)
