import typing_extensions as t
from pydantic import BaseModel


class ModelOptions(t.TypedDict, total=False):
    model_name: str | None
    seed: t.NotRequired[int | None]


class BasePydanticRequest(BaseModel):
    seed: int | None = None
    model: str | ModelOptions | dict[str, ModelOptions] | None = None


class BasePydanticResponse(BaseModel):
    prompts: dict[str, str] | None = None


class BaseRequest(t.TypedDict, total=False):
    seed: t.NotRequired[int | None]
    model: t.NotRequired[str | ModelOptions | dict[str, ModelOptions] | None]


class BaseResponse(t.TypedDict, total=False):
    prompts: t.NotRequired[dict[str, str]]


class FeatureFlagsRequest(t.TypedDict):
    licenseType: str


class FeatureFlags(t.TypedDict):
    enableConfigureActivity: bool


class FeatureFlagsResponse(t.TypedDict):
    featureFlags: FeatureFlags
