from langchain_text_splitters import CharacterTextSplitter


def split_by_character(
    text: str,
    model_name: str,
    chunk_size: int,
    chunk_overlap: int = 0,
    sepparator: str = "\n",
) -> list[str]:
    """
    Function to split text into chunks of given size.

    Args:
        text (str): The input text string to be splitted.
        model_name (str): tiktoken encoder model name to be used.
        chunk_size (int): The maximum size of a chunk that will be split if splitting is possible.
        chunk_overlap (int): The amount of overlap that should exist between two consecutive chunks, defaults to 0.
        sepparator (str): The character used to delimit chunks, defaults to newline.

    Returns:
        List[str]: The list of text chunks.
    """

    text_splitter: CharacterTextSplitter = CharacterTextSplitter.from_tiktoken_encoder(
        model_name="gpt-4o",  # should be model_name but langchain_text_splitters doesn't support Gemini or Claude
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separator=sepparator,
    )
    return text_splitter.split_text(text)
