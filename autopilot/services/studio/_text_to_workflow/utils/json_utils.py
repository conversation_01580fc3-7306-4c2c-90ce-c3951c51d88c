import json
import pathlib

import typing_extensions as t


def json_load_from_path(path: pathlib.Path, **kwargs) -> t.Any:
    """
    Load a JSON file or string and return its contents as a Python object.
    """
    with path.open("r", encoding="utf-8") as fd:
        return json.load(fd, **kwargs)


@t.overload
def json_dump(data: t.Any, path_or_stream: pathlib.Path | t.TextIO, **kwargs) -> None: ...


@t.overload
def json_dump(data: t.Any, **kwargs) -> str: ...


def json_dump(data: t.Any, path_or_stream: pathlib.Path | t.TextIO | None = None, **kwargs) -> str | None:
    """
    Serialize a Python object to JSON and write it to a file/stream or return it as a string.
    """
    # Set default kwargs for consistent formatting
    kwargs.setdefault("indent", 2)
    kwargs.setdefault("ensure_ascii", False)

    if isinstance(path_or_stream, pathlib.Path):
        with path_or_stream.open("w", encoding="utf-8") as fd:
            return json_dump(data, fd, **kwargs)

    if path_or_stream is None:
        return json.dumps(data, **kwargs)

    return json.dump(data, path_or_stream, **kwargs)
