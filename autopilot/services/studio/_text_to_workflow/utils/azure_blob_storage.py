import datetime

import azure.identity as azid
import azure.storage.blob as azblob

_storage_account_url = "https://{storage_account_name}.blob.core.windows.net/"
_container_url = "https://{storage_account_name}.blob.core.windows.net/{container_name}"


def request_user_delegation_key(storage_account_name: str) -> azblob.UserDelegationKey:
    delegation_key_start_time = datetime.datetime.now(datetime.timezone.utc)
    delegation_key_expiry_time = delegation_key_start_time + datetime.timedelta(days=1)
    storage_account_url = _storage_account_url.format(storage_account_name=storage_account_name)
    blob_service_client: azblob.BlobServiceClient = azblob.BlobServiceClient(account_url=storage_account_url, credential=azid.DefaultAzureCredential())
    user_delegation_key = blob_service_client.get_user_delegation_key(key_start_time=delegation_key_start_time, key_expiry_time=delegation_key_expiry_time)

    return user_delegation_key


def create_user_delegation_sas_container_token(storage_account_name: str, container_name: str, user_delegation_key: azblob.UserDelegationKey) -> str:
    start_time = datetime.datetime.now(datetime.timezone.utc)
    expiry_time = start_time + datetime.timedelta(days=1)
    sas_token = azblob.generate_container_sas(
        account_name=storage_account_name,
        container_name=container_name,
        user_delegation_key=user_delegation_key,
        permission=azblob.ContainerSasPermissions(read=True, list=True),
        expiry=expiry_time,
        start=start_time,
    )

    return sas_token


def create_user_delegation_sas_blob_token(storage_account_name: str, container_name: str, blob_name: str, user_delegation_key: azblob.UserDelegationKey) -> str:
    start_time = datetime.datetime.now(datetime.timezone.utc)
    expiry_time = start_time + datetime.timedelta(days=1)
    sas_token = azblob.generate_blob_sas(
        account_name=storage_account_name,
        container_name=container_name,
        blob_name=blob_name,
        user_delegation_key=user_delegation_key,
        permission=azblob.BlobSasPermissions(read=True),
        expiry=expiry_time,
        start=start_time,
    )

    return sas_token


def create_container_sas_url(storage_account_name: str, container_name: str) -> str:
    user_delegation_key = request_user_delegation_key(storage_account_name)
    sas_token = create_user_delegation_sas_container_token(storage_account_name, container_name, user_delegation_key)
    container_url = _container_url.format(storage_account_name=storage_account_name, container_name=container_name)
    sas_url = f"{container_url}?{sas_token}"

    return sas_url


def create_blob_sas_url(storage_account_name: str, container_name: str, blob_name: str, sas_token: str) -> str:
    container_url = _container_url.format(storage_account_name=storage_account_name, container_name=container_name)
    blob_url = f"{container_url}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"

    return sas_url
