# ======== runMinHashExample =======
# https://github.com/chrisjmccormick/MinHash
# This example code demonstrates comparing documents using the MinHash
# approach.
#
# First, each document is represented by the set of shingles it contains. The
# documents can then be compared using the Jaccard similarity of their
# shingle sets. This is computationally expensive, however, for large numbers
# of documents.
#
# For comparison, we will also use the MinHash algorithm to calculate short
# signature vectors to represent the documents. These MinHash signatures can
# then be compared quickly by counting the number of components in which the
# signatures agree. We'll compare all possible pairs of documents, and find
# the pairs with high similarity.
#
# The program follows these steps:
# 1. Convert each test file into a set of shingles.
#    - The shingles are formed by combining three consecutive words together.
#    - Shingles are mapped to shingle IDs using the CRC32 hash.
# 2. Calculate all Jaccard similarities directly.
#    - This is ok for small dataset sizes. For the full 10,000 articles, it
#      takes 20 minutes!
# 3. Calculate the MinHash signature for each document.
#    - The MinHash algorithm is implemented using the random hash function
#      trick which prevents us from having to explicitly compute random
#      permutations of all of the shingle IDs. For further explanation, see
#      section 3.3.5 of http://infolab.stanford.edu/~ullman/mmds/ch3.pdf
# 4. Compare all MinHash signatures to one another.
#    - Compare MinHash signatures by counting the number of components in which
#      the signatures are equal. Divide the number of matching components by
#      the signature length to get a similarity value.
#    - Display pairs of documents / signatures with similarity greater than a
#      threshold.

from __future__ import division

import binascii
import random
import time
from collections import defaultdict
from typing import Literal

Subset = Literal["TRAIN", "VALIDATE"]
silent = True


def output(*args):
    if silent:
        return
    print(*args)


def create_shingles(docs, docids):
    # Create a dictionary of the articles, mapping the article identifier (e.g.,
    # "t8470") to the list of shingle IDs that appear in the document.
    docsAsShingleSets = {}
    totalShingles = 0
    t0 = time.time()

    output("Shingling articles...")

    for docid, doc in zip(docids, docs, strict=False):
        words = doc
        # 'shinglesInDoc' will hold all of the unique shingle IDs present in the
        # current document. If a shingle ID occurs multiple times in the document,
        # it will only appear once in the set (this is a property of Python sets).
        shinglesInDoc = set()

        # For each word in the document...
        for index in range(0, len(words) - 2):
            # Construct the shingle text by combining three words together.
            shingle = words[index] + " " + words[index + 1] + " " + words[index + 2]

            # Hash the shingle to a 32-bit integer.
            crc = binascii.crc32(shingle.encode("utf-8")) & 0xFFFFFFFF

            # Add the hash value to the list of shingles for the current document.
            # Note that set objects will only add the value to the set if the set
            # doesn't already contain it.
            shinglesInDoc.add(crc)

        # Store the completed list of shingles for this document in the dictionary.
        docsAsShingleSets[docid] = shinglesInDoc

        # Count the number of shingles across all documents.
        totalShingles = totalShingles + (len(words) - 2)

    # Report how long shingling took.
    output("\nShingling " + str(len(docs)) + " docs took %.2f sec." % (time.time() - t0))
    output("\nAverage shingles per doc: %.2f" % (totalShingles / len(docs)))

    return docsAsShingleSets


def find_duplicates(docs, docids, docsAsShingleSets, numHashes: int, threshold: float):
    # =============================================================================
    #                     Define Triangle Matrices
    # =============================================================================

    # Define virtual Triangle matrices to hold the similarity values. For storing
    # similarities between pairs, we only need roughly half the elements of a full
    # matrix. Using a triangle matrix requires less than half the memory of a full
    # matrix, and can protect the programmer from inadvertently accessing one of
    # the empty/invalid cells of a full matrix.

    numDocs = len(docs)
    # Calculate the number of elements needed in our triangle matrix
    numElems = int(numDocs * (numDocs - 1) / 2)

    # Initialize two empty lists to store the similarity values.
    # 'JSim' will be for the actual Jaccard Similarity values.
    # 'estJSim' will be for the estimated Jaccard Similarities found by comparing
    # the MinHash signatures.
    # JSim = [0 for x in range(numElems)]
    estJSim = [0 for x in range(numElems)]

    # Define a function to map a 2D matrix coordinate into a 1D index.
    def getTriangleIndex(i, j):
        # If i == j that's an error.
        assert i != j
        # If j < i just swap the values.
        if j < i:
            temp = i
            i = j
            j = temp

        # Calculate the index within the triangular array.
        # This fancy indexing scheme is taken from pg. 211 of:
        # http://infolab.stanford.edu/~ullman/mmds/ch6.pdf
        # But I adapted it for a 0-based index.
        # Note: The division by two should not truncate, it
        #       needs to be a float.
        k = int(i * (numDocs - (i + 1) / 2.0) + j - i) - 1

        return k

    # =============================================================================
    #                 Generate MinHash Signatures
    # =============================================================================

    # Time this step.
    t0 = time.time()

    output("\nGenerating random hash functions...")

    # Record the maximum shingle ID that we assigned.
    maxShingleID = 2**32 - 1

    # We need the next largest prime number above 'maxShingleID'.
    # I looked this value up here:
    # http://compoasso.free.fr/primelistweb/page/prime/liste_online_en.php
    nextPrime = 4294967311

    # Our random hash function will take the form of:
    #   h(x) = (a*x + b) % c
    # Where 'x' is the input value, 'a' and 'b' are random coefficients, and 'c' is
    # a prime number just greater than maxShingleID.

    # Generate a list of 'k' random coefficients for the random hash functions,
    # while ensuring that the same value does not appear multiple times in the
    # list.
    def pickRandomCoeffs(k):
        # Create a list of 'k' random values.
        randList = []

        while k > 0:
            # Get a random shingle ID.
            randIndex = random.randint(0, maxShingleID)

            # Ensure that each random number is unique.
            while randIndex in randList:
                randIndex = random.randint(0, maxShingleID)

            # Add the random number to the list.
            randList.append(randIndex)
            k = k - 1

        return randList

    # For each of the 'numHashes' hash functions, generate a different coefficient 'a' and 'b'.
    coeffA = pickRandomCoeffs(numHashes)
    coeffB = pickRandomCoeffs(numHashes)

    output("\nGenerating MinHash signatures for all documents...")

    # List of documents represented as signature vectors
    signatures = []

    # Rather than generating a random permutation of all possible shingles,
    # we'll just hash the IDs of the shingles that are *actually in the document*,
    # then take the lowest resulting hash code value. This corresponds to the index
    # of the first shingle that you would have encountered in the random order.

    # For each document...
    for docID in docids:
        # Get the shingle set for this document.
        shingleIDSet = docsAsShingleSets[docID]

        # The resulting minhash signature for this document.
        signature = []

        # For each of the random hash functions...
        for i in range(0, numHashes):
            # For each of the shingles actually in the document, calculate its hash code
            # using hash function 'i'.

            # Track the lowest hash ID seen. Initialize 'minHashCode' to be greater than
            # the maximum possible value output by the hash.
            minHashCode = nextPrime + 1

            # For each shingle in the document...
            for shingleID in shingleIDSet:
                # Evaluate the hash function.
                hashCode = (coeffA[i] * shingleID + coeffB[i]) % nextPrime

                # Track the lowest hash code seen.
                if hashCode < minHashCode:
                    minHashCode = hashCode

            # Add the smallest hash code value as component number 'i' of the signature.
            signature.append(minHashCode)

        # Store the MinHash signature for this document.
        signatures.append(signature)

    # Calculate the elapsed time (in seconds)
    elapsed = time.time() - t0

    output("\nGenerating MinHash signatures took %.2fsec" % elapsed)

    # =============================================================================
    #                     Compare All Signatures
    # =============================================================================

    output("\nComparing all signatures...")

    # Creates a N x N matrix initialized to 0.

    # Time this step.
    t0 = time.time()

    # For each of the test documents...
    for i in range(0, numDocs):
        # Get the MinHash signature for document i.
        signature1 = signatures[i]

        # For each of the other test documents...
        for j in range(i + 1, numDocs):
            # Get the MinHash signature for document j.
            signature2 = signatures[j]

            count = 0
            # Count the number of positions in the minhash signature which are equal.
            for k in range(0, numHashes):
                count = count + (signature1[k] == signature2[k])

            # Record the percentage of positions which matched.
            estJSim[getTriangleIndex(i, j)] = count / numHashes

    # Calculate the elapsed time (in seconds)
    elapsed = time.time() - t0

    output("\nComparing MinHash signatures took %.2fsec" % elapsed)

    # =============================================================================
    #                   Find Similar Document Pairs
    # =============================================================================

    t0 = time.time()

    duplicate_pairs: set[frozenset[str, str]] = set()
    similarity_scores: dict[frozenset[str, str], float] = {}

    # For each of the document pairs...
    for i in range(0, numDocs):
        for j in range(i + 1, numDocs):
            # Retrieve the estimated similarity value for this pair.
            estJ = estJSim[getTriangleIndex(i, j)]

            if estJ < threshold:
                continue

            # If the similarity is above the threshold, calculate
            # the actual Jaccard similarity for validation.
            s1 = docsAsShingleSets[docids[i]]
            s2 = docsAsShingleSets[docids[j]]
            jaccard_similarity = len(s1.intersection(s2)) / len(s1.union(s2))

            if jaccard_similarity >= threshold:
                pair = frozenset((docids[i], docids[j]))
                duplicate_pairs.add(pair)
                similarity_scores[pair] = jaccard_similarity

    original_count = len(duplicate_pairs)
    missing_pairs = find_missing_pairs(duplicate_pairs)

    for fs_pair in missing_pairs:
        pair = tuple(fs_pair)
        s1 = docsAsShingleSets[pair[0]]
        s2 = docsAsShingleSets[pair[1]]
        jaccard_similarity = len(s1.intersection(s2)) / len(s1.union(s2))

        if jaccard_similarity >= threshold:
            duplicate_pairs.add(fs_pair)
            similarity_scores[fs_pair] = jaccard_similarity

    elapsed = time.time() - t0
    output(f"\nFound {original_count} similar pairs and {len(duplicate_pairs) - original_count} duplicates among {len(missing_pairs)} missing pairs")
    output("\nFinding similar pairs and checking missing pairs took %.2fsec" % elapsed)

    return list(duplicate_pairs), similarity_scores


def find_missing_pairs(pairs: set[frozenset[str, str]]) -> set[frozenset[str, str]]:
    graph = defaultdict(set)
    for a, b in pairs:
        graph[a].add(b)
        graph[b].add(a)

    new_pairs = pairs.copy()
    for node in graph:
        # For each document this document is a pair (near duplicate) of...
        for pair in graph[node]:
            # For each transitive pair...
            for tpair in graph[pair]:
                # If the transitive pair doesn't exist, create it (not sure if the second two checks are needed now with frozenset).
                if tpair != node and node not in graph[tpair] and tpair not in graph[node]:
                    new_pairs.add(frozenset((node, tpair)))

    return new_pairs


def prune_cross_subset_contamination(duplicate_pairs: set[frozenset[str, str]], doc_subsets: dict[str, str]):
    pruned_docs: set[str] = set()

    for fs_pair in duplicate_pairs:
        pair = tuple(fs_pair)
        if doc_subsets[pair[0]] == "TRAIN" and doc_subsets[pair[1]] == "VALIDATE":
            pruned_docs.add(pair[1])
        if doc_subsets[pair[0]] == "VALIDATE" and doc_subsets[pair[1]] == "TRAIN":
            pruned_docs.add(pair[0])

    return pruned_docs


def prune_subset_repeats(duplicate_pairs: set[frozenset[str, str]], doc_subsets: dict[str, str], already_pruned: set[str], subset: str):
    subset_pairs = [pair for pair in duplicate_pairs if {doc_subsets[doc] for doc in pair} == {subset}]

    pair_count = defaultdict(int)
    for fs_pair in subset_pairs:
        pair = tuple(fs_pair)
        pair_count[pair[0]] += 1
        pair_count[pair[1]] += 1

    pruned_docs: set[str] = set(already_pruned)
    for fs_pair in subset_pairs:
        pair = tuple(fs_pair)
        if pair[0] in pruned_docs or pair[1] in pruned_docs:
            continue

        # For any group of near duplicate documents (which may span several pairs),
        # we remove the most overlapping document so that we can remove as few as possible.
        if pair_count[pair[0]] > pair_count[pair[1]]:
            pruned_docs.add(pair[0])
        else:
            pruned_docs.add(pair[1])

    return pruned_docs


def identify_docs_to_drop(duplicate_pairs: set[frozenset[str, str]], doc_subsets: dict[str, str]):
    pruned_docs: set[str] = set()

    pruned_docs.update(prune_cross_subset_contamination(duplicate_pairs, doc_subsets))
    pruned_docs.update(prune_subset_repeats(duplicate_pairs, doc_subsets, pruned_docs, "TRAIN"))
    pruned_docs.update(prune_subset_repeats(duplicate_pairs, doc_subsets, pruned_docs, "VALIDATE"))

    return pruned_docs
