import pytest

from services.studio._text_to_workflow.semantic_diff_text import (
    SemanticDiffTextsTask,
    load_request,
)
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@pytest.fixture
def semantic_diff_fixtures_path():
    return conftest._fixtures_path / "semantic_diff_text_fixtures"


@pytest.fixture
def test_semantic_diff_config_path(project_root_path):
    return project_root_path / "semantic_diff_text/prompt.yaml"


@pytest.mark.parametrize(
    "input, override",
    [
        ("01", {}),  # Testing semantically different case
        ("02", {}),  # Testing semantically same case
        ("01", {"max_input_tokens": 6000, "min_chunk_size": 1500}),  # Testing chunking by limiting max_input_tokens
    ],
)
@pytest.mark.asyncio
async def test_semantic_diff(semantic_diff_fixtures_path, test_semantic_diff_config_path, input, override):
    req = load_request(semantic_diff_fixtures_path / input)
    task = SemanticDiffTextsTask(test_semantic_diff_config_path)

    if override:
        task.config = task.config.override(**override)

    response = await task.run(req.export())
    reference = yaml_load(req.ref)

    assert response.semantic_diff == reference["semantic_diff"]
