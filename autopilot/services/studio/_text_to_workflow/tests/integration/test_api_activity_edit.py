import httpx
import pytest

import services.studio._text_to_workflow.tests.conftest as conftest
import services.studio._text_to_workflow.utils.yaml_utils as yu

_api_activity_edit_fixtures_path = conftest._fixtures_path / "api_activity_edit_fixtures"
_endpoint_cases_path = _api_activity_edit_fixtures_path / "endpoint_cases.yaml"
_endpoint_cases = yu.yaml_load(_endpoint_cases_path)


@pytest.mark.asyncio
@pytest.mark.parametrize("case_name", list(_endpoint_cases.keys()))
async def test_api_activity_edit_dap_from_input_arguments(case_name: str, client: httpx.AsyncClient):
    case = _endpoint_cases[case_name]
    response = await client.post("/v2/api-activity-edit", json=case["request"])
    status_code = response.status_code
    assert status_code == 200, f"Status code is not 200, but {status_code}"
    response = response.json()
    response_properties_changes = {change["propertyName"]: change["propertyValue"] for change in response["propertiesChange"]["data"]}
    expected_properties_changes = {change["propertyName"]: change["propertyValue"] for change in case["expected"]["propertiesChange"]["data"]}
    response_arguments_changes = {change["argumentLocation"]: change["argumentSchema"]["type"] for change in response["argumentsChange"]["data"]}
    expected_arguments_changes = {change["argumentLocation"]: change["argumentSchema"]["type"] for change in case["expected"]["argumentsChange"]["data"]}
    assert response["message"], "Falsy message in the response"
    assert response["propertiesChange"]["activityId"] == case["expected"]["propertiesChange"]["activityId"], "Activity id is not the same"
    assert len(response_properties_changes) == len(expected_properties_changes), "Changes are not the same"
    for name, value in response_properties_changes.items():
        assert value in expected_properties_changes[name], f"Change {name}: {value} is not in expected change values {expected_properties_changes[name]}"
    assert len(response_arguments_changes) == len(expected_arguments_changes), "Changes are not the same"
    for name, value in response_arguments_changes.items():
        assert value in expected_arguments_changes[name], f"Change {name}: {value} is not in expected change values {expected_arguments_changes[name]}"
