import pytest

from services.studio._text_to_workflow.activity_config import activity_config_config as acc
from services.studio._text_to_workflow.activity_config import activity_config_dataset as acd
from services.studio._text_to_workflow.activity_config import activity_config_endpoint as ace
from services.studio._text_to_workflow.activity_config import activity_config_schema as acs
from services.studio._text_to_workflow.common import walkers
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils import yaml_utils as yu

_config = acc.get()
_activity_config_fixtures_path = conftest._fixtures_path / "activity_config_fixtures"
_build_demo_examples_cases_path = _activity_config_fixtures_path / "build_demo_examples_cases.yaml"
_build_eval_examples_cases_path = _activity_config_fixtures_path / "build_eval_examples_cases.yaml"
_build_context_cases_path = _activity_config_fixtures_path / "build_context_cases.yaml"
_endpoint_cases_path = _activity_config_fixtures_path / "endpoint_cases.yaml"
_endpoint_deprecated_cases_path = _activity_config_fixtures_path / "endpoint_deprecated_cases.yaml"
_build_demo_examples_cases = ("respond to emails",)
_build_eval_examples_cases = ("respond to emails for each email in outlook",)
_build_context_cases = (
    "01___00___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections",
    "01___01___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections",
    "01___02___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections",
    "01___03___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections",
)


@pytest.fixture(scope="module")
def build_activity_config_demo_examples_cases() -> dict:
    return yu.yaml_load(_build_demo_examples_cases_path)


@pytest.fixture(scope="module")
def build_activity_config_eval_examples_cases() -> dict:
    return yu.yaml_load(_build_eval_examples_cases_path)


@pytest.fixture(scope="module")
def build_activity_config_context_cases() -> dict:
    return yu.yaml_load(_build_context_cases_path)


@pytest.fixture(scope="module")
def activity_config_endpoint_cases() -> dict:
    return yu.yaml_load(_endpoint_cases_path)


@pytest.fixture(scope="module")
def activity_config_endpoint_deprecated_cases() -> dict:
    return yu.yaml_load(_endpoint_deprecated_cases_path)


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _build_demo_examples_cases)
async def test_build_activity_config_demo_examples(case: str, build_activity_config_demo_examples_cases: dict):
    workflow_generation_example, expected = (
        build_activity_config_demo_examples_cases[case]["input"],
        build_activity_config_demo_examples_cases[case]["expected"],
    )
    result = await get_activity_config_demo_examples_testable_result(workflow_generation_example)
    assert result == expected


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _build_eval_examples_cases)
async def test_build_activity_config_eval_examples(case: str, build_activity_config_eval_examples_cases: dict):
    workflow_generation_example, expected = (
        build_activity_config_eval_examples_cases[case]["input"],
        build_activity_config_eval_examples_cases[case]["expected"],
    )
    result = await get_activity_config_eval_examples_testable_result(workflow_generation_example)
    assert result == expected


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _build_context_cases)
async def test_build_activity_config_context(case: str, build_activity_config_context_cases: dict):
    activity_config_example, expected = build_activity_config_context_cases[case]["input"], build_activity_config_context_cases[case]["expected"]
    result = await get_activity_config_context_testable_result(activity_config_example)
    assert result == expected


async def get_activity_config_demo_examples_testable_result(workflow_generation_example: dict) -> list[acs.ActivityConfigExample]:
    return acd.create_activity_config_demo_examples(workflow_generation_example, "test")


async def get_activity_config_eval_examples_testable_result(activity_config_demo_example: acs.ActivityConfigExample) -> list[acs.ActivityConfigExample]:
    return acd.create_activity_config_eval_examples(activity_config_demo_example)


async def get_activity_config_context_testable_result(activity_config_input: acs.ActivityConfigInput) -> acs.ActivityConfigContext:
    context = walkers.BuildActivityConfigurationContext(activity_config_input, None).build()
    del context["activity"]["id"]  # Remove id because it is not deterministic
    return context


async def get_activity_config_endpoint_testable_result(
    activity_config_request: acs.ActivityConfigRequest, testable_param_keys: list[str] | None = None
) -> str | None:
    response = await ace.generate(activity_config_request)
    if response is not None:
        lmyaml = yu.yaml_load(response["result"])
        testable_params = lmyaml["params"]
        if testable_param_keys is not None:
            testable_params = {key: testable_params[key] for key in testable_param_keys}
        lmyaml_testable = {"activity": lmyaml["activity"], "params": testable_params}
        response = yu.multiline_str(yu.yaml_dump(lmyaml_testable))
    return response
