import pytest

from services.studio._text_to_workflow.models.model_manager import <PERSON><PERSON><PERSON><PERSON>
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import FIXYAML_TASK


@pytest.mark.asyncio
async def test_fix_yaml(fixyaml_testcases):
    for testcase in fixyaml_testcases:
        mode = testcase["mode"]
        workflow_str = testcase["workflow"]
        errors = testcase["errors"]
        model = ModelManager().get_llm_model("workflow_generation_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)
        predicted_worklow = (await FIXYAML_TASK.run(model, {"workflow": workflow_str, "errors": errors}, mode))["fixed_workflow"]
        expected_workflow = testcase["fixed_workflow"]
        assert predicted_worklow == expected_workflow
