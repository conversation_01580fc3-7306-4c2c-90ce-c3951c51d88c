import copy
from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump
from services.studio._text_to_workflow.workflow_fix import workflow_fix_endpoint, workflow_fix_schema
from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WORKFLOW_FIX_MODEL_NAME


@pytest.mark.asyncio
async def test_send_email_on_upload(send_email_on_onedrive_upload):
    workflow_as_str = yaml_dump(send_email_on_onedrive_upload)
    altered_workflow_str = workflow_as_str.replace("Result: '[[AddedFile]]'", "Result: '[[AddedFile'")

    request: workflow_fix_schema.WorkflowFixRequest = {
        "currentWorkflow": altered_workflow_str,
        "errors": ["System.NullReferenceException: Object reference not set to an instance of an object."],
        "availableAdditionalTypeDefinitions": "",
        "targetFramework": "Portable",
        "connections": None,
    }
    response = await workflow_fix_endpoint.fix_workflow(request)
    assert "reasoning" in response, "The response should contain a reasoning."
    assert "fixedWorkflow" in response, "The response should contain a fixed workflow."
    assert "Result: '[[AddedFile]]'" in response["fixedWorkflow"], "The fixed workflow should contain the fixed variable."


@pytest.mark.asyncio
async def test_analysis_only(send_email_on_onedrive_upload):
    # bloated_workflow = copy.deepcopy(send_email_on_onedrive_upload)
    # bloated_workflow["workflow"] = [copy.deepcopy(bloated_workflow["workflow"][0]) for _ in range(150)]
    # workflow_as_str = yaml_dump(bloated_workflow)
    workflow_as_str = yaml_dump(send_email_on_onedrive_upload)
    altered_workflow_str = workflow_as_str.replace("Result: '[[AddedFile]]'", "Result: '[[AddedFile'")

    request: workflow_fix_schema.WorkflowFixRequest = {
        "currentWorkflow": altered_workflow_str,
        "errors": ["System.NullReferenceException: Object reference not set to an instance of an object."],
        "availableAdditionalTypeDefinitions": "",
        "targetFramework": "Portable",
        "connections": None,
    }

    model = ModelManager().get_llm_model(WORKFLOW_FIX_MODEL_NAME, ConsumingFeatureType.WORKFLOW_FIX)
    model.max_model_tokens = 500
    with patch("services.studio._text_to_workflow.workflow_fix.workflow_fix_endpoint.TASK._get_model", return_value=(model, True)):
        response = await workflow_fix_endpoint.fix_workflow(request)
    assert "reasoning" in response, "The response should contain a reasoning."
    assert "fixedWorkflow" not in response, "The response should not contain a fixed workflow."


# @pytest.mark.asyncio
async def test_retry_on_diffing_exception(send_email_on_onedrive_upload):
    """Test that retry_full_rewrite_on_partial_generation_failure works correctly when encountering a DiffingException."""
    workflow_as_str = yaml_dump(send_email_on_onedrive_upload)
    altered_workflow_str = workflow_as_str.replace("Result: '[[AddedFile]]'", "Result: '[[AddedFile'")

    request: workflow_fix_schema.WorkflowFixRequest = {
        "currentWorkflow": altered_workflow_str,
        "errors": ["System.NullReferenceException: Object reference not set to an instance of an object."],
        "availableAdditionalTypeDefinitions": "",
        "targetFramework": "Portable",
        "connections": None,
    }

    # Mock for the replacement functions to raise DiffingException
    def mock_replacement_func(*args, **kwargs):
        from services.studio._text_to_workflow.utils.unidiff_utils import DiffingException

        raise DiffingException("Mock diffing error for testing")

    # Override the flair parameter in the run_fix_workflow method
    original_run_fix_workflow = workflow_fix_endpoint.TASK.run_fix_workflow

    async def run_with_modified_params(*args, **kwargs):
        # Set this to simulate the usage of a merge hybrid flair
        # It's being set only once such that it would not interfere with subsequent calls
        if not hasattr(run_with_modified_params, "called"):
            kwargs["flair"] = workflow_fix_schema.WorkflowFixFlairType.MergeHybrid
            run_with_modified_params.called = True
        return await original_run_fix_workflow(*args, **kwargs)

    original_config = workflow_fix_endpoint.TASK.config
    try:
        # Manually set retry temporarily
        mocked_config = copy.deepcopy(original_config)
        mocked_config["retry_full_rewrite_on_partial_generation_failure"] = True
        workflow_fix_endpoint.TASK.config = mocked_config

        # Use context managers to patch other functions
        with (
            patch("services.studio._text_to_workflow.workflow_fix.workflow_fix_task.replace_diff_with_yaml", side_effect=mock_replacement_func),
            patch("services.studio._text_to_workflow.workflow_fix.workflow_fix_task.replace_patch_with_yaml", side_effect=mock_replacement_func),
            patch("services.studio._text_to_workflow.workflow_fix.workflow_fix_task.replace_skip_to_the_good_bit_with_yaml", side_effect=mock_replacement_func),
            patch.object(workflow_fix_endpoint.TASK, "run_fix_workflow", side_effect=run_with_modified_params),
        ):
            response = await workflow_fix_endpoint.fix_workflow(request)
            assert "reasoning" in response, "The response should contain a reasoning."
            assert "fixedWorkflow" in response, "The response should contain a fixed workflow."
            assert "Result: '[[AddedFile]]'" in response["fixedWorkflow"], "The fixed workflow should contain the fixed variable."
    finally:
        # Restore the original config
        workflow_fix_endpoint.TASK.config = original_config
