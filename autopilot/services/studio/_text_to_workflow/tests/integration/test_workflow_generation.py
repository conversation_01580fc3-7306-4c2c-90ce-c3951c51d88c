import pytest

from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    GenerateWorkflowRequest,
    GenerateWorkflowRequestPydantic,
)


@pytest.mark.asyncio
async def test_send_email_on_upload(send_email_on_upload):
    request: GenerateWorkflowRequest = {"userRequest": send_email_on_upload["description"], "connections": [], "targetFramework": "Portable"}
    request_pydantic = GenerateWorkflowRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_workflow(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    assert predicted_workflow["trigger"]["activity"] == send_email_on_upload["trigger"]
    for i, predicted_node in enumerate(predicted_workflow["workflow"]):
        expected_node = send_email_on_upload["workflow"][i]
        assert predicted_node["activity"] == expected_node["activity"]


@pytest.mark.asyncio
async def test_deserializable_accessor():
    request: GenerateWorkflowRequest = {
        "userRequest": "Read the content of Sheet1 in an excel file into a DataTable. use modern excel and use an application card and a read range within it",
        "connections": [],
        "targetFramework": "Windows",
    }
    request_pydantic = GenerateWorkflowRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_workflow(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    assert predicted_workflow["workflow"][0]["activity"] == "UiPath.Excel.Activities.Business.ExcelApplicationCard"

    read_range_activity = predicted_workflow["workflow"][0]["params"]["Body"]["Handler"][0]
    assert read_range_activity["activity"] == "UiPath.Excel.Activities.Business.ReadRangeX"
    assert read_range_activity["params"]["Range"] in ('[[Excel.Sheet["Sheet1"]]]', '[[currentIWorkbookQuickHandle.Sheet["Sheet1"]]]')


@pytest.mark.asyncio
async def test_send_email_on_upload_apply_junk_filter(send_email_on_upload):
    request: GenerateWorkflowRequest = {"userRequest": send_email_on_upload["description"], "connections": [], "targetFramework": "Portable"}
    request_pydantic = GenerateWorkflowRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_workflow(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    assert predicted_workflow["trigger"]["activity"] == send_email_on_upload["trigger"]
    for i, predicted_node in enumerate(predicted_workflow["workflow"]):
        expected_node = send_email_on_upload["workflow"][i]
        assert predicted_node["activity"] == expected_node["activity"]


@pytest.mark.asyncio
async def test_workflow_generation_junk_filter():
    request: GenerateWorkflowRequest = {
        "userRequest": "Prepare me a pizza",
        "connections": [],
        "targetFramework": "Portable",
    }
    request_pydantic = GenerateWorkflowRequestPydantic.model_validate(request)

    out = (await workflow_generation_endpoint.generate_workflow(request_pydantic))["prompt_class"]
    assert out == "junk"
