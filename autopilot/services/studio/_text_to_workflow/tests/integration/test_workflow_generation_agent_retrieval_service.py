from unittest.mock import AsyncMock

import pytest

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.agent_fetcher import AgentFetcher
from services.studio._text_to_workflow.common.schema import AgentDefinition
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_agent_retrieval_service import (
    WorkflowGenerationAgentRetrievalService,
)

EMAIL_SCHEMA = {
    "type": "object",
    "properties": {
        "id": {"type": "string", "description": "Unique identifier of the email"},
        "subject": {"type": "string", "description": "Subject line of the email"},
        "body": {"type": "string", "description": "Content of the email"},
        "sender": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "Display name of the sender"},
                "email": {"type": "string", "description": "Email address of the sender"},
            },
            "required": ["email"],
        },
        "date": {"type": "string", "format": "date-time", "description": "Timestamp when the email was received"},
    },
    "required": ["id", "subject", "sender", "date"],
}


@pytest.fixture
def mock_agent_fetcher():
    """Fixture to provide a mock agent fetcher with predefined agents."""
    mock = AsyncMock(spec=AgentFetcher)
    mock.get_agents.return_value = [
        AgentDefinition(
            id=1,
            key="email_reader",
            name="EmailReader",
            description="Reads and processes emails from various email providers",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "provider": {"type": "string", "enum": ["Gmail", "Outlook", "Exchange"], "description": "Email provider to read from"},
                        "max_emails": {"type": "integer", "default": 10, "description": "Maximum number of emails to retrieve"},
                    },
                    "required": [],
                },
                "Output": {"type": "object", "properties": {"emails": {"type": "array", "items": EMAIL_SCHEMA}}},
            },
        ),
        AgentDefinition(
            id=2,
            key="email_analyzer",
            name="EmailAnalyzer",
            description="Analyzes email content for importance, sentiment, and potential threats",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "analysis_type": {
                            "type": "string",
                            "enum": ["importance", "sentiment", "phishing"],
                            "description": "Type of analysis to perform on the email",
                        },
                        "confidence_threshold": {"type": "number", "default": 0.8, "description": "Minimum confidence threshold for the analysis"},
                        "email": EMAIL_SCHEMA,
                    },
                    "required": ["email"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "result": {
                            "type": "object",
                            "properties": {"score": {"type": "number"}, "confidence": {"type": "number"}, "details": {"type": "string"}},
                        }
                    },
                },
            },
        ),
        AgentDefinition(
            id=3,
            key="salesforce_monitor",
            name="SalesforceMonitor",
            description="Monitors Salesforce for new account creation and related events",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "event_type": {"type": "string", "enum": ["account_created", "contact_created"], "description": "Type of Salesforce event to monitor"}
                    },
                    "required": ["event_type"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "events": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "type": {"type": "string"},
                                    "data": {
                                        "type": "object",
                                        "properties": {
                                            "account_name": {"type": "string", "description": "Name of the Salesforce account"},
                                            "industry": {"type": "string", "description": "Industry sector of the business"},
                                            "annual_revenue": {"type": "number", "description": "Annual revenue of the business"},
                                            "employee_count": {"type": "integer", "description": "Number of employees in the business"},
                                            "billing_address": {
                                                "type": "object",
                                                "properties": {
                                                    "street": {"type": "string"},
                                                    "city": {"type": "string"},
                                                    "state": {"type": "string"},
                                                    "country": {"type": "string"},
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        }
                    },
                },
            },
        ),
        AgentDefinition(
            id=4,
            key="email_generator",
            name="EmailGenerator",
            description="Sends personalized corporate emails using generative AI based on business details",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "recipient": {
                            "type": "object",
                            "properties": {
                                "email": {"type": "string", "description": "Email address of the recipient"},
                                "name": {"type": "string", "description": "Full name of the recipient"},
                                "title": {"type": "string", "description": "Job title of the recipient"},
                            },
                            "required": ["email", "name"],
                        },
                        "business": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "Name of the company"},
                                "industry": {"type": "string", "description": "Industry sector"},
                                "size": {"type": "string", "description": "Company size (e.g., small, medium, enterprise)"},
                                "location": {"type": "string", "description": "Company location"},
                            },
                            "required": ["name", "industry"],
                        },
                        "context": {"type": "string", "description": "Purpose or context of the email"},
                        "tone": {"type": "string", "enum": ["formal", "casual", "friendly", "professional"], "description": "Desired tone of the email"},
                    },
                    "required": ["recipient", "business", "context"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "message_id": {"type": "string", "description": "ID of the sent email message"},
                        "status": {"type": "string", "enum": ["sent", "failed"], "description": "Status of the email sending operation"},
                        "generated_content": {"type": "string", "description": "The AI-generated email content that was sent"},
                    },
                },
            },
        ),
        # Decoy agents
        AgentDefinition(
            id=5,
            key="email_archiver",
            name="EmailArchiver",
            description="Archives emails based on age, importance, and category",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "archive_criteria": {
                            "type": "object",
                            "properties": {
                                "age_days": {"type": "integer", "description": "Age in days after which to archive"},
                                "importance_threshold": {"type": "number", "description": "Minimum importance score to keep"},
                                "categories": {"type": "array", "items": {"type": "string"}, "description": "Categories to archive"},
                            },
                            "required": ["age_days"],
                        }
                    },
                    "required": ["archive_criteria"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "archived_count": {"type": "integer", "description": "Number of emails archived"},
                        "failed_count": {"type": "integer", "description": "Number of emails that failed to archive"},
                    },
                },
            },
        ),
        AgentDefinition(
            id=6,
            key="email_categorizer",
            name="EmailCategorizer",
            description="Automatically categorizes emails based on content and sender",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "email": EMAIL_SCHEMA,
                        "categories": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of possible categories",
                        },
                    },
                    "required": ["email", "categories"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "category": {"type": "string", "description": "Assigned category"},
                        "confidence": {"type": "number", "description": "Confidence score of the categorization"},
                        "keywords": {"type": "array", "items": {"type": "string"}, "description": "Keywords that influenced the categorization"},
                    },
                },
            },
        ),
        AgentDefinition(
            id=7,
            key="salesforce_contact_sync",
            name="SalesforceContactSync",
            description="Synchronizes contact information between email and Salesforce",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "email": EMAIL_SCHEMA,
                        "sync_direction": {
                            "type": "string",
                            "enum": ["to_salesforce", "from_salesforce"],
                            "description": "Direction of synchronization",
                        },
                    },
                    "required": ["email", "sync_direction"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "sync_status": {"type": "string", "enum": ["success", "failed"], "description": "Status of the synchronization"},
                        "updated_fields": {"type": "array", "items": {"type": "string"}, "description": "Fields that were updated"},
                    },
                },
            },
        ),
        AgentDefinition(
            id=8,
            key="email_template_manager",
            name="EmailTemplateManager",
            description="Manages and organizes email templates for different business scenarios",
            arguments_schema={
                "Input": {
                    "type": "object",
                    "properties": {
                        "action": {
                            "type": "string",
                            "enum": ["create", "update", "delete", "list"],
                            "description": "Action to perform on templates",
                        },
                        "template": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "Template name"},
                                "content": {"type": "string", "description": "Template content"},
                                "variables": {"type": "array", "items": {"type": "string"}, "description": "Required variables"},
                            },
                        },
                    },
                    "required": ["action"],
                },
                "Output": {
                    "type": "object",
                    "properties": {
                        "status": {"type": "string", "enum": ["success", "failed"], "description": "Operation status"},
                        "template_id": {"type": "string", "description": "ID of the affected template"},
                    },
                },
            },
        ),
    ]
    return mock


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query, expected_agents",
    [
        (
            "Get the last email and evaluate the importance of the email",
            {"EmailReader", "EmailAnalyzer"},
        ),
        (
            "Whenever a new salesforce account is created, send a jovial, personalized email to the customer, based on their name and business details",
            {"SalesforceMonitor", "EmailGenerator"},
        ),
        (
            "Determine whether the last email is phishing or not",
            {"EmailReader", "EmailAnalyzer"},
        ),
        (
            "Scan the the emails received in the last month and archive unimportant ones, as well as sync any important senders with the  Salesforce contacts",
            {"EmailReader", "EmailAnalyzer", "SalesforceContactSync", "EmailArchiver"},
        ),
    ],
)
async def test_generate_relevant_agents(
    mock_agent_fetcher: AsyncMock,
    query: str,
    expected_agents: set[str],
):
    """Test that generate_relevant_agents returns appropriate agents for different queries."""
    activities_retriever = ActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, embedding_model)

    service = WorkflowGenerationAgentRetrievalService(
        mock_agent_fetcher,
        activities_retriever,
        connection_embeddings_retriever,
    )

    context = AsyncMock()
    result = await service.generate_relevant_agents(
        query=query,
        context=context,
        connections=[],
    )

    assert result is not None
    retrieved_agent_names = {agent.name for agent in result.retrieved_agents}
    assert retrieved_agent_names == expected_agents
    assert result.reasoning is not None
    assert len(result.reasoning) > 0

    mock_agent_fetcher.get_agents.assert_called_once_with(context)
