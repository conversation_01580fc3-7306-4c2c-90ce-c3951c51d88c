import json

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflowDataPoint
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils.json_utils import json_load_from_path

_api_draft_fixtures_path = conftest._fixtures_path / "api_draft_fixtures"
_dummy_demonstrations_path = _api_draft_fixtures_path / "demonstrations.json"
_dummy_dap_config_map_path = _api_draft_fixtures_path / "dummy_dap_config_map.json"


def load_api_workflow_datapoints() -> list[ApiWorkflowDataPoint]:
    data: dict = json_load_from_path(_dummy_demonstrations_path)
    return [ApiWorkflowDataPoint.model_validate(demo) for demo in data.values()]


def load_raw_api_workflow_datapoint(demo_name: str = "CancelLatestTimeOffRequest") -> str:
    """Load a raw API workflow datapoint as a JSON string from the demonstrations.json file"""
    data: dict = json_load_from_path(_dummy_demonstrations_path)
    return json.dumps(data[demo_name]["solution_workflow"])


def mock_dynamic_activities_config_map() -> dict[str, dict]:
    """Load a dummy DAP config map"""
    with open(_dummy_dap_config_map_path, "r") as f:
        mock_activities_config = json.loads(f.read())

    return mock_activities_config
