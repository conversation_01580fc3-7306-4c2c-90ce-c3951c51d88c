from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent


def test_workflow_get_variables(workflow_postprocessing_input):
    for wf_input in workflow_postprocessing_input:
        workflow_valid_dict = wf_input["workflowValidDict"]
        used_variables, _, _ = workflow_generation_endpoint.TASK.postprocess_component.get_used_variables(workflow_valid_dict, {}, [], "Portable")
        expected_variables = wf_input["expectedVariables"]

        assert len(used_variables) == len(expected_variables)
        for variable in used_variables:
            assert variable["name"] in expected_variables
            assert variable["type"] == expected_variables[variable["name"]]


def test_workflow_get_used_jit_types(used_types_input):
    used_trigger_and_activities = used_types_input["used_trigger_and_activities"]
    jit_types = used_types_input["jit_types"]
    activities_config_map = used_types_input["activities_config_map"]

    used_jit_types = WorkflowGenerationPostProcessComponent.get_used_jit_types(used_trigger_and_activities, jit_types, activities_config_map)

    # assert duplicate jit configurations are removed
    expected_jit_types = used_types_input["expectedJitTypes"]
    assert len(expected_jit_types) == len(used_jit_types)
    for jit_type in used_jit_types:
        assert jit_type["EntityAssemblyKey"]["AssemblyName"] in expected_jit_types
