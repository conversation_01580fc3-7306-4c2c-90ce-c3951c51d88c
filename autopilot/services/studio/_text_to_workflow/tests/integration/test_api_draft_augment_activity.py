from unittest.mock import patch

import numpy as np
import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.schema import (
    ConnectorIntegration,
)
from services.studio._text_to_workflow.tests.integration.utils.dynamic_activities_mocks import (
    mock_dynamic_activities_config_map,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivitiesProposal, APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ActivityRetrievalGeneration


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ["query", "activities_names"],
    [
        [
            "Use the GitHubCreateIssue activity to create an issue in GitHub",
            [
                "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRChange_Time_off_Request_Status",
                "UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User",
                "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests",
                "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_TeamsSend_Channel_Message",
                "UiPath.IntegrationService.Activities.Runtime.Activities.ServiceNowCreate_New_Incident",
                "UiPath.IntegrationService.Activities.Runtime.Activities.GitHubCreate_Issue",
            ],
        ],
    ],
)
async def test_augment_json_schema_activity(query: str, activities_names: list[str], api_activities_retriever: APIActivitiesRetriever):
    post_gen_processing_service = ApiWfPostGenerationProcessingService(api_activities_retriever)
    draft_service = ApiWfDraftService(api_activities_retriever, post_gen_processing_service)

    # we will mock the activity retriever to return null instead of json schema and other related properties
    def mock_get(activity_name, _):
        activity_def = api_activities_retriever.get(activity_name, "activity")
        if activity_def is None:
            return None
        activity_def["jsonSchema"] = None
        activity_def["inputPropertiesJsonSchema"] = None
        activity_def["outputTypeJsonSchema"] = None
        activity_def["httpMethod"] = None
        return activity_def

    config_map = mock_dynamic_activities_config_map()
    with (
        patch.object(draft_service, "activities_retriever", autospec=True) as mock_activities_retriever,
        patch.object(WorkflowGenerationDynamicActivitiesComponent, "augment_type_definitions_for_dynamic_activities") as mock_augment,
    ):
        mock_activities_retriever.get.side_effect = mock_get
        mock_augment.return_value = ({}, config_map)
        activity_retrieval_result: APIActivityRetrievalResult = APIActivityRetrievalResult(
            retrieved_activities=activities_names,
            demonstrations=[],
            generation=ActivityRetrievalGeneration(
                plan="",
                ambiguities="",
                score=0,
                triggers=[],
                activities=[],
                inexistentActivities=[],
                inexistentTriggers=[],
            ),
            ignored_activities=set(),
            proposed_activities=[],
            prompt="",
            token_usage=TokenUsage(
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0,
            ),
            unprocessed_retrieved_activities=[],
            query_embedding=np.array([]),
            connections_embedding=np.array([]),
            connections_by_key={},
            generation_details=ActivitiesProposal(
                query_proposal_activities=[],
                workflow_proposal_activities=[],
                query_proposal_triggers=[],
                workflow_proposal_triggers=[],
            ),
            raw_response="",
        )
        result = await draft_service.generate_workflow_draft(
            workflow=None,
            mode="workflow",
            query=query,
            connections=[],
            expression_language="js",
            activity_retrieval_result=activity_retrieval_result,
        )

        assert result.api_workflow_details is not None
        assert result.api_workflow_details.generated_workflow is not None
        assert result.api_workflow_details.generated_workflow.root is not None
        assert result.api_workflow_details.generated_workflow.root.do is not None
        assert isinstance(result.api_workflow_details.generated_workflow.root.do[0], ConnectorIntegration)

    # check that the activity definitions have the correct json schema, input properties json schema, output type json schema, and http method
    for activity in result.activity_defs:
        assert activity["jsonSchema"] == config_map[activity["fullClassName"]]["jsonSchema"]
        assert activity["inputPropertiesJsonSchema"] == config_map[activity["fullClassName"]]["inputPropertiesJsonSchema"]
        assert activity["outputTypeJsonSchema"] == config_map[activity["fullClassName"]]["outputTypeJsonSchema"]
        assert activity["httpMethod"] == config_map[activity["fullClassName"]]["httpMethod"]
