import pytest

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint

ACTIVITIES = [
    "UiPath.GSuite.Activities.GetEmailListConnections",
    "UiPath.GSuite.Activities.ForEachEmailConnections",
    "UiPath.Core.Activities.Assign",
]
WORKFLOW_RAW_YAML = """```yaml
- 'thought': 'Get All Unread Emails from Gmail'
  'activity': 'UiPath.GSuite.Activities.GetEmailListConnections'
  'params':
    'Folder':
      'ConnectionKey': ''
      'ConnectionDescriptor': ''
      'Folder':
        'BrowserFolderId': 'Inbox'
        'BrowserFolderName': ''
      'FolderInputSelectionMode': 'Browse'
      'IncludeSubfolders': 'False'
      'MaxResults': '100'
      'UnreadOnly': 'True'
      'WithAttachmentsOnly': 'False'
      'ImportantOnly': 'False'
      'StarredOnly': 'False'
      'MarkAsRead': 'False'
      'Filter': {}
      'EmailList': '[[RetrievedEmails]]'
      'ManualEntryFolderName': ''
      'BrowserFolder': 'Inbox'
- 'thought': 'For Each Email in Gmail'
  'activity': 'UiPath.GSuite.Activities.ForEachEmailConnections'
  'params':
    'Folder':
      'ConnectionKey': ''
      'ConnectionDescriptor': ''
      'Folder':
        'BrowserFolderId': 'Inbox'
        'BrowserFolderName': ''
      'FolderInputSelectionMode': 'Browse'
      'IncludeSubfolders': 'False'
      'MaxResults': '100'
      'UnreadOnly': 'True'
      'WithAttachmentsOnly': 'False'
      'ImportantOnly': 'False'
      'StarredOnly': 'False'
      'MarkAsRead': 'False'
      'Filter': {}
      'Length': '[[summaries.Count]]'
      'ManualEntryFolderName': ''
      'BrowserFolder': 'Inbox'
    'Body':
      'variables':
      - 'name': 'current.Email'
        'type': 'GmailMessage'
      'Handler':
      - 'thought': 'Create a variable to store the generated text'
        'activity': 'UiPath.Core.Activities.Assign<System.String>'
        'params':
          'To': '[[generateText.text]]'
          'Value': '[["Hello"]]'
      - 'thought': 'Add summary to summaries list'
        'activity': 'UiPath.Core.Activities.Assign<System.String>'
        'params':
          'To': '[[summaries]]'
          'Value': '[[summaries.Append(generateText.text)]]'
```"""


@pytest.mark.asyncio
async def test_variable_name_replacement():
    activities = []
    for activity_id in ACTIVITIES:
        activities.append(workflow_generation_endpoint.TASK.activities_retriever.get(activity_id, "Portable", "activity"))
    model = ModelManager().get_llm_model("workflow_generation_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)
    workflow_raw_yaml, _ = await workflow_generation_endpoint.TASK.postprocess_component.postprocess_sequence_generation(model, WORKFLOW_RAW_YAML, "", False)

    result = await workflow_generation_endpoint.TASK.postprocess_component.postprocess_generation(
        model=model,
        workflow_raw_yaml=workflow_raw_yaml,
        query="Get All Unread Emails from Gmail",
        jit_types={},
        activities_config_map={},
        connections_by_key={},
        target_framework="Portable",
        objects=[],
        activities_and_triggers=activities,
        variables=[],
        localization="en",
        allow_triggers=False,
        allow_assign_activity_on_uia_expansion=False,
    )
    used_variable_names = [var["name"] for var in result["used_variables"]]
    used_local_variable_names = [var["name"] for var in result["used_local_variables"]]

    assert "generateText.text" not in result["workflow_valid"]
    assert "current.Email" not in result["workflow_valid"]
    assert used_variable_names == ["generateText_text", "summaries"]
    assert used_local_variable_names == ["current_Email"]
