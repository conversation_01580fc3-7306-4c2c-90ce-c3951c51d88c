import pytest

from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionLanguageType
from services.studio._text_to_workflow.expression_generation.expression_generation_task import ExpressionGenerationTask
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

TASK = ExpressionGenerationTask("prompt.yaml", "demos.yaml")
TASK_JS_INVOKE = ExpressionGenerationTask("js_invoke/config.yaml", "js_invoke/examples.yaml")


@pytest.mark.asyncio
async def test_fix_expression(fix_expression_cases):
    fix_expression_cases["seed"] = 42
    response = await expression_generation_endpoint.fix_expression(fix_expression_cases)
    result = yaml_load(response["result"])
    result_expression = result["expression"]
    assert result_expression == fix_expression_cases["benchmarkExpression"]


def test_demonstrations_retrieval():
    csharp_demos = TASK.get_demonstrations("fix_expression", ExpressionLanguageType.CSharp.value)
    assert len(csharp_demos) > 0
    vb_demos = TASK.get_demonstrations("fix_expression", ExpressionLanguageType.VBNET.value)
    assert len(vb_demos) > 0
    jq_demos = TASK.get_demonstrations("fix_expression", ExpressionLanguageType.JQ.value)
    assert len(jq_demos) > 0
    js_demos = TASK.get_demonstrations("fix_expression", ExpressionLanguageType.JavaScript.value)
    assert len(js_demos) > 0
    js_invoke_demos = TASK_JS_INVOKE.get_demonstrations("fix_expression", ExpressionLanguageType.JavaScript.value)
    assert len(js_invoke_demos) > 0
