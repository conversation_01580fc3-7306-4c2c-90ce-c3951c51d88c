import pytest
from nltk.translate.bleu_score import sentence_bleu as bleu

from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask

TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")
BLEU_THRESHOLD = 0.8


@pytest.mark.asyncio
async def test_translate_text_single(translate_single):
    feature = ConsumingFeatureType.SEQUENCE_GENERATION
    translation_result = await TRANSLATE_TASK.translate_single_str({"input_string": translate_single["text"], "feature": feature, "target_language": "de"})
    assert bleu(translate_single["references"], translation_result) > BLEU_THRESHOLD


@pytest.mark.asyncio
async def test_translate_text_multi(translate_multi):
    feature = ConsumingFeatureType.SEQUENCE_GENERATION
    translation_result = await TRANSLATE_TASK.translate_multi_str({"input_strings": translate_multi["texts"], "feature": feature, "target_language": "de"})
    for i in range(len(translate_multi["texts"])):
        assert bleu(translate_multi["references"][i], translation_result[i]) > BLEU_THRESHOLD
