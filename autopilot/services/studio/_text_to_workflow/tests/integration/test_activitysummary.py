import copy
from typing import Any, Dict
from unittest.mock import patch

import pytest
from nltk.translate.bleu_score import sentence_bleu as bleu

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

BLEU_THRESHOLD_YAML = 0.6
BLEU3_WEIGHTS = (1.0 / 3.0, 1.0 / 3.0, 1.0 / 3.0)


def _assert_valid_summarization_response(response):
    assert isinstance(response["result"]["workflowShortDescription"], str)
    assert isinstance(response["result"]["workflowSummary"], str)
    assert isinstance(response["result"]["workflowFileName"], str)
    assert isinstance(response["result"]["activityDescriptions"], list)
    for activity_description in response["result"]["activityDescriptions"]:
        assert isinstance(activity_description["id"], str)
        assert isinstance(activity_description["description"], str)
    assert isinstance(response["result"]["activitySummaries"], list)
    for activity_summary in response["result"]["activitySummaries"]:
        assert isinstance(activity_summary["id"], str)
        assert isinstance(activity_summary["summary"], str)


@pytest.mark.asyncio
async def test_log_person_data(log_person_data: Dict[str, Any]) -> None:
    request: ActivitySummaryRequest = {
        "activity": yaml_dump(log_person_data["input"]),
        "seed": 42,
        "deterministic_mode": True,
    }
    response = await activity_summary_endpoint.summarize(request)

    _assert_valid_summarization_response(response)


@pytest.mark.asyncio
async def test_log_person_data_localized(log_person_data_localized):
    request: ActivitySummaryRequest = {
        "activity": yaml_dump(log_person_data_localized["input"]),
        "seed": 42,
        "deterministic_mode": True,
    }
    response = await activity_summary_endpoint.summarize(request, "de")
    actual = response["result"]
    expected = log_person_data_localized["output"]

    assert actual != {}
    assert "activityDescriptions" in actual
    assert "activitySummaries" in actual
    assert "workflowShortDescription" in actual
    assert "workflowFileName" in actual
    assert "workflowSummary" in actual

    assert len(actual["activityDescriptions"]) == 1
    assert (
        _bleu3(
            expected["activityDescriptions"],
            actual["activityDescriptions"][0]["description"],
        )
        > BLEU_THRESHOLD_YAML
    ), actual["activityDescriptions"][0]["description"]

    assert len(actual["activitySummaries"]) == 1
    assert _bleu3(expected["activitySummaries"], actual["activitySummaries"][0]["summary"]) > BLEU_THRESHOLD_YAML, actual["activitySummaries"][0]["summary"]

    assert _bleu3(expected["workflowShortDescription"], actual["workflowShortDescription"]) > BLEU_THRESHOLD_YAML, actual["workflowShortDescription"]
    assert _bleu3(expected["workflowFileName"], actual["workflowFileName"]) > BLEU_THRESHOLD_YAML, actual["workflowFileName"]
    assert _bleu3(expected["workflowSummary"], actual["workflowSummary"]) > BLEU_THRESHOLD_YAML, actual["workflowSummary"]


def _bleu3(ref, hyp) -> float:
    return bleu(ref, hyp, weights=BLEU3_WEIGHTS)  # type: ignore


@pytest.mark.asyncio
async def test_activity_summary_overflow(log_person_data: Dict[str, Any]) -> None:
    # around 700 would be needed for this example
    with patch("services.studio._text_to_workflow.activity_summary.activity_summary_endpoint.TASK._get_prompt_tokens_limit", return_value=500):
        with pytest.raises(errors.PromptOverflowError):
            await activity_summary_endpoint.summarize({"activity": yaml_dump(log_person_data["input"]), "seed": 42})


@pytest.mark.asyncio
async def test_activity_summary_collapsing(log_person_data: Dict[str, Any], workflow_to_prune) -> None:
    before, _, after = workflow_to_prune["process"]["workflow"]
    left = [copy.deepcopy(before) | {"thought": f"Log Message before {i}"} for i in range(10)]
    right = [copy.deepcopy(after) | {"thought": f"Log Message after {i}"} for i in range(10)]

    workflow = copy.deepcopy(log_person_data["input"])
    workflow["workflow"] = left + workflow["workflow"] + right

    request: ActivitySummaryRequest = {
        "activity": yaml_dump(workflow),
        "seed": 42,
    }
    # should start collapsing from 2900+ tokens to around 2500+ tokens
    with patch("services.studio._text_to_workflow.activity_summary.activity_summary_endpoint.TASK._get_prompt_tokens_limit", return_value=3000):
        response = await activity_summary_endpoint.summarize(request)
    _assert_valid_summarization_response(response)


@pytest.fixture()
def integration_service_freshdesk_fixture(activity_summary_fixtures_path):
    return yaml_load(activity_summary_fixtures_path / "integration_service_freshdesk_workflow.yaml")


@pytest.mark.asyncio
async def test_integration_service_translation(integration_service_freshdesk_fixture):
    request: ActivitySummaryRequest = {
        "activity": yaml_dump(integration_service_freshdesk_fixture),
        "seed": 42,
    }
    response = await activity_summary_endpoint.summarize(request)
    generations: list[str] = []
    generations.append(response["result"]["activityDescriptions"][0]["description"])
    generations.append(response["result"]["activitySummaries"][0]["summary"])
    generations.append(response["result"]["workflowFileName"])
    generations.append(response["result"]["workflowShortDescription"])
    generations.append(response["result"]["workflowSummary"])
    n_freshdesk_generations = sum(["freshdesk" in generation.lower() for generation in generations])
    assert n_freshdesk_generations >= 3, generations
