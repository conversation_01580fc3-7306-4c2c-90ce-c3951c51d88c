import pytest

from services.studio._text_to_workflow.testdata_generation import testdata_generation_endpoint
from services.studio._text_to_workflow.testdata_generation.testdata_generation_schema import TestDataGenerationRequest
from services.studio._text_to_workflow.utils.workflow_utils import load_completion_yaml
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@pytest.fixture
def input(request):
    return request.getfixturevalue(request.param)


@pytest.mark.parametrize(
    "input",
    ["testdata_generation_input_simple_function", "testdata_generation_input_function_with_multiple_params", "testdata_generation_input_yaml"],
    indirect=True,
)
@pytest.mark.asyncio
async def test_check_required_number_test_data_generation(input):
    parameters = input["parameters"]
    number_of_tests = input["number_of_tests"]
    request = TestDataGenerationRequest(
        parameters=input["parameters"],
        userRequest=input["user_request_first_time"],
        seed=input["seed"],
        implementation=input["implementation"],
        implementationFormat=input["implementation_format"],
    )

    result = await testdata_generation_endpoint.generate(request)
    result_yaml = yaml_load(result["output"])

    assert "test_data" in result_yaml
    assert len(result_yaml["test_data"]) == number_of_tests

    for testdata in result_yaml["test_data"]:
        for parameter in parameters:
            assert parameter["name"] in testdata


@pytest.mark.parametrize(
    "input",
    ["testdata_generation_input_simple_function", "testdata_generation_input_function_with_multiple_params", "testdata_generation_input_yaml"],
    indirect=True,
)
@pytest.mark.asyncio
async def test_check_current_data_test_data_generation(input):
    parameters = input["parameters"]
    current_data = load_completion_yaml(input["current_data"])["test_data"]

    request = TestDataGenerationRequest(
        parameters=parameters,
        userRequest=input["user_request_second_time"],
        seed=input["seed"],
        implementation=input["implementation"],
        implementationFormat=input["implementation_format"],
        currentData=input["current_data"],
    )

    result = await testdata_generation_endpoint.generate(request)
    result_yaml = yaml_load(result["output"])

    assert "test_data" in result_yaml

    for testdata in result_yaml["test_data"]:
        for parameter in parameters:
            assert parameter["name"] in testdata
        assert testdata not in current_data
