import json
from unittest.mock import patch

import numpy as np
import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflow
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.tests.integration.utils.dynamic_activities_mocks import (
    load_api_workflow_datapoints,
    mock_dynamic_activities_config_map,
)
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivitiesProposal, APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ActivityRetrievalGeneration

_api_draft_fixtures_path = conftest._fixtures_path / "api_draft_fixtures"
_api_draft_query_samples_path = _api_draft_fixtures_path / "query_samples.yaml"

_query_samples = ("basic_api_workflow", "api_integration_activity_workflow", "response_activity_workflow", "basic_skip_to_the_good_bit_edit_scenario")


@pytest.fixture(scope="module")
def api_draft_query_samples() -> dict:
    return yu.yaml_load(_api_draft_query_samples_path)


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _query_samples)
async def test_api_draft_service(case: str, api_draft_query_samples: dict, api_activities_retriever: APIActivitiesRetriever):
    test_data: dict = api_draft_query_samples[case]

    post_gen_processing_service = ApiWfPostGenerationProcessingService(api_activities_retriever)
    draft_service = ApiWfDraftService(api_activities_retriever, post_gen_processing_service)

    # Get connections data
    _, connections = get_connections_data()

    # Now also mock the augment_type_definitions_for_dynamic_activities method

    with patch.object(WorkflowGenerationDynamicActivitiesComponent, "augment_type_definitions_for_dynamic_activities") as mock_augment:
        # Set the return value to match the expected tuple format (jit_types, activities_config_map)
        mock_augment.return_value = ({}, mock_dynamic_activities_config_map())

        activity_retrieval_result: APIActivityRetrievalResult = APIActivityRetrievalResult(
            retrieved_activities=test_data["retrieved_activities"],
            demonstrations=load_api_workflow_datapoints(),
            generation=ActivityRetrievalGeneration(
                plan="",
                ambiguities="",
                score=0,
                triggers=[],
                activities=[],
                inexistentActivities=[],
                inexistentTriggers=[],
            ),
            ignored_activities=set(),
            proposed_activities=[],
            prompt="",
            token_usage=TokenUsage(
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0,
            ),
            unprocessed_retrieved_activities=[],
            query_embedding=np.array([]),
            connections_embedding=np.array([]),
            connections_by_key={},
            generation_details=ActivitiesProposal(
                query_proposal_activities=[],
                workflow_proposal_activities=[],
                query_proposal_triggers=[],
                workflow_proposal_triggers=[],
            ),
            raw_response="",
        )
        # if the test data has an existing workflow, we should use it
        existing_workflow = None
        if test_data["existing_workflow"]:
            raw_existing_workflow = json.loads(test_data["existing_workflow"])
            existing_workflow = ApiWorkflow.model_validate(raw_existing_workflow)

        # Generate workflow draft
        result = await draft_service.generate_workflow_draft(
            workflow=existing_workflow,
            mode=test_data["mode"],
            query=test_data["query"],
            connections=connections,
            expression_language="js",
            activity_retrieval_result=activity_retrieval_result,
        )

    # Check that the draft was generated successfully
    assert result is not None
    assert result.api_workflow_details is not None

    workflow = result.api_workflow_details.generated_workflow
    # Check that the raw workflow was generated
    assert workflow is not None

    for i, activity in enumerate(workflow.root.do):
        assert activity.activity == test_data["expected_solution_activities"][i]
