import pytest

from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionLanguageType, SourceType
from services.studio._text_to_workflow.expression_generation.expression_generation_task import ExpressionGenerationTask
from services.studio._text_to_workflow.tests.fixtures.expression_generation_fixtures.generate_expression_input import generate_expression_input
from services.studio._text_to_workflow.tests.utils.similarity_helper import text_similarity
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

TASK = ExpressionGenerationTask("prompt.yaml", "demos.yaml")
TASK_JS_INVOKE = ExpressionGenerationTask("js_invoke/config.yaml", "js_invoke/examples.yaml")


@pytest.mark.asyncio
@pytest.mark.parametrize("gen_expression_case", generate_expression_input)
async def test_compute_expression_individual(gen_expression_case):
    if gen_expression_case.get("source"):
        gen_expression_case["source"] = SourceType(gen_expression_case.get("source"))

    response = await expression_generation_endpoint.generate(gen_expression_case)
    result = yaml_load(response["result"])
    assert result["expression"] is not None
    assert result["explanation"] is not None
    # the responses changes up quite a bit with gemini, so we add text_similarity
    assert text_similarity(result["expression"].strip(), gen_expression_case["benchmarkExpression"][0]) > 0.5


def test_demonstrations_retrieval():
    csharp_demos = TASK.get_demonstrations("expression_generation", ExpressionLanguageType.CSharp.value)
    assert len(csharp_demos) > 0
    vb_demos = TASK.get_demonstrations("expression_generation", ExpressionLanguageType.VBNET.value)
    assert len(vb_demos) > 0
    jq_demos = TASK.get_demonstrations("expression_generation", ExpressionLanguageType.JQ.value)
    assert len(jq_demos) > 0
    js_demos = TASK.get_demonstrations("expression_generation", ExpressionLanguageType.JavaScript.value)
    assert len(js_demos) > 0
    js_invoke_demos = TASK_JS_INVOKE.get_demonstrations("expression_generation", ExpressionLanguageType.JavaScript.value)
    assert len(js_invoke_demos) > 0
