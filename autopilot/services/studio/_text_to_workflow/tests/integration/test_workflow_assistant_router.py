import json
import os
from typing import Any, Callable, Optional

import pytest
import pytest_asyncio

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_router_task import WorkflowRouterTask
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import RPAWorkflowAssistantRequest, UiPathEntities
from services.studio._text_to_workflow.tests.utils.similarity_helper import text_similarity
from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter

ROUTER_TASK = WorkflowRouterTask()
SIMILARITY_THRESHOLD = 0.45


# Create a dummy message emitter for testing
class DummyMessageEmitter(IMessageEmitter):
    async def emit_message_part(self, content: Any, message_id_delta: int) -> int:
        pass

    async def emit_message(self, content: Any, message_type: str = "message") -> int:
        pass

    async def emit_debug_message(self, content: Any | Callable[[], Any]) -> int | None:
        pass

    async def emit_error(self, content: Any, end_stream: bool = True) -> int:
        pass

    async def emit_completion(self, content: Optional[Any] = None) -> int:
        pass


async def run_scenario(test_data: dict):
    """Test the workflow router for a specific scenario with test data"""
    # Create a request from the test data
    request_data = test_data["request"]

    # Load expected response
    expected_response = test_data["expectedResponse"]

    # Create a request instance
    request = RPAWorkflowAssistantRequest(
        userRequest=request_data["userRequest"],
        messageHistory=request_data.get("messageHistory", []),
        projectDefinition=request_data["projectDefinition"],
        currentWorkflow=request_data.get("currentWorkflow"),
        currentWorkflowDesignerState=request_data.get("currentWorkflowDesignerState"),
        availableEntities=request_data.get("availableEntities", UiPathEntities()),
        attachments=request_data.get("attachments", []),
    )

    message_emitter = DummyMessageEmitter()

    # Process the request
    response, _ = await ROUTER_TASK._process_internal(request, message_emitter)

    # If the query is invalid, return the response directly
    if not response.valid:
        return response

    assert response.scenario == expected_response["scenario"], f"Expected scenario {expected_response['scenario']}, got {response.scenario}"
    assert response.valid == expected_response["valid"], f"Expected valid {expected_response['valid']}, got {response.valid}"

    if "followUp" in expected_response:
        assert response.followUp == expected_response["followUp"], f"Expected followUp {expected_response['followUp']}, got {response.followUp}"

    if "score" in expected_response:
        assert abs(response.score - expected_response["score"]) <= 1, f"Expected score {expected_response['score']}, got {response.score}"

    # Check the router correctly identified workflow references
    if expected_response.get("workflowRefs"):
        assert response.workflowRefs is not None, "Expected workflowRefs, got None"
        assert set(response.workflowRefs) == set(expected_response["workflowRefs"]), (
            f"Expected workflowRefs {expected_response['workflowRefs']}, got {response.workflowRefs}"
        )

    # Calculate semantic similarity for the thought
    thought_similarity = text_similarity(expected_response["thought"], response.thought)
    assert thought_similarity >= SIMILARITY_THRESHOLD, (
        f"Thought semantic similarity score {thought_similarity} is below threshold {SIMILARITY_THRESHOLD}. Response: {response.thought}"
    )

    # Calculate semantic similarity for the message
    message_similarity = text_similarity(expected_response["message"], response.message)
    assert message_similarity >= SIMILARITY_THRESHOLD, (
        f"Message semantic similarity score {message_similarity} is below threshold {SIMILARITY_THRESHOLD}. Response: {response.message}"
    )

    # Return response for additional checks if needed
    return response


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "fixture_file,scenario",
    [
        ("assistant_generate_workflow.json", "generate-workflow"),
        ("assistant_edit_activity.json", "edit-workflow"),
        ("assistant_edit_workflow.json", "edit-workflow"),
        ("assistant_edit_workflow_current.json", "edit-workflow"),
        ("assistant_append_workflow.json", "edit-workflow"),
        ("assistant_append_workflow_current.json", "edit-workflow"),
        ("assistant_fix_workflow.json", "fix-workflow"),
        ("assistant_analyze_workflow.json", "analyze-workflow"),
        ("assistant_entity_selection.json", "generate-workflow"),
        ("assistant_follow_up_question.json", "generate-workflow"),
        ("assistant_complex_query.json", "generate-workflow"),
        ("assistant_reference_messages.json", "generate-workflow"),
        ("assistant_invalid_query.json", "generate-workflow"),
        ("assistant_project_file_mention.json", "generate-workflow"),
        ("assistant_attachment_mention.json", "generate-workflow"),
        ("assistant_image_attachment.json", "generate-workflow"),
        ("assistant_non_english_query.json", "generate-workflow"),
        ("rewrite_workflow.json", "rewrite-workflow"),
    ],
)
async def test_workflow_router_scenarios(fixture_file, scenario, fixture_loader):
    """Test the workflow router for different scenarios"""
    # Load the fixture
    test_data = fixture_loader(fixture_file)

    # Run the test
    response = await run_scenario(test_data)

    # Check for specific behaviors in each scenario
    if fixture_file == "assistant_invalid_query.json":
        # Check for invalid query
        assert response.valid is False, "Invalid query should have valid set to false"
        return

    if scenario == "generate-workflow":
        if fixture_file == "assistant_follow_up_question.json":
            # Check that followUp is true and there's a question
            assert response.followUp is True, "Follow-up question should have followUp set to true"
            assert response.question is not None, "Follow-up question should have a question"

        elif fixture_file == "assistant_complex_query.json":
            # Check for high score
            assert response.score >= 4, "Complex query should have a high score"

        elif fixture_file == "assistant_non_english_query.json":
            # Verify the response is in the same language as the request (Italian in this case)
            assert "flusso" in response.message.lower(), "Response should be in Italian"
            assert "workflow" not in response.message.lower(), "Response should not contain English terms"


@pytest_asyncio.fixture
def fixture_loader(request):
    """Load a workflow fixture from the fixtures directory"""

    fixtures_dir = os.path.join(os.path.dirname(__file__), "..", "fixtures", "workflow_assistant_fixtures")

    def _load_fixture(filename):
        fixture_path = os.path.join(fixtures_dir, filename)
        with open(fixture_path, "r") as f:
            return json.load(f)

    return _load_fixture
