import asyncio
import json
import os
from dataclasses import dataclass
from typing import Any, Optional, cast

import pytest
from tqdm.asyncio import tqdm

from services.studio._text_to_workflow.assistants.workflow_assistant.api_workflow_assistant_endpoint import APIWorkflowAssistantEndpoint
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    APIWorkflowAssistantChangeResponse,
    APIWorkflowAssistantRequest,
)
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    ConnectorIntegrationWithMetadata,
    PostGenApiWorkflow,
    PostGenIf,
    PostGenTryCatch,
)
from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflow, HttpRequest, JsInvoke, Response
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import list_activities, list_activities_post_gen
from services.studio._text_to_workflow.common.prompt_utils import append_debug_runid_to_prompt
from services.studio._text_to_workflow.tests.utils.testable_message_emitter import TestableMessageEmitter
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import InvalidConnectionError

API_ROUTER = APIWorkflowAssistantEndpoint()


@pytest.mark.asyncio
async def test_generate_workflow_validation(run_id: str | None = None):
    """Test with an in-distribution prompt that we are properly able to generate expressions and code for an input validation scenario."""
    test_data = load_fixture("assistant_generate_workflow_validation.json")

    # The additional prompt for the router is needed to prevent caching.
    if run_id:
        test_data["request"]["userRequest"] = append_debug_runid_to_prompt(test_data["request"]["userRequest"], run_id)
    response, _ = await run_scenario(test_data, run_id)

    assert response.scenario in (["generate-workflow", "edit-workflow"]), (
        f"Expected scenario to be one of ['generate-workflow', 'edit-workflow'], got {response.scenario}"
    )
    assert response.newWorkflowContent is not None

    original_workflow = json.loads(test_data["request"]["currentWorkflow"]["content"])
    new_workflow_content = json.loads(response.newWorkflowContent)

    new_workflow = ApiWorkflow(input=json.dumps(new_workflow_content["input"]), root=new_workflow_content["root"])

    assert original_workflow["input"]["schema"]["document"] == new_workflow_content["input"]

    new_workflow_activities = list_activities(new_workflow)
    jsinvoke_activity_list: list[JsInvoke] = []
    response_activity_list: list[Response] = []
    for new_workflow_activity in new_workflow_activities:
        if new_workflow_activity.activity == "JsInvoke":
            jsinvoke_activity_list.append(cast(JsInvoke, new_workflow_activity))
        if new_workflow_activity.activity == "Response":
            response_activity_list.append(cast(Response, new_workflow_activity))

    assert len(jsinvoke_activity_list) >= 1
    assert len(response_activity_list) >= 1

    has_workflow_input_reference = False
    has_applicant_reference = False

    for jsinvoke_activity in jsinvoke_activity_list:
        assert jsinvoke_activity.code is not None
        assert jsinvoke_activity.code != ""
        assert not jsinvoke_activity.code.startswith("${")

        if "$workflow.input" in jsinvoke_activity.code:
            has_workflow_input_reference = True
        if ".applicant" in jsinvoke_activity.code:
            has_applicant_reference = True

    assert has_applicant_reference
    assert has_workflow_input_reference

    for response_activity in response_activity_list:
        if response_activity.type != "SUCCESS":
            assert response_activity.response_message is not None
            assert response_activity.response_message != ""

    assert response.errors is not None
    assert len(response.errors) == 0


@pytest.mark.asyncio
async def test_edit_workflow_salesforce(run_id: str | None = None):
    """Test with an in-distribution prompt that we are properly able to edit a workflow to add some Salesforce operations."""
    test_data = load_fixture("assistant_edit_workflow_salesforce.json")

    # The additional prompt for the router is needed to prevent caching.
    if run_id:
        test_data["request"]["userRequest"] = append_debug_runid_to_prompt(test_data["request"]["userRequest"], run_id)
    response, _ = await run_scenario(test_data, run_id)

    assert response.scenario == "edit-workflow", f"Expected scenario to be 'edit-workflow', got {response.scenario}"
    assert response.newWorkflowContent is not None

    original_workflow = json.loads(test_data["request"]["currentWorkflow"]["content"])
    new_workflow_content = json.loads(response.newWorkflowContent)

    new_workflow = PostGenApiWorkflow(input=new_workflow_content["input"], root=new_workflow_content["root"])

    assert original_workflow["input"]["schema"]["document"] == new_workflow_content["input"]

    new_workflow_activities = list_activities_post_gen(new_workflow)
    found_try_catch = False
    jsinvoke_activity_list: list[JsInvoke] = []
    response_activity_list: list[Response] = []
    connector_activity_list: list[ConnectorIntegrationWithMetadata] = []
    if_activity_list: list[PostGenIf] = []
    has_raw_http_request = False
    for new_workflow_activity in new_workflow_activities:
        if isinstance(new_workflow_activity, JsInvoke):
            jsinvoke_activity_list.append(new_workflow_activity)
        if isinstance(new_workflow_activity, Response):
            response_activity_list.append(new_workflow_activity)
        if isinstance(new_workflow_activity, PostGenTryCatch):
            found_try_catch = True
        if isinstance(new_workflow_activity, ConnectorIntegrationWithMetadata):
            connector_activity_list.append(cast(ConnectorIntegrationWithMetadata, new_workflow_activity))
        if isinstance(new_workflow_activity, PostGenIf):
            if_activity_list.append(cast(PostGenIf, new_workflow_activity))
        if isinstance(new_workflow_activity, HttpRequest):
            has_raw_http_request = True

    assert len(jsinvoke_activity_list) >= 2
    assert len(response_activity_list) >= 1
    assert len(if_activity_list) >= 1
    assert len(connector_activity_list) >= 3
    assert not has_raw_http_request

    for connector_activity in connector_activity_list:
        assert connector_activity.id in ["curated_soqlQuery_1", "curated_account_1", "curated_opportunity_1", "Salesforce_HTTP_Request_1"]

        if connector_activity.id == "curated_soqlQuery_1":
            assert connector_activity.metadata.method == "POST"
            assert connector_activity.with_.bodyParameters["query"] is not None
            assert connector_activity.with_.bodyParameters["query"] != "", (
                f"Expected query to be not empty, got {connector_activity.with_.bodyParameters['query']}"
            )
            assert "FROM Account" in connector_activity.with_.bodyParameters["query"], (
                f"Expected query to be a SOQL query, got {connector_activity.with_.bodyParameters['query']}"
            )

        if connector_activity.id == "curated_account_1":
            assert connector_activity.metadata.method == "GET"
            assert connector_activity.with_.pathParameters["curated_accountId"] == "${$context.outputs.curated_soqlQuery_1.content[0].Id}", (
                f"Expected AccountId to be the first result of the SOQL query, got {connector_activity.with_.pathParameters['curated_accountId']}"
            )

        if connector_activity.id == "curated_opportunity_1":
            assert connector_activity.metadata.method == "POST"
            assert (
                "$workflow.input" in connector_activity.with_.bodyParameters["Name"]
                or "$context.outputs.JsInvoke_" in connector_activity.with_.bodyParameters["Name"]
            ), f"Expected Name to be using a workflow input or a JSInvoke result, got {connector_activity.with_.bodyParameters['Name']}"
            assert '"' in connector_activity.with_.bodyParameters["StageName"], (
                f"Expected StageName to be a string, got {connector_activity.with_.bodyParameters['StageName']}"
            )
            assert "Date" in connector_activity.with_.bodyParameters["CloseDate"], (
                f"Expected CloseDate to be a date, got {connector_activity.with_.bodyParameters['CloseDate']}"
            )
            assert "$context.outputs.curated_" in connector_activity.with_.bodyParameters["AccountId"], (
                f"Expected AccountId to be a context output, got {connector_activity.with_.bodyParameters['AccountId']}"
            )
            assert "curated_account_Retrieve" not in connector_activity.with_.bodyParameters["AccountId"], (
                f"Expected to not incorrectly use curated_account_Retrieve, got {connector_activity.with_.bodyParameters['AccountId']}"
            )
            assert "$workflow.input.loanDetails.amount" in connector_activity.with_.bodyParameters["Amount"], (
                f"Expected Amount to be a workflow input, got {connector_activity.with_.bodyParameters['Amount']}"
            )

        if connector_activity.id == "Salesforce_HTTP_Request_1":
            assert connector_activity.metadata.method is None
            assert connector_activity.with_.connectionId is not None, (
                f"Expected HTTP Request connectionId to be not None, got {connector_activity.with_.connectionId}"
            )
            assert connector_activity.with_.bodyParameters["body"] is not None
            assert connector_activity.with_.bodyParameters["body"] != "", (
                f"Expected HTTP Request body to be not empty, got {connector_activity.with_.bodyParameters['body']}"
            )
            assert connector_activity.with_.bodyParameters["method"] == "${'POST'}", (
                f"Expected HTTP Request method to be POST, got {connector_activity.with_.bodyParameters['method']}"
            )
            assert connector_activity.with_.bodyParameters["url"] == '${"/services/data/v53.0/sobjects/Note"}', (
                f"Expected HTTP Request URL to be /services/data/v53.0/sobjects/Note, got {connector_activity.with_.bodyParameters['url']}"
            )

    has_workflow_input_reference = False
    has_applicant_reference = False

    for jsinvoke_activity in jsinvoke_activity_list:
        assert jsinvoke_activity.code is not None
        assert jsinvoke_activity.code != ""
        assert not jsinvoke_activity.code.startswith("${")

        if "$workflow.input" in jsinvoke_activity.code:
            has_workflow_input_reference = True
        if ".applicant" in jsinvoke_activity.code:
            has_applicant_reference = True

    assert has_applicant_reference
    assert has_workflow_input_reference
    assert found_try_catch

    for response_activity in response_activity_list:
        if response_activity.type != "SUCCESS":
            assert response_activity.response_message is not None
            assert response_activity.response_message != ""

    assert response.errors is not None
    non_connection_errors = [error for error in response.errors if not isinstance(error, InvalidConnectionError)]
    assert len(non_connection_errors) == 0, f"Expected no errors, got errors: {yaml_dump(non_connection_errors)}"


@dataclass
class TestResult:
    """Result of a test run with run_id"""

    run_id: str
    success: bool
    error_message: Optional[str] = None


async def run_validation_test(run_id: str) -> TestResult:
    """Run a validation test with a specific run_id"""
    try:
        await test_generate_workflow_validation(run_id)

        return TestResult(run_id=run_id, success=True)
    except Exception as e:
        return TestResult(run_id=run_id, success=False, error_message=str(e))


async def run_edit_workflow_test(run_id: str) -> TestResult:
    """Run an edit workflow test with a specific run_id"""
    try:
        await test_edit_workflow_salesforce(run_id)

        return TestResult(run_id=run_id, success=True)
    except Exception as e:
        return TestResult(run_id=run_id, success=False, error_message=str(e))


@pytest.mark.asyncio
@pytest.mark.flaky(retries=0)
async def test_generate_workflow_validation_parallel():
    """Test running 100 validation tests in parallel batches of 8"""
    # Total number of test runs
    total_runs = 0
    # Maximum concurrent tests
    max_concurrent = 8

    if total_runs == 0:
        return

    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrent)

    async def run_with_semaphore(run_id: int) -> TestResult:
        async with semaphore:
            return await run_validation_test("VALIDATION_" + str(run_id))

    # Schedule all test runs
    test_tasks = [run_with_semaphore(i) for i in range(1, total_runs + 1)]

    # Wait for all tests to complete with progress bar
    results = await tqdm.gather(*test_tasks, desc="Running generate workflow tests", total=total_runs)

    # Collect the failures
    failures = [result for result in results if not result.success]

    # Report results
    print(f"\nTest Summary: {len(results) - len(failures)}/{len(results)} tests passed")

    if failures:
        print(f"\nFailed Tests ({len(failures)}):")
        for failure in failures:
            print(f"Run #{failure.run_id} failed: {failure.error_message}")

    # Assert that all tests passed
    assert len(failures) == 0, f"{len(failures)} tests failed"


@pytest.mark.asyncio
@pytest.mark.flaky(retries=0)
async def test_edit_workflow_salesforce_parallel():
    """Test running 100 edit workflow tests in parallel batches of 8"""
    # Total number of test runs
    total_runs = 0
    # Maximum concurrent tests
    max_concurrent = 8

    if total_runs == 0:
        return

    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrent)

    async def run_with_semaphore(run_id: int) -> TestResult:
        async with semaphore:
            return await run_edit_workflow_test("EDIT_" + str(run_id))

    # Schedule all test runs
    test_tasks = [run_with_semaphore(i) for i in range(1, total_runs + 1)]

    # Wait for all tests to complete with progress bar
    results = await tqdm.gather(*test_tasks, desc="Running edit workflow tests", total=total_runs)

    # Collect the failures
    failures = [result for result in results if not result.success]

    # Report results
    print(f"\nTest Summary: {len(results) - len(failures)}/{len(results)} tests passed")

    if failures:
        print(f"\nFailed Tests ({len(failures)}):")
        for failure in failures:
            print(f"Run #{failure.run_id} failed: {failure.error_message}")

    # Assert that all tests passed
    assert len(failures) == 0, f"{len(failures)} tests failed"


def load_fixture(filename: str) -> dict[str, Any]:
    """Load a workflow fixture from the fixtures directory"""
    fixtures_dir = os.path.join(os.path.dirname(__file__), "..", "fixtures", "api_workflow_assistant_fixtures")
    fixture_path = os.path.join(fixtures_dir, filename)
    with open(fixture_path, "r") as f:
        return json.load(f)


async def run_scenario(test_data: dict, run_id: str | None = None) -> tuple[APIWorkflowAssistantChangeResponse, TestableMessageEmitter]:
    """Test the workflow router for a specific scenario with test data"""
    # Create a request from the test data
    request_data = test_data["request"]

    # Create a request instance
    request = APIWorkflowAssistantRequest(**request_data)
    if run_id:
        request.runId = run_id

    message_emitter = TestableMessageEmitter()

    # Process the request
    response: APIWorkflowAssistantChangeResponse = await API_ROUTER.perform_assistant_request(request, message_emitter)

    # If the query is invalid, return the response directly
    assert response.path == request_data["currentWorkflow"]["path"], f"Expected path {request_data['currentWorkflow']['path']}, got {response.path}"

    return response, message_emitter
