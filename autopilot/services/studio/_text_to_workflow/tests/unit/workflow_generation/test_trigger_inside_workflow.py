import copy

import pytest

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent

dummy_trigger_activity = yaml_load("""
thought: When envelope signed by recipient - using DocuSign
activity: UiPath.IntegrationService.Activities.Runtime.Activities.DocuSignEnvelope_Signed_by_All_Recipients
params: {}
""")

dummy_activity_config_object = yaml_load("""
UiPath.IntegrationService.Activities.Runtime.Activities.DocuSignEnvelope_Signed_by_All_Recipients:
  activityTypeId: 8dfc11f2-876a-35fb-af7d-4a0cbdda159f
  configuration: H4sIAAAAAAAAA81W627bNhR+FUJ//EeSI99jIAUaW2nc2YkRudmwoQgo6djhTJEqSbl1gwB9kO3l+iQ9tOVL4rrIgHVYAhjmuZDf9/GcQz84Fwx4qntSGMoEKKf74IQLEGZtd7p/PDhX1LAFTJY5OF1HG8XEzHGda5Xa8MBFf2Y9iQJqIO3jx4ShxXX6TOecLkt/b+0nKX4Ss454vumFVBk1aLFBXhk0onqOpiX+eaORl6aVSeXysptlXa39KIp+x5iB7hXKbr8D73SNKuDRfTEByCjj57xQ8QH20LpILNPld0D/a2dHRfwnJObI6Xrr/SkAxAK4zGGQ2uNBJ4rlhkmBLptLzD0QgaFEKjLoEzldWTZZPhk8NRCmiZCGTGUhUsLEypkqmXup/ChcspQFSaggU6a0IXia5PxpDJZIaYqlMTIjRpIZGEJLK12gKDTmuzM1oXgW+jB3A3mLB2ErQPaweOZAMlNlt0djXyZFxGaC0DznLKFWAOK9IpURFXQGFWJobNdvJIL5+uWvCOX++uVvuzmu+opOV0ukDCtLuEFmrZjXw03nRK7VUKBzvE+8nB0YS+DHMN/dDH2CbULgE81yDi5ha+XRYUXHU++NyXW3WkUSKWTST5GVRlZ+IrOqXWQIW1dTwJbnulqn0GpDs+FN263Aa9Bm7NFaHby4CY1Ovdk4rSUJ4ncP8OBplZckVw4LutzlJ9Uyp9qMZMqm7AfjaIhBJCuj/r9DSWPmUQ62/v5r6O/RKfJi90Cg4bow+5YH59k966MVoA+mzY7FOzam5t4fCAMzterFCNSCJeC/tm3DDAPE+2sozPprr3XaaoUn/ddBeLedCXcTxWYzUP45diUH/8Dhku/l+b/ljfBz/XNw3rsMopS/vQjS+qgTN1xyC0ojlrPAP7H/mF9wUyg4E1AYRblLxkWMjf4LLCdyDuJMFJwjqxv4UIDGy5hSrsFdv7xDuZ4ySHct4kr7t2xfcmsZ4et8gwNDCg0HhfRUUWzN43KXziOKR0ttIPOjVfm4pFyOFVtgKfg9qWDI4p0CnZcq0E4g6TTTdgy03T7twD9T4xj394+u05NZjL9b0gvGDajwU45TVW9pDYQ2VCQwpgp1wABtf9+U8DFkdYWO3UUIHMVSIWI0FyzHwvM2c3P7BSPLyluWgpVNUpaS7d/VG/3sTbVFvuqj6xzUhmB4dRsOr8fhXTR4cxX2NyE4t2zqGN9D28KWIk3uAaWR8yK/pRxVQxJb+7mSHzWoAV5UacfryqhaIpWyO8c0mePrtU8bb86Lcf77tZNa4+S0VveDYF+HksDmPdx37W8T+PXOlvTxBj8qypCKWYHQ7G09fgOxgb7yjAoAAA==
  actualActivityName: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity
  connectionId: 6fdca74c-5574-4d11-b8e3-a86966e0da1e
  connectorKey: uipath-docusign-docusign
  activityIsConfigured: true
  dynamicActivityDetails: '{"ActivityConfiguration":"H4sIAAAAAAAAA81W627bNhR+FUJ//EeSI99jIAUaW2nc2YkRudmwoQgo6djhTJEqSbl1gwB9kO3l+iQ9tOVL4rrIgHVYAhjmuZDf9/GcQz84Fwx4qntSGMoEKKf74IQLEGZtd7p/PDhX1LAFTJY5OF1HG8XEzHGda5Xa8MBFf2Y9iQJqIO3jx4ShxXX6TOecLkt/b+0nKX4Ss454vumFVBk1aLFBXhk0onqOpiX+eaORl6aVSeXysptlXa39KIp+x5iB7hXKbr8D73SNKuDRfTEByCjj57xQ8QH20LpILNPld0D/a2dHRfwnJObI6Xrr/SkAxAK4zGGQ2uNBJ4rlhkmBLptLzD0QgaFEKjLoEzldWTZZPhk8NRCmiZCGTGUhUsLEypkqmXup/ChcspQFSaggU6a0IXia5PxpDJZIaYqlMTIjRpIZGEJLK12gKDTmuzM1oXgW+jB3A3mLB2ErQPaweOZAMlNlt0djXyZFxGaC0DznLKFWAOK9IpURFXQGFWJobNdvJIL5+uWvCOX++uVvuzmu+opOV0ukDCtLuEFmrZjXw03nRK7VUKBzvE+8nB0YS+DHMN/dDH2CbULgE81yDi5ha+XRYUXHU++NyXW3WkUSKWTST5GVRlZ+IrOqXWQIW1dTwJbnulqn0GpDs+FN263Aa9Bm7NFaHby4CY1Ovdk4rSUJ4ncP8OBplZckVw4LutzlJ9Uyp9qMZMqm7AfjaIhBJCuj/r9DSWPmUQ62/v5r6O/RKfJi90Cg4bow+5YH59k966MVoA+mzY7FOzam5t4fCAMzterFCNSCJeC/tm3DDAPE+2sozPprr3XaaoUn/ddBeLedCXcTxWYzUP45diUH/8Dhku/l+b/ljfBz/XNw3rsMopS/vQjS+qgTN1xyC0ojlrPAP7H/mF9wUyg4E1AYRblLxkWMjf4LLCdyDuJMFJwjqxv4UIDGy5hSrsFdv7xDuZ4ySHct4kr7t2xfcmsZ4et8gwNDCg0HhfRUUWzN43KXziOKR0ttIPOjVfm4pFyOFVtgKfg9qWDI4p0CnZcq0E4g6TTTdgy03T7twD9T4xj394+u05NZjL9b0gvGDajwU45TVW9pDYQ2VCQwpgp1wABtf9+U8DFkdYWO3UUIHMVSIWI0FyzHwvM2c3P7BSPLyluWgpVNUpaS7d/VG/3sTbVFvuqj6xzUhmB4dRsOr8fhXTR4cxX2NyE4t2zqGN9D28KWIk3uAaWR8yK/pRxVQxJb+7mSHzWoAV5UacfryqhaIpWyO8c0mePrtU8bb86Lcf77tZNa4+S0VveDYF+HksDmPdx37W8T+PXOlvTxBj8qypCKWYHQ7G09fgOxgb7yjAoAAA==","Fields":[{"Id":"Envelopes","FieldType":1,"ClrType":"UiPath.IntegrationService.Activities.SWEntities.C6966E0DA1E_envelopes_Trigger.Bundle.envelopes_Trigger, C6966E0DA1E_envelope.Xp4Ez3z1BCH1SdlJF1d3M8b4, Version=*******, Culture=neutral, PublicKeyToken=null","Required":false,"VisibleByDefault":true},{"Id":"out_Envelopes_20_ID","FieldType":1,"ClrType":"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e","Required":false,"VisibleByDefault":true}],"Mappings":{"Connections":[{"ConnectorKey":"uipath-docusign-docusign","ConnectionId":"6fdca74c-5574-4d11-b8e3-a86966e0da1e","ConnectorKeyPropertyName":null,"ConnectionIdPropertyName":"ConnectionId"}]},"Arguments":null,"ManagedDynamicProperties":["Arguments","FieldObjects"]}'
""")


@pytest.mark.asyncio
async def test_placeholder_conversion_on_trigger_inside_workflow(activities_retriever: ActivitiesRetriever):
    trigger_activity = copy.deepcopy(dummy_trigger_activity)
    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activities_retriever)
    postprocessing_component = WorkflowGenerationPostProcessComponent(activities_retriever, dynamic_activities_component, {"Portable": set(), "Windows": set()})
    await postprocessing_component._postprocess_workflow_activity(
        node=trigger_activity,
        parent=[trigger_activity],  # type: ignore (during testing this was a list)
        activity_config=dummy_activity_config_object,
        # the following are not necessary for now; if needed, add the actual value
        jit_types={},
        name2fqn={},
        connections_by_key={},
        target_framework="Portable",
    )
    placeholder = activities_retriever.placeholder_activity()
    assert trigger_activity["activity"] == placeholder, "The trigger activity should be replaced with a placeholder activity"
