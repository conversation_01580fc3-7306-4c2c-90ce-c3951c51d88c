import random
from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import GenerateWorkflowRequest, GenerateWorkflowRequestPydantic


@pytest.mark.asyncio
async def test_demonstrations_ordering(send_email_on_upload, alter_order_of_demonstrations=False):
    original_method = workflow_generation_endpoint.TASK.prompt_builder_component.build_generation_demonstration_messages

    def modified_method(*args, **kwargs):
        demonstrations = args[0]
        queries = []
        for i, demo in enumerate(demonstrations["train"], 1):
            demo["similarity"] = i
            demo["description"] = f"THIS IS THE QUERY TO BE FOUND AS {i}th final train demonstration"
            queries.append(demo["description"])
        if alter_order_of_demonstrations:
            random.shuffle(demonstrations["train"])
        other_demos_count = sum((len(demos) for subset, demos in demonstrations.items() if subset != "train"))

        demo_messages = original_method(*args, **kwargs)

        # should be in ascending order of similarity; the best demonstration should be last
        demos_train = demo_messages[other_demos_count * 2 :]
        assert all(query in human_msg.content for human_msg, query in zip(demos_train[::2], queries, strict=False))
        return demo_messages

    request: GenerateWorkflowRequest = {"userRequest": send_email_on_upload["description"], "connections": [], "targetFramework": "Portable"}
    requests_pydantic = GenerateWorkflowRequestPydantic.model_validate(request)
    # TODO: maybe patch the model call to avoid unnecessary usage; currently, I found no easy way to do it
    with patch.object(workflow_generation_endpoint.TASK.prompt_builder_component, "build_generation_demonstration_messages", new=modified_method):
        (await workflow_generation_endpoint.generate_workflow(requests_pydantic))["result"]
