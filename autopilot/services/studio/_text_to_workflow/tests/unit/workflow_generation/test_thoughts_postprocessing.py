import pytest

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent

# All thoughts have categories
test_case_1 = (
    {"workflow": [{"thought": "This is a thought - using category", "activity": {"thought": "Another thought - using another category"}}]},
    {"workflow": [{"thought": "This is a thought", "activity": {"thought": "Another thought"}}]},
)

# No thoughts have categories
test_case_2 = (
    {"workflow": [{"thought": "This is a thought", "activity": {"thought": "Another thought"}}]},
    {"workflow": [{"thought": "This is a thought", "activity": {"thought": "Another thought"}}]},
)

# Some thoughts have categories
test_case_3 = (
    {"workflow": [{"thought": "This is a thought - using category", "activity": {"thought": "Another thought"}}]},
    {"workflow": [{"thought": "This is a thought", "activity": {"thought": "Another thought"}}]},
)


# Use the test cases in the parametrize decorator
@pytest.mark.parametrize("workflow_dict, expected_output", [test_case_1, test_case_2, test_case_3])
def test_postprocess_thoughts(workflow_dict, expected_output, activities_retriever: ActivitiesRetriever):
    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activities_retriever)
    postprocessing_component = WorkflowGenerationPostProcessComponent(activities_retriever, dynamic_activities_component, {"Portable": set(), "Windows": set()})
    postprocessing_component._postprocess_thoughts(workflow_dict)
    assert workflow_dict == expected_output
