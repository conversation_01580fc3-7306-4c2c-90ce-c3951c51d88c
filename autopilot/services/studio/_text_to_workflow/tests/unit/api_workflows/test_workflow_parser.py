import json

import pytest

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import ApiWorkflowParser
from services.studio._text_to_workflow.tests import conftest

_api_workflow_fixtures_path = conftest._fixtures_path / "api_workflow_fixtures"


@pytest.fixture
def workflow_parser(api_activities_retriever: APIActivitiesRetriever) -> ApiWorkflowParser:
    """Create the API workflow parser component for testing."""
    return ApiWorkflowParser(api_activities_retriever)


@pytest.mark.parametrize(
    "raw_workflow_dataset",
    [
        # test unconfigured curated activities are parsed correctly
        (
            # file name
            "raw_workflow_with_curated_activities.json",
            # expected activities
            [
                ("BambooHRGet_Time_off_Requests", "Get_Time_off_Requests_1"),
                ("BambooHRChange_Time_off_Request_Status", "Change_Time_off_Request_Status_1"),
            ],
        ),
        # test that the activity with invalid export property is parsed correctly (export property contains invalid characters for an identifier)
        (
            "raw_workflow_with_invalid_export_property.json",
            [
                ("Microsoft_TeamsSend_Group_Chat_Message", "normalised-chats_messages-v2_1"),
            ],
        ),
    ],
)
def test_parse_raw_workflow_successfully(workflow_parser: ApiWorkflowParser, raw_workflow_dataset: tuple[str, list[tuple[str, str]]]):
    """Test that raw workflows can be parsed successfully."""
    # Load the raw workflow from the fixture
    filename = raw_workflow_dataset[0]
    with open(_api_workflow_fixtures_path / filename, "r") as f:
        raw_workflow = json.load(f)

    # Parse the workflow
    api_workflow, _, _ = workflow_parser.parse_api_workflow(raw_workflow)

    # Simple assertions to verify successful parsing
    assert api_workflow is not None
    assert api_workflow.root is not None

    # Verify that the activity names are parsed correctly
    for i, activity in enumerate(raw_workflow_dataset[1]):
        assert api_workflow.root.do[i].activity == activity[0]
        assert api_workflow.root.do[i].id == activity[1]
