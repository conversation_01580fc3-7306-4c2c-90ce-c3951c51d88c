from typing import cast

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetrieverBase
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_api_wf_parser import PostGenerationApiWfParser
from services.studio._text_to_workflow.common.api_workflow.schema import Response
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ActivitySearchOptions, ActivityType, Connection, PlanStep


class MockActivitiesRetriever(ActivitiesRetrieverBase):
    def __init__(self):
        super().__init__()
        self.index = {}

    def search(
        self, step: PlanStep, connections: list[Connection], activity_search_options: ActivitySearchOptions, activity_type: ActivityType, k: int
    ) -> list[ActivityDefinition]:
        return []

    def get_all_connectors(self) -> set[str]:
        return set()

    def get_relevant(
        self, steps: list[PlanStep], connections: list[Connection], activity_search_options: ActivitySearchOptions, k: int = 5
    ) -> tuple[list[PlanStep], list[ActivityDefinition], list[ActivityDefinition], list[str]]:
        return [], [], [], []


parser = PostGenerationApiWfParser(cast(APIActivitiesRetriever, MockActivitiesRetriever()), "js", {}, {}, True)


async def test_process_multiline_json_expression():
    """Test that the parser can process a multiline JSON expression with variable references."""
    # Test expression with newlines and a variable reference
    test_expression = '${{ \n    "code" : "9001",\n    "status": "Data Validation Error",\n    "message": $error.title\n}}'

    test_activity = Response(id="Response_1", thought="Test Activity", activity="Response", type="FAILURE", response_message=test_expression)

    # Process the expression
    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    # The expression should be kept as is, since it's already in the correct format
    # and the clear_invalid_expressions flag is True, we just need to verify it's not empty
    assert result == test_expression


async def test_process_valid_string_literal_expression():
    """Test that the parser can process a string literal expression."""
    test_expression = '${"Hello, world!"}'

    test_activity = Response(id="Response_2", thought="Test String Literal", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_process_invalid_string_literal_expression():
    """Test that the parser can process a string literal expression."""
    test_expression = '${"Hello, world!}'

    test_activity = Response(id="Response_2", thought="Test String Literal", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == ""


async def test_process_integer_expression():
    """Test that the parser can process an integer expression."""
    test_expression = "${42}"

    test_activity = Response(id="Response_3", thought="Test Integer", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_process_float_expression():
    """Test that the parser can process a float expression."""
    test_expression = "${3.14159}"

    test_activity = Response(id="Response_4", thought="Test Float", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_process_js_expression():
    """Test that the parser can process a JavaScript expression."""
    test_expression = "${`User ${$workflow.input.userName} has ${$context.items.length} items in cart`}"

    test_activity = Response(id="Response_5", thought="Test JS Expression", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_process_complex_js_expression():
    """Test that the parser can process a complex JavaScript expression with subfunctions."""
    test_expression = """${
        (function() {
            const items = $items || [];
            return items
                .filter(item => item.price > 10)
                .map(item => item.name)
                .join(', ');
        })()
    }"""

    test_activity = Response(id="Response_6", thought="Test Complex JS", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_invalid_complex_js_expression():
    """Test that the parser can process an invalid complex JavaScript expression with subfunctions (due to extra {} around the expression)."""
    test_expression = """${{
        (function() {
            const items = $items || [];
            return items
                .filter(item => item.price > 10)
                .map(item => item.name)
                .join(', ');
        })()
    }}"""

    test_activity = Response(id="Response_6", thought="Test Complex JS", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == ""


async def test_var_referencing_json_expression():
    """Test that the parser handles malformed JSON expressions correctly."""
    test_expression = '${{"code": 9001, "message": missing_quotes}}'

    test_activity = Response(id="Response_7", thought="Test Malformed JSON", activity="Response", type="FAILURE", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    assert result == test_expression


async def test_missing_closing_brace():
    """Test that the parser handles expressions with missing closing braces."""
    test_expression = "${$workflow.input.userName"

    test_activity = Response(id="Response_8", thought="Test Missing Brace", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    # With clear_invalid_expressions=True, the result should be empty
    assert result == ""


async def test_invalid_js_syntax():
    """Test that the parser handles expressions with invalid JavaScript syntax."""
    test_expression = "${function() { return x +* y }}"

    test_activity = Response(id="Response_9", thought="Test Invalid Syntax", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    # With clear_invalid_expressions=True, the result should be empty
    assert result == ""


async def test_empty_expression():
    """Test that the parser handles empty expressions."""
    test_expression = "${}"

    test_activity = Response(id="Response_10", thought="Test Empty Expression", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    # With clear_invalid_expressions=True, the result should be empty
    assert result == ""


async def test_nested_expressions():
    """Test that the parser handles incorrectly nested expressions."""
    test_expression = "${${$workflow.input.value}}"

    test_activity = Response(id="Response_11", thought="Test Nested Expressions", activity="Response", type="SUCCESS", response_message=test_expression)

    result = await parser._process_expression(test_expression, [], test_activity.id, "response_message", test_activity, False)

    # With clear_invalid_expressions=True, the result should be empty
    assert result == ""
