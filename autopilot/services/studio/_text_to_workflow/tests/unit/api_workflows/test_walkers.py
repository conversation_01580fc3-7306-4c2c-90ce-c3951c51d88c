import pytest

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflow
from services.studio._text_to_workflow.common.api_workflow.walkers import ApiActivityIdCollector, ApiWorkflowExpressionRemover
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

_api_workflow_fixtures_path = conftest._fixtures_path / "api_workflow_walkers_fixtures"


@pytest.fixture
def dummy_workflow():
    with open(_api_workflow_fixtures_path / "test_workflow.yaml", "r") as f:
        raw_wf = yaml_load(f)

    return ApiWorkflow.model_validate(raw_wf)


def test_api_activity_id_collector(dummy_workflow: ApiWorkflow):
    # Test collection of all IDs

    collector = ApiActivityIdCollector()
    ids = collector.collect(dummy_workflow)

    assert ids == {"Sequence_1", "SearchCustomers_1", "If_1", "Response_1", "Response_2", "ForEach_1", "tasks_1"}


def test_api_workflow_expression_remover(dummy_workflow):
    # Test removing expressions referencing activity1
    remover = ApiWorkflowExpressionRemover(activity_ids_to_remove={"SearchCustomers_1"})

    # check the values are as expected before processing
    assert dummy_workflow.root.do[1].condition
    assert dummy_workflow.root.do[1].then[0].response_message
    assert dummy_workflow.root.do[2].do[0].with_["workspace"]

    remover.process_workflow(dummy_workflow)

    # Check if expressions referencing SearchCustomers_1 are removed
    assert dummy_workflow.root.do[1].condition == ""
    assert dummy_workflow.root.do[1].then[0].response_message == ""
    assert dummy_workflow.root.do[2].do[0].with_["workspace"] == ""
