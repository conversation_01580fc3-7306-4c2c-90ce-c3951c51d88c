import pathlib

import numpy as np
import pytest

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.workflow_generation.api_workflow_generation_retrievers import APIWorkflowDemonstrationsRetriever


@pytest.fixture
def api_activities_retriever():
    """Fixture to provide an instance of APIActivitiesRetriever."""
    retriever = APIActivitiesRetriever()
    retriever.build()
    return retriever


@pytest.fixture
def workflow_demonstrations_retriever(api_activities_retriever):
    """Fixture to provide an instance of APIWorkflowDemonstrationsRetriever."""
    config_path = (pathlib.Path(__file__).parent.parent.parent).absolute() / "workflow_generation" / "config" / "activity_retrieval_prompt.yaml"
    retriever = APIWorkflowDemonstrationsRetriever(
        config_path.as_posix(),
        paths.get_api_workflow_retriever_dataset_path(),
        paths.get_api_workflow_demonstrations_retriever_path(),
        api_activities_retriever,
    )
    return retriever


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.parametrize("query", ["Create a workflow to get time off requests from BambooHR"])
def test_api_workflow_demonstrations_retriever_returns_relevant_demonstrations(workflow_demonstrations_retriever, embedding_model, query):
    """Test that the APIWorkflowDemonstrationsRetriever returns relevant demonstrations."""
    # Create embeddings for the query
    query_embedding = embedding_model.encode([query])[0]
    connections_embedding = np.zeros_like(query_embedding)  # Empty connections embedding

    # Get relevant demonstrations
    demonstrations = workflow_demonstrations_retriever.get_relevant(
        query=query,
        query_embedding=query_embedding,
        connections_embedding=connections_embedding,
        ignored_namespaces=set(),
        ignored_identifiers=set(),
        ignored_activities=set(),
        verbose=False,
    )

    # Check we got at least one result
    assert demonstrations, "No demonstrations returned"
    assert "BambooHR" in demonstrations["JS"][0]["query"]

    # Check the structure of the returned demonstrations
    for dataset_name, demos in demonstrations.items():
        assert isinstance(dataset_name, str), "Dataset name should be a string"
        assert isinstance(demos, list), "Demonstrations should be a list"
        assert len(demos) > 0, f"No demonstrations returned for dataset {dataset_name}"

        for demo in demos:
            # Check required fields in each demonstration
            assert "similarity" in demo, "Demonstration missing similarity score"
            assert "query" in demo, "Demonstration missing query field"
            assert "used_activities" in demo, "Demonstration missing used_activities field"

            # used_activities should be a list of activity names
            assert isinstance(demo["used_activities"], list), "used_activities should be a list"
            assert all(isinstance(act, str) for act in demo["used_activities"]), "Activity names should be strings"
