import pytest

from services.studio._text_to_workflow.expression_generation.expression_generation_task import (
    ApiWorkflowExpressionGenerationTask,
    JsInvokeExpressionGenerationTask,
)


@pytest.fixture
def api_workflow_task():
    return ApiWorkflowExpressionGenerationTask("prompt.yaml", "demos.yaml")


@pytest.fixture
def js_invoke_task():
    return JsInvokeExpressionGenerationTask("js_invoke/config.yaml", "js_invoke/examples.yaml")


META_VARIABLES = [
    {"name": "$workflow", "type": "{input : System.object}"},
    {"name": "$context", "type": "{context : System.object}"},
    {"name": "$input", "type": "{input : System.object}"},
]


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "adds_meta_variables",
            "input": {"additionalTypeDefinitions": "", "availableVariables": [{"name": "testVar", "type": "string"}]},
            "expected": {
                "additionalTypeDefinitions": "",
                "availableVariables_length": 4,
                "user_variable": {"name": "testVar", "type": "string"},
                "has_meta_vars": True,
            },
        },
        {
            "name": "handles_empty_variables",
            "input": {"additionalTypeDefinitions": ""},
            "expected": {"additionalTypeDefinitions": "", "availableVariables_length": 3, "has_meta_vars": True},
        },
    ],
)
def test_api_workflow_preprocess_request(api_workflow_task, test_case):
    """Test ApiWorkflowExpressionGenerationTask preprocess_request with multiple scenarios"""
    request = test_case["input"]
    expected = test_case["expected"]

    api_workflow_task.preprocess_request(request)

    assert request["additionalTypeDefinitions"] == expected["additionalTypeDefinitions"]
    assert len(request["availableVariables"]) == expected["availableVariables_length"]

    if expected["has_meta_vars"]:
        for meta_var in META_VARIABLES:
            assert meta_var in request["availableVariables"]

    if "user_variable" in expected:
        assert request["availableVariables"][3] == expected["user_variable"]


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "adds_meta_variables",
            "input": {"additionalTypeDefinitions": "", "availableVariables": [{"name": "testVar", "type": "string"}]},
            "expected": {
                "additionalTypeDefinitions": "",
                "availableVariables_length": 4,
                "user_variable": {"name": "testVar", "type": "string"},
                "has_meta_vars": True,
            },
        },
        {
            "name": "handles_empty_variables",
            "input": {"additionalTypeDefinitions": ""},
            "expected": {"additionalTypeDefinitions": "", "availableVariables_length": 3, "has_meta_vars": True},
        },
    ],
)
def test_js_invoke_preprocess_request(js_invoke_task, test_case):
    """Test JsInvokeExpressionGenerationTask preprocess_request with multiple scenarios"""
    request = test_case["input"]
    expected = test_case["expected"]

    js_invoke_task.preprocess_request(request)

    assert request["additionalTypeDefinitions"] == expected["additionalTypeDefinitions"]
    assert len(request["availableVariables"]) == expected["availableVariables_length"]

    if expected["has_meta_vars"]:
        for meta_var in META_VARIABLES:
            assert meta_var in request["availableVariables"]

    if "user_variable" in expected:
        assert request["availableVariables"][3] == expected["user_variable"]
