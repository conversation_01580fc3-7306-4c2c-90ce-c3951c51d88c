import pytest

from services.studio._text_to_workflow.common.schema import ActivityDict
from services.studio._text_to_workflow.common.workflow import Activity
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@pytest.fixture
def activity_placeholder():
    return Activity(lmyaml={"thought": "", "activity": "", "params": {}}, workflow_arguments={}, workflow_variables={}, parent=None)


@pytest.mark.parametrize(
    "case_key, case_value, expected",
    [
        ("case_key", None, True),
        ("case_key", [{"activity": "activity1", "thought": "thought1"}], True),
        ("case_key", [{"activity": "activity1"}], False),
        ("case_key", {"activity": "activity1", "thought": "thought1"}, True),
        ("case_key", {"activity": "activity1"}, False),
    ],
)
def test_is_switch_case(activity_placeholder, case_key, case_value, expected):
    assert activity_placeholder._is_switch_case(case_key, case_value) is expected


@pytest.fixture
def switch(workflow_abstraction_fixtures_path) -> ActivityDict:
    return yaml_load(workflow_abstraction_fixtures_path / "switch.yaml")


@pytest.fixture
def switch_with_cascaded_case(workflow_abstraction_fixtures_path):
    switch = yaml_load(workflow_abstraction_fixtures_path / "switch.yaml")
    switch["params"]["Cases"] = {"0": None, **switch["params"]["Cases"]}
    return switch


@pytest.fixture
def switch_with_sole_activity_case(workflow_abstraction_fixtures_path):
    switch = yaml_load(workflow_abstraction_fixtures_path / "switch.yaml")
    switch["params"]["Cases"][1] = switch["params"]["Cases"][1][0]
    return switch


def test_switch_parsing(switch):
    activity_object = Activity(switch, {}, {}, None)
    assert activity_object.to_dict() == switch


def test_switch_with_cascaded_case(switch_with_cascaded_case):
    activity_object = Activity(switch_with_cascaded_case, {}, {}, None)
    assert activity_object.to_dict() == switch_with_cascaded_case


def test_switch_case_sole_activity(switch_with_sole_activity_case, switch):
    activity_object = Activity(switch_with_sole_activity_case, {}, {}, None)
    assert activity_object.to_dict() == switch
