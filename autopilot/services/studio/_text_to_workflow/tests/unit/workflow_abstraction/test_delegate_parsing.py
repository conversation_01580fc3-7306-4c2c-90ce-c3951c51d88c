import pytest

from services.studio._text_to_workflow.common.schema import ActivityDict
from services.studio._text_to_workflow.common.workflow import Activity
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@pytest.fixture
def activity_placeholder():
    return Activity(lmyaml={"thought": "", "activity": "", "params": {}}, workflow_arguments={}, workflow_variables={}, parent=None)


@pytest.mark.parametrize(
    "delegate_key, delegate_value, expected",
    [
        ("delegate_key", {"Handler": [{"activity": "activity1", "thought": "thought1"}], "variables": [{"name": "var1", "type": "string"}]}, True),
        ("delegate_key", {"Handler": [{"activity": "activity1", "thought": "thought1"}]}, True),
        ("delegate_key", {"Handler": [{"activity": "activity1"}]}, False),
        ("delegate_key", {"variables": [{"name": "var1", "type": "string"}]}, False),
        ("delegate_key", {"Handler": "invalid_type"}, False),
    ],
)
def test_is_activity_delegate_property(activity_placeholder, delegate_key, delegate_value, expected):
    assert activity_placeholder._is_activity_delegate_property(delegate_key, delegate_value) is expected


@pytest.fixture
def delegate_standard(workflow_abstraction_fixtures_path) -> ActivityDict:
    return yaml_load(workflow_abstraction_fixtures_path / "delegate.yaml")


@pytest.fixture
def delegate_sole_activity(workflow_abstraction_fixtures_path) -> ActivityDict:
    fixture = yaml_load(workflow_abstraction_fixtures_path / "delegate.yaml")
    fixture["params"]["Body"]["Handler"] = fixture["params"]["Body"]["Handler"][0]
    return fixture


def test_delegate_parsing(delegate_standard):
    activity_object = Activity(delegate_standard, {}, {}, None)
    assert activity_object.to_dict() == delegate_standard


def test_delegate_sole_activity(delegate_sole_activity, delegate_standard):
    activity_object = Activity(delegate_sole_activity, {}, {}, None)
    assert activity_object.to_dict() == delegate_standard
