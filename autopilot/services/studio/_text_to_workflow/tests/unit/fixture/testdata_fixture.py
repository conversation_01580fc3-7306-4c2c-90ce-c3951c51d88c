case_lmyaml_1 = {
    "request": {
        "parameters": [
            {"name": "arg1_in", "type": "System.String", "direction": "In"},
            {"name": "arg3_in", "type": "System.String", "direction": "In"},
            {"name": "arg_int", "type": "System.Int32", "direction": "In"},
            {"name": "argBool", "type": "System.Boolean", "direction": "In"},
        ],
        "additionalTypeDefinitions": None,
        "implementation_yaml": """
processName: _5variations
packages:
- UiPath.Database.Activities, v1.9.0-alpha.5693499
- UiPath.Excel.Activities, v[2.24.0-alpha.6222792]
- UiPath.GSuite.Activities, v2.7.22
- UiPath.IntegrationService.Activities, v1.8.0
- UiPath.Mail.Activities, v1.22.2
- UiPath.System.Activities, v24.11.0-alpha.6341656
- UiPath.Testing.Activities, v[24.10.2]
- UiPath.UIAutomation.Activities, v[24.10.6]
- UiPath.Workday.IntegrationService.Activities, v[5.3.1]
workflow:
- thought: '... Given'
  displayName: '... Given'
  activity: System.Activities.Statements.Sequence
  params:
    Activities: []
- thought: '... When'
  displayName: '... When'
  activity: System.Activities.Statements.Sequence
  params:
    Activities:
    - thought: Main - Invoke Workflow File (Main.xaml)
      displayName: Main - Invoke Workflow File (Main.xaml)
      activity: UiPath.Core.Activities.InvokeWorkflowFile
      params:
        Arguments:
          arg_int: '[[(arg_int) as System.Activities.InArgument<System.Int32>]]'
          arg1_in: '[[(arg1_in) as System.Activities.InArgument<System.String>]]'
          arg3_in: '[[(arg3_in) as System.Activities.InOutArgument<System.String>]]'
          argBool: '[[(argBool) as System.Activities.InArgument<System.Boolean>]]'
        TargetSession: Current
        UnSafe: 'False'
        WorkflowFileName: Main.xaml
        ArgumentsVariable: '[[]]'
        ContinueOnError: '[[]]'
        Level: '[[]]'
        LogEntry: '[[]]'
        LogExit: '[[]]'
        Timeout: '[[]]'
- thought: '... Then'
  displayName: '... Then'
  activity: System.Activities.Statements.Sequence
  params:
    Activities: []
arguments:
- direction: In
  name: arg1_in
  type: System.String
- direction: In
  name: arg3_in
  type: System.String
- direction: In
  name: arg_int
  type: System.Int32
- direction: In
  name: argBool
  type: System.Boolean
""",
        "implementationFormat": "LMYAML",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "generate 20 test data",
        "deterministic_mode": False,
        "implementation": """
public class Workflow
{
  // Define the entry point function
  public void Execute(int arg_int, string arg1_in, ref string arg3_in, bool argBool)
  {
    // Given
    // This section is currently empty, but can be used for setup or initialization if needed.
    
    // When
    // Invoke the Main workflow file (Main.xaml) with the specified arguments
    UiPath.Core.Activities.InvokeWorkflowFile.Invoke(
      WorkflowFileName: "Main.xaml",
      Arguments: new Dictionary<string, object>
      {
        { "arg_int", arg_int },
        { "arg1_in", arg1_in },
        { "arg3_in", arg3_in }, // Note: This is an InOut argument
        { "argBool", argBool }
      },
      TargetSession: "Current",
      UnSafe: false,
      ArgumentsVariable: null,
      ContinueOnError: null,
      Level: null,
      LogEntry: null,
      LogExit: null,
      Timeout: null
    );
  }
}
    """,
    },
    "result_load_demos_yaml_to_code": ["For each file in folder"],
    "result_generate_code_from_lmyaml": "void Execute(",
    "test_data": [
        {"arg1_in": "", "arg3_in": "NormalString", "arg_int": 5, "argBool": True},
        {"arg1_in": "TestString", "arg3_in": "", "arg_int": -10, "argBool": False},
        {"arg1_in": "!@#$%^&*()", "arg3_in": "AnotherString", "arg_int": 0, "argBool": True},
        {"arg1_in": "This is a very long string that exceeds normal length for testing purposes.", "arg3_in": "Short", "arg_int": 100000, "argBool": False},
        {"arg1_in": "   Leading and trailing spaces   ", "arg3_in": "12345", "arg_int": 1, "argBool": True},
        {"arg1_in": None, "arg3_in": "ValidString", "arg_int": 15, "argBool": False},
        {"arg1_in": "     ", "arg3_in": "AnotherValidString", "arg_int": -5, "argBool": True},
        {"arg1_in": "MiXeD CaSe", "arg3_in": "@#&*()", "arg_int": 0, "argBool": False},
        {"arg1_in": "12345", "arg3_in": "   Spaces   ", "arg_int": -99999, "argBool": True},
        {"arg1_in": "Hello 🌍", "arg3_in": "ValidString", "arg_int": 25, "argBool": False},
        {"arg1_in": "<div>HTML Content</div>", "arg3_in": "AnotherString", "arg_int": 10, "argBool": True},
        {"arg1_in": "' OR '1'='1", "arg3_in": "ValidString", "arg_int": 30, "argBool": False},
        {"arg1_in": "12345678901234567890", "arg3_in": "AnotherString", "arg_int": 50, "argBool": True},
        {"arg1_in": "abc123", "arg3_in": "ValidString", "arg_int": 75, "argBool": False},
        {"arg1_in": "A", "arg3_in": "ValidString", "arg_int": 1, "argBool": True},
        {
            "arg1_in": "This is a very long string that is used to test the limits of the input handling in the system.",
            "arg3_in": "ValidString",
            "arg_int": 100,
            "argBool": False,
        },
        {"arg1_in": "TeStInG", "arg3_in": "ValidString", "arg_int": 20, "argBool": True},
        {"arg1_in": "   Leading and trailing spaces   ", "arg3_in": "ValidString", "arg_int": 30, "argBool": False},
        {"arg1_in": "9", "arg3_in": "ValidString", "arg_int": 5, "argBool": True},
        {"arg1_in": "abc!@#", "arg3_in": "ValidString", "arg_int": 15, "argBool": False},
    ],
}

case_lmyaml_2 = {
    "request": {
        "parameters": [
            {"name": "emailFolder", "type": "System.String", "direction": "In"},
            {"name": "condition", "type": "System.Boolean", "direction": "In"},
            {"name": "name", "type": "System.String", "direction": "In"},
        ],
        "additionalTypeDefinitions": None,
        "implementation_yaml": """
processName: Get_All_Emails_prompt
packages:
- UiPath.Excel.Activities, v[2.24.0-alpha.6118715]
- UiPath.GSuite.Activities, v2.7.1-preview
- UiPath.Mail.Activities, v[1.23.10-alpha.5964877]
- UiPath.MicrosoftOffice365.Activities, v2.7.10-alpha.6133107
- UiPath.System.Activities, v24.10.0-alpha.6118717
- UiPath.Testing.Activities, v[24.10.0-beta.6126687]
- UiPath.UIAutomation.Activities, v24.10.7
workflow:
- thought: Get Gmail Email List
  displayName: Get Gmail Email List
  activity: UiPath.GSuite.Activities.GetEmailListConnections
  params:
    Filter:
      LogicalOperator: And
      Filters: []
    Folder:
      BrowserFolderId: \'\'
      ConnectionDescriptor: \'\'
      ConnectionKey: \'\'
      InputMode: EnterPath
      BrowserFolderName: \'[[]]\'
      ManualEntryFolderName: \'[[emailFolder]]\'
    FolderInputSelectionMode: EnterPath
    ImportantOnly: \'False\'
    IncludeSubfolders: \'False\'
    MarkAsRead: \'False\'
    MaxResults: \'100\'
    StarredOnly: \'False\'
    UnreadOnly: \'False\'
    UseConnectionService: \'True\'
    WithAttachmentsOnly: \'False\'
    BrowserFolder: \'[[]]\'
    ContinueOnError: \'[[]]\'
    CurrentIndex: \'[[]]\'
    EmailList: \'[[EmailsList]]\'
    ManualEntryFolder: \'[[emailFolder]]\'
- thought: For Each Email in Gmail Email List
  displayName: For Each Email in Gmail Email List
  activity: UiPath.Core.Activities.ForEach<UiPath.GSuite.Models.GmailMessage>
  params:
    CurrentIndex: \'[[]]\'
    MaxIterations: \'[[]]\'
    Values: \'[[EmailsList]]\'
    Body:
      variables:
      - name: currentEmail
        type: UiPath.GSuite.Models.GmailMessage
      Handler:
      - thought: If Email has XLS Attachments
        displayName: If Email has XLS Attachments
        activity: System.Activities.Statements.If
        params:
          Condition: \'[[condition]]\'
          Then:
            thought: Then
            displayName: Then
            activity: System.Activities.Statements.Sequence
            params:
              Activities:
              - thought: Download XLS Attachments
                displayName: Download XLS Attachments
                activity: UiPath.GSuite.Activities.DownloadAttachmentsConnections
                params:
                  ExcludeInlineAttachments: \'False\'
                  SearchMode: UseSimple
                  UseConnectionService: \'True\'
                  ContinueOnError: \'[[]]\'
                  Email: \'[[currentEmail]]\'
                  FilterByFileNames: \'[[name]]\'
                  NewResult: \'[[DownloadAttachments]]\'
              - thought: For Each XLS Attachment
                displayName: For Each XLS Attachment
                activity: UiPath.Core.Activities.ForEach<UiPath.GSuite.Gmail.Models.GmailAttachmentLocalItem>
                params:
                  CurrentIndex: \'[[]]\'
                  MaxIterations: \'[[]]\'
                  Values: \'[[DownloadAttachments]]\'
                  Body:
                    variables:
                    - name: currentAttachment
                      type: UiPath.GSuite.Gmail.Models.GmailAttachmentLocalItem
                    Handler:
                    - thought: Read "Name" column from "Website" sheet
                      displayName: Read "Name" column from "Website" sheet
                      activity: UiPath.Excel.Activities.Business.ExcelForEachRow
                      params:
                        EmptyRowBehavior: StopAfterThreeConsecutiveEmptyRows
                        HasHeaders: \'True\'
                        SaveAfterEachRow: \'False\'
                        Range: \'[[currentAttachment.LocalPath]]\'
                        Body:
                          variables:
                          - name: CurrentRow
                            type: UiPath.Excel.CurrentRowQuickHandle
                          - name: CurrentIndex
                            type: System.Int32
                          Handler:
                            thought: Open URL from "Name" column
                            displayName: Open URL from "Name" column
                            activity: UiPath.Core.Activities.InvokeWorkflowFile
                            params:
                              Arguments: {}
                              TargetSession: Main
                              UnSafe: \'False\'
                              WorkflowFileName: OpenURLWorkflow.xaml
                              ArgumentsVariable: \'[[]]\'
                              ContinueOnError: \'[[]]\'
                              Level: \'[[]]\'
                              LogEntry: \'[[]]\'
                              LogExit: \'[[]]\'
                              Timeout: \'[[]]\'
                    DisplayName: Body
      DisplayName: Body
variables:
- name: EmailsList
  type: System.Collections.Generic.List<UiPath.GSuite.Models.GmailMessage>
- name: DownloadAttachments
  type: UiPath.GSuite.Gmail.Models.GmailAttachmentLocalItem[]
- name: Length
  type: System.Int32
arguments:
- direction: In
  name: emailFolder
  type: System.String
- direction: In
  name: condition
  type: System.Boolean
- direction: In
  name: name
  type: System.String
        """,
        "implementation": """
public class Workflow
{
    // Define the entry point function
    public void Execute(string emailFolder, bool condition, string name)
    {
        // Declare variables
        List<UiPath.GSuite.Models.GmailMessage> EmailsList = new List<UiPath.GSuite.Models.GmailMessage>();
        List<UiPath.GSuite.Gmail.Models.GmailAttachmentLocalItem> DownloadAttachments = new List<UiPath.GSuite.Gmail.Models.GmailAttachmentLocalItem>();

        // Get Gmail Email List
        EmailsList = UiPath.GSuite.Activities.GetEmailListConnections(
            Filter = new EmailFilter
            {
                LogicalOperator = "And",
                Filters = new List<EmailFilterCriteria>()
            },
            Folder = new FolderDetails
            {
                BrowserFolderId = "",
                ConnectionDescriptor = "",
                ConnectionKey = "",
                InputMode = "EnterPath",
                BrowserFolderName = "[[]]",
                ManualEntryFolderName = emailFolder
            },
            FolderInputSelectionMode = "EnterPath",
            ImportantOnly = "False",
            IncludeSubfolders = "False",
            MarkAsRead = "False",
            MaxResults = "100",
            StarredOnly = "False",
            UnreadOnly = "False",
            UseConnectionService = "True",
            WithAttachmentsOnly = "False",
            BrowserFolder = "[[]]",
            ContinueOnError = "[[]]",
            CurrentIndex = "[[]]"
        );

        // For Each Email in Gmail Email List
        foreach (var currentEmail in EmailsList)
        {
            // Check if Email has XLS Attachments
            if (currentEmail.Attachments.Any(a => a.FileName.EndsWith(".xls") || a.FileName.EndsWith(".xlsx")))
            {
                // Download XLS Attachments
                DownloadAttachments = UiPath.GSuite.Activities.DownloadAttachmentsConnections(
                    ExcludeInlineAttachments = "False",
                    SearchMode = "UseSimple",
                    UseConnectionService = "True",
                    ContinueOnError = "[[]]",
                    Email = currentEmail,
                    FilterByFileNames = "[[name]]"
                );

                // For Each XLS Attachment
                foreach (var currentAttachment in DownloadAttachments)
                {
                    // Read "Name" column from "Website" sheet
                    UiPath.Excel.Activities.Business.ExcelForEachRow(
                        EmptyRowBehavior = "StopAfterThreeConsecutiveEmptyRows",
                        HasHeaders = "True",
                        SaveAfterEachRow = "False",
                        Range = currentAttachment.LocalPath,
                        Body = new ExcelRowHandler
                        {
                            CurrentRow = new UiPath.Excel.CurrentRowQuickHandle(),
                            CurrentIndex = 0,
                            Handler = () =>
                            {
                                // Open URL from "Name" column
                                UiPath.Core.Activities.InvokeWorkflowFile(
                                    Arguments = new Dictionary<string, object>(),
                                    TargetSession = "Main",
                                    UnSafe = "False",
                                    WorkflowFileName = "OpenURLWorkflow.xaml",
                                    ArgumentsVariable = "[[]]",
                                    ContinueOnError = "[[]]",
                                    Level = "[[]]",
                                    LogEntry = "[[]]",
                                    LogExit = "[[]]",
                                    Timeout = "[[]]"
                                );
                            }
                        }
                    );
                }
            }
        }
    }
}
        """,
        "implementationFormat": "LMYAML",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "",
        "deterministic_mode": False,
    },
    "result_generate_code_from_lmyaml": "void Execute(",
    "result_load_demos_yaml_to_code": ["Count emails from domain", "Check email sender in salesforce and reply with openAI"],
    "test_data": [
        {"emailFolder": "Inbox", "condition": True, "name": "report.xlsx"},
        {"emailFolder": "Sent Items", "condition": False, "name": "summary.xls"},
        {"emailFolder": "", "condition": True, "name": "data.xlsx"},
        {"emailFolder": "InvalidFolder", "condition": False, "name": "presentation.xls"},
        {"emailFolder": "Projects", "condition": True, "name": "<EMAIL>"},
        {"emailFolder": "Drafts", "condition": False, "name": "summary_2021#final.xls"},
        {"emailFolder": "Archive", "condition": True, "name": "2021_Annual_Report_Complete_Version.xlsx"},
        {"emailFolder": "Archive", "condition": False, "name": "2021_Summary_of_Activities_and_Results.xls"},
        {"emailFolder": "Inbox", "condition": True, "name": ""},
        {"emailFolder": "Sent Items", "condition": False, "name": ""},
        {"emailFolder": "Reports", "condition": True, "name": "This_is_a_very_long_file_name_that_exceeds_normal_length_limits_for_files.xlsx"},
        {"emailFolder": "Reports", "condition": False, "name": "This_is_another_very_long_file_name_that_exceeds_normal_length_limits_for_files.xls"},
        {"emailFolder": "Work", "condition": True, "name": "FinalReport.XLSX"},
        {"emailFolder": "Work", "condition": False, "name": "Summary.REPORT.xls"},
        {"emailFolder": "Finance", "condition": True, "name": "2022_Financial_Report_123.xlsx"},
        {"emailFolder": "Finance", "condition": False, "name": "2022_Summary_456.xls"},
        {"emailFolder": "Personal", "condition": True, "name": "My Report.xlsx"},
        {"emailFolder": "Personal", "condition": False, "name": "My Summary.xls"},
        {"emailFolder": "Shared", "condition": True, "name": "data.file.xlsx"},
        {"emailFolder": "Shared", "condition": False, "name": "summary.file.xls"},
    ],
}
case_lmyaml_3 = {
    "request": {
        "parameters": [{"name": "path", "type": "System.String", "direction": "In"}],
        "additionalTypeDefinitions": None,
        "implementation_yaml": """
processName: Get Filename of QA_Work.xlsx on OneDrive
packages:
- StudioWeb.Sdk, v1.0.0-alpha.20240424.6
- UiPath.AzureActiveDirectory.Activities, v1.4.2-dev.230822.53041
- UiPath.Excel.Activities, v[2.23.3-preview]
- UiPath.IntegrationService.Activities, v1.6.0-alpha.20240607.9
- UiPath.Mail.Activities, v[1.23.1]
- UiPath.MicrosoftOffice365.Activities, v2.7.0-alpha.6020846
- UiPath.System.Activities, v24.10.0-alpha.6027511
- UiPath.Testing.Activities, v[23.10.0]
- UiPath.UIAutomation.Activities, v[24.10.0-preview]
workflow:
- thought: Get Filename of QA_Work.xlsx on OneDrive
  displayName: Get Filename of QA_Work.xlsx on OneDrive
  activity: UiPath.MicrosoftOffice365.Activities.Files.GetFileFolderConnections
  params:
    BrowserDriveName: OneDrive
    BrowserItemFriendlyName: QA_Work.xlsx
    BrowserSiteUrl: https://uipath-my.sharepoint.com/personal/ionut_babencu_uipath_com/Documents
    ItemSelectionMode: FullPath
    UseConnectionService: \'True\'
    BrowserParentDriveId: \'[[]]\'
    BrowserParentDriveName: \'[[]]\'
    BrowserParentItemFriendlyName: \'[[]]\'
    BrowserParentPath: \'[[]]\'
    ConnectionId: \'[[]]\'
    ContinueOnError: \'[[]]\'
    Item: \'[[]]\'
    ManualEntryDriveName: \'[[]]\'
    ManualEntryItemFullPath: \'[[path]]\'
    ManualEntryItemRelativePath: \'[[]]\'
    ManualEntryItemUrl: \'[[]]\'
    ManualEntrySiteUrl: \'[[]]\'
    Result: \'[[fileInfo]]\'
    RetryTimeoutSeconds: \'[[]]\'
- thought: Save Filename in Variable
  displayName: Save Filename in Variable
  activity: System.Activities.Statements.Assign
  params:
    To: \'[[fileName]]\'
    Value: \'[[fileInfo.Name]]\'
- thought: Log Filename Message
  displayName: Log Filename Message
  activity: UiPath.Core.Activities.LogMessage
  params:
    Level: \'[[]]\'
    Message: \'[[string.Format("The filename is {0}", fileName)]]\'
variables:
- name: fileInfo
  type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem
- name: fileName
  type: System.String
arguments:
- direction: In
  name: path
  type: System.String
        """,
        "implementation": """
public class Workflow
{
  // Define the entry point function
  public void Execute(string path)
  {
    // Declare variables
    UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem fileInfo;
    string fileName;

    // Get the filename of QA_Work.xlsx on OneDrive
    fileInfo = UiPath.MicrosoftOffice365.Activities.Files.GetFileFolderConnections
    (
      BrowserDriveName: "OneDrive",
      BrowserItemFriendlyName: "QA_Work.xlsx",
      BrowserSiteUrl: "https://uipath-my.sharepoint.com/personal/ionut_babencu_uipath_com/Documents",
      ItemSelectionMode: "FullPath",
      UseConnectionService: true,
      BrowserParentDriveId: null,
      BrowserParentDriveName: null,
      BrowserParentItemFriendlyName: null,
      BrowserParentPath: null,
      ConnectionId: null,
      ContinueOnError: null,
      Item: null,
      ManualEntryDriveName: null,
      ManualEntryItemFullPath: path,
      ManualEntryItemRelativePath: null,
      ManualEntryItemUrl: null,
      ManualEntrySiteUrl: null,
      Result: null,
      RetryTimeoutSeconds: null
    );

    // Save the filename in the variable
    fileName = fileInfo.Name;

    // Log the filename message
    UiPath.Core.Activities.LogMessage
    (
      Level: LogLevel.Info, // Assuming LogLevel.Info for logging
      Message: string.Format("The filename is {0}", fileName)
    );
  }
}
        """,
        "implementationFormat": "LMYAML",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "generate 20 test data",
        "deterministic_mode": False,
    },
    "result_load_demos_yaml_to_code": ["For each file in folder", "Send Excel File From SharePoint With Outlook"],
    "test_data": [
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\documents\\qa_work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\My Files\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\QA_Work_#1.xlsx"},
        {"path": "C:\\Users\\<USER>\\Documents\\Work\\Projects\\2023\\OneDrive\\QA_Work.xlsx"},
        {"path": ""},
        {"path": None},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\NonExistentFile.xlsx"},
        {"path": ".\\Documents\\QA_Work.xlsx"},
        {"path": "\\\\Server\\Share\\Documents\\QA_Work.xlsx"},
        {"path": "Z:\\Documents\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\ThisIsAVeryLongFileNameThatExceedsNormalLength\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\QA_Work.txt"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\.hidden\\QA_Work.xlsx"},
        {"path": "C:\\Program Files\\QA_Work.xlsx"},
        {"path": "C:\\Windows\\System32\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\A.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\ThisIsAVeryLongDirectoryNameThatMightCauseIssues\\QA_Work.xlsx"},
        {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\QA_Work"},
    ],
}

case_lmyaml_4 = {
    "request": {
        "parameters": [{"name": "folderName", "type": "System.String", "direction": "In"}],
        "additionalTypeDefinitions": None,
        "implementation_yaml": """
processName: Print file name from a given folder
packages:
- StudioWeb.Sdk, v1.0.0-alpha.20240424.6
- UiPath.AzureActiveDirectory.Activities, v1.4.2-dev.230822.53041
- UiPath.Excel.Activities, v[2.23.3-preview]
- UiPath.IntegrationService.Activities, v1.6.0-alpha.20240607.9
- UiPath.Mail.Activities, v[1.23.1]
- UiPath.MicrosoftOffice365.Activities, v2.7.0-alpha.6020846
- UiPath.System.Activities, v24.10.0-alpha.6027511
- UiPath.Testing.Activities, v[23.10.0]
- UiPath.UIAutomation.Activities, v[24.10.0-preview]
workflow:
- thought: "For each file in C:\\\\\\\\Temp"
  displayName: "For each file in C:\\\\\\\\Temp"
  activity: UiPath.Core.Activities.ForEachFileX
  params:
    Filter: "*.*"
    IncludeSubDirectories: "False"
    OrderBy: "NameAscFirst"
    SkipFolderWithoutPermission: "True"
    Folder: "[[folderName]]"
    Body:
      variables:
      - name: "CurrentFile"
        type: "System.IO.FileInfo"
      - name: "CurrentIndex"
        type: "System.Int32"
      Handler:
      - thought: ""
        displayName: ""
        activity: System.Activities.Statements.Sequence
        params:
          Activities:
          - thought: "Read Text File"
            displayName: "Read Text File"
            activity: UiPath.Core.Activities.ReadTextFile
            params:
              Content: "[[FileContent]]"
              ContinueOnError: "[[]]"
              Encoding: "[[]]"
              File: "[[]]"
              FileName: "[[CurrentFile.FullName]]"
              Result: "[[]]"
      - thought: "Log Message"
        displayName: "Log Message"
        activity: UiPath.Core.Activities.LogMessage
        params:
          Level: "[[Info]]"
          Message: "[[CurrentFile.FullName]]"
variables:
- name: FileContent
  type: System.String
arguments:
- direction: In
  name: folderName
  type: System.String
    """,
        "implementation": """
public class Workflow
{
  // Define the entry point function with arguments
  public void Execute(string folderName)
  {
    // For each file in the specified folder
    foreach (System.IO.FileInfo CurrentFile in new System.IO.DirectoryInfo(folderName).GetFiles("*.*", System.IO.SearchOption.TopDirectoryOnly))
    {
      // Read the content of the current file (if needed)
      string FileContent = string.Empty; // Placeholder for file content
      try
      {
        // Read the text file
        FileContent = System.IO.File.ReadAllText(CurrentFile.FullName);
      }
      catch (Exception ex)
      {
        // Handle any errors that occur during file reading
        // Continue on error logic can be implemented here if needed
      }

      // Log the full name of the current file
      UiPath.Core.Activities.LogMessage
      (
        Level: "Info",
        Message: CurrentFile.FullName
      );
    }
  }
}
    """,
        "implementationFormat": "LMYAML",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "generate 20 test data",
        "deterministic_mode": False,
    },
    "result_load_demos_yaml_to_code": ["For each file in folder"],
    "test_data": [
        {"folderName": "C:\\TestFolder\\MultipleFiles"},
        {"folderName": "C:\\TestFolder\\EmptyFolder"},
        {"folderName": "C:\\TestFolder\\SingleFile"},
        {"folderName": "C:\\TestFolder\\MixedFiles"},
        {"folderName": "C:\\Test Folder With Spaces"},
        {"folderName": "\\\\NetworkPath\\SharedFolder"},
        {"folderName": "\\\\ServerName\\SharedFolder"},
        {"folderName": "C:\\InvalidFolderPath"},
        {"folderName": "C:\\"},
        {"folderName": "C:\\Windows"},
        {"folderName": "C:\\TestFolder\\.hidden"},
        {"folderName": "C:\\TestFolder\\ThisIsAVeryLongFolderNameThatExceedsTheTypicalPathLengthLimit\\SubFolder\\AnotherSubFolder"},
        {"folderName": "C:\\TestFolder\\Special@Characters!"},
        {"folderName": None},
        {"folderName": ""},
        {"folderName": ".\\TestFolder"},
        {"folderName": "C:\\TestFolder\\"},
        {"folderName": "\\TestFolder"},
        {"folderName": "C:/TestFolder\\MixedSlashes"},
        {"folderName": "C:\\TestFolder\\file.txt"},
    ],
}

case_code_1 = {
    "request": {
        "parameters": [
            {"name": "input1", "type": "System.String", "direction": "In"},
            {"name": "input2", "type": "System.Int32", "direction": "In"},
            {"name": "input3", "type": "System.Boolean", "direction": "In"},
        ],
        "additionalTypeDefinitions": None,
        "implementation": """
using System.Data;
using UiPath.CodedWorkflows;

namespace Demo
{
    public class Workflow : CodedWorkflow
    {
        UiPath.Core.Activities.API.ISystemService system;
        UiPath.Testing.API.ITestingService testing;

        [TestCase]
        public void Execute(string input1, int input2, bool input3)
        {
            if (input3)
            {
                var randomValue = testing.RandomValue("path/to/file");
                var result = input1 + randomValue + input2.ToString();
                system.CreateFile(result, "path/to/save");
            }
            else
            {
                var dataTable = new DataTable();
                dataTable.Columns.Add("Column1", typeof(string));
                dataTable.Columns.Add("Column2", typeof(int));
                dataTable.Columns.Add("Column3", typeof(bool));

                var dataRow = dataTable.NewRow();
                dataRow["Column1"] = input1;
                dataRow["Column2"] = input2;
                dataRow["Column3"] = input3;

                system.AddDataRow(ref dataTable, dataRow);

                var output = system.OutputDataTable(dataTable);
                system.CreateFile(output, "path/to/save");
            }
        }
    }
}""",
        "implementationFormat": "Code",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "",
    },
    "test_data": [
        {"input1": "TestString", "input2": 42, "input3": True},
        {"input1": "", "input2": 100, "input3": True},
        {"input1": "AnotherTest", "input2": 0, "input3": True},
        {"input1": "NegativeTest", "input2": -10, "input3": True},
        {"input1": "DataEntry", "input2": 25, "input3": False},
        {"input1": "", "input2": 50, "input3": False},
        {"input1": "ZeroEntry", "input2": 0, "input3": False},
        {"input1": "NegativeData", "input2": -5, "input3": False},
        {"input1": "ThisIsAVeryLongStringToTestTheFunctionality", "input2": 99, "input3": True},
        {"input1": "AnotherLongStringForTestingPurpose", "input2": 75, "input3": False},
        {"input1": "!@#$%^&*()_+", "input2": 30, "input3": True},
        {"input1": "!@#$%^&*()_+", "input2": 15, "input3": False},
        {"input1": None, "input2": 20, "input3": True},
        {"input1": None, "input2": 10, "input3": False},
        {"input1": "   LeadingAndTrailingSpaces   ", "input2": 5, "input3": True},
    ],
}

case_code_2 = {
    "request": {
        "parameters": [
            {"name": "input1", "type": "System.String", "direction": "In"},
            {"name": "input2", "type": "System.Int32", "direction": "In"},
            {"name": "input3", "type": "System.Boolean", "direction": "In"},
        ],
        "additionalTypeDefinitions": None,
        "implementation": """
using System.Data;
using UiPath.CodedWorkflows;
using UiPath.Excel.Activities.API;
using System;

namespace DemoForward
{
    public class Workflow5 : CodedWorkflow
    {
        UiPath.Core.Activities.API.ISystemService system;
        UiPath.Testing.API.ITestingService testing;
        IExcelService excel;

        [TestCase]
        public void Execute(string input1, int input2, bool input3)
        {
            // Use the Excel service to open the file
            var workbook = excel.UseExcelFile("example.xlsx");
        
            // Read data from the first sheet
            var dataTable = workbook.Sheet["Sheet1"].ReadRange(true, true);
        
            // Display the content of the datatable
            PrintDataTable(dataTable);
        
            // Write some data to the first sheet
            var newDataTable = new DataTable();
            newDataTable.Columns.Add("Column1");
            newDataTable.Columns.Add("Column2");
            newDataTable.Rows.Add(input1, input2.ToString());
            workbook.Sheet["Sheet1"].WriteRange(newDataTable, input3, true);
        
        }
        
        private void PrintDataTable(System.Data.DataTable dt)
        {
            Console.WriteLine("start print data table");
            
            for(int i = 0; i < dt.Rows.Count; ++i) 
            {
                string row = string.Empty; 
                foreach (var value in dt.Rows[i].ItemArray)
                {
                    row += value.ToString() + " ";
                }
                Console.WriteLine(row);
            }
            
            Console.WriteLine("end print data table");
        }
    }
}
""",
        "implementationFormat": "Code",
        "implementationType": "TestCase",
        "currentData": None,
        "userRequest": "",
    },
    "test_data": [
        {"input1": "Test String", "input2": 10, "input3": True},
        {"input1": "", "input2": 20, "input3": False},
        {"input1": "This is a very long test string that exceeds normal length", "input2": -5, "input3": True},
        {"input1": "!@#$%^&*()_+", "input2": 0, "input3": False},
        {"input1": "   ", "input2": 100000, "input3": True},
        {"input1": None, "input2": 15, "input3": False},
        {"input1": "12345", "input2": -10, "input3": True},
        {"input1": "MiXeD CaSe", "input2": 0, "input3": False},
        {"input1": "Hello 🌍", "input2": 25, "input3": True},
        {"input1": "  Leading and trailing spaces  ", "input2": 30, "input3": False},
        {"input1": "A", "input2": 5, "input3": True},
        {"input1": "1", "input2": -1, "input3": False},
        {"input1": "Test123", "input2": 50, "input3": True},
        {"input1": "<div>Test</div>", "input2": 40, "input3": False},
        {"input1": "' OR '1'='1", "input2": 100, "input3": True},
    ],
}

cases = {
    "case_lmyaml_1": case_lmyaml_1,
    "case_lmyaml_2": case_lmyaml_2,
    "case_lmyaml_3": case_lmyaml_3,
    "case_lmyaml_4": case_lmyaml_4,
    "case_code_1": case_code_1,
    "case_code_2": case_code_2,
}
