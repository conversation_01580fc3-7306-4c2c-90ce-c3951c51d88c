import pytest

from services.studio._text_to_workflow.expression_generation.expression_generation_helper import ExpressionGenerationHelper
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionGenerationRequest


@pytest.mark.parametrize(
    "test_name, user_request, name, expected_result, error_message",
    [
        (
            "regex_in_user_request",
            "Create a regex pattern to match email addresses",
            "regex_in_user_request",
            True,
            "Request with 'regex' in userRequest should return True",
        ),
        (
            "match_in_user_request",
            "I need to match all occurrences of a pattern",
            "match_in_user_request",
            True,
            "Request with 'match' in userRequest should return True",
        ),
        (
            "regular_expression_in_user_request",
            "I need a regular expression for phone numbers",
            "regular_expression_in_user_request",
            True,
            "Request with 'regular expression' in userRequest should return True",
        ),
        ("keyword_in_name", "Create a function", "Regex validator", True, "Request with keyword in name should return True"),
        ("no_keywords", "Create a function to add two numbers", "Addition function", False, "Request with no keywords should return False"),
        ("empty_request", "", "empty_request", False, "Empty request should return False"),
        ("case_insensitivity", "I need a REGEX pattern", "empty_request", True, "Keywords should be matched case-insensitively"),
    ],
)
def test_check_is_complex_request(test_name, user_request, name, expected_result, error_message):
    """Test the check_is_complex_request method with various inputs."""
    request = ExpressionGenerationRequest(userRequest=user_request, name=name)
    result = ExpressionGenerationHelper.check_is_complex_request(request)
    assert result == expected_result, error_message
