import pytest

from services.studio._text_to_workflow.expression_generation.variables_parser import parse_variables, parse_variables_api_workflows, parse_variables_bpmn


@pytest.mark.parametrize(
    "available_variables, expected",
    [
        ([], ""),
        ([{"name": "employee", "type": "Employee"}], "employee as Employee"),
        (
            [{"name": "var1", "type": "String"}, {"name": "var2", "type": "Int32"}, {"name": "var3", "type": "Boolean"}],
            "var1 as String, var2 as Int32, var3 as Boolean",
        ),
        ([{"name": "employee1", "type": "Employee"}, {"name": "employee2", "type": "Employee"}], "employee1 as Employee, employee2 as Employee"),
        (
            [
                {
                    "name": "email",
                    "type": "UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
                }
            ],
            "email as UiPath.MicrosoftOffice365.Models.Office365Message",
        ),
    ],
    ids=["empty_variables", "single_variable", "multiple_variables_different_types", "multiple_variables_same_type", "single_complex_type"],
)
def test_parse_variables_api_workflows(available_variables, expected):
    result = parse_variables_api_workflows(available_variables)
    assert result == expected


@pytest.mark.parametrize(
    "available_variables, expected",
    [
        ([], ""),
        ([{"name": "employee", "type": "Employee"}], "employee as Employee"),
        (
            [{"name": "var1", "type": "String"}, {"name": "var2", "type": "Int32"}, {"name": "var3", "type": "Boolean"}],
            "var1 as String\nvar2 as Int32\nvar3 as Boolean",
        ),
        ([{"name": "employee1", "type": "Employee"}, {"name": "employee2", "type": "Employee"}], "employee1 as Employee\nemployee2 as Employee"),
        (
            [
                {
                    "name": "email",
                    "type": "UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
                }
            ],
            "email as UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
        ),
    ],
    ids=["empty_variables", "single_variable", "multiple_variables_different_types", "multiple_variables_same_type", "single_complex_type"],
)
def test_parse_variables(available_variables, expected):
    result = parse_variables(available_variables)
    assert result == expected


def test_parse_variables_bpmn():
    variable1 = {
        "name": "email",
        "type": "UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
        "id": "id1234",
        "activityName": "MyActivity",
    }
    variable2 = {
        "name": "employeeName",
        "type": "System.String",
        "id": "v4321",
        "activityName": "Operate Machine",
    }
    result = parse_variables_bpmn([variable1, variable2])
    expected = "email as UiPath.MicrosoftOffice365.Models.Office365Message with Id: id1234 in Activity: 'MyActivity', employeeName as System.String with Id: v4321 in Activity: 'Operate Machine'"
    assert result == expected
