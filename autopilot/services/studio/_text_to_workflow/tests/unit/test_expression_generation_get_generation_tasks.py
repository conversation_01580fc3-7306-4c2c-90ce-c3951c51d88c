from typing import Any, Dict, Generator
from unittest.mock import MagicMock, patch

import pytest

from services.studio._text_to_workflow.expression_generation.expression_generation_endpoint import _get_generation_task
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    ExpressionGenerationRequest,
    ExpressionLanguageType,
    SourceType,
)
from services.studio._text_to_workflow.expression_generation.expression_generation_task import (
    ApiWorkflowExpressionGenerationTask,
    BPMNExpressionGenerationTask,
    ExpressionGenerationTask,
    JsInvokeExpressionGenerationTask,
)


@pytest.fixture
def mock_task_configurations() -> Generator[Dict[SourceType, Any], None, None]:
    with patch("services.studio._text_to_workflow.expression_generation.expression_generation_endpoint.GENERATION_TASK_CONFIGURATIONS") as mock:
        # Set up mock task objects
        mock_workflow_task = MagicMock(spec=ExpressionGenerationTask)
        mock_api_workflow_task = MagicMock(spec=ApiWorkflowExpressionGenerationTask)
        mock_bpmn_task = MagicMock(spec=BPMNExpressionGenerationTask)
        mock_jsinvoke_task = MagicMock(spec=JsInvokeExpressionGenerationTask)
        # Configure the mock dictionary to return our mock tasks
        mock_task_mapping = {
            SourceType.Workflow: mock_workflow_task,
            SourceType.ApiWorkflow: mock_api_workflow_task,
            SourceType.BPMN: mock_bpmn_task,
            SourceType.JsInvoke: mock_jsinvoke_task,
        }

        mock.__getitem__.side_effect = lambda key: mock_task_mapping[key]

        yield mock_task_mapping


@pytest.mark.parametrize(
    "source, expression_language, expected_task_type, activityTypeName",
    [
        # Test case 1: BPMN source should return BPMN task
        (SourceType.BPMN, ExpressionLanguageType.CSharp.value, SourceType.BPMN, None),
        # Test case 2: JavaScript language should return ApiWorkflow task
        (SourceType.Workflow, ExpressionLanguageType.JavaScript.value, SourceType.ApiWorkflow, None),
        # Test case 3: JQ language should return ApiWorkflow task
        (SourceType.Workflow, ExpressionLanguageType.JQ.value, SourceType.ApiWorkflow, None),
        # Test case 4: CSharp language with default source should return Workflow task
        (None, ExpressionLanguageType.CSharp.value, SourceType.Workflow, None),
        # Test case 5: VBNET language with default source should return Workflow task
        (None, ExpressionLanguageType.VBNET.value, SourceType.Workflow, None),
        # Test case 6: Javascript language with activity type name should return JsInvoke task
        (None, ExpressionLanguageType.JavaScript.value, SourceType.JsInvoke, "JsInvoke"),
    ],
)
def test_get_generation_task(mock_task_configurations, source, expression_language, expected_task_type, activityTypeName):
    # Create request with the current test parameters
    request_kwargs = {
        "expressionLanguage": expression_language,
        "userRequest": "test",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "availableVariables": [],
        "activityTypeName": activityTypeName,
    }

    # Add source parameter only if it's provided (to test default behavior)
    if source is not None:
        request_kwargs["source"] = source

    request = ExpressionGenerationRequest(**request_kwargs)

    # Call the function and verify result
    result = _get_generation_task(request)
    assert result == mock_task_configurations[expected_task_type]
