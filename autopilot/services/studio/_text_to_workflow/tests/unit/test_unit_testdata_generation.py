import copy

import pytest
from langchain.schema.messages import SystemMessage

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.testdata_generation.testdata_generation_task import TestdataGenerationTask
from services.studio._text_to_workflow.tests.unit.fixture.testdata_fixture import cases
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


@pytest.fixture
def setup_config():
    _testdata_generation_task = TestdataGenerationTask()

    llm_model = ModelManager().get_llm_model({"model_name": "testdata_generation_model", "seed": 30}, ConsumingFeatureType.TESTDATA_GENERATION)

    system_msg = SystemMessage(content=_testdata_generation_task.config["testdata_generation"]["system_template_instructions"])
    messages_lmyaml = [system_msg]
    messages_code = [system_msg]

    _testdata_generation_task.add_demos_to_messages(messages_lmyaml, _testdata_generation_task.load_demos_code_to_testdata)
    _testdata_generation_task.add_demos_to_messages(messages_code, _testdata_generation_task.load_demos_coded)

    llm_model_extra_data = ModelManager().get_llm_model(
        {"model_name": "testdata_generation_extra_data_model", "seed": 30}, ConsumingFeatureType.TESTDATA_GENERATION
    )
    messages_extra_testdata = _testdata_generation_task.get_extra_testdata_demo_messages()
    return _testdata_generation_task, llm_model, messages_lmyaml, messages_code, llm_model_extra_data, messages_extra_testdata


@pytest.fixture(scope="module")
def cases_fixture():
    return cases


@pytest.fixture
def case(request):
    return request.getfixturevalue(request.param)


lmyaml_cases = ["case_lmyaml_1", "case_lmyaml_2", "case_lmyaml_3", "case_lmyaml_4"]
code_cases = ["case_code_1", "case_code_2"]


@pytest.mark.asyncio
@pytest.mark.parametrize("case", lmyaml_cases)
async def test_generate_code_from_lmyaml(case: str, cases_fixture: dict, setup_config):
    _testdata_generation_task, llm_model, _, _, _, _ = setup_config

    input = cases_fixture[case]
    input["request"]["implementation"] = input["request"]["implementation_yaml"]
    code = await _testdata_generation_task.generate_code_from_lmyaml(llm_model, input["request"])

    def check_if_code(code: str) -> bool:
        if "Execute(" in code:
            return True
        return False

    assert check_if_code(code)


@pytest.mark.parametrize("case", lmyaml_cases)
def test_load_demos_yaml_to_code(case: str, cases_fixture: dict, setup_config):
    _testdata_generation_task, _, _, _, _, _ = setup_config

    input = cases_fixture[case]

    input["request"]["implementation"] = input["request"]["implementation_yaml"]
    _testdata_generation_task.load_demos_yaml_to_code(input["request"])

    def check_demos(demos, expected_result: list) -> bool:
        demos_name = [demo["name"] for demo in demos]
        for demo in expected_result:
            if demo not in demos_name:
                return False
        return True

    assert check_demos(_testdata_generation_task.demos, input["result_load_demos_yaml_to_code"])


@pytest.mark.asyncio
@pytest.mark.parametrize("case", lmyaml_cases + code_cases)
async def test_generate_testdata(case: str, cases_fixture: dict, setup_config):
    _testdata_generation_task, llm_model, messages_lmyaml, messages_code, _, _ = setup_config

    input = cases_fixture[case]

    if input["request"]["implementation"] == "LMYAML":
        messages = copy.deepcopy(messages_lmyaml)
    else:
        messages = copy.deepcopy(messages_code)

    testdata_dict, _ = await _testdata_generation_task.generate_testdata(messages, llm_model, input["request"])

    def check_format(testdata_dict: dict) -> bool:
        if "requested_entries" not in testdata_dict:
            return False
        if "test_data" not in testdata_dict:
            return False
        return True

    assert check_format(testdata_dict)


@pytest.mark.asyncio
@pytest.mark.parametrize("case", lmyaml_cases + code_cases)
async def test_request_extra_data(case: str, cases_fixture: dict, setup_config):
    _testdata_generation_task, _, _, _, llm_model_extra_data, messages_extra_testdata = setup_config

    input = cases_fixture[case]

    result_extra_data = await _testdata_generation_task.request_extra_data(llm_model_extra_data, input["request"], messages_extra_testdata, input["test_data"])

    def check_extra_data(extra_data, current_data) -> bool:
        if "test_data" not in extra_data:
            return False
        for data in extra_data.get("test_data"):
            if data in current_data:
                return False
        return True

    check_extra_data(result_extra_data, input["test_data"])
