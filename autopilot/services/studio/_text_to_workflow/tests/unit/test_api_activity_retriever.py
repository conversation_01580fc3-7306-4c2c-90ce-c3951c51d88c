import pytest

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.common.schema import (
    ActivityDefinition,
    ActivitySearchOptions,
    PlanStep,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager


@pytest.fixture
def retriever():
    """Fixture to provide an instance of APIActivitiesRetriever."""
    api_retriever = APIActivitiesRetriever()
    api_retriever.build()
    return api_retriever


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.parametrize("description", ["Search on youtube for the latest videos about AI"])
def test_api_activity_retriever_returns_allowed_activities(retriever, embedding_model, description):
    """Test that the API activity retriever returns only allowed activities"""
    # Create a plan step with the given description
    embeddings = embedding_model.encode([description])
    step = PlanStep(text=description, embedding=embeddings, triggers=[], activities=[])

    # Search for activities
    activities = retriever.search(
        step=step,
        connections=[],
        activity_search_options=ActivitySearchOptions(
            mode=None,
            target_framework="Portable",
            ignored_namespaces=set(),
            ignored_activities=set(),
        ),
        activity_type="activity",
        k=5,
    )

    # If we expect to find something, check we got at least one result
    assert len(activities) >= 1
    # Check the activity has a name
    for activity in activities:
        activity_def: ActivityDefinition = activity
        assert activity_def["fullActivityName"] in constants.API_ACTIVITY_NAMES


def test_api_activity_retriever_does_not_return_excluded_activities(retriever):
    """Test that the API activity retriever doesn't return excluded activities"""
    # Create a plan step with the given description

    for activity_name in constants.ACTIVITIES_EXCLUDED:
        activity = retriever.get(constants.DAP_NAMESPACE + "." + activity_name, "activity")
        assert activity is None

    for activity_name in ["Microsoft_Azure_OpenAIGenerate_Chat_Completion", "OpenAIGenerate_Chat_Completion"]:
        activity = retriever.get(constants.DAP_NAMESPACE + "." + activity_name, "activity")
        assert activity is not None
