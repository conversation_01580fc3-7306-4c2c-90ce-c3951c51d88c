import copy
import pathlib

import pytest

from services.studio._text_to_workflow.common.walkers import ActivityPropertiesRestorer, ActivityPropertiesTruncator
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

cases_path = pathlib.Path(__file__).parent / "test_activity_truncator_restorer_examples.yaml"
_cases = ("test_assign",)


@pytest.fixture(scope="module")
def cases():
    return yaml_load(cases_path)


@pytest.mark.parametrize("case", _cases)
def test_erase_after_current_activity(case: str, cases):
    workflow_dict = cases[case]
    workflow = Workflow("", "", workflow_dict)
    truncator = ActivityPropertiesTruncator()
    restorer = ActivityPropertiesRestorer()
    original_workflow = copy.deepcopy(workflow.to_dict())

    original_param_values = truncator.truncate(workflow)
    truncated_workflow = copy.deepcopy(workflow.to_dict())
    assert truncated_workflow["workflow"][0]["params"]["Value"].endswith("... (truncated value)]]"), "The parameter value should be trucated"

    restorer.restore(workflow, original_param_values)
    restored_workflow = copy.deepcopy(workflow.to_dict())
    assert original_workflow == restored_workflow, "The workflow should be restored exactly as is"
