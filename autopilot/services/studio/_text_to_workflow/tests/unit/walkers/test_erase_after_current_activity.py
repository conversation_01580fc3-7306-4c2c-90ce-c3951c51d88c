import pathlib

import pytest

from services.studio._text_to_workflow.common.walkers import erase_after_current_activity
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils.workflow_utils import get_diff
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

cases_path = pathlib.Path(__file__).parent / "test_erase_after_current_activity_examples.yaml"
_cases = (
    "test trigger",
    "test root activity",
    "test inner activity",
    "test if activity else branch",
    "test if activity then branch",
    "test switch cases",
)


@pytest.fixture(scope="module")
def cases():
    return yaml_load(cases_path)


@pytest.mark.parametrize("case", _cases)
def test_erase_after_current_activity(case: str, cases):
    case_pair = cases[case]
    pre_erasure_yaml = yaml_dump(case_pair["input"]).strip()
    post_erasure_expected_yaml = yaml_dump(case_pair["output"]).strip()

    workflow_object = Workflow("", "", yaml_load(pre_erasure_yaml))
    erase_after_current_activity(workflow_object)
    post_erasure_yaml = workflow_object.lmyaml(include_packages=False)
    assert post_erasure_expected_yaml == post_erasure_yaml, f"Erasure failed!\n{get_diff(post_erasure_expected_yaml, post_erasure_yaml, 3)}"
