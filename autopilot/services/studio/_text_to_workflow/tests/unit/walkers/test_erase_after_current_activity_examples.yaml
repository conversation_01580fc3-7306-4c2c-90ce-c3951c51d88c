test trigger:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
      currentActivity: true
      params:
        Result: '[[Result]]'
    workflow:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: Info
        Message: '[["Please don''t erase me!"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
      currentActivity: true
      params:
        Result: '[[Result]]'
    workflow: []
test root activity:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: Info
        Message: '[["I''m confident you won''t erase me!"]]'
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      currentActivity: true
      params:
        Level: Info
        Message: '[["Hello world!"]]'
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: Info
        Message: '[["What''s the meaning of life?"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: Info
        Message: '[["I''m confident you won''t erase me!"]]'
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      currentActivity: true
      params:
        Level: Info
        Message: '[["Hello world!"]]'
test inner activity:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: For Each number in list
      activity: UiPath.Core.Activities.ForEach<int>
      params:
        Values: '[[numbers]]'
        Body:
          variables:
          - name: currentNumber
            type: int
          Handler:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
              Message: '[["We are at"]]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            currentActivity: true
            params:
              Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
              Message: '[[currentNumber.ToString()]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
              Message: '[["What''s there to follow?"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: For Each number in list
      activity: UiPath.Core.Activities.ForEach<int>
      params:
        Values: '[[numbers]]'
        Body:
          variables:
          - name: currentNumber
            type: int
          Handler:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
              Message: '[["We are at"]]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            currentActivity: true
            params:
              Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
              Message: '[[currentNumber.ToString()]]'
test if activity else branch:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: If the test is failing
      activity: System.Activities.Statements.If
      params:
        Condition: '[["innevitable" == "failure"]]'
        Then:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["Couldn''t help it!"]]'
        Else:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          currentActivity: true
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["How did we get here?"]]'
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["Are we living in a simulation?"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: If the test is failing
      activity: System.Activities.Statements.If
      params:
        Condition: '[["innevitable" == "failure"]]'
        Then:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["Couldn''t help it!"]]'
        Else:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          currentActivity: true
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["How did we get here?"]]'
test if activity then branch:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: If the test is failing
      activity: System.Activities.Statements.If
      params:
        Condition: '[["innevitable" == "failure"]]'
        Then:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          currentActivity: true
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["Couldn''t help it!"]]'
        Else:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["How did we get here?"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: If the test is failing
      activity: System.Activities.Statements.If
      params:
        Condition: '[["innevitable" == "failure"]]'
        Then:
        - thought: Log Message
          activity: UiPath.Core.Activities.LogMessage
          currentActivity: true
          params:
            Level: '[[UiPath.Core.Activities.LogLevel.Info]]'
            Message: '[["Couldn''t help it!"]]'
        Else: []
test switch cases:
  input:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: Switch based on integer value
      activity: System.Activities.Statements.Switch<System.Int32>
      params:
        Expression: '[[ValueINT]]'
        Cases:
          1:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["First"]]'
          2:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["Second"]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            currentActivity: true
            params:
              Level: '[[Info]]'
              Message: '[["Second again?"]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["Second again and again"]]'
          3:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["Third"]]'
        Default:
        - thought: Log "Other choice"
          activity: UiPath.Core.Activities.LogMessage
          params:
            Level: '[[Info]]'
            Message: '[["Just in case"]]'
  output:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: Switch based on integer value
      activity: System.Activities.Statements.Switch<System.Int32>
      params:
        Expression: '[[ValueINT]]'
        Cases:
          1:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["First"]]'
          2:
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            params:
              Level: '[[Info]]'
              Message: '[["Second"]]'
          - thought: Log Message
            activity: UiPath.Core.Activities.LogMessage
            currentActivity: true
            params:
              Level: '[[Info]]'
              Message: '[["Second again?"]]'
          3: []
        Default: []
