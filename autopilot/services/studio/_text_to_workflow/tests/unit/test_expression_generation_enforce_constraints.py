from unittest.mock import AsyncMock, MagicMock, patch

import langchain.schema
import pytest

from services.studio._text_to_workflow.expression_generation.expression_generation_schema import (
    ExpressionGenerationModelResponse,
    ExpressionLanguageType,
)
from services.studio._text_to_workflow.expression_generation.expression_generation_task import ExpressionGenerationTask
from services.studio._text_to_workflow.models.model_manager import ModelManager


class TestExpressionGenerationEnforceGenerationConstraints:
    @pytest.fixture
    def expression_generation_task(self):
        with patch("pathlib.Path"), patch("services.studio._text_to_workflow.expression_generation.expression_generation_task.yaml_load"):
            return ExpressionGenerationTask("config.yaml", "demos.yaml")

    @pytest.mark.asyncio
    async def test_enforce_generation_constraints_with_variable_declaration(self, expression_generation_task):
        # Setup
        request = {
            "expressionLanguage": ExpressionLanguageType.CSharp.value,
            "userRequest": "Sort a list of numbers",
            "currentExpression": "",
            "additionalTypeDefinitions": "",
            "availableVariables": [{"name": "numbers", "type": "List<int>"}],
            "expressionTypeDefinition": "List<int>",
        }

        # Result with variable declaration
        result_with_var = ExpressionGenerationModelResponse(
            explanation="This expression sorts the numbers list",
            expression="var sortedList = numbers.OrderBy(n => n).ToList();",
        )

        # Expected result after validation
        expected_result = ExpressionGenerationModelResponse(
            explanation="This sorts the numbers without declaring a variable",
            expression="numbers.OrderBy(n => n).ToList()",
        )

        # Mock model and its response
        model_mock = MagicMock()
        model_mock.deployment_name = "test-model"

        # Mock the ModelManager to return our mocked model
        with patch.object(ModelManager, "get_llm_model", return_value=model_mock):
            # Mock structured output
            model_mock.with_structured_output.return_value = model_mock

            # Mock the chat chain invocation
            chat_chain_mock = AsyncMock()
            chat_chain_mock.ainvoke.return_value = expected_result

            # Replace the prompt | model operation to return our mocked chat chain
            with patch("langchain.prompts.ChatPromptTemplate.__or__", return_value=chat_chain_mock):
                # Mock call_open_ai to return our expected result
                expression_generation_task.call_open_ai_with_structured_output = AsyncMock(return_value=(expected_result, None))

                # Call the method under test
                validated_result = await expression_generation_task.enforce_generation_constraints(
                    request, result_with_var, [langchain.schema.SystemMessage(content="System message")], {"key": "value"}
                )

                # Verify the result
                assert validated_result.explanation == expected_result.explanation
                assert validated_result.expression == expected_result.expression

                # Verify that call_open_ai was called
                expression_generation_task.call_open_ai_with_structured_output.assert_called_once()

    @pytest.mark.asyncio
    async def test_enforce_generation_constraints_no_variable_declaration(self, expression_generation_task):
        # Setup for a valid expression without variable declaration
        request = {
            "expressionLanguage": ExpressionLanguageType.CSharp.value,
            "userRequest": "Sort a list of numbers",
            "currentExpression": "",
            "additionalTypeDefinitions": "",
            "availableVariables": [{"name": "numbers", "type": "List<int>"}],
            "expressionTypeDefinition": "List<int>",
        }

        # Valid result without variable declaration
        valid_result = ExpressionGenerationModelResponse(explanation="This expression sorts the numbers list", expression="numbers.OrderBy(n => n).ToList()")

        # Call the method under test
        validated_result = await expression_generation_task.enforce_generation_constraints(request, valid_result, [], {})

        # Verify no modifications were made
        assert validated_result.explanation == valid_result.explanation
        assert validated_result.expression == valid_result.expression

    @pytest.mark.asyncio
    async def test_enforce_generation_constraints_vbnet_variable_declaration(self, expression_generation_task):
        # Setup for VB.NET
        request = {
            "expressionLanguage": ExpressionLanguageType.VBNET.value,
            "userRequest": "Sort a list of numbers",
            "currentExpression": "",
            "additionalTypeDefinitions": "",
            "availableVariables": [{"name": "numbers", "type": "List(Of Integer)"}],
            "expressionTypeDefinition": "List(Of Integer)",
        }

        # Result with Dim declaration in VB.NET
        result_with_dim = ExpressionGenerationModelResponse(
            explanation="This expression sorts the numbers list",
            expression="Dim sortedList = numbers.OrderBy(Function(n) n).ToList()",
        )

        # Expected result after validation
        expected_result = ExpressionGenerationModelResponse(
            explanation="This sorts the numbers without declaring a variable",
            expression="numbers.OrderBy(Function(n) n).ToList()",
        )

        # Mock model and its response
        model_mock = MagicMock()
        model_mock.deployment_name = "test-model"

        # Mock the ModelManager to return our mocked model
        with patch.object(ModelManager, "get_llm_model", return_value=model_mock):
            # Mock structured output
            model_mock.with_structured_output.return_value = model_mock

            # Mock the chat chain invocation
            chat_chain_mock = AsyncMock()
            chat_chain_mock.ainvoke.return_value = expected_result

            # Replace the prompt | model operation to return our mocked chat chain
            with patch("langchain.prompts.ChatPromptTemplate.__or__", return_value=chat_chain_mock):
                # Mock call_open_ai to return our expected result
                expression_generation_task.call_open_ai = AsyncMock(return_value=(expected_result, None))

                # Call the method under test
                validated_result = await expression_generation_task.enforce_generation_constraints(
                    request, result_with_dim, [langchain.schema.SystemMessage(content="System message")], {"key": "value"}
                )

                # Verify the result
                assert validated_result.explanation == expected_result.explanation
                assert validated_result.expression == expected_result.expression
