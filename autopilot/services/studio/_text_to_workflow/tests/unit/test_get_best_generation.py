from unittest.mock import MagicMock

import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    WorkflowProcessingErrorType,
)


@pytest.fixture
def api_wf_draft_service():
    """Create a mock ApiWfDraftService instance for testing."""
    activities_retriever = MagicMock()
    post_gen_processing_service = MagicMock()
    return ApiWfDraftService(activities_retriever, post_gen_processing_service)


@pytest.mark.parametrize(
    "generated_answers,expected_index",
    [
        (
            [
                [WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST, WorkflowProcessingErrorType.MISSING_TRIGGER_TYPE],
                [WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST, WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST],
                [WorkflowProcessingErrorType.MISSING_OR_INVALID_CONNECTION],
            ],
            2,
        ),
        (
            [
                [WorkflowProcessingErrorType.MISSING_PARAM, WorkflowProcessingErrorType.MISSING_PARAM],
                [WorkflowProcessingErrorType.MISSING_PARAM],
            ],
            1,
        ),
        (
            [
                [WorkflowProcessingErrorType.MISSING_OR_INVALID_CONNECTION],
                [WorkflowProcessingErrorType.MISSING_OR_INVALID_CONNECTION],
            ],
            0,
        ),
        (
            [
                [WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST],
                [WorkflowProcessingErrorType.MISSING_PARAM, WorkflowProcessingErrorType.MISSING_PARAM],
                [WorkflowProcessingErrorType.MISSING_PARAM],
            ],
            2,
        ),
        (
            [
                [WorkflowProcessingErrorType.MISSING_PARAM],
                [],
            ],
            1,
        ),
    ],
)
def test_get_least_severe_error_trace_index(generated_answers, expected_index):
    """Test that the method correctly identifies the generation with the least severe errors."""
    result = ApiWfDraftService.get_least_severe_error_trace_index(generated_answers)
    assert result == expected_index


@pytest.mark.parametrize(
    "severity_level,generation_errors,expected_count",
    [
        (1, [WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST, WorkflowProcessingErrorType.MISSING_TRIGGER_TYPE], 1),
        (2, [WorkflowProcessingErrorType.MISSING_PARAM, WorkflowProcessingErrorType.MISSING_PARAM], 2),
        (0, [], 0),
    ],
)
def test_get_current_level_error_count(severity_level, generation_errors, expected_count):
    """Test that the method correctly counts errors of a specific severity level."""
    result = ApiWfDraftService.get_current_level_error_count(severity_level, generation_errors)
    assert result == expected_count
