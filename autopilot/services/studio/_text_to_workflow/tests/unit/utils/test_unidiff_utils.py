import functools

import pytest

from services.studio._text_to_workflow.utils.unidiff_utils import (
    MergeConflict,
    SkipToTheGoodBit,
    SoftUniDiff,
    WorkflowFixFlairType,
    apply_merge_conflict,
    apply_skip_to_the_good_bit,
    apply_soft_unidiff,
    apply_standard_unidiff,
    get_merge_conflict,
    get_skip_to_the_good_bit,
    get_soft_unidiff,
    get_unidiff,
)


# Test fixtures for diff testing
@pytest.fixture
def original_content():
    return """\
def calculate_sum(a, b):
    # Add two numbers and return the result
    return a + b

def multiply(a, b):
    # Multiply two numbers
    return a * b

def divide(a, b):
    # Divide a by b
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b
"""


@pytest.fixture
def modified_content():
    return """\
def calculate_sum(a, b):
    # Add two numbers and return the result
    result = a + b
    return result

def multiply(a, b):
    # Multiply two numbers
    return a * b

def subtract(a, b):
    # Subtract b from a
    return a - b

def divide(a, b):
    # Divide a by b
    if b == 0:
        raise ValueError("Division by zero is not allowed")
    return a / b
"""


@pytest.mark.skip(reason="Not used currently")  # TODO: if we come back to this, fix tests
class TestStandardUniDiff:
    """Test cases for standard unidiff functionality."""

    def test_get_unidiff(self, original_content, modified_content):
        """Test that get_unidiff produces correct diff."""
        diff = get_unidiff(original_content, modified_content)

        # Check that diff contains expected changes
        assert "@@ " in diff, "Diff should contain hunk headers"
        assert "-    return a + b" in diff, "Diff should show removed line"
        assert "+    result = a + b" in diff, "Diff should show added line"
        assert "+    return result" in diff, "Diff should show added line"
        assert "+def subtract(a, b):" in diff, "Diff should show new function"
        assert '-        raise ValueError("Cannot divide by zero")' in diff, "Diff should show changed error message"
        assert '+        raise ValueError("Division by zero is not allowed")' in diff, "Diff should show changed error message"

    def test_apply_standard_unidiff(self, original_content, modified_content):
        """Test that applying standard unidiff works correctly."""
        diff = get_unidiff(original_content, modified_content)
        result = apply_standard_unidiff(original_content, diff)

        # Result should match modified content
        assert result == modified_content, "Applying diff should transform original to modified content"

        # Test round-trip consistency
        reverse_diff = get_unidiff(modified_content, original_content)
        round_trip = apply_standard_unidiff(modified_content, reverse_diff)
        assert round_trip == original_content, "Applying reverse diff should transform back to original"


class TestSoftUniDiff:
    """Test cases for soft unidiff functionality."""

    def test_get_soft_unidiff(self, original_content, modified_content):
        """Test that get_soft_unidiff produces correct diff."""
        diff = get_soft_unidiff(original_content, modified_content)

        # Check that diff uses the soft unidiff format with |, +, - markers
        assert "@@ ... @@" in diff, "Soft diff should contain simplified hunk headers"
        assert "-    return a + b" in diff, "Soft diff should show removed line with - prefix"
        assert "+    result = a + b" in diff, "Soft diff should show added line with + prefix"
        assert "+    return result" in diff, "Soft diff should show added line with + prefix"

    def test_apply_soft_unidiff(self, original_content, modified_content):
        """Test that applying soft unidiff works correctly."""
        diff = get_soft_unidiff(original_content, modified_content)
        result = apply_soft_unidiff(original_content, diff)

        # Result should match modified content
        assert result == modified_content, "Applying soft diff should transform original to modified content"

        # Test round-trip consistency
        reverse_diff = get_soft_unidiff(modified_content, original_content)
        round_trip = apply_soft_unidiff(modified_content, reverse_diff)
        assert round_trip == original_content, "Applying reverse soft diff should transform back to original"

    def test_softunidiff_class(self, original_content, modified_content):
        """Test the SoftUniDiff class directly."""
        diff = get_soft_unidiff(original_content, modified_content)
        soft_diff = SoftUniDiff(diff)

        # Test that the class can recreate the diff
        assert "@@ ... @@" in soft_diff.get_diff(), "SoftUniDiff should recreate diff with hunk headers"

        # Test that patch method works
        result = soft_diff.patch(original_content)
        assert result == modified_content, "SoftUniDiff patch method should transform content"


class TestMergeConflict:
    """Test cases for merge conflict functionality."""

    def test_get_merge_conflict(self, original_content, modified_content):
        """Test that get_merge_conflict produces correct merge conflict format."""
        conflict = get_merge_conflict(original_content, modified_content)

        # Check that conflict has correct format
        assert "=======" in conflict, "Merge conflict should contain separator markers"
        assert "<<<<<<< SEARCH" in conflict, "Merge conflict should contain search section"
        assert ">>>>>>> REPLACE" in conflict, "Merge conflict should contain replace section"

        # Check content is included
        assert "return a + b" in conflict, "Original content should be in conflict"
        assert "result = a + b" in conflict, "Modified content should be in conflict"

    def test_get_merge_hybrid(self, original_content, modified_content):
        """Test that get_merge_hybrid produces correct merge hybrid format."""
        conflict = get_merge_conflict(original_content, modified_content, flair=WorkflowFixFlairType.MergeHybrid, use_ellipsis=True)

        # Check that conflict has correct format
        assert "=======" in conflict, "Merge hybrid should contain separator markers"
        assert "<<<<<<< SEARCH" in conflict, "Merge conflict should contain search section"
        assert ">>>>>>> REPLACE" in conflict, "Merge conflict should contain replace section"

        # Check content is included
        assert "return a + b" in conflict, "Original content should be in conflict"
        assert "result = a + b" in conflict, "Modified content should be in conflict"

    def test_apply_merge_conflict(self, original_content, modified_content):
        """Test that applying merge conflict patch works correctly."""
        conflict = get_merge_conflict(original_content, modified_content, flair=WorkflowFixFlairType.MergeConflict)
        result = apply_merge_conflict(original_content, conflict, flair=WorkflowFixFlairType.MergeConflict)

        # Result should match modified content
        assert result == modified_content, "Applying merge conflict patch should transform original to modified content"

        # Test round-trip consistency
        reverse_conflict = get_merge_conflict(modified_content, original_content, flair=WorkflowFixFlairType.MergeConflict)
        round_trip = apply_merge_conflict(modified_content, reverse_conflict, flair=WorkflowFixFlairType.MergeConflict)
        assert round_trip == original_content, "Applying reverse merge conflict should transform back to original"

    def test_apply_merge_hybrid(self, original_content, modified_content):
        """Test that applying merge hybrid patch works correctly."""
        conflict = get_merge_conflict(original_content, modified_content, flair=WorkflowFixFlairType.MergeHybrid, use_ellipsis=True)
        result = apply_merge_conflict(original_content, conflict, flair=WorkflowFixFlairType.MergeHybrid)

        # Result should match modified content
        assert result == modified_content, "Applying merge conflict patch should transform original to modified content"

        # Test round-trip consistency
        reverse_conflict = get_merge_conflict(modified_content, original_content, flair=WorkflowFixFlairType.MergeHybrid, use_ellipsis=True)
        round_trip = apply_merge_conflict(modified_content, reverse_conflict, flair=WorkflowFixFlairType.MergeHybrid)
        assert round_trip == original_content, "Applying reverse merge conflict should transform back to original"

    def test_merge_conflict_class(self, original_content, modified_content):
        """Test the MergeConflict class directly."""
        conflict = get_merge_conflict(original_content, modified_content, flair=WorkflowFixFlairType.MergeConflict)

        # Test with default flair
        merger = MergeConflict(conflict)
        result, _ = merger.patch(original_content)
        assert result == modified_content, "MergeConflict patch method should transform content"

        # Test with different flair types
        conflict_with_lines = get_merge_conflict(original_content, modified_content, flair=WorkflowFixFlairType.MergeConflictWithLines)
        merger_with_lines = MergeConflict(conflict_with_lines, flair=WorkflowFixFlairType.MergeConflictWithLines)
        result_with_lines, _ = merger_with_lines.patch(original_content)
        assert result_with_lines == modified_content, "MergeConflict with lines should transform content"


class TestSkipToTheGoodBit:
    """Test cases for skip to the good bit functionality."""

    def test_get_skip_to_the_good_bit(self, original_content, modified_content):
        """Test that get_skip_to_the_good_bit produces correct format."""
        skip = get_skip_to_the_good_bit(original_content, modified_content)

        # Check that skip has correct format
        assert "=======" in skip, "Skip should contain separator markers"
        assert "-------" in skip, "Skip should contain section dividers"

        # Format should have four sections: line numbers, prev context, replacement, next context
        sections = skip.count("-------")
        assert sections >= 3, "Skip format should have at least 3 section dividers"

    def test_apply_skip_to_the_good_bit(self, original_content, modified_content):
        """Test that applying skip to the good bit patch works correctly."""
        skip = get_skip_to_the_good_bit(original_content, modified_content)
        result = apply_skip_to_the_good_bit(original_content, skip)

        # Result should match modified content
        assert result == modified_content, "Applying skip patch should transform original to modified content"

        # Test round-trip consistency
        reverse_skip = get_skip_to_the_good_bit(modified_content, original_content)
        round_trip = apply_skip_to_the_good_bit(modified_content, reverse_skip)
        assert round_trip == original_content, "Applying reverse skip should transform back to original"

    def test_skip_to_the_good_bit_class(self, original_content, modified_content):
        """Test the SkipToTheGoodBit class directly."""
        skip = get_skip_to_the_good_bit(original_content, modified_content)
        skipper = SkipToTheGoodBit(skip)

        # Test that patch method works
        result = skipper.patch(original_content)
        assert result == modified_content, "SkipToTheGoodBit patch method should transform content"


def test_indentation_preservation(original_content):
    """Test that diffs preserve indentation correctly."""
    # Create a modified version with different indentation
    modified_with_indent = original_content.replace("    # Add", "        # Add")

    # Test all diff formats preserve indentation
    for diff_type, diff_func, apply_func in [
        ("unidiff", get_unidiff, apply_standard_unidiff),
        ("softunidiff", get_soft_unidiff, apply_soft_unidiff),
        ("mergeconflict", get_merge_conflict, apply_merge_conflict),
        (
            "mergehybrid",
            functools.partial(get_merge_conflict, flair=WorkflowFixFlairType.MergeHybrid, use_ellipsis=True),
            functools.partial(apply_merge_conflict, flair=WorkflowFixFlairType.MergeHybrid),
        ),
        ("skiptothegoodbit", get_skip_to_the_good_bit, apply_skip_to_the_good_bit),
    ]:
        diff = diff_func(original_content, modified_with_indent)
        result = apply_func(original_content, diff)
        assert "        # Add" in result, f"{diff_type} should preserve indentation changes"


def test_large_changes():
    """Test handling of large changes in the content."""
    original = "A\nB\nC\nD\nE\nF\nG\nH\nI\nJ"
    modified = "A\nB\nNEW1\nNEW2\nNEW3\nNEW4\nNEW5\nI\nJ"

    # Test all diff formats handle large changes
    for diff_type, diff_func, apply_func in [
        ("unidiff", get_unidiff, apply_standard_unidiff),
        ("softunidiff", get_soft_unidiff, apply_soft_unidiff),
        ("mergeconflict", get_merge_conflict, apply_merge_conflict),
        (
            "mergehybrid",
            functools.partial(get_merge_conflict, flair=WorkflowFixFlairType.MergeHybrid, use_ellipsis=True),
            functools.partial(apply_merge_conflict, flair=WorkflowFixFlairType.MergeHybrid),
        ),
        ("skiptothegoodbit", get_skip_to_the_good_bit, apply_skip_to_the_good_bit),
    ]:
        diff = diff_func(original, modified)
        result = apply_func(original, diff)
        assert result == modified, f"{diff_type} should handle large changes"


def test_edge_case_empty_content():
    """Test diffing when content is empty."""
    empty = ""
    content = "Some\ncontent\nhere"

    # Test all diff formats handle empty content
    for diff_func, apply_func in [
        (get_unidiff, apply_standard_unidiff),
        (get_soft_unidiff, apply_soft_unidiff),
        (get_merge_conflict, apply_merge_conflict),
        (get_skip_to_the_good_bit, apply_skip_to_the_good_bit),
    ]:
        # Empty to content
        diff1 = diff_func(empty, content)
        result1 = apply_func(empty, diff1)
        assert result1 == content, f"{diff_func.__name__} should handle empty to content"

        # Content to empty
        diff2 = diff_func(content, empty)
        result2 = apply_func(content, diff2)
        assert result2 == empty, f"{diff_func.__name__} should handle content to empty"


@pytest.mark.skip(reason="Trailing newlines")
def test_edge_case_newlines():
    """Test diffing with different newline situations."""
    with_newline = "Line1\nLine2\n"
    without_newline = "Line1\nLine2"

    # Test all diff formats handle newline differences
    for diff_func, apply_func in [
        (get_unidiff, apply_standard_unidiff),
        (get_soft_unidiff, apply_soft_unidiff),
        (get_merge_conflict, apply_merge_conflict),
        (get_skip_to_the_good_bit, apply_skip_to_the_good_bit),
    ]:
        # With to without newline
        diff1 = diff_func(with_newline, without_newline)
        result1 = apply_func(with_newline, diff1)
        assert result1 == without_newline, f"{diff_func.__name__} should handle trailing newline removal"

        # Without to with newline
        diff2 = diff_func(without_newline, with_newline)
        result2 = apply_func(without_newline, diff2)
        assert result2 == with_newline, f"{diff_func.__name__} should handle trailing newline addition"
