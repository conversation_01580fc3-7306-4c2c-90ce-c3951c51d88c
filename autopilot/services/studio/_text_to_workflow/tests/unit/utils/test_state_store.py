import tempfile
from pathlib import Path

import pytest

from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore


# Define a builder class for testing
class TestStateBuilder(StateBuilder):
    def build(self) -> tuple[dict, dict]:
        return {"created_at": "2024-03-21", "data": "test data", "counter": 0}, {}


# Define another builder class for testing
class AnotherStateBuilder(StateBuilder):
    def build(self) -> tuple[dict, dict]:
        return {"different": "data"}, {}


# Fixture to provide a temporary directory for test files
@pytest.fixture(autouse=True)
def temp_test_dir(request):
    with tempfile.TemporaryDirectory() as tmpdir_name:
        tmp_path = Path(tmpdir_name).resolve()
        yield tmp_path  # Yield the resolved path to the temporary directory
        # The temporary directory and its contents are automatically removed here


# --- Test Functions ---


def test_singleton_behavior(temp_test_dir):
    """Tests that StateStore implements the singleton pattern correctly based on key."""
    builder = TestStateBuilder()
    # Construct paths within the temporary directory
    path1 = temp_test_dir / "test1.pkl"
    path2 = temp_test_dir / "test2.pkl"
    path5 = temp_test_dir / "test5.pkl"

    store1 = StateStore(path1, builder)
    store2 = StateStore(path1, builder)
    assert store1 is store2, "Instances with same path and builder should be the same"

    store3 = StateStore(path2, builder)
    assert store1 is not store3, "Instances with different paths should be different"

    another_builder = AnotherStateBuilder()
    store4 = StateStore(path1, another_builder)
    assert store1 is store4, "Instances with different builders should be the same"
    assert store4.state == store1.state, "State prioritizes the loaded state"

    store5 = StateStore(path5, another_builder)
    assert store5.state == {"different": "data"}, "State should match the new builder's output"


def test_lazy_loading(temp_test_dir):
    """Tests the lazy loading functionality."""
    builder = TestStateBuilder()
    lazy_path = temp_test_dir / "lazy_test.pkl"
    lazy_store = StateStore(lazy_path, builder, lazy_load=True)
    # Access internal attribute for testing purposes ONLY
    assert lazy_store._state is None, "State should be None before first access with lazy load"
    # Trigger loading by accessing the state property
    state = lazy_store.state
    assert state is not None, "State should be loaded after first access"
    assert state == builder.build()[0], "Loaded state should match builder output"
    assert lazy_store.is_loaded() is True


def test_state_persistence(temp_test_dir):
    """Tests that state is correctly saved and reloaded."""
    builder = TestStateBuilder()
    file_path = temp_test_dir / "persist_test.pkl"
    store1 = StateStore(file_path, builder)

    # Ensure initial state exists and modify it
    initial_state = store1.state
    assert initial_state["counter"] == 0
    initial_state["counter"] += 1
    store1.save()

    # Create a new instance pointing to the same file
    # Reset internal cache to force reload for testing purposes
    instance_key = (StateStore, builder, False, file_path)  # Use class in key
    if instance_key in StateStore._instances:
        del StateStore._instances[instance_key]

    store2 = StateStore(file_path, builder)
    reloaded_state = store2.state
    assert reloaded_state["counter"] == 1, "Counter should be incremented after reload"


def test_force_rebuild(temp_test_dir):
    """Tests the force_rebuild functionality."""
    builder = TestStateBuilder()
    file_path = temp_test_dir / "rebuild_test.pkl"
    store = StateStore(file_path, builder)

    # Modify the state and save
    store.state["counter"] += 5
    store.save()

    # Force rebuild
    store.force_rebuild()
    rebuilt_state = store.state

    # Check if the state is reset to the builder's default
    assert rebuilt_state["counter"] == 0, "Counter should be reset after force_rebuild"

    # Verify the file also reflects the rebuilt state
    # Reset internal cache to force reload from file
    instance_key = (StateStore, builder, False, file_path)  # Use class in key
    if instance_key in StateStore._instances:
        del StateStore._instances[instance_key]

    store_reloaded = StateStore(file_path, builder)
    reloaded_state_after_rebuild = store_reloaded.state
    assert reloaded_state_after_rebuild["counter"] == 0, "Counter in file should be reset after force_rebuild and reload"


def test_exists_method(temp_test_dir):
    """Tests the exists() method."""
    builder = TestStateBuilder()
    file_path = temp_test_dir / "exists_test.pkl"
    store = StateStore(file_path, builder, lazy_load=True)

    assert not store.exists(), "File should not exist before save/load"
    store.save()  # save should create the file
    assert store.exists(), "File should exist after save"


def test_is_loaded_method(temp_test_dir):
    """Tests the is_loaded() method."""
    builder = TestStateBuilder()
    file_path_lazy = temp_test_dir / "is_loaded_test.pkl"
    file_path_non_lazy = temp_test_dir / "is_loaded_test_non_lazy.pkl"

    # Test with lazy loading
    store_lazy = StateStore(file_path_lazy, builder, lazy_load=True)
    assert not store_lazy.is_loaded(), "State should not be loaded initially with lazy load"
    _ = store_lazy.state  # Access state to trigger load
    assert store_lazy.is_loaded(), "State should be loaded after access with lazy load"

    # Test without lazy loading
    store_non_lazy = StateStore(file_path_non_lazy, builder, lazy_load=False)
    assert store_non_lazy.is_loaded(), "State should be loaded immediately without lazy load"


def test_builder_called_on_load_if_not_exists(temp_test_dir):
    """Tests that the builder is called during load if the file doesn't exist."""
    builder = TestStateBuilder()
    file_path = temp_test_dir / "builder_on_load_test.pkl"
    store = StateStore(file_path, builder, lazy_load=True)

    assert not Path(file_path).exists()
    assert not store.is_loaded()

    # Accessing state should trigger load, which finds no file, calls builder, saves
    state = store.state
    assert state == builder.build()[0]  # Compare with builder output
    assert store.is_loaded()
    assert Path(file_path).exists()  # File should now exist


def test_builder_called_on_save_if_state_is_none(temp_test_dir):
    """Tests that the builder is called during save if _state is None."""
    builder = TestStateBuilder()
    file_path = temp_test_dir / "builder_on_save_test.pkl"
    store = StateStore(file_path, builder, lazy_load=True)

    assert not store.is_loaded()
    store.save()  # Should call builder since _state is None
    assert store.is_loaded()
    assert Path(file_path).exists()
    assert store.state == builder.build()[0]  # Compare with builder output
