import pytest

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_endpoint import (
    RPAWorkflowAssistantRequest,
    WorkflowAssistantChangeResponse,
    WorkflowAssistantEndpoint,
    WorkflowAssistantRouterResponse,
)
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter


# Define a fixture for setup
@pytest.fixture
def test_setup(mocker):
    """Sets up the test environment for each test function using pytest-mock."""
    endpoint = WorkflowAssistantEndpoint()

    # Use mocker to create mocks
    message_emitter = mocker.MagicMock(spec=MessageEmitter)
    message_emitter.emit_message = mocker.AsyncMock()
    message_emitter.emit_debug_message = mocker.AsyncMock()

    # Common test data
    request = mocker.MagicMock(spec=RPAWorkflowAssistantRequest)
    router_response = mocker.MagicMock(spec=WorkflowAssistantRouterResponse)

    # Setup default return value for handlers
    mock_response = WorkflowAssistantChangeResponse(
        scenario="test",
        newWorkflowContent="new_content",
        previousWorkflowContent="prev_content",
        message="Test message",
    )

    # Use mocker.patch.object to mock methods on the instance
    # Provide return_value and specify new_callable=AsyncMock for async methods
    mocker.patch.object(endpoint, "handle_edit_workflow", return_value=mock_response, new_callable=mocker.AsyncMock, name="handle_edit_workflow")
    mocker.patch.object(endpoint, "handle_rewrite_workflow", return_value=mock_response, new_callable=mocker.AsyncMock, name="handle_rewrite_workflow")
    mocker.patch.object(endpoint, "handle_fix_workflow", return_value=mock_response, new_callable=mocker.AsyncMock, name="handle_fix_workflow")
    mocker.patch.object(endpoint, "handle_analyze_workflow", return_value=mock_response, new_callable=mocker.AsyncMock, name="handle_analyze_workflow")
    mocker.patch.object(endpoint, "handle_generate_workflow", return_value=mock_response, new_callable=mocker.AsyncMock, name="handle_generate_workflow")

    # Return a dictionary containing all setup items
    return {"endpoint": endpoint, "message_emitter": message_emitter, "request": request, "router_response": router_response, "mocker": mocker}


# Parametrize the tests for known scenarios
@pytest.mark.parametrize(
    "scenario, handler_name, pass_request_arg",
    [
        ("edit-workflow", "handle_edit_workflow", True),
        ("rewrite-workflow", "handle_rewrite_workflow", True),
        ("fix-workflow", "handle_fix_workflow", True),
        ("analyze-workflow", "handle_analyze_workflow", False),  # Does not expect request
        ("generate-workflow", "handle_generate_workflow", True),
    ],
)
@pytest.mark.asyncio
async def test_handle_known_scenarios(test_setup, scenario, handler_name, pass_request_arg):
    """Tests that the correct handler is called for each known scenario."""
    # Arrange
    endpoint = test_setup["endpoint"]
    router_response = test_setup["router_response"]
    request = test_setup["request"]
    message_emitter = test_setup["message_emitter"]

    # Get the *mocked* handler method from the endpoint instance
    handler_mock = getattr(endpoint, handler_name)

    # Determine expected arguments for the handler call
    expected_args = [router_response]
    if pass_request_arg:
        expected_args.append(request)
    expected_args.append(message_emitter)

    # Act
    result = await endpoint.handle_assistant_scenario(scenario, router_response, request, message_emitter)

    # Assert
    handler_mock.assert_called_once_with(*expected_args)
    assert result == handler_mock.return_value

    # Assert that other handlers were not called (optional but good practice)
    all_handlers = [
        endpoint.handle_edit_workflow,
        endpoint.handle_rewrite_workflow,
        endpoint.handle_fix_workflow,
        endpoint.handle_analyze_workflow,
        endpoint.handle_generate_workflow,
    ]
    for other_handler in all_handlers:
        if other_handler != handler_mock:
            other_handler.assert_not_called()


@pytest.mark.asyncio
async def test_handle_unknown_scenario(test_setup):
    # Arrange
    scenario = "unknown-scenario"
    endpoint = test_setup["endpoint"]
    router_response = test_setup["router_response"]
    request = test_setup["request"]
    message_emitter = test_setup["message_emitter"]

    # Act
    result = await endpoint.handle_assistant_scenario(scenario, router_response, request, message_emitter)

    # Assert
    endpoint.handle_edit_workflow.assert_not_called()
    endpoint.handle_rewrite_workflow.assert_not_called()
    endpoint.handle_fix_workflow.assert_not_called()
    endpoint.handle_analyze_workflow.assert_not_called()
    endpoint.handle_generate_workflow.assert_not_called()

    assert result.scenario == "error"
    assert result.newWorkflowContent is None
    assert result.previousWorkflowContent is None
