from unittest.mock import MagicMock, patch

import pytest

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_router_task import WorkflowRouterTask
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    RPAWorkflowAssistantRequest,
    WorkflowAssistantContext,
)


@pytest.fixture
def router_task():
    return WorkflowRouterTask()


@pytest.fixture
def request_mock():
    mock = MagicMock(spec=RPAWorkflowAssistantRequest)
    mock.userRequest = MagicMock()
    mock.projectDefinition = MagicMock()
    mock.attachments = MagicMock()
    return mock


@pytest.fixture
def router_context():
    mock = MagicMock(spec=WorkflowAssistantContext)
    mock.current_time = MagicMock()
    mock.assets = MagicMock()
    mock.queues = MagicMock()
    mock.processes = MagicMock()
    return mock


@pytest.fixture
def model_mock():
    return MagicMock()


@pytest.mark.asyncio
async def test_build_prompt_content_calls_all_build_methods_once(router_task, request_mock, router_context, model_mock):
    # Arrange
    user_localization = "en-US"

    with (
        patch.object(router_task, "_build_message_history_prompt") as mock_build_message_history,
        patch.object(router_task, "_build_available_entities_prompt") as mock_build_available_entities,
        patch.object(router_task, "_build_current_workflow_prompt") as mock_build_current_workflow,
        patch.object(router_task, "_build_additional_instructions_prompt") as mock_build_additional_instructions,
    ):
        # Mock return values to avoid None issues
        mock_build_message_history.return_value = "message_history"
        mock_build_available_entities.return_value = "available_entities"
        mock_build_current_workflow.return_value = "current_workflow"
        mock_build_additional_instructions.return_value = "additional_instructions"

        # Act
        await router_task._build_prompt_content(request_mock, router_context, user_localization, model_mock)

        # Assert
        mock_build_message_history.assert_called_once_with(router_context)
        mock_build_available_entities.assert_called_once_with(router_context)
        mock_build_current_workflow.assert_called_once_with(router_context)
        mock_build_additional_instructions.assert_called_once_with(request_mock)
