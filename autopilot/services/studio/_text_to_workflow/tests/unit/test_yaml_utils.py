import pytest

from services.studio._text_to_workflow.utils.yaml_utils import (
    enforce_single_quotes_on_yaml_values,
    multiline_str,
    yaml_load,
)

_test_cases = [
    # Simple values
    (
        """
key1: value1
key2: '123
key3: 456'
key4: 789
""",
        """
key1: 'value1'
key2: '123'
key3: '456'
key4: '789'
""",
    ),
    # Already quoted values
    (
        """
key1: 'value1'
key2: "value2"
""",
        """
key1: 'value1'
key2: "value2"
""",
    ),
    # Values with quotes
    (
        """
key1: value's
key2: value"s
key3: value's"s
""",
        """
key1: 'value''s'
key2: 'value"s'
key3: 'value''s"s'
""",
    ),
    # Multiline values
    (
        """
key1: |
  This is a multiline value
  It spans multiple lines
  And should not be escaped
key2: 'single quoted value'
""",
        """
key1: |
  This is a multiline value
  It spans multiple lines
  And should not be escaped
key2: 'single quoted value'
""",
    ),
    # Nested structure
    # using |
    (
        """
parent:
  child1: value1
  child2: |
    multiline value
    that spans lines
  child3: value3
sibling: value4
""",
        """
parent:
  child1: 'value1'
  child2: |
    multiline value
    that spans lines
  child3: 'value3'
sibling: 'value4'
""",
    ),
    # using |-
    (
        """
parent:
  child1: value1
  child2: |-
    multiline value
    that spans lines
  child3: value3
sibling: value4
""",
        """
parent:
  child1: 'value1'
  child2: |-
    multiline value
    that spans lines
  child3: 'value3'
sibling: 'value4'
""",
    ),
    # Check that problematic single quotes are escaped, even if the value is already quoted
    (
        """
key1: 'It's a test'
""",
        """
key1: 'It''s a test'
""",
    ),
    (
        """
key1: 'It\'s a test'
""",
        """
key1: 'It''s a test'
""",
    ),
    (
        """
key1: 'It''s a test'
""",
        """
key1: 'It''s a test'
""",
    ),
]


@pytest.mark.parametrize("yaml_to_process, expected_yaml_value", _test_cases)
def test_enforce_single_quotes_on_yaml_values(yaml_to_process: str, expected_yaml_value: str):
    """
    Test that the enforce_single_quotes_on_yaml_values function correctly adds single quotes to YAML values.
    """
    # Process the YAML string
    result = enforce_single_quotes_on_yaml_values(yaml_to_process, allow_double_quoted_values=True)

    # Check the result matches the expected YAML
    assert result.strip("\n") == expected_yaml_value.strip("\n")


_test_cases_merge_lists = [
    (
        """\
a:
- 1
- 2
a:
- 3
- 4
b: test
c:
- 5
d:
- 6
d:
- 7
- 8
""",
        {
            "regular": {"a": [3, 4], "b": "test", "c": [5], "d": [7, 8]},
            "merged": {"a": [1, 2, 3, 4], "b": "test", "c": [5], "d": [6, 7, 8]},
        },
    ),
    (
        """\
a:
- 1
- 2
a:
- 3
- 4
b: test
c:
- 5
d:
- |
  line1
  line2
d:
- 7
- 8
""",
        {
            "regular": {"a": [3, 4], "b": "test", "c": [5], "d": [7, 8]},
            "merged": {"a": [1, 2, 3, 4], "b": "test", "c": [5], "d": ["line1\nline2\n", 7, 8]},
            "multiline_key_path": ["d", 0],
        },
    ),
]


@pytest.mark.parametrize("yaml_content,expected", _test_cases_merge_lists)
def test_yaml_load_merge_lists(yaml_content: str, expected: dict):
    """Test that yaml_load correctly handles merging lists and multiline values."""
    # Test regular YAML loader overwrites duplicate keys
    regular_load = yaml_load(yaml_content)
    assert regular_load == expected["regular"]

    # Test custom loader merges lists
    merged_load = yaml_load(yaml_content, merge_conflicting_keys=True)
    assert merged_load == expected["merged"]

    # Test multiline if specified
    if (multiline_key_path := expected.get("multiline_key_path")) is not None:
        merged_multiline_load = yaml_load(yaml_content, merge_conflicting_keys=True, load_multiline=True)

        multiline_element = merged_multiline_load
        for key in multiline_key_path:
            multiline_element = multiline_element[key]

        assert isinstance(multiline_element, multiline_str)
        assert merged_multiline_load == expected["merged"]
