import pytest

from services.studio._text_to_workflow.common import typedefs_parser
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

_typedefs_parser_fixtures_path = conftest._fixtures_path / "typedefs_parser_fixtures"
_parse_typedef_cases_path = _typedefs_parser_fixtures_path / "parse_typedef_cases.yaml"
_parse_typedefs_cases_path = _typedefs_parser_fixtures_path / "parse_typedefs_cases.yaml"
_parse_namespace_cases_path = _typedefs_parser_fixtures_path / "parse_namespaces_cases.yaml"
_parse_typedef_cases = (
    "empty typedef",
    "class typedef with required arguments",
    "class type definition with description",
    "class type definition with unconstrained type parameter",
    "class type definition with constrained type parameter",
    "enum type definition",
    "interface type definition",
    "interface type definition with attributes",
    "record type definition",
    "struct type definition",
    "record struct type definition",
    "configure activity request",
)
_parse_typedefs_cases = (
    "empty typedefs",
    "embeddings.db typedefs",
    "configure activity request",
    "typedefs with accessor overload",
)
_parse_namespaces_cases = (
    "empty string",
    "empty namespace",
    "namespace with one class",
    "namespace with multiple classes",
    "multiple namespaces with multiple classes",
    "generate expression request",
    "fix expression request",
    "generate sequence request",
)


@pytest.fixture(scope="module")
def parse_typedef_cases() -> dict:
    return yaml_load(_parse_typedef_cases_path)


@pytest.fixture(scope="module")
def parse_typedefs_cases() -> dict:
    return yaml_load(_parse_typedefs_cases_path)


@pytest.fixture(scope="module")
def parse_namespace_cases() -> dict:
    return yaml_load(_parse_namespace_cases_path)


@pytest.mark.parametrize("case", _parse_typedef_cases)
def test_parse_typedef(case: str, parse_typedef_cases: dict):
    typedef, expected = parse_typedef_cases[case]["input"], parse_typedef_cases[case]["expected"]
    result = typedefs_parser.parse_typedef(typedef)
    assert result == expected


@pytest.mark.parametrize("case", _parse_typedefs_cases)
def test_parse_typedefs(case: str, parse_typedefs_cases: dict):
    typedefs, expected = parse_typedefs_cases[case]["input"], parse_typedefs_cases[case]["expected"]
    result = typedefs_parser.parse_typedefs(typedefs)
    assert result == expected


@pytest.mark.parametrize("case", _parse_namespaces_cases)
def test_parse_namespaces(case: str, parse_namespace_cases: dict):
    namespaces, expected = parse_namespace_cases[case]["input"], parse_namespace_cases[case]["expected"]
    result = typedefs_parser.parse_namespaces(namespaces)
    assert result == expected


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "param_name": "myParam",
            "param_type": "string",
            "expected": {
                "name": "myParam",
                "type": "string",
                "modifier": "public",
                "description": "",
                "required": True,
                "category": "Property",
                "components": [],
            },
        },
        {
            "param_name": "myParam?",
            "param_type": "string",
            "expected": {
                "name": "myParam?",
                "type": "string",
                "modifier": "public",
                "description": "",
                "required": False,
                "category": "Property",
                "components": [],
            },
        },
        {
            "param_name": "const myConstParam",
            "param_type": "string",
            "expected": {
                "name": "myConstParam",
                "type": "const string",
                "modifier": "public",
                "description": "",
                "required": True,
                "category": "Property",
                "components": [],
            },
        },
    ],
)
def test_parse_param_api_workflows(test_case):
    param_name = test_case["param_name"]
    param_type = test_case["param_type"]
    namespace = test_case.get("namespace", "")
    expected = test_case["expected"]

    result = typedefs_parser.parse_param_api_workflows(param_name, param_type, namespace)
    assert result == expected


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "typedef_str": "function simpleFunc(param1: string, param2: number): boolean",
            "description": "This is a simple function",
            "expected": {
                "name": "simpleFunc",
                "type": "function",
                "description": "This is a simple function",
                "type_params": {"returnType": "boolean"},
                "params": {
                    "param1": {
                        "name": "param1",
                        "type": "string",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                    "param2": {
                        "name": "param2",
                        "type": "number",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                },
                "text": "function simpleFunc(param1: string, param2: number): boolean",
            },
        },
        {
            "typedef_str": "function genericFunc<T=string>(data: T): T",
            "description": "A generic function example",
            "expected": {
                "name": "genericFunc",
                "type": "function",
                "description": "A generic function example",
                "type_params": {"returnType": "T"},
                "params": {
                    "T": {"name": "T", "type": "string", "modifier": "public", "description": "", "required": True, "category": "Property", "components": []},
                    "data": {"name": "data", "type": "T", "modifier": "public", "description": "", "required": True, "category": "Property", "components": []},
                },
                "text": "function genericFunc<T=string>(data: T): T",
            },
        },
        {
            "typedef_str": "declare function declaredFunc(x: number): void",
            "description": "A declared function",
            "expected": {
                "name": "declaredFunc",
                "type": "function",
                "description": "A declared function",
                "type_params": {"returnType": "void"},
                "params": {
                    "x": {"name": "x", "type": "number", "modifier": "public", "description": "", "required": True, "category": "Property", "components": []}
                },
                "text": "declare function declaredFunc(x: number): void",
            },
        },
    ],
)
def test_parse_function_typedef(test_case):
    typedef_str = test_case["typedef_str"]
    description = test_case["description"]
    expected = test_case["expected"]

    result = typedefs_parser.parse_function_typedef(typedef_str, description)
    assert result == expected


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "typedef_str": "enum Color { Red, Green, Blue }",
            "expected": {
                "enumerator": {
                    "name": "values",
                    "type": "string",
                    "modifier": "private",
                    "category": "Property",
                    "components": ["string"],
                    "description": "",
                    "required": True,
                    "values": ["Red", "Green", "Blue"],
                },
                "text": "enum Color { Red, Green, Blue }",
            },
        },
        {
            "typedef_str": """enum Status {
            Active,
            Inactive,
            Pending
        }""",
            "expected": {
                "enumerator": {
                    "name": "values",
                    "type": "string",
                    "modifier": "private",
                    "category": "Property",
                    "components": ["string"],
                    "description": "",
                    "required": True,
                    "values": ["Active", "Inactive", "Pending"],
                },
                "text": """enum Status {
            Active,
            Inactive,
            Pending
        }""",
            },
        },
        {
            "typedef_str": "declare enum Direction { Up, Down, Left, Right }",
            "expected": {
                "enumerator": {
                    "name": "values",
                    "type": "string",
                    "modifier": "private",
                    "category": "Property",
                    "components": ["string"],
                    "description": "",
                    "required": True,
                    "values": ["Up", "Down", "Left", "Right"],
                },
                "text": "declare enum Direction { Up, Down, Left, Right }",
            },
        },
    ],
)
def test_parse_enum_typedef_api_workflows(test_case):
    typedef_str = test_case["typedef_str"]
    expected = test_case["expected"]

    result = typedefs_parser.parse_enum_typedef_api_workflows(typedef_str)
    assert result == expected


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "typedef_str": "type UserId = string;",
            "description": "Represents a user identifier",
            "expected": {
                "name": "UserId",
                "type": "type",
                "description": "Represents a user identifier",
                "type_params": {},
                "params": {"string": "string"},
                "text": "type UserId = string;",
            },
        },
        {
            "typedef_str": "type Status = 'active' | 'inactive' | 'pending';",
            "description": "Possible status values",
            "expected": {
                "name": "Status",
                "type": "type",
                "description": "Possible status values",
                "type_params": {},
                "params": {"'active'": "'active'", "'inactive'": "'inactive'", "'pending'": "'pending'"},
                "text": "type Status = 'active' | 'inactive' | 'pending';",
            },
        },
        {
            "typedef_str": "declare type Result = number | string;",
            "description": "Function result type",
            "expected": {
                "name": "Result",
                "type": "type",
                "description": "Function result type",
                "type_params": {},
                "params": {"number": "number", "string": "string"},
                "text": "declare type Result = number | string;",
            },
        },
    ],
)
def test_parse_type_typedef(test_case):
    typedef_str = test_case["typedef_str"]
    description = test_case["description"]
    expected = test_case["expected"]

    result = typedefs_parser.parse_type_typedef(typedef_str, description)
    assert result == expected


@pytest.mark.parametrize(
    "test_case",
    [
        {
            "typedef_str": "\n        namespace SimpleSpace {\n            x: number;\n            y: string;\n        }",
            "description": "A simple namespace",
            "expected": {
                "name": "SimpleSpace",
                "type": "namespace",
                "description": "A simple namespace",
                "type_params": {},
                "params": {
                    "x": {"name": "x", "type": "number", "modifier": "public", "description": "", "required": True, "category": "Property", "components": []},
                    "y": {"name": "y", "type": "string", "modifier": "public", "description": "", "required": True, "category": "Property", "components": []},
                },
                "text": "\n        namespace SimpleSpace {\n            x: number;\n            y: string;\n        }",
            },
        },
        {
            "typedef_str": "\n        namespace Outer {\n            namespace Inner {\n                value: boolean;\n            }\n            count: number;\n        }",
            "description": "Nested namespace example",
            "expected": {
                "name": "Outer",
                "type": "namespace",
                "description": "Nested namespace example",
                "type_params": {},
                "params": {
                    "value": {
                        "name": "Inner.value",
                        "type": "boolean",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                    "count": {
                        "name": "count",
                        "type": "number",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                },
                "text": "\n        namespace Outer {\n            namespace Inner {\n                value: boolean;\n            }\n            count: number;\n        }",
            },
        },
        {
            "typedef_str": "\n        namespace API {\n            class User {\n                id: string;\n            }\n            interface Config {\n                setting: boolean;\n            }\n            version: string;\n        }",
            "description": "API namespace",
            "expected": {
                "name": "API",
                "type": "namespace",
                "description": "API namespace",
                "type_params": {},
                "params": {
                    "id": {
                        "name": "User.id",
                        "type": "string",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                    "setting": {
                        "name": "Config.setting",
                        "type": "boolean",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                    "version": {
                        "name": "version",
                        "type": "string",
                        "modifier": "public",
                        "description": "",
                        "required": True,
                        "category": "Property",
                        "components": [],
                    },
                },
                "text": "\n        namespace API {\n            class User {\n                id: string;\n            }\n            interface Config {\n                setting: boolean;\n            }\n            version: string;\n        }",
            },
        },
    ],
)
def test_parse_namespace_typedef_api_workflows(test_case):
    typedef_str = test_case["typedef_str"]
    description = test_case["description"]
    expected = test_case["expected"]

    result = typedefs_parser.parse_namespace_typedef_api_workflows(typedef_str, description)
    assert result == expected
