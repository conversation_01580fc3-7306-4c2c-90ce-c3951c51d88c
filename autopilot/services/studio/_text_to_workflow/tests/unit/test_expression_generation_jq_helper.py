import pytest

from services.studio._text_to_workflow.expression_generation.expression_generation_jq_helper import JqExpressionsHelper


@pytest.mark.parametrize(
    "expression, available_variables, expected",
    [
        ("", [], ""),
        (".foo | .bar", [], ".foo | .bar"),
        ("null as $var | $var", [{"name": "var"}], "null as $var | $var"),
        ("$foo | $bar", [{"name": "foo"}, {"name": "bar"}], "null as $bar|null as $foo | $foo | $bar"),
        ("null as $foo | $foo | $bar", [{"name": "foo"}, {"name": "bar"}], "null as $bar | null as $foo | $foo | $bar"),
        ("$unknown", [{"name": "known"}], "$unknown"),
        (
            "null as $var1 | .items[] | null as $var2 | $var1 + $var2 + $var3",
            [{"name": "var1"}, {"name": "var2"}, {"name": "var3"}],
            "null as $var3 | null as $var1 | .items[] | null as $var2 | $var1 + $var2 + $var3",
        ),
        (
            "$camelCase | $snake_case | $PascalCase | $with123Numbers",
            [{"name": "camelCase"}, {"name": "snake_case"}, {"name": "PascalCase"}, {"name": "with123Numbers"}],
            "null as $PascalCase|null as $camelCase|null as $snake_case|null as $with123Numbers | $camelCase | $snake_case | $PascalCase | $with123Numbers",
        ),
        ("$var + $var + $var", [{"name": "var"}], "null as $var | $var + $var + $var"),
        (
            "null as $outer | .items[] | null as $inner | $outer + $inner + $undeclared",
            [{"name": "outer"}, {"name": "inner"}, {"name": "undeclared"}],
            "null as $undeclared | null as $outer | .items[] | null as $inner | $outer + $inner + $undeclared",
        ),
    ],
    ids=[
        "empty_expression",
        "expression_with_no_variables",
        "expression_with_declared_variables",
        "expression_with_variables_to_mock",
        "expression_with_some_variables_to_mock",
        "expression_with_unavailable_variables",
        "complex_expression_with_multiple_declarations",
        "expression_with_different_variable_formats",
        "expression_with_repeated_variables",
        "expression_with_nested_variable_declarations",
    ],
)
def test_jq_mock_variables(expression, available_variables, expected):
    """Test JqExpressionsHelper.jq_mock_variables with various expressions and variable configurations"""
    result = JqExpressionsHelper.jq_mock_variables(expression, available_variables)
    assert result == expected
