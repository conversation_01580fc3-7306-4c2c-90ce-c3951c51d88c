from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema
from services.studio._text_to_workflow.workflow_generation.workflow_generation_uia_integration import expand_uia_scopes

CLICK_ACTION_MOCK = {"method_type": "click", "description": "Click on element", "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q"}
TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_STR_MOCK = {
    "method_type": "type_into",
    "description": "Type into element",
    "default_value": "'what's your name?'",
    "variable": "$var1",
    "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q",
}
# When the prompt is a string with quotes, the model will not quote the default value
TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_UNQUOTED_STR_MOCK = {
    "method_type": "type_into",
    "description": "Type into element",
    "default_value": "hello world",
    "variable": "$var1",
    "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q",
}
TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_INT_MOCK = {
    "method_type": "type_into",
    "description": "Type into element",
    "default_value": "123",
    "variable": "$var1",
    "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q",
}
TYPE_ACTION_WITH_PROMPTED_VARIABLE_MOCK = {
    "method_type": "type_into",
    "description": "Type into element",
    "variable": "$var1",
    "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q",
}
TYPE_ACTION_WITH_UNCLEAR_PROMPT_MOCK = {
    "method_type": "type_into",
    "description": "Type into element",
    "variable": "$var1",
    "default_value": "'sample value'",
    "box_reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q",
}

DEFAULT_VARIABLES = [
    workflow_generation_schema.Variable(name="var1", type="string"),
    workflow_generation_schema.Variable(name="var2", type="int"),
]

NO_VARIABLES = []


@pytest.mark.asyncio
async def test_expand_uia_scopes():
    uia_scopes = _build_test_uia_scopes_object()

    await _test_uia_scopes_expansion(uia_scopes, "test prompt", [CLICK_ACTION_MOCK])

    assert len(uia_scopes[0]["parent"]) == 1
    assert uia_scopes[0]["parent"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NApplicationCard"
    assert uia_scopes[0]["parent"][0]["params"]["Body"]["Handler"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NClick"


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_default_value_str_in_prompt():
    uia_scopes = _build_test_uia_scopes_object(expected_inputs=["'what's your name?'"])

    await _verify_type_into_parameters(
        uia_scopes, "type what's your name? in the text box", [TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_STR_MOCK], '[["what\'s your name?"]]'
    )


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_default_value_int_in_prompt():
    uia_scopes = _build_test_uia_scopes_object()

    await _verify_type_into_parameters(uia_scopes, "type 123 in the text box", [TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_INT_MOCK], '[["123"]]')


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_default_value_quoted_string_in_prompt():
    uia_scopes = _build_test_uia_scopes_object()

    await _verify_type_into_parameters(
        uia_scopes, 'type "hello world" in the text box', [TYPE_ACTION_WITH_PROMPTED_DEFAULT_VALUE_UNQUOTED_STR_MOCK], '[["hello world"]]'
    )


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_variable_in_prompt():
    uia_scopes = _build_test_uia_scopes_object()

    await _verify_type_into_parameters(uia_scopes, "type var1 in the text box", [TYPE_ACTION_WITH_PROMPTED_VARIABLE_MOCK], "[[var1]]")


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_unclear_prompt_expecting_variable_testcase():
    uia_scopes = _build_test_uia_scopes_object(expected_inputs=["'var1'"])

    await _test_uia_scopes_expansion(uia_scopes, "type a variable in the text box", [TYPE_ACTION_WITH_UNCLEAR_PROMPT_MOCK], "testcase", DEFAULT_VARIABLES)

    assert len(uia_scopes[0]["parent"]) == 1

    assert uia_scopes[0]["parent"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NApplicationCard"

    application_card_sequence = uia_scopes[0]["parent"][0]["params"]["Body"]["Handler"]
    assert len(application_card_sequence) == 1
    assert application_card_sequence[0]["activity"] == "UiPath.UIAutomationNext.Activities.NTypeInto"
    assert application_card_sequence[0]["params"]["Text"] == "[[var1]]"


@pytest.mark.asyncio
async def test_expand_uia_scopes_with_unclear_prompt_expecting_default_value_sequence():
    # For sequence mode, if a default is provided or suggested as input but that default didn't really exist in the prompt,
    # we prefer to just leave the field empty
    uia_scopes = _build_test_uia_scopes_object(expected_inputs=["'sample value'"])

    await _verify_type_into_parameters(uia_scopes, "type a variable in the text box", [TYPE_ACTION_WITH_UNCLEAR_PROMPT_MOCK], "[[var1]]")


@pytest.mark.asyncio
async def test_expand_uia_scopes_in_activity_delegate():
    uia_scopes = _build_test_uia_scopes_object_in_activity_delegate()

    await _test_uia_scopes_expansion(uia_scopes, "test prompt", [CLICK_ACTION_MOCK])

    assert len(uia_scopes[0]["parent"]) == 1
    assert uia_scopes[0]["parent"]["Body"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NApplicationCard"
    assert uia_scopes[0]["parent"]["Body"][0]["params"]["Body"]["Handler"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NClick"


async def _verify_type_into_parameters(
    uia_scopes: list[dict], prompt: str, expected_action: str, expected_text: str, mode: ActivitiesGenerationMode = "sequence"
):
    await _test_uia_scopes_expansion(uia_scopes, prompt, expected_action, mode)

    assert len(uia_scopes[0]["parent"]) == 1
    assert uia_scopes[0]["parent"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NApplicationCard"
    assert uia_scopes[0]["parent"][0]["params"]["Body"]["Handler"][0]["activity"] == "UiPath.UIAutomationNext.Activities.NTypeInto"
    assert uia_scopes[0]["parent"][0]["params"]["Body"]["Handler"][0]["params"]["Text"] == expected_text


async def _test_uia_scopes_expansion(
    uia_scopes: list[dict],
    prompt: str,
    expected_actions: list[str],
    mode: ActivitiesGenerationMode = "testcase",
    variables: list[workflow_generation_schema.Variable] = DEFAULT_VARIABLES,
):
    objects: list[workflow_generation_schema.UIObject] = [
        {
            "type": "App",
            "reference": "TBA11fJKq0uQqpMFpNqs8w/OJYXYFtUPUC93oeokr5Iww",
            "children": [
                {
                    "type": "Screen",
                    "reference": "TBA11fJKq0uQqpMFpNqs8w/PdqUcYVZCk-Uay7_ruEblQ",
                    "children": [{"type": "Element", "reference": "TBA11fJKq0uQqpMFpNqs8w/WXZXfPCtPUODB_ZJ_upH2Q"}],
                }
            ],
        },
    ]

    with patch("services.studio._text_to_workflow.workflow_generation.workflow_generation_uia_integration.UiAutomationTask.predict") as mock_predict:
        mock_predict.return_value = (
            {"step": {"actions": expected_actions}},
            None,
        )

        await expand_uia_scopes(variables, objects, uia_scopes, prompt, mode == "testcase")

    return uia_scopes


def _build_test_uia_scopes_object(expected_inputs: list[str] = ["var1"], expected_outputs: list[str] = ["var2"]) -> list[dict]:
    uia_scopes = []

    uia_scope_node = {"params": {"AutoGenerationOptions": {"Prompt": "Test Prompt", "ExpectedInputs": expected_inputs, "ExpectedOutputs": expected_outputs}}}
    uia_scope = {
        "node": uia_scope_node,
        "parent": [uia_scope_node],
    }
    uia_scopes.append(uia_scope)
    return uia_scopes


def _build_test_uia_scopes_object_in_activity_delegate(expected_inputs: list[str] = ["var1"], expected_outputs: list[str] = ["var2"]) -> list[dict]:
    uia_scopes = []

    uia_scope_node = {"params": {"AutoGenerationOptions": {"Prompt": "Test Prompt", "ExpectedInputs": expected_inputs, "ExpectedOutputs": expected_outputs}}}
    uia_scope = {
        "node": uia_scope_node,
        "parent": {"Body": [uia_scope_node]},
    }
    uia_scopes.append(uia_scope)
    return uia_scopes
