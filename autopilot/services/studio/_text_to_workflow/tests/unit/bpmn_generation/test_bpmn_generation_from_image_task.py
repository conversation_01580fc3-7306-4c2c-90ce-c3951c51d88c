from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_image_task import (
    BpmnGenerationFromImageTask,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnGenerationToolResult,
    BpmnRequestContext,
    Tool,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage


@pytest.fixture
def mock_base_task():
    with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_image_task.BpmnBaseTask") as MockBaseTask:
        mock_task = MagicMock()
        MockBaseTask.return_value = mock_task
        MockBaseTask.read_content.return_value = "mock content"

        mock_task.common_config = {"default_bpmn_xml": "<bpmn>default</bpmn>"}
        mock_task.config = {
            "prompt": {"system_template": {"convert_image": "System template with {supported_element_examples} and {convert_image_bpmn_examples}"}}
        }

        mock_task._system_template.return_value = "System template with {supported_element_examples} and {convert_image_bpmn_examples}"
        mock_task._call_llm = AsyncMock(
            return_value=('```json\n{"explanation": "Test explanation", "add": []}\n```', TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30))
        )
        mock_task._validate_output = AsyncMock(
            return_value=BpmnGenerationChatTaskResult(explanation="Test explanation", usage=TokenUsage(), add=[], update=[], delete=[])
        )

        yield mock_task


@pytest.fixture
def image_task(mock_base_task):
    task = BpmnGenerationFromImageTask()
    task._get_image_mime_type = MagicMock(return_value=("image/png", None))
    yield task


@pytest.fixture
def sample_png_base64():
    # A minimal valid PNG image in base64
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="


@pytest.fixture
def sample_request_context(sample_png_base64):
    return BpmnRequestContext(
        user_request="Convert this BPMN diagram",
        current_bpmn="<bpmn>existing</bpmn>",
        image_data=sample_png_base64.encode("utf-8"),
        support_validation=True,
        chat_history={},
        request_context=None,
    )


@pytest.fixture
def empty_bpmn_request_context(sample_png_base64):
    return BpmnRequestContext(
        user_request="Convert this BPMN diagram",
        current_bpmn="",  # Empty BPMN
        image_data=sample_png_base64.encode("utf-8"),
        support_validation=True,
        chat_history={},
        request_context=None,
    )


@pytest.fixture
def mock_logger():
    with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_image_task.LOGGER") as mock:
        yield mock


class TestBpmnGenerationFromImageTask:
    def test_get_image_mime_type_valid(self):
        """Test mime type detection for valid images"""
        task = BpmnGenerationFromImageTask()

        # Test PNG
        mime_type, error = task._get_image_mime_type("data:image/png;base64,abc123")
        assert mime_type == "image/png"
        assert error is None

        # Test JPEG
        mime_type, error = task._get_image_mime_type("data:image/jpeg;base64,abc123")
        assert mime_type == "image/jpeg"
        assert error is None

        # Test GIF
        mime_type, error = task._get_image_mime_type("data:image/gif;base64,abc123")
        assert mime_type == "image/gif"
        assert error is None

        # Test WEBP
        mime_type, error = task._get_image_mime_type("data:image/webp;base64,abc123")
        assert mime_type == "image/webp"
        assert error is None

    def test_get_image_mime_type_invalid(self, mock_logger):
        """Test mime type detection for invalid images"""
        task = BpmnGenerationFromImageTask()

        # Test empty string
        mime_type, error = task._get_image_mime_type("")
        assert mime_type == ""
        assert "No image data provided" in (error or "")

        # Test unsupported format
        mime_type, error = task._get_image_mime_type("data:image/bmp;base64,abc123")
        assert mime_type == ""
        assert "Unsupported image format" in (error or "")
        assert mock_logger.warning.called

        # Test no mime type at all
        mime_type, error = task._get_image_mime_type("abc123")
        assert mime_type == ""
        assert "missing MIME type prefix" in (error or "")

    def test_get_image_mime_type_exception(self, mock_logger):
        """Test exception handling in mime type detection"""
        task = BpmnGenerationFromImageTask()

        # Mock re.match to raise an exception
        with patch("re.match", side_effect=Exception("Test exception")):
            mime_type, error = task._get_image_mime_type("data:image/png;base64,abc123")
            assert mime_type == ""
            assert "Unexpected error" in (error or "")
            assert "Test exception" in (error or "")
            assert mock_logger.error.called

    @pytest.mark.asyncio
    async def test_generate_with_invalid_image(self, image_task, sample_request_context, mock_logger):
        """Test generate method with invalid image format"""
        # Override the mocked _get_image_mime_type to return an error
        image_task._get_image_mime_type = MagicMock(return_value=("", "Unsupported image format"))

        # Call generate with the mocked error
        result: BpmnGenerationToolResult = await image_task.generate(sample_request_context)

        # Verify the result
        assert result["tool"] == Tool.CONVERT_IMAGE
        assert "Unsupported Image Format" in (result.get("title") or "")
        assert "not supported" in result["explanation"]
        assert len(result.get("add") or []) == 0
        assert len(result.get("update") or []) == 0
        assert len(result.get("delete") or []) == 0
        assert mock_logger.error.called

    @pytest.mark.asyncio
    async def test_generate_with_valid_image(self, image_task, sample_request_context, mock_logger):
        """Test generate method with valid image"""
        # Setup mocks
        image_task._generate = AsyncMock(
            return_value=('```json\n{"explanation": "Test explanation", "add": []}\n```', TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30))
        )

        # Call generate
        result: BpmnGenerationToolResult = await image_task.generate(sample_request_context)

        # Verify the result
        assert result["tool"] == Tool.CONVERT_IMAGE
        assert result["explanation"] == "Test explanation"

        # Verify _generate was called with the standard query and image data
        image_task._generate.assert_called_once_with(
            "Convert this image to a proper BPMN diagram following BPMN 2.0 standard. Make sure to fully include bpmnDiagram as well.",
            model_name="bpmn_generation_chat_model_anthropic",
            image_data=sample_request_context.image_data,
        )
        assert mock_logger.info.called

    @pytest.mark.asyncio
    async def test_generate_without_image_data(self, image_task):
        """Test generate method without image data"""
        # Create context without image data
        context = BpmnRequestContext(
            user_request="Convert this BPMN diagram",
            current_bpmn="<bpmn>existing</bpmn>",
            image_data=None,
            support_validation=True,
            chat_history={},
            request_context=None,
        )

        # Setup mocks
        image_task._generate = AsyncMock(
            return_value=('```json\n{"explanation": "Test explanation", "add": []}\n```', TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30))
        )

        # Call generate
        await image_task.generate(context)

        # Verify _generate was called without image data
        image_task._generate.assert_called_once_with(
            "Convert this image to a proper BPMN diagram following BPMN 2.0 standard. Make sure to fully include bpmnDiagram as well.",
            model_name="bpmn_generation_chat_model_anthropic",
            image_data=None,
        )
