# Test cases for BpmnValidator class
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import BpmnValidator


def test_create_error_message():
    validator = BpmnValidator()
    error_log = ["Error 1", "Error 2"]
    error_message = validator.create_error_message(error_log)
    assert error_message == "Error 1\nError 2"


def test_validate_json_valid():
    validator = BpmnValidator()
    bpmn_result = "<root><child id='1'>data</child></root>"
    json_string = '{"explanation": "test"}'
    result = validator.validate_json(bpmn_result, json_string)
    assert result["valid"]
    assert not result["error_message"]


def test_validate_json_missing_explanation():
    validator = BpmnValidator()
    bpmn_result = "<root><child id='1'>data</child></root>"
    json_string = '{"add": [{"id": "2", "type": "task"}]}'
    result = validator.validate_json(bpmn_result, json_string)
    assert not result["valid"]
    assert result["error_message"] == "Explanation is missing in JSON."


def test_validate_json_duplicate_id():
    validator = BpmnValidator()
    bpmn_result = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="1d463ae1">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="c30b9b9f-36f4-40a8-ad13-b9aa72bfd33a"/>
                                </bpmn:extensionElements>
                                </bpmn:startEvent>
                            </bpmn:process>
                            </bpmn:definitions>
                            """
    json_string = """{
                            "explanation": "I've added a new task 'Update Database' after 'Calculate Taxes' and adjusted the sequence flows accordingly.",
                            "title": "Add Database Task",
                            "add": [
                                {
                                    "type": "bpmn:task",
                                    "id": "Activity_UpdateDatabase",
                                    "parentId": "Process_1",
                                    "name": "Update Database",
                                    "data": {
                                        "label": "Update Database"
                                    }
                                },
                                {
                                    "type": "bpmn:sequenceFlow",
                                    "id": "edge_CalculateTaxes_UpdateDatabase",
                                    "source": "Activity_mF6lEh",
                                    "target": "Activity_UpdateDatabase",
                                    "parentId": "Process_1"
                                },
                                {
                                    "type": "bpmn:sequenceFlow",
                                    "id": "edge_CalculateTaxes_UpdateDatabase",
                                    "source": "Activity_UpdateDatabase",
                                    "target": "Activity_KW82cg",
                                    "parentId": "Process_1"
                                }
                            ],
                            "update": [],
                            "delete": [
                                {
                                    "type": "bpmn:sequenceFlow",
                                    "id": "edge_HTTKXy"
                                }
                            ]
                        }"""
    result = validator.validate_json(bpmn_result, json_string)
    assert not result["valid"]
    assert (
        result["error_message"] is not None
        and "Duplicated element id edge_CalculateTaxes_UpdateDatabase in JSON for type: bpmn:sequenceFlow." in result["error_message"]
    )


def test_validate_json_parent_exists():
    validator = BpmnValidator()
    bpmn_result = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="1d463ae1">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="c30b9b9f-36f4-40a8-ad13-b9aa72bfd33a"/>
                                </bpmn:extensionElements>
                                </bpmn:startEvent>
                            </bpmn:process>
                            </bpmn:definitions>
                            """
    json_string = """{
                        "explanation": "I've added a new task 'Update Database' after 'Calculate Taxes' and adjusted the sequence flows accordingly.",
                        "title": "Add Database Task",
                        "add": [
                            {
                                "type": "bpmn:task",
                                "id": "Activity_UpdateDatabase",
                                "parentId": "Process_1",
                                "name": "Update Database",
                                "data": {
                                    "label": "Update Database"
                                }
                            }
                        ],
                        "update": []
                    }"""
    result = validator.validate_json(bpmn_result, json_string)
    assert result["valid"]
    assert not result["error_message"]


def test_validate_json_parent_does_not_exist():
    validator = BpmnValidator()
    bpmn_result = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="1d463ae1">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="c30b9b9f-36f4-40a8-ad13-b9aa72bfd33a"/>
                                </bpmn:extensionElements>
                                </bpmn:startEvent>
                            </bpmn:process>
                            </bpmn:definitions>
                            """
    json_string = """{
                            "explanation": "I've added a new task 'Update Database' after 'Calculate Taxes' and adjusted the sequence flows accordingly.",
                            "title": "Add Database Task",
                            "add": [
                                {
                                    "type": "bpmn:task",
                                    "id": "Activity_UpdateDatabase",
                                    "parentId": "Process_2",
                                    "name": "Update Database",
                                    "data": {
                                        "label": "Update Database"
                                    }
                                }
                            ],
                            "update": [],
                            "delete": [
                                {
                                    "type": "bpmn:sequenceFlow",
                                    "id": "edge_HTTKXy"
                                }
                            ]
                        }"""
    result = validator.validate_json(bpmn_result, json_string)
    assert not result["valid"]


def test_validate_json_flow_source_and_target_exists():
    validator = BpmnValidator()
    bpmn_result = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.com/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="52a2e5da">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="b39d445a-2a71-488c-99d9-fc8d97b008e3"/>
                                </bpmn:extensionElements>
                                <bpmn:outgoing>edge_PSHwS0</bpmn:outgoing>
                                </bpmn:startEvent>
                                <bpmn:task id="Activity_uzumXt" name="Request Score">
                                <bpmn:incoming>edge_PSHwS0</bpmn:incoming>
                                <bpmn:outgoing>edge_hQT1wu</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:intermediateCatchEvent id="Event_HBS0P1">
                                <bpmn:incoming>edge_hQT1wu</bpmn:incoming>
                                <bpmn:outgoing>edge_XM4gdb</bpmn:outgoing>
                                <bpmn:outgoing>edge_fq5uoa</bpmn:outgoing>
                                <bpmn:outgoing>edge_UStqCi</bpmn:outgoing>
                                <bpmn:messageEventDefinition/>
                                </bpmn:intermediateCatchEvent>
                                <bpmn:task id="Activity_XAgqPn" name="Score Delayed Message">
                                <bpmn:incoming>edge_XM4gdb</bpmn:incoming>
                                <bpmn:outgoing>edge_XohuCd</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:task id="Activity_bkhbNg" name="Result">
                                <bpmn:incoming>edge_XohuCd</bpmn:incoming>
                                <bpmn:incoming>edge_fq5uoa</bpmn:incoming>
                                <bpmn:incoming>edge_wfHmS1</bpmn:incoming>
                                <bpmn:outgoing>edge_LzO4bD</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:endEvent id="Event_Kiwr0S">
                                <bpmn:incoming>edge_LzO4bD</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:task id="Activity_mF6lEh" name="Calculate Taxes">
                                <bpmn:incoming>edge_UStqCi</bpmn:incoming>
                                <bpmn:outgoing>edge_HTTKXy</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:task id="Activity_KW82cg" name="Pay Taxes">
                                <bpmn:incoming>edge_HTTKXy</bpmn:incoming>
                                <bpmn:outgoing>edge_wfHmS1</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:sequenceFlow id="edge_PSHwS0" sourceRef="Event_start" targetRef="Activity_uzumXt"/>
                                <bpmn:sequenceFlow id="edge_hQT1wu" sourceRef="Activity_uzumXt" targetRef="Event_HBS0P1"/>
                                <bpmn:sequenceFlow id="edge_XM4gdb" sourceRef="Event_HBS0P1" targetRef="Activity_XAgqPn"/>
                                <bpmn:sequenceFlow id="edge_XohuCd" sourceRef="Activity_XAgqPn" targetRef="Activity_bkhbNg"/>
                                <bpmn:sequenceFlow id="edge_fq5uoa" sourceRef="Event_HBS0P1" targetRef="Activity_bkhbNg"/>
                                <bpmn:sequenceFlow id="edge_LzO4bD" sourceRef="Activity_bkhbNg" targetRef="Event_Kiwr0S"/>
                                <bpmn:sequenceFlow id="edge_UStqCi" sourceRef="Event_HBS0P1" targetRef="Activity_mF6lEh"/>
                                <bpmn:sequenceFlow id="edge_HTTKXy" sourceRef="Activity_mF6lEh" targetRef="Activity_KW82cg"/>
                                <bpmn:sequenceFlow id="edge_wfHmS1" sourceRef="Activity_KW82cg" targetRef="Activity_bkhbNg"/>
                            </bpmn:process>
                            </bpmn:definitions>"""
    json_string = """{
                                "explanation": "I've added a new sequence flow between tasks.",
                                "title": "Add Sequence Flow",
                                "add": [
                                    {
                                        "type": "bpmn:sequenceFlow",
                                        "id": "edge_CalculateTaxes_UpdateDatabase",
                                        "source": "Activity_mF6lEh",
                                        "target": "Activity_KW82cg",
                                        "parentId": "Process_1"
                                    }
                                ],
                                "update": [],
                                "delete": []
                            }"""
    result = validator.validate_json(bpmn_result, json_string)
    assert result["valid"]
    assert not result["error_message"]


def test_validate_json_flow_target_does_not_exist():
    validator = BpmnValidator()
    bpmn_result = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="52a2e5da">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="b39d445a-2a71-488c-99d9-fc8d97b008e3"/>
                                </bpmn:extensionElements>
                                <bpmn:outgoing>edge_PSHwS0</bpmn:outgoing>
                                </bpmn:startEvent>
                                <bpmn:task id="Activity_uzumXt" name="Request Score">
                                <bpmn:incoming>edge_PSHwS0</bpmn:incoming>
                                <bpmn:outgoing>edge_hQT1wu</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:intermediateCatchEvent id="Event_HBS0P1">
                                <bpmn:incoming>edge_hQT1wu</bpmn:incoming>
                                <bpmn:outgoing>edge_XM4gdb</bpmn:outgoing>
                                <bpmn:outgoing>edge_fq5uoa</bpmn:outgoing>
                                <bpmn:outgoing>edge_UStqCi</bpmn:outgoing>
                                <bpmn:messageEventDefinition/>
                                </bpmn:intermediateCatchEvent>
                                <bpmn:task id="Activity_XAgqPn" name="Score Delayed Message">
                                <bpmn:incoming>edge_XM4gdb</bpmn:incoming>
                                <bpmn:outgoing>edge_XohuCd</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:task id="Activity_bkhbNg" name="Result">
                                <bpmn:incoming>edge_XohuCd</bpmn:incoming>
                                <bpmn:incoming>edge_fq5uoa</bpmn:incoming>
                                <bpmn:incoming>edge_wfHmS1</bpmn:incoming>
                                <bpmn:outgoing>edge_LzO4bD</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:endEvent id="Event_Kiwr0S">
                                <bpmn:incoming>edge_LzO4bD</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:task id="Activity_mF6lEh" name="Calculate Taxes">
                                <bpmn:incoming>edge_UStqCi</bpmn:incoming>
                                <bpmn:outgoing>edge_HTTKXy</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:task id="Activity_KW82cg" name="Pay Taxes">
                                <bpmn:incoming>edge_HTTKXy</bpmn:incoming>
                                <bpmn:outgoing>edge_wfHmS1</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:sequenceFlow id="edge_PSHwS0" sourceRef="Event_start" targetRef="Activity_uzumXt"/>
                                <bpmn:sequenceFlow id="edge_hQT1wu" sourceRef="Activity_uzumXt" targetRef="Event_HBS0P1"/>
                                <bpmn:sequenceFlow id="edge_XM4gdb" sourceRef="Event_HBS0P1" targetRef="Activity_XAgqPn"/>
                                <bpmn:sequenceFlow id="edge_XohuCd" sourceRef="Activity_XAgqPn" targetRef="Activity_bkhbNg"/>
                                <bpmn:sequenceFlow id="edge_fq5uoa" sourceRef="Event_HBS0P1" targetRef="Activity_bkhbNg"/>
                                <bpmn:sequenceFlow id="edge_LzO4bD" sourceRef="Activity_bkhbNg" targetRef="Event_Kiwr0S"/>
                                <bpmn:sequenceFlow id="edge_UStqCi" sourceRef="Event_HBS0P1" targetRef="Activity_mF6lEh"/>
                                <bpmn:sequenceFlow id="edge_HTTKXy" sourceRef="Activity_mF6lEh" targetRef="Activity_KW82cg"/>
                                <bpmn:sequenceFlow id="edge_wfHmS1" sourceRef="Activity_KW82cg" targetRef="Activity_bkhbNg"/>
                            </bpmn:process>
                            </bpmn:definitions>"""
    json_string = """{
                                "explanation": "I've added a new sequence flow between tasks.",
                                "title": "Add Sequence Flow",
                                    "add": [
                                        {
                                            "type": "bpmn:task",
                                            "id": "Activity_UpdateDatabase",
                                            "parentId": "Process_1",
                                            "name": "Update Database",
                                            "data": {
                                                "label": "Update Database"
                                            }
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "edge_CalculateTaxes_UpdateDatabase",
                                            "source": "Activity_mF6lEh",
                                            "target": "Activity_UpdateDatabase",
                                            "parentId": "Process_1"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "edge_UpdateDatabase_PayTaxes",
                                            "source": "Activity_UpdateDatabase",
                                            "target": "Activity_KW82ck",
                                            "parentId": "Process_1"
                                        }
                                    ],
                                    "update": [],
                                    "delete": [
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "edge_HTTKXy"
                                        }
                                    ]
                            }"""
    result = validator.validate_json(bpmn_result, json_string)
    assert not result["valid"]
    assert (
        result["error_message"] is not None
        and "Target element with id Activity_KW82ck for bpmn:sequenceFlow element with id edge_UpdateDatabase_PayTaxes does not exist."
        in result["error_message"]
    )
