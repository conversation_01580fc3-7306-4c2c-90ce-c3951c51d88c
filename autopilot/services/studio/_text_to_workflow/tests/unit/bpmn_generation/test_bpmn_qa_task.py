import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import CANNOT_PROCESS_REQUEST_EXPLANATION
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnRequestContext,
    ChatHistory,
    ModelType,
    Tool,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task import BpmnQATask
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage


@pytest.fixture
def mock_validator():
    with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock:
        yield mock


@pytest.fixture
def mock_logger():
    with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.LOGGER") as mock:
        yield mock


@pytest.fixture
def sample_bpmn_xml():
    return """<?xml version="1.0" encoding="UTF-8"?>
    <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL">
      <bpmn:process id="Process_1">
        <bpmn:startEvent id="StartEvent_1" />
      </bpmn:process>
    </bpmn:definitions>"""


@pytest.fixture
def default_bpmn_xml():
    return """<?xml version="1.0" encoding="UTF-8"?>
    <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL">
      <bpmn:process id="Process_Default">
        <bpmn:startEvent id="StartEvent_Default" />
      </bpmn:process>
    </bpmn:definitions>"""


@pytest.fixture
def mock_bpmn_qa_task(default_bpmn_xml):
    with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_base_task.BpmnBaseTask") as MockBaseTask:
        # Configure the base task
        mock_task = MagicMock()
        MockBaseTask.return_value = mock_task
        MockBaseTask.read_content.return_value = "mock content"

        # Set up configuration dictionary
        mock_task.common_config = {"default_bpmn_xml": default_bpmn_xml}
        mock_task.config = {
            "prompt": {"system_template": {"q&a": "system {bpmn_qa_examples}"}, "user_template": {"q&a": "user {current_bpmn} {query} {chat_history}"}}
        }

        # Mock the template methods
        mock_task._system_template.return_value = "system {bpmn_qa_examples}"
        mock_task._human_template.return_value = "user {current_bpmn} {query} {chat_history}"

        # Create the actual task
        task = BpmnQATask()

        # Connect the mocked methods
        task._call_llm = AsyncMock()
        task._get_related_chat_history = AsyncMock(return_value="mock chat history")

        yield task


class TestBpmnQATask:
    @pytest.mark.asyncio
    async def test_generate_calls_generate_bpmn_qa(self, mock_bpmn_qa_task):
        """Test that generate calls _generate_bpmn_qa with correct parameters."""
        # Setup
        mock_bpmn_qa_task._generate_bpmn_qa = AsyncMock(
            return_value=BpmnGenerationChatTaskResult(explanation="Test explanation", add=None, update=None, delete=None, title=None, usage=TokenUsage())
        )

        context = BpmnRequestContext(
            user_request="What is this BPMN?",
            current_bpmn="<bpmn>test</bpmn>",
            chat_history={},
            support_validation=True,
            image_data=None,
            request_context=None,
        )

        # Execute
        result = await mock_bpmn_qa_task.generate(context)

        # Verify
        mock_bpmn_qa_task._generate_bpmn_qa.assert_called_once_with(context.user_request, context.current_bpmn, context.chat_history, ModelType.OpenAI)

        assert result["tool"] == Tool.QA
        assert result["explanation"] == "Test explanation"

    @pytest.mark.asyncio
    async def test_generate_bpmn_qa_with_empty_bpmn(self, mock_bpmn_qa_task, default_bpmn_xml):
        """Test _generate_bpmn_qa with empty BPMN (should use default)."""
        # Setup
        usage = TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_response = '```json\n{"explanation": "This is a default BPMN"}\n```'
        mock_bpmn_qa_task._call_llm.return_value = (mock_response, usage)

        # Mock validator to return a successful validation
        with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock_validator:
            mock_validator.validate_qa_json.return_value = {"valid": True}

            # Execute
            result = await mock_bpmn_qa_task._generate_bpmn_qa("What is this BPMN?", "", None)

            # Verify
            assert mock_bpmn_qa_task._get_related_chat_history.called
            assert result["explanation"] == "This is a default BPMN"
            assert result["usage"] == usage

    @pytest.mark.asyncio
    async def test_generate_bpmn_qa_with_chat_history(self, mock_bpmn_qa_task, sample_bpmn_xml):
        """Test _generate_bpmn_qa with chat history."""
        # Setup
        usage = TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_response = '```json\n{"explanation": "Answer with chat history context"}\n```'
        mock_bpmn_qa_task._call_llm.return_value = (mock_response, usage)

        chat_history = {Tool.QA: [ChatHistory(request="Previous question", response="Previous answer", tool=Tool.QA.value, timestamp=datetime.datetime.now())]}

        # Mock validator
        with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock_validator:
            mock_validator.validate_qa_json.return_value = {"valid": True}

            # Execute
            result = await mock_bpmn_qa_task._generate_bpmn_qa("What is this BPMN?", sample_bpmn_xml, chat_history)

            # Verify
            mock_bpmn_qa_task._get_related_chat_history.assert_called_with(
                chat_history, mock_bpmn_qa_task.history_dependencies, "bpmn_generation_chat_model_openai"
            )
            assert result["explanation"] == "Answer with chat history context"

    @pytest.mark.asyncio
    async def test_generate_bpmn_qa_validation_failure(self, mock_bpmn_qa_task, sample_bpmn_xml, mock_logger):
        """Test _generate_bpmn_qa handling validation failures."""
        # Setup
        usage = TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_response = '```json\n{"invalid": "json"}\n```'
        mock_bpmn_qa_task._call_llm.return_value = (mock_response, usage)

        # Mock validator to return failure
        with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock_validator:
            mock_validator.validate_qa_json.return_value = {"valid": False, "error_message": "Invalid JSON structure"}

            # Execute
            result = await mock_bpmn_qa_task._generate_bpmn_qa("What is this BPMN?", sample_bpmn_xml, None)

            # Verify
            assert result["explanation"] == CANNOT_PROCESS_REQUEST_EXPLANATION
            assert mock_logger.error.called
            assert result["usage"] == usage

    @pytest.mark.asyncio
    async def test_json_parsing(self, mock_bpmn_qa_task, sample_bpmn_xml):
        """Test the parsing of JSON from LLM response."""
        # Setup
        usage = TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_response = '```json\n{"explanation": "Test explanation", "title": "Test title"}\n```'
        mock_bpmn_qa_task._call_llm.return_value = (mock_response, usage)

        # Mock validator
        with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock_validator:
            mock_validator.validate_qa_json.return_value = {"valid": True}

            # Execute
            result = await mock_bpmn_qa_task._generate_bpmn_qa("What is this BPMN?", sample_bpmn_xml, None)

            # Verify correct JSON parsing
            assert result["explanation"] == "Test explanation"
            assert result["title"] == "Test title"
            assert result["usage"] == usage

    @pytest.mark.asyncio
    async def test_call_llm_integration(self, mock_bpmn_qa_task, sample_bpmn_xml):
        """Test the integration with LLM call."""
        # Setup
        usage = TokenUsage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_bpmn_qa_task._call_llm.return_value = ('```json\n{"explanation": "LLM generated answer"}\n```', usage)

        # Mock validator
        with patch("services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task.VALIDATOR") as mock_validator:
            mock_validator.validate_qa_json.return_value = {"valid": True}

            # Execute
            await mock_bpmn_qa_task._generate_bpmn_qa("What is this BPMN?", sample_bpmn_xml, None)

            # Verify
            # Check that _call_llm was called with the expected system and user messages
            system_msg, user_msg, model_name = mock_bpmn_qa_task._call_llm.call_args[0]
            assert "system" == system_msg.type
            assert "human" == user_msg.type
            assert sample_bpmn_xml.strip() in user_msg.content
            assert "bpmn_generation_chat_model_openai" == model_name
