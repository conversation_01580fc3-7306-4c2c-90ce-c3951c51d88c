import numpy as np

from services.studio._text_to_workflow.models.model_manager import Model<PERSON><PERSON><PERSON>


def cosine_similarity(vec1: list[float], vec2: list[float]) -> float:
    """Calculate cosine similarity between two vectors"""
    try:
        dot_product = np.dot(vec1, vec2)
        norm_a = np.linalg.norm(vec1)
        norm_b = np.linalg.norm(vec2)

        # Avoid division by zero
        if norm_a == 0 or norm_b == 0:
            return 0.0

        return float(dot_product / (norm_a * norm_b))
    except Exception as e:
        print(f"Error calculating cosine similarity: {e}")
        # Return a low similarity score on error
        return 0.0


def text_similarity(text1: str, text2: str) -> float:
    # Get embedding model from model manager
    embedding_model = ModelManager().get_embeddings_model()

    # Clean and normalize texts
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()

    # Get embeddings for both texts
    embeddings = embedding_model.encode_batch([text1, text2], batch_size=2)

    # Calculate cosine similarity
    return cosine_similarity(embeddings[0], embeddings[1])
