import codecs
import pathlib
import typing as t

from services.studio._text_to_workflow.utils import yaml_utils


def get_case_name() -> str:
    msg = "Enter case name (Leave blank to update all existing cases): "
    case_name = codecs.decode(input(msg), "unicode_escape")
    return case_name


def get_case_input_from_stdin(case_name: str, cases: dict) -> str:
    msg = f"Enter input for '{case_name}'"
    if case_name in cases:
        msg += " (Leave blank to use existing)"
    msg += ": "
    answer = codecs.decode(input(msg), "unicode_escape")
    if not answer and case_name in cases:
        return cases[case_name]["input"]
    return answer


def get_case_input_from_yaml(case_name: str, cases: dict) -> t.Any:
    msg = f"Enter yaml file path for '{case_name}' input"
    if case_name in cases:
        msg += " (Leave blank to use existing)"
    msg += ": "
    answer = codecs.decode(input(msg), "unicode_escape")
    if not answer and case_name in cases:
        return cases[case_name]["input"]
    path = pathlib.Path(answer)
    if not path.exists():
        raise FileNotFoundError(f"File not found: {path}")
    return yaml_utils.yaml_load(path, load_multiline=True)


def get_cases(path: pathlib.Path) -> tuple[list[str], dict[str, dict]]:
    cases = load_cases(path)
    case_name = get_case_name()
    case_names = [case_name] if case_name else list(cases.keys())
    return case_names, cases


def load_cases(cases_path: pathlib.Path) -> dict:
    return yaml_utils.yaml_load(cases_path, load_multiline=True)


def save_cases(cases: dict, cases_path: pathlib.Path) -> None:
    yaml_utils.yaml_dump(cases, cases_path)
