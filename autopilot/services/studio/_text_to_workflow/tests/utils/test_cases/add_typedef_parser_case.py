import argparse
import pathlib

import typing_extensions as t

from services.studio._text_to_workflow.common import typedefs_parser
from services.studio._text_to_workflow.tests.unit import test_typedefs_parser
from services.studio._text_to_workflow.tests.utils.test_cases import common as test_cases_common
from services.studio._text_to_workflow.utils import yaml_utils


def _add_case(cases_path: pathlib.Path, case_fn: t.Callable[[str], t.Any]):
    case_names, cases = test_cases_common.get_cases(cases_path)
    for current_case_name in case_names:
        current_case_input = test_cases_common.get_case_input_from_stdin(current_case_name, cases)
        try:
            expected = case_fn(current_case_input)
        except Exception:
            expected = {}
        cases[current_case_name] = {"input": current_case_input, "expected": expected}
    yaml_utils.yaml_dump(cases, cases_path)


def add_parse_typedef_case():
    _add_case(test_typedefs_parser._parse_typedef_cases_path, typedefs_parser.parse_typedef)


def add_parse_typedefs_case():
    _add_case(test_typedefs_parser._parse_typedefs_cases_path, typedefs_parser.parse_typedefs)


def add_parse_namespaces_case():
    _add_case(test_typedefs_parser._parse_namespace_cases_path, typedefs_parser.parse_namespaces)


def program(type: t.Literal["typedef", "typedefs", "namespaces"]):
    print(f"Adding/Updating {type} case(s).")
    if type == "typedef":
        add_parse_typedef_case()
    elif type == "typedefs":
        add_parse_typedefs_case()
    elif type == "namespaces":
        add_parse_namespaces_case()
    else:
        raise ValueError("Unknown case type")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("type", type=str, choices=["typedef", "typedefs", "namespaces"], help="Type of case to add.")
    args = ap.parse_args()
    program(args.type)
