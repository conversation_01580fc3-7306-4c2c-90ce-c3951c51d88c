# Create a dummy message emitter for testing
from typing import Any, Callable, Optional

from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter, SSEMessage


class TestableMessageEmitter(IMessageEmitter):
    def __init__(self):
        self.messages: dict[str, list[SSEMessage]] = {"message": [], "debug": [], "error": []}
        self.completion_message = None
        self.message_id = 0

    async def emit_message_part(self, content: Any, message_id_delta: int) -> int:
        for message in self.messages["message"]:
            if message.id == message_id_delta:
                message.content = message.content + content
                self.message_id += 1

                return message.id

        raise ValueError(f"Message with id {message_id_delta} not found")

    async def emit_message(self, content: Any, message_type: str = "message") -> int:
        msg = SSEMessage(id=self.message_id, content=content)
        if message_type not in self.messages:
            self.messages[message_type] = []

        self.messages[message_type].append(msg)
        self.message_id += 1
        return msg.id

    async def emit_debug_message(self, content: Any | Callable[[], Any]) -> int | None:
        msg = SSEMessage(id=self.message_id, content=content)
        self.messages["debug"].append(msg)
        self.message_id += 1
        return msg.id

    async def emit_error(self, content: Any, end_stream: bool = True) -> int:
        msg = SSEMessage(id=self.message_id, content=content)
        self.messages["error"].append(msg)
        self.message_id += 1
        return msg.id

    async def emit_completion(self, content: Optional[Any] = None) -> int:
        if self.completion_message:
            raise ValueError("Completion message already emitted")

        msg = SSEMessage(id=self.message_id, content=content)
        self.completion_message = msg
        self.message_id += 1
        return msg.id
