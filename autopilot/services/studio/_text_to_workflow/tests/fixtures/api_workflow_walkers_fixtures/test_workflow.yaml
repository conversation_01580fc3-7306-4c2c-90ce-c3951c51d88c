input: ''
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Search for customer by email
    activity: Oracle_NetSuiteSearch_Customers
    id: SearchCustomers_1
    with:
      email: ${$workflow.input.email}
  - thought: Check if exactly one customer was found
    activity: If
    id: If_1
    condition: ${$context.outputs.SearchCustomers_1.content.length == 1}
    then:
    - thought: Return the phone number of the customer
      activity: Response
      id: Response_1
      type: SUCCESS
      response_message: ${$context.outputs.SearchCustomers_1.content[0].phone}
    else:
    - thought: Return an error message if none or more than one customers were found
      activity: Response
      id: Response_2
      type: FAILURE
      response_message: '${"Error: None or more than one customers were found with the given email address."}'
  - thought: For each ticket, create a task in Asana
    activity: ForEach
    id: ForEach_1
    for:
      each: ticket
      in: $context.outputs.query_tickets_1
      at: index
    do:
    - thought: Create a task in Asana
      activity: AsanaCreate_Task
      id: tasks_1
      with:
        workspace: ${$context.outputs.SearchCustomers_1.content[0].workspace}