generate_expression_input = [
    # vb simple test case
    {
        "userRequest": "Calculate the average value of valueList",
        "availableVariables": [{"name": "valueList", "type": "List(Of Double)"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "currentExpression": None,
        "additionalTypeDefinitions": "",
        "benchmarkExpression": [
            "valueList.Average()",
            "If(valueList.Count = 0, 0.0, valueList.Average())",
            "If(valueList IsNot Nothing AndAlso valueList.Count > 0, valueList.Average(), 0.0D)",
        ],
    },
    # csharp simple test case
    {
        "userRequest": "Calculate the average value of valueList",
        "availableVariables": [{"name": "valueList", "type": "List<Double>"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "csharp",
        "currentExpression": None,
        "additionalTypeDefinitions": "",
        "benchmarkExpression": ["valueList.Average()"],
    },
    # vb update expression test case
    {
        "userRequest": "Calculate the average value of valueList",
        "availableVariables": [{"name": "valueList", "type": "List<Double>"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "currentExpression": "valueList",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": [
            "valueList.Average()",
            "If(valueList.Count = 0, 0.0, valueList.Average())",
            "If(valueList IsNot Nothing AndAlso valueList.Count > 0, valueList.Average(), 0.0D)",
        ],
    },
    # csharp update expression test case
    {
        "userRequest": "Calculate the average value of valueList",
        "availableVariables": [{"name": "valueList", "type": "List<Double>"}],
        "expressionTypeDefinition": "Double",
        "currentExpression": "valueList",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": ["valueList.Average()"],
    },
    # bpmn simple test case
    {
        "userRequest": "date time now minus 7 days",
        "availableVariables": [],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "csharp",
        "currentExpression": "",
        "additionalTypeDefinitions": "",
        "source": "bpmn",
        "benchmarkExpression": ["DateTime.Now.AddDays(-7).ToString()"],
    },
]
