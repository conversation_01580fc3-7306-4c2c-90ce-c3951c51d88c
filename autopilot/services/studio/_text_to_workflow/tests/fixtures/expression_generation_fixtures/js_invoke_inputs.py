examples = [
    {
        "userRequest": "Compute the sum of two numbers",
        "currentExpression": "",
        "benchmarkExpression": "const sum = var1 + var2;\nreturn sum;",
        "availableVariables": [{"name": "var1", "type": "number"}, {"name": "var2", "type": "number"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "outputType": "yaml",
    }
]
