examples = [
    {
        "userRequest": "Check if the number num is even",
        "currentExpression": "",
        "benchmarkExpression": "num % 2 === 0;",
        "availableVariables": [{"name": "num", "type": "number"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "boolean",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "WriteLine",
    },
    {
        "userRequest": "Extract all names from an array of users present in the users variable",
        "currentExpression": "",
        "benchmarkExpression": "$users | .[] | .name",
        "availableVariables": [{"name": "users", "type": "array[object]"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string[]",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "WriteLine",
    },
]
