﻿empty string:
  input: ''
  expected:
    global: {}
empty namespace:
  input: namespace TestNamespace { }
  expected:
    TestNamespace: {}
namespace with one class:
  input: namespace TestNamespace { class TestClass { string property1; string property2;}}
  expected:
    TestNamespace:
      TestClass:
        name: TestClass
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass { string property1; string property2;}
namespace with multiple classes:
  input: namespace TestNamespace { class TestClass { string property1; string property2;} class TestClass2 { string property1; string property2;}}
  expected:
    TestNamespace:
      TestClass:
        name: TestClass
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass { string property1; string property2;}
      TestClass2:
        name: TestClass2
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass2 { string property1; string property2;}
multiple namespaces with multiple classes:
  input: namespace TestNamespace { class TestClass { string property1; string property2;} class TestClass2 { string property1; string property2;}} namespace TestNamespace2 { class TestClass3 { string property1; string property2;}}
  expected:
    TestNamespace:
      TestClass:
        name: TestClass
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass { string property1; string property2;}
      TestClass2:
        name: TestClass2
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass2 { string property1; string property2;}
    TestNamespace2:
      TestClass3:
        name: TestClass3
        type: class
        description: ''
        type_params: {}
        params:
          property1:
            name: property1
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          property2:
            name: property2
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class TestClass3 { string property1; string property2;}
generate expression request:
  input: namespace UiPath.MicrosoftOffice365.Models { class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; MessageImportance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; System.Net.Mail.AttachmentCollection Attachments; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; IEnumerable<string> ReplyToAddressList; System.Net.Mail.MailAddressCollection To; IEnumerable<string> ToAddressList; System.Net.Mail.MailAddressCollection CC; IEnumerable<string> CCAddressList; System.Net.Mail.MailAddressCollection Bcc; IEnumerable<string> BccAddressList; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; int AttachmentCount; IEnumerable<string> AttachmentsNamesList; List<AttachmentInfoSlim> AttachmentsInfoList; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AlternateViewCollection AlternateViews; } } namespace System.Net.Mail { enum MailPriority { Normal, Low, High, } class MailAddress { string DisplayName; string User; string Host; string Address; } enum DeliveryNotificationOptions { None, OnSuccess, OnFailure, Delay, Never, } } namespace UiPath.Shared.Services.Graph.Mail.Models { class AttachmentInfoSlim { string Filename; string MimeType; long? Size; } }
  expected:
    UiPath.MicrosoftOffice365.Models:
      Office365Message:
        name: Office365Message
        type: class
        description: ''
        type_params: {}
        params:
          MessageId:
            name: MessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyPreview:
            name: BodyPreview
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Flag:
            name: Flag
            type: Microsoft.Graph.FollowupFlag
            ctype: Microsoft.Graph.FollowupFlag
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - Microsoft.Graph.FollowupFlag
          Importance:
            name: Importance
            type: MessageImportance?
            ctype: MessageImportance?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MessageImportance
            - '?'
          IsRead:
            name: IsRead
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          LastModifiedDateTime:
            name: LastModifiedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          ParentFolderId:
            name: ParentFolderId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReceivedDateTime:
            name: ReceivedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          SentDateTime:
            name: SentDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          WebLink:
            name: WebLink
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Priority:
            name: Priority
            type: System.Net.Mail.MailPriority
            ctype: System.Net.Mail.MailPriority
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailPriority
          Categories:
            name: Categories
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InternetMessageId:
            name: InternetMessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Attachments:
            name: Attachments
            type: System.Net.Mail.AttachmentCollection
            ctype: System.Net.Mail.AttachmentCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.AttachmentCollection
          Body:
            name: Body
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyAsHtml:
            name: BodyAsHtml
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Subject:
            name: Subject
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromDisplayName:
            name: FromDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromAddress:
            name: FromAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderDisplayName:
            name: SenderDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderAddress:
            name: SenderAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReplyToList:
            name: ReplyToList
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          ReplyToAddressList:
            name: ReplyToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          To:
            name: To
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          ToAddressList:
            name: ToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          CC:
            name: CC
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          CCAddressList:
            name: CCAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          Bcc:
            name: Bcc
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          BccAddressList:
            name: BccAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          StandardAttachmentCount:
            name: StandardAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          StandardAttachmentNames:
            name: StandardAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InlineAttachmentCount:
            name: InlineAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          InlineAttachmentNames:
            name: InlineAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentCount:
            name: AttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          AttachmentsNamesList:
            name: AttachmentsNamesList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentsInfoList:
            name: AttachmentsInfoList
            type: List<AttachmentInfoSlim>
            ctype: List<AttachmentInfoSlim>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - List
            - AttachmentInfoSlim
          From:
            name: From
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          Sender:
            name: Sender
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          ReplyTo:
            name: ReplyTo
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          DeliveryNotificationOptions:
            name: DeliveryNotificationOptions
            type: System.Net.Mail.DeliveryNotificationOptions
            ctype: System.Net.Mail.DeliveryNotificationOptions
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.DeliveryNotificationOptions
          SubjectEncoding:
            name: SubjectEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          Headers:
            name: Headers
            type: NameValueCollection
            ctype: NameValueCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - NameValueCollection
          HeadersEncoding:
            name: HeadersEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          BodyEncoding:
            name: BodyEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          BodyTransferEncoding:
            name: BodyTransferEncoding
            type: System.Net.Mime.TransferEncoding
            ctype: System.Net.Mime.TransferEncoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mime.TransferEncoding
          IsBodyHtml:
            name: IsBodyHtml
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          AlternateViews:
            name: AlternateViews
            type: System.Net.Mail.AlternateViewCollection
            ctype: System.Net.Mail.AlternateViewCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.AlternateViewCollection
        text: class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; MessageImportance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; System.Net.Mail.AttachmentCollection Attachments; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; IEnumerable<string> ReplyToAddressList; System.Net.Mail.MailAddressCollection To; IEnumerable<string> ToAddressList; System.Net.Mail.MailAddressCollection CC; IEnumerable<string> CCAddressList; System.Net.Mail.MailAddressCollection Bcc; IEnumerable<string> BccAddressList; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; int AttachmentCount; IEnumerable<string> AttachmentsNamesList; List<AttachmentInfoSlim> AttachmentsInfoList; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AlternateViewCollection AlternateViews;}
    System.Net.Mail:
      MailPriority:
        name: MailPriority
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - Normal
            - Low
            - High
        text: enum MailPriority { Normal, Low, High,}
      MailAddress:
        name: MailAddress
        type: class
        description: ''
        type_params: {}
        params:
          DisplayName:
            name: DisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          User:
            name: User
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Host:
            name: Host
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Address:
            name: Address
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class MailAddress { string DisplayName; string User; string Host; string Address;}
      DeliveryNotificationOptions:
        name: DeliveryNotificationOptions
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - None
            - OnSuccess
            - OnFailure
            - Delay
            - Never
        text: enum DeliveryNotificationOptions { None, OnSuccess, OnFailure, Delay, Never,}
    UiPath.Shared.Services.Graph.Mail.Models:
      AttachmentInfoSlim:
        name: AttachmentInfoSlim
        type: class
        description: ''
        type_params: {}
        params:
          Filename:
            name: Filename
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          MimeType:
            name: MimeType
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Size:
            name: Size
            type: long?
            ctype: long?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - long
            - '?'
        text: class AttachmentInfoSlim { string Filename; string MimeType; long? Size;}
fix expression request:
  input: namespace UiPath.MicrosoftOffice365.Models { class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; MessageImportance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; System.Net.Mail.AttachmentCollection Attachments; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; IEnumerable<string> ReplyToAddressList; System.Net.Mail.MailAddressCollection To; IEnumerable<string> ToAddressList; System.Net.Mail.MailAddressCollection CC; IEnumerable<string> CCAddressList; System.Net.Mail.MailAddressCollection Bcc; IEnumerable<string> BccAddressList; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; int AttachmentCount; IEnumerable<string> AttachmentsNamesList; List<AttachmentInfoSlim> AttachmentsInfoList; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AlternateViewCollection AlternateViews; } } namespace System.Net.Mail { enum MailPriority { Normal, Low, High, } class MailAddress { string DisplayName; string User; string Host; string Address; } enum DeliveryNotificationOptions { None, OnSuccess, OnFailure, Delay, Never, } } namespace UiPath.Shared.Services.Graph.Mail.Models { class AttachmentInfoSlim { string Filename; string MimeType; long? Size; } }
  expected:
    UiPath.MicrosoftOffice365.Models:
      Office365Message:
        name: Office365Message
        type: class
        description: ''
        type_params: {}
        params:
          MessageId:
            name: MessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyPreview:
            name: BodyPreview
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Flag:
            name: Flag
            type: Microsoft.Graph.FollowupFlag
            ctype: Microsoft.Graph.FollowupFlag
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - Microsoft.Graph.FollowupFlag
          Importance:
            name: Importance
            type: MessageImportance?
            ctype: MessageImportance?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MessageImportance
            - '?'
          IsRead:
            name: IsRead
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          LastModifiedDateTime:
            name: LastModifiedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          ParentFolderId:
            name: ParentFolderId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReceivedDateTime:
            name: ReceivedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          SentDateTime:
            name: SentDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          WebLink:
            name: WebLink
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Priority:
            name: Priority
            type: System.Net.Mail.MailPriority
            ctype: System.Net.Mail.MailPriority
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailPriority
          Categories:
            name: Categories
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InternetMessageId:
            name: InternetMessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Attachments:
            name: Attachments
            type: System.Net.Mail.AttachmentCollection
            ctype: System.Net.Mail.AttachmentCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.AttachmentCollection
          Body:
            name: Body
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyAsHtml:
            name: BodyAsHtml
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Subject:
            name: Subject
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromDisplayName:
            name: FromDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromAddress:
            name: FromAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderDisplayName:
            name: SenderDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderAddress:
            name: SenderAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReplyToList:
            name: ReplyToList
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          ReplyToAddressList:
            name: ReplyToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          To:
            name: To
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          ToAddressList:
            name: ToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          CC:
            name: CC
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          CCAddressList:
            name: CCAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          Bcc:
            name: Bcc
            type: System.Net.Mail.MailAddressCollection
            ctype: System.Net.Mail.MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddressCollection
          BccAddressList:
            name: BccAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          StandardAttachmentCount:
            name: StandardAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          StandardAttachmentNames:
            name: StandardAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InlineAttachmentCount:
            name: InlineAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          InlineAttachmentNames:
            name: InlineAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentCount:
            name: AttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          AttachmentsNamesList:
            name: AttachmentsNamesList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentsInfoList:
            name: AttachmentsInfoList
            type: List<AttachmentInfoSlim>
            ctype: List<AttachmentInfoSlim>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - List
            - AttachmentInfoSlim
          From:
            name: From
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          Sender:
            name: Sender
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          ReplyTo:
            name: ReplyTo
            type: System.Net.Mail.MailAddress
            ctype: System.Net.Mail.MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.MailAddress
          DeliveryNotificationOptions:
            name: DeliveryNotificationOptions
            type: System.Net.Mail.DeliveryNotificationOptions
            ctype: System.Net.Mail.DeliveryNotificationOptions
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.DeliveryNotificationOptions
          SubjectEncoding:
            name: SubjectEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          Headers:
            name: Headers
            type: NameValueCollection
            ctype: NameValueCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - NameValueCollection
          HeadersEncoding:
            name: HeadersEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          BodyEncoding:
            name: BodyEncoding
            type: System.Text.Encoding
            ctype: System.Text.Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Text.Encoding
          BodyTransferEncoding:
            name: BodyTransferEncoding
            type: System.Net.Mime.TransferEncoding
            ctype: System.Net.Mime.TransferEncoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mime.TransferEncoding
          IsBodyHtml:
            name: IsBodyHtml
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          AlternateViews:
            name: AlternateViews
            type: System.Net.Mail.AlternateViewCollection
            ctype: System.Net.Mail.AlternateViewCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - System.Net.Mail.AlternateViewCollection
        text: class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; MessageImportance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; System.Net.Mail.AttachmentCollection Attachments; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; IEnumerable<string> ReplyToAddressList; System.Net.Mail.MailAddressCollection To; IEnumerable<string> ToAddressList; System.Net.Mail.MailAddressCollection CC; IEnumerable<string> CCAddressList; System.Net.Mail.MailAddressCollection Bcc; IEnumerable<string> BccAddressList; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; int AttachmentCount; IEnumerable<string> AttachmentsNamesList; List<AttachmentInfoSlim> AttachmentsInfoList; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AlternateViewCollection AlternateViews;}
    System.Net.Mail:
      MailPriority:
        name: MailPriority
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - Normal
            - Low
            - High
        text: enum MailPriority { Normal, Low, High,}
      MailAddress:
        name: MailAddress
        type: class
        description: ''
        type_params: {}
        params:
          DisplayName:
            name: DisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          User:
            name: User
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Host:
            name: Host
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Address:
            name: Address
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: class MailAddress { string DisplayName; string User; string Host; string Address;}
      DeliveryNotificationOptions:
        name: DeliveryNotificationOptions
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - None
            - OnSuccess
            - OnFailure
            - Delay
            - Never
        text: enum DeliveryNotificationOptions { None, OnSuccess, OnFailure, Delay, Never,}
    UiPath.Shared.Services.Graph.Mail.Models:
      AttachmentInfoSlim:
        name: AttachmentInfoSlim
        type: class
        description: ''
        type_params: {}
        params:
          Filename:
            name: Filename
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          MimeType:
            name: MimeType
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Size:
            name: Size
            type: long?
            ctype: long?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - long
            - '?'
        text: class AttachmentInfoSlim { string Filename; string MimeType; long? Size;}
generate sequence request:
  input: "namespace UiPath.MicrosoftOffice365.Models { class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nList<AttachmentInfoSlim> AttachmentsInfoList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;\r\n}\r\n} namespace System.Net.Mail { enum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,\r\n}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;\r\n}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,\r\n}\r\n} namespace UiPath.Shared.Services.Graph.Mail.Models { class AttachmentInfoSlim\r\n{\r\nstring Filename;\r\nstring MimeType;\r\nlong? Size;\r\n}\r\n}"
  expected:
    UiPath.MicrosoftOffice365.Models:
      Office365Message:
        name: Office365Message
        type: class
        description: ''
        type_params: {}
        params:
          MessageId:
            name: MessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyPreview:
            name: BodyPreview
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Flag:
            name: Flag
            type: FollowupFlag
            ctype: FollowupFlag
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - FollowupFlag
          Importance:
            name: Importance
            type: MessageImportance?
            ctype: MessageImportance?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MessageImportance
            - '?'
          IsRead:
            name: IsRead
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          LastModifiedDateTime:
            name: LastModifiedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          ParentFolderId:
            name: ParentFolderId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReceivedDateTime:
            name: ReceivedDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          SentDateTime:
            name: SentDateTime
            type: DateTime?
            ctype: DateTime?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DateTime
            - '?'
          WebLink:
            name: WebLink
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Priority:
            name: Priority
            type: MailPriority
            ctype: MailPriority
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailPriority
          Categories:
            name: Categories
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InternetMessageId:
            name: InternetMessageId
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Attachments:
            name: Attachments
            type: AttachmentCollection
            ctype: AttachmentCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - AttachmentCollection
          Body:
            name: Body
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          BodyAsHtml:
            name: BodyAsHtml
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Subject:
            name: Subject
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromDisplayName:
            name: FromDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          FromAddress:
            name: FromAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderDisplayName:
            name: SenderDisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          SenderAddress:
            name: SenderAddress
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          ReplyToList:
            name: ReplyToList
            type: MailAddressCollection
            ctype: MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddressCollection
          ReplyToAddressList:
            name: ReplyToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          To:
            name: To
            type: MailAddressCollection
            ctype: MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddressCollection
          ToAddressList:
            name: ToAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          CC:
            name: CC
            type: MailAddressCollection
            ctype: MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddressCollection
          CCAddressList:
            name: CCAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          Bcc:
            name: Bcc
            type: MailAddressCollection
            ctype: MailAddressCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddressCollection
          BccAddressList:
            name: BccAddressList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          StandardAttachmentCount:
            name: StandardAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          StandardAttachmentNames:
            name: StandardAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          InlineAttachmentCount:
            name: InlineAttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          InlineAttachmentNames:
            name: InlineAttachmentNames
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentCount:
            name: AttachmentCount
            type: int
            ctype: int
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - int
          AttachmentsNamesList:
            name: AttachmentsNamesList
            type: IEnumerable<string>
            ctype: IEnumerable<string>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - IEnumerable
            - string
          AttachmentsInfoList:
            name: AttachmentsInfoList
            type: List<AttachmentInfoSlim>
            ctype: List<AttachmentInfoSlim>
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - List
            - AttachmentInfoSlim
          From:
            name: From
            type: MailAddress
            ctype: MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddress
          Sender:
            name: Sender
            type: MailAddress
            ctype: MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddress
          ReplyTo:
            name: ReplyTo
            type: MailAddress
            ctype: MailAddress
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - MailAddress
          DeliveryNotificationOptions:
            name: DeliveryNotificationOptions
            type: DeliveryNotificationOptions
            ctype: DeliveryNotificationOptions
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - DeliveryNotificationOptions
          SubjectEncoding:
            name: SubjectEncoding
            type: Encoding
            ctype: Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - Encoding
          Headers:
            name: Headers
            type: NameValueCollection
            ctype: NameValueCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - NameValueCollection
          HeadersEncoding:
            name: HeadersEncoding
            type: Encoding
            ctype: Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - Encoding
          BodyEncoding:
            name: BodyEncoding
            type: Encoding
            ctype: Encoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - Encoding
          BodyTransferEncoding:
            name: BodyTransferEncoding
            type: TransferEncoding
            ctype: TransferEncoding
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - TransferEncoding
          IsBodyHtml:
            name: IsBodyHtml
            type: bool
            ctype: bool
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - bool
          AlternateViews:
            name: AlternateViews
            type: AlternateViewCollection
            ctype: AlternateViewCollection
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - AlternateViewCollection
        text: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nList<AttachmentInfoSlim> AttachmentsInfoList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}"
    System.Net.Mail:
      MailPriority:
        name: MailPriority
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - Normal
            - Low
            - High
        text: "enum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}"
      MailAddress:
        name: MailAddress
        type: class
        description: ''
        type_params: {}
        params:
          DisplayName:
            name: DisplayName
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          User:
            name: User
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Host:
            name: Host
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Address:
            name: Address
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
        text: "class MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}"
      DeliveryNotificationOptions:
        name: DeliveryNotificationOptions
        type: enum
        description: ''
        type_params: {}
        params:
          enumerator:
            name: values
            type: string
            modifier: private
            category: Property
            components:
            - string
            description: ''
            required: true
            values:
            - None
            - OnSuccess
            - OnFailure
            - Delay
            - Never
        text: "enum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}"
    UiPath.Shared.Services.Graph.Mail.Models:
      AttachmentInfoSlim:
        name: AttachmentInfoSlim
        type: class
        description: ''
        type_params: {}
        params:
          Filename:
            name: Filename
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          MimeType:
            name: MimeType
            type: string
            ctype: string
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - string
          Size:
            name: Size
            type: long?
            ctype: long?
            modifier: public
            description: ''
            required: false
            category: Property
            components:
            - long
            - '?'
        text: "class AttachmentInfoSlim\r\n{\r\nstring Filename;\r\nstring MimeType;\r\nlong? Size;}"
