﻿empty typedefs:
  input: ''
  expected: {}
embeddings.db typedefs:
  input: "class Office365Message\r\n{\r\nstring MessageId ;\r\nstring BodyPreview ;\r\nFollowupFlag Flag ;\r\nMessageImportance? Importance ;\r\nbool IsRead ;\r\nDateTime? LastModifiedDateTime ;\r\nstring ParentFolderId ;\r\nDateTime? ReceivedDateTime ;\r\nDateTime? SentDateTime ;\r\nstring WebLink ;\r\nMailPriority Priority ;\r\nIEnumerable<string> Categories ;\r\nstring InternetMessageId ;\r\nAttachmentCollection Attachments ;\r\nstring Body ;\r\nstring BodyAsHtml ;\r\nstring Subject ;\r\nstring FromDisplayName ;\r\nstring FromAddress ;\r\nstring SenderDisplayName ;\r\nstring SenderAddress ;\r\nMailAddressCollection ReplyToList ;\r\nIEnumerable<string> ReplyToAddressList ;\r\nMailAddressCollection To ;\r\nIEnumerable<string> ToAddressList ;\r\nMailAddressCollection CC ;\r\nIEnumerable<string> CCAddressList ;\r\nMailAddressCollection Bcc ;\r\nIEnumerable<string> BccAddressList ;\r\nint StandardAttachmentCount ;\r\nIEnumerable<string> StandardAttachmentNames ;\r\nint InlineAttachmentCount ;\r\nIEnumerable<string> InlineAttachmentNames ;\r\nint AttachmentCount ;\r\nIEnumerable<string> AttachmentsNamesList ;\r\nList<AttachmentInfoSlim> AttachmentsInfoList ;\r\nMailAddress? From ;\r\nMailAddress? Sender ;\r\nMailAddress? ReplyTo ;\r\nDeliveryNotificationOptions DeliveryNotificationOptions ;\r\nEncoding? SubjectEncoding ;\r\nNameValueCollection Headers ;\r\nEncoding? HeadersEncoding ;\r\nEncoding? BodyEncoding ;\r\nTransferEncoding BodyTransferEncoding ;\r\nbool IsBodyHtml ;\r\nAlternateViewCollection AlternateViews ;\r\n}\r\nclass MailAddressCollection : Collection<MailAddress>\r\n{\r\n}\r\nclass MailAddress\r\n{\r\nstring DisplayName ;\r\nstring User ;\r\nstring Host ;\r\nstring Address ;\r\n}\r\nclass AttachmentInfoSlim\r\n{\r\nstring Filename ;\r\nstring MimeType ;\r\nlong? Size ;\r\n}\r\nenum MailPriority\r\n{\r\nNormal,Low,High}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,OnSuccess,OnFailure,Delay,Never}\r\nsealed class AttachmentCollection : Collection<Attachment>, IDisposable\r\n{\r\n}\r\nclass Attachment\r\n{\r\nstring? Name ;\r\nEncoding? NameEncoding ;\r\nContentDisposition? ContentDisposition ;\r\nStream ContentStream ;\r\nstring ContentId ;\r\nContentType ContentType ;\r\nTransferEncoding TransferEncoding ;\r\n}\r\nsealed class AlternateViewCollection\r\n{\r\n}\r\nclass AlternateView\r\n{\r\nLinkedResourceCollection LinkedResources ;\r\nUri? BaseUri ;\r\nStream ContentStream ;\r\nstring ContentId ;\r\nContentType ContentType ;\r\nTransferEncoding TransferEncoding ;\r\n}\r\nclass FollowupFlag\r\n{\r\nDateTimeTimeZone CompletedDateTime ;\r\nDateTimeTimeZone DueDateTime ;\r\nFollowupFlagStatus? FlagStatus ;\r\nDateTimeTimeZone StartDateTime ;\r\nIDictionary<string, object> AdditionalData ;\r\nstring ODataType ;\r\n}\r\n[DataContract]\r\nreadonly record struct MessageImportance\r\n{\r\nMessageImportance Low ;\r\nMessageImportance Normal ;\r\nMessageImportance High ;\r\n}\r\n"
  expected:
    Office365Message:
      name: Office365Message
      type: class
      description: ''
      type_params: {}
      params:
        MessageId:
          name: MessageId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        BodyPreview:
          name: BodyPreview
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Flag:
          name: Flag
          type: FollowupFlag
          ctype: FollowupFlag
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - FollowupFlag
        Importance:
          name: Importance
          type: MessageImportance?
          ctype: MessageImportance?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MessageImportance
          - '?'
        IsRead:
          name: IsRead
          type: bool
          ctype: bool
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - bool
        LastModifiedDateTime:
          name: LastModifiedDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        ParentFolderId:
          name: ParentFolderId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ReceivedDateTime:
          name: ReceivedDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        SentDateTime:
          name: SentDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        WebLink:
          name: WebLink
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Priority:
          name: Priority
          type: MailPriority
          ctype: MailPriority
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailPriority
        Categories:
          name: Categories
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        InternetMessageId:
          name: InternetMessageId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Attachments:
          name: Attachments
          type: AttachmentCollection
          ctype: AttachmentCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - AttachmentCollection
        Body:
          name: Body
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        BodyAsHtml:
          name: BodyAsHtml
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Subject:
          name: Subject
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        FromDisplayName:
          name: FromDisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        FromAddress:
          name: FromAddress
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        SenderDisplayName:
          name: SenderDisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        SenderAddress:
          name: SenderAddress
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ReplyToList:
          name: ReplyToList
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        ReplyToAddressList:
          name: ReplyToAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        To:
          name: To
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        ToAddressList:
          name: ToAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        CC:
          name: CC
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        CCAddressList:
          name: CCAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        Bcc:
          name: Bcc
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        BccAddressList:
          name: BccAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        StandardAttachmentCount:
          name: StandardAttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        StandardAttachmentNames:
          name: StandardAttachmentNames
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        InlineAttachmentCount:
          name: InlineAttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        InlineAttachmentNames:
          name: InlineAttachmentNames
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        AttachmentCount:
          name: AttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        AttachmentsNamesList:
          name: AttachmentsNamesList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        AttachmentsInfoList:
          name: AttachmentsInfoList
          type: List<AttachmentInfoSlim>
          ctype: List<AttachmentInfoSlim>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - List
          - AttachmentInfoSlim
        From:
          name: From
          type: MailAddress?
          ctype: MailAddress?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
          - '?'
        Sender:
          name: Sender
          type: MailAddress?
          ctype: MailAddress?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
          - '?'
        ReplyTo:
          name: ReplyTo
          type: MailAddress?
          ctype: MailAddress?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
          - '?'
        DeliveryNotificationOptions:
          name: DeliveryNotificationOptions
          type: DeliveryNotificationOptions
          ctype: DeliveryNotificationOptions
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DeliveryNotificationOptions
        SubjectEncoding:
          name: SubjectEncoding
          type: Encoding?
          ctype: Encoding?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
          - '?'
        Headers:
          name: Headers
          type: NameValueCollection
          ctype: NameValueCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - NameValueCollection
        HeadersEncoding:
          name: HeadersEncoding
          type: Encoding?
          ctype: Encoding?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
          - '?'
        BodyEncoding:
          name: BodyEncoding
          type: Encoding?
          ctype: Encoding?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
          - '?'
        BodyTransferEncoding:
          name: BodyTransferEncoding
          type: TransferEncoding
          ctype: TransferEncoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - TransferEncoding
        IsBodyHtml:
          name: IsBodyHtml
          type: bool
          ctype: bool
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - bool
        AlternateViews:
          name: AlternateViews
          type: AlternateViewCollection
          ctype: AlternateViewCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - AlternateViewCollection
      text: "class Office365Message\r\n{\r\nstring MessageId ;\r\nstring BodyPreview ;\r\nFollowupFlag Flag ;\r\nMessageImportance? Importance ;\r\nbool IsRead ;\r\nDateTime? LastModifiedDateTime ;\r\nstring ParentFolderId ;\r\nDateTime? ReceivedDateTime ;\r\nDateTime? SentDateTime ;\r\nstring WebLink ;\r\nMailPriority Priority ;\r\nIEnumerable<string> Categories ;\r\nstring InternetMessageId ;\r\nAttachmentCollection Attachments ;\r\nstring Body ;\r\nstring BodyAsHtml ;\r\nstring Subject ;\r\nstring FromDisplayName ;\r\nstring FromAddress ;\r\nstring SenderDisplayName ;\r\nstring SenderAddress ;\r\nMailAddressCollection ReplyToList ;\r\nIEnumerable<string> ReplyToAddressList ;\r\nMailAddressCollection To ;\r\nIEnumerable<string> ToAddressList ;\r\nMailAddressCollection CC ;\r\nIEnumerable<string> CCAddressList ;\r\nMailAddressCollection Bcc ;\r\nIEnumerable<string> BccAddressList ;\r\nint StandardAttachmentCount ;\r\nIEnumerable<string> StandardAttachmentNames ;\r\nint InlineAttachmentCount ;\r\nIEnumerable<string> InlineAttachmentNames ;\r\nint AttachmentCount ;\r\nIEnumerable<string> AttachmentsNamesList ;\r\nList<AttachmentInfoSlim> AttachmentsInfoList ;\r\nMailAddress? From ;\r\nMailAddress? Sender ;\r\nMailAddress? ReplyTo ;\r\nDeliveryNotificationOptions DeliveryNotificationOptions ;\r\nEncoding? SubjectEncoding ;\r\nNameValueCollection Headers ;\r\nEncoding? HeadersEncoding ;\r\nEncoding? BodyEncoding ;\r\nTransferEncoding BodyTransferEncoding ;\r\nbool IsBodyHtml ;\r\nAlternateViewCollection AlternateViews ;}"
    MailAddressCollection:
      name: MailAddressCollection
      type: class
      description: ''
      type_params:
        MailAddress: object
      params: {}
      text: "class MailAddressCollection : Collection<MailAddress>\r\n{}"
    MailAddress:
      name: MailAddress
      type: class
      description: ''
      type_params: {}
      params:
        DisplayName:
          name: DisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        User:
          name: User
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Host:
          name: Host
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Address:
          name: Address
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
      text: "class MailAddress\r\n{\r\nstring DisplayName ;\r\nstring User ;\r\nstring Host ;\r\nstring Address ;}"
    AttachmentInfoSlim:
      name: AttachmentInfoSlim
      type: class
      description: ''
      type_params: {}
      params:
        Filename:
          name: Filename
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        MimeType:
          name: MimeType
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Size:
          name: Size
          type: long?
          ctype: long?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - long
          - '?'
      text: "class AttachmentInfoSlim\r\n{\r\nstring Filename ;\r\nstring MimeType ;\r\nlong? Size ;}"
    MailPriority:
      name: MailPriority
      type: enum
      description: ''
      type_params: {}
      params:
        enumerator:
          name: values
          type: string
          modifier: private
          category: Property
          components:
          - string
          description: ''
          required: true
          values:
          - Normal
          - Low
          - High
      text: "enum MailPriority\r\n{\r\nNormal,Low,High}"
    DeliveryNotificationOptions:
      name: DeliveryNotificationOptions
      type: enum
      description: ''
      type_params: {}
      params:
        enumerator:
          name: values
          type: string
          modifier: private
          category: Property
          components:
          - string
          description: ''
          required: true
          values:
          - None
          - OnSuccess
          - OnFailure
          - Delay
          - Never
      text: "enum DeliveryNotificationOptions\r\n{\r\nNone,OnSuccess,OnFailure,Delay,Never}"
    AttachmentCollection:
      name: AttachmentCollection
      type: class
      description: ''
      type_params:
        Attachment: object
      params: {}
      text: "sealed class AttachmentCollection : Collection<Attachment>, IDisposable\r\n{}"
    Attachment:
      name: Attachment
      type: class
      description: ''
      type_params: {}
      params:
        Name:
          name: Name
          type: string?
          ctype: string?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
          - '?'
        NameEncoding:
          name: NameEncoding
          type: Encoding?
          ctype: Encoding?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
          - '?'
        ContentDisposition:
          name: ContentDisposition
          type: ContentDisposition?
          ctype: ContentDisposition?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - ContentDisposition
          - '?'
        ContentStream:
          name: ContentStream
          type: Stream
          ctype: Stream
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Stream
        ContentId:
          name: ContentId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ContentType:
          name: ContentType
          type: ContentType
          ctype: ContentType
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - ContentType
        TransferEncoding:
          name: TransferEncoding
          type: TransferEncoding
          ctype: TransferEncoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - TransferEncoding
      text: "class Attachment\r\n{\r\nstring? Name ;\r\nEncoding? NameEncoding ;\r\nContentDisposition? ContentDisposition ;\r\nStream ContentStream ;\r\nstring ContentId ;\r\nContentType ContentType ;\r\nTransferEncoding TransferEncoding ;}"
    AlternateViewCollection:
      name: AlternateViewCollection
      type: class
      description: ''
      type_params: {}
      params: {}
      text: "sealed class AlternateViewCollection\r\n{}"
    AlternateView:
      name: AlternateView
      type: class
      description: ''
      type_params: {}
      params:
        LinkedResources:
          name: LinkedResources
          type: LinkedResourceCollection
          ctype: LinkedResourceCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - LinkedResourceCollection
        BaseUri:
          name: BaseUri
          type: Uri?
          ctype: Uri?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Uri
          - '?'
        ContentStream:
          name: ContentStream
          type: Stream
          ctype: Stream
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Stream
        ContentId:
          name: ContentId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ContentType:
          name: ContentType
          type: ContentType
          ctype: ContentType
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - ContentType
        TransferEncoding:
          name: TransferEncoding
          type: TransferEncoding
          ctype: TransferEncoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - TransferEncoding
      text: "class AlternateView\r\n{\r\nLinkedResourceCollection LinkedResources ;\r\nUri? BaseUri ;\r\nStream ContentStream ;\r\nstring ContentId ;\r\nContentType ContentType ;\r\nTransferEncoding TransferEncoding ;}"
    FollowupFlag:
      name: FollowupFlag
      type: class
      description: ''
      type_params: {}
      params:
        CompletedDateTime:
          name: CompletedDateTime
          type: DateTimeTimeZone
          ctype: DateTimeTimeZone
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTimeTimeZone
        DueDateTime:
          name: DueDateTime
          type: DateTimeTimeZone
          ctype: DateTimeTimeZone
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTimeTimeZone
        FlagStatus:
          name: FlagStatus
          type: FollowupFlagStatus?
          ctype: FollowupFlagStatus?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - FollowupFlagStatus
          - '?'
        StartDateTime:
          name: StartDateTime
          type: DateTimeTimeZone
          ctype: DateTimeTimeZone
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTimeTimeZone
        AdditionalData:
          name: AdditionalData
          type: IDictionary<string, object>
          ctype: IDictionary<string, object>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IDictionary
          - string
          - object
        ODataType:
          name: ODataType
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
      text: "class FollowupFlag\r\n{\r\nDateTimeTimeZone CompletedDateTime ;\r\nDateTimeTimeZone DueDateTime ;\r\nFollowupFlagStatus? FlagStatus ;\r\nDateTimeTimeZone StartDateTime ;\r\nIDictionary<string, object> AdditionalData ;\r\nstring ODataType ;}"
    MessageImportance:
      name: MessageImportance
      type: record struct
      description: ''
      type_params: {}
      params:
        Low:
          name: Low
          type: MessageImportance
          ctype: MessageImportance
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MessageImportance
        Normal:
          name: Normal
          type: MessageImportance
          ctype: MessageImportance
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MessageImportance
        High:
          name: High
          type: MessageImportance
          ctype: MessageImportance
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MessageImportance
      text: "[DataContract]\r\nreadonly record struct MessageImportance\r\n{\r\nMessageImportance Low ;\r\nMessageImportance Normal ;\r\nMessageImportance High ;}"
configure activity request:
  input: "enum LogLevel\r\n{\r\nFatal,\r\nError,\r\nWarn,\r\nInfo,\r\nTrace,\r\n}\r\nclass Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nList<AttachmentInfoSlim> AttachmentsInfoList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;\r\n}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,\r\n}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;\r\n}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,\r\n}\r\nclass AttachmentInfoSlim\r\n{\r\nstring Filename;\r\nstring MimeType;\r\nlong? Size;\r\n}"
  expected:
    LogLevel:
      name: LogLevel
      type: enum
      description: ''
      type_params: {}
      params:
        enumerator:
          name: values
          type: string
          modifier: private
          category: Property
          components:
          - string
          description: ''
          required: true
          values:
          - Fatal
          - Error
          - Warn
          - Info
          - Trace
      text: "enum LogLevel\r\n{\r\nFatal,\r\nError,\r\nWarn,\r\nInfo,\r\nTrace,}"
    Office365Message:
      name: Office365Message
      type: class
      description: ''
      type_params: {}
      params:
        MessageId:
          name: MessageId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        BodyPreview:
          name: BodyPreview
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Flag:
          name: Flag
          type: FollowupFlag
          ctype: FollowupFlag
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - FollowupFlag
        Importance:
          name: Importance
          type: MessageImportance?
          ctype: MessageImportance?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MessageImportance
          - '?'
        IsRead:
          name: IsRead
          type: bool
          ctype: bool
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - bool
        LastModifiedDateTime:
          name: LastModifiedDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        ParentFolderId:
          name: ParentFolderId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ReceivedDateTime:
          name: ReceivedDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        SentDateTime:
          name: SentDateTime
          type: DateTime?
          ctype: DateTime?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DateTime
          - '?'
        WebLink:
          name: WebLink
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Priority:
          name: Priority
          type: MailPriority
          ctype: MailPriority
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailPriority
        Categories:
          name: Categories
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        InternetMessageId:
          name: InternetMessageId
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Attachments:
          name: Attachments
          type: AttachmentCollection
          ctype: AttachmentCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - AttachmentCollection
        Body:
          name: Body
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        BodyAsHtml:
          name: BodyAsHtml
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Subject:
          name: Subject
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        FromDisplayName:
          name: FromDisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        FromAddress:
          name: FromAddress
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        SenderDisplayName:
          name: SenderDisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        SenderAddress:
          name: SenderAddress
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        ReplyToList:
          name: ReplyToList
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        ReplyToAddressList:
          name: ReplyToAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        To:
          name: To
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        ToAddressList:
          name: ToAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        CC:
          name: CC
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        CCAddressList:
          name: CCAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        Bcc:
          name: Bcc
          type: MailAddressCollection
          ctype: MailAddressCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddressCollection
        BccAddressList:
          name: BccAddressList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        StandardAttachmentCount:
          name: StandardAttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        StandardAttachmentNames:
          name: StandardAttachmentNames
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        InlineAttachmentCount:
          name: InlineAttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        InlineAttachmentNames:
          name: InlineAttachmentNames
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        AttachmentCount:
          name: AttachmentCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        AttachmentsNamesList:
          name: AttachmentsNamesList
          type: IEnumerable<string>
          ctype: IEnumerable<string>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IEnumerable
          - string
        AttachmentsInfoList:
          name: AttachmentsInfoList
          type: List<AttachmentInfoSlim>
          ctype: List<AttachmentInfoSlim>
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - List
          - AttachmentInfoSlim
        From:
          name: From
          type: MailAddress
          ctype: MailAddress
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
        Sender:
          name: Sender
          type: MailAddress
          ctype: MailAddress
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
        ReplyTo:
          name: ReplyTo
          type: MailAddress
          ctype: MailAddress
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - MailAddress
        DeliveryNotificationOptions:
          name: DeliveryNotificationOptions
          type: DeliveryNotificationOptions
          ctype: DeliveryNotificationOptions
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DeliveryNotificationOptions
        SubjectEncoding:
          name: SubjectEncoding
          type: Encoding
          ctype: Encoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
        Headers:
          name: Headers
          type: NameValueCollection
          ctype: NameValueCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - NameValueCollection
        HeadersEncoding:
          name: HeadersEncoding
          type: Encoding
          ctype: Encoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
        BodyEncoding:
          name: BodyEncoding
          type: Encoding
          ctype: Encoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - Encoding
        BodyTransferEncoding:
          name: BodyTransferEncoding
          type: TransferEncoding
          ctype: TransferEncoding
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - TransferEncoding
        IsBodyHtml:
          name: IsBodyHtml
          type: bool
          ctype: bool
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - bool
        AlternateViews:
          name: AlternateViews
          type: AlternateViewCollection
          ctype: AlternateViewCollection
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - AlternateViewCollection
      text: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nList<AttachmentInfoSlim> AttachmentsInfoList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}"
    MailPriority:
      name: MailPriority
      type: enum
      description: ''
      type_params: {}
      params:
        enumerator:
          name: values
          type: string
          modifier: private
          category: Property
          components:
          - string
          description: ''
          required: true
          values:
          - Normal
          - Low
          - High
      text: "enum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}"
    MailAddress:
      name: MailAddress
      type: class
      description: ''
      type_params: {}
      params:
        DisplayName:
          name: DisplayName
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        User:
          name: User
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Host:
          name: Host
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Address:
          name: Address
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
      text: "class MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}"
    DeliveryNotificationOptions:
      name: DeliveryNotificationOptions
      type: enum
      description: ''
      type_params: {}
      params:
        enumerator:
          name: values
          type: string
          modifier: private
          category: Property
          components:
          - string
          description: ''
          required: true
          values:
          - None
          - OnSuccess
          - OnFailure
          - Delay
          - Never
      text: "enum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}"
    AttachmentInfoSlim:
      name: AttachmentInfoSlim
      type: class
      description: ''
      type_params: {}
      params:
        Filename:
          name: Filename
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        MimeType:
          name: MimeType
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Size:
          name: Size
          type: long?
          ctype: long?
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - long
          - '?'
      text: "class AttachmentInfoSlim\r\n{\r\nstring Filename;\r\nstring MimeType;\r\nlong? Size;}"
typedefs with accessor overload:
  input: "[Browsable (false)]\r\ninterface IPresentationQuickHandle\r\n{\r\nISlideIndexer Slide;\r\n}\r\ninterface ISlideIndexer\r\n{\r\nISlideQuickHandle this [int index];\r\nint Count;\r\n}\r\ninterface ISlideQuickHandle\r\n{\r\nstring Layout;\r\nIShapeIndexer Shape;\r\nint ShapeCount;\r\n}\r\ninterface IShapeIndexer\r\n{\r\nIShapeQuickHandle this [string name];\r\n}\r\ninterface IShapeQuickHandle\r\n{\r\nstring Text;\r\nfloat FontSize;\r\nint ZIndex;\r\nDataTable Table;\r\n}\r\n"
  expected:
    IPresentationQuickHandle:
      name: IPresentationQuickHandle
      type: interface
      description: ''
      type_params: {}
      params:
        Slide:
          name: Slide
          type: ISlideIndexer
          ctype: ISlideIndexer
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - ISlideIndexer
      text: "[Browsable (false)]\r\ninterface IPresentationQuickHandle\r\n{\r\nISlideIndexer Slide;}"
    ISlideIndexer:
      name: ISlideIndexer
      type: interface
      description: ''
      type_params: {}
      params:
        '[int index]':
          name: '[int index]'
          type: ISlideQuickHandle
          ctype: ISlideQuickHandle
          modifier: public
          description: ''
          required: false
          category: Accessor
          components:
          - ISlideQuickHandle
        Count:
          name: Count
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
      text: "interface ISlideIndexer\r\n{\r\nISlideQuickHandle this [int index];\r\nint Count;}"
    ISlideQuickHandle:
      name: ISlideQuickHandle
      type: interface
      description: ''
      type_params: {}
      params:
        Layout:
          name: Layout
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        Shape:
          name: Shape
          type: IShapeIndexer
          ctype: IShapeIndexer
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - IShapeIndexer
        ShapeCount:
          name: ShapeCount
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
      text: "interface ISlideQuickHandle\r\n{\r\nstring Layout;\r\nIShapeIndexer Shape;\r\nint ShapeCount;}"
    IShapeIndexer:
      name: IShapeIndexer
      type: interface
      description: ''
      type_params: {}
      params:
        '[string name]':
          name: '[string name]'
          type: IShapeQuickHandle
          ctype: IShapeQuickHandle
          modifier: public
          description: ''
          required: false
          category: Accessor
          components:
          - IShapeQuickHandle
      text: "interface IShapeIndexer\r\n{\r\nIShapeQuickHandle this [string name];}"
    IShapeQuickHandle:
      name: IShapeQuickHandle
      type: interface
      description: ''
      type_params: {}
      params:
        Text:
          name: Text
          type: string
          ctype: string
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - string
        FontSize:
          name: FontSize
          type: float
          ctype: float
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - float
        ZIndex:
          name: ZIndex
          type: int
          ctype: int
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - int
        Table:
          name: Table
          type: DataTable
          ctype: DataTable
          modifier: public
          description: ''
          required: false
          category: Property
          components:
          - DataTable
      text: "interface IShapeQuickHandle\r\n{\r\nstring Text;\r\nfloat FontSize;\r\nint ZIndex;\r\nDataTable Table;}"
