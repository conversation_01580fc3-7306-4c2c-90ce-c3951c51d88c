﻿empty typedef:
  input: "class Empty : Activity\r\n{\r\n}\r\n"
  expected:
    name: Empty
    type: class
    description: ''
    type_params: {}
    params: {}
    text: "class Empty : Activity\r\n{\r\n}\r\n"
class typedef with required arguments:
  input: "class GCPRunScriptOnInstance : Activity\r\n{\r\n[RequiredArgument]\r\nInArgument<GCPInstance> Instance;\r\n[RequiredArgument]\r\nInArgument<string> Script;\r\n[RequiredArgument]\r\nInArgument<string> ScriptKey;\r\nOutArgument<string> CommandOutput;\r\n}\r\n"
  expected:
    name: GCPRunScriptOnInstance
    type: class
    description: ''
    type_params: {}
    params:
      Instance:
        name: Instance
        type: InArgument<GCPInstance>
        ctype: GCPInstance
        modifier: public
        description: ''
        required: true
        category: InArgument
        components:
        - InArgument
        - GCPInstance
      Script:
        name: Script
        type: InArgument<string>
        ctype: string
        modifier: public
        description: ''
        required: true
        category: InArgument
        components:
        - InArgument
        - string
      ScriptKey:
        name: ScriptKey
        type: InArgument<string>
        ctype: string
        modifier: public
        description: ''
        required: true
        category: InArgument
        components:
        - InArgument
        - string
      CommandOutput:
        name: CommandOutput
        type: OutArgument<string>
        ctype: string
        modifier: public
        description: ''
        required: false
        category: OutArgument
        components:
        - OutArgument
        - string
    text: "class GCPRunScriptOnInstance : Activity\r\n{\r\n[RequiredArgument]\r\nInArgument<GCPInstance> Instance;\r\n[RequiredArgument]\r\nInArgument<string> Script;\r\n[RequiredArgument]\r\nInArgument<string> ScriptKey;\r\nOutArgument<string> CommandOutput;\r\n}\r\n"
class type definition with description:
  input: "// Assigns an expression resulting in type T to a variable of type T\r\nsealed class Assign<T> : Activity\r\n{\r\n\t[RequiredArgument]\r\n\tOutArgument<T> To ;\r\n\t[RequiredArgument]\r\n\tInArgument<T> Value ;\r\n}"
  expected:
    name: Assign
    type: class
    description: Assigns an expression resulting in type T to a variable of type T
    type_params:
      T: object
    params:
      To:
        name: To
        type: OutArgument<T>
        ctype: T
        modifier: public
        description: ''
        required: true
        category: OutArgument
        components:
        - OutArgument
        - T
      Value:
        name: Value
        type: InArgument<T>
        ctype: T
        modifier: public
        description: ''
        required: true
        category: InArgument
        components:
        - InArgument
        - T
    text: "// Assigns an expression resulting in type T to a variable of type T\r\nsealed class Assign<T> : Activity\r\n{\r\n\t[RequiredArgument]\r\n\tOutArgument<T> To ;\r\n\t[RequiredArgument]\r\n\tInArgument<T> Value ;\r\n}"
class type definition with unconstrained type parameter:
  input: "class BuildCollection<T> : Activity\r\n{\r\n[RequiredArgument]\r\nInArgument<T> FirstItem;\r\nOutArgument<System.Collections.Generic.List<T>> Result;\r\n}\r\n"
  expected:
    name: BuildCollection
    type: class
    description: ''
    type_params:
      T: object
    params:
      FirstItem:
        name: FirstItem
        type: InArgument<T>
        ctype: T
        modifier: public
        description: ''
        required: true
        category: InArgument
        components:
        - InArgument
        - T
      Result:
        name: Result
        type: OutArgument<System.Collections.Generic.List<T>>
        ctype: System.Collections.Generic.List<T>
        modifier: public
        description: ''
        required: false
        category: OutArgument
        components:
        - OutArgument
        - System.Collections.Generic.List
        - T
    text: "class BuildCollection<T> : Activity\r\n{\r\n[RequiredArgument]\r\nInArgument<T> FirstItem;\r\nOutArgument<System.Collections.Generic.List<T>> Result;\r\n}\r\n"
class type definition with constrained type parameter:
  input: "class WaitForValidationAction<T> : Activity where T : ExtendedExtractionResultsForDocumentData\r\n{\r\nOutArgument<IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>> ValidatedExtractionResults;\r\n}\r\n"
  expected:
    name: WaitForValidationAction
    type: class
    description: ''
    type_params:
      T: ExtendedExtractionResultsForDocumentData
    params:
      ValidatedExtractionResults:
        name: ValidatedExtractionResults
        type: OutArgument<IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>>
        ctype: IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>
        modifier: public
        description: ''
        required: false
        category: OutArgument
        components:
        - OutArgument
        - IDocumentData
        - UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData
    text: "class WaitForValidationAction<T> : Activity where T : ExtendedExtractionResultsForDocumentData\r\n{\r\nOutArgument<IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>> ValidatedExtractionResults;\r\n}\r\n"
enum type definition:
  input: "enum InstanceStatus\r\n{\r\nProvisioning,Staging,Running,Stopping,Suspending,Suspended,Repairing,Terminated}"
  expected:
    name: InstanceStatus
    type: enum
    description: ''
    type_params: {}
    params:
      enumerator:
        name: values
        type: string
        modifier: private
        category: Property
        components:
        - string
        description: ''
        required: true
        values:
        - Provisioning
        - Staging
        - Running
        - Stopping
        - Suspending
        - Suspended
        - Repairing
        - Terminated
    text: "enum InstanceStatus\r\n{\r\nProvisioning,Staging,Running,Stopping,Suspending,Suspended,Repairing,Terminated}"
interface type definition:
  input: "interface ISheetRef\r\n{\r\nstring Name;\r\nstring Worksheet;\r\nstring Address;\r\nstring FirstCell;\r\nstring FullRangeName;\r\nint RowCount;}"
  expected:
    name: ISheetRef
    type: interface
    description: ''
    type_params: {}
    params:
      Name:
        name: Name
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      Worksheet:
        name: Worksheet
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      Address:
        name: Address
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      FirstCell:
        name: FirstCell
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      FullRangeName:
        name: FullRangeName
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      RowCount:
        name: RowCount
        type: int
        ctype: int
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - int
    text: "interface ISheetRef\r\n{\r\nstring Name;\r\nstring Worksheet;\r\nstring Address;\r\nstring FirstCell;\r\nstring FullRangeName;\r\nint RowCount;}"
interface type definition with attributes:
  input: "[ExtensionMethod (typeof(ResourceExtensions), \"GetExtension\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"GetName\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"GetSizeInBytes\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"WithContext\")]\r\n[FeatureRequirement (\"ResourceHandlingV1\")]\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;\r\nstring GetExtension();\r\nstring GetName();\r\nlong GetSizeInBytes();}"
  expected:
    name: IResource
    type: interface
    description: ''
    type_params: {}
    params:
      MimeType:
        name: MimeType
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      IconUri:
        name: IconUri
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      FullName:
        name: FullName
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      ID:
        name: ID
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      IsFolder:
        name: IsFolder
        type: bool
        ctype: bool
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - bool
      CreationDate:
        name: CreationDate
        type: DateTime?
        ctype: DateTime?
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - DateTime
        - '?'
      LastModifiedDate:
        name: LastModifiedDate
        type: DateTime?
        ctype: DateTime?
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - DateTime
        - '?'
      Metadata:
        name: Metadata
        type: Dictionary<string, string>
        ctype: Dictionary<string, string>
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - Dictionary
        - string
        - string
      GetExtension():
        name: GetExtension()
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      GetName():
        name: GetName()
        type: string
        ctype: string
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
      GetSizeInBytes():
        name: GetSizeInBytes()
        type: long
        ctype: long
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - long
    text: "[ExtensionMethod (typeof(ResourceExtensions), \"GetExtension\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"GetName\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"GetSizeInBytes\")]\r\n[ExtensionMethod (typeof(ResourceExtensions), \"WithContext\")]\r\n[FeatureRequirement (\"ResourceHandlingV1\")]\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;\r\nstring GetExtension();\r\nstring GetName();\r\nlong GetSizeInBytes();}"
record type definition:
  input: "sealed record FormSelector\r\n{\r\nFormSourceId? SourceId;\r\nstring? InstanceName;\r\nRuntimeId? RuntimeId;}"
  expected:
    name: FormSelector
    type: record
    description: ''
    type_params: {}
    params:
      SourceId:
        name: SourceId
        type: FormSourceId?
        ctype: FormSourceId?
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - FormSourceId
        - '?'
      InstanceName:
        name: InstanceName
        type: string?
        ctype: string?
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - string
        - '?'
      RuntimeId:
        name: RuntimeId
        type: RuntimeId?
        ctype: RuntimeId?
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - RuntimeId
        - '?'
    text: "sealed record FormSelector\r\n{\r\nFormSourceId? SourceId;\r\nstring? InstanceName;\r\nRuntimeId? RuntimeId;}"
record struct type definition:
  input: "[DataContract]\r\nrecord struct MessageImportance\r\n{\r\nstatic MessageImportance Low;\r\nstatic MessageImportance Normal;\r\nstatic MessageImportance High;}"
  expected:
    name: MessageImportance
    type: record struct
    description: ''
    type_params: {}
    params:
      Low:
        name: Low
        type: MessageImportance
        ctype: MessageImportance
        modifier: static public
        description: ''
        required: false
        category: Property
        components:
        - MessageImportance
      Normal:
        name: Normal
        type: MessageImportance
        ctype: MessageImportance
        modifier: static public
        description: ''
        required: false
        category: Property
        components:
        - MessageImportance
      High:
        name: High
        type: MessageImportance
        ctype: MessageImportance
        modifier: static public
        description: ''
        required: false
        category: Property
        components:
        - MessageImportance
    text: "[DataContract]\r\nrecord struct MessageImportance\r\n{\r\nstatic MessageImportance Low;\r\nstatic MessageImportance Normal;\r\nstatic MessageImportance High;}"
struct type definition:
  input: "struct Point\r\n{\r\nint X;\r\nint Y;\r\n}"
  expected:
    name: Point
    type: struct
    description: ''
    type_params: {}
    params:
      X:
        name: X
        type: int
        ctype: int
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - int
      Y:
        name: Y
        type: int
        ctype: int
        modifier: public
        description: ''
        required: false
        category: Property
        components:
        - int
    text: "struct Point\r\n{\r\nint X;\r\nint Y;\r\n}"
configure activity request:
  input: "class LogMessage : Activity\r\n{\r\nInArgument<object> Message;\r\nInArgument<LogLevel> Level;\r\n}"
  expected:
    name: LogMessage
    type: class
    description: ''
    type_params: {}
    params:
      Message:
        name: Message
        type: InArgument<object>
        ctype: object
        modifier: public
        description: ''
        required: false
        category: InArgument
        components:
        - InArgument
        - object
      Level:
        name: Level
        type: InArgument<LogLevel>
        ctype: LogLevel
        modifier: public
        description: ''
        required: false
        category: InArgument
        components:
        - InArgument
        - LogLevel
    text: "class LogMessage : Activity\r\n{\r\nInArgument<object> Message;\r\nInArgument<LogLevel> Level;\r\n}"
