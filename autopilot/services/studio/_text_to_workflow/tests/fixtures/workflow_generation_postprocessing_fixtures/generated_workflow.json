[{"workflowValidDict": {"processName": "NewRowAddedToGoogleSheet", "trigger": {"thought": "When a new row is added to the Google Sheet \"Test Spreadsheet\"", "activity": "UiPath.GSuite.Activities.Sheets.Triggers.RowAddedToSheetBottom<System.Data.DataRow>", "params": {"BrowserItemFriendlyName": "Test Spreadsheet", "SheetName": "[[She<PERSON><PERSON><PERSON>]]", "HasHeaders": "[[<PERSON><PERSON><PERSON><PERSON>]]", "AddedRow": "[[AddedRow]]", "Spreadsheet": "[[Spreadsheet]]", "JobData": "[[Job<PERSON><PERSON>]]"}}, "workflow": [{"thought": "Log a message \"New row added to Test Spreadsheet\"", "activity": "UiPath.Core.Activities.LogMessage", "params": {"Message": "New row added to Test Spreadsheet"}}]}, "expectedVariables": {"AddedRow": "System.Data.DataRow", "Spreadsheet": "GDriveRemoteItem", "JobData": "JobInformation"}}, {"workflowValidDict": {"processName": "AssignAndWriteList", "trigger": {"thought": "Manual Trigger", "activity": "UiPath.Core.Activities.ManualTrigger", "params": {}}, "workflow": [{"thought": "Assign List to Variable", "activity": "UiPath.Core.Activities.Assign<System.Collections.Generic.List<System.String>>", "params": {"To": "[[myList]]", "Value": "[[new List(Of String)() From {\"item1\", \"item2\", \"item3\"}]]"}}, {"thought": "For Each item in List", "activity": "UiPath.Core.Activities.ForEach<System.String>", "params": {"Values": "[[myList]]", "Body": {"variables": [{"name": "currentItem", "type": "String"}], "Handler": [{"thought": "Write Line", "activity": "System.Activities.Statements.WriteLine", "params": {"Text": "[[currentItem]]"}}]}}}]}, "expectedVariables": {"myList": "System.Collections.Generic.List<System.String>"}}]