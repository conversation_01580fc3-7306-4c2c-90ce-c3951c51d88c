{"used_trigger_and_activities": ["UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity@e76d8ea0-27ef-317f-be59-8cec8537e87d", "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity@c536bd6c-c015-3471-af5d-d2059c868e4c", "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@bf418c13-5947-3050-9b7c-f9fc327f06bd", "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@7bed1117-a116-319c-b272-a74ac1660bfd"], "jit_types": {"UiPath.IntegrationService.Activities.Runtime.Activities.QuickBooks_OnlineInvoice_Created": [{"ProviderName": "CustomEntities", "BundleOptions": {"Hash": "H8Be+1OAmMO/UXP19KQZl7y260m/Pe9IxcLqEUiluzw46YLIjImnH7X/yyoZG+FajenaIwDNw1pRaHAf/LqFrA==", "BundleName": "CD311E5C524_invoice_Trigger", "EntitiesBundle": {"Types": {"TaxClassificationRef": {"Name": "TaxClassificationRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ClassRef": {"Name": "ClassRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxCodeRef": {"Name": "TaxCodeRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MarkUpIncomeAccountRef": {"Name": "MarkUpIncomeAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "PriceLevelRef": {"Name": "PriceLevelRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MarkupInfo": {"Name": "MarkupInfo", "Properties": [{"Name": "MarkUpIncomeAccountRef", "Type": {"DefinitionName": "MarkUpIncomeAccountRef"}}, {"Name": "PriceLevelRef", "Type": {"DefinitionName": "PriceLevelRef"}}, {"Name": "Percent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "ItemAccountRef": {"Name": "ItemAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ItemRef": {"Name": "ItemRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "SalesItemLineDetail": {"Name": "SalesItemLineDetail", "Properties": [{"Name": "TaxClassificationRef", "Type": {"DefinitionName": "TaxClassificationRef"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "MarkupInfo", "Type": {"DefinitionName": "MarkupInfo"}}, {"Name": "ItemAccountRef", "Type": {"DefinitionName": "ItemAccountRef"}}, {"Name": "ItemRef", "Type": {"DefinitionName": "ItemRef"}}, {"Name": "UnitPrice", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "ServiceDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "DiscountAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TaxInclusiveAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DiscountRate", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Qty", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "Line1": {"Name": "Line1", "Properties": [{"Name": "SalesItemLineDetail", "Type": {"DefinitionName": "SalesItemLineDetail"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "LineNum", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "GroupItemRef": {"Name": "GroupItemRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "GroupLineDetail": {"Name": "GroupLineDetail", "Properties": [{"Name": "Line", "Type": {"DefinitionName": "Line1"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "GroupItemRef", "Type": {"DefinitionName": "GroupItemRef"}}, {"Name": "Quantity", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "DiscountAccountRef": {"Name": "DiscountAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DiscountLineDetail": {"Name": "DiscountLineDetail", "Properties": [{"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "DiscountAccountRef", "Type": {"DefinitionName": "DiscountAccountRef"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "DiscountPercent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "PercentBased", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "SubtotalLineDetail": {"Name": "SubtotalLineDetail", "Properties": [{"Name": "ItemRef", "Type": {"DefinitionName": "ItemRef"}}], "BaseType": null}, "DescriptionLineDetail": {"Name": "DescriptionLineDetail", "Properties": [{"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "ServiceDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}], "BaseType": null}, "Line": {"Name": "Line", "Properties": [{"Name": "GroupLineDetail", "Type": {"DefinitionName": "GroupLineDetail"}}, {"Name": "SalesItemLineDetail", "Type": {"DefinitionName": "SalesItemLineDetail"}}, {"Name": "DiscountLineDetail", "Type": {"DefinitionName": "DiscountLineDetail"}}, {"Name": "SubtotalLineDetail", "Type": {"DefinitionName": "SubtotalLineDetail"}}, {"Name": "DescriptionLineDetail", "Type": {"DefinitionName": "DescriptionLineDetail"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "LineNum", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "ShipFromAddr": {"Name": "ShipFromAddr", "Properties": [{"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DepositToAccountRef": {"Name": "DepositToAccountRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillAddr": {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Properties": [{"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ShipAddr": {"Name": "ShipAddr", "Properties": [{"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "LinkedTxn": {"Name": "LinkedTxn", "Properties": [{"Name": "TxnType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnId", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnLineId", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxRateRef": {"Name": "TaxRateRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxLineDetail": {"Name": "TaxLineDetail", "Properties": [{"Name": "TaxRateRef", "Type": {"DefinitionName": "TaxRateRef"}}, {"Name": "TaxPercent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "PercentBased", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "OverrideDeltaAmount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TaxInclusiveAmount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "NetAmountTaxable", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "TaxLine": {"Name": "TaxLine", "Properties": [{"Name": "TaxLineDetail", "Type": {"DefinitionName": "TaxLineDetail"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TxnTaxCodeRef": {"Name": "TxnTaxCodeRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TxnTaxDetail": {"Name": "TxnTaxDetail", "Properties": [{"Name": "TaxLine", "Type": {"DefinitionName": "TaxLine"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "TxnTaxCodeRef", "Type": {"DefinitionName": "TxnTaxCodeRef"}}, {"Name": "TotalTax", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "CustomerRef": {"Name": "CustomerRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "RecurDataRef": {"Name": "RecurDataRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmail": {"Name": "Bill<PERSON>mail", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "CurrencyRef": {"Name": "CurrencyRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmailBcc": {"Name": "BillEmailBcc", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxExemptionRef": {"Name": "TaxExemptionRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MetaData": {"Name": "MetaData", "Properties": [{"Name": "CreateTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "LastUpdatedTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}], "BaseType": null}, "DepartmentRef": {"Name": "DepartmentRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DeliveryInfo": {"Name": "DeliveryInfo", "Properties": [{"Name": "DeliveryTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "DeliveryType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmailCc": {"Name": "BillEmailCc", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "CustomerMemo": {"Name": "CustomerMemo", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ShipMethodRef": {"Name": "ShipMethodRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "SalesTermRef": {"Name": "SalesTermRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "invoice_Trigger": {"Name": "invoice_Trigger", "Properties": [{"Name": "Line", "Type": {"DefinitionName": "Line"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "ShipFromAddr", "Type": {"DefinitionName": "ShipFromAddr"}}, {"Name": "DepositToAccountRef", "Type": {"DefinitionName": "DepositToAccountRef"}}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": {"DefinitionName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Name": "ShipAddr", "Type": {"DefinitionName": "ShipAddr"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "LinkedTxn", "Type": {"DefinitionName": "LinkedTxn"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "TxnTaxDetail", "Type": {"DefinitionName": "TxnTaxDetail"}}, {"Name": "CustomerRef", "Type": {"DefinitionName": "CustomerRef"}}, {"Name": "RecurDataRef", "Type": {"DefinitionName": "RecurDataRef"}}, {"Name": "Bill<PERSON>mail", "Type": {"DefinitionName": "Bill<PERSON>mail"}}, {"Name": "CurrencyRef", "Type": {"DefinitionName": "CurrencyRef"}}, {"Name": "BillEmailBcc", "Type": {"DefinitionName": "BillEmailBcc"}}, {"Name": "TaxExemptionRef", "Type": {"DefinitionName": "TaxExemptionRef"}}, {"Name": "MetaData", "Type": {"DefinitionName": "MetaData"}}, {"Name": "DepartmentRef", "Type": {"DefinitionName": "DepartmentRef"}}, {"Name": "DeliveryInfo", "Type": {"DefinitionName": "DeliveryInfo"}}, {"Name": "BillEmailCc", "Type": {"DefinitionName": "BillEmailCc"}}, {"Name": "CustomerMemo", "Type": {"DefinitionName": "CustomerMemo"}}, {"Name": "ShipMethodRef", "Type": {"DefinitionName": "ShipMethodRef"}}, {"Name": "SalesTermRef", "Type": {"DefinitionName": "SalesTermRef"}}, {"Name": "EmailStatus", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "AllowOnlineACHPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DocNumber", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PrivateNote", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "ShipDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "TotalAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "domain", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TrackingNum", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PrintStatus", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "FreeFormAddress", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "GlobalTaxCalculation", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "HomeTotalAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "InvoiceLink", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "SyncToken", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "sparse", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Balance", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "ExchangeRate", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "AllowIPNPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TransactionLocationType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "DueDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "AllowOnlinePayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TxnSource", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "HomeBalance", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "AllowOnlineCreditCardPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "ApplyTaxAfterDiscount", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}}, "Version": 1}}, "EntityAssemblyKey": {"Name": "UiPath.IntegrationService.Activities.SWEntities.CD311E5C524_invoice_Trigger", "Hash": "SDhCZSsxT0FtTU8vVVhQMTlLUVpsN3kyNjBtL1BlOUl4Y0xxRVVpbHV6dzQ2WUxJakltbkg3WC95eW9aRytGYWplbmFJd0ROdzFwUmFIQWYvTHFGckE9PQ", "AssemblyName": "CD311E5C524_invoice_.uCIyu2n0ipI13ozRy2LVivM2"}}], "UiPath.IntegrationService.Activities.Runtime.Activities.ExpensifyReimburse_Report": [{"ProviderName": "CustomEntities", "BundleOptions": {"Hash": "pII0iO6mhHazIsanhS34xKlgGcNRDPEdZWyZxb73uqnB6u5+HNbWKL9iVeYUyFa9CIHZVs7xL9WOEzjo8vKHNg==", "BundleName": "CF977E92267_reports_status_reimbursed_Update", "EntitiesBundle": {"Types": {"skippedReports": {"Name": "skippedReports", "Properties": [{"Name": "reportID", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "reason", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "failedReports": {"Name": "failedReports", "Properties": [{"Name": "reason", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "reportID", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "reports_status_reimbursed_Update": {"Name": "reports_status_reimbursed_Update", "Properties": [{"Name": "skippedReports", "Type": {"DefinitionName": "skippedReports"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "failedReports", "Type": {"DefinitionName": "failedReports"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "reportIDs", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}], "BaseType": null}}, "Version": 1}}, "EntityAssemblyKey": {"Name": "UiPath.IntegrationService.Activities.SWEntities.CF977E92267_reports_status_reimbursed_Update", "Hash": "cElJMGlPNm1oSGF6SXNhbmhTMzR4S2xnR2NOUkRQRWRaV3laeGI3M3VxbkI2dTUrSE5iV0tMOWlWZVlVeUZhOUNJSFpWczd4TDlXT0V6am84dktITmc9PQ", "AssemblyName": "CF977E92267_reports_.juHPh176J3K1aI54HFm65v1"}}], "UiPath.IntegrationService.Activities.Runtime.Activities.QuickBooks_OnlineInvoice_Updated": [{"ProviderName": "CustomEntities", "BundleOptions": {"Hash": "H8Be+1OAmMO/UXP19KQZl7y260m/Pe9IxcLqEUiluzw46YLIjImnH7X/yyoZG+FajenaIwDNw1pRaHAf/LqFrA==", "BundleName": "CD311E5C524_invoice_Trigger", "EntitiesBundle": {"Types": {"TaxClassificationRef": {"Name": "TaxClassificationRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ClassRef": {"Name": "ClassRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxCodeRef": {"Name": "TaxCodeRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MarkUpIncomeAccountRef": {"Name": "MarkUpIncomeAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "PriceLevelRef": {"Name": "PriceLevelRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MarkupInfo": {"Name": "MarkupInfo", "Properties": [{"Name": "MarkUpIncomeAccountRef", "Type": {"DefinitionName": "MarkUpIncomeAccountRef"}}, {"Name": "PriceLevelRef", "Type": {"DefinitionName": "PriceLevelRef"}}, {"Name": "Percent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "ItemAccountRef": {"Name": "ItemAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ItemRef": {"Name": "ItemRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "SalesItemLineDetail": {"Name": "SalesItemLineDetail", "Properties": [{"Name": "TaxClassificationRef", "Type": {"DefinitionName": "TaxClassificationRef"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "MarkupInfo", "Type": {"DefinitionName": "MarkupInfo"}}, {"Name": "ItemAccountRef", "Type": {"DefinitionName": "ItemAccountRef"}}, {"Name": "ItemRef", "Type": {"DefinitionName": "ItemRef"}}, {"Name": "UnitPrice", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "ServiceDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "DiscountAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TaxInclusiveAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DiscountRate", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Qty", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "Line1": {"Name": "Line1", "Properties": [{"Name": "SalesItemLineDetail", "Type": {"DefinitionName": "SalesItemLineDetail"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "LineNum", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "GroupItemRef": {"Name": "GroupItemRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "GroupLineDetail": {"Name": "GroupLineDetail", "Properties": [{"Name": "Line", "Type": {"DefinitionName": "Line1"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "GroupItemRef", "Type": {"DefinitionName": "GroupItemRef"}}, {"Name": "Quantity", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "DiscountAccountRef": {"Name": "DiscountAccountRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DiscountLineDetail": {"Name": "DiscountLineDetail", "Properties": [{"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "DiscountAccountRef", "Type": {"DefinitionName": "DiscountAccountRef"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "DiscountPercent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "PercentBased", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "SubtotalLineDetail": {"Name": "SubtotalLineDetail", "Properties": [{"Name": "ItemRef", "Type": {"DefinitionName": "ItemRef"}}], "BaseType": null}, "DescriptionLineDetail": {"Name": "DescriptionLineDetail", "Properties": [{"Name": "TaxCodeRef", "Type": {"DefinitionName": "TaxCodeRef"}}, {"Name": "ServiceDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}], "BaseType": null}, "Line": {"Name": "Line", "Properties": [{"Name": "GroupLineDetail", "Type": {"DefinitionName": "GroupLineDetail"}}, {"Name": "SalesItemLineDetail", "Type": {"DefinitionName": "SalesItemLineDetail"}}, {"Name": "DiscountLineDetail", "Type": {"DefinitionName": "DiscountLineDetail"}}, {"Name": "SubtotalLineDetail", "Type": {"DefinitionName": "SubtotalLineDetail"}}, {"Name": "DescriptionLineDetail", "Type": {"DefinitionName": "DescriptionLineDetail"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "LineNum", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "ShipFromAddr": {"Name": "ShipFromAddr", "Properties": [{"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DepositToAccountRef": {"Name": "DepositToAccountRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillAddr": {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Properties": [{"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ShipAddr": {"Name": "ShipAddr", "Properties": [{"Name": "Line3", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line1", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line2", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Line5", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "CountrySubDivisionCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "City", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_Long", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"Long\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "Lat", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PostalCode", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "LinkedTxn": {"Name": "LinkedTxn", "Properties": [{"Name": "TxnType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnId", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnLineId", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxRateRef": {"Name": "TaxRateRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxLineDetail": {"Name": "TaxLineDetail", "Properties": [{"Name": "TaxRateRef", "Type": {"DefinitionName": "TaxRateRef"}}, {"Name": "TaxPercent", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "PercentBased", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "OverrideDeltaAmount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TaxInclusiveAmount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "NetAmountTaxable", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "TaxLine": {"Name": "TaxLine", "Properties": [{"Name": "TaxLineDetail", "Type": {"DefinitionName": "TaxLineDetail"}}, {"Name": "Amount", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DetailType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TxnTaxCodeRef": {"Name": "TxnTaxCodeRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TxnTaxDetail": {"Name": "TxnTaxDetail", "Properties": [{"Name": "TaxLine", "Type": {"DefinitionName": "TaxLine"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "TxnTaxCodeRef", "Type": {"DefinitionName": "TxnTaxCodeRef"}}, {"Name": "TotalTax", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "CustomerRef": {"Name": "CustomerRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "RecurDataRef": {"Name": "RecurDataRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmail": {"Name": "Bill<PERSON>mail", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "CurrencyRef": {"Name": "CurrencyRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmailBcc": {"Name": "BillEmailBcc", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "TaxExemptionRef": {"Name": "TaxExemptionRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "MetaData": {"Name": "MetaData", "Properties": [{"Name": "CreateTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "LastUpdatedTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}], "BaseType": null}, "DepartmentRef": {"Name": "DepartmentRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "DeliveryInfo": {"Name": "DeliveryInfo", "Properties": [{"Name": "DeliveryTime", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd'T'HH:mm:ss.SSSXXX\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "DeliveryType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "BillEmailCc": {"Name": "BillEmailCc", "Properties": [{"Name": "Address", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "CustomerMemo": {"Name": "CustomerMemo", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "ShipMethodRef": {"Name": "ShipMethodRef", "Properties": [{"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "SalesTermRef": {"Name": "SalesTermRef", "Properties": [{"Name": "value", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "name", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "invoice_Trigger": {"Name": "invoice_Trigger", "Properties": [{"Name": "Line", "Type": {"DefinitionName": "Line"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "ShipFromAddr", "Type": {"DefinitionName": "ShipFromAddr"}}, {"Name": "DepositToAccountRef", "Type": {"DefinitionName": "DepositToAccountRef"}}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": {"DefinitionName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Name": "ShipAddr", "Type": {"DefinitionName": "ShipAddr"}}, {"Name": "ClassRef", "Type": {"DefinitionName": "ClassRef"}}, {"Name": "LinkedTxn", "Type": {"DefinitionName": "LinkedTxn"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "TxnTaxDetail", "Type": {"DefinitionName": "TxnTaxDetail"}}, {"Name": "CustomerRef", "Type": {"DefinitionName": "CustomerRef"}}, {"Name": "RecurDataRef", "Type": {"DefinitionName": "RecurDataRef"}}, {"Name": "Bill<PERSON>mail", "Type": {"DefinitionName": "Bill<PERSON>mail"}}, {"Name": "CurrencyRef", "Type": {"DefinitionName": "CurrencyRef"}}, {"Name": "BillEmailBcc", "Type": {"DefinitionName": "BillEmailBcc"}}, {"Name": "TaxExemptionRef", "Type": {"DefinitionName": "TaxExemptionRef"}}, {"Name": "MetaData", "Type": {"DefinitionName": "MetaData"}}, {"Name": "DepartmentRef", "Type": {"DefinitionName": "DepartmentRef"}}, {"Name": "DeliveryInfo", "Type": {"DefinitionName": "DeliveryInfo"}}, {"Name": "BillEmailCc", "Type": {"DefinitionName": "BillEmailCc"}}, {"Name": "CustomerMemo", "Type": {"DefinitionName": "CustomerMemo"}}, {"Name": "ShipMethodRef", "Type": {"DefinitionName": "ShipMethodRef"}}, {"Name": "SalesTermRef", "Type": {"DefinitionName": "SalesTermRef"}}, {"Name": "EmailStatus", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "AllowOnlineACHPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "DocNumber", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PrivateNote", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "ShipDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "TotalAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "domain", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "Id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TrackingNum", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "PrintStatus", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "TxnDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}]}}, {"Name": "FreeFormAddress", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "GlobalTaxCalculation", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "HomeTotalAmt", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "InvoiceLink", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "SyncToken", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "sparse", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "Balance", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "ExchangeRate", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "AllowIPNPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TransactionLocationType", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "DueDate", "Type": {"ClrType": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib]], System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonConverterAttribute, Newtonsoft.Json"}, "ConstructorArguments": [{"Value": "{\n  \"ClrType\": \"UiPath.IntegrationService.Activities.Runtime.Helpers.DateTimeOffsetMaskDeserializer, UiPath.IntegrationService.Activities.Runtime\"\n}", "Type": {"ClrType": "System.Activities.DesignViewModels.EntityTypeReference, System.Activities.ViewModels"}}, {"Value": "\"yyyy-MM-dd\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.CategoryAttribute, System.ComponentModel.Primitives"}, "ConstructorArguments": [{"Value": "\"Simplified\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "NamedArguments": []}, {"Type": {"ClrType": "System.ComponentModel.DataAnnotations.DisplayAttribute, System.ComponentModel.Annotations"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "Order", "Value": {"Value": "1", "Type": {"ClrType": "System.Int32, System.Private.CoreLib"}}}]}]}}, {"Name": "AllowOnlinePayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "TxnSource", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "HomeBalance", "Type": {"ClrType": "System.Nullable`1[[System.Double, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "AllowOnlineCreditCardPayment", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "ApplyTaxAfterDiscount", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}}, "Version": 1}}, "EntityAssemblyKey": {"Name": "UiPath.IntegrationService.Activities.SWEntities.CD311E5C524_invoice_Trigger", "Hash": "SDhCZSsxT0FtTU8vVVhQMTlLUVpsN3kyNjBtL1BlOUl4Y0xxRVVpbHV6dzQ2WUxJakltbkg3WC95eW9aRytGYWplbmFJd0ROdzFwUmFIQWYvTHFGckE9PQ", "AssemblyName": "CD311E5C524_invoice_.uCIyu2n0ipI13ozRy2LVivM2"}}], "UiPath.IntegrationService.Activities.Runtime.Activities.StripeSearch_Payments_by_Status": [{"ProviderName": "CustomEntities", "BundleOptions": {"Hash": "BD9uOk6AeH+2yoD/35xdwDrWjG8Ndb/uDtBXtPErtIHTKEUTEYuKFtJD5U2YmRs/TxCkIbt+xpTE2ziRRyjjAQ==", "BundleName": "C5505505AF8_searchpaymentsbystatus_List", "EntitiesBundle": {"Types": {"card1": {"Name": "card1", "Properties": [{"Name": "last4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "exp_year", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "three_d_secure", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "funding", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "address_zip_check", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "fingerprint", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "exp_month", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "brand", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "address": {"Name": "address", "Properties": [{"Name": "postal_code", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "owner": {"Name": "owner", "Properties": [{"Name": "address", "Type": {"DefinitionName": "address"}}], "BaseType": null}, "source": {"Name": "source", "Properties": [{"Name": "card", "Type": {"DefinitionName": "card1"}}, {"Name": "owner", "Type": {"DefinitionName": "owner"}}, {"Name": "created", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "usage", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "status", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "client_secret", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_object", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"object\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "customer", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "livemode", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "type", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "flow", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "outcome": {"Name": "outcome", "Properties": [{"Name": "type", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "risk_score", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "risk_level", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "network_status", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "seller_message", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "checks": {"Name": "checks", "Properties": [{"Name": "address_postal_code_check", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "card2": {"Name": "card2", "Properties": [{"Name": "checks", "Type": {"DefinitionName": "checks"}}, {"Name": "fingerprint", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "exp_year", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "brand", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "country", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "funding", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "exp_month", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "network", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "last4", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "payment_method_details": {"Name": "payment_method_details", "Properties": [{"Name": "card", "Type": {"DefinitionName": "card2"}}, {"Name": "type", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "refunds": {"Name": "refunds", "Properties": [{"Name": "url", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_object", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"object\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "total_count", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "has_more", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}], "BaseType": null}, "billing_details": {"Name": "billing_details", "Properties": [{"Name": "address", "Type": {"DefinitionName": "address"}}], "BaseType": null}, "data": {"Name": "data", "Properties": [{"Name": "source", "Type": {"DefinitionName": "source"}}, {"Name": "outcome", "Type": {"DefinitionName": "outcome"}}, {"Name": "payment_method_details", "Type": {"DefinitionName": "payment_method_details"}}, {"Name": "refunds", "Type": {"DefinitionName": "refunds"}}, {"Name": "billing_details", "Type": {"DefinitionName": "billing_details"}}, {"Name": "livemode", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "paid", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "amount", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "balance_transaction", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "payment_intent", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "disputed", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "calculated_statement_descriptor", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "receipt_url", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "customer", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "statement_descriptor", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "currency", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "payment_method", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "refunded", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "amount_refunded", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "created", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "captured", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "amount_captured", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "status", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_object", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"object\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}], "BaseType": null}, "charges": {"Name": "charges", "Properties": [{"Name": "data", "Type": {"DefinitionName": "data"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "total_count", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "url", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "has_more", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "_object", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"object\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}], "BaseType": null}, "card": {"Name": "card", "Properties": [{"Name": "request_three_d_secure", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}, "payment_method_options": {"Name": "payment_method_options", "Properties": [{"Name": "card", "Type": {"DefinitionName": "card"}}], "BaseType": null}, "searchpaymentsbystatus_List": {"Name": "searchpaymentsbystatus_List", "Properties": [{"Name": "charges", "Type": {"DefinitionName": "charges"}}, {"Name": "payment_method_options", "Type": {"DefinitionName": "payment_method_options"}}, {"Name": "livemode", "Type": {"ClrType": "System.Nullable`1[[System<PERSON>Boolean, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "source", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "statement_descriptor", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "id", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "client_secret", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "capture_method", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "created", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "amount_received", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "confirmation_method", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "_object", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "Attributes": {"Values": [{"Type": {"ClrType": "Newtonsoft.Json.JsonPropertyAttribute, Newtonsoft.Json"}, "ConstructorArguments": [], "NamedArguments": [{"Name": "PropertyName", "Value": {"Value": "\"object\"", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}}]}]}}, {"Name": "status", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "amount_capturable", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "description", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "payment_method_types", "Type": {"ClrType": "System.String, System.Private.CoreLib"}, "IsCollection": true, "CollectionGenericType": {"ClrType": "System.Collections.Generic.List`1, System.Private.CoreLib"}}, {"Name": "currency", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "amount", "Type": {"ClrType": "System.Nullable`1[[System.Int64, System.Private.CoreLib]], System.Private.CoreLib"}}, {"Name": "setup_future_usage", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}, {"Name": "customer", "Type": {"ClrType": "System.String, System.Private.CoreLib"}}], "BaseType": null}}, "Version": 1}}, "EntityAssemblyKey": {"Name": "UiPath.IntegrationService.Activities.SWEntities.C5505505AF8_searchpaymentsbystatus_List", "Hash": "QkQ5dU9rNkFlSCsyeW9ELzM1eGR3RHJXakc4TmRiL3VEdEJYdFBFcnRJSFRLRVVURVl1S0Z0SkQ1VTJZbVJzL1R4Q2tJYnQreHBURTJ6aVJSeWpqQVE9PQ", "AssemblyName": "C5505505AF8_searchpa.enasS3xGx9I1WAXYS2BCdYa3"}}]}, "activities_config_map": {"UiPath.IntegrationService.Activities.Runtime.Activities.StripeSearch_Payments_by_Status": {"activityTypeId": "7bed1117-a116-319c-b272-a74ac1660bfd", "configuration": "H4sIAAAAAAAAA5VVbU/bMBD+K1Y+hywtIEElPpQORhmsYS1MaEKVm1xbj8QOfgE61P++s+OGsLaDqZVa3z13vpfnzi/BKYM8Uz3BNWUcZNB5CYZAZTqnkxwqZdD5eRcGfV4aXQtegm+0gKATPBiQiyAMPjNV5nThpUNNtVFWDCqVrNRMcBSP5kCU806cHVFaMj6LyBD+UuSUzwydAaE8IxrNcqY0EVOiTFkKqSHzwKmLiEyFJCVdFMA1YVzjj4rw+tGitOFU1+B5iKYDmdk8W2Fwwk3RzOU7PBgmQdWeCtBzkaHdDc2NRUiPGHvE2COW4bqTVPApkwV1uW9w8Ua/yQFNt5l6TcMokSIFpaosV/jyVbjJf4+W2kjYGJtXNcx6lKeQQ7Ma6UrUgA1NmgJkb3Cqli3vHCXYjFuiJUIxz4xEsoIikZbhKr4s6GhpIKwYeiFS6qFXnnF9demKn1CJV2vbU2vQiCVnBdNr3LxYSd9Qs0scmgju6IbMmIC0hBOTX5BqRbQgEyASsC4csog4NwRLQCRyFVCpnwA4aTnKtuI4rLmbwZSaHImpUP4OLdvvF+h/C4I1Hxi9cXrPmR5Xc+cJrSYLVc/u25GuxjPxo9F3Q7ZWxtfkrllC9TyyuJl0oQ5BPrIUoi6y9xHzAhUNf5xwXf3t7e/H9ts9PdgS0vgCd0B0bHiWQ/QPCG4rsslbxG9nV6P2w9VzdtbaP49vu3uHN9MBbYXkBqTCCI9aUWw/aI/9wgE44mC0pHlIEjPJWfoVFiNxD/yImzwPKq6C0kFnSnMF9qxKwRVs427VB9crLL3TrqDYPdzAKwcN1fIOe37NGW66zNYWl3VRokPcz/W9fY754zDWrVeWPj4rvNflhdfinufIZiExERQbho7mO5aHJfgfRPkGLXwnewb75wZ64GbBE2Irb860LismIuoLaMvZHk3ngJUQ96Z0a8EGWMuPpXhSIPsaCi/3dMcw/fOT0PQe34NmSgdRvINTR6N23N6LD9u7UavVzLF+i3xataLppB3txnVmH2P8x6owKEGu2m5ZuVaYk1Fg09JzPHza6ubCP4TVbF3S5wQPQ/YbBW3cMcEInvVxLtJ7V6flH+UqomTRBwAA", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "d3f4083a-eab2-4037-a53e-ca5505505af8", "connectorKey": "uipath-stripe-stripe", "activityIsConfigured": true, "dynamicActivityDetails": "{\"ActivityConfiguration\":\"H4sIAAAAAAAAA5VVbU/bMBD+K1Y+hywtIEElPpQORhmsYS1MaEKVm1xbj8QOfgE61P++s+OGsLaDqZVa3z13vpfnzi/BKYM8Uz3BNWUcZNB5CYZAZTqnkxwqZdD5eRcGfV4aXQtegm+0gKATPBiQiyAMPjNV5nThpUNNtVFWDCqVrNRMcBSP5kCU806cHVFaMj6LyBD+UuSUzwydAaE8IxrNcqY0EVOiTFkKqSHzwKmLiEyFJCVdFMA1YVzjj4rw+tGitOFU1+B5iKYDmdk8W2Fwwk3RzOU7PBgmQdWeCtBzkaHdDc2NRUiPGHvE2COW4bqTVPApkwV1uW9w8Ua/yQFNt5l6TcMokSIFpaosV/jyVbjJf4+W2kjYGJtXNcx6lKeQQ7Ma6UrUgA1NmgJkb3Cqli3vHCXYjFuiJUIxz4xEsoIikZbhKr4s6GhpIKwYeiFS6qFXnnF9demKn1CJV2vbU2vQiCVnBdNr3LxYSd9Qs0scmgju6IbMmIC0hBOTX5BqRbQgEyASsC4csog4NwRLQCRyFVCpnwA4aTnKtuI4rLmbwZSaHImpUP4OLdvvF+h/C4I1Hxi9cXrPmR5Xc+cJrSYLVc/u25GuxjPxo9F3Q7ZWxtfkrllC9TyyuJl0oQ5BPrIUoi6y9xHzAhUNf5xwXf3t7e/H9ts9PdgS0vgCd0B0bHiWQ/QPCG4rsslbxG9nV6P2w9VzdtbaP49vu3uHN9MBbYXkBqTCCI9aUWw/aI/9wgE44mC0pHlIEjPJWfoVFiNxD/yImzwPKq6C0kFnSnMF9qxKwRVs427VB9crLL3TrqDYPdzAKwcN1fIOe37NGW66zNYWl3VRokPcz/W9fY754zDWrVeWPj4rvNflhdfinufIZiExERQbho7mO5aHJfgfRPkGLXwnewb75wZ64GbBE2Irb860LismIuoLaMvZHk3ngJUQ96Z0a8EGWMuPpXhSIPsaCi/3dMcw/fOT0PQe34NmSgdRvINTR6N23N6LD9u7UavVzLF+i3xataLppB3txnVmH2P8x6owKEGu2m5ZuVaYk1Fg09JzPHza6ubCP4TVbF3S5wQPQ/YbBW3cMcEInvVxLtJ7V6flH+UqomTRBwAA\",\"Fields\":[{\"Id\":\"query\",\"FieldType\":0,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":true,\"VisibleByDefault\":true},{\"Id\":\"limit\",\"FieldType\":0,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":false,\"VisibleByDefault\":true},{\"Id\":\"Jit_searchpaymentsbystatus\",\"FieldType\":1,\"ClrType\":\"UiPath.IntegrationService.Activities.SWEntities.C5505505AF8_searchpaymentsbystatus_List.Bundle.searchpaymentsbystatus_List[], C5505505AF8_searchpa.nYgQT2qQxdH15J0YA49VfOa1, Version=*******, Culture=neutral, PublicKeyToken=null\",\"Required\":false,\"VisibleByDefault\":true}],\"Mappings\":{\"Connections\":[{\"ConnectorKey\":\"uipath-stripe-stripe\",\"ConnectionId\":\"d3f4083a-eab2-4037-a53e-ca5505505af8\",\"ConnectorKeyPropertyName\":null,\"ConnectionIdPropertyName\":\"ConnectionId\"}]},\"Arguments\":null,\"ManagedDynamicProperties\":[\"Arguments\",\"FieldObjects\"]}"}, "UiPath.IntegrationService.Activities.Runtime.Activities.ExpensifyReimburse_Report": {"activityTypeId": "bf418c13-5947-3050-9b7c-f9fc327f06bd", "configuration": "H4sIAAAAAAAAA81UW2/aMBT+K1aeNimkQC+sSH1ooQh6WVlpu4epqkxyIC6OndnHtBniv+/kAqUq3aZpD4OX2N+5+PvOZeH1BMjIdrRCLhQYr73wRsBNGPOxhBL02t/ufW+gUofri4X3mSfgtT0DqTY46F4Ii57vdYVNJc8q8LoA2aCbI2BDI1IUWhHS0UnCaxZSbjhCxCS5Mz1hZuVhGWrm0ojQgLxvsjQPaNEINS2jiSkFWix9yvLdCQOR10bjwC8pXeiQV7m+ODAZ+QzsJWCsoyHlTABzsrnD0l+TARV1KeEbHqcqYivgFY2ekBTHMu2QcSmr51tm3TgRmBPThoUGCo58QrYMY2BTMQfFcm4+e4pFGMOcEB2GzphcDE5ifBAqlM6S5ceA9bRJODKhWEa/WpLUomibLH9P3SI3uJX8KEf+Af0xTLSB/4s/9fWVw62NfSbwoeLzQOKgs3QUydgZS622RSSyeCPQyxNvxZBjHAwUwtQUzxuBmYsQguMQxVygABuMvp4qLD87vcNW6/Sw2Txovf+Mh9tyQE6ciiQEv7Pz2baowaPrD+NG6+Bs97zBB/t7/V5ysD9v+OyOaksPPWoE9fxP7k6iM3CkwKHh0mdDN5YiPIfsRs9AHSknpVcOJNA6aE+4tJCfbaqVhfcGtCxBUSZSvUBXplQ42kurABvQ8p4m/1aJiYAol5hWWJJSQNpa67wDRUqoENZVt/l6q1hR3oIXpaXtpyBEbYgIXTtBgeIaPKegrJhkL19kW1Urq8raccX+IuBq/EgxXm3F7Y3TR0zLbiRDaoow9ohKh9MUkCh65tI7Lkm/crmV9ydGP1kwA4Skuh8akXCT0Yur/Tzk4YxPYZPdp6BeGwPyoFlv7tUPm7tBo7FJd7XdNuitsddx9tYEty14y8r2Z9ebPP9YkKsUzKoVykZ9o9LxTafv5RwxpuNO7MZ2J+VZAgp3fhX6gqupI1XKUbzkz0M6jMQPutit1+s0nfCMJ1KHs0LF5U+/ribjEAcAAA==", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "3341d1d5-62bf-43df-acd1-05f977e92267", "connectorKey": "uipath-expensify-expensify", "activityIsConfigured": true, "dynamicActivityDetails": "{\"ActivityConfiguration\":\"H4sIAAAAAAAAA81UW2/aMBT+K1aeNimkQC+sSH1ooQh6WVlpu4epqkxyIC6OndnHtBniv+/kAqUq3aZpD4OX2N+5+PvOZeH1BMjIdrRCLhQYr73wRsBNGPOxhBL02t/ufW+gUofri4X3mSfgtT0DqTY46F4Ii57vdYVNJc8q8LoA2aCbI2BDI1IUWhHS0UnCaxZSbjhCxCS5Mz1hZuVhGWrm0ojQgLxvsjQPaNEINS2jiSkFWix9yvLdCQOR10bjwC8pXeiQV7m+ODAZ+QzsJWCsoyHlTABzsrnD0l+TARV1KeEbHqcqYivgFY2ekBTHMu2QcSmr51tm3TgRmBPThoUGCo58QrYMY2BTMQfFcm4+e4pFGMOcEB2GzphcDE5ifBAqlM6S5ceA9bRJODKhWEa/WpLUomibLH9P3SI3uJX8KEf+Af0xTLSB/4s/9fWVw62NfSbwoeLzQOKgs3QUydgZS622RSSyeCPQyxNvxZBjHAwUwtQUzxuBmYsQguMQxVygABuMvp4qLD87vcNW6/Sw2Txovf+Mh9tyQE6ciiQEv7Pz2baowaPrD+NG6+Bs97zBB/t7/V5ysD9v+OyOaksPPWoE9fxP7k6iM3CkwKHh0mdDN5YiPIfsRs9AHSknpVcOJNA6aE+4tJCfbaqVhfcGtCxBUSZSvUBXplQ42kurABvQ8p4m/1aJiYAol5hWWJJSQNpa67wDRUqoENZVt/l6q1hR3oIXpaXtpyBEbYgIXTtBgeIaPKegrJhkL19kW1Urq8raccX+IuBq/EgxXm3F7Y3TR0zLbiRDaoow9ohKh9MUkCh65tI7Lkm/crmV9ydGP1kwA4Skuh8akXCT0Yur/Tzk4YxPYZPdp6BeGwPyoFlv7tUPm7tBo7FJd7XdNuitsddx9tYEty14y8r2Z9ebPP9YkKsUzKoVykZ9o9LxTafv5RwxpuNO7MZ2J+VZAgp3fhX6gqupI1XKUbzkz0M6jMQPutit1+s0nfCMJ1KHs0LF5U+/ribjEAcAAA==\",\"Fields\":[{\"Id\":\"reportIDList\",\"FieldType\":0,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":true,\"VisibleByDefault\":true},{\"Id\":\"endDate\",\"FieldType\":0,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":false,\"VisibleByDefault\":false},{\"Id\":\"startDate\",\"FieldType\":0,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":false,\"VisibleByDefault\":false},{\"Id\":\"Jit_reports_status_reimbursed\",\"FieldType\":1,\"ClrType\":\"UiPath.IntegrationService.Activities.SWEntities.CF977E92267_reports_status_reimbursed_Update.Bundle.reports_status_reimbursed_Update, CF977E92267_reports_.juHPh176J3K1aI54HFm65v1, Version=*******, Culture=neutral, PublicKeyToken=null\",\"Required\":false,\"VisibleByDefault\":true}],\"Mappings\":{\"Connections\":[{\"ConnectorKey\":\"uipath-expensify-expensify\",\"ConnectionId\":\"3341d1d5-62bf-43df-acd1-05f977e92267\",\"ConnectorKeyPropertyName\":null,\"ConnectionIdPropertyName\":\"ConnectionId\"}]},\"Arguments\":null,\"ManagedDynamicProperties\":[\"Arguments\",\"FieldObjects\"]}"}, "UiPath.IntegrationService.Activities.Runtime.Activities.QuickBooks_OnlineInvoice_Updated": {"activityTypeId": "c536bd6c-c015-3471-af5d-d2059c868e4c", "configuration": "H4sIAAAAAAAAA61V21LiQBD9lVRefAkpQV2UKh8UsMwqwhq8bG1Z1iRpYZbJTJwLLmX579tDhosC1uoKL9Dd033O6e6ZZ/+EAstUU3BNKAfpN5799hi4Lu1+49ezf0E0HUN/UoDf8DNIaU6YH/hdmdn4aoABuXUdE0Z4CuhqUVUwMlmxuxzc5AkeDfwTIXOibVZhEmYjItU0kmjIFiD8hpYGXoK3QDBK0xzWIemAJi2iSdiUgGH9Muw1qtKVeTbPAprSkvLBK2jor7hCHaJGaJrgp9LpVLJsq791etrI84ZSYRzHt7e3H+Iwr7bCADNI4OnkEh5CTtbBdwFroH9NfaVFDvKd+mWA57yfB+EasAKhZaBVul5XRvs/NW1dv75InXZOKIs10UatwJv6PDVzfl6XhA7eWba+0IQd5Xql/tThkVwYrv9z5e7QyQuzuAvQ0DV62fI8wxPxsaBrln/JDiqVtNBUcLQvoF3RHtHDMOIaBogD3THIMZ4Jj1KUg2oKuFw3ba7Ln83WTrXa3mvu1XbvaZn9vi/pYAAyPDY8YxC+MQfeujNh9HOQnO10vo0PTqp7w/QmrvH4aTgMvGuQCmEcVsNt+8Xjhmkj4ZCD0ZKwwOuhdjQ9g0lfjIAfcsNsky7h0YBCcR8IUxCUd+u5SInjXGo3lfw7XVbaWjp4/16CKgRXsDIWy1J6UWuTys61Qeh4ojTkYTwdxcBzf3uSjrH7YVNIOKfJgv3+v7Kvp5Du72X1BEi9frAPH1NiE++7l8BvijzBVyk7oUyDbP8pJCg1pxVx3DJ8WHpEogoYoOzr5eBjyLR9vs3COaRaSESMZkMLnLcK5dpQXXk0NB0lQoyU4AxrYbwbu4mTzW2HmyW7i8lvTOeUp/P5nq5PtwA5IxldXHejZvv+qtc66rdbs5COyOzBnmDMXgmWJkmHgPKIkSmuCUPlkMjcfizFkwIZYbOcHVuWEzlBOm4peyQdkQEsU8fuVRJ8BsPadm13+6C2E1ary1o4+D8s/WNL3+vO+M9jlvPVw5059U0bvkGYc8IHBuHZrr38BTEZwn1yCAAA", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity", "connectionId": "865039bb-60bf-4811-b91d-3bd311e5c524", "connectorKey": "uipath-intuit-quickbooksonline", "activityIsConfigured": true, "dynamicActivityDetails": "{\"ActivityConfiguration\":\"H4sIAAAAAAAAA61V21LiQBD9lVRefAkpQV2UKh8UsMwqwhq8bG1Z1iRpYZbJTJwLLmX579tDhosC1uoKL9Dd033O6e6ZZ/+EAstUU3BNKAfpN5799hi4Lu1+49ezf0E0HUN/UoDf8DNIaU6YH/hdmdn4aoABuXUdE0Z4CuhqUVUwMlmxuxzc5AkeDfwTIXOibVZhEmYjItU0kmjIFiD8hpYGXoK3QDBK0xzWIemAJi2iSdiUgGH9Muw1qtKVeTbPAprSkvLBK2jor7hCHaJGaJrgp9LpVLJsq791etrI84ZSYRzHt7e3H+Iwr7bCADNI4OnkEh5CTtbBdwFroH9NfaVFDvKd+mWA57yfB+EasAKhZaBVul5XRvs/NW1dv75InXZOKIs10UatwJv6PDVzfl6XhA7eWba+0IQd5Xql/tThkVwYrv9z5e7QyQuzuAvQ0DV62fI8wxPxsaBrln/JDiqVtNBUcLQvoF3RHtHDMOIaBogD3THIMZ4Jj1KUg2oKuFw3ba7Ln83WTrXa3mvu1XbvaZn9vi/pYAAyPDY8YxC+MQfeujNh9HOQnO10vo0PTqp7w/QmrvH4aTgMvGuQCmEcVsNt+8Xjhmkj4ZCD0ZKwwOuhdjQ9g0lfjIAfcsNsky7h0YBCcR8IUxCUd+u5SInjXGo3lfw7XVbaWjp4/16CKgRXsDIWy1J6UWuTys61Qeh4ojTkYTwdxcBzf3uSjrH7YVNIOKfJgv3+v7Kvp5Du72X1BEi9frAPH1NiE++7l8BvijzBVyk7oUyDbP8pJCg1pxVx3DJ8WHpEogoYoOzr5eBjyLR9vs3COaRaSESMZkMLnLcK5dpQXXk0NB0lQoyU4AxrYbwbu4mTzW2HmyW7i8lvTOeUp/P5nq5PtwA5IxldXHejZvv+qtc66rdbs5COyOzBnmDMXgmWJkmHgPKIkSmuCUPlkMjcfizFkwIZYbOcHVuWEzlBOm4peyQdkQEsU8fuVRJ8BsPadm13+6C2E1ary1o4+D8s/WNL3+vO+M9jlvPVw5059U0bvkGYc8IHBuHZrr38BTEZwn1yCAAA\",\"Fields\":[{\"Id\":\"Invoice\",\"FieldType\":1,\"ClrType\":\"UiPath.IntegrationService.Activities.SWEntities.CD311E5C524_invoice_Trigger.Bundle.invoice_Trigger, CD311E5C524_invoice_.uCIyu2n0ipI13ozRy2LVivM2, Version=*******, Culture=neutral, PublicKeyToken=null\",\"Required\":false,\"VisibleByDefault\":true},{\"Id\":\"out_Invoice_20_ID\",\"FieldType\":1,\"ClrType\":\"System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e\",\"Required\":false,\"VisibleByDefault\":true}],\"Mappings\":{\"Connections\":[{\"ConnectorKey\":\"uipath-intuit-quickbooksonline\",\"ConnectionId\":\"865039bb-60bf-4811-b91d-3bd311e5c524\",\"ConnectorKeyPropertyName\":null,\"ConnectionIdPropertyName\":\"ConnectionId\"}]},\"Arguments\":null,\"ManagedDynamicProperties\":[\"Arguments\",\"FieldObjects\"]}"}, "UiPath.IntegrationService.Activities.Runtime.Activities.QuickBooks_OnlineInvoice_Created": {"activityTypeId": "e76d8ea0-27ef-317f-be59-8cec8537e87d", "configuration": "{\"connectorKey\":\"uipath-intuit-quickbooksonline\",\"objectName\":\"invoice\",\"activityType\":\"CuratedTrigger\",\"version\":\"1.0.0\",\"eventOperation\":\"INVOICE_CREATED\",\"eventMode\":\"polling\",\"supportsStreaming\":false}", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity", "connectorKey": "uipath-intuit-quickbooksonline", "connectionId": null, "activityIsConfigured": false, "dynamicActivityDetails": "NotConfigured"}}, "expectedJitTypes": ["CD311E5C524_invoice_.uCIyu2n0ipI13ozRy2LVivM2", "CF977E92267_reports_.juHPh176J3K1aI54HFm65v1", "C5505505AF8_searchpa.enasS3xGx9I1WAXYS2BCdYa3"]}