edit_dap_from_input_arguments:
  request:
    messages:
    - role: user
      content: Configure this activity from the inputs.
    activityId: c581e4b3-7bfb-4f8e-bec2-68504a57a734
    expressionLanguage: jq
    workflow:
      document:
        dsl: 1.0.0
        name: Untitled
        version: 0.0.2
        namespace: default
      input:
        schema:
          format: json
          document:
            type: object
            required:
            - BambooUserId
            - StartDate
            - EndDate
            properties:
              BambooUserId:
                type: string
                default: '285'
              StartDate:
                type: string
                default: '2025-01-01'
              EndDate:
                type: string
                default: '2025-12-31'
      do:
      - Sequence_1:
          do:
          - c581e4b3-7bfb-4f8e-bec2-68504a57a734:
              call: UiPath.IntSvc
              with:
                connector: uipath-bamboo-bamboohr
                connectionId: 9dccba1b-2b6f-4a1e-9166-37982a45e4cc
                method: GET
                endpoint: /get_time_off_requests
                pathParameters: {}
                headers: {}
                queryParameters: {}
                bodyParameters: {}
              export:
                as: '$context + { outputs: ($context.outputs + { get_time_off_requests:  . }) }'
              metadata:
                icon:
                  inline: ''
                description: ''
                requiresConfiguration: false
                isTransparent: false
                activityColor: '#b4da55'
                isErrorActivity: false
                isAppActivity: false
                fullName: Connector
                assemblyQualifiedName: ConnectorActivity.Dummy.AssemblyQualifiedName
                activityType: Connector
                displayName: Get Time off Requests
                configuration: '{"objectActions":null,"primaryKeys":[],"connectorName":"BambooHR","connectorVersion":"1.8.1","objectDisplayName":"Get time off requests","objectName":"get_time_off_requests","httpMethod":"GET","path":"/get_time_off_requests","maxPageSize":200,"hasBreakingChanges":false,"customFieldsRequestDetails":null,"textBlocks":[],"jobArguments":null,"instanceParameters":{"connectorKey":"uipath-bamboo-bamboohr","objectName":"get_time_off_requests","httpMethod":"GET","activityType":"Curated","version":"1.0.0","supportsStreaming":false},"activitiesVersion":"1.0.0","cachedLookupValues":[["employeeId",null]],"cachedBrowserItems":[],"additionalHeaders":null,"fieldsContainer":{"inputFields":[{"sortOrder":1,"format":"date","mask":"yyyy-MM-dd","fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"start","displayName":"Start","description":"Only show time off that occurs on/after the specified start date","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"start","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":2,"format":"date","mask":"yyyy-MM-dd","fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"end","displayName":"End","description":"Only show time off that occurs on/before the specified end date","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"end","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":3,"lookup":{"objectName":"employees_directory","lookupNames":["firstName","lastName","id"],"lookupValue":"id","path":"/employees_directory"},"design":{"loadByDefault":true,"isMultiSelect":false,"enableUserOverride":false,"displayPattern":"{firstName} {lastName} - {id}","isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"employeeId","displayName":"Employee ID","description":"Select the employee from the dropdown or pass custom employee ID","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"employeeId","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":4,"lookup":{"objectName":"time_off_request_types","lookupNames":["name","id"],"lookupValue":"name","path":"/time_off_request_types"},"design":{"loadByDefault":true,"isMultiSelect":false,"enableUserOverride":false,"displayPattern":"{name} - {id}","isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"type","displayName":"Type","description":"Select the time off type from the dropdown","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"type","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":5,"enum":[{"name":"Approved","value":"approved"},{"name":"Denied","value":"denied"},{"name":"Superceded","value":"superceded"},{"name":"Requested","value":"requested"},{"name":"Canceled","value":"canceled"}],"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"timeOffStatus","displayName":"Status","description":"Select the status of the time-off from the dropdown","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"timeOffStatus","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":6,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"id","displayName":"Request ID","description":"A particular request ID to limit the response to.","type":"integer","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"id","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":7,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"action","displayName":"Action","description":"Limit to requests that the user has a particular level of access to. Legal values are: \"view\" or \"approve\". Defaults to view.","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"action","forceExpressionEditor":false,"disableExpressions":false},{"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"status","displayName":"Status","description":"A comma separated list of request status values to include. If omitted, requests of all status values are included. Legal values are \"approved\", \"denied\", \"superceded\", \"requested\", \"canceled\".","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"status","forceExpressionEditor":false,"disableExpressions":false}],"outputFields":[{"sortOrder":null,"format":null,"mask":null,"defaultValue":null,"lookup":null,"enum":null,"design":null,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"get_time_off_requests","displayName":"Get time off requests","description":"","type":"mock-output-type","primaryKey":false,"searchable":false,"required":false,"request":false,"response":true,"responseCurated":false,"fieldLocation":"output","isJitField":true,"isFieldActionParent":false,"isMainResponseField":true,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"get_time_off_requests","forceExpressionEditor":false,"disableExpressions":false}],"jitTypeNeedsRefresh":false,"jitInputField":null,"inputMode":null,"hasUnifiedTypes":false,"searchableFields":[],"jitTypeHash":null},"unifiedTypesCompatible":true,"operation":"List"}'
                uiPathActivityTypeId: 468d10a7-5d93-3cca-98fc-a319dda2b118
              then: c4ae3bbf-7dea-4e78-b59e-73530c3a8c66
          - c4ae3bbf-7dea-4e78-b59e-73530c3a8c66:
              call: UiPath.IntSvc
              with:
                connector: uipath-bamboo-bamboohr
                connectionId: 9dccba1b-2b6f-4a1e-9166-37982a45e4cc
                method: PUT
                endpoint: /update_time_off_status/{id}
                pathParameters:
                  id: ${"\($context.outputs.get_time_off_requests.content.at(-1).id)"}
                headers: {}
                queryParameters:
                  start_date: ${"\("\($context.outputs.get_time_off_requests.content.at(-1).start)")"}
                  end_date: ${"\($context.outputs.get_time_off_requests.content.at(-1).end)"}
                bodyParameters:
                  status: ${"cancelled"}
              export:
                as: '$context + { outputs: ($context.outputs + { update_time_off_status:  . }) }'
              metadata:
                icon:
                  inline: ''
                description: ''
                requiresConfiguration: false
                isTransparent: false
                activityColor: '#b4da55'
                isErrorActivity: false
                isAppActivity: false
                fullName: Connector
                assemblyQualifiedName: ConnectorActivity.Dummy.AssemblyQualifiedName
                activityType: Connector
                displayName: Change Time off Request Status
                configuration: '{"objectActions":null,"primaryKeys":[],"connectorName":"BambooHR","connectorVersion":"1.8.1","objectDisplayName":"Update time off status","objectName":"update_time_off_status","httpMethod":"PUT","path":"/update_time_off_status/{id}","maxPageSize":200,"hasBreakingChanges":false,"customFieldsRequestDetails":null,"textBlocks":[],"jobArguments":null,"instanceParameters":{"connectorKey":"uipath-bamboo-bamboohr","objectName":"update_time_off_status","httpMethod":"PUT","activityType":"Curated","version":"1.0.0","supportsStreaming":false},"activitiesVersion":"1.0.0","cachedLookupValues":[["id",null]],"cachedBrowserItems":[],"additionalHeaders":null,"fieldsContainer":{"inputFields":[{"sortOrder":1,"format":"date","mask":"yyyy-MM-dd","design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"start_date","displayName":"Start date","description":"Start date for the request to update","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"start_date","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":2,"format":"date","mask":"yyyy-MM-dd","design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"end_date","displayName":"End date","description":"End date for the request to update","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"end_date","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":3,"lookup":{"objectName":"get_time_off_requests","lookupNames":["name","id"],"lookupValue":"id","path":"/get_time_off_requests?start={start_date}&end={end_date}"},"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"fieldActions":[{"actionType":"hide","priorityOrder":1,"rules":[{"type":"field","refFieldName":"end_date","isCleared":true,"isVariable":false}]},{"actionType":"hide","priorityOrder":2,"rules":[{"type":"field","refFieldName":"start_date","isCleared":true,"isVariable":false}]},{"actionType":"show","priorityOrder":3,"rules":[{"type":"field","refFieldName":"start_date","refFieldValues":["*"],"isCleared":false,"isVariable":true},{"type":"field","refFieldName":"end_date","refFieldValues":["*"],"isCleared":false,"isVariable":true}]}],"position":"primary"},"fieldActions":[{"rules":[{"refFieldName":"start_date","isCleared":false,"isConstantValue":null,"isWorkflowExpression":false,"refFieldValues":["*"],"onRefFieldUpdate":true,"customRecordLevelFields":null}],"actionType":"clear","groupNames":null,"messages":null},{"rules":[{"refFieldName":"end_date","isCleared":false,"isConstantValue":null,"isWorkflowExpression":false,"refFieldValues":["*"],"onRefFieldUpdate":true,"customRecordLevelFields":null}],"actionType":"clear","groupNames":null,"messages":null},{"actionType":"hide","priorityOrder":1,"rules":[{"type":"field","refFieldName":"end_date","isCleared":true,"isVariable":false}]},{"actionType":"hide","priorityOrder":2,"rules":[{"type":"field","refFieldName":"start_date","isCleared":true,"isVariable":false}]},{"actionType":"show","priorityOrder":3,"rules":[{"type":"field","refFieldName":"start_date","refFieldValues":["*"],"isCleared":false,"isVariable":true},{"type":"field","refFieldName":"end_date","refFieldValues":["*"],"isCleared":false,"isVariable":true}]}],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"id","displayName":"Request ID","description":"Select the time off request from the dropdown or pass the request ID","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"path","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"id","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":null,"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"note","displayName":"Note","description":"Add a comment along with the status change","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"body","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"note","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":null,"enum":[{"name":"Approved","value":"approved"},{"name":"Cancelled","value":"cancelled"},{"name":"Denied","value":"denied"}],"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"status","displayName":"Status","description":"Select the status of the time off request","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"body","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"status","forceExpressionEditor":false,"disableExpressions":false}],"outputFields":[{"sortOrder":null,"format":null,"mask":null,"defaultValue":null,"lookup":null,"enum":null,"design":null,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"update_time_off_status","displayName":"Update time off status","description":"","type":"mock-output-type","primaryKey":false,"searchable":false,"required":false,"request":false,"response":true,"responseCurated":false,"fieldLocation":"output","isJitField":true,"isFieldActionParent":false,"isMainResponseField":true,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"update_time_off_status","forceExpressionEditor":false,"disableExpressions":false}],"jitTypeNeedsRefresh":false,"jitInputField":null,"inputMode":null,"hasUnifiedTypes":false,"searchableFields":[],"jitTypeHash":null},"unifiedTypesCompatible":true,"operation":"Replace"}'
                uiPathActivityTypeId: 93d7da58-c901-3e60-be55-14860918bafd
              then: exit
          metadata:
            icon:
              inline: ''
            description: ''
            requiresConfiguration: false
            isTransparent: true
            activityColor: '#526069'
            isErrorActivity: false
            isAppActivity: false
            helpURL: ''
            fullName: Sequence
            assemblyQualifiedName: Sequence
            activityType: Sequence
            displayName: Sequence
    schemas:
      c581e4b3-7bfb-4f8e-bec2-68504a57a734:
        endpoint: /get_time_off_requests
        method: GET
        definitions:
          actions:
            type: object
            properties:
              approve:
                type: boolean
              bypass:
                type: boolean
              cancel:
                type: boolean
              deny:
                type: boolean
              edit:
                type: boolean
              view:
                type: boolean
          amount:
            type: object
            properties:
              amount:
                type: string
              unit:
                type: string
          notes:
            type: object
            properties:
              manager:
                type: string
              employee:
                type: string
          status:
            type: object
            properties:
              lastChanged:
                type: string
                format: date-time
              lastChangedByUserId:
                type: string
              status:
                type: string
          type:
            type: object
            properties:
              icon:
                type: string
              id:
                type: string
              name:
                type: string
          get_time_off_requests_List:
            type: object
            properties:
              actions:
                $ref: '#/definitions/actions'
              amount:
                $ref: '#/definitions/amount'
              notes:
                $ref: '#/definitions/notes'
              status:
                $ref: '#/definitions/status'
              type:
                $ref: '#/definitions/type'
              created:
                type: string
                format: date-time
              employeeId:
                type: string
              end:
                type: string
                format: date-time
              id:
                type: string
              name:
                type: string
              start:
                type: string
                format: date-time
        properties:
          start:
            description: Only show time off that occurs on/after the specified start date
            location: Query
            direction: input
            type: string
            format: date-time
          end:
            description: Only show time off that occurs on/before the specified end date
            location: Query
            direction: input
            type: string
            format: date-time
          employeeId:
            description: Select the employee from the dropdown or pass custom employee ID
            location: Query
            direction: input
            type: string
          type:
            description: Select the time off type from the dropdown
            location: Query
            direction: input
            type: string
          timeOffStatus:
            description: Select the status of the time-off from the dropdown
            location: Query
            direction: input
            type: string
            oneOf:
            - description: Approved
              const: approved
            - description: Denied
              const: denied
            - description: Superceded
              const: superceded
            - description: Requested
              const: requested
            - description: Canceled
              const: canceled
          Jit_get_time_off_requests:
            direction: output
            type: array
            items:
              $ref: '#/definitions/get_time_off_requests_List'
        required:
        - start
        - end
      c4ae3bbf-7dea-4e78-b59e-73530c3a8c66:
        endpoint: /update_time_off_status/{id}
        method: PUT
        definitions:
          update_time_off_status_Replace:
            type: object
            properties:
              code:
                type: integer
              status:
                type: string
                description: Select the status of the time off request
        properties:
          start_date:
            description: Start date for the request to update
            location: Query
            direction: input
            type: string
            format: date-time
          end_date:
            description: End date for the request to update
            location: Query
            direction: input
            type: string
            format: date-time
          id:
            description: Select the time off request from the dropdown or pass the request ID
            location: Path
            direction: input
            type: string
          status:
            description: Select the status of the time off request
            location: Body
            direction: input
            type: string
            oneOf:
            - description: Approved
              const: approved
            - description: Cancelled
              const: cancelled
            - description: Denied
              const: denied
          note:
            description: Add a comment along with the status change
            location: Body
            direction: input
            type: string
          Jit_update_time_off_status:
            $ref: '#/definitions/update_time_off_status_Replace'
            direction: output
        required:
        - start_date
        - end_date
        - id
  expected:
    message: I'll configure the activity to use the workflow inputs for the start date, end date, and employee ID.
    argumentsChange:
      type: workflowArguments
      data: []
    propertiesChange:
      type: activityConfiguration
      activityId: get_time_off_requests
      data:
      - propertyName: with.start
        propertyOperation: update
        propertyValue:
        - ${$workflow.input.StartDate}
        propertyInfo: {}
        explanation: Set the start date from the workflow input.
      - propertyName: with.end
        propertyOperation: update
        propertyValue:
        - ${$workflow.input.EndDate}
      - propertyName: with.employeeId
        propertyOperation: update
        propertyValue:
        - ${$workflow.input.BambooUserId}
edit_dap_from_context_outputs:
  request:
    messages:
    - role: user
      content: Set the status to cancelled and the id, start and end from that of the last time off request.
    activityId: c4ae3bbf-7dea-4e78-b59e-73530c3a8c66
    expressionLanguage: jq
    workflow:
      document:
        dsl: 1.0.0
        name: Untitled
        version: 0.0.2
        namespace: default
      input:
        schema:
          format: json
          document:
            type: object
            required:
            - BambooUserId
            - StartDate
            - EndDate
            properties:
              BambooUserId:
                type: string
                default: '285'
              StartDate:
                type: string
                default: '2025-01-01'
              EndDate:
                type: string
                default: '2025-12-31'
      do:
      - Sequence_1:
          do:
          - c581e4b3-7bfb-4f8e-bec2-68504a57a734:
              call: UiPath.IntSvc
              with:
                connector: uipath-bamboo-bamboohr
                connectionId: 9dccba1b-2b6f-4a1e-9166-37982a45e4cc
                method: GET
                endpoint: /get_time_off_requests
                pathParameters: {}
                headers: {}
                queryParameters:
                  start: ${"\($workflow.input.StartDate)"}
                  end: ${$workflow.input.EndDate}
                  employeeId: ${"\($workflow.input.BambooUserId)"}
                bodyParameters: {}
              export:
                as: '$context + { outputs: ($context.outputs + { get_time_off_requests:  . }) }'
              metadata:
                icon:
                  inline: ''
                description: ''
                requiresConfiguration: false
                isTransparent: false
                activityColor: '#b4da55'
                isErrorActivity: false
                isAppActivity: false
                fullName: Connector
                assemblyQualifiedName: ConnectorActivity.Dummy.AssemblyQualifiedName
                activityType: Connector
                displayName: Get Time off Requests
                configuration: '{"objectActions":null,"primaryKeys":[],"connectorName":"BambooHR","connectorVersion":"1.8.1","objectDisplayName":"Get time off requests","objectName":"get_time_off_requests","httpMethod":"GET","path":"/get_time_off_requests","maxPageSize":200,"hasBreakingChanges":false,"customFieldsRequestDetails":null,"textBlocks":[],"jobArguments":null,"instanceParameters":{"connectorKey":"uipath-bamboo-bamboohr","objectName":"get_time_off_requests","httpMethod":"GET","activityType":"Curated","version":"1.0.0","supportsStreaming":false},"activitiesVersion":"1.0.0","cachedLookupValues":[["employeeId",null]],"cachedBrowserItems":[],"additionalHeaders":null,"fieldsContainer":{"inputFields":[{"sortOrder":1,"format":"date","mask":"yyyy-MM-dd","fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"start","displayName":"Start","description":"Only show time off that occurs on/after the specified start date","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"start","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":2,"format":"date","mask":"yyyy-MM-dd","fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"end","displayName":"End","description":"Only show time off that occurs on/before the specified end date","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"end","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":3,"lookup":{"objectName":"employees_directory","lookupNames":["firstName","lastName","id"],"lookupValue":"id","path":"/employees_directory"},"design":{"loadByDefault":true,"isMultiSelect":false,"enableUserOverride":false,"displayPattern":"{firstName} {lastName} - {id}","isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"employeeId","displayName":"Employee ID","description":"Select the employee from the dropdown or pass custom employee ID","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"employeeId","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":4,"lookup":{"objectName":"time_off_request_types","lookupNames":["name","id"],"lookupValue":"name","path":"/time_off_request_types"},"design":{"loadByDefault":true,"isMultiSelect":false,"enableUserOverride":false,"displayPattern":"{name} - {id}","isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"type","displayName":"Type","description":"Select the time off type from the dropdown","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"type","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":5,"enum":[{"name":"Approved","value":"approved"},{"name":"Denied","value":"denied"},{"name":"Superceded","value":"superceded"},{"name":"Requested","value":"requested"},{"name":"Canceled","value":"canceled"}],"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"timeOffStatus","displayName":"Status","description":"Select the status of the time-off from the dropdown","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"timeOffStatus","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":6,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"id","displayName":"Request ID","description":"A particular request ID to limit the response to.","type":"integer","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"id","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":7,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"action","displayName":"Action","description":"Limit to requests that the user has a particular level of access to. Legal values are: \"view\" or \"approve\". Defaults to view.","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"action","forceExpressionEditor":false,"disableExpressions":false},{"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"status","displayName":"Status","description":"A comma separated list of request status values to include. If omitted, requests of all status values are included. Legal values are \"approved\", \"denied\", \"superceded\", \"requested\", \"canceled\".","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"status","forceExpressionEditor":false,"disableExpressions":false}],"outputFields":[{"sortOrder":null,"format":null,"mask":null,"defaultValue":null,"lookup":null,"enum":null,"design":null,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"get_time_off_requests","displayName":"Get time off requests","description":"","type":"mock-output-type","primaryKey":false,"searchable":false,"required":false,"request":false,"response":true,"responseCurated":false,"fieldLocation":"output","isJitField":true,"isFieldActionParent":false,"isMainResponseField":true,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"get_time_off_requests","forceExpressionEditor":false,"disableExpressions":false}],"jitTypeNeedsRefresh":false,"jitInputField":null,"inputMode":null,"hasUnifiedTypes":false,"searchableFields":[],"jitTypeHash":null},"unifiedTypesCompatible":true,"operation":"List"}'
                uiPathActivityTypeId: 468d10a7-5d93-3cca-98fc-a319dda2b118
              then: c4ae3bbf-7dea-4e78-b59e-73530c3a8c66
          - c4ae3bbf-7dea-4e78-b59e-73530c3a8c66:
              call: UiPath.IntSvc
              with:
                connector: uipath-bamboo-bamboohr
                connectionId: 9dccba1b-2b6f-4a1e-9166-37982a45e4cc
                method: PUT
                endpoint: /update_time_off_status/{id}
                pathParameters: {}
                headers: {}
                queryParameters: {}
                bodyParameters: {}
              export:
                as: '$context + { outputs: ($context.outputs + { update_time_off_status:  . }) }'
              metadata:
                icon:
                  inline: ''
                description: ''
                requiresConfiguration: false
                isTransparent: false
                activityColor: '#b4da55'
                isErrorActivity: false
                isAppActivity: false
                fullName: Connector
                assemblyQualifiedName: ConnectorActivity.Dummy.AssemblyQualifiedName
                activityType: Connector
                displayName: Change Time off Request Status
                configuration: '{"objectActions":null,"primaryKeys":[],"connectorName":"BambooHR","connectorVersion":"1.8.1","objectDisplayName":"Update time off status","objectName":"update_time_off_status","httpMethod":"PUT","path":"/update_time_off_status/{id}","maxPageSize":200,"hasBreakingChanges":false,"customFieldsRequestDetails":null,"textBlocks":[],"jobArguments":null,"instanceParameters":{"connectorKey":"uipath-bamboo-bamboohr","objectName":"update_time_off_status","httpMethod":"PUT","activityType":"Curated","version":"1.0.0","supportsStreaming":false},"activitiesVersion":"1.0.0","cachedLookupValues":[["id",null]],"cachedBrowserItems":[],"additionalHeaders":null,"fieldsContainer":{"inputFields":[{"sortOrder":1,"format":"date","mask":"yyyy-MM-dd","design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"start_date","displayName":"Start date","description":"Start date for the request to update","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"start_date","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":2,"format":"date","mask":"yyyy-MM-dd","design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"end_date","displayName":"End date","description":"End date for the request to update","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"query","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"end_date","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":3,"lookup":{"objectName":"get_time_off_requests","lookupNames":["name","id"],"lookupValue":"id","path":"/get_time_off_requests?start={start_date}&end={end_date}"},"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"fieldActions":[{"actionType":"hide","priorityOrder":1,"rules":[{"type":"field","refFieldName":"end_date","isCleared":true,"isVariable":false}]},{"actionType":"hide","priorityOrder":2,"rules":[{"type":"field","refFieldName":"start_date","isCleared":true,"isVariable":false}]},{"actionType":"show","priorityOrder":3,"rules":[{"type":"field","refFieldName":"start_date","refFieldValues":["*"],"isCleared":false,"isVariable":true},{"type":"field","refFieldName":"end_date","refFieldValues":["*"],"isCleared":false,"isVariable":true}]}],"position":"primary"},"fieldActions":[{"rules":[{"refFieldName":"start_date","isCleared":false,"isConstantValue":null,"isWorkflowExpression":false,"refFieldValues":["*"],"onRefFieldUpdate":true,"customRecordLevelFields":null}],"actionType":"clear","groupNames":null,"messages":null},{"rules":[{"refFieldName":"end_date","isCleared":false,"isConstantValue":null,"isWorkflowExpression":false,"refFieldValues":["*"],"onRefFieldUpdate":true,"customRecordLevelFields":null}],"actionType":"clear","groupNames":null,"messages":null},{"actionType":"hide","priorityOrder":1,"rules":[{"type":"field","refFieldName":"end_date","isCleared":true,"isVariable":false}]},{"actionType":"hide","priorityOrder":2,"rules":[{"type":"field","refFieldName":"start_date","isCleared":true,"isVariable":false}]},{"actionType":"show","priorityOrder":3,"rules":[{"type":"field","refFieldName":"start_date","refFieldValues":["*"],"isCleared":false,"isVariable":true},{"type":"field","refFieldName":"end_date","refFieldValues":["*"],"isCleared":false,"isVariable":true}]}],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"id","displayName":"Request ID","description":"Select the time off request from the dropdown or pass the request ID","type":"string","primaryKey":false,"searchable":false,"required":true,"request":true,"response":false,"responseCurated":false,"fieldLocation":"path","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":true,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"id","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":null,"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"note","displayName":"Note","description":"Add a comment along with the status change","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"body","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"note","forceExpressionEditor":false,"disableExpressions":false},{"sortOrder":null,"enum":[{"name":"Approved","value":"approved"},{"name":"Cancelled","value":"cancelled"},{"name":"Denied","value":"denied"}],"design":{"loadByDefault":false,"isMultiSelect":false,"enableUserOverride":false,"isHidden":false,"position":"primary"},"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":true,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"status","displayName":"Status","description":"Select the status of the time off request","type":"string","primaryKey":false,"searchable":false,"required":false,"request":true,"response":false,"responseCurated":false,"fieldLocation":"body","isJitField":false,"isFieldActionParent":false,"isMainResponseField":false,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"status","forceExpressionEditor":false,"disableExpressions":false}],"outputFields":[{"sortOrder":null,"format":null,"mask":null,"defaultValue":null,"lookup":null,"enum":null,"design":null,"fieldActions":[],"requiredGroups":null,"searchableOperators":null,"searchableNames":null,"searchableDisplayName":null,"searchableJoins":null,"backingField":null,"onCanvas":null,"filterTree":null,"filterNeedsRefresh":null,"isBackgroundField":false,"name":"update_time_off_status","displayName":"Update time off status","description":"","type":"mock-output-type","primaryKey":false,"searchable":false,"required":false,"request":false,"response":true,"responseCurated":false,"fieldLocation":"output","isJitField":true,"isFieldActionParent":false,"isMainResponseField":true,"isMethodParameter":false,"arrayInputPaths":null,"clrType":{"isArray":false,"assemblyQualifiedName":"function"},"id":"update_time_off_status","forceExpressionEditor":false,"disableExpressions":false}],"jitTypeNeedsRefresh":false,"jitInputField":null,"inputMode":null,"hasUnifiedTypes":false,"searchableFields":[],"jitTypeHash":null},"unifiedTypesCompatible":true,"operation":"Replace"}'
                uiPathActivityTypeId: 93d7da58-c901-3e60-be55-14860918bafd
              then: exit
          metadata:
            icon:
              inline: ''
            description: ''
            requiresConfiguration: false
            isTransparent: true
            activityColor: '#526069'
            isErrorActivity: false
            isAppActivity: false
            helpURL: ''
            fullName: Sequence
            assemblyQualifiedName: Sequence
            activityType: Sequence
            displayName: Sequence
    schemas:
      c581e4b3-7bfb-4f8e-bec2-68504a57a734:
        endpoint: /get_time_off_requests
        method: GET
        definitions:
          actions:
            type: object
            properties:
              approve:
                type: boolean
              bypass:
                type: boolean
              cancel:
                type: boolean
              deny:
                type: boolean
              edit:
                type: boolean
              view:
                type: boolean
          amount:
            type: object
            properties:
              amount:
                type: string
              unit:
                type: string
          notes:
            type: object
            properties:
              manager:
                type: string
              employee:
                type: string
          status:
            type: object
            properties:
              lastChanged:
                type: string
                format: date-time
              lastChangedByUserId:
                type: string
              status:
                type: string
          type:
            type: object
            properties:
              icon:
                type: string
              id:
                type: string
              name:
                type: string
          get_time_off_requests_List:
            type: object
            properties:
              actions:
                $ref: '#/definitions/actions'
              amount:
                $ref: '#/definitions/amount'
              notes:
                $ref: '#/definitions/notes'
              status:
                $ref: '#/definitions/status'
              type:
                $ref: '#/definitions/type'
              created:
                type: string
                format: date-time
              employeeId:
                type: string
              end:
                type: string
                format: date-time
              id:
                type: string
              name:
                type: string
              start:
                type: string
                format: date-time
        properties:
          start:
            description: Only show time off that occurs on/after the specified start date
            location: Query
            direction: input
            type: string
            format: date-time
          end:
            description: Only show time off that occurs on/before the specified end date
            location: Query
            direction: input
            type: string
            format: date-time
          employeeId:
            description: Select the employee from the dropdown or pass custom employee ID
            location: Query
            direction: input
            type: string
          type:
            description: Select the time off type from the dropdown
            location: Query
            direction: input
            type: string
          timeOffStatus:
            description: Select the status of the time-off from the dropdown
            location: Query
            direction: input
            type: string
            oneOf:
            - description: Approved
              const: approved
            - description: Denied
              const: denied
            - description: Superceded
              const: superceded
            - description: Requested
              const: requested
            - description: Canceled
              const: canceled
          Jit_get_time_off_requests:
            direction: output
            type: array
            items:
              $ref: '#/definitions/get_time_off_requests_List'
        required:
        - start
        - end
      c4ae3bbf-7dea-4e78-b59e-73530c3a8c66:
        endpoint: /update_time_off_status/{id}
        method: PUT
        definitions:
          update_time_off_status_Replace:
            type: object
            properties:
              code:
                type: integer
              status:
                type: string
                description: Select the status of the time off request
        properties:
          start_date:
            description: Start date for the request to update
            location: Query
            direction: input
            type: string
            format: date-time
          end_date:
            description: End date for the request to update
            location: Query
            direction: input
            type: string
            format: date-time
          id:
            description: Select the time off request from the dropdown or pass the request ID
            location: Path
            direction: input
            type: string
          status:
            description: Select the status of the time off request
            location: Body
            direction: input
            type: string
            oneOf:
            - description: Approved
              const: approved
            - description: Cancelled
              const: cancelled
            - description: Denied
              const: denied
          note:
            description: Add a comment along with the status change
            location: Body
            direction: input
            type: string
          Jit_update_time_off_status:
            $ref: '#/definitions/update_time_off_status_Replace'
            direction: output
        required:
        - start_date
        - end_date
        - id
  expected:
    message: Set the status to 'cancelled' and use the 'id', 'start', and 'end' from the last time off request retrieved.
    argumentsChange:
      type: workflowArguments
      data: []
    propertiesChange:
      type: activityConfiguration
      activityId: update_time_off_status
      data:
      - propertyName: with.status
        propertyOperation: update
        propertyValue:
        - cancelled
        - ${"cancelled"}
        - '"cancelled"'
      - propertyName: with.id
        propertyOperation: update
        propertyValue:
        - ${$context.outputs.get_time_off_requests.content[-1].id}
        - '"\\($context.outputs.get_time_off_requests.content[-1].id)"' # this is not a correct expression, will remove once the bug is fixed
      - propertyName: with.start_date
        propertyOperation: update
        propertyValue:
        - ${$context.outputs.get_time_off_requests.content[-1].start}
        - '"\\($context.outputs.get_time_off_requests.content[-1].start)"' # this is not a correct expression, will remove once the bug is fixed
      - propertyName: with.end_date
        propertyOperation: update
        propertyValue:
        - ${$context.outputs.get_time_off_requests.content[-1].end}
        - '"\\($context.outputs.get_time_off_requests.content[-1].end)"' # this is not a correct expression, will remove once the bug is fixed
extract_input_arguments:
  request:
    messages:
    - role: user
      content: Extract the endpoint to an input argument called endpointUrl.
    activityId: HTTP_5
    expressionLanguage: jq
    workflow:
      document:
        dsl: 1.0.0
        name: API
        version: 0.0.1
        namespace: default
      input:
        schema:
          format: json
          document:
            type: object
            properties:
              nums:
                type: array
                items:
                  type: integer
                description: A list of numbers
              div:
                type: integer
                description: The divisor
      output:
        schema:
          format: json
          document:
            type: object
            properties:
              succes:
                type: boolean
                description: Success or failure
      do:
      - Sequence_1:
          do:
          - For_Each_1:
              for:
                each: currentItem
                in: $workflow.input.nums
                at: currentItemIndex
              do:
              - For_Each_1#Body:
                  do:
                  - JS_Invoke_2:
                      run:
                        script:
                          code: return $currentItem % $workflow.input.div === 0
                          language: javascript
                          arguments: "${{\n                                \"$context\": $context,\n                                \"$workflow\": $workflow,\n                                \"$input\": $input,\n                            \"$currentItem\": $currentItem,\n\"$currentItemIndex\": $currentItemIndex,\n}}"
                      export:
                        as: '$context + { outputs: ($context.outputs + { "JS_Invoke_2": . }) }'
                      metadata:
                        activityType: JsInvoke
                        displayName: JS Invoke - Check if num is divisible by div
                        fullName: JsInvoke
                  - If_1:
                      do:
                      - If_1#Switch:
                          switch:
                          - case:
                              when: ${$context.outputs.JS_Invoke_2}
                              then: If_1#Then
                          - default:
                              then: If_1#Else
                      - If_1#Then:
                          do:
                          - HTTP_5:
                              with:
                                method: POST
                                endpoint: ${"https://httpbin.org/post"}
                                body: "${{\n    \"divisible\": true\n}}"
                              export:
                                as: '$context + { outputs: ($context.outputs + { "HTTP_5": { statusCode: .statusCode, headers: .headers, content: .content, request: .request } }) }'
                              call: http
                              metadata:
                                activityType: Http
                                displayName: HTTP Request - Divisible
                                fullName: Http
                          then: exit
                      - If_1#Else:
                          do:
                          - HTTP_6:
                              with:
                                method: POST
                                endpoint: ${"https://httpbin.org/post"}
                                body: "${{\n    \"divisible\": false\n}}"
                              export:
                                as: '$context + { outputs: ($context.outputs + { "HTTP_6": { statusCode: .statusCode, headers: .headers, content: .content, request: .request } }) }'
                              call: http
                              metadata:
                                activityType: Http
                                displayName: HTTP Request - Not Divisible
                                fullName: Http
                          then: exit
                      export:
                        as: '$context + { outputs: ($context.outputs + { "If_1": . }) }'
                      metadata:
                        activityType: If
                        displayName: If
                        fullName: If
                  export:
                    as: '$context + { outputs: ($context.outputs + { "For_Each_1": ($context.outputs.For_Each_1 + {results: ($context.outputs.For_Each_1.results + [ . ])}) }) }'
              output:
                as: ${$context.outputs.For_Each_1}
              metadata:
                activityType: ForEach
                displayName: For Each
                fullName: ForEach
          metadata:
            activityType: Sequence
            displayName: Sequence
            fullName: Sequence
      evaluate:
        mode: strict
        language: jq
    schemas: {}
  expected:
    message: I'll extract the endpoint from the HTTP request activity and create a new workflow input argument for it.
    argumentsChange:
      type: workflowArguments
      data:
      - argumentLocation: endpointUrl
        argumentOperation: update
        argumentDirection: input
        argumentRequired: true
        argumentSchema:
          type: string
          description: The URL endpoint for the HTTP request.
    propertiesChange:
      type: activityConfiguration
      activityId: HTTP_5
      data:
      - propertyName: with.endpoint
        propertyOperation: update
        propertyValue:
        - ${$workflow.input.endpointUrl}
create_object_argument:
  request:
    messages:
    - role: user
      content: Create an input argument named foobar of type object with 2 properties foo and bar, both of which are strings.
    activityId: HTTP_5
    expressionLanguage: jq
    workflow:
      document:
        dsl: 1.0.0
        name: API
        version: 0.0.1
        namespace: default
      input:
        schema:
          format: json
          document:
            type: object
            properties:
              nums:
                type: array
                items:
                  type: integer
                description: A list of numbers
              div:
                type: integer
                description: The divisor
      output:
        schema:
          format: json
          document:
            type: object
            properties:
              succes:
                type: boolean
                description: Success or failure
      do:
      - Sequence_1:
          do:
          - For_Each_1:
              for:
                each: currentItem
                in: $workflow.input.nums
                at: currentItemIndex
              do:
              - For_Each_1#Body:
                  do:
                  - JS_Invoke_2:
                      run:
                        script:
                          code: return $currentItem % $workflow.input.div === 0
                          language: javascript
                          arguments: "${{\n                                \"$context\": $context,\n                                \"$workflow\": $workflow,\n                                \"$input\": $input,\n                            \"$currentItem\": $currentItem,\n\"$currentItemIndex\": $currentItemIndex,\n}}"
                      export:
                        as: '$context + { outputs: ($context.outputs + { "JS_Invoke_2": . }) }'
                      metadata:
                        activityType: JsInvoke
                        displayName: JS Invoke - Check if num is divisible by div
                        fullName: JsInvoke
                  - If_1:
                      do:
                      - If_1#Switch:
                          switch:
                          - case:
                              when: ${$context.outputs.JS_Invoke_2}
                              then: If_1#Then
                          - default:
                              then: If_1#Else
                      - If_1#Then:
                          do:
                          - HTTP_5:
                              with:
                                method: POST
                                endpoint: ${"https://httpbin.org/post"}
                                body: "${{\n    \"divisible\": true\n}}"
                              export:
                                as: '$context + { outputs: ($context.outputs + { "HTTP_5": { statusCode: .statusCode, headers: .headers, content: .content, request: .request } }) }'
                              call: http
                              metadata:
                                activityType: Http
                                displayName: HTTP Request - Divisible
                                fullName: Http
                          then: exit
                      - If_1#Else:
                          do:
                          - HTTP_6:
                              with:
                                method: POST
                                endpoint: ${"https://httpbin.org/post"}
                                body: "${{\n    \"divisible\": false\n}}"
                              export:
                                as: '$context + { outputs: ($context.outputs + { "HTTP_6": { statusCode: .statusCode, headers: .headers, content: .content, request: .request } }) }'
                              call: http
                              metadata:
                                activityType: Http
                                displayName: HTTP Request - Not Divisible
                                fullName: Http
                          then: exit
                      export:
                        as: '$context + { outputs: ($context.outputs + { "If_1": . }) }'
                      metadata:
                        activityType: If
                        displayName: If
                        fullName: If
                  export:
                    as: '$context + { outputs: ($context.outputs + { "For_Each_1": ($context.outputs.For_Each_1 + {results: ($context.outputs.For_Each_1.results + [ . ])}) }) }'
              output:
                as: ${$context.outputs.For_Each_1}
              metadata:
                activityType: ForEach
                displayName: For Each
                fullName: ForEach
          metadata:
            activityType: Sequence
            displayName: Sequence
            fullName: Sequence
      evaluate:
        mode: strict
        language: jq
    schemas: {}
  expected:
    message: I'll create a new workflow input argument of type object with a property foo and bar, both of which are strings.
    argumentsChange:
      type: workflowArguments
      data:
      - argumentLocation: foobar
        argumentOperation: update
        argumentDirection: input
        argumentRequired: true
        argumentSchema:
          type: object
          description: The foobar object
          properties:
            foo:
              type: string
              description: The foo property
            bar:
              type: string
              description: The bar property
    propertiesChange:
      type: activityConfiguration
      activityId: HTTP_5
      data: []
