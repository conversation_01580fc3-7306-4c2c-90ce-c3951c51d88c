{"request": {"userRequest": "The workflow should be rewritten using Salesforce instead of Oracle NetSuite.", "messageHistory": [], "projectDefinition": {"name": "CreateCustomer", "description": "Create Oracle NetSuite customer", "files": []}, "currentWorkflow": {"content": "{\"document\":{\"dsl\":\"1.0.0\",\"name\":\"API\",\"version\":\"0.0.1\",\"namespace\":\"default\"},\"input\":{\"schema\":{\"format\":\"json\",\"document\":{\"type\":\"object\",\"properties\":{\"CustomerEmail\":{\"type\":\"string\"},\"CustomerType\":{\"type\":\"string\"},\"CustomerName\":{\"type\":\"string\"}},\"required\":[\"CustomerEmail\",\"CustomerType\"]}}},\"do\":[{\"Sequence_1\":{\"do\":[{\"Search_Customers_1\":{\"call\":\"UiPath.IntSvc\",\"with\":{\"connector\":\"uipath-oracle-netsuite\",\"connectionId\":\"9c1d0636-4660-4e2f-9671-f1c46257325c\",\"method\":\"GET\",\"endpoint\":\"/SearchCustomers\",\"pathParameters\":{},\"queryParameters\":{\"email\":\"${$workflow.input.CustomerEmail}\"},\"bodyParameters\":{}},\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, SearchCustomers_1: $output } }\"},\"metadata\":{\"activityType\":\"Connector\",\"displayName\":\"Search NetSuite customer based on provided email\",\"fullName\":\"Connector\",\"uiPathActivityTypeId\":\"c703980c-b7a0-36b1-9551-e0e249695186\",\"objectName\":\"SearchCustomers\"}}},{\"If_1\":{\"do\":[{\"If_1#Switch\":{\"switch\":[{\"case\":{\"when\":\"${$context.outputs.SearchCustomers_1.content.length == 0}\",\"then\":\"If_1#Then\"}},{\"default\":{\"then\":\"If_1#Else\"}}]}},{\"If_1#Then\":{\"do\":[{\"Create_Customer_1\":{\"call\":\"UiPath.IntSvc\",\"with\":{\"connector\":\"uipath-oracle-netsuite\",\"connectionId\":\"9c1d0636-4660-4e2f-9671-f1c46257325c\",\"method\":\"POST\",\"endpoint\":\"/CuratedCustomer\",\"pathParameters\":{},\"queryParameters\":{},\"bodyParameters\":{\"customerType\":\"${$workflow.input.CustomerType}\",\"companyName\":\"${$workflow.input.CustomerName}\",\"email\":\"${$workflow.input.CustomerEmail}\"}},\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, CuratedCustomer_1: $output } }\"},\"metadata\":{\"activityType\":\"Connector\",\"displayName\":\"Create new NetSuite customer\",\"fullName\":\"Connector\",\"uiPathActivityTypeId\":\"2d4cf49c-d6f8-3e80-8643-a690588bdea5\",\"objectName\":\"CuratedCustomer\"}}}],\"then\":\"exit\"}},{\"If_1#Else\":{\"do\":[{\"Response_1\":{\"raise\":{\"error\":{\"title\":\"Internal Server Error\",\"detail\":\"${\\\"Could not identify Customer. Either none or multiple matches were found.\\\"}\",\"status\":500}},\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, Response_1: $output } }\"},\"metadata\":{\"activityType\":\"Response\",\"displayName\":\"Return 500 status code\",\"fullName\":\"Response\"}}}],\"then\":\"exit\"}}],\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, If_1: $output } }\"},\"metadata\":{\"activityType\":\"If\",\"displayName\":\"If NetSuite customer record not found\",\"fullName\":\"If\"}}}],\"metadata\":{\"activityType\":\"Sequence\",\"displayName\":\"Sequence\",\"fullName\":\"Sequence\"}}}],\"evaluate\":{\"mode\":\"strict\",\"language\":\"javascript\"}}", "description": "Create Oracle NetSuite customer", "errors": []}, "currentWorkflowDesignerState": {"availableVariables": [], "availableAdditionalTypeDefinitions": "", "selectedActivityId": null, "selectedProperty": null}, "attachments": [], "availableEntities": {"assets": [], "queues": [], "processes": [], "connections": [{"name": "Office365", "type": "Email", "connector": "uipath-microsoft-outlook365"}]}, "expressionLanguage": "js"}, "expectedResponse": {"valid": true, "scenario": "rewrite-workflow", "workflowRefs": [0]}}