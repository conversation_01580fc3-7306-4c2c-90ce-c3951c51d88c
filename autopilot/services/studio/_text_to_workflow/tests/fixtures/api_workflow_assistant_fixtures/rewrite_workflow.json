{"request": {"userRequest": "Instead of extracting candidates from Workable, get a specific candidate from Greenhouse. Also, instead of creating a PreHire in Worday, simply open a new position.", "messageHistory": [], "projectDefinition": {"name": "CreatePreHire", "description": "Create Pre-hire Workday with retrieved Workable candidate data by email", "files": []}, "currentWorkflow": {"content": "{\"document\":{\"dsl\":\"1.0.0\",\"name\":\"bindings_v2\",\"version\":\"0.0.1\",\"namespace\":\"default\"},\"input\":{\"schema\":{\"format\":\"json\",\"document\":{\"type\":\"object\",\"properties\":{\"workableEmail\":{\"type\":\"string\",\"description\":\"Candidate email to search in Workable\"},\"workdayPositionId\":{\"type\":\"string\",\"description\":\"Workday Postion ID to create the Pre-Hire for\"},\"workdayBirthCountry\":{\"type\":\"string\",\"description\":\"Birth country to use in Workday create prehire\"}},\"required\":[\"workdayPositionId\",\"workableEmail\",\"workdayBirthCountry\"]}}},\"do\":[{\"Sequence_1\":{\"do\":[{\"Search_Candidates_1\":{\"call\":\"UiPath.IntSvc\",\"with\":{\"connector\":\"uipath-workable-workable\",\"connectionId\":\"841cba41-140d-42fd-be49-8460c2b1c79f\",\"method\":\"GET\",\"endpoint\":\"/candidates_search\",\"pathParameters\":{},\"queryParameters\":{\"email\":\"${$workflow.input.workableEmail}\"},\"bodyParameters\":{}},\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, \\\"candidates_search_1\\\": $output } }\"}}},{\"Create_Pre-Hire_1\":{\"call\":\"UiPath.IntSvc\",\"with\":{\"connector\":\"uipath-workday-workday\",\"connectionId\":\"b7a80118-0b76-4378-9f3d-f1c4ce6bf1e1\",\"method\":\"POST\",\"endpoint\":\"/Create_PreHire\",\"pathParameters\":{},\"queryParameters\":{},\"bodyParameters\":{\"Personal_Data.Name_Data.Legal_Name_Data.Name_Detail_Data.First_Name\":\"${$context.outputs.candidates_search_1.content\\n  .find(item => true)\\n  ?.firstname}\",\"Personal_Data.Name_Data.Legal_Name_Data.Name_Detail_Data.Last_Name\":\"${$context.outputs.get_employee_1.content.lastName}\",\"Personal_Data.Name_Data.Legal_Name_Data.Name_Detail_Data.Country_Reference.ID.Attribute_type\":\"${\\\"ISO_3166-1_Alpha-3_Code\\\"}\",\"Personal_Data.Name_Data.Legal_Name_Data.Name_Detail_Data.Country_Reference.ID.Attribute_Value\":\"${$workflow.input.workdayBirthCountry}\",\"Personal_Data.Contact_Data.Email_Address_Data.Email_Address\":\"${$context.outputs.candidates_search_1.content\\n  .find(item => true)\\n  ?.email}\",\"Personal_Data.Contact_Data.Address_Data.Address_Line_Data.Attribute_Type\":\"${\\\"ADDRESS_LINE_1\\\"}\",\"Personal_Data.Contact_Data.Email_Address_Data.Usage_Data.Type_Data.Type_Reference.ID.Attribute_type\":\"${\\\"Communication_Usage_Type_ID\\\"}\",\"Personal_Data.Contact_Data.Email_Address_Data.Usage_Data.Type_Data.Primary_Attribute\":\"${true}\",\"Personal_Data.Contact_Data.Phone_Data.Phone_Device_Type_Reference.ID.Attribute_type\":\"${\\\"Phone_Device_Type_ID\\\"}\",\"Personal_Data.Contact_Data.Phone_Data.Usage_Data.Type_Data.Type_Reference.ID.Attribute_type\":\"${\\\"Communication_Usage_Type_ID\\\"}\",\"Recruiting_Data.Positions_Considered_for_Reference.ID.Attribute_type\":\"${\\\"Position_ID\\\"}\",\"Recruiting_Data.Positions_Considered_for_Reference.ID.Attribute_Value\":\"${$workflow.input.workdayPositionID}\",\"Qualification_Data.Education.Education_Data.Country_Reference.ID.Attribute_type\":\"${\\\"ISO_3166-1_Alpha-3_Code\\\"}\"},\"multipartParameters\":[{\"name\":\"body\",\"dataType\":\"string\"},{\"name\":\"File\",\"dataType\":\"file\"}]},\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, \\\"Create_PreHire_1\\\": $output } }\"},\"displayName\":\"Create Pre-Hire based on Workable data\",\"fullName\":\"Connector\",\"uiPathActivityTypeId\":\"e7e82870-b518-300d-87b2-0a2447a953bb\",\"objectName\":\"Create_PreHire\"}}]}},{\"Response_1\":{\"set\":\"${`Workday pre-hire creation successful, Applicant_ID: ${$context.outputs.Create_PreHire_1.content.Applicant_Reference.ID.Attribute_Value}`}\",\"then\":\"end\",\"export\":{\"as\":\"{ ...$context, outputs: { ...$context?.outputs, \\\"Response_1\\\": $output } }\"},\"metadata\":{\"activityType\":\"Response\",\"displayName\":\"Response Return Workday Pre-hire applicant ID\",\"fullName\":\"Response\"}}}],\"metadata\":{\"activityType\":\"Sequence\",\"displayName\":\"Sequence\",\"fullName\":\"Sequence\"}}", "description": "Create Pre-hire Workday with retrieved Workable candidate data by email", "errors": []}, "currentWorkflowDesignerState": {"availableVariables": [], "availableAdditionalTypeDefinitions": "", "selectedActivityId": null, "selectedProperty": null}, "attachments": [], "availableEntities": {"assets": [], "queues": [], "processes": [], "connections": [{"name": "Office365", "type": "Email", "connector": "uipath-microsoft-outlook365"}]}, "expressionLanguage": "js"}, "expectedResponse": {"valid": true, "scenario": "rewrite-workflow", "workflowRefs": [0]}}