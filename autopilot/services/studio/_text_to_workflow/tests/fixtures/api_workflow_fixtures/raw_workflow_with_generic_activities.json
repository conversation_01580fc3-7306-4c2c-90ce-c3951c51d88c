{"document": {"dsl": "1.0.0", "name": "API", "version": "0.0.1", "namespace": "default"}, "do": [{"Sequence_1": {"do": [{"Insert_Record_1_old": {"call": "UiPath.IntSvc", "with": {"connector": "uipath-bamboo-bamboohr", "connectionId": "9dccba1b-2b6f-4a1e-9166-37982a45e4cc", "endpoint": null, "pathParameters": {}, "queryParameters": {}, "bodyParameters": {}}, "metadata": {"activityType": "Connector", "configuration": "", "displayName": "Insert Record", "fullName": "Connector", "uiPathActivityTypeId": "403bc248-86ef-376d-bc17-6f417e9f4c0d"}}}, {"Update_Record_1": {"call": "UiPath.IntSvc", "with": {"connector": "uipath-bamboo-bamboohr", "connectionId": "9dccba1b-2b6f-4a1e-9166-37982a45e4cc", "endpoint": null, "pathParameters": {}, "queryParameters": {}, "bodyParameters": {}}, "metadata": {"activityType": "Connector", "configuration": "", "displayName": "Update Record", "fullName": "Connector", "uiPathActivityTypeId": "c1403cfb-5389-3fd0-8974-4d836c281217"}}}]}}], "evaluate": {"mode": "strict", "language": "javascript"}}