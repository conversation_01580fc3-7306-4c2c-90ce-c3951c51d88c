{"document": {"dsl": "1.0.0", "name": "API", "version": "0.0.1", "namespace": "default"}, "do": [{"Sequence_1": {"do": [{"Get_Time_off_Requests_1": {"call": "UiPath.IntSvc", "with": {"connector": "uipath-bamboo-bamboohr", "connectionId": "9dccba1b-2b6f-4a1e-9166-37982a45e4cc", "endpoint": null, "pathParameters": {}, "queryParameters": {}, "bodyParameters": {}}, "metadata": {"activityType": "Connector", "configuration": "", "displayName": "Get Time off Requests", "fullName": "Connector", "uiPathActivityTypeId": "468d10a7-5d93-3cca-98fc-a319dda2b118"}}}, {"Change_Time_off_Request_Status_1": {"call": "UiPath.IntSvc", "with": {"connector": "uipath-bamboo-bamboohr", "connectionId": "9dccba1b-2b6f-4a1e-9166-37982a45e4cc", "endpoint": null, "pathParameters": {}, "queryParameters": {}, "bodyParameters": {}}, "metadata": {"activityType": "Connector", "configuration": "", "displayName": "Change Time off Request Status", "fullName": "Connector", "uiPathActivityTypeId": "93d7da58-c901-3e60-be55-14860918bafd"}}}]}}], "evaluate": {"mode": "strict", "language": "javascript"}}