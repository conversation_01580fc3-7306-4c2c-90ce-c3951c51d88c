{"document": {"dsl": "1.0.0", "name": "API", "version": "0.0.1", "namespace": "default"}, "do": [{"Sequence_1": {"do": [{"Send_Group_Chat_Message_1": {"call": "UiPath.IntSvc", "with": {"connector": "uipath-microsoft-teams", "connectionId": "a5bbd8bb-3544-42b3-afcb-9eb0c43d93a7", "method": "POST", "endpoint": "/normalised-chats/{chat_id}/messages-v2/drive/items", "pathParameters": {}, "queryParameters": {}, "bodyParameters": {"attachmentIds[*]": "${\"asdfasdf dfasdf asdfasdf\"}"}, "multipartParameters": [{"name": "file", "dataType": "file"}, {"name": "body", "dataType": "string"}]}, "export": {"as": "$context + { outputs: ($context.outputs + { \"normalised-chats_messages-v2_1\": . }) }"}, "metadata": {"activityType": "Connector", "configuration": "{\"objectActions\":null,\"primaryKeys\":[],\"connectorName\":\"Microsoft Teams\",\"connectorVersion\":\"1.4.7\",\"objectDisplayName\":\"Normalised chats messages V2\",\"objectName\":\"normalised-chats_messages-v2\",\"httpMethod\":\"POST\",\"path\":\"/normalised-chats/{chat_id}/messages-v2/drive/items\",\"maxPageSize\":\"50\",\"hasBreakingChanges\":false,\"customFieldsRequestDetails\":null,\"textBlocks\":[],\"jobArguments\":null,\"instanceParameters\":{\"connectorKey\":\"uipath-microsoft-teams\",\"objectName\":\"normalised-chats_messages-v2\",\"httpMethod\":\"POST\",\"activityType\":\"Curated\",\"version\":\"1.0.0\",\"supportsStreaming\":false},\"activitiesVersion\":\"1.0.0\",\"cachedLookupValues\":[[\"chat_id\",null]],\"cachedBrowserItems\":[],\"additionalHeaders\":null,\"fieldsContainer\":{\"inputFields\":[{\"sortOrder\":1,\"lookup\":{\"objectName\":\"chats\",\"lookupNames\":[\"id\",\"topic\"],\"lookupValue\":\"id\",\"path\":\"/chats\",\"filterPattern\":\"$filter=chatType eq 'group' and contains(tolower(topic),tolower('{filter}'))\"},\"design\":{\"loadByDefault\":true,\"isMultiSelect\":false,\"enableUserOverride\":false,\"displayPattern\":\"{topic}\",\"description\":\"Type upto 3 characters of the name of either the chat group or the user to select the chat or pass chat ID\",\"isHidden\":false,\"position\":\"primary\"},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"backingField\":null,\"onCanvas\":null,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"chat_id\",\"displayName\":\"Chat ID\",\"description\":\"Type upto 3 characters of the name of either the chat group or the user to select the chat or pass chat ID\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":true,\"request\":true,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"path\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":true,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"chat_id\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":2,\"design\":{\"isMultiSelect\":false,\"loadByDefault\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\",\"textBlocks\":null,\"dictionaryWidget\":null},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"body.content\",\"displayName\":\"Message body\",\"description\":\"The content of the message to be sent\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"body_sub_content\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":3,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"body.adaptiveCardContent\",\"displayName\":\"Adaptive card JSON\",\"description\":\"Pass the string by converting the generated JSON from https://adaptivecards.io/designer/. Replace \\\\\\\" with ' and remove all the escape characters having '\\\\' such as \\\\n, \\\\r as these are not supported. Ex: Replace \\\\r\\\\n\\\\\\\"type\\\\\\\" with 'type'\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"body_sub_adaptiveCardContent\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":3,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"file\",\"displayName\":\"Image\",\"description\":\"Image attachment to be uploaded\",\"type\":\"file\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"multipart\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":true,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"Object\",\"assemblyQualifiedName\":\"Object\"},\"id\":\"file\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":5,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false,\"position\":\"primary\"},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"attachmentIds[*]\",\"displayName\":\"File IDs\",\"description\":\"Pass one or more SharePoint file IDs in comma-separated format from the output of \\\"Get File or Folder\\\" or \\\"Upload Files\\\" activities of \\\"Microsoft 365\\\" package\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"attachmentIds_array\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"format\":\"int32\",\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"mentions.id\",\"displayName\":\"Mentions ID\",\"type\":\"integer\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"BigInt\",\"assemblyQualifiedName\":\"BigInt\"},\"id\":\"mentions_sub_id\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"mentions.mentionText\",\"displayName\":\"Mentions mention text\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"mentions_sub_mentionText\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"mentions.mentioned.user.id\",\"displayName\":\"Mentions mentioned user ID\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"mentions_sub_mentioned_sub_user_sub_id\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"mentions.mentioned.user.displayName\",\"displayName\":\"Mentions mentioned user display name\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"mentions_sub_mentioned_sub_user_sub_displayName\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"mentions.mentioned.user.userIdentityType\",\"displayName\":\"Mentions mentioned user identity type\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"body\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"mentions_sub_mentioned_sub_user_sub_userIdentityType\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"design\":{\"displayName\":null,\"description\":null,\"displayPattern\":null,\"isMultiSelect\":null,\"loadByDefault\":null,\"enableUserOverride\":null,\"isHidden\":true,\"fieldActions\":null,\"position\":null,\"component\":null,\"requiredGroups\":null,\"textBlocks\":null,\"dictionaryWidget\":null},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"backingField\":null,\"onCanvas\":null,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"body\",\"displayName\":\"Body\",\"description\":\"The object body\",\"type\":\"string\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":true,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"multipart\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":true,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"body\",\"forceExpressionEditor\":false,\"disableExpressions\":false}],\"outputFields\":[{\"sortOrder\":1,\"design\":{\"loadByDefault\":false,\"isMultiSelect\":false,\"enableUserOverride\":false,\"isHidden\":false},\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"id\",\"displayName\":\"Message ID\",\"description\":\"The ID of the message to send reply to in teams. The ID of the message can be retrieved from the output parameter 'ID' of the Send Channel Message or Send Chat Message activity\",\"type\":\"string\",\"primaryKey\":true,\"searchable\":false,\"required\":false,\"request\":false,\"response\":true,\"responseCurated\":true,\"fieldLocation\":\"output\",\"isJitField\":false,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"string\",\"assemblyQualifiedName\":\"string\"},\"id\":\"out_id\",\"forceExpressionEditor\":false,\"disableExpressions\":false},{\"sortOrder\":null,\"format\":null,\"mask\":null,\"defaultValue\":null,\"lookup\":null,\"enum\":null,\"design\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":true,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"normalised-chats_messages-v2\",\"displayName\":\"Messages\",\"description\":\"Chat messages retrieved\",\"type\":\"normalised-chats_messages-v2\",\"primaryKey\":false,\"searchable\":false,\"required\":false,\"request\":false,\"response\":true,\"responseCurated\":false,\"fieldLocation\":\"output\",\"isJitField\":true,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":null,\"tsDefinition\":null,\"name\":\"normalised-chats_messages-v2\",\"assemblyQualifiedName\":\"normalised-chats_messages-v2\"},\"id\":\"normalised-chats_messages-v2\",\"forceExpressionEditor\":false,\"disableExpressions\":false}],\"jitTypeNeedsRefresh\":false,\"jitInputField\":{\"sortOrder\":null,\"format\":null,\"mask\":null,\"defaultValue\":null,\"lookup\":null,\"enum\":null,\"design\":null,\"fieldActions\":[],\"requiredGroups\":null,\"searchableOperators\":null,\"searchableNames\":null,\"searchableDisplayName\":null,\"searchableJoins\":null,\"backingField\":null,\"onCanvas\":null,\"filterTree\":null,\"filterNeedsRefresh\":null,\"isBackgroundField\":false,\"name\":\"normalised-chats_messages-v2\",\"displayName\":\"Messages\",\"description\":\"\",\"type\":\"Generatednormalised-chats_messages-v2\",\"primaryKey\":false,\"searchable\":false,\"required\":true,\"request\":false,\"response\":false,\"responseCurated\":false,\"fieldLocation\":\"jitObjectInput\",\"isJitField\":true,\"isFieldActionParent\":false,\"isMainResponseField\":false,\"isMethodParameter\":false,\"arrayInputPaths\":null,\"clrType\":{\"isArray\":false,\"jsonInput\":\"{\\n  \\\"attachmentIds\\\": [\\n    null\\n  ],\\n  \\\"body\\\": {\\n    \\\"adaptiveCardContent\\\": null,\\n    \\\"content\\\": null\\n  },\\n  \\\"mentions\\\": {\\n    \\\"id\\\": null,\\n    \\\"mentionText\\\": null,\\n    \\\"mentioned\\\": {\\n      \\\"user\\\": {\\n        \\\"displayName\\\": null,\\n        \\\"id\\\": null,\\n        \\\"userIdentityType\\\": null\\n      }\\n    }\\n  }\\n}\",\"tsDefinition\":\"{\\\"attachmentIds\\\":string[];\\\"body\\\":{\\\"adaptiveCardContent\\\":string;\\\"content\\\":string};\\\"mentions\\\":{\\\"id\\\":number;\\\"mentionText\\\":string;\\\"mentioned\\\":{\\\"user\\\":{\\\"displayName\\\":string;\\\"id\\\":string;\\\"userIdentityType\\\":string}}}}\",\"name\":\"Generatednormalised-chats_messages-v2\",\"assemblyQualifiedName\":\"Generatednormalised-chats_messages-v2\"},\"id\":\"in_normalised-chats_messages-v2\",\"forceExpressionEditor\":false,\"disableExpressions\":false},\"inputMode\":\"Fields\",\"hasUnifiedTypes\":true,\"searchableFields\":[],\"jitTypeHash\":null},\"unifiedTypesCompatible\":true,\"operation\":\"Create\"}", "displayName": "Send Group Chat Message", "fullName": "Connector", "uiPathActivityTypeId": "7b6adc45-6dc6-3278-9df5-e40893d1654c", "objectName": "normalised-chats_messages-v2"}}}], "metadata": {"activityType": "Sequence", "displayName": "Sequence", "fullName": "Sequence"}}}], "evaluate": {"mode": "strict", "language": "jq"}}