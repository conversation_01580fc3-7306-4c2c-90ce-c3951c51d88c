mode: workflow
workflow: |-
  'processName': 'Process Analyzer'
  'thought': 'Analyze the process'
  'workflow':
  - 'thought': 'Analyze Customer's Process'
    'activity': 'UiPath.Process.Analyze'
    'params':
      'Result': '[[Analysis]]'
errors: |-
  while parsing a block mapping
    in "<unicode string>", line 4, column 3:
      - 'thought': 'Analyze Customer's P ...
        ^
  expected <block end>, but found '<scalar>'
    in "<unicode string>", line 4, column 32:
      - 'thought': 'Analyze Customer's Process'
                                    ^
fixed_workflow:
  processName: Process Analyzer
  thought: Analyze the process
  workflow:
  - thought: Analyze Customer's Process
    activity: UiPath.Process.Analyze
    params:
      Result: '[[Analysis]]'
