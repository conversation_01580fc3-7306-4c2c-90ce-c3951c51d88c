mode: workflow
workflow: |-
  'processName': 'Read Website URLs from Gmail Attachments'
  'trigger':
    'thought': 'Manual Trigger'
    'activity': 'UiPath.Core.Activities.ManualTrigger'
  'workflow':
  - 'thought': 'For Each Gmail Email'
    'activity': 'UiPath.GSuite.Activities.ForEachEmailConnections'
    'params':
      'BrowserFolder': 'Inbox'
      'FolderInputSelectionMode': 'EnterPath'
      'IncludeSubfolders': 'False'
      'ImportantOnly': 'False'
      'ManualEntryFolder': 'Inbox'
      'MarkAsRead': 'False'
      'MaxResults': '100'
      'StarredOnly': 'False'
      'UnreadOnly': 'False'
      'WithAttachmentsOnly': 'True'
      'Body':
        'variables':
        - 'name': 'CurrentEmail'
          'type': 'UiPath.GSuite.Models.GmailMessage'
        - 'name': 'CurrentEmailIndex'
          'type': 'System.Int32'
        'Handler':
        - 'thought': 'Download Gmail Email Attachments'
          'activity': 'UiPath.GSuite.Activities.DownloadAttachmentsConnections'
          'params':
            'ExcludeInlineAttachments': 'False'
            'SearchMode': 'UseSimple'
            'Email': '[[CurrentEmail]]'
            'NewResult': '[[DownloadedAttachments]]'
        - 'thought': 'For Each Attachment'
          'activity': 'UiPath.Core.Activities.ForEach<UiPath.GSuite.Models.GmailAttachmentLocalItem>'
          'params':
            'Values': '[[DownloadedAttachments]]'
            'Body':
              'variables':
              - 'name': 'currentAttachment'
                'type': 'UiPath.GSuite.Models.GmailAttachmentLocalItem'
              'Handler':
              - 'thought': 'If Attachment is of type xls'
                'activity': 'System.Activities.Statements.If'
                'params':
                  'Condition': '[[currentAttachment.Extension.ToLower() == ".xls"]]'
                  'Then':
                    'thought': 'Read "Website" sheet from Attachment'
                    'activity': 'UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections<System.Data.DataTable>'
                    'params':
                      'HasHeaders': 'True'
                      'ReadAs': 'Values'
                      'Result': '[[WebsiteSheetData]]'
                      'Range': 'Website'
                      'ItemSelectionMode': 'UseExisting'
                      'Item': '[[currentAttachment]]'
                      'BrowserDriveName': 'OneDrive'
                  - 'thought': 'Get URL from "Name" column in the first row'
                    'activity': 'UiPath.Core.Activities.Assign<string>'
                    'params':
                      'To': '[[WebsiteURL]]'
                      'Value': '[[WebsiteSheetData.Rows[0]["Name"].ToString()]]'
                  - 'thought': 'Open URL in browser'
                    'activity': 'UiPath.UIAutomationNext.Activities.NApplicationCard'
                    'params':
                      'AutoGenerationOptions':
                        'Url': '[[WebsiteURL]]'
                        'Prompt': 'Opening Website URL'
errors: |-
  while parsing a block mapping
    in "<unicode string>", line 45, column 17:
                      'Condition': '[[currentAttachmen ...
                      ^
  expected <block end>, but found '-'
    in "<unicode string>", line 58, column 17:
                      - 'thought': 'Get URL from "Name ...
                      ^
fixed_workflow:
  processName: Read Website URLs from Gmail Attachments
  trigger:
    thought: Manual Trigger
    activity: UiPath.Core.Activities.ManualTrigger
  workflow:
  - thought: For Each Gmail Email
    activity: UiPath.GSuite.Activities.ForEachEmailConnections
    params:
      BrowserFolder: Inbox
      FolderInputSelectionMode: EnterPath
      IncludeSubfolders: 'False'
      ImportantOnly: 'False'
      ManualEntryFolder: Inbox
      MarkAsRead: 'False'
      MaxResults: '100'
      StarredOnly: 'False'
      UnreadOnly: 'False'
      WithAttachmentsOnly: 'True'
      Body:
        variables:
        - name: CurrentEmail
          type: UiPath.GSuite.Models.GmailMessage
        - name: CurrentEmailIndex
          type: System.Int32
        Handler:
        - thought: Download Gmail Email Attachments
          activity: UiPath.GSuite.Activities.DownloadAttachmentsConnections
          params:
            ExcludeInlineAttachments: 'False'
            SearchMode: UseSimple
            Email: '[[CurrentEmail]]'
            NewResult: '[[DownloadedAttachments]]'
        - thought: For Each Attachment
          activity: UiPath.Core.Activities.ForEach<UiPath.GSuite.Models.GmailAttachmentLocalItem>
          params:
            Values: '[[DownloadedAttachments]]'
            Body:
              variables:
              - name: currentAttachment
                type: UiPath.GSuite.Models.GmailAttachmentLocalItem
              Handler:
              - thought: If Attachment is of type xls
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[currentAttachment.Extension.ToLower() == ".xls"]]'
                  Then:
                  - thought: Read "Website" sheet from Attachment
                    activity: UiPath.MicrosoftOffice365.Activities.Excel.ReadRangeConnections<System.Data.DataTable>
                    params:
                      HasHeaders: 'True'
                      ReadAs: Values
                      Result: '[[WebsiteSheetData]]'
                      Range: Website
                      ItemSelectionMode: UseExisting
                      Item: '[[currentAttachment]]'
                      BrowserDriveName: OneDrive
                  - thought: Get URL from "Name" column in the first row
                    activity: UiPath.Core.Activities.Assign<string>
                    params:
                      To: '[[WebsiteURL]]'
                      Value: '[[WebsiteSheetData.Rows[0]["Name"].ToString()]]'
                  - thought: Open URL in browser
                    activity: UiPath.UIAutomationNext.Activities.NApplicationCard
                    params:
                      AutoGenerationOptions:
                        Url: '[[WebsiteURL]]'
                        Prompt: Opening Website URL
