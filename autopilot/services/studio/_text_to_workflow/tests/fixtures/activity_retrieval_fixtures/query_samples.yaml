basic_deserialization:
  target_framework: Portable
  query: Deserialize a JSON string into a JSON array and store it in a variable.
  mode: sequence
  expected_activities:
  - UiPath.Web.Activities.DeserializeJsonArray
  expected_triggers: []
  workflow: null
gsuite_trigger:
  target_framework: Portable
  query: Summarize new Gmail email using OpenAI and share the summary via Slack
  mode: workflow
  expected_activities:
  - UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesSummarize_Text
  - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_Channel
  expected_triggers:
  - UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
  workflow: null
edit_workflow_sample:
  target_framework: Portable
  query: Replace Gsuite with Office365
  mode: edit
  expected_activities:
  - UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections
  expected_triggers: []
  workflow:
    processName: Send Email Using Gmail
    packages:
    - UiPath.GSuite.Activities, v2.8.25
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: Send email using Gmail
      activity: UiPath.GSuite.Activities.SendEmailConnections
      params:
        AttachmentInputMode: UseExisting
        Body: Hello, World!
        Importance: Normal
        InputType: TEXT
        SaveAsDraft: 'False'
        Attachments: '[[AttachmentsList]]'
        Bcc: '[[Enumerable.Repeat(BCCAddress, 1).ToList()]]'
        Cc: '[[Enumerable.Repeat(CCAddress, 1).ToList()]]'
        Subject: '[[Subject]]'
        TextBody: '[[Body]]'
        To: '[[Enumerable.Repeat(ToAddress, 1).ToList()]]'