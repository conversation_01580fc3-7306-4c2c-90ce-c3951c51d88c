# Workflow Assistant Test Fixtures

This directory contains test fixtures for the workflow assistant router. Each fixture represents a different scenario for testing the router's ability to categorize and respond to user requests.

## Base Scenarios

- **assistant_generate_workflow.json**: Tests the basic workflow generation capability.
- **assistant_edit_activity.json**: Tests editing a single activity in a workflow.
- **assistant_edit_workflow.json**: Tests editing an entire workflow.
- **assistant_append_workflow.json**: Tests appending to an existing workflow.
- **assistant_fix_workflow.json**: Tests fixing issues in a workflow.
- **assistant_analyze_workflow.json**: Tests analyzing a workflow.

## Generate Workflow Variants

- **assistant_entity_selection.json**: Tests selecting specific entities mentioned in the user's prompt.
- **assistant_follow_up_question.json**: Tests generating a follow-up question when the user request is too vague.
- **assistant_complex_query.json**: Tests handling a complex query with a high complexity score.
- **assistant_reference_messages.json**: Tests referencing previous messages in the conversation.
- **assistant_trigger_true.json**: Tests a scenario where trigger should be true.
- **assistant_invalid_query.json**: Tests handling an invalid query that's not related to workflow generation.
- **assistant_project_file_mention.json**: Tests referencing a specific project file mentioned in the user's prompt.
- **assistant_image_attachment.json**: Tests referencing an image attachment mentioned in the user's prompt.

## Running Tests

Tests can be run using pytest:

```bash
pytest studio/text_to_workflow/tests/integration/test_workflow_assistant_router.py -v
```

## Test Structure

Each test fixture follows this structure:

```json
{
  "request": {
    "userRequest": "The user's request text",
    "messageHistory": [...],
    "projectDefinition": {...},
    "currentWorkflow": null,
    "currentWorkflowDesignerState": {...},
    "availableEntities": {...},
    "attachments": [...]
  },
  "expectedResponse": {
    "thought": "Expected thought process from the assistant",
    "valid": true/false,
    "request": "The user's request that was processed",
    "followUp": true/false,
    "question": "Follow-up question if needed",
    "score": 0-5,
    "trigger": true/false,
    "files": [],
    "entities": [...],
    "scenario": "scenario-name",
    "projectFiles": [...],
    "attachments": [...],
    "activityRefs": [...],
    "workflowRefs": [...],
    "usefulWorkflows": [...],
    "messageRefs": [...],
    "instructions": null
  }
}
``` 