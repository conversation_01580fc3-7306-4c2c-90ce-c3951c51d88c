```yaml
input_arguments:
  EmployeeId:
    type: string
    default: null
  StartDate:
    type: string
    default: null
  EndDate:
    type: string
    default: null
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Get All BambooHR Time Off Requests for EmployeeId between StartDate and EndDate
    activity: BambooHRGet_Time_off_Requests
    id: BambooHRGet_Time_off_Requests_1
    export:
      as: '$context + { outputs: ($context.outputs + { timeOffRequests: . }) }'
    with:
      employeeId: ${$workflow.input.EmployeeId}
      start: ${$workflow.input.StartDate}
      end: ${$workflow.input.EndDate}
  - thought: Cancel the last time off request in the list
    activity: If
    id: If_1
    export:
      as: '$context + { outputs: { If_1: . } }'
    condition: '$context.outputs.timeOffRequests | length > 0'
    then:
    - thought: Get the last time off request
      activity: InvokeCSharpActivity
      id: JsInvokeActivity_1
      export:
        as: '$context + { outputs: ($context.outputs + { lastTimeOffRequest: . }) }'
      code: |
        const timeOffRequests = $context.outputs.timeOffRequests;
        if (timeOffRequests && timeOffRequests.length > 0) {
          const lastRequest = timeOffRequests[timeOffRequests.length - 1];
          return lastRequest;
        } else {
          return null;
        }
    - thought: Change the status of the last time off request to cancelled
      activity: BambooHRChange_Time_off_Request_Status
      id: BambooHRChange_Time_off_Request_Status_1
      export:
        as: '$context + { outputs: ($context.outputs + { changeTimeOffStatus: . }) }'
      with:
        id: ${$context.outputs.lastTimeOffRequest.id}
        start_date: ${$workflow.input.StartDate}
        end_date: ${$workflow.input.EndDate}
        status: '"cancelled"'
    else: []
```