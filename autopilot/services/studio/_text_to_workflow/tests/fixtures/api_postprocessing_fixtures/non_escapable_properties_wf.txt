```yaml
input: "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"AsanaWorkSpace\": {\n      \"type\": \"string\"\n    },\n    \"AsanaProject\": {\n      \"type\": \"string\"\n    }\n  },\n  \"required\": [\n    \"AsanaWorkSpace\",\n    \"AsanaProject\"\n  ]\n}"
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Search Freshdesk Tickets created today
    activity: FreshdeskSearch_Tickets
    id: search_sub_tickets_1
    with:
      query: ${`created_at:'${new Date().toISOString().split('T')[0]}'`}
  - thought: For Each Freshdesk ticket
    activity: ForEach
    id: For_Each_1
    for:
      each: currentItem
      in: $context.outputs.search_sub_tickets_1.content
      at: currentItemIndex
    do:
    - thought: Create Corresponding Asana Task
      activity: HttpRequest
      id: HTTP_1
      with:
        method: GET
        endpoint: ${`https://asana.createtask.org/?project=${$workflow.input.AsanaProject.toString()}&wkspate=${$workflow.input.AsanaWorkSpace.toString()}`}
    - thought: Retrieve task for tasks for project
      activity: JsInvoke
      id: Javascript_1
      code: "const asana = require('asana');\nconst client = asana.Client.create().useAccessToken('your_personal_access_token');\nclient.tasks.findByProject(${$workflow.input.AsanaProject.toString()}).then(tasks => {\n  tasks.forEach(task => {\n    console.log(task);\n  });\n}).catch(err => console.error(err));"
```
