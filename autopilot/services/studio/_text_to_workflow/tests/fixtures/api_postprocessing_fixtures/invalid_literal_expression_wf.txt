```yaml
input: "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"EmailAddress\": {\n      \"type\": \"string\"\n    }}\n  \"required\": [\n    \"EmailAddress\"\n  ],\n  \"additionalProperties\": false\n}"
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Get Oracle Netsuite Campaigns via HTTP Request
    activity: Oracle_NetSuite_HTTP_Request
    id: Oracle_NetSuite_HTTP_Request_1
    with:
      method: GET
      endpoint: ${"Campaign"}
  - thought: Extract the fields internalId, title, message, expectedRevenue
    activity: JsInvoke
    id: Javascript_1
    code: ''
  - thought: Send Gmail with the extracted data
    activity: GmailSend_Email
    id: SendEmail_1
    with:
      SaveAsDraft: false
      To: ${$workflow.input.EmailAddress}
      Subject: "Oracle Netsuite Campaigns Data"
      Body: ${`Here is the extracted data from Oracle Netsuite Campaigns:\n\n        ${JSON.stringify($context.outputs.Javascript_1, null, 2)}`}
      Importance: ${"normal"}
      TestInt: 0
      TestDouble: 1.4
```
