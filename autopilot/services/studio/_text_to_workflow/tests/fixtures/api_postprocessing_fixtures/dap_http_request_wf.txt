```yaml
input: '{"type": "object", "properties": {"GitHubUser": {"type": "string"}, "GithubRepository": {"type": "string"}}, "required": ["GitHubUser"]}'
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Get GitHub Repository commits using HTTP Request
    activity: GitHub_HTTP_Request
    id: GitHub_HTTP_Request_1
    with:
      method: GET
      url: ${[ "/repos/", $workflow.input.GithubRepository, "/commits" ].join('')}
```