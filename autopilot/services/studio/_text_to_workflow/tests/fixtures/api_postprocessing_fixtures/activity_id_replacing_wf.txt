```yaml
input: "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ContactId\": {\n      \"type\": \"string\"\n    },\n    \"SlackUserEmail\": {\n      \"type\": \"string\"\n    }\n  },\n  \"required\": [\n    \"ContactId\",\n    \"SlackUserEmail\"\n  ],\n  \"additionalProperties\": false\n}"
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Get all Freshdesk tickets created today
    activity: Freshdesk_HTTP_Request
    id: get_tickets
    with:
      method: GET
      url: ${"/api/v2/tickets"}
      query: ${"created_at:>=" + new Date().toISOString().slice(0, 10)}
  - thought: Send Message to Teams Channel with low severity incident list
    activity: Microsoft_TeamsSend_Channel_Message
    id: send_channel_message_1
    with:
      team_id: ${$workflow.input.TeamsLowSeverityIncidentsTeamId}
      channel_id: ${"General"}
      body.content: '${$context.outputs.get_tickets.content.filter}'
  - thought: For each ticket, create a task in Asana
    activity: ForEach
    id: For_Each_1
    for:
      each: ticket
      in: $context.outputs.get_tickets.content
      at: index
    do:
    - thought: Create a task in Asana for the current ticket
      activity: AsanaCreate_Task
      id: create_task
      with:
        workspace: ${"12345"}
        projectGids[*]: ${["67890"]}
        name: ${$item.subject}
        notes: ${$context.outputs.send_channel_message_1.content.text}
  - thought: Get Salesforce Contact by ID
    activity: SalesforceGet_Contact
    id: get_contact
    with:
      curated_contactId: ${$workflow.input.ContactId}
  - thought: Get Salesforce Contact by ID
    activity: SalesforceGet_Contact
    id: get_contact_2
    with:
      curated_contactId: ${$context.outputs.get_contact.content.text}
  - thought: Send Slack Message to User with Contact and Account Details
    activity: SlackSend_Message_to_User
    id: test123
    with:
      send_as: ${"bot"}
      messageToSend: '${$context.outputs.get_contact_2.content.FirstName}'
      channel: ${$workflow.input.SlackUserEmail}
```