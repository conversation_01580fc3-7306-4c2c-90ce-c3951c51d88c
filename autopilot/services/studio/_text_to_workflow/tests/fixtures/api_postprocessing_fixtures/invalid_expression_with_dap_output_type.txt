input: '{"type": "object", "properties": {"EmailAddress": {"type": "string"}}}'
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Search SendGrid contact based on provided EmailAddress
    activity: SendGridGet_Contact_Details_by_Email
    id: getContactByEmail_1
    with:
      email: ${$workflow.input.EmailAddress}
  - thought: If contact is found
    activity: If
    id: If_1
    condition: ${$context.outputs.getContactByEmail_1.content.getContactByEmail_Retrieve != null}
    then: []
    else: []