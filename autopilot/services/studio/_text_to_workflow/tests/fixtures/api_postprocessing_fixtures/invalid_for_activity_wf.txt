```yaml
input: '{}'
root:
  thought: Sequence
  activity: Sequence
  id: Sequence_1
  do:
  - thought: Search for Freshdesk tickets with status 'Open' and past the due date
    activity: FreshdeskSearch_Tickets
    id: search_sub_tickets_1
    with:
      query: ${"status:2 AND due_by<'"+new Date().toISOString()+"'"}
  - thought: For each ticket found, set the priority to 'Urgent'
    activity: ForEach
    id: ForEach_1
    do:
    - thought: Update the ticket priority to 'Urgent'
      activity: FreshdeskUpdate_Ticket
      id: tickets_1
      with:
        ticket_id: ${current_ticket.id}
        priority: ${"1"}
    ```