﻿activity in whitelist:
  input:
    userRequest: Reply with orchestrator documentation link
    targetFramework: Portable
    workflow: |
      processName: RespondeToDiffrentEmails
      packages:
      - UiPath.IntegrationService.Activities, v[1.5.0]
      - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
      trigger:
        thought: Manual Trigger
        activity: UiPath.Core.Activities.ManualTrigger
        params:
          Result: '[[Result]]'
      workflow:
      - thought: For each email in Outlook
        activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
        params:
          AuthScopesInvalid: 'False'
          BrowserFolder: Inbox
          Importance: Any
          IncludeSubfolders: 'False'
          MarkAsRead: 'False'
          MaxResults: '100'
          OrderByDate: NewestFirst
          SelectionMode: Browse
          UnreadOnly: 'False'
          UseSharedMailbox: 'False'
          WithAttachmentsOnly: 'False'
          Length: '[[Length]]'
          Body:
            variables:
            - name: CurrentEmail
              type: UiPath.MicrosoftOffice365.Models.Office365Message
            - name: CurrentEmailIndex
              type: System.Int32
            Handler:
            - thought: If email subject contains 'robot'
              activity: System.Activities.Statements.If
              params:
                Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                Then:
                - thought: Reply with robot documentation link
                  activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                  params:
                    AttachmentInputMode: Existing
                    AttachmentsBackup:
                      StoredValue: Existing
                    Body: '<P><SPAN>The documentation for Robot is here.</SPAN></P>'
                Else:
                - thought: If email subject contains 'orchestrator'
                  activity: System.Activities.Statements.If
                  params:
                    Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                    Then:
                    - thought: Reply with orchestrator documentation link
                      activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                      currentActivity: true
                      params:
                        AttachmentInputMode: Existing
                        AttachmentsBackup:
                          StoredValue: Existing
                        Importance: Normal
                        ReplyToAll: 'False'
                        SaveAsDraft: 'True'
                        UseSharedMailbox: 'False'
                        Body: '<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>'
                        Email: '[[CurrentEmail]]'
                    Else:
                    - thought: Send Slack message with email subject
                      activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                      params:
                        Jit_send_message_to_channel_v2: '[[Jit_send_message_to_channel_v2]]'
                        out_ts: '[[out_ts]]'
                        channel: C01HCS3VAHY
                        send_as: bot
                        UiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
      variables:
      - name: Length
        type: System.Int32
      - name: Result
        type: CurrentJobInfo
      - name: Jit_send_message_to_channel_v2
        type: send_message_to_channel_v2_Create
      - name: out_ts
        type: String
    activityTypeDefinition: "class ReplyToEmailConnections : Activity\r\n{\r\nInArgument<Office365Message> Email;\r\nbool UseSharedMailbox;\r\nInArgument<string> ReplyAs;\r\nInArgument<string> Body;\r\nAttachmentInputMode AttachmentInputMode;\r\nInArgument<IEnumerable<IResource>> Attachments;\r\nIEnumerable<InArgument<IResource>> ArgumentAttachments;\r\nInArgument<IEnumerable<string>> AttachmentPaths;\r\nIEnumerable<InArgument<string>> ArgumentAttachmentPaths;\r\nInArgument<string> NewSubject;\r\nInArgument<IEnumerable<string>> To;\r\nInArgument<IEnumerable<string>> Cc;\r\nInArgument<IEnumerable<string>> Bcc;\r\nInArgument<Importance> Importance;\r\nInArgument<bool> SaveAsDraft;\r\nInArgument<bool> ReplyToAll;\r\n}"
    additionalTypeDefinitions: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName ;\r\nstring ProcessName ;\r\nstring ProcessVersion ;\r\nstring RobotName ;\r\nstring Key ;\r\nstring TenantName ;\r\nstring FolderName ;\r\nstring UserEmail ;\r\nPictureInPictureMode PictureInPictureMode ;}"
    availableVariables:
    - name: Result
      type: CurrentJobInfo
    - name: CurrentEmail
      type: Office365Message
    - name: CurrentEmailIndex
      type: Int32
    - name: Length
      type: Int32
  expected: |
    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    params:
      AttachmentInputMode: Existing
      AttachmentsBackup:
        StoredValue: Existing
      Importance: Normal
      ReplyToAll: 'False'
      SaveAsDraft: 'True'
      UseSharedMailbox: 'False'
      Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
      Email: '[[CurrentEmail]]'
activity not in whitelist:
  input:
    userRequest: Manual Trigger
    targetFramework: Portable
    workflow: |
      processName: RespondeToDiffrentEmails
      packages:
      - UiPath.IntegrationService.Activities, v[1.5.0]
      - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
      trigger:
        thought: Manual Trigger
        activity: UiPath.Core.Activities.ManualTrigger
        currentActivity: true
        params:
          Result: '[[Result]]'
      workflow:
      - thought: For each email in Outlook
        activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
        params:
          AuthScopesInvalid: 'False'
          BrowserFolder: Inbox
          Importance: Any
          IncludeSubfolders: 'False'
          MarkAsRead: 'False'
          MaxResults: '100'
          OrderByDate: NewestFirst
          SelectionMode: Browse
          UnreadOnly: 'False'
          UseSharedMailbox: 'False'
          WithAttachmentsOnly: 'False'
          Length: '[[Length]]'
          Body:
            variables:
            - name: CurrentEmail
              type: UiPath.MicrosoftOffice365.Models.Office365Message
            - name: CurrentEmailIndex
              type: System.Int32
            Handler:
            - thought: If email subject contains 'robot'
              activity: System.Activities.Statements.If
              params:
                Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                Then:
                - thought: Reply with robot documentation link
                  activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                  params:
                    AttachmentInputMode: Existing
                    AttachmentsBackup:
                      StoredValue: Existing
                    Importance: Normal
                    ReplyToAll: 'False'
                    SaveAsDraft: 'True'
                    UseSharedMailbox: 'False'
                    Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                    Email: '[[CurrentEmail]]'
                Else:
                - thought: If email subject contains 'orchestrator'
                  activity: System.Activities.Statements.If
                  params:
                    Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                    Then:
                    - thought: Reply with orchestrator documentation link
                      activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                      params:
                        AttachmentInputMode: Existing
                        AttachmentsBackup:
                          StoredValue: Existing
                        Importance: Normal
                        ReplyToAll: 'False'
                        SaveAsDraft: 'True'
                        UseSharedMailbox: 'False'
                        Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                        Email: '[[CurrentEmail]]'
                    Else:
                    - thought: Send Slack message with email subject
                      activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                      params:
                        Jit_send_message_to_channel_v2: '[[Jit_send_message_to_channel_v2]]'
                        out_ts: '[[out_ts]]'
                        channel: C01HCS3VAHY
                        send_as: bot
                        UiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
      variables:
      - name: Length
        type: System.Int32
      - name: Result
        type: CurrentJobInfo
      - name: Jit_send_message_to_channel_v2
        type: send_message_to_channel_v2_Create
      - name: out_ts
        type: String
    activityTypeDefinition: "class ManualTrigger : Activity\r\n{\r\nOutArgument<CurrentJobInfo> Result ;\r\n}\r\n"
    additionalTypeDefinitions: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName ;\r\nstring ProcessName ;\r\nstring ProcessVersion ;\r\nstring RobotName ;\r\nstring Key ;\r\nstring TenantName ;\r\nstring FolderName ;\r\nstring UserEmail ;\r\nPictureInPictureMode PictureInPictureMode ;}"
    availableVariables: []
  expected: null
