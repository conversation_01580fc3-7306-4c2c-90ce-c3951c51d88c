﻿respond to emails:
  input:
    target_framework: Portable
    name: RespondToDifferentEmails
    description: Check all my emails in outlook and respond with a link to the documentation to emails mentioning 'robot' or 'orchestrator' in subject. If not found, send a message to 'C01HCS3VAHY' slack channel
    conversion_success: true
    conversion_errors: ''
    plan: |-
      1. Manual Trigger
      2. For each email in Outlook
        2.1. If email subject contains ''robot''
          2.1.1. Reply with robot documentation link
        2.2. Else
          2.2.1. If email subject contains ''orchestrator''
            *******. Reply with orchestrator documentation link
          2.2.2. Else
            *******. Send Slack message with email subject
    retrieved_triggers:
    - UiPath.Core.Activities.ManualTrigger
    retrieved_activities:
    - UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
    - System.Activities.Statements.If
    - UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    - UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@37a305b2-89b1-315d-b73f-1778839a6c47
    process:
      processName: RespondToDifferentEmails
      packages:
      - UiPath.IntegrationService.Activities, v[1.5.0]
      - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
      trigger:
        thought: Manual Trigger
        activity: UiPath.Core.Activities.ManualTrigger
      workflow:
      - thought: For each email in Outlook
        activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
        params:
          AuthScopesInvalid: 'False'
          BrowserFolder: Inbox
          Importance: Any
          IncludeSubfolders: 'False'
          MarkAsRead: 'False'
          MaxResults: '100'
          OrderByDate: NewestFirst
          SelectionMode: Browse
          UnreadOnly: 'False'
          UseSharedMailbox: 'False'
          WithAttachmentsOnly: 'False'
          Length: '[[Length]]'
          Body:
            variables:
            - name: CurrentEmail
              type: UiPath.MicrosoftOffice365.Models.Office365Message
            - name: CurrentEmailIndex
              type: System.Int32
            Handler:
            - thought: If email subject contains 'robot'
              activity: System.Activities.Statements.If
              params:
                Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                Then:
                - thought: Reply with robot documentation link
                  activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                  params:
                    AttachmentInputMode: Existing
                    AttachmentsBackup:
                      StoredValue: Existing
                    Importance: Normal
                    ReplyToAll: 'False'
                    SaveAsDraft: 'True'
                    UseSharedMailbox: 'False'
                    Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                    Email: '[[CurrentEmail]]'
                Else:
                - thought: If email subject contains 'orchestrator'
                  activity: System.Activities.Statements.If
                  params:
                    Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                    Then:
                    - thought: Reply with orchestrator documentation link
                      activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                      params:
                        AttachmentInputMode: Existing
                        AttachmentsBackup:
                          StoredValue: Existing
                        Importance: Normal
                        ReplyToAll: 'False'
                        SaveAsDraft: 'True'
                        UseSharedMailbox: 'False'
                        Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                        Email: '[[CurrentEmail]]'
                    Else:
                    - thought: Send Slack message with email subject
                      activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                      uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                      params:
                        channel: C01HCS3VAHY
                        send_as: bot
                        messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                        Jit_send_message_to_channel_v2: '[[MessageSent]]'
                        out_ts: '[[MessageTimestamp]]'
      variables:
      - name: Length
        type: System.Int32
      - name: MessageSent
        type: send_message_to_channel_v2_Create
      - name: MessageTimestamp
        type: out_ts
    typedefs:
      UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections:
        Success: true
        TypeDefinition: "class ReplyToEmailConnections : Activity\r\n{\r\nInArgument<Office365Message> Email;\r\nbool UseSharedMailbox;\r\nInArgument<string> ReplyAs;\r\nInArgument<string> Body;\r\nAttachmentInputMode AttachmentInputMode;\r\nInArgument<IEnumerable<IResource>> Attachments;\r\nIEnumerable<InArgument<IResource>> ArgumentAttachments;\r\nInArgument<IEnumerable<string>> AttachmentPaths;\r\nIEnumerable<InArgument<string>> ArgumentAttachmentPaths;\r\nInArgument<string> NewSubject;\r\nInArgument<IEnumerable<string>> To;\r\nInArgument<IEnumerable<string>> Cc;\r\nInArgument<IEnumerable<string>> Bcc;\r\nInArgument<Importance> Importance;\r\nInArgument<bool> SaveAsDraft;\r\nInArgument<bool> ReplyToAll;\r\n}"
        AdditionalTypeDefinitions: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;\r\n}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,\r\n}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,\r\n}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;\r\n}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,\r\n}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;\r\n}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,\r\n}"
        AdditionalTypeNamespaces: "UiPath.MicrosoftOffice365.Activities.Mail\r\nUiPath.MicrosoftOffice365.Models\r\nUiPath.MicrosoftOffice365.Activities.Mail.Enums\r\nUiPath.Platform.ResourceHandling\r\nSystem.Net.Mail"
      UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections:
        Success: true
        TypeDefinition: "class ForEachEmailConnections : Activity\r\n{\r\nActivityActionForEachEmailConnections1 Body;\r\nInArgument<string> ManualEntryFolder;\r\nInArgument<string> BrowserFolder;\r\nItemSelectionMode SelectionMode;\r\nbool UseSharedMailbox;\r\nInArgument<string> Mailbox;\r\nInArgument<bool> IncludeSubfolders;\r\nInArgument<int> MaxResults;\r\nInArgument<bool> UnreadOnly;\r\nInArgument<bool> WithAttachmentsOnly;\r\nInArgument<bool> MarkAsRead;\r\nInArgument<ImportanceFilter> Importance;\r\nMailFilterCollection Filter;\r\nOutArgument<int> Length;\r\n}"
        AdditionalTypeDefinitions: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;\r\n}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,\r\n}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,\r\n}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;\r\n}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;\r\n}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,\r\n}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;\r\n}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,\r\n}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,\r\n}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;\r\n}"
        AdditionalTypeNamespaces: "UiPath.MicrosoftOffice365.Activities.Mail\r\nUiPath.MicrosoftOffice365.Models\r\nUiPath.MicrosoftOffice365.Activities.Mail.Enums\r\nUiPath.MicrosoftOffice365.Activities.Mail.Filters\r\nSystem.Net.Mail\r\nUiPath.MicrosoftOffice365.Mail.Enums"
      System.Activities.Statements.If:
        Success: true
        TypeDefinition: "class If : Activity\r\n{\r\nInArgument<bool> Condition;\r\nActivity Then;\r\nActivity Else;\r\n}"
        AdditionalTypeDefinitions: ''
        AdditionalTypeNamespaces: System.Activities.Statements
      UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@37a305b2-89b1-315d-b73f-1778839a6c47:
        Success: true
        TypeDefinition: "class ConnectorActivity : Activity\r\n{\r\n    InArgument<String> channel;\r\n    InArgument<String> messageToSend;\r\n    InArgument<String> send_as;\r\n    InArgument<String> fields;\r\n    InArgument<String> buttons;\r\n    InArgument<String> image;\r\n    InArgument<String> parse;\r\n    InArgument<Boolean> link_names;\r\n    InArgument<Boolean> unfurl_links;\r\n    InArgument<String> username;\r\n    InArgument<String> icon_emoji;\r\n    OutArgument<send_message_to_channel_v2_Create> Jit_send_message_to_channel_v2;\r\n    OutArgument<String> out_ts;\r\n}"
        AdditionalTypeDefinitions: "class send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;\r\n}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;\r\n}\r\nclass icons\r\n{\r\nstring emoji;\r\n}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;\r\n}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;\r\n}\r\nclass response_metadata\r\n{\r\nList<string> warnings;\r\n}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;\r\n}"
        AdditionalTypeNamespaces: UiPath.IntegrationService.Activities.SWEntities.CE96B3A21A6_send_message_to_channel_v2_Create.Bundle
  expected:
  - id: test/00___UiPath.Core.Activities.ManualTrigger
    demo_id: test/00___UiPath.Core.Activities.ManualTrigger
    workflow_id: test
    activity_id: UiPath.Core.Activities.ManualTrigger
    activity_fqn: UiPath.Core.Activities.ManualTrigger
    input:
      target_framework: Portable
      user_query: Manual Trigger
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          currentActivity: true
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      activity_typedef: "class ManualTrigger : Activity\r\n{\r\nOutArgument<CurrentJobInfo> Result;\r\n}\r\n"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        Result: '[[Result]]'
  - id: test/01___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
    demo_id: test/01___UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
    workflow_id: test
    activity_id: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
    activity_fqn: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
    input:
      target_framework: Portable
      user_query: For each email in Outlook
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For Each Email
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          currentActivity: true
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      activity_typedef: "class ForEachEmailConnections : Activity\r\n{\r\nActivityActionForEachEmailConnections1 Body;\r\nInArgument<string> ManualEntryFolder;\r\nInArgument<string> BrowserFolder;\r\nItemSelectionMode SelectionMode;\r\nbool UseSharedMailbox;\r\nInArgument<string> Mailbox;\r\nInArgument<bool> IncludeSubfolders;\r\nInArgument<int> MaxResults;\r\nInArgument<bool> UnreadOnly;\r\nInArgument<bool> WithAttachmentsOnly;\r\nInArgument<bool> MarkAsRead;\r\nInArgument<ImportanceFilter> Importance;\r\nMailFilterCollection Filter;\r\nOutArgument<int> Length;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        BrowserFolder: Inbox
        Importance: Any
        IncludeSubfolders: 'False'
        MarkAsRead: 'False'
        MaxResults: '100'
        SelectionMode: Browse
        UnreadOnly: 'False'
        UseSharedMailbox: 'False'
        WithAttachmentsOnly: 'False'
        Length: '[[Length]]'
  - id: test/02___System.Activities.Statements.If
    demo_id: test/02___System.Activities.Statements.If
    workflow_id: test
    activity_id: System.Activities.Statements.If
    activity_fqn: System.Activities.Statements.If
    input:
      target_framework: Portable
      user_query: If email subject contains 'robot'
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If
                activity: System.Activities.Statements.If
                currentActivity: true
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      activity_typedef: "class If : Activity\r\n{\r\nInArgument<bool> Condition;\r\nActivity Then;\r\nActivity Else;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
  - id: test/03___UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    demo_id: test/03___UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    workflow_id: test
    activity_id: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    activity_fqn: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    input:
      target_framework: Portable
      user_query: Reply with robot documentation link
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply to Email
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    currentActivity: true
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      activity_typedef: "class ReplyToEmailConnections : Activity\r\n{\r\nInArgument<Office365Message> Email;\r\nbool UseSharedMailbox;\r\nInArgument<string> ReplyAs;\r\nInArgument<string> Body;\r\nAttachmentInputMode AttachmentInputMode;\r\nInArgument<IEnumerable<IResource>> Attachments;\r\nIEnumerable<InArgument<IResource>> ArgumentAttachments;\r\nInArgument<IEnumerable<string>> AttachmentPaths;\r\nIEnumerable<InArgument<string>> ArgumentAttachmentPaths;\r\nInArgument<string> NewSubject;\r\nInArgument<IEnumerable<string>> To;\r\nInArgument<IEnumerable<string>> Cc;\r\nInArgument<IEnumerable<string>> Bcc;\r\nInArgument<Importance> Importance;\r\nInArgument<bool> SaveAsDraft;\r\nInArgument<bool> ReplyToAll;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        AttachmentInputMode: Existing
        Importance: Normal
        ReplyToAll: 'False'
        SaveAsDraft: 'True'
        UseSharedMailbox: 'False'
        Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
        Email: '[[CurrentEmail]]'
  - id: test/04___System.Activities.Statements.If
    demo_id: test/04___System.Activities.Statements.If
    workflow_id: test
    activity_id: System.Activities.Statements.If
    activity_fqn: System.Activities.Statements.If
    input:
      target_framework: Portable
      user_query: If email subject contains 'orchestrator'
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If
                    activity: System.Activities.Statements.If
                    currentActivity: true
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      activity_typedef: "class If : Activity\r\n{\r\nInArgument<bool> Condition;\r\nActivity Then;\r\nActivity Else;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
  - id: test/05___UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    demo_id: test/05___UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    workflow_id: test
    activity_id: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    activity_fqn: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
    input:
      target_framework: Portable
      user_query: Reply with orchestrator documentation link
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply to Email
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        currentActivity: true
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Slack message with email subject
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      activity_typedef: "class ReplyToEmailConnections : Activity\r\n{\r\nInArgument<Office365Message> Email;\r\nbool UseSharedMailbox;\r\nInArgument<string> ReplyAs;\r\nInArgument<string> Body;\r\nAttachmentInputMode AttachmentInputMode;\r\nInArgument<IEnumerable<IResource>> Attachments;\r\nIEnumerable<InArgument<IResource>> ArgumentAttachments;\r\nInArgument<IEnumerable<string>> AttachmentPaths;\r\nIEnumerable<InArgument<string>> ArgumentAttachmentPaths;\r\nInArgument<string> NewSubject;\r\nInArgument<IEnumerable<string>> To;\r\nInArgument<IEnumerable<string>> Cc;\r\nInArgument<IEnumerable<string>> Bcc;\r\nInArgument<Importance> Importance;\r\nInArgument<bool> SaveAsDraft;\r\nInArgument<bool> ReplyToAll;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        AttachmentInputMode: Existing
        Importance: Normal
        ReplyToAll: 'False'
        SaveAsDraft: 'True'
        UseSharedMailbox: 'False'
        Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
        Email: '[[CurrentEmail]]'
  - id: test/06___UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@37a305b2-89b1-315d-b73f-1778839a6c47
    demo_id: test/06___UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@37a305b2-89b1-315d-b73f-1778839a6c47
    workflow_id: test
    activity_id: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@37a305b2-89b1-315d-b73f-1778839a6c47
    activity_fqn: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
    input:
      target_framework: Portable
      user_query: Send Slack message with email subject
      workflow:
        processName: RespondToDifferentEmails
        packages:
        - UiPath.IntegrationService.Activities, v[1.5.0]
        - UiPath.MicrosoftOffice365.Activities, v[2.6.21]
        trigger:
          thought: Manual Trigger
          activity: UiPath.Core.Activities.ManualTrigger
          params:
            Result: '[[Result]]'
        workflow:
        - thought: For each email in Outlook
          activity: UiPath.MicrosoftOffice365.Activities.Mail.ForEachEmailConnections
          params:
            AuthScopesInvalid: 'False'
            BrowserFolder: Inbox
            Importance: Any
            IncludeSubfolders: 'False'
            MarkAsRead: 'False'
            MaxResults: '100'
            OrderByDate: NewestFirst
            SelectionMode: Browse
            UnreadOnly: 'False'
            UseSharedMailbox: 'False'
            WithAttachmentsOnly: 'False'
            Length: '[[Length]]'
            Body:
              variables:
              - name: CurrentEmail
                type: UiPath.MicrosoftOffice365.Models.Office365Message
              - name: CurrentEmailIndex
                type: System.Int32
              Handler:
              - thought: If email subject contains 'robot'
                activity: System.Activities.Statements.If
                params:
                  Condition: '[[CurrentEmail.Subject.ToLower().Contains("robot")]]'
                  Then:
                  - thought: Reply with robot documentation link
                    activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                    params:
                      AttachmentInputMode: Existing
                      AttachmentsBackup:
                        StoredValue: Existing
                      Importance: Normal
                      ReplyToAll: 'False'
                      SaveAsDraft: 'True'
                      UseSharedMailbox: 'False'
                      Body: '[[string.Format("<P><SPAN>The documentation for Robot is here.</SPAN></P>")]]'
                      Email: '[[CurrentEmail]]'
                  Else:
                  - thought: If email subject contains 'orchestrator'
                    activity: System.Activities.Statements.If
                    params:
                      Condition: '[[CurrentEmail.Subject.ToLower().Contains("orchestrator")]]'
                      Then:
                      - thought: Reply with orchestrator documentation link
                        activity: UiPath.MicrosoftOffice365.Activities.Mail.ReplyToEmailConnections
                        params:
                          AttachmentInputMode: Existing
                          AttachmentsBackup:
                            StoredValue: Existing
                          Importance: Normal
                          ReplyToAll: 'False'
                          SaveAsDraft: 'True'
                          UseSharedMailbox: 'False'
                          Body: '[[string.Format("<P><SPAN>The documentation for Orchestrator is here.</SPAN></P>")]]'
                          Email: '[[CurrentEmail]]'
                      Else:
                      - thought: Send Message to Channel
                        activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
                        currentActivity: true
                        uiPathActivityTypeId: 37a305b2-89b1-315d-b73f-1778839a6c47
                        params:
                          channel: C01HCS3VAHY
                          send_as: bot
                          messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
                          Jit_send_message_to_channel_v2: '[[MessageSent]]'
                          out_ts: '[[MessageTimestamp]]'
        variables:
        - name: Length
          type: System.Int32
        - name: MessageSent
          type: send_message_to_channel_v2_Create
        - name: MessageTimestamp
          type: out_ts
        - name: Result
          type: CurrentJobInfo
      available_variables:
      - name: Result
        type: CurrentJobInfo
      - name: CurrentEmail
        type: UiPath.MicrosoftOffice365.Models.Office365Message
      - name: CurrentEmailIndex
        type: System.Int32
      - name: Length
        type: System.Int32
      - name: MessageSent
        type: send_message_to_channel_v2_Create
      - name: MessageTimestamp
        type: out_ts
      activity_typedef: "class ConnectorActivity : Activity\r\n{\r\n    InArgument<String> channel;\r\n    InArgument<String> messageToSend;\r\n    InArgument<String> send_as;\r\n    InArgument<String> fields;\r\n    InArgument<String> buttons;\r\n    InArgument<String> image;\r\n    InArgument<String> parse;\r\n    InArgument<Boolean> link_names;\r\n    InArgument<Boolean> unfurl_links;\r\n    InArgument<String> username;\r\n    InArgument<String> icon_emoji;\r\n    OutArgument<send_message_to_channel_v2_Create> Jit_send_message_to_channel_v2;\r\n    OutArgument<String> out_ts;\r\n}"
      additional_typedefs: "class Office365Message\r\n{\r\nstring MessageId;\r\nstring BodyPreview;\r\nFollowupFlag Flag;\r\nMessageImportance? Importance;\r\nbool IsRead;\r\nDateTime? LastModifiedDateTime;\r\nstring ParentFolderId;\r\nDateTime? ReceivedDateTime;\r\nDateTime? SentDateTime;\r\nstring WebLink;\r\nMailPriority Priority;\r\nIEnumerable<string> Categories;\r\nstring InternetMessageId;\r\nAttachmentCollection Attachments;\r\nstring Body;\r\nstring BodyAsHtml;\r\nstring Subject;\r\nstring FromDisplayName;\r\nstring FromAddress;\r\nstring SenderDisplayName;\r\nstring SenderAddress;\r\nMailAddressCollection ReplyToList;\r\nIEnumerable<string> ReplyToAddressList;\r\nMailAddressCollection To;\r\nIEnumerable<string> ToAddressList;\r\nMailAddressCollection CC;\r\nIEnumerable<string> CCAddressList;\r\nMailAddressCollection Bcc;\r\nIEnumerable<string> BccAddressList;\r\nint StandardAttachmentCount;\r\nIEnumerable<string> StandardAttachmentNames;\r\nint InlineAttachmentCount;\r\nIEnumerable<string> InlineAttachmentNames;\r\nint AttachmentCount;\r\nIEnumerable<string> AttachmentsNamesList;\r\nMailAddress From;\r\nMailAddress Sender;\r\nMailAddress ReplyTo;\r\nDeliveryNotificationOptions DeliveryNotificationOptions;\r\nEncoding SubjectEncoding;\r\nNameValueCollection Headers;\r\nEncoding HeadersEncoding;\r\nEncoding BodyEncoding;\r\nTransferEncoding BodyTransferEncoding;\r\nbool IsBodyHtml;\r\nAlternateViewCollection AlternateViews;}\r\nenum AttachmentInputMode\r\n{\r\nExisting,\r\nBuilder,\r\nUseSingle,\r\nFilePaths,\r\nFilePathsBuilder,}\r\nenum Importance\r\n{\r\nLow,\r\nNormal,\r\nHigh,}\r\ninterface IResource\r\n{\r\nstring MimeType;\r\nstring IconUri;\r\nstring FullName;\r\nstring ID;\r\nbool IsFolder;\r\nDateTime? CreationDate;\r\nDateTime? LastModifiedDate;\r\nDictionary<string, string> Metadata;}\r\nenum MailPriority\r\n{\r\nNormal,\r\nLow,\r\nHigh,}\r\nclass MailAddress\r\n{\r\nstring DisplayName;\r\nstring User;\r\nstring Host;\r\nstring Address;}\r\nenum DeliveryNotificationOptions\r\n{\r\nNone,\r\nOnSuccess,\r\nOnFailure,\r\nDelay,\r\nNever,}\r\nenum ItemSelectionMode\r\n{\r\nBrowse,\r\nEnterPath,}\r\nenum ImportanceFilter\r\n{\r\nAny,\r\nLow,\r\nNormal,\r\nHigh,}\r\nclass MailFilterCollection\r\n{\r\nMailFilterLogicalOperator LogicalOperator;\r\nList<MailFilterElement> Filters;}\r\nclass MailFilterElement\r\n{\r\nMailFilterField Criteria;\r\nMailStringFilterOperator StringOperator;\r\nMailTypeFilterOperator TypeOperator;\r\nMailDateFilterOperator DateOperator;\r\nMailType TypeValue;\r\nInArgument<string> InStringValue;\r\nInArgument<DateTime> DateValue;}\r\nenum MailFilterLogicalOperator\r\n{\r\nAnd,\r\nOr,}\r\nclass ActivityActionForEachEmailConnections1\r\n{\r\n    VariableDefinition<Office365Message> currentOffice365Message;\r\n    VariableDefinition<int> currentIndex;\r\n    Sequence Handler;}\r\nclass send_message_to_channel_v2_Create\r\n{\r\nList<blocks> blocks;\r\nicons icons;\r\nmessage message;\r\nmetadata metadata;\r\nresponse_metadata response_metadata;\r\nroot root;\r\nstring app_id;\r\nstring channel;\r\nbool? ok;\r\nstring subtype;\r\nstring thread_ts;\r\nstring ts;\r\nstring username;}\r\nclass blocks\r\n{\r\ntext text;\r\nstring block_id;\r\nstring type;}\r\nclass icons\r\n{\r\nstring emoji;}\r\nclass message\r\n{\r\nList<attachments> attachments;\r\nList<blocks> blocks;\r\nbot_profile bot_profile;\r\nicons icons;\r\nmetadata metadata;\r\nroot root;\r\nstring app_id;\r\nstring bot_id;\r\nstring subtype;\r\nstring team;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring user;\r\nstring username;}\r\nclass metadata\r\n{\r\nevent_payload event_payload;\r\nstring event_type;}\r\nclass response_metadata\r\n{\r\nList<string> warnings;}\r\nclass root\r\n{\r\nList<blocks1> blocks;\r\nicons icons;\r\nmetadata1 metadata;\r\nstring app_id;\r\nstring bot_id;\r\nbool? is_locked;\r\nstring latest_reply;\r\nint? reply_count;\r\nList<string> reply_users;\r\nint? reply_users_count;\r\nbool? subscribed;\r\nstring subtype;\r\nstring text;\r\nstring thread_ts;\r\nstring ts;\r\nstring type;\r\nstring username;}\r\nclass CurrentJobInfo\r\n{\r\nstring WorkflowName;\r\nstring ProcessName;\r\nstring ProcessVersion;\r\nstring RobotName;\r\nstring Key;\r\nstring TenantName;\r\nstring FolderName;\r\nstring UserEmail;\r\nPictureInPictureMode PictureInPictureMode;}"
    output:
      explanation: '...'
      configuration:
        channel: C01HCS3VAHY
        send_as: bot
        messageToSend: '[["I don''t have answer for this email: " + CurrentEmail.Subject]]'
        Jit_send_message_to_channel_v2: '[[MessageSent]]'
        out_ts: '[[MessageTimestamp]]'
