description: "Write the data table in the arguments to an Excel file"
targetFramework: Windows
process:
  processName: test_use_excel_business
  packages:
  - UiPath.Excel.Activities, v[2.23.2-preview]
  - UiPath.System.Activities, v[24.3.1]
  trigger:
    thought: "Manual Trigger"
    activity: UiPath.Core.Activities.ManualTrigger
  workflow:
  - thought: Protect Excel Sheet
    activity: UiPath.Excel.Activities.Business.ProtectSheetX
  - thought: Sequence
    activity: System.Activities.Statements.Sequence
    currentActivity: true
  arguments:
  - name: dataTable
    type: UiPath.Core.Activities.DataTable
    direction: In
