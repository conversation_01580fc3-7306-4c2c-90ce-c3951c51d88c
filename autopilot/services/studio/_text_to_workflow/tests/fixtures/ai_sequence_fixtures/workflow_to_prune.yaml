description: Send an Outlook email containing the message 'Hello, <PERSON>!' to <EMAIL>.
process:
  processName: Support Request Email
  workflow:
  - thought: Log Message
    activity: UiPath.Core.Activities.LogMessage
    params:
      Level: Info
      Message: '[["Before current activity"]]'
  - activity: System.Activities.Statements.Sequence
    thought: ''
    currentActivity: true
  - thought: Log Message
    activity: UiPath.Core.Activities.LogMessage
    params:
      Level: Info
      Message: '[["After current activity"]]'
