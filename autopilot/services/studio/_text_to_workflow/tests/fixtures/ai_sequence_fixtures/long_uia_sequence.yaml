userRequest: "Navigate to \u0027PRR User Login Screen\u0027. It is expected to: Should navigate to User login screen\r\ntype into email with \<EMAIL>\u0022. It is expected to: should enter into email input \<EMAIL>\u0022\r\nclick on \u0027Next\u0027 Button on User Login Screen. It is expected to: Should click on Next button on user login screen\r\ntype into Password with \u0022xyz\u0022. It is expected to: Should input into password field \u0022xyz\u0022\r\nClick on \u0027Create New RMA\u0027 Button. It is expected to: Should click on Create New RMA button\r\n\u0022Click on \u0027Verify\u0027 button on User Login Screen\u0022. It is expected to: Should click on Verify button on User Login Screen\r\nInput Requester\u0027s CCOID as \u0027grambo1980\u0027. It is expected to: Should input requester\u0027s CCOID \u0027grambo 1980\u0027\r\n\u0022Input \u0027start search\u0027 with \u0027DCH2330RHGP\u0027 and click on \u0027Next\u0027 button on Landing Page\u0022. It is expected to: should input in start search \u0027DCH2330RHGP and click on next button\r\n\u0022Click on \u0027search by\u0027 dropdown and select \u0027Start With Serial Number\u0027 on Landing Page\u0022. It is expected to: Should click on search by and select Start with serial number\r\nSelect Failure Category as \u0027Operation Failure\u0027 on Part Page. It is expected to: Should Select Failure category as Operation failure on part page\r\nSelect Failure Subcategory as \u0027HW Fail - Disk\u0027 On Part Page. It is expected to: Should select Failure subcategory as HW Fail - Disk\r\nInput Failure Description as \u0022Test autopilot\u0022 Part Page. It is expected to: should have in Failure Description \u0027TestAutopilot\u0027\r\nClick on \u0027Complete RMA for the Customer\u0027 button on Part Page. It is expected to: Should click on Complete RMA for the Customer button\r\nInput Acknowledgement Contact phone country code as \u0027\u002B1\u0027 on Site Information Page. It is expected to: Should input phone country code as \u002B1\r\nInput \u0027Acknowledgement Contact Alt Phone Country Code\u0027 as \u0027\u002B1\u0027 on Site Information Page. It is expected to: Alt country code should be \u002B1\r\nclick on \u0027Same as Acknowledgement Contact\u0027 Check box on site information page\u0022. It is expected to: Should enable Same As Acknowledgement Contact checkbox\r\nInput \u0027Street Address\u0027 as \u00271201 ROUTE DES CRATES - BP 39\u0027 on Site information Page. It is expected to: should input \u00221201 ROUTE DES CRATES - BP 39\u0022 in street address\r\nClick on \u0027Next\u0027 button on Site information Page. It is expected to: should click on next button\r\nClick on \u0027use standardized address\u0027 on site information page. It is expected to: clicks on Use standarized address\r\nSelect Delivery Details page Delivery Type as \u0027Advance Replacement - 10th Business Day (Downgrade)\u0027 on Delivery Details Page. It is expected to: Should select Advanced replacement - 10th Business Day (Downgrade) option\r\nSelect \u0027Service Level Variance Reason\u0027 as \u0027Downgrade Based on Product Availability\u0027. It is expected to: should select Service Level Variance Reason\r\nClick on \u0027Delivery Details Next\u0027 button on Delivery Details Page. It is expected to: should click on Delivery Details Next button\r\nClick on \u0027Submit\u0027 button on Review and Submit Page. It is expected to: should click on Submit button\r\nGet the RMA Submitted Message. It is expected to: should get the rma submitted message from the screen\r\nVerify that the RMA Submitted Message is equal to success"
expectedActivities:
- UiPath.UIAutomationNext.Activities.NApplicationCard
- UiPath.UIAutomationNext.Activities.NTypeInto
- UiPath.UIAutomationNext.Activities.NClick
- UiPath.UIAutomationNext.Activities.NSelectItem
- UiPath.Testing.Activities.VerifyExpression
