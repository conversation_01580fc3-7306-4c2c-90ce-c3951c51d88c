differences:
- fact: Added namespace imports for better code organization and readability
  source_quotes:
  - span: "+namespaceImports:"
  - span: "+- UiPath.MicrosoftOffice365.Files.Models"
  - span: "+- UiPath.Platform.ResourceHandling"
  - span: "+- UiPath.MicrosoftOffice365.Activities.Utilities"
  - span: "+- UiPath.Shared.Activities.Triggers"
  - span: "+- UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction"
  - span: "+- UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification"
  - span: "+- UiPath.IntelligentOCR.DataExtraction"
  - span: "+- UiPath.IntegrationService.Activities.SWEntities.C50C1A94AD7_Create_PreHire_Upload.Bundle"
- fact: Updated package versions and removed UiPath.DocumentUnderstanding.Activities
  source_quotes:
  - span: "- UiPath.DocumentUnderstanding.Activities, v2.6.0-preview"
  - span: "-- UiPath.IntegrationService.Activities, v1.4.0"
  - span: "-- UiPath.MicrosoftOffice365.Activities, v2.5.9"
  - span: "+ UiPath.MicrosoftOffice365.Activities, 2.6.21"
  - span: "+- UiPath.DocumentUnderstanding.Activities, 2.9.2"
  - span: "+- UiPath.IntegrationService.Activities, 1.5.0-beta.20240507.15"
- fact: Changed process name to be more specific
  source_quotes:
  - span: "-processName: Create Workday pre-hire when CV is uploaded to OneDrive folder"
  - span: "+processName: Create Workday Prehire from Uploaded CV"
- fact: Updated trigger activity parameters for OneDrive file upload
  source_quotes:
  - span: "+  connectorKey: uipath-microsoft-onedrive"
  - span: "-    BrowserDriveId: b!hrLTzom4skatE7U03PK7Yey7RYE9XphAsZjosaeKRS8JszrXf_9mTaMVXfUu6WPD"
  - span: "-    BrowserItemFriendlyName: OneDrive"
  - span: "+    BrowserItemFriendlyName: Documents"
  - span: "-    BrowserItemId: 01XIWSATF6Y2GOVW7725BZO354PWSELRRZ"
  - span: "+    BrowserItemId: 61C16C9E85AA78D6!102"
  - span: "+    BrowserSiteUrl: https://yourtenant.sharepoint.com/sites/YourSite"
  - span: "+    ConnectionId: 78df4a9d-38d4-476d-9786-847f5c69f45d"
- fact: Simplified and renamed variables for CV processing
  source_quotes:
  - span: "- name: JobData"
  - span: "-  type: UiPath.MicrosoftOffice365.Activities.Utilities.JobInformation"
  - span: "-- name: UploadedCVFile"
  - span: "-  type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem"
  - span: "-- name: ExtractionResults"
  - span: "-  type: UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.SWEntities.ExtendedExtractionResultsForDocumentData.IDCards.Bundle.IDCards>"
  - span: "-- name: OutExtractDataFromCvExtractorResult"
  - span: "-  type: UiPath.DocumentProcessing.Contracts.DataExtraction.ExtractorResult"
  - span: "+ name: CVFile"
  - span: "+  type: O365DriveRemoteItem"
  - span: "+- name: CVJobData"
  - span: "+  type: JobInformation"
  - span: "+- name: ExtractedData"
  - span: "+  type: IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>"
- fact: Updated document extraction activity to use GPT-based generative extraction
  source_quotes:
  - span: "- activity: UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>"
  - span: "+ activity: UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData<ExtendedExtractionResultsForDocumentData>"
  - span: "-    DocType: id_cards"
  - span: "+    DocType: Generative"
  - span: "+    GptPromptWithVariables:"
  - span: "+      Country: What is the birth country of the applicant"
  - span: "+      Email: What is the email of the applicant"
  - span: "+      FirstName: What is the first name of the applicant"
  - span: "+      LastName: What is the last name of the applicant"
  - span: "+    ProjectId: YourProjectId"
- fact: Updated Workday connector activity with new configuration and parameters
  source_quotes:
  - span: "+  configuration: dd3a9a2243247e2afc17f0a350ebc5df6c07f6edb6d08916e2545f35506c8d33"
  - span: "+  connectorKey: uipath-workday-workday"
  - span: "+  dynamicActivityDetails: 560bb3fbfc1155c97e912ff68b1749f401678702730bbd8348ec2bae108cd7fa"
  - span: "+  isConfigured: true"
  - span: "+  isDynamic: true"
  - span: "-    Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Email_Address: '[[\"string.Format(\"{0}.{1}@example.com\","
  - span: "-      extractionResults.Data.FirstName, extractionResults.Data.LastName)\"]]'"
  - span: "+    Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Email_Address: '[[ExtractedData.Data.Email.ToString()]]'"
  - span: "-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Country_Reference_sub_ID_sub_Attribute_Value: '[[ExtractionResults.Data.BirthPlace.ToString()]]'"
  - span: "+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Country_Reference_sub_ID_sub_Attribute_Value: '[[ExtractedData.Data.Country.ToString()]]'"
  - span: "-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_First_Name: '[[\"$\"{extractionResults.Data.FirstName}\"\"]]'"
  - span: "-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Last_Name: '[[\"$\"{extractionResults.Data.LastName}\"\"]]'"
  - span: "+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_First_Name: '[[ExtractedData.Data.FirstName.ToString()]]'"
  - span: "+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Last_Name: '[[ExtractedData.Data.LastName.ToString()]]'"
explanation: |
  The process has been updated to use more modern UiPath components and a GPT-based generative extraction method for CV data.
  The workflow now uses a specific OneDrive connector, extracts data using AI-powered prompts, and creates pre-hire record in Workday using the extracted data.
semantic_diff: true
