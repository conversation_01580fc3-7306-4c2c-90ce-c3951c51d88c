 @@ -1,3953  1,118891 @@
+namespaceImports:
+- UiPath.MicrosoftOffice365.Files.Models
+- UiPath.Platform.ResourceHandling
+- UiPath.MicrosoftOffice365.Activities.Utilities
+- UiPath.Shared.Activities.Triggers
+- UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction
+- UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification
+- UiPath.IntelligentOCR.DataExtraction
+- UiPath.IntegrationService.Activities.SWEntities.C50C1A94AD7_Create_PreHire_Upload.Bundle
  packages:
- UiPath.DocumentUnderstanding.Activities, v2.6.0-preview
-- UiPath.IntegrationService.Activities, v1.4.0
-- UiPath.MicrosoftOffice365.Activities, v2.5.9
-processName: Create Workday pre-hire when CV is uploaded to OneDrive folder
+ UiPath.MicrosoftOffice365.Activities, 2.6.21
+- UiPath.DocumentUnderstanding.Activities, 2.9.2
+- UiPath.IntegrationService.Activities, 1.5.0-beta.20240507.15
+processName: Create Workday Prehire from Uploaded CV
  trigger:
   activity: UiPath.MicrosoftOffice365.Activities.Files.Triggers.NewFileCreated
+  connectorKey: uipath-microsoft-onedrive
    params:
-    BrowserDriveId: b!hrLTzom4skatE7U03PK7Yey7RYE9XphAsZjosaeKRS8JszrXf_9mTaMVXfUu6WPD
      BrowserDriveName: OneDrive
-    BrowserItemFriendlyName: OneDrive
-    BrowserItemId: 01XIWSATF6Y2GOVW7725BZO354PWSELRRZ
-    FilterExpression: (ParentID=='01XIWSATF6Y2GOVW7725BZO354PWSELRRZ')&&(ParentDriveID=='b!hrLTzom4skatE7U03PK7Yey7RYE9XphAsZjosaeKRS8JszrXf_9mTaMVXfUu6WPD')
-    JobData: '[[JobData]]'
-    Result: '[[UploadedCVFile]]'
-  thought: Trigger when a new file is uploaded to OneDrive folder
+    BrowserItemFriendlyName: Documents
+    BrowserItemId: 61C16C9E85AA78D6!102
+    BrowserSiteUrl: https://yourtenant.sharepoint.com/sites/YourSite
+    ConnectionId: 78df4a9d-38d4-476d-9786-847f5c69f45d
+    JobData: '[[CVJobData]]'
+    Result: '[[CVFile]]'
+  thought: CV uploaded to OneDrive
  variables:
- name: JobData
-  type: UiPath.MicrosoftOffice365.Activities.Utilities.JobInformation
-- name: UploadedCVFile
-  type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem
-- name: ExtractionResults
-  type: UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.SWEntities.ExtendedExtractionResultsForDocumentData.IDCards.Bundle.IDCards>
-- name: OutExtractDataFromCvExtractorResult
-  type: UiPath.DocumentProcessing.Contracts.DataExtraction.ExtractorResult
+ name: CVFile
+  type: O365DriveRemoteItem
+- name: CVJobData
+  type: JobInformation
+- name: ExtractedData
+  type: IDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>
  workflow:
- activity: UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData<UiPath.IntelligentOCR.StudioWeb.Activities.DataExtraction.ExtendedExtractionResultsForDocumentData>
+ activity: UiPath.IntelligentOCR.StudioWeb.Activities.ExtractDocumentDataWithDocumentData<ExtendedExtractionResultsForDocumentData>
    params:
-    AutoValidationConfidenceThreshold: '0'
-    DocType: id_cards
-    ExtractionResults: '[[ExtractionResults]]'
-    ExtractorResult: '[[OutExtractDataFromCvExtractorResult]]'
-    FileInput: '[[UploadedCVFile]]'
-    TimeoutInSeconds: '3600'
-  thought: Extract data from CV
+    DocType: Generative
+    ExtractionResults: '[[ExtractedData]]'
+    FileInput: '[[CVFile]]'
+    GptPromptWithVariables:
+      Country: What is the birth country of the applicant
+      Email: What is the email of the applicant
+      FirstName: What is the first name of the applicant
+      LastName: What is the last name of the applicant
+    ProjectId: YourProjectId
+    TimeoutInSeconds: 3600
+  thought: Extract CV data
  - activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity
+  configuration: dd3a9a2243247e2afc17f0a350ebc5df6c07f6edb6d08916e2545f35506c8d33
+  connectorKey: uipath-workday-workday
+  dynamicActivityDetails: 560bb3fbfc1155c97e912ff68b1749f401678702730bbd8348ec2bae108cd7fa
+  isConfigured: true
+  isDynamic: true
    params:
-    Personal_Data_sub_Contact_Data_sub_Address_Data_sub_Address_Line_Data_sub_Attribute_Type: ADDRESS_LINE_1
-    Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Email_Address: '[["string.Format(\"{0}.{1}@example.com\",
-      extractionResults.Data.FirstName, extractionResults.Data.LastName)"]]'
-    Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Usage_Data_sub_Type_Data_sub_Primary_Attribute: 'True'
-    ? Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Usage_Data_sub_Type_Data_sub_Type_Reference_sub_ID_sub_Attribute_Value
-    : Home
-    ? Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Usage_Data_sub_Type_Data_sub_Type_Reference_sub_ID_sub_Attribute_type
-    : Communication_Usage_Type_ID
-    Personal_Data_sub_Contact_Data_sub_Phone_Data_sub_Phone_Device_Type_Reference_sub_ID_sub_Attribute_type: Phone_Device_Type_ID
-    Personal_Data_sub_Contact_Data_sub_Phone_Data_sub_Usage_Data_sub_Type_Data_sub_Type_Reference_sub_ID_sub_Attribute_type: Communication_Usage_Type_ID
-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Country_Reference_sub_ID_sub_Attribute_Value: '[[ExtractionResults.Data.BirthPlace.ToString()]]'
+    Personal_Data_sub_Contact_Data_sub_Email_Address_Data_sub_Email_Address: '[[ExtractedData.Data.Email.ToString()]]'
+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Country_Reference_sub_ID_sub_Attribute_Value: '[[ExtractedData.Data.Country.ToString()]]'
      Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Country_Reference_sub_ID_sub_Attribute_type: ISO_3166-1_Alpha-3_Code
-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_First_Name: '[["$\"{extractionResults.Data.FirstName}\""]]'
-    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Last_Name: '[["$\"{extractionResults.Data.LastName}\""]]'
-    Qualification_Data_sub_Education_sub_Education_Data_sub_Country_Reference_sub_ID_sub_Attribute_type: ISO_3166-1_Alpha-3_Code
-    Recruiting_Data_sub_Positions_Considered_for_Reference_sub_ID_sub_Attribute_type: Position_ID
-    UiPathActivityTypeId: e7e82870-b518-300d-87b2-0a2447a953bb
-  thought: Create pre-hire in Workday using the CV data
+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_First_Name: '[[ExtractedData.Data.FirstName.ToString()]]'
+    Personal_Data_sub_Name_Data_sub_Legal_Name_Data_sub_Name_Detail_Data_sub_Last_Name: '[[ExtractedData.Data.LastName.ToString()]]'
+  thought: Create pre-hire data in Workday using extracted data
+  uiPathActivityTypeId: e7e82870-b518-300d-87b2-0a2447a953bb
