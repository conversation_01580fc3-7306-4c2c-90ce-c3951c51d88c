input:
  description: Log Person Data
  workflow:
  - thought: Log Message
    activity: UiPath.Core.Activities.LogMessage
    id: LogMessage_1
    params:
      Level: '[[LogLevel.Info]]'
      Message: '[[string.Format("First Name: {0}\r\nLastName: {1}\r\nAge: {2}", firstName, lastName, age)]]'
  arguments:
  - direction: In
    name: firstName
    type: String
  - direction: In
    name: lastName
    type: String
  - direction: In
    name: age
    type: int
output:
  activityDescriptions:
  - id: LogMessage_1
    description: Log personal data
  activitySummaries:
  - id: LogMessage_1
    summary: Log the first name, last name and age
  workflowSummary: Logs a given first name, last name and age
  workflowFileName: Log_Person_Data