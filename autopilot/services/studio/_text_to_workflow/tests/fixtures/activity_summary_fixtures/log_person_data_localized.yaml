input:
  description: Log Person Data
  workflow:
  - thought: Log Message
    activity: UiPath.Core.Activities.LogMessage
    id: LogMessage_1
    params:
      Level: '[[LogLevel.Info]]'
      Message: '[[string.Format("First Name: {0}\r\nLastName: {1}\r\nAge: {2}", firstName, lastName, age)]]'
  arguments:
  - direction: In
    name: firstName
    type: String
  - direction: In
    name: lastName
    type: String
  - direction: In
    name: age
    type: int
output:
  activityDescriptions:
  - Protokolliere persönliche Daten
  - Protokolliere Vorname, Nachname und Alter
  - Logge Vorname, Nachname und Alter
  - Protokolliere einen gegebenen Vorname, Nachname und das Alter
  - Protokolliere den Vorname, Nachname und das Alter
  - Protokolliere den Vornamen, Nachnamen und das Alter
  - Protokolliere Personendaten mit Vorname, Nachname und Alter
  - Protokolliere Personendaten einschließlich Vorname, Nachname und Alter
  - Logge Personendaten
  - Benutzerinformationen protokollieren # Log user information
  activitySummaries:
  - Protokolliere den Vorname, Nachname und das Alter
  - Protokolliere Vorname, Nachname und Alter
  - Logge Vorname, Nachname und Alter
  - Protokolliere einen gegebenen Vorname, Nachname und das Alter
  - Protokolliere den Vorname, Nachname und das Alter
  - Protokolliere den Vornamen, Nachnamen und das Alter
  - Protokolliere persönliche Daten
  - Protokolliere Personendaten mit Vorname, Nachname und Alter
  - Protokolliere Personendaten einschließlich Vorname, Nachname und Alter
  - Protokolliere formatierte Personendaten
  - Logge Personendaten
  workflowSummary:
  - Protokolliert einen gegebenen Vorname, Nachname und das Alter
  - Protokolliert Vorname, Nachname und Alter
  - Loggt Vorname, Nachname und Alter
  - Protokolliert Vorname, Nachname und Alter
  - Protokolliere Personendaten mit Vorname, Nachname und Alter
  - Protokolliere Personendaten einschließlich Vorname, Nachname und Alter
  - Protokolliere formatierte Personendaten
  - Protokolliere persönliche Daten
  - Protokolliere den Vorname, Nachname und das Alter
  - Logge Personendaten
  - Benutzerdetails protokollieren # Log user information
  - Protokollnachricht, die Benutzerdetails anzeigt # Log message displaying user details
  - Protokolliert einen gegebenen Vorname, Nachname und das Alter # Logs a given first name, last name and age
  workflowFileName:
  - Protokolliere_Personen_Daten
  - Protokolliere_Vorname_Nachname_Alter
  - Logge_Vorname_Nachname_Alter
  - Protokolliere_einen_gegebenen_Vorname_Nachname_und_das_Alter
  - Protokolliere_den_Vorname_Nachname_und_das_Alter
  - Protokolliere_den_Vornamen_Nachnamen_und_das_Alter
  - Protokolliere_persoenliche_Daten
  - Protokolliere_Personendaten_mit_Vorname_Nachname_und_Alter
  - Protokolliere_Personendaten_einschliesslich_Vorname_Nachname_und_Alter
  - Protokolliere_formatierte_Personendaten
  - Log_Personendaten
  workflowShortDescription:
  - Protokolliert Vorname, Nachname und Alter
  - Loggt Vorname, Nachname und Alter
  - Protokolliere Personen Daten
  - Protokolliere Persoenliche Daten
  - Protokolliere Vorname, Nachname und Alter
  - Protokolliere den Vornamen, Nachnamen und das Alter
  - Protokolliere formatierte Personendaten
  - Logge Personendaten
  - Benutzerdetails protokollieren
