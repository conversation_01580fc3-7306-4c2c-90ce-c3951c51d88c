processName: Send an email when a file is added to OneDrive folder
packages:
- UiPath.MicrosoftOffice365.Activities, v2.5.9
trigger:
  thought: When a new file is created in OneDrive folder
  activity: UiPath.MicrosoftOffice365.Activities.Files.Triggers.NewFileCreated
  params:
    BrowserDriveId: 61c16c9e85aa78d6
    BrowserItemFriendlyName: ExampleFolder
    BrowserItemId: 61C16C9E85AA78D6!113
    FilterExpression: (ParentID=='61C16C9E85AA78D6!113')&&(ParentDriveID=='61c16c9e85aa78d6')
    JobData: '[[newFileCreatedJobData]]'
    Result: '[[AddedFile]]'
workflow:
- thought: Send Outlook email with URL to added OneDrive file
  activity: UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections
  params:
    AttachmentInputMode: Existing
    AttachmentsBackup:
      StoredValue: Existing
    Importance: Normal
    InputType: HTML
    InputTypeBackup:
      StoredValue: HTML
    SaveAsDraft: 'False'
    Subject: New file added on OneDrive
    Body: '[["New file added on OneDrive: " + AddedFile.Uri]]'
    To: '[[new[] { ToEmailAddress }]]'
variables:
- name: AddedFile
  type: UiPath.MicrosoftOffice365.Files.Models.O365DriveRemoteItem
- name: newFileCreatedJobData
  type: UiPath.MicrosoftOffice365.Activities.Utilities.JobInformation
arguments:
- direction: In
  name: ToEmailAddress
  type: System.String
