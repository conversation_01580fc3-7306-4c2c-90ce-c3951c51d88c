simple_function_input = {
    "parameters": [{"name": "b1", "type": "int", "direction": "in"}, {"name": "b2", "type": "int", "direction": "in"}],
    "number_of_tests": 40,
    "implementation_format": "Code",
    "implementation": """\
        /*
        * C# Program to Find the Sum of two Binary Numbers */
        using System;
        using System.Collections.Generic;
        using System.Linq;
        using System.Text;
        namespace ConsoleApplication
        {
            class Program
            {
                [Workflow]
                static void Execute(int b1, int b2)
                {
                    int i = 0, rem = 0;
                    int[] sum = new int[20];
                    while (b1 != 0 || b2 != 0)
                    {
                        sum[i++] = (b1 % 10 + b2 % 10 + rem) % 2;
                        rem = (b1 % 10 + b2 % 10 + rem) / 2;
                        b1 = b1 / 10;
                        b2 = b2 / 10;
                    }
                    if (rem != 0)
                        sum[i++] = rem;
                    --i;
                    Console.WriteLine("Sum of two binary numbers: ");
                    while (i >= 0)
                        Console.Write("{0}", sum[i--]);
                }
            }
        }""",
    "seed": 42,
    "deterministic_mode": True,
    "user_request_first_time": "Generate 40 test data",
    "user_request_second_time": "Generate another 10 test data",
    "current_data": "test_data:\n- b1: 0\n  b2: 0\n- b1: 0\n  b2: 1\n- b1: 1\n  b2: 0\n- b1: 1\n  b2: 1\n- b1: 10\n  b2: 11\n- b1: 101\n  b2: 110\n"
    "- b1: 111\n  b2: 1001\n- b1: 10000\n  b2: 11111\n- b1: 101010\n  b2: 110011\n- b1: 111000\n  b2: 1001001\n",
}

function_with_multiple_params_input = {
    "parameters": [
        {"name": "price", "type": "double", "direction": "in"},
        {"name": "quantity", "type": "int", "direction": "in"},
        {"name": "discount1", "type": "double", "direction": "in"},
        {"name": "discount2", "type": "double", "direction": "in"},
        {"name": "discount3", "type": "double", "direction": "in"},
        {"name": "discount4", "type": "double", "direction": "in"},
    ],
    "number_of_tests": 25,
    "implementation_format": "Code",
    "implementation": """\
        class Program
        {
            [Workflow]
            static void Execute(double price, int quantity, double discount1, double discount2, double discount3, double discount4)
            {
                double total = CalculateTotalPrice(
                    price,
                    quantity,
                    discount1,
                    discount2,
                    discount3,
                    discount4
                );
                Console.WriteLine("The total price of the items is: " + total);
            }

            static double CalculateTotalPrice(double price, int quantity, double discount1,
                                            double discount2, double discount3, double discount4)
            {
                double total = price * quantity;
                total -= total * discount1;
                total -= total * discount2;
                total -= total * discount3;
                total -= total * discount4;
                return total;
            }
        }""",
    "seed": 42,
    "deterministic_mode": True,
    "user_request_first_time": "Generate 25 test data",
    "user_request_second_time": "Generate another 10 test data",
    "current_data": "test_data:\n- discount1: 0.0\n  discount2: 0.0\n  discount3: 0.0\n  discount4: 0.0\n  price: 100.0\n  quantity: 5\n"
    "- discount1: 0.1\n  discount2: 0.0\n  discount3: 0.0\n  discount4: 0.0\n  price: 100.0\n  quantity: 5\n"
    "- discount1: 0.0\n  discount2: 0.2\n  discount3: 0.0\n  discount4: 0.0\n  price: 90.0\n  quantity: 5\n"
    "- discount1: 0.0\n  discount2: 0.0\n  discount3: 0.15\n  discount4: 0.0\n  price: 80.0\n  quantity: 5\n"
    "- discount1: 0.0\n  discount2: 0.0\n  discount3: 0.0\n  discount4: 0.25\n  price: 70.0\n  quantity: 5\n"
    "- discount1: 0.1\n  discount2: 0.2\n  discount3: 0.15\n  discount4: 0.35\n  price: 60.0\n  quantity: 5\n"
    "- discount1: 0.1\n  discount2: 0.2\n  discount3: 0.15\n  discount4: 0.2\n  price: 50.0\n  quantity: 5\n"
    "- discount1: 0.1\n  discount2: 0.2\n  discount3: 0.15\n  discount4: 0.25\n  price: 40.0\n  quantity: 0\n"
    "- discount1: 0.1\n  discount2: 0.2\n  discount3: 0.15\n  discount4: 0.25\n  price: 30.0\n  quantity: 0\n"
    "- discount1: 1.0\n  discount2: 1.0\n  discount3: 1.0\n  discount4: 1.0\n  price: 20.0\n  quantity: 5\n",
}

lm_yaml_input = {
    "parameters": [{"name": "in_hashFormula", "type": "string", "direction": "in"}],
    "number_of_tests": 10,
    "implementation_format": "LMYAML",
    "implementation": """
        processName: GetHashCode
        packages:
          - UiPath.Credentials.Activities, v[1.1.6479.13204]
          - UiPath.Excel.Activities, v[2.10.4]
          - UiPath.Mail.Activities, v[1.10.5]
          - UiPath.System.Activities, v[21.4.0]
          - UiPath.UIAutomation.Activities, v[21.4.3]
        workflow:
          - thought: Attach Browser SHA1 online
            displayName: Attach Browser SHA1 online
            activity: UiPath.Core.Activities.BrowserScope
            id: BrowserScope_1
            params:
              BrowserType: Chrome
              InformativeScreenshot: a6fbe98160da1866aa604a030736cc78
              Selector: <html app=\'chrome.exe\' title=\'SHA1 online\' />
              Body: null
        variables:
          - name: ContextTarget
            type: System.Object
            Handler:
              - thought: Type Into string to hash
                displayName: Type Into string to hash
                activity: UiPath.Core.Activities.TypeInto
                id: TypeInto_1
                params:
                  Activate: "True"
                  SimulateType: "True"
                  Target:
                    Id: null
                    __type: System.ComponentModel.GuidConverter
                    source: 74f6b95d-3a83-4f50-8d8f-e2eb1f0e9b5f
                    InformativeScreenshot: ae5339f5bdbccb77b4700609370fdaff
                    IsUsedAsImplementation: "False"
                    Selector: <webctrl tag=\'INPUT\' />
                    Text: "[[in_hashFormula]]"
              - thought: Click Hash button
                displayName: Click Hash button
                activity: UiPath.Core.Activities.Click
                id: Click_1
                params:
                  ClickType: CLICK_SINGLE
                  CursorPosition:
                    Position: Center
                    KeyModifiers: None
                    MouseButton: BTN_LEFT
                    Target:
                      Id: null
                      __type: System.ComponentModel.GuidConverter
                      source: 4b1df85e-330a-4693-a2e3-688d82402401
                      InformativeScreenshot: 0cff5e647ebbfffa1d610a2cb65c3275
                      IsUsedAsImplementation: "False"
                      Selector: <webctrl tag=\'INPUT\' type=\'submit\' />
              - thought: Get Text SHA1
                displayName: Get Text SHA1
                activity: UiPath.Core.Activities.GetValue
                id: GetValue_1
                params:
                  Target:
                    Id: null
                    __type: System.ComponentModel.GuidConverter
                    source: d657ca48-524c-438b-ba1f-404ae6322571
                    InformativeScreenshot: c243acd42c5d0d9a2bc9e50994171379
                    IsUsedAsImplementation: "False"
                    Selector: <webctrl tag=\'SPAN\' />
                    Value: "[[out_hashCode]]"
              - thought: Go Back
                displayName: Go Back
                activity: UiPath.Core.Activities.GoBack
                id: GoBack_1
                DisplayName: Do
        arguments:
          - direction: In
            name: in_hashFormula
            type: System.String
          - direction: Out
            name: out_hashCode
            type: System.String
    """,
    "seed": 42,
    "deterministic_mode": True,
    "user_request_first_time": "Generate 10 test data",
    "user_request_second_time": "Generate another 6 test data",
    "current_data": "test_data:\n- in_hashFormula: abc\n"
    "- in_hashFormula: '123'\n"
    "- in_hashFormula: abc123\n"
    "- in_hashFormula: '456'\n"
    "- in_hashFormula: ''\n"
    "- in_hashFormula: '   '\n"
    "- in_hashFormula: null\n"
    "- in_hashFormula: abc\n"
    "- in_hashFormula: abc\n"
    "- in_hashFormula: abc\n",
}
