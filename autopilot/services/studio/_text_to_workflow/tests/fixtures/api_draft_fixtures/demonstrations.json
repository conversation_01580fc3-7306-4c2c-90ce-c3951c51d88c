{"GetFreshServiceTicketsSample": {"mode": "workflow", "query": "Search for FreshService tickets based on an input query, if there are any tickets matching a PriotityLevel received as an argument,  then send a whatsapp message using Twilio with the tickets, else send a Slack message to a user.\n\nThe Twilio SenderPhoneNumber, ReceiverPhoneNumber, SlackUserId, SendSlackAsUserOrBot, TicketPriority, FreshServiceSearchTicketsQuery should be received as arguments", "plan": "1. Search Tickets based on provided query\n2. If Ticket priority == High\n  2.1. Send WhatsApp Message with hight priority ticket list\n3. Else\n  3.1. Send Message to User with low priority ticket list", "used_activities": ["UiPath.IntegrationService.Activities.Runtime.Activities.TwilioSend_WhatsApp_Message", "IfActivity", "UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User", "UiPath.IntegrationService.Activities.Runtime.Activities.FreshserviceSearch_Tickets"], "language": "jq", "solution_workflow": {"input": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"TicketPriority\": {\n      \"type\": \"string\",\n      \"description\": \"Urgency level for the Freshservice ticket (e.g., High, Medium, Low).\"\n    },\n    \"SendSlackAsUserOrBot\": {\n      \"type\": \"string\",\n      \"description\": \"Whether the Slack message is posted as the user or as a bot.\"\n    },\n    \"SenderPhoneNumber\": {\n      \"type\": \"string\",\n      \"description\": \"E.164‑formatted phone number of the SMS sender.\"\n    },\n    \"ReceiverPhoneNumber\": {\n      \"type\": \"string\",\n      \"description\": \"E.164‑formatted phone number of the SMS recipient.\"\n    },\n    \"SlackUserId\": {\n      \"type\": \"string\",\n      \"description\": \"Slack user ID to associate with the message.\"\n    },\n    \"FreshServiceSearchTicketsQuery\": {\n      \"type\": \"string\",\n      \"description\": \"Search query string used to look up tickets in Freshservice.\"\n    }\n  },\n  \"required\": [\n    \"TicketPriority\",\n    \"SendSlackAsUserOrBot\",\n    \"SenderPhoneNumber\",\n    \"ReceiverPhoneNumber\",\n    \"SlackUserId\",\n    \"FreshServiceSearchTicketsQuery\"\n  ],\n  \"additionalProperties\": false\n}", "root": {"thought": "Sequence", "activity": "Sequence", "id": "Sequence_1", "do": [{"thought": "Search Tickets based on provided query", "activity": "FreshserviceSearch_Tickets", "id": "query_tickets", "with": {"query": "${$workflow.input.FreshServiceSearchTicketsQuery}"}}, {"thought": "If Ticket priority == High", "activity": "If", "id": "If_1", "condition": "", "then": [{"thought": "Send WhatsApp Message with hight priority ticket list", "activity": "TwilioSend_WhatsApp_Message", "id": "curated_whatsapp_message", "with": {"body": "${\"Here are your high priority tickets: \\($context.outputs.If_1 | map([.subject, .description_text, .status, .priority, .id, .is_escalated] | join(\", \")) | join(\" | \"))\"}", "from": "${$workflow.input.SenderPhoneNumber}", "to": "${$workflow.input.ReceiverPhoneNumber}"}}], "else": [{"thought": "Send Message to User with low priority ticket list", "activity": "SlackSend_Message_to_User", "id": "send_message_to_user_v2", "with": {"send_as": "${$workflow.input.SendSlackAsUserOrBot}", "messageToSend": "${\"Here are your low priority tickets: \\($context.outputs.If_1 | map([.subject, .description_text, .status, .priority, .id, .is_escalated] | join(\", \")) | join(\" | \"))\"}", "channel": "${$workflow.input.SlackUserName}"}}]}]}}}, "CancelLatestTimeOffRequest": {"mode": "workflow", "query": "Get All BambooHR Time Off Requests for 'EmployeeId' between 'StartDate' and 'EndDate' and cancel the last one in the retrieved list", "plan": "1. Get Time off Requests\n2. Change Time off Request Status", "used_activities": ["UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests", "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRChange_Time_off_Request_Status"], "language": "jq", "solution_workflow": {"input": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"BambooUserId\": {\n      \"type\": \"string\"\n    },\n    \"StartDate\": {\n      \"type\": \"string\",\n      \"default\": \"2025-01-01\"\n    },\n    \"EndDate\": {\n      \"type\": \"string\",\n      \"default\": \"2025-12-31\"\n    }\n  },\n  \"required\": [\n    \"BambooUserId\",\n    \"StartDate\",\n    \"EndDate\"\n  ],\n  \"additionalProperties\": false\n}", "root": {"thought": "Sequence", "activity": "Sequence", "id": "Sequence_1", "do": [{"thought": "Get Time off Requests", "activity": "BambooHRGet_Time_off_Requests", "id": "get_time_off_requests", "with": {"start": "${$workflow.input.StartDate}", "end": "${$workflow.input.EndDate}", "employeeId": "${$workflow.input.BambooUserId}"}}, {"thought": "Change Time off Request Status", "activity": "BambooHRChange_Time_off_Request_Status", "id": "update_time_off_status", "with": {"id": "${$context.outputs.get_time_off_requests.content.at(-1).id}", "start_date": "${$context.outputs.get_time_off_requests.content.at(-1).start}", "end_date": "${$context.outputs.get_time_off_requests.content.at(-1).end}", "status": "cancelled"}}]}}}, "Count GitHub commits made by a specific user": {"mode": "workflow", "query": "Get via an HTTP Request the Github commits made by a specific user to a specific repository. If the user did not make any commits to the repository, return a message indicating that.", "language": "js", "plan": "1. Get GitHub Repository commits using HTTP Request\n2. If the specified github user made any commits to the given repository\n  2.1. Return message - number of commits made by the user\n3. Else\n  3.1. Return message - no commits made by user", "used_activities": ["UiPath.IntegrationService.Activities.Runtime.Activities.GitHub_HTTP_Request"], "solution_workflow": {"input": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"GitHubUser\": {\n      \"type\": \"string\"\n    },\n    \"GithubRepository\": {\n      \"type\": \"string\"\n    }\n  },\n  \"required\": [\n    \"GitHubUser\",\n    \"GithubRepository\"\n  ],\n  \"additionalProperties\": false\n}", "root": {"thought": "Sequence", "activity": "Sequence", "id": "Sequence_1", "do": [{"thought": "Get GitHub Repository commits using HTTP Request", "activity": "GitHub_HTTP_Request", "id": "GitHub_HTTP_Request_1", "with": {"method": "${\"GET\"}", "url": "${[ \"/repos/\", $workflow.input.GithubRepository, \"/commits\" ].join('')}"}}, {"thought": "If the specified github user made any commits to the given repository", "activity": "If", "id": "If_1", "condition": "${$context.outputs.GitHub_HTTP_Request_1.content.filter(x => x.commit.author.name == $workflow.input.GitHubUser).length > 0}", "then": [{"thought": "Return message - number of commits made by the user", "activity": "Response", "id": "Response_1", "type": "SUCCESS", "response_message": "${\"The user \"+$workflow.input.GitHubUser +\" made \"+ $context.outputs.GitHub_HTTP_Request_1.content.filter(x => x.commit.author.name == $workflow.input.GitHubUser).length+ \" commits to GitHub repository \"+$workflow.input.GithubRepository}"}], "else": [{"thought": "Return message - no commits made by user", "activity": "Response", "id": "Response_2", "type": "SUCCESS", "response_message": "${\"The user \"+$workflow.input.GitHubUser + \" did not make any commits to the GitHub repository \"+ $workflow.input.GithubRepository}"}]}]}}}}