{"input": "{\"type\": \"object\", \"properties\": {\"OldEmailAddress\": {\"type\": \"string\"}, \"NewEmailAddress\": {\"type\": \"string\"}}}", "root": {"thought": "Sequence", "activity": "Sequence", "id": "Sequence_1", "do": [{"thought": "Search for NetSuite contact using the old email address", "activity": "Oracle_NetSuiteExecute_SuiteQL_Query", "id": "executeSuiteQL_2", "with": {"q": "${\"SELECT id FROM contact where email = '\"+$workflow.input.ContactEmailAddress+\"'\"}"}}, {"thought": "If one NetSuite contact with the given old email address is found", "activity": "If", "id": "If_1", "condition": "${$context.outputs.executeSuiteQL_2.content.length == 1}", "then": [{"thought": "Update the NetSuite contact with the new email address", "activity": "Oracle_NetSuiteUpdate_Contact", "id": "CuratedUpdateContact_3", "with": {"curatedContactId": "${$context.outputs.executeSuiteQL_2.content[0].id}", "email": "${$workflow.input.NewEmailAddress}"}}], "else": [{"thought": "Return multiple or no contacts found error", "activity": "Response", "id": "Response_1", "type": "FAILURE", "response_message": "${\"Zero or more than one contacts found with email address: \"+$workflow.input.OldEmailAddress}"}]}]}}