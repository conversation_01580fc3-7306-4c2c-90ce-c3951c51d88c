fix_expression_input = {
    "availableVariables": [{"name": "valueList", "type": "List(Of Double)"}],
    "expressionTypeDefinition": "Double",
    "expressionLanguage": "vbnet",
    "additionalTypeDefinitions": "",
    "currentExpression": "valueList.Avrage()",
    "benchmarkExpression": "valueList.Average()",
    "currentError": (
        "Compiler error(s) encountered processing expression "
        "\"valueList.Avrage()\".(1,91): error CS1061: 'int[]' does not contain "
        "a definition for 'Avrage' and no accessible extension method "
        "'Avrage' accepting a first argument of type 'int[]' could be found (are "
        "you missing a using directive or an assembly reference?)"
    ),
}
