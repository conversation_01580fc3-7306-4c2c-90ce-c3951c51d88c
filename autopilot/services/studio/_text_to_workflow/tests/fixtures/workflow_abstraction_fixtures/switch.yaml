thought: Switch based on integer value
activity: System.Activities.Statements.Switch<System.Int32>
params:
  Expression: '[[ValueINT]]'
  Cases:
    1:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: '[[Info]]'
        Message: '[["First"]]'
    2:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: '[[Info]]'
        Message: '[["Second"]]'
    3:
    - thought: Log Message
      activity: UiPath.Core.Activities.LogMessage
      params:
        Level: '[[Info]]'
        Message: '[["Third"]]'
