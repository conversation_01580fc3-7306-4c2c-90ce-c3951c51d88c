agent_evaluation_input = {
    "agent": {
        "name": "Slack bot",
        "description": "Sending slack messages",
        "systemPrompt": "You're a helpful agent",
        "userPrompt": "Use the send message tool to tell scott that every little thing is going to be alright.  ",
        "inputSchema": {"type": "jsonSchema", "schema": {"type": "object", "properties": {}, "required": []}},
        "outputSchema": {"type": "jsonSchema", "schema": {"type": "object", "properties": {"output": {"type": "string"}}}},
        "tools": [{"name": "Send_Message_to_User", "description": "Send a message to an individual user"}],
        "availableModels": ["gpt-4o-mini-2024-07-18"],
        "datasets": [],
    }
}
