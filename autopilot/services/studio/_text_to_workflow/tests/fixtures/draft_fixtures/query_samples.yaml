basic_deserialization:
  target_framework: Portable
  query: Deserialize a JSON string into a JSON array and store it in a variable.
  mode: sequence
  retrieved_activities:
  - UiPath.Web.Activities.DeserializeJson
  - UiPath.Web.Activities.DeserializeJsonArray
  retrieved_triggers: []
  demonstrations:
  - Portable/static/DivisibleByFourChecker/03__0_0_DivisibleByFourChecker.yaml
  - Portable/uia/[UIA] API Fetch/02__0_0_[UIA] API Fetch.yaml
  - Portable/train/StudioDesktop_ExtractCustomerDataFromPDFs_ScrapeProductNamesAndPrices/02__1_1_StudioDesktop_ExtractCustomerDataFromPDFs_ScrapeProductNamesAndPrices.yaml
  - Portable/train/StudioDesktop_WEBAPIExample_OAUTH/04__1_1_StudioDesktop_WEBAPIExample_OAUTH.yaml
  - Portable/train/StudioDesktop_fixes_20240412_[Client] Weather Forecast Slack Notifier/04__2_2_StudioDesktop_fixes_20240412_[Client] Weather Forecast Slack Notifier.yaml
  workflow:
    trigger:
      thought: Manual Trigger
      activity: UiPath.Core.Activities.ManualTrigger
    workflow:
    - thought: <sequence generation insert locus>
      activity: System.Activities.Statements.Sequence
      currentActivity: true
  solution:
    trigger:
    activities:
    - UiPath.Web.Activities.DeserializeJsonArray
gsuite_trigger:
  target_framework: Portable
  query: Summarize new Gmail email using OpenAI and share the summary via a Slack channel
  mode: workflow
  retrieved_activities:
  - UiPath.GSuite.Activities.DownloadEmailConnections
  - UiPath.GSuite.Activities.DownloadAttachmentsConnections
  - UiPath.GSuite.Activities.GetNewestEmailConnections
  - UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesSummarize_Text
  - UiPath.GSuite.Activities.ApplyEmailLabelsConnections
  - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_Channel
  retrieved_triggers:
  - UiPath.GSuite.Activities.Gmail.Triggers.EmailSent
  - UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
  demonstrations:
  - Portable/static/DivisibleByFourChecker/02__3_3_DivisibleByFourChecker.yaml
  - Portable/uia/[UIA] UIA Form Input.yaml
  - Portable/train/StudioDesktop_fixes_20240403_Get Outlook email gpt summary on slack channel/05__1_2_StudioDesktop_fixes_20240403_Get Outlook email gpt summary on slack channel.yaml
  - Portable/train/StudioDesktop_fixes_20240412_[Client] Summarize Email And Post On Slack.yaml
  - Portable/train/StudioDesktop_fixes_20240412_[Client] Summarize Outlook Emails And Send The Summary To Slack.yaml
  - Portable/train/StudioDesktop_fixes_20240408_[Confluence] Everyday summarize all my unread emails with openai and send me a slack message/05__1_2_StudioDesktop_fixes_20240408_[Confluence] Everyday summarize all my unread emails with openai and send me a slack message.yaml
  - Portable/train/StudioDesktop_fixes_20240403_Get argument gmail summary on slack channel.yaml
  - Portable/train/StudioDesktop_fixes_20240403_Get gmail gpt summary on slack channel.yaml
  - Portable/train/StudioDesktop_negulescu_20241112_Summarize salesforce opportunity and notify on slack.yaml
  solution:
    trigger: UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
    activities:
    - UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesSummarize_Text
    - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_Channel
