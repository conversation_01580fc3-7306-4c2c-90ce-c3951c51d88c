from pydantic.json_schema import GenerateJsonSchema, JsonSchemaMode, JsonSchemaValue
from pydantic_core import core_schema

from services.studio._text_to_workflow.models.output_parsers import MinifiedYamlOutputParser

ACTIVITY_DEFINITION_NAME = "ApiWorkflowActivity"
ACTIVITY_REFERENCE = {"$ref": f"#/$defs/{ACTIVITY_DEFINITION_NAME}"}

ACTIVITY_BLOCK_PROPERTIES: dict[str, list[str]] = {
    "If": ["then", "else"],
    "TryCatch": ["try"],
    "ForEach": ["do"],
    "Sequence": ["do"],
    "Catch": ["do"],
}


class ApiWorkflowSchemaGenerator(GenerateJsonSchema):
    """
    Custom schema generator that injects a reusable Union of Activities definitions into the JSON schema.
    This is used to avoid having to define the Activity union in every scope property.
    """

    injected_activity: bool = False

    def generate(self, schema: core_schema.CoreSchema, mode: JsonSchemaMode = "validation") -> JsonSchemaValue:
        # This method is the entry point; we override it to inject Activity once
        json_schema = super().generate(schema, mode)

        for scope_properties in json_schema["$defs"].values():
            if "title" not in scope_properties or scope_properties["title"] not in ACTIVITY_BLOCK_PROPERTIES.keys():
                continue

            for property_name in ACTIVITY_BLOCK_PROPERTIES[scope_properties["title"]]:
                if ACTIVITY_DEFINITION_NAME not in json_schema["$defs"]:
                    # inject the Activity definition into the schema
                    json_schema["$defs"] = {ACTIVITY_DEFINITION_NAME: scope_properties["properties"][property_name]["items"]} | json_schema["$defs"]

                # replace the Activity definition with a reference to the injected Activity definition
                scope_properties["properties"][property_name]["items"] = ACTIVITY_REFERENCE.copy()

        return json_schema


class APIWorkflowYamlOutputParser(MinifiedYamlOutputParser):
    def get_model_json_schema(self) -> dict:
        # Copy schema to avoid altering original Pydantic schema.
        return {k: v for k, v in self.pydantic_object.model_json_schema(schema_generator=ApiWorkflowSchemaGenerator).items()}
