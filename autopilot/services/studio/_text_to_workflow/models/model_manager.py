import pathlib

import langchain_aws
import langchain_core
import langchain_core.language_models
import langchain_google_vertexai

from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel, RerankingModel
from services.studio._text_to_workflow.utils.inference import llm_gateway_model, llm_schema
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

MAX_MODEL_TOKENS = 65536

MODEL_MAX_OUTPUT_TOKENS_CEILING = {
    "gpt-4o-mini-2024-07-18": 16384,
    "gemini-2.0-flash-001": 8192,
    "gpt-4o-2024-08-06": 16384,
}


class ModelConfigurationException(Exception):
    """Exception raised for errors in model configuration."""

    pass


class ModelManager:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModelManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not ModelManager._initialized:
            config_file = pathlib.Path(__file__).parent / "model_config.yaml"
            self.config = yaml_load(config_file)
            ModelManager._initialized = True

    # New helper to get a merged configuration for generation models.
    def get_llm_config(self, use_case: str) -> dict:
        if use_case in self.config:
            config = self.config[use_case]
        else:
            raise ModelConfigurationException(f"LLM model configuration for '{use_case}' not found in config file.")

        # If this configuration uses inheritance, merge the parent (base) configuration.
        if "inherits" in config:
            parent_key = config["inherits"]
            parent_config = self.config.get(parent_key)
            if not parent_config:
                raise ModelConfigurationException(f"Parent configuration '{parent_key}' not found for '{use_case}'.")
            # Merge the parent's default parameters with the child's override parameters.
            merged = {**parent_config.get("params", {}), **config.get("params", {})}

            # Set the deployment name from the child's "name" (if provided)
            merged["deployment_name"] = config["name"] if "name" in config else parent_config["name"]

            # Remove null values from the merged configuration
            merged = {k: v for k, v in merged.items() if v is not None}

            # Set the model type from the parent's configuration.
            merged["type"] = parent_config.get("type")

            if "name" not in config:
                if "name" in parent_config:
                    merged["name"] = parent_config["name"]
                else:
                    raise ModelConfigurationException(f"LLM model configuration for '{use_case}' does not have a 'name' key.")
            return merged
        else:
            # For configurations without inheritance, return as-is.
            if "name" not in config or "type" not in config:
                raise ModelConfigurationException(f"LLM model configuration for '{use_case}' does not have a 'name' or 'type' key.")

            # Create a merged config similar to inherited case
            merged = {**config.get("params", {}), "deployment_name": config["name"], "type": config["type"], "name": config["name"]}

            # Remove null values
            merged = {k: v for k, v in merged.items() if v is not None}

            return merged

    def get_llm_model(
        self, use_case: str | ModelOptions, feature_type: llm_schema.ConsumingFeatureType, forced_context: RequestContext | None = None
    ) -> langchain_core.language_models.BaseChatModel:
        """
        Retrieve an LLM model configured for generation use cases.
        `use_case` can be a key such as "generation" or a specific key like "workflow_generation_model".
        `feature_type` is expected to be a value from llm_schema.ConsumingFeatureType.
        """
        if isinstance(use_case, dict) and "model_name" in use_case:
            config = self.get_llm_config(use_case["model_name"])
            if "seed" in use_case:
                config["seed"] = use_case["seed"]
        else:
            config = self.get_llm_config(use_case)

        # Remove the type and max_total_tokens from the config,
        # as they are non-standard keys and would otherwise be passed to the LLM model
        model_type = config.pop("type")
        max_total_tokens = config.pop("max_total_tokens", None)
        additional_headers = config.pop("additional_headers", None)

        model = ModelManager._build_llm_model(model_type, config, feature_type, additional_headers, forced_context)

        if max_total_tokens is not None:
            model.max_total_tokens = max_total_tokens

        return model

    def get_embeddings_model(self, embedding_key: str = "embedding_model") -> EmbeddingModel:
        """
        Retrieve an embeddings model using the given key from the configuration.
        The default key is 'embedding_model'; other examples include 'activities_embedding_model'.
        """
        if embedding_key not in self.config:
            raise ModelConfigurationException(f"Embedding model configuration '{embedding_key}' not found in config file.")
        return EmbeddingModel(self.config[embedding_key])

    def get_reranking_model(self, reranking_key: str = "rerank_model") -> RerankingModel:
        """
        Retrieve the reranking model with the default configuration.
        """
        if reranking_key not in self.config:
            raise ModelConfigurationException(f"Reranking model configuration '{reranking_key}' not found in config file.")
        return RerankingModel(self.config[reranking_key])

    @staticmethod
    def _build_llm_model(
        model_type: str,
        model_config: dict,
        feature_type: llm_schema.ConsumingFeatureType,
        additional_headers: dict | None,
        forced_context: RequestContext | None = None,
    ) -> langchain_core.language_models.BaseChatModel:
        if model_type == "azureopenai":
            return llm_gateway_model.LLMGatewayModel(feature_type=feature_type, context=forced_context, additional_headers=additional_headers, **model_config)
        elif model_type == "googlevertexai":
            if settings.USE_LLM_GATEWAY:
                return llm_gateway_model.LLMGatewayNormalizedModel(
                    feature_type=feature_type, context=forced_context, additional_headers=additional_headers, **model_config
                )
            else:
                return llm_gateway_model.LLMGeminiModel(
                    feature_type=feature_type, context=forced_context, additional_headers=additional_headers, **model_config
                )
        elif model_type == "anthropic":
            if settings.USE_LLM_GATEWAY:
                return llm_gateway_model.LLMGatewayNormalizedModel(
                    feature_type=feature_type, context=forced_context, additional_headers=additional_headers, **model_config
                )
            else:
                return llm_gateway_model.LLMBedrockModel(
                    feature_type=feature_type, context=forced_context, additional_headers=additional_headers, **model_config
                )
        else:
            raise ModelConfigurationException(f"Unsupported LLM model type: {model_type} for feature type: {feature_type.name}")


# Add extension methods to BaseChatModel
def _add_extensions():
    def has_max_total_tokens(self: langchain_core.language_models.BaseChatModel):
        return hasattr(self, "_ext_max_total_tokens")

    """Gets/sets the maximum total tokens (input + output) allowed for the model.

    Returns:
        int: The maximum total tokens allowed for the model.

    Note:
        This limit is used to determine if the model can handle the input and output at runtime.
    """

    @property
    def max_total_tokens(self: langchain_core.language_models.BaseChatModel):
        return getattr(self, "_ext_max_total_tokens", MAX_MODEL_TOKENS)

    @max_total_tokens.setter
    def max_total_tokens(self: langchain_core.language_models.BaseChatModel, value: int):
        self._ext_max_total_tokens = value

    def has_max_model_tokens(self: langchain_core.language_models.BaseChatModel):
        if isinstance(self, llm_gateway_model.LLMGatewayModel) or isinstance(self, llm_gateway_model.LLMGatewayNormalizedModel):
            return "max_tokens" in self.model_kwargs or self.max_tokens is not None
        elif isinstance(self, langchain_google_vertexai.ChatVertexAI):
            return self.max_output_tokens is not None
        else:
            return False

    """Gets/sets the maximum output tokens for the model.

    Returns:
        int: The maximum output tokens allowed for the model.

    Note:
        This limit is used to determine if the model can handle the output at runtime.
    """

    @property
    def max_model_tokens(self: langchain_core.language_models.BaseChatModel):
        if isinstance(self, llm_gateway_model.LLMGatewayModel) or isinstance(self, llm_gateway_model.LLMGatewayNormalizedModel):
            if has_max_model_tokens(self):
                if "max_tokens_to_sample" in self.model_kwargs and self.model_name is not None and self.model_name.startswith("anthropic"):
                    return self.model_kwargs["max_tokens_to_sample"]
                elif "max_tokens" in self.model_kwargs:
                    return self.model_kwargs["max_tokens"]
            return self.max_tokens
        elif isinstance(self, langchain_google_vertexai.ChatVertexAI):
            return self.max_output_tokens
        elif isinstance(self, langchain_aws.ChatBedrockConverse):
            return self.max_tokens
        else:
            return None

    @max_model_tokens.setter
    def max_model_tokens(self: langchain_core.language_models.BaseChatModel, value: int):
        if isinstance(self, llm_gateway_model.LLMGatewayModel) or isinstance(self, llm_gateway_model.LLMGatewayNormalizedModel):
            if self.model_name is not None and not self.model_name.startswith("o") and not self.model_name.startswith("anthropic"):
                self.model_kwargs["max_tokens"] = value
                self.max_tokens = value
                self._serialized["kwargs"]["model_kwargs"]["max_tokens"] = value
            if self.model_name is not None and self.model_name.startswith("anthropic"):
                self.extra_body = {"max_tokens": value}
                self.max_tokens = value
        elif isinstance(self, langchain_google_vertexai.ChatVertexAI):
            self.max_output_tokens = value
            self._serialized["kwargs"]["max_output_tokens"] = value
        elif isinstance(self, langchain_aws.ChatBedrockConverse):
            self.max_tokens = value
            self._serialized["kwargs"]["max_tokens"] = value

    # Add the methods to the class
    langchain_core.language_models.BaseChatModel.max_total_tokens = max_total_tokens
    langchain_core.language_models.BaseChatModel.has_max_total_tokens = has_max_total_tokens
    langchain_core.language_models.BaseChatModel.max_model_tokens = max_model_tokens
    langchain_core.language_models.BaseChatModel.has_max_model_tokens = has_max_model_tokens


# Call the function to add extensions
_add_extensions()
