{
  "UseDeterministicMode": true,
  "Seed": 42,
  "Urls": {
    "UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.WorkflowGeneration.Models.WorkflowGenerationRequestModel": "http://localhost:5002/v2/generate-workflow",
    "UiPath.Studio.Plugin.Workflow.Autopilot.SequenceGeneration.Models.SequenceGenerationRequestModel": "http://localhost:5002/v2/generate-sequence",
    "UiPath.Studio.Plugin.Workflow.Autopilot.CodeGeneration.Models.CodeGenerationRequestData": "http://localhost:5002/v2/generate-code",    
    "UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.TestDataGeneration.Models.AutopilotDataVariationRequest": "http://localhost:5002/v2/generate-testdata",
	  "UiPath.Studio.Plugin.Workflow.Autopilot.WorkflowFix.Models.WorkflowFixRequestModel": "http://localhost:5002/v2/fix-workflow",
	  "UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.ActivitySummarization.Models.ActivitySummarizationRequestData": "http://localhost:5002/v2/summarize-workflow",
	  "UiPath.Studio.Plugin.Workflow.Autopilot.ConfigureActivity.Models.ConfigureActivityRequest": "http://localhost:5002/v2/configure-activity",
	  "UiPath.Studio.Plugin.Workflow.Autopilot.ExpressionGeneration.Models.FixExpressionRequest": "http://localhost:5002/v2/fix-expression",
    "UiPath.Studio.Plugin.Workflow.Autopilot.ExpressionGeneration.Models.GenerateExpressionRequest": "http://localhost:5002/v2/generate-expression",
    "UiPath.Studio.Plugin.Workflow.Autopilot.WorkflowAssistant.Services.Models.WorkflowAssistantRequest": "http://localhost:5002/v2/assistants/workflow",
  }
}