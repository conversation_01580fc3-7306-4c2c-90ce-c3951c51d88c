{"UseDeterministicMode": true, "Seed": 42, "Urls": {"UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.WorkflowGeneration.Models.WorkflowGenerationRequestModel": "http://localhost:5002/api-automation", "UiPath.Studio.Plugin.Workflow.Autopilot.SequenceGeneration.Models.SequenceGenerationRequestModel": "http://localhost:5002/generate-sequence", "UiPath.Studio.Plugin.Workflow.Autopilot.CodeGeneration.Models.CodeGenerationRequestData": "http://localhost:5002/code-generation", "UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.TestDataGeneration.Models.AutopilotDataVariationRequest": "http://localhost:5002/testdata-generation", "UiPath.Studio.Plugin.Workflow.Autopilot.WorkflowFix.Models.WorkflowFixRequestModel": "http://localhost:5002/v2/fix-workflow", "UiPath.Studio.Plugin.Workflow.Autopilot.Contracts.ActivitySummarization.Models.ActivitySummarizationRequestData": "http://localhost:5002/activity-summary", "UiPath.Studio.Plugin.Workflow.Autopilot.ConfigureActivity.Models.ConfigureActivityRequest": "http://localhost:5002/activity-configuration", "UiPath.Studio.Plugin.Workflow.Autopilot.ExpressionGeneration.Models.FixExpressionRequest": "http://localhost:5002/fix-expression", "UiPath.Studio.Plugin.Workflow.Autopilot.ExpressionGeneration.Models.GenerateExpressionRequest": "http://localhost:5002/expression-generation"}}