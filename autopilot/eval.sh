#!/bin/bash
cd "$(dirname "$0")"

date_time_string=$(date +"%Y_%m_%d_%H_%M_%S")

rm -rf .data/eval_results || mkdir -p .data/eval_results

python ./autopilot/services/_text_to_workflow/workflow_generation/main.py bt --subset test --target-framework Portable --results-file .data/eval_results/WorkflowGenerationPortable_${date_time_string}.html
# TODO: Enable when workflow validation is out of process
# python ./studio/text_to_workflow/workflow_generation/main.py test --subset test --target-framework Windows --mode sequence --results-file .data/eval_results/SequenceGenerationWindows_${date_time_string}.html
