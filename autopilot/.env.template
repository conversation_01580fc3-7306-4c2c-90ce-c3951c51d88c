# Use the below template to create your .env file.
# Important:
#   Whenever you update your .env file, you must reload
#   your terminal to see the changes - close all VS Code
#   or Cursor terminal windows and reopen them

# Development
UV_INDEX_UIPATH_PASSWORD=<your-azure-devops-access-token>

# Application
# The port on which the autopilot service will run.
AUTOPILOT_PORT=5002
# The environment name.
ENV_NAME=staging
# The base URL of the UiPath Cloud instance.
CLOUD_URL_BASE=https://staging.uipath.com
# Whether to use the LLM Gateway or Azure OpenAI.
USE_LLM_GATEWAY=true
# Whether to force the rebuild of the dataset on startup.
SHOULD_FORCE_REBUILD_DATASET=false
# Whether to dynamically fetch IS activities metadata.
USE_DAP_ACTIVITIES=true

# External services and resources
AZURE_OPENAI_ENDPOINT=https://autopilot-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=<your-azure-openai-api-key>

# Embeddings
# Whether to run the embeddings background rebuild. This will pull embeddings.db from a storage account.
RUN_EMBEDDINGS_BACKGROUND_REBUILD=false
# The interval in minutes at which the embeddings will be refreshed.
REFRESH_EMBEDDINGS_INTERVAL=5
#EMBEDDINGS_STORAGE_ACCOUNT_CONNECTION_STRING=<your-embeddings-storage-account-connection-string>

# Evaluation
# Whether to use cached connections when running DAP cli. Used during evaluation for performance.
USE_CACHED_CONNECTIONS=false
# Remove if not doing evaluation. This is the token to be used with LLM Gateway. It should be refreshed each hour.
# UIPATH_TOKEN="<your-uipath-token>"
# These 2 are an alternative to UIPATH_TOKEN to programatically fetch it.
# UIPATH_CLIENT_ID=<your_client_id>
# UIPATH_CLIENT_SECRET=<your_client_secret>

# Platform-specific
# Disable tokenizers parallelism on MacOS or Windows, enable on WSL or Linux
TOKENIZERS_PARALLELISM=true

# Telemetry
ENABLE_TELEMETRY=false
TRACELOOP_TRACE_CONTENT=false

# Add if you want to use Application Insights instance for local development
#APP_INSIGHTS_INSTRUMENTATION_KEY=<your-app-insights-instrumentation-key>
#APP_INSIGHTS_KEY=<your-app-insights-key>

# UI Automation Development
# Enable if you want to do UI Automation Development with custom CV models
#CV_URL=https://cv-staging.uipath.com
#CV_LICENSE_KEY=<your-cv-license-key>

# Dataset related
# This is used for calling the xaml to yaml conversion API
# CONVERTER_WINDOWS_KEY=<converter_windows_key>
