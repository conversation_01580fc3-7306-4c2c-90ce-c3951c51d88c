# Autopilot for Developers

## Setup development environment

The preferred way to setup your dev environment is on the host using a `setup.sh` script and `uv`. The setup should work as is on Linux/Mac. For Windows, we recommend using [WSL](#setup-wsl). Alternatively, a minimal [devcontainer](#devcontainers-setup-guide) setup is available that reuses the `uv` setup process.

Before we start, if you want your Studio instance to redirect to a local Autopilot service deployment, you need to place the `autopilot/.client/autopilot.config.example` in the `C:\Users\<USER>\AppData\Roaming\UiPath` folder. If you want to disable it, rename the file and restart Studio.

### Local Setup Guide (VS Code/Cursor, WSL/Linux/Mac)

Follow the steps below to get a local Autopilot service up and running on port 5002 (the service can be called either via Postman or directly with Studio) using VSCode/Cursor:

1. **[Install Python 3.10.x (latest) and pip](https://www.python.org/downloads/)**

2. **[Install UV](https://docs.astral.sh/uv/getting-started/installation/)**

3. **[Install AZ CLI](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-windows?tabs=azure-cli).**

4. **[Install .NET](https://dotnet.microsoft.com/en-us/download)**

5. **[Get a Azure Artifacts Access Token](https://uipath.visualstudio.com/_usersSettings/tokens)**. You need to make sure you have read access to the `ml-packages` feed. Check [here](https://uipath.visualstudio.com/SemanticProxy/_artifacts/feed/ml-packages@Local/settings/permissions). If you don't, reach out to one of the feed owners.

6. **Clone this repo `git clone https://github.com/UiPath/ML.git` and open `autopilot/.autopilot.code-workspace`**. To open a workspace, open the Command Palette and run `File: Open Workspace from File...`. Minimal settings and extensions are setup in the workspace. For any user-specific settings, please use `.vscode/settings.json`, `.vscode/extensions.json`, and `.vscode/launch.json` as they are gitignored. All further commands asume the current directory to be `autopilot/`.

7. Setup your environment variables using your preferred method. You should have all the variables listed in `.env.template` loaded. Ask one of the maintainers for a valid environment configuration.  Note that `ENV_NAME` and `CLOUD_URL_BASE` may change depending on your context.

8. Run `bash setup.sh` to clone the data repo and download models/.net dependencies. These will be placed under the directory`.data/`. **Important:** In order to run the stella model on Mac or Windows, go to `config.json` in the `stella-trained` folder, and set both `use_memory_efficient_attention` and `unpad_inputs` to `false`. The data repo contains file names with long paths, so enable that in both [git](https://stackoverflow.com/questions/22575662/filename-too-long-in-git-for-windows) and [windows](https://www.itprotoday.com/windows-10/enable-long-file-name-support-in-windows-10) (this will require you to reboot the laptop unfortunately). Optionally, replace both *embeddings.db* and *embeddingsHash.txt* under `.data/Autopilot.Samples\Dataset\Embeddings` with the latest from the [Type Definitions Pipeline](https://uipath.visualstudio.com/Autopilot.Integration/_build) for your target environment (typically staging). These are used during the initial bootstrap to build the embeddings and may need to be different depending on your usecase (e.g. trying to repro a bug that's manifesting only in a certain environment).

9. Run `uv sync --no-sources --dev` to install the python dependencies. This will create a virtual environment under the directory `.venv/`. Source it using `source .venv/bin/activate` on Linux/Mac or `.venv\bin\activate.bat` on Windows. Also make sure to select the python interpreter in VSCode/Cursor.

10. **Run ML from VS Code by clicking F5 Start Debugging**. During the first start you should see the activities info getting pulled for building the retrieval database (may take a few minutes). It will automatically build the `.data/Retrievers` folder. If you ever want to re-generate these, after the Autopilot.Samples repo has new changes, just delete the Retrievers folder and re-run the ML Service, it will rebuild it automatically. After that you should see the instance live serving on the 5002 port.

For reference, downloading .net dependencies and models can be done by running the following commands. For the last command, to determine the version of Dynamic Activity Package CLI, go [here](https://uipath.visualstudio.com/Public.Feeds/_artifacts/feed/ML-models/UPack/dynamic.activities.discovery/versions) and use the latest version. You might be prompted to install "azure-devops" tool, do so. Assuming the workdir is set to `C:\Dev`:

```shell
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-embeddings-model" --version "0.0.5" --path "C:\Dev\Models\stella-trained"
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-cross-encoder-model" --version "1.0.0" --path "C:\Dev\Models\cross-encoder"
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "dynamic.activities.discovery" --version "25.0.0-alpha.19987" --path "C:\Dev\DotNet\dynamic.activities.discovery"
```

### Optional: Installing the Workflow Converter for evaluation purposes

Navigate to `.data` and run the following commands (make sure to replace `<FRAMEWORK>` with your framework, see below):

```shell
mkdir DotNet
cd DotNet
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "workflow.converter.<FRAMEWORK>" --version "<WORKFLOW_CONVERTER_VERSION>" --path "C:\Dev\DotNet\workflow.converter.<VERSION>"
```

You can find the workflow converters versions grouped by framework below (use the latest version):

- [net8](https://uipath.visualstudio.com/Public.Feeds/_artifacts/feed/ML-models/UPack/workflow.converter.net8/versions)
- [net8.windows](https://uipath.visualstudio.com/Public.Feeds/_artifacts/feed/ML-models/UPack/workflow.converter.net8.windows/versions)
- [net461](https://uipath.visualstudio.com/Public.Feeds/_artifacts/feed/ML-models/UPack/workflow.converter.net461/versions)

### Run code checks and tests

This project uses [Ruff](https://docs.astral.sh/ruff/) for linting and formatting, [BasedPyright](https://docs.basedpyright.com/latest/) for type checking and [PyTest](https://docs.pytest.org/en/stable/) for testing. Sensible defaults and configuration are read from `pyproject.toml`. Run these with the following commands:

```shell
ruff check    # linting
basedpyright  # type checking
pytest        # tests
```

To be able to explore and debug tests, make sure you installed the extensions listed in `.autopilot.code-workspace`. Open the Test Explorer in VSCode/Cursor and you should see all tests populated there. If not open the Command Palette and run `Python: Configure Tests`. By defualt, the retrievers are rebuilt at every run. If you would like to reused the cached demo data, set the env variable `SHOULD_FORCE_REBUILD_DATASET=false`.

- Install [Test Explorer](https://marketplace.visualstudio.com/items?itemName=LittleFoxTeam.vscode-python-test-adapter) to be able to browse the tests using VSCode.
- Make sure Pytest test is enabled in the VSCode preferences: `python.testing.pytestEnabled`
- Add the following `.env` file, required for when running the integration tests, under `text_to_workflow/tests/`. For the `AZURE_OPENAI_API_KEY` refer to the #engineering Slack channel and `ENV_NAME` and `CLOUD_URL_BASE` may change depending on your context:

## Devcontainers Setup Guide

This is a guide on how to set up devcontainers dev environment using WSL and the Remote Development Extension. Below we will describe how to set up WSL, vscode/cursor with devcontainers and any other prerequisites. Once those are done, simply open the `autopilot/services/_text_to_workflow` folder in vscode/cursor and hit `Reopen in Container` once prompted by the IDE. This will set up a devcontainer and attach to it, mounting the sources from the root repo. Once inside, follow the instructions outlined above to setup your environment inside the container.

**Notes**:

- This guide is written for Windows users, but also general steps also work on Linux/Mac.
- This guide assumes WSL users will use the Docker engine installed directly in WSL, not Docker Desktop. If you are using Docker Desktop instead, please be aware of the following:
  - Ensure you have a license for it, as Docker Desktop is no longer fully open-source (the Docker engine itself is still open source).
  - When using Docker Desktop, mounted folders are accessed within the Windows filesystem. If you use the `workdir` paths as described below they might not work. Change the paths accordingly.
- You might be able to use Rancher Desktop as an open-source alternative to Docker Desktop. However, this hasn't been tested yet.

## Prerequisites

- VSCode
- [Remote Development Extension Pack](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack)
- [Dev Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
- Optional: [Docker](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker)

## Setup WSL

### Install WSL

```shell
wsl --install -d Ubuntu-22.04 --web-download
```

Notes:

- Make sure you're using WSL2.
- If you're having problems check the [official guide](https://learn.microsoft.com/en-gb/windows/wsl/install). keep in mind that the version that we know to work is Ubuntu-22.04.
- Installing using just `wsl --install` doesn't seem to work.

**[Install az cli]([https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-linux?pivots=apt](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-linux?pivots=apt)) and login**

WSL/Linux

```shell
sudo apt update && sudo apt upgrade
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

Mac

```shell
brew update && brew install azure-cli
brew install automake libtool
```

Then

```shell
az login
# Install azure-devops extension
az extension add --name azure-devops
```

*Note: make sure you are using the WSL `az` CLI by running `which az`.*

Here you will have to login using the link and SSO. Then select a subscription and tenant or just hit enter for default.

**Setup git**

You will need to pull some repos so it's best to set this up in WSL.

WSL/Linux

```shell
sudo apt-get update
sudo apt install git-lfs
```

Mac

```shell
brew install git-lfs
```

Then

```shell
git lfs install
```

*Setup Authentication Method*

Here are some examples of setting up git auth. Use what works best for you.

*Git Credentials Manager on Windows Setup*

This is the fastest way if you already have the credentials manager set up on Windows.

```shell
git config --global credential.helper "/mnt/c/Program\ Files/Git/mingw64/bin/git-credential-manager.exe"
```

Note: See <https://learn.microsoft.com/en-us/windows/wsl/tutorials/wsl-git> for a reference

*SSH setup*

```shell
ssh-keygen -t ed25519 -C "<EMAIL>"
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519
```

Copy the key into clipboard and add it to your git account. Don't forget to Authorize SSO on that ssh key in git to give it access to UiPath projects, this is under the Configure SSO button.

```shell
cat ~/.ssh/id_ed25519.pub | clip.exe
```

Test that it works using

```shell
ssh -T **************
```

**Clone ML repo**

Clone this repo wherever you want

```shell
<NAME_EMAIL>:UiPath/ML.git
```

**Optional**
If you want to push using the ssh key you should do this inside the repo

```shell
git remote set-url origin $(git remote get-url origin | sed 's/https:\/\/github.com\//**************:/')
```

and setup this

```shell
git config --global user.email "<EMAIL>"
git config --global user.name "Your Name"
```

**Create a workdir folder**

Create a workdir folder inside WSL and keep that location in mind. We will use it later.

```shell
# Example
mkdir ~/dev/workdir
```

**Clone Autopilot.Samples repo**

Clone this repo in the workdir folder

```shell
git clone --recursive **************:UiPath/Autopilot.Samples.git
```

Note: We use recursive to make sure we get all the files from git-lfs.

**Install Docker with Nvidia Drivers on WSL/Linux**

This step will install Docker with GPU support for Nvidia on WSL/Linux. If you have an Nvidia GPU it is recommended to install the drivers but if not just set `install_nvidia=false` in the script.

***Note***: If you have a different GPU provider (AMD, Intel etc) then you will need to install the correct drivers for them.

You need to run the below script, it will:

- add nvidia repo, if `install_nvidia` is `true`.
- update packages.
- install docker.
  - with gpu drivers if `install_nvidia` is `true`.
- add user to docker groups.

```shell
# Set this to true if you want to install NVIDIA support, false otherwise
install_nvidia=true

# Get the distribution ID
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)

if [ "$install_nvidia" = true ]; then
    # These are idempotent so they can be run multiple times
    echo "Adding NVIDIA's GPG key..."
    curl -s -L https://nvidia.github.io/libnvidia-container/gpgkey | sudo apt-key add -

    echo "Adding the NVIDIA repository..."
    curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
fi

# Update package lists
echo "Update package lists..."
sudo apt-get update

# Install packages if they aren't already present
packages=(docker.io libsecret-1-0)
if [ "$install_nvidia" = true ]; then
    packages+=(nvidia-docker2)
fi

for package in "${packages[@]}"
do
    if ! dpkg -s "$package" >/dev/null 2>&1; then
        echo "$package is not installed. Installing..."
        sudo apt-get install -y "$package"
    else
        echo "$package is already installed."
    fi
done

# Check if the user is already in the docker group
if groups $USER | grep &>/dev/null '\bdocker\b'; then
    echo "User $USER is already in the docker group"
else
    # Add the user to the docker group
    sudo usermod -aG docker $USER
    if [ $? -eq 0 ]; then
        echo "User $USER has been added to the docker group"

        # Apply the new group membership
        exec newgrp docker
    else
        echo "Failed to add user $USER to the docker group"
        exit 1
    fi
fi

# Restart docker
sudo service docker restart
```

**Install Docker on MacOS**

We will be installing Docker using [Colima](https://github.com/abiosoft/colima) as a container runtime. Alternatively, you can use Docker Desktop if you have a license for it.

```shell
brew install colima
brew install docker
colima start
```

**Open the ML Repo using Remote Development Extension**

Using the extension open the repo you created inside WSL

**Install the recommended extensions**

- Install [Docker](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker)
  - Make sure to install in WSL.
- Install [Azure Functions](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-azurefunctions)

**Run configuration task**

Run a task from `.vscode` folder to configure `.devcontainer` folder. This will delete the existing one if present.

- Command Palette (`Ctrl` + `Shift` + `P`) > Run Build Task > Configure Container - Studio Text to Workflow Linux
  - You will have to give the following env variables
    - The path to the workdir folder we crated previously
    - Port
    - Select GPU or CPU version. GPU is prefered because it is faster.
    - LLM endpoint
    - API Key
- With the folder created you can now reopen in devcontainer. You will be prompted to do this by VSCode but you can also do it via command pallete (cmd + shift + p) then `Open Folder in Container`.
  - Once this is done the devcontainer will start building.
  - This takes a long time the first time it happens.
- After `Open Folder in Container` finishes, you will be prompted to Open workspace in Container. Without it, you won't have the multi-root workspace loaded.
  - Another tip here is to keep only the code repo in this window and open a new window with the data directory. You can simply do this by running in the terminal code ../data. This will keep the vscode search indices independent for code and data. Otherwise you need to do some shenanigans to make the vscode search more efficient. (see the next section in the readme)
  - Tip: Comment out the `data` root in the workspace, load only the code repo, and open data in a separate VSCode window (`code ../data`) for better performance.

Now you have both the codebase repo and the data directory in a multi-root workspace under vscode devcontainer.

Note: For some users on WSL after the first build there is a bug were the `docker inspect --type container` cmd hangs for about 4-5 minutes. Currently the cause for this is unknown.

**Recommended use**

It's recommended to open the project in WSL and connect to the container without opening it in VSCode. There are three key commands you need to know. You can access these through the Command Palette (Ctrl+Shift+P):

- To start, just use: `Docker Containers: Start`.
- To connect the terminal to the container, use: `Docker Containers: Attach Shell`.
- To start the server, use the `start.sh` script.
  - It will then be available on <http://localhost:5002>.
  - Use `CTRL` + `C` to stop it.
  - For development, if you want the server to restart when a file is saved automatically, do: `./start.sh reload`
  - Note: If you want to use `uvloop` know that is doesn't work on Windows.
- To stop the container when you're done, use: `Docker Containers: Stop`.

# Considerations on setting up vscode devcontainer multi-root workspace

Make sure to include a `pyrightconfig.json` file in the data roots such that the `Python Language Server` (e.g. Pylance) would not try to look for python files in that directory. This is important to avoid unnecessary hangs in the editor.

### *<data_dir> / pyrightconfig.json*

```json
{
    "exclude": [
        "**",
    ]
}
```

Another useful thing to avoid hangs is to exclude these directories from file watchers and search. To do this, create a `.vscode` folder and add a `settings.json` in it, which should contain the following

### *<data_dir> / .vscode / settings.json*

```json
{
    "search.exclude": {
        "**": true,
    },
    "files.watcherExclude": {
        "**": true,
    }
}
```

## Using Debugger in container

To use debugger you need to add this entry to `launch.json`:

```json
{
      "name": "Python: Remote Attach",
      "type": "python",
      "request": "attach",
      "connect": {
          "host": "localhost",
          "port": 5678
      },
      "pathMappings": [
          {
              "localRoot": "${workspaceFolder}",
              "remoteRoot": "/workspace/src"
          }
      ]
    },
```

Start the server using debug mode: `./start.sh debug`

You will see a message like: `Listening for debugger on 0.0.0.0:5678 ...`

You can then run the debug configuration shown above and it will then start the server with the debugger connected.

# Details for setting up azure linux machine

in case you need install nvidia drivers e.g. <https://www.nvidia.com/en-us/drivers/>
after this `nvidia-smi` should work

install docker e.g. <https://docs.docker.com/engine/install/ubuntu/> including the post-installation steps
after this `docker run hello-world` should work

install nvidia container toolkit e.g. <https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/install-guide.html> in order to be able to use gpus inside docker
after this `docker run --rm --runtime=nvidia --gpus all ubuntu nvidia-smi` should work

install git lfs e.g. <https://github.com/git-lfs/git-lfs/blob/main/INSTALLING.md>
after this `git lfs install` should work

install az cli

```shell
sudo apt update && sudo apt upgrade
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

```shell
az login
# Install azure-devops extension
az extension add --name azure-devops
```

create a root folder for your code and data e.g. `/data/$(whoami)`
e.g. this can be on the attached larger disk on the machine (usually we mount it to `/data`)

clone `UiPath/ML` repo `git clone https://github.com/UiPath/ML.git` in the root folder

create a data folder in your root folder i.e. `/data/$(whoami)/data`
here put all the data and tools

clone `UiPath/Autopilot.Samples` in the data folder `git clone https://github.com/UiPath/Autopilot.Samples.git`
if the files are not LFS you can just do `git lfs pull` to get the actual files

create a `DotNet` folder in `/data/$(whoami)/data` then

```shell
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "dynamic.activities.discovery" --version "25.0.0-alpha.19987" --path "DotNet/dynamic.activities.discovery"
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "workflow.converter.net8" --version "25.0.0-alpha.19987" --path "DotNet/workflow.converter.net8"
```

(please check the versions in <https://uipath.visualstudio.com/Receipts/_artifacts/feed/ML-models>)

create a `Models` folder in `/data/$(whoami)/data` then

```shell
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-embeddings-model" --version "0.0.5" --path "Models/stella-trained"
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-cross-encoder-model" --version "1.0.0" --path "Models/cross-encoder"
az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-workflow-junk-filter-model" --version "1.0.2" --path "Models/junk-filter"
```

(please check the versions in <https://uipath.visualstudio.com/Receipts/_artifacts/feed/ML-models>)

in folder `/data/$(whoami)/ML/autopilot/services/_text_to_workflow` add the .env file e.g.

```shell
OPENAI_API_TYPE=azure
AZURE_OPENAI_ENDPOINT=https://autopilot-openai-us.openai.azure.com/
OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_API_KEY=<API_KEY>
USE_LLM_GATEWAY=True
ENV_NAME=staging
CLOUD_URL_BASE=https://staging.uipath.com
STUDIO_TEXT_TO_WORKFLOW_WORKDIR=/workspace/data
SHOULD_FORCE_REBUILD_DATASET=False
```

build the docker container

```shell
cd /data/$(whoami)/ML/
docker build -t wingman_dev -f autopilot/services/_text_to_workflow/Dockerfile --target=dev .
```

run the docker container

```shell
docker run -it --shm-size=2gb \
    --cap-add SYS_PTRACE \
    -v /etc/passwd:/etc/passwd \
    -v /etc/group:/etc/group \
    -v /${PWD%/*/*}:/workspace/src \
    -v /${PWD%/*/*/*}/data:/workspace/data \
    -v /tmp:/tmp \
    --user "$(id -u):$(id -g)" \
    --gpus all wingman_dev
```

from the docker container you need to setup the retrievers:

```shell
cd /workspace/src/autopilot/services/_text_to_workflow/workflow_generation
python3 main.py build
```

after this you should be able to do an evaluation on the wf gen model:

```shell
cd /workspace/src/autopilot/services/_text_to_workflow/workflow_generation
OPENAI_API_TYPE=azure AZURE_OPENAI_ENDPOINT=https://autopilot-openai-us.openai.azure.com/ OPENAI_API_VERSION=2024-02-15-preview AZURE_OPENAI_API_KEY=<API_KEY> USE_LLM_GATEWAY=false USE_CACHED_CONNECTIONS=true python3 main.py test --subset="test" --target-framework="Portable" --mode="workflow" --seed=42 | tee wgfen_plan_gpt4o_gen_gpt35_20240903.html
```

*Considerations when evaluating*

Use the following environment variables when evaluating the workflow generation model:

- `USE_CACHED_CONNECTIONS=true` - this will use the cached connections for dap typedefs (for consistency)
- either
  - `USE_LLM_GATEWAY=false` + `AZURE_OPENAI_API_KEY=<API_KEY>` + `AZURE_OPENAI_ENDPOINT=<endpoint>` - to avoid having to provide an ephemeral uipath token
  - `USE_LLM_GATEWAY=true` + `UIPATH_TOKEN=<token>` - use the uipath token instead of the azure key

## Contributing to the project

### Pull Request Guidelines

Use the following format for the PR title: `[Autopilot]: Description JIRA-ISSUE-ID`, e.g. `[Autopilot]: Add a new feature PILOT-123`.

Add the WIP label to the PR title or use a draft PR if you are still working on it.

### How to fix linting errors

Ensure you have activated your venv and run the following command to fix linting errors:

```shell
./azure-pipelines-code-check.sh --folder studio --fix
```

# Using Gemini models without LLM Gateway
**[Install Google Cloud SDK (gcloud)](https://cloud.google.com/sdk/docs/install).**

For Windows:
```shell
# Download the installer and run it
# After installation, open a terminal and run:
gcloud init
```

For Linux/WSL:
```shell
# Add the Cloud SDK distribution URI as a package source
echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list

# Import the Google Cloud public key
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -

# Update and install the Cloud SDK
sudo apt-get update && sudo apt-get install -y google-cloud-sdk

# Initialize gcloud
gcloud init
```

For Mac:
```shell
# Using Homebrew
brew install --cask google-cloud-sdk

# Initialize gcloud
gcloud init
```

To authenticate with your GCP service account:
```shell
# Using a service account key file (JSON)
gcloud auth activate-service-account --key-file=path/to/your/gcp-credentials.json

# Set the default project
gcloud config set project devtest-autopilot
```

