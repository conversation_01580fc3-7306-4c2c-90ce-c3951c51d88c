import argparse
import time
import uuid

from azure.storage.blob import BlobServiceClient, ContainerClient
from deleted_account_ids import account_ids_to_delete

# list of operations, collected from the endpoints in studio/text_to_workflow/service.py

operations_list = [
    # legacy operations
    "text-to-workflow/activity-summary",
    "text-to-workflow/testdata-generation",
    "text-to-workflow/api-automation",
    "text-to-workflow/generate-sequence",
    "text-to-workflow/expression-generation",
    "text-to-workflow/fix-expression",
    "text-to-workflow/ui-automation",
    "text-to-workflow/code-generation",
    "text-to-workflow/activity-configuration",
    # currently supported operations
    "generate-workflow",
    "generate-sequence",
    "summarize-workflow",
    "generate-expression",
    "fix-expression",
    "configure-activity",
    "generate-code",
    "fix-code",
    "generate-testdata",
    "generate-screen-actions",
    "qa-screen",
    "qa-dom",
    "get-dom-element-description",
    "fix-workflow",
    "agent-act-on-screen",
    "agent-screen-extract",
    "api-workflow",
    "bpmn",
    "chat",
    "close-popup",
    "diff-text",
    "enabled-features",
    "generate-agent",
    "generate-agenteval",
    "generate-bpmn",
    "get-bbox-element-description",
    "get-element-description",
    "match-popup",
    "predict-expression",
    "telemetry",
    "testdata-generation",
    "workflow",
]


def delete_data_for_organization(container_client: ContainerClient, organization_id):
    print("Deleting data for organization", organization_id)

    # checking if the organization_id is a valid UUID
    # if not, this will raise an exception
    uuid.UUID(organization_id)

    for operation in operations_list:
        prefix = f"{operation}/{organization_id}"
        blobs = container_client.list_blobs(name_starts_with=prefix)
        for blob in blobs:
            print("Deleting blob", blob.name)
            container_client.delete_blob(blob=blob, delete_snapshots="include")


def delete_data_for_organizations():
    container_name = "studio"
    connection_string = args.storage_connection_string

    blob_service_client = BlobServiceClient.from_connection_string(connection_string)
    if blob_service_client:
        container_client = blob_service_client.get_container_client(container_name)
        for organization_id in account_ids_to_delete:
            try:
                delete_data_for_organization(container_client, organization_id)
            except Exception as e:
                print(f"Error deleting data for organization {organization_id}: {e}")
        print("Data deletion completed")
    else:
        print("Error, could not connect to blob storage")


if __name__ == "__main__":
    start = time.time()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--storage-connection-string",
        help="The connection string to the destination blob storage account",
    )
    global args
    args = parser.parse_args()

    delete_data_for_organizations()
