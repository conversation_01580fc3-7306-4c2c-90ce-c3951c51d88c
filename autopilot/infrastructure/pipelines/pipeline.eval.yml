name: "v$(Date:yyyy.MM.dd)_$(Rev:rr)"
pr: none
resources:
  repositories:
    - repository: "Autopilot.Samples"
      type: "github"
      endpoint: "UiPath"
      name: "UiPath/Autopilot.Samples"
      ref: refs/heads/release/wingman/250520
trigger:
  batch: true
  branches:
    include:
      - "release/studio/*"
stages:
  - stage: "EVAL"
    jobs:
      - job: "run"
        timeoutInMinutes: 60
        pool: "Autopilot-Developers-Test-Agents"
        steps:
          - script: |
              set -x
              ip a
              ip a | grep -q "********/24" && ip a del ********/24 dev eth0 2>/dev/null || true
              ip a
          - template: .pipeline.common.yml
          - task: "Docker@2"
            displayName: "Build Docker Test Image"
            inputs:
              command: "build"
              imageName: "studio/text_to_workflow"
              dockerfile: "$(Build.SourcesDirectory)/ml.studio/studio/text_to_workflow/Dockerfile"
              buildContext: "$(Build.SourcesDirectory)/ml.studio"
              arguments: |
                --target eval
                --tag autopilot/services/_text_to_workflow:eval
          - task: "Docker@2"
            displayName: "Run Docker Eval Image"
            inputs:
              command: "run"
              arguments: |
                -e OPENAI_API_TYPE=$(gpt-api-type)
                -e AZURE_OPENAI_ENDPOINT=$(gpt-api-base)
                -e OPENAI_API_VERSION=$(gpt-api-version)
                -e AZURE_OPENAI_API_KEY=$(gpt-api-key)
                -e CV_URL=$(cv-url)
                -e CV_LICENSE_KEY=$(cv-license-key)
                -e OPENAI_CU_API_KEY=$(openai-computer-use-api-key)
                -e USE_LLM_GATEWAY=false
                -e RUN_EMBEDDINGS_BACKGROUND_REBUILD=false
                -e UIPATH_CLIENT_ID=$(uipath-client-id)
                -e UIPATH_CLIENT_SECRET=$(uipath-client-secret)
                -e TRACELOOP_TRACE_CONTENT=false
                -v $(Build.SourcesDirectory)/ml.studio.workdir:/workspace/data
                --rm --gpus all
                autopilot/services/_text_to_workflow:eval
          - publish: $(Build.SourcesDirectory)/ml.studio.workdir/eval_results
            artifact: Evaluations
