{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"location": {"value": "westeurope"}, "enableAcceleratedNetworking": {"value": true}, "networkSecurityGroupRules": {"value": [{"name": "HTTPS", "properties": {"priority": 1010, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceApplicationSecurityGroups": [], "destinationApplicationSecurityGroups": [], "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "443"}}, {"name": "default-allow-ssh", "properties": {"priority": 1020, "protocol": "TCP", "access": "Allow", "direction": "Inbound", "sourceApplicationSecurityGroups": [], "destinationApplicationSecurityGroups": [], "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "22"}}]}, "subnetName": {"value": "default"}, "virtualNetworkId": {"value": "/subscriptions/5db48574-8a20-418f-b488-1fafd8d021df/resourceGroups/dev-ml-train/providers/Microsoft.Network/virtualNetworks/dev-ml-train-vnet"}, "publicIpAddressType": {"value": "Static"}, "publicIpAddressSku": {"value": "Standard"}, "pipDeleteOption": {"value": "<PERSON><PERSON>"}, "virtualMachineName": {"value": null}, "virtualMachineRG": {"value": "dev-ml-train"}, "osDiskType": {"value": "Premium_LRS"}, "osDiskSizeGiB": {"value": 1024}, "osDiskDeleteOption": {"value": "Delete"}, "virtualMachineSize": {"value": "Standard_NC48ads_A100_v4"}, "nicDeleteOption": {"value": "<PERSON><PERSON>"}, "hibernationEnabled": {"value": false}, "adminUsername": {"value": "wingman"}, "adminPassword": {"value": null}, "virtualMachine2Zone": {"value": "1"}}}