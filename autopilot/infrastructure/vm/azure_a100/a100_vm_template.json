{"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "string"}, "enableAcceleratedNetworking": {"type": "bool"}, "networkSecurityGroupRules": {"type": "array"}, "subnetName": {"type": "string"}, "virtualNetworkId": {"type": "string"}, "publicIpAddressType": {"type": "string"}, "publicIpAddressSku": {"type": "string"}, "pipDeleteOption": {"type": "string"}, "virtualMachineName": {"type": "string"}, "virtualMachineRG": {"type": "string"}, "osDiskType": {"type": "string"}, "osDiskSizeGiB": {"type": "int"}, "osDiskDeleteOption": {"type": "string"}, "virtualMachineSize": {"type": "string"}, "nicDeleteOption": {"type": "string"}, "hibernationEnabled": {"type": "bool"}, "adminUsername": {"type": "string"}, "adminPassword": {"type": "secureString"}, "virtualMachine2Zone": {"type": "string"}}, "variables": {"nsgId": "[resourceId(resourceGroup().name, 'Microsoft.Network/networkSecurityGroups', concat(parameters('virtualMachineName'), '-nsg'))]", "vnetId": "[parameters('virtualNetworkId')]", "vnetName": "[last(split(variables('vnetId'), '/'))]", "subnetRef": "[concat(variables('vnetId'), '/subnets/', parameters('subnetName'))]", "networkInterfaceName2": "[concat(parameters('virtualMachineName'), '443_z2')]", "networkSecurityGroupName": "[concat(parameters('virtualMachineName'), '-nsg')]", "publicIpAddressName2": "[concat(parameters('virtualMachineName'), '-ip')]", "virtualMachineName2": "[parameters('virtualMachineName')]", "virtualMachineComputerName2": "[parameters('virtualMachineName')]"}, "resources": [{"name": "[variables('networkInterfaceName2')]", "type": "Microsoft.Network/networkInterfaces", "apiVersion": "2022-11-01", "location": "[parameters('location')]", "dependsOn": ["[concat('Microsoft.Network/networkSecurityGroups/', variables('networkSecurityGroupName'))]", "[concat('Microsoft.Network/publicIpAddresses/', variables('publicIpAddressName2'))]"], "properties": {"ipConfigurations": [{"name": "ipconfig1", "properties": {"subnet": {"id": "[variables('subnetRef')]"}, "privateIPAllocationMethod": "Dynamic", "publicIpAddress": {"id": "[resourceId(resourceGroup().name, 'Microsoft.Network/publicIpAddresses', variables('publicIpAddressName2'))]", "properties": {"deleteOption": "[parameters('pipDeleteOption')]"}}}}], "enableAcceleratedNetworking": "[parameters('enableAcceleratedNetworking')]", "networkSecurityGroup": {"id": "[variables('nsgId')]"}}}, {"name": "[variables('networkSecurityGroupName')]", "type": "Microsoft.Network/networkSecurityGroups", "apiVersion": "2019-02-01", "location": "[parameters('location')]", "properties": {"securityRules": "[parameters('networkSecurityGroupRules')]"}}, {"name": "[variables('publicIpAddressName2')]", "type": "Microsoft.Network/publicIpAddresses", "apiVersion": "2020-08-01", "location": "[parameters('location')]", "properties": {"publicIpAllocationMethod": "[parameters('publicIpAddressType')]"}, "sku": {"name": "[parameters('publicIpAddressSku')]"}, "zones": ["[parameters('virtualMachine2Zone')]"]}, {"name": "[variables('virtualMachineName2')]", "type": "Microsoft.Compute/virtualMachines", "apiVersion": "2022-11-01", "location": "[parameters('location')]", "dependsOn": ["[concat('Microsoft.Network/networkInterfaces/', variables('networkInterfaceName2'))]"], "properties": {"hardwareProfile": {"vmSize": "[parameters('virtualMachineSize')]"}, "storageProfile": {"osDisk": {"createOption": "fromImage", "managedDisk": {"storageAccountType": "[parameters('osDiskType')]"}, "diskSizeGB": "[parameters('osDiskSizeGiB')]", "deleteOption": "[parameters('osDiskDeleteOption')]"}, "imageReference": {"publisher": "nvidia", "offer": "ngc_azure_17_11", "sku": "ngc-base-version-23_09_1_gen2", "version": "latest"}}, "networkProfile": {"networkInterfaces": [{"id": "[resourceId('Microsoft.Network/networkInterfaces', variables('networkInterfaceName2'))]", "properties": {"deleteOption": "[parameters('nicDeleteOption')]"}}]}, "additionalCapabilities": {"hibernationEnabled": false}, "osProfile": {"computerName": "[variables('virtualMachineComputerName2')]", "adminUsername": "[parameters('adminUsername')]", "adminPassword": "[parameters('adminPassword')]", "linuxConfiguration": {"patchSettings": {"patchMode": "ImageDefault"}}}, "diagnosticsProfile": {"bootDiagnostics": {"enabled": true}}}, "plan": {"name": "ngc-base-version-23_09_1_gen2", "publisher": "nvidia", "product": "ngc_azure_17_11"}, "zones": ["[parameters('virtualMachine2Zone')]"]}], "outputs": {"adminUsername": {"type": "string", "value": "[parameters('adminUsername')]"}}}