#!/bin/bash

# script that takes in a VM name, user and password and creates a VM
# env variables that can be overriden:
#   RESOURCE_GROUP: name of the resource group to create the VM in
#   SUBSCRIPTION_ID: subscription id to create the VM in
#   SOURCE_DATA_DISK: source data disk to clone from
# usage: ./create_vm.sh <vm_name> <user> <password>
# example: ./create_vm.sh myvm myuser mypassword

# in case you need to find the vnet id, use the following command:
# az network vnet list --resource-group dev-ml-train --subscription 5db48574-8a20-418f-b488-1fafd8d021df

# Get the directory where the script is located
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

# parameters
RESOURCE_GROUP="${RESOURCE_GROUP:-dev-ml-train}"
SUBSCRIPTION_ID="${SUBSCRIPTION_ID:-5db48574-8a20-418f-b488-1fafd8d021df}"
# SOURCE_DATA_DISK="${SOURCE_DATA_DISK:-/subscriptions/5db48574-8a20-418f-b488-1fafd8d021df/resourceGroups/CV-ML-TRAINING/providers/Microsoft.Compute/disks/emavm_DataDisk_1}"


# check if name, user and password parameters were passed in
if [ $# -lt 3 ]; then
    echo "Usage: ./create_vm.sh <vm_name> <user> <password>"
    exit 1
fi

VM_NAME=$1
USER=$2
PASSWORD=$3

if az vm show --name $VM_NAME --resource-group $RESOURCE_GROUP --subscription $SUBSCRIPTION_ID > /dev/null 2>&1; then
    echo "VM $VM_NAME already exists."
    exit 2
else
    echo "VM $VM_NAME does not exist. Creating VM..."
fi

if az deployment group create \
    --resource-group $RESOURCE_GROUP \
    --subscription $SUBSCRIPTION_ID \
    --template-file $SCRIPT_DIR/a100_vm_template.json \
    --parameters @$SCRIPT_DIR/a100_vm_parameters.json virtualMachineName=$VM_NAME adminUsername=$USER adminPassword=$PASSWORD; \
    then
    echo "VM $VM_NAME created successfully."
else
    echo "VM $VM_NAME creation failed."
    exit 3
fi


# --source $SOURCE_DATA_DISK \
if az disk create \
    --resource-group $RESOURCE_GROUP \
    --subscription $SUBSCRIPTION_ID \
    --name "$VM_NAME"_DataDisk_1 \
    --location westeurope \
    --size-gb 4095 \
    --zone 1; \
    then
    echo "Data disk created successfully."
else
    echo "Data disk creation failed."
    exit 4
fi

# Attach the clone disk to the VM
if az vm disk attach \
    --resource-group $RESOURCE_GROUP \
    --subscription $SUBSCRIPTION_ID \
    --name "$VM_NAME"_DataDisk_1 \
    --vm-name $VM_NAME \
    # --caching ReadWrite; \ # Only Disk CachingType 'None' is supported for disk with size greater than 4095 GB

    then
    echo "Data disk attached successfully."
else
    echo "Data disk attachment failed."
    exit 5
fi

az vm run-command invoke \
    --resource-group $RESOURCE_GROUP \
    --subscription $SUBSCRIPTION_ID \
    --name $VM_NAME \
    --command-id RunShellScript \
    --scripts @"$SCRIPT_DIR/mount_disk.sh"
