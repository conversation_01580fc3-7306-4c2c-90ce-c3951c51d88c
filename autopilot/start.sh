#!/bin/bash

# Function to display help information
show_help() {
    echo "Usage: ./start.sh [OPTIONS]"
    echo
    echo "This script starts the Autopilot Studio FastAPI server with customizable options."
    echo
    echo "Options:"
    echo "  help     Show this help message and exit"
    echo "  uvloop   Use uvloop and httptools"
    echo "  reload   Enable auto-reload (mutually exclusive with uvloop)"
    echo "  debug    Enable debugpy for VS Code debugging"
    echo
    echo "Environment variables:"
    echo "  HOST         The host to bind to (default: 0.0.0.0)"
    echo "  PORT         The port to bind to (default: 5002). Is overridden if AUTOPILOT_PORT is set."
    echo "  LOG_LEVEL    The log level to use (default: info)"
    echo
    echo "Examples:"
    echo "  ./start.sh"
    echo "  ./start.sh uvloop"
    echo "  ./start.sh reload"
    echo "  ./start.sh debug"
    echo "  ./start.sh debug uvloop"
    echo "  ./start.sh debug reload"
}

# Check for help argument
if [[ "$*" == *"help"* ]]; then
    show_help
    exit 0
fi

# Default values
HOST=${HOST:-"0.0.0.0"}
AUTOPILOT_PORT=${AUTOPILOT_PORT:-5002}
PORT=${PORT:-$AUTOPILOT_PORT}
LOG_LEVEL=${LOG_LEVEL:-"info"}
DEBUG_PORT=${DEBUG_PORT:-"5678"}

# Equivalent to python -o
export PYTHONOPTIMIZE=1

# Base command
CMD="uvicorn services.studio.service:app --host $HOST --port $PORT --log-level $LOG_LEVEL"

# Add uvloop if specified
if [[ "$*" == *"uvloop"* ]]; then
    if [[ "$*" == *"reload"* ]]; then
        echo "Warning: 'uvloop' is mutually exclusive with 'reload'. Using uvloop only."
    fi
    CMD="$CMD --loop uvloop --http httptools"
elif [[ "$*" == *"reload"* ]]; then
    CMD="$CMD --reload"
fi

# Add debug if specified
if [[ "$*" == *"debug"* ]]; then
    echo "Listening for debugger on $HOST:$DEBUG_PORT ..."
    CMD="python -m debugpy --listen $HOST:$DEBUG_PORT --wait-for-client -m $CMD"
fi

# Execute the command
exec $CMD
