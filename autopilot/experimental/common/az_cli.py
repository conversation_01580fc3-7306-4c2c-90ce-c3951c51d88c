import json
import os
import re
import subprocess
import sys


def get_az_version(item_name, cli_output):
    version_re = r"( )*(\d)*.(\d)*.(\d)*"

    m = re.match(item_name + version_re, cli_output)
    if m:
        return cli_output[m.start() : m.end()].replace(item_name, "").strip()
    return None


def check_command_output(cmd):
    return subprocess.check_output(cmd, stderr=subprocess.STDOUT, shell=True).decode("utf8")


def check_az_cli_versions():
    try:
        az_version_output = check_command_output("az --version")
    except Exception:
        print("To install the azure CLI run the command: \n> pip install azure-cli\n")
        sys.exit(-1)

    versions_dict = dict(re.findall(r"([\w-]+)\s*([\d.]+)", az_version_output))

    tool_names = ["azure-cli", "azure-devops"]
    tool_helps = ["", 'To add the "azure-devops" extension run following command: \n> az extension add --name azure-devops\n']
    tool_versions = [versions_dict.get(x, None) for x in tool_names]

    if all(tool_versions):
        for n, v in zip(tool_names, tool_versions, strict=False):
            print(f'Found "{n}" \t with version "{v}"')
    else:
        for v, h in zip(tool_versions, tool_helps, strict=False):
            if not v:
                print(h)
        sys.exit(-1)


def check_az_login_state():
    try:
        az_login_output = check_command_output("az account show")

        try:
            data = json.loads(az_login_output)
            print(f'Logged in as "{data["user"]["name"]}"')
        except Exception:
            print(az_login_output)
            sys.exit(-1)
    except Exception:
        cmd = "az login"
        print(f"> {cmd}")
        os.system(cmd)


def check_az_req():
    check_az_cli_versions()
    check_az_login_state()


def download_package(name, version, path):
    cmd = f'az artifacts universal download --organization "https://dev.azure.com/uipath/" \
    --feed "ML-models" --name "{name}" --version "{version}" --path "{path}"'
    subprocess.check_call(cmd, shell=True)


def publish_package(name, version, path):
    cmd = f'az artifacts universal publish  --organization "https://dev.azure.com/uipath/" \
    --feed "ML-models" --name "{name}" --version "{version}" --path "{path}"'
    subprocess.check_call(cmd, shell=True)
