import services.studio._text_to_workflow.common.workflow_parser as wf_parser
from services.studio._text_to_workflow.common.api_workflow.schema import (
    BASE_API_ACTIVITIES,
    If,
    TryCatch,
)
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import get_do_block


def get_spacing(counters: list[int]) -> str:
    return wf_parser.get_indentation(len(counters) - 1)


def get_activities_plan(activities: list[BASE_API_ACTIVITIES], counters: list[int] | None = None) -> list[str]:
    counters = counters + [0] if counters else [0]
    result = []
    for nested_activity in activities:
        counters[-1] += 1
        result.extend(get_workflow_plan(nested_activity, counters))
    return result


def get_workflow_plan(
    root_activity: BASE_API_ACTIVITIES,
    counters: list[int] | None = None,
    ignore_root_thought: bool = False,
) -> list[str]:
    """Extract thoughts from an API workflow activity dictionary in a hierarchical format."""
    result = []
    counters = counters if counters else []

    # Get the thought
    if not ignore_root_thought and root_activity.thought:
        result.append(f"{get_spacing(counters)}{wf_parser.get_counter_str(counters)}. {root_activity.thought.strip()}")

    if isinstance(root_activity, If):
        if root_activity.then and len(root_activity.then) > 0:
            result.extend(get_activities_plan(root_activity.then, counters))

        if root_activity.else_ and len(root_activity.else_) > 0:
            # only log the else block if it has activities
            counters[-1] += 1
            result.append(f"{get_spacing(counters)}{wf_parser.get_counter_str(counters)}. Else")
            result.extend(get_activities_plan(root_activity.else_, counters))

    if isinstance(root_activity, TryCatch):
        if root_activity.try_ and len(root_activity.try_) > 0:
            result.extend(get_activities_plan(root_activity.try_, counters))

        if root_activity.catch and len(root_activity.catch.do) > 0:
            # only log the catch block if it has activities
            counters[-1] += 1
            result.append(f"{get_spacing(counters)}{wf_parser.get_counter_str(counters)}. Catch")
            result.extend(get_activities_plan(root_activity.catch.do, counters))

    result.extend(get_activities_plan(get_do_block(root_activity), counters))

    return result
