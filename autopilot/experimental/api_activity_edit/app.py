import asyncio
import json
import os

import streamlit as st

import experimental.api_activity_edit.controller as ct


def main():
    # Config
    st.set_page_config(page_title="Activity Edit Chat", layout="wide")
    st.title("Activity Edit Chat")
    # Session
    if "datapoints" not in st.session_state:
        st.session_state.datapoints, st.session_state.index, st.session_state.filters = ct.get_data()
    if "selected_filters" not in st.session_state:
        st.session_state.selected_filters = {"languages": "*", "workflows": "*", "activities": "*"}
    if "filtered_datapoints" not in st.session_state:
        st.session_state.filtered_datapoints = ct.filter_datapoints()
    if "selected_datapoint" not in st.session_state:
        st.session_state.selected_datapoint = None
    if "token" not in st.session_state:
        st.session_state.token = os.environ.get("UIPATH_TOKEN", "")
    if "tenant_id" not in st.session_state:
        st.session_state.tenant_id = os.environ.get("UIPATH_TENANT_ID", "")
    if "model_name" not in st.session_state:
        st.session_state.model_name = ct.get_supported_models()[0]
    if "chat_client" not in st.session_state:
        st.session_state.chat_client = None
    # Sidebar
    with st.sidebar:
        st.session_state.token = st.text_input("Token", value=st.session_state.token, key="token_input")
        st.session_state.tenant_id = st.text_input("Tenant ID", value=st.session_state.tenant_id, key="tenant_id_input")
        st.session_state.model_name = st.selectbox("Model", options=ct.get_supported_models(), key="model_name_select")
        st.session_state.selected_filters["languages"] = st.selectbox(
            "Language",
            index=2,
            options=st.session_state.filters["languages"],
            key="language_filter_select",
        )
        st.session_state.selected_filters["workflows"] = st.selectbox(
            "Workflow",
            index=0,
            options=st.session_state.filters["workflows"],
            key="workflow_filter_select",
        )
        st.session_state.selected_filters["activities"] = st.selectbox(
            "Activity",
            index=0,
            options=st.session_state.filters["activities"],
            key="activity_filter_select",
        )
        st.session_state.filtered_datapoints = ct.filter_datapoints()
        st.session_state.selected_datapoint = st.selectbox(
            "Datapoint",
            options=st.session_state.filtered_datapoints,
            format_func=lambda p: p.name,
            key="datapoint_selector",
        )
        if st.button("New Chat"):
            st.session_state.messages = []
            st.session_state.chat_client = ct.create_chat()
    # Main content
    if st.session_state.chat_client is None:
        return
    st.subheader(st.session_state.selected_datapoint)
    col1, col2 = st.columns([1, 1])
    # Chat
    with col1:
        st.subheader("Chat")
        if prompt := st.chat_input("..."):
            asyncio.run(ct.send_message(prompt))
        for message in st.session_state.chat_client.messages[::-1]:
            st.chat_message(message["role"]).code(message["content"], language="markdown", wrap_lines=True)
        st.divider()
        if st.session_state.chat_client.responses:
            st.subheader("Change")
            st.code(json.dumps(st.session_state.chat_client.responses[-1].model_dump(), indent=2), language="json", wrap_lines=True)
    # Context
    with col2:
        st.subheader("Activity")
        st.code(json.dumps(st.session_state.chat_client.activity, indent=2), language="json", wrap_lines=True)
        st.subheader("Expected")
        st.code(json.dumps(st.session_state.chat_client.expected_activity, indent=2), language="json", wrap_lines=True)
        st.subheader("Workflow")
        st.code(json.dumps(st.session_state.chat_client.workflow, indent=2), language="json", wrap_lines=True)
    st.divider()
    # Trace
    if not st.session_state.chat_client.traces:
        return
    col1, col2 = st.columns([1, 1])
    trace = st.session_state.chat_client.traces[-1]
    with col1:
        st.header("Latest Trace")
        for message in trace.messages:
            st.chat_message(message.type).code(message.content, language="markdown", wrap_lines=True)
        st.chat_message("assistant").code(trace.response.model_dump_json(indent=2), language="json", wrap_lines=True)
    with col2:
        st.header("Response Model")
        st.chat_message("format").code(json.dumps(trace.response_model.model_json_schema(), indent=2), language="json", wrap_lines=True)


if __name__ == "__main__":
    main()
