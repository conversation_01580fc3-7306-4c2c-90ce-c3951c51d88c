import asyncio
import json
import pprint

import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as aes
import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_task as aet
import services.studio._text_to_workflow.common.schema as cs


class ActivityEditChatClient:
    def __init__(self, model_name: str, datapoint: dict):
        self.model_name = model_name
        self.datapoint = datapoint
        self.workflow = datapoint["input"]["workflow"]
        self.metadata = datapoint["input"]["metadata"]
        self.schemas = datapoint["input"]["schemas"]
        self.expression_language = datapoint["input"]["language"]
        self.expected_activity = datapoint["expected"]["activity"]
        self.activity_id = datapoint["input"]["activity_id"]
        self.activity = find_activity_by_key(self.workflow, self.activity_id)
        self.messages: list[cs.Message] = []
        self.responses: list[aes.TaskResponse] = []
        self.traces: list[aes.Trace] = []

    async def ask(self, user_message: cs.Message):
        self.messages.append(user_message)
        task = aet.get(self.model_name)
        request = aes.TaskRequest(
            messages=self.messages,
            activity_id=self.activity_id,
            expression_language=self.expression_language,
            workflow=self.workflow,
            metadata=self.metadata,
            schemas=self.schemas,
        )
        response, usage, trace = await task.run(request)
        self.messages.append({"role": "assistant", "content": response.message})
        self.responses.append(response)
        self.traces.append(trace)
        self.update_current_workflow(response)

    def update_current_workflow(self, response: aes.TaskResponse) -> None:
        """Apply each operation from the response to update the workflow."""
        for operation in response.arguments_operations:
            self._apply_workflow_input_operation(operation)
        for operation in response.properties_operations:
            self._apply_field_operation(operation)

    def _apply_field_operation(self, operation: aes.ActivityPropertyOperation) -> None:
        """Apply a field operation to the current activity."""
        if operation.type == aes.OperationType.UPDATE:
            try:
                obj = self.activity
                for part in operation.target.split(".")[:-1]:
                    obj = obj[part]
                obj[operation.target.split(".")[-1]] = operation.value
            except Exception:
                print(f"Could not set property {operation.target} in {self.activity_id} with value {operation.value}")
        elif operation.type == aes.OperationType.CLEAR:
            try:
                obj = self.activity
                for part in operation.target.split(".")[:-1]:
                    obj = obj[part]
                del obj[operation.target.split(".")[-1]]
            except Exception:
                print(f"Could not remove property {operation.target} from {self.activity_id}")
        else:
            raise ValueError(f"Unknown field operation type: {operation.type}")

    def _apply_workflow_input_operation(self, operation: aes.WorkflowArgumentOperation) -> None:
        """Apply a workflow input operation to the workflow."""
        if operation.direction == aes.WorkflowArgumentDirection.INPUT:
            arguments = self.workflow["input"]
        else:
            arguments = self.workflow["output"]

        if operation.type == aes.OperationType.UPDATE:
            arguments[operation.name] = operation.schema_.model_dump()
        elif operation.type == aes.OperationType.CLEAR:
            del arguments[operation.name]
        else:
            raise ValueError(f"Unknown workflow input operation type: {operation.type}")


def find_activity_by_key(workflow: dict, activity_id: str) -> dict:
    """Find the activity based on its key in the workflow. Simple traversal using a list with pop operations."""
    activities = list_activities(workflow=workflow)
    for activity in activities:
        if activity["id"] == activity_id:
            return activity
    raise ValueError(f"Activity with key {activity_id} not found")


def list_activities(workflow: dict) -> list[dict]:
    """List all activities in the workflow."""
    to_visit: list = [(None, workflow)]
    activities: list[dict] = []
    while to_visit:
        obj_key, obj_value = to_visit.pop()
        if isinstance(obj_value, dict):
            if "activity" in obj_value:
                activities.append(obj_value)
            for key, value in obj_value.items():
                to_visit.append((key, value))
        elif isinstance(obj_value, list):
            for index, value in enumerate(obj_value):
                to_visit.append((f"{obj_key}[{index}]", value))
    return activities


if __name__ == "__main__":
    import services.studio._text_to_workflow.utils.request_utils as ru
    import services.studio._text_to_workflow.utils.testing as ut

    ru.set_request_context(ut.get_testing_request_context("en", "7751aee1-3ac2-4280-8d9f-e5075c7cde28", "API Activity Edit"))
    path = "/data/andreisili/ml.autopilot/autopilot/.data/Autopilot.Samples/Dataset/APIActivityEdit/JQ/SmallComplexity/Cancel Latest Time Off Request in BambooHR/BambooHRChange_Time_off_Request_Status.update_time_off_status.json"
    message = "Cancel the latest time off request"
    with open(path) as f:
        datapoint = json.load(f)
    chat = ActivityEditChatClient(model_name="api_activity_edit_openai_model", datapoint=datapoint)
    asyncio.run(chat.ask({"role": "user", "content": message}))
    pprint.pprint(chat.traces[-1])
