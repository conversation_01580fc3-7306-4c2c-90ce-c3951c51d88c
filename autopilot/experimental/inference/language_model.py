import asyncio
import time
import typing as t

import anthropic
import httpx
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
import langchain_openai
from azure.core.credentials import AzureKeyCredential

from services.studio._text_to_workflow.utils.errors import FailedDependencyError
from services.studio._text_to_workflow.utils.inference import llm_gateway_model, llm_schema
from services.studio._text_to_workflow.workflow_generation import workflow_generation_schema

N_RETRIES = 1


class LLModel:
    def __init__(self, model_type, model_config, allow_cache=True, consuming_feature_type=llm_schema.ConsumingFeatureType.DEFAULT):
        # model_config is a dict containing deployment_name temperature, etc. for OpenAI
        self.model_type = model_type
        self.model_config = model_config
        if self.model_type == "openai":
            if not allow_cache:
                model_config["cache"] = False
            self.model = llm_gateway_model.LLMGatewayModel(consuming_feature_type, **model_config)
        elif self.model_type == "anthropic":
            self.model = anthropic.Anthropic()
        elif self.model_type == "llama2":
            self.model = "llama2"
        elif self.model_type == "llama3":
            self.model = "llama3"
        elif self.model_type == "llama3-azure":
            self.model = "llama3"
        elif self.model_type == "llmgateway":
            self.model = llm_gateway_model.LLMGatewayMultiModel(consuming_feature_type, **model_config)
        else:
            raise Exception("unknown LLM model type")

    def get_deployment_name(self):
        if self.model_type == "openai":
            return self.model.deployment_name
        elif self.model_type == "anthropic":
            return self.model_config["model"]
        elif self.model_type in {
            "llama2",
            "llama3",
            "llama3-azure",
            "llmgateway",
        }:
            return self.model_config["deployment_name"]
        else:
            raise Exception("unknown LLM model type")

    def get_num_tokens_from_messages(self, messages):
        """This method is currently used in generation for pruning the prompt"""
        if self.model_type == "openai":
            # There are some inconsistencies here, as openai returns a higher usage than expected
            # 14 messages expected 9085 returned 9104 (19 diff)
            # 22 messages expected 1259 returned 1284 (25 diff)
            # 22 messages expected 1304 returned 1330 (26 diff)
            # it does not seem to be dependent on the number of messages solely (I expected it's something to do with the roles)
            # verified that the self.model.get_num_tokens use the correct encoder and for gpt35 it uses the correct one (cl100k_base)
            # for the moment, it's good to have some tolerance on the maximum number of tokens
            prompt = langchain.prompts.ChatPromptTemplate.from_messages(messages).format()
            return self.model.get_num_tokens(prompt)
        elif self.model_type == "anthropic":
            # WARNING: this in an approximation, as anthropic does not yet provide a clear way to do it
            return sum(self.model.count_tokens(f"Role {m.type}:\n\n{m.content}\n\n") for m in messages)
        else:
            # broad approximation
            return int(sum([len(message.content) for message in messages]) / 4)  # approximate 1 token at each 4 characters

    @property
    def max_tokens(self):
        return self.model_config["max_tokens"]

    @max_tokens.setter
    def max_tokens(self, value: int):
        self.model_config["max_tokens"] = value
        if isinstance(self.model, langchain_openai.AzureChatOpenAI):
            self.model.model_kwargs["max_tokens"] = value
            self.model.max_tokens = value
        elif isinstance(self.model, langchain_openai.OpenAI):
            self.model.model_kwargs["max_tokens"] = value
            self.model.max_tokens = value

    async def predict(self, chat_prompt_template, inputs, retries=N_RETRIES) -> tuple[str, workflow_generation_schema.TokenUsage]:
        start_time = time.time()
        if self.model_type == "openai":
            chat_chain = chat_prompt_template | self.model
            with langchain_community.callbacks.get_openai_callback() as cb:
                result = (await chat_chain.ainvoke(inputs)).content
                usage = workflow_generation_schema.TokenUsage(
                    model=self.model.deployment_name,
                    prompt_tokens=cb.prompt_tokens,
                    completion_tokens=cb.completion_tokens,
                    total_tokens=cb.total_tokens,
                )
        elif self.model_type == "anthropic":

            async def retry():
                sleeping_time = 8 ** (N_RETRIES - retries)
                print(f"Waiting {sleeping_time} to retry.")
                await asyncio.sleep(sleeping_time)
                return await self.predict(chat_prompt_template, inputs, retries - 1)

            try:
                # version 1 - using the langchain_anthropic
                # chat_model = langchain_anthropic.ChatAnthropic(**self.model_config)
                # chain = chat_prompt_template | chat_model
                # chain.invoke(inputs)
                # problems with langchain compatibility

                # version 2 - using the anthropic library directly - messages
                messages = chat_prompt_template.format_messages(**inputs)

                system_message = "\n\n".join(m.content for m in messages if m.type == "system")
                _type_mapping = {"human": "user", "ai": "assistant"}
                chat_messages = [{"role": _type_mapping[m.type], "content": m.content} for m in messages if m.type != "system"]

                response = self.model.messages.create(
                    model=self.model_config["model"],
                    system=system_message,
                    messages=chat_messages,
                    max_tokens=self.model_config["max_tokens"],  # for completion it's "max_tokens_to_sample", for chat it's "max_tokens"
                )
                result = "\n\n".join(reply.text for reply in response.content)  # response.content is a list of ContentBlocks (.text is the actual text)
                prompt_tokens = response.usage.input_tokens
                completion_tokens = response.usage.output_tokens

                # version 3 - using the anthropic library directly - completions
                # altered_prompt = prompt.replace("Human:", "\n\nHuman:").replace("AI:", "\n\nAssistant:") + "\n\nAssistant: "
                # anthropic requires the newlines to be doubled before each message
                # completion = self.model.completions.create(
                #     prompt=altered_prompt,
                #     **self.model_config,
                # )
                # result = completion.completion
                # prompt_tokens = self.model.count_tokens(prompt)
                # completion_tokens = self.model.count_tokens(result)

                usage = workflow_generation_schema.TokenUsage(
                    model=self.model_config["model"],
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=prompt_tokens + completion_tokens,
                )
                if "1." in result:
                    # claude 1 on planning would often prefix the response with some chatter
                    _, result = result.split("1.", 1)
                    result = "1." + result
                if "```yaml" in result:
                    _, result = result.split("```yaml", 1)
                    result = "```yaml" + result
            except anthropic.APIConnectionError as e:
                print("The server could not be reached")
                print(e.__cause__)  # an underlying Exception, likely raised within httpx.
                raise e
            except anthropic.RateLimitError as e:
                print("A 429 status code was received; we should back off a bit.")
                if retries > 0:
                    return await retry()
                raise e
            except anthropic.InternalServerError as e:
                if e.status_code == 529:
                    print("Anthropic server overloaded.")
                    if retries > 0:
                        return await retry()
                print("Internal server error.")
                if retries > 0:  # retry for this too for the moment
                    return await retry()
                raise e
            except anthropic.APIStatusError as e:
                print("Another non-200-range status code was received")
                print(e.status_code)
                print(e.response)
                # import traceback
                # traceback.print_exc()
                raise e

        elif self.model_type == "llama2-vllm" or self.model_type == "llama3":
            messages = []
            for message in chat_prompt_template.format_messages(**inputs):
                if message.type == "system":
                    role = "system"
                elif message.type == "human":
                    role = "user"
                else:
                    role = "assistant"

                message_dict = {"role": role, "content": message.content}
                messages.append(message_dict)

            params = {
                "model": self.model_config["deployment_name"],
                "messages": messages,
                "temperature": self.model_config.get("temperature", 0),
                "max_tokens": 2000,
            }
            # "model": "/data/llama3.1/Meta-Llama-3.1-70B-Instruct",
            # "model": "/data/llama3.1/Meta-Llama-3.1-70B-Instruct-quantized.w8a8",
            # "use_beam_search": False,
            # "n": 1,
            try:
                resp = await httpx.AsyncClient(timeout=10).post(self.model_config["custom_url"], json=params)
            except httpx.ConnectTimeout as e:
                raise FailedDependencyError("Timeout connecting to the LLM model") from e
            content = resp.json()
            result = content["choices"][0]["message"]["content"]
            usage = workflow_generation_schema.TokenUsage(
                model=self.model_type,
                prompt_tokens=content.get("usage", {}).get("prompt_tokens", 0),
                completion_tokens=content.get("usage", {}).get("completion_tokens", 0),
                total_tokens=content.get("usage", {}).get("total_tokens", 0),
            )
        elif self.model_type == "llama3-azure":
            messages = []
            for message in chat_prompt_template.format_messages(**inputs):
                if message.type == "system":
                    role = "system"
                elif message.type == "human":
                    role = "user"
                else:
                    role = "assistant"

                message_dict = {"role": role, "content": message.content}
                messages.append(message_dict)

            from azure.ai.inference import ChatCompletionsClient

            client = ChatCompletionsClient(
                endpoint=self.model_config["azure_endpoint"],
                credential=AzureKeyCredential(self.model_config["azure_key"]),
            )
            # model_info = client.get_model_info()
            # print("Model name:", model_info.model_name)
            # print("Model type:", model_info.model_type)
            # print("Model provider name:", model_info.model_provider_name)
            payload = {
                # "model": self.model_config["deployment_name"],
                "messages": messages,
                "temperature": self.model_config.get("temperature", 0),
                "max_tokens": self.model_config.get("max_tokens", 2000),
            }
            response = client.complete(payload)

            result = response.choices[0].message.content
            usage = workflow_generation_schema.TokenUsage(
                model=response.model,
                prompt_tokens=response.usage.prompt_tokens,
                completion_tokens=response.usage.completion_tokens,
                total_tokens=response.usage.total_tokens,
            )
        elif self.model_type == "llmgateway":
            self.model = t.cast(llm_gateway_model.LLMGatewayMultiModel, self.model)
            messages = chat_prompt_template.format_messages(**inputs)
            response = await self.model.send_llm_request(messages, **inputs)

            result = response["choices"][0]["message"]["content"]

            n_prompt_tokens = response["usage"].get("prompt_tokens", self.get_num_tokens_from_messages(messages))
            n_completion_tokens = response["usage"].get(
                "completion_tokens", self.get_num_tokens_from_messages([langchain.schema.BaseMessage(type="ai", content=result)])
            )
            n_total_tokens = response["usage"].get("total_tokens", n_prompt_tokens + n_completion_tokens)
            usage = workflow_generation_schema.TokenUsage(
                model=self.get_deployment_name(),
                prompt_tokens=n_prompt_tokens,
                completion_tokens=n_completion_tokens,
                total_tokens=n_total_tokens,
            )
        else:
            raise Exception("unknown LLM model type")

        print("llm", time.time() - start_time, self.model_type, self.get_deployment_name(), "tokens", usage.total_tokens)
        return result, usage
