import argparse
import asyncio
import copy
import pathlib

import tqdm
import typing_extensions as t

from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint
from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest
from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework
from services.studio._text_to_workflow.common.walkers import DAPClassNameTranslator, PlanBuilder, WorkflowThoughtsModifier, WorkflowThoughtsModifierWithLookup
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.testing import build_testing_request_context
from services.studio._text_to_workflow.utils.workflow_utils import get_display_names
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import prompt_llm_for_plan_refinement, prompt_llm_for_query


async def generate_query_refinement(datapoint):
    workflow_obj = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])
    return await prompt_llm_for_query(build_testing_request_context(), workflow_obj)


async def generate_plan_refinement(datapoint):
    workflow = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])
    loaded_workflow = DAPClassNameTranslator().translate(workflow).to_dict()
    refined_plan = await prompt_llm_for_plan_refinement(yaml_dump(loaded_workflow), datapoint["description"], datapoint["plan"], loaded_workflow)
    return refined_plan.strip().removeprefix("```yaml\n").removesuffix("```").strip()


def are_equivalent_plans(workflow: dict, plan1: str, plan2: str):
    process_name = workflow.get("processName")
    if process_name == "IFELSE" or process_name == "RETRYSCOPE":  # temporary fallback due to invalid structure
        return plan1 == plan2
    # this removes the eventual overlap between synthetic steps and generated ones
    workflow_object = Workflow("", "", workflow)
    try:
        WorkflowThoughtsModifier(get_display_names(plan1)).replace(workflow_object)
    except Exception as e:
        print("WARNING! Attempt to use plan1 for comparison failed for", process_name, e)
        return plan1 == plan2
    plan1 = PlanBuilder().build(workflow_object)
    try:
        WorkflowThoughtsModifier(get_display_names(plan2)).replace(workflow_object)
    except Exception as e:
        print("WARNING! Attempt to use plan1 for comparison failed for", process_name, e)
        return plan1 == plan2
    plan2 = PlanBuilder().build(workflow_object)

    # exclude the trigger from the comparison if it is a manual trigger (is forcefully added)
    if workflow_object.trigger is not None and workflow_object.trigger.fqn == "UiPath.Core.Activities.ManualTrigger":
        plan1 = "\n".join(plan1.split("\n")[1:])
        plan2 = "\n".join(plan2.split("\n")[1:])
    return plan1 == plan2


async def _infer_refinement_for_metadata(
    identifier: str, metadatapoint: dict[str, str], datapoint: dict[str, t.Any], force_generate: bool = False
) -> dict[str, str]:
    identifier_placeholder = datapoint.get("name", "N/A")

    if not force_generate and "query-refined" in metadatapoint:
        query_refined = metadatapoint["query-refined"]
    else:
        print(f"Generating query refinement for {identifier_placeholder}")
        query_refined = await generate_query_refinement(datapoint)

    if not force_generate and "generated-plan-refined" in metadatapoint:
        plan_refined = metadatapoint["generated-plan-refined"]
    else:
        print(f"Generating plan refinement for {identifier_placeholder}")
        plan_refined = await generate_plan_refinement(datapoint)

    return {"query-refined": query_refined, "generated-plan-refined": plan_refined}


async def _infer_summarization_for_metadata(
    identifier: str,
    metadatapoint: dict[str, str],
    datapoint: dict[str, t.Any],
    force_generate: bool = False,
    model_name: str = "gpt-4o-2024-08-06",
) -> dict[str, str]:
    if not force_generate and all(
        k in metadatapoint
        for k in [
            # "proposal-filename",
            "query-description",
            "query-summary",
            "generated-plan-descriptions",
            "generated-plan-summaries",
        ]
    ):
        proposal_filename = metadatapoint.get("proposal-filename", "")
        query_description = metadatapoint["query-description"]
        query_summary = metadatapoint["query-summary"]
        plan_descriptions = metadatapoint["generated-plan-descriptions"]
        plan_summaries = metadatapoint["generated-plan-summaries"]
    else:
        workflow_object = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])
        workflow_str = workflow_object.lmyaml(include_ids=True, include_name=False, include_packages=False)

        endpoint_response = await activity_summary_endpoint.summarize(
            ActivitySummaryRequest(activity=workflow_str),
            identifier=identifier,
            model_to_use=model_name,
        )
        summary_result = endpoint_response["result"]

        _workflow_with_descriptions = copy.deepcopy(workflow_object)
        _id_to_descriptions = {item["id"]: item["description"] for item in summary_result.get("activityDescriptions", [])}
        WorkflowThoughtsModifierWithLookup(_id_to_descriptions).replace(_workflow_with_descriptions)
        query_description = summary_result.get("workflowShortDescription", "")
        plan_descriptions = PlanBuilder().build(_workflow_with_descriptions)

        workflow_with_summaries = copy.deepcopy(workflow_object)
        id_to_summaries = {item["id"]: item["summary"] for item in summary_result.get("activitySummaries", [])}
        WorkflowThoughtsModifierWithLookup(id_to_summaries).replace(workflow_with_summaries)
        query_summary = summary_result.get("workflowSummary", "")
        plan_summaries = PlanBuilder().build(workflow_with_summaries)

        proposal_filename = summary_result["workflowFileName"]
    return {
        "proposal-filename": proposal_filename,
        "query-description": query_description,
        "query-summary": query_summary,
        "generated-plan-descriptions": plan_descriptions,
        "generated-plan-summaries": plan_summaries,
    }


async def _add_to_metadata(
    infer_function_on_datapoint: t.Callable[[str, dict[str, str], dict[str, t.Any], bool], t.Awaitable[dict[str, str]]],
    target_framework: TargetFramework,
    subset: SubsetName | None = None,
    force_generate: bool = False,
    tqdm_desc: str = "adding to metadata",
):
    if subset is None:
        await _add_to_metadata(infer_function_on_datapoint, target_framework, "train", force_generate, tqdm_desc)
        await _add_to_metadata(infer_function_on_datapoint, target_framework, "test", force_generate, tqdm_desc)
        return

    metadata_path = paths.get_workflow_generation_metadata_path(target_framework)
    metadata = yaml_load(metadata_path)

    # ensure proper formatting in case of partial processing
    for _identifier, metadatapoint in metadata.items():
        for k, v in metadatapoint.items():
            if "plan" in k and not isinstance(v, multiline_str):
                metadatapoint[k] = multiline_str(v)

    dataset_path = paths.get_workflow_generation_dataset_path(target_framework, subset)
    listing = sorted(dataset_path.glob("*.yaml"))
    iterator_obj = tqdm.tqdm(listing, desc=tqdm_desc)
    for i, datapoint_path in enumerate(iterator_obj):
        identifier = datapoint_path.stem
        datapoint = yaml_load(datapoint_path)

        if identifier not in metadata:
            print(f"First time entry in metadata for {identifier}")
            metadata[identifier] = {"query-original": datapoint["description"], "plan-original": datapoint["plan"]}
        elif all(not are_equivalent_plans(datapoint["process"], datapoint["plan"], v) for k, v in metadata[identifier].items() if "plan" in k):
            print(f"Unknown current plan, regenerating metadata for {identifier}")
            metadata[identifier] = {k: v for k, v in metadata[identifier].items() if k == "query-original" or k == "plan-original"}
        elif all(datapoint["description"] != v for k, v in metadata[identifier].items() if "query" in k):
            print(f"Unknown current query, regenerating metadata for {identifier}")
            metadata[identifier] = {k: v for k, v in metadata[identifier].items() if k == "query-original" or k == "plan-original"}
        metadata[identifier].update({"query-current": datapoint["description"], "plan-current": datapoint["plan"]})

        existing_metadatapoint = metadata[identifier]
        try:
            incoming_metadatapoint = await infer_function_on_datapoint(identifier, existing_metadatapoint, datapoint, force_generate)
        except Exception as e:
            print(f"Failed to infer for {identifier}: {e}")
            incoming_metadatapoint = {}

        metadatapoint = {}  # enforce order
        metadatapoint.update({k: v for k, v in existing_metadatapoint.items() if "query" not in k and "plan" not in k})
        metadatapoint.update({k: v for k, v in incoming_metadatapoint.items() if "query" not in k and "plan" not in k})
        metadatapoint.update({k: v for k, v in existing_metadatapoint.items() if "query" in k})
        metadatapoint.update({k: v for k, v in incoming_metadatapoint.items() if "query" in k})
        metadatapoint.update({k: multiline_str(v) for k, v in existing_metadatapoint.items() if "plan" in k})
        metadatapoint.update({k: multiline_str(v) for k, v in incoming_metadatapoint.items() if "plan" in k})
        metadata[identifier] = metadatapoint

        if i and i % 30 == 0:  # periodical flush
            yaml_dump(metadata, metadata_path)
    yaml_dump(metadata, metadata_path)


async def generate_query_and_plan_into_metadata(
    target_framework: t.Literal["Portable", "Windows"],
    subset: SubsetName | None = None,
    force_generate: bool = False,
):
    await _add_to_metadata(_infer_refinement_for_metadata, target_framework, subset, force_generate, "Generating refinements")


async def generate_summarization_into_metadata(
    target_framework: t.Literal["Portable", "Windows"],
    subset: SubsetName | None = None,
    force_generate: bool = False,
):
    await _add_to_metadata(_infer_summarization_for_metadata, target_framework, subset, force_generate, "Generating summarizations")


async def generate_query_and_plan_inline(yaml_path: pathlib.Path, target_framework: t.Literal["Portable", "Windows"] = "Portable"):
    print(yaml_path.as_posix())
    datapoint = yaml_load(yaml_path)

    metadata = yaml_load(paths.get_workflow_generation_metadata_path(target_framework))
    if yaml_path.stem in metadata:
        # refined_query = metadata[yaml_path.stem].get("query-refined")
        refined_query = metadata[yaml_path.stem].get("query-description")
        # refined_plan = metadata[yaml_path.stem].get("generated-plan-refined")
        refined_plan = metadata[yaml_path.stem].get("generated-plan-descriptions")
    else:
        print("Did not find entry in metadata.yaml. Generating...")
        refined_query = await generate_query_refinement(datapoint)
        refined_plan = await generate_plan_refinement(datapoint)

    # keep the order of datapoint and insert the refined variants after the original ones
    output = {}
    for k, v in datapoint.items():
        if k in ["conversion_errors", "plan"]:
            v = multiline_str(v)
        output[k] = v
        if k == "description":
            output["description-refined"] = refined_query
        elif k == "plan":
            output["plan-refined"] = multiline_str(refined_plan)
    # in case we don't have plan / descriptions in datapoint, this is a fallback
    output["description-refined"] = output.get("description-refined", refined_query)
    output["plan-refined"] = output.get("plan-refined", multiline_str(refined_plan))
    yaml_dump(output, yaml_path)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("job", type=str, choices=["metadata", "metadata-refinement", "metadata-summarization", "file"], help="Mode of operation.")
    ap.add_argument(
        "--target-framework",
        "--target_framework",
        "--framework",
        type=str,
        default="Portable",
        choices=["Portable", "Windows"],
        help="Target framework to generate for.",
    )
    args, unknown = ap.parse_known_args()

    if args.job in {"metadata", "metadata-refinement", "metadata-summarization"}:
        ap.add_argument(
            "--subset",
            type=str,
            default=None,
            choices=["train", "test"],
            help="Subset of the dataset to process. If not provided, both train and test subsets will be processed.",
        )
        ap.add_argument(
            "--force-generation",
            "--force_generation",
            action="store_true",
            help="Force generation of metadata, even if it",
        )
        args = ap.parse_args()
        if args.job == "metadata" or args.job == "metadata-refinement":
            asyncio.run(generate_query_and_plan_into_metadata(args.target_framework, args.subset, args.force_generation))
        if args.job == "metadata" or args.job == "metadata-summarization":
            asyncio.run(generate_summarization_into_metadata(args.target_framework, args.subset, args.force_generation))
    if args.job == "file":
        ap.add_argument("yaml_path", type=pathlib.Path, help="Path to a worfklow generation yaml.")
        args = ap.parse_args()
        asyncio.run(generate_query_and_plan_inline(args.yaml_path, args.target_framework))
