import concurrent.futures
import json
import os
import shutil
import tempfile
import time
import zipfile
from pathlib import Path

import requests

# windows converter also supports portable
# the keys are in 1pass under "wingman converter"
# older method (used azure functions) - not used anymore
# CONVERTER_CONFIGS = {
#     "legacy": {"url": "https://wf-converter-legacy.azurewebsites.net/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_LEGACY_KEY", "")},
#     "windows": {"url": "https://wf-converter.azurewebsites.net/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_WINDOWS_KEY", "")},
# }

# CONVERTER_CONFIGS = {
#     "legacy": {"url": "http://**************/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_LEGACY_KEY", "")},
#     "windows": {"url": "http://**************/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_WINDOWS_KEY", "")},
# }


CONVERTER_CONFIGS = {
    "legacy": {
        "url": "https://autopilot-workfow-api-f6gkgqapf3cdb0dk.germanywestcentral-01.azurewebsites.net/v1/workflow/workflow-to-yaml",
        "key": os.environ.get("CONVERTER_LEGACY_KEY", ""),
    },
    "windows": {
        "url": "https://autopilot-workfow-api-f6gkgqapf3cdb0dk.germanywestcentral-01.azurewebsites.net/v1/workflow/workflow-to-yaml",
        "key": os.environ.get("CONVERTER_WINDOWS_KEY", ""),
    },
}

# CONVERTER_CONFIGS = {
#     "legacy": {"url": "http://52.155.231.71/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_LEGACY_KEY", "")},
#     "windows": {"url": "http://52.155.231.71/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_WINDOWS_KEY", "")},
# }

# CONVERTER_CONFIGS = {
#     "legacy": {"url": "http://52.155.231.76/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_LEGACY_KEY", "")},
#     "windows": {"url": "http://52.155.231.76/trigger-job/api/Workflow2Yaml", "key": os.environ.get("CONVERTER_WINDOWS_KEY", "")},
# }


def _get_request_url(target_platform: str):
    # older method (used azure functions) - not used anymore
    # return f"{CONVERTER_CONFIGS[target_platform]['url']}?code={CONVERTER_CONFIGS[target_platform]['key']}"
    url = CONVERTER_CONFIGS[target_platform]["url"]
    print(f"{url=}")
    return url


def _get_payload_data(workflow_name: str | None = None, mutli_workflow: bool | None = None):
    request_input_data = {
        "Simulate": True,
        "ReplaceWriteLine": True,
        "EnhanceSystemActivities": True,
        "SkipNull": True,
        "Truncate": False,
        "GenerateTypeDefinition": True,
        "IncludeActivityIds": True,
        # "IsolateAssemblyContext": False,
    }
    if workflow_name is not None:
        request_input_data["WorkflowName"] = workflow_name  # type: ignore
    if mutli_workflow is not None:
        request_input_data["MultiWorkflow"] = mutli_workflow
    payload_data = {"requestInput": json.dumps(request_input_data)}

    return payload_data


def send_conversion_request(target_platform, zip_path, workflow_name=None, multi_worklow=True):
    """Send a request to the converter API to convert an archived XAML to a YAML workflow.

    Args:
        target_platform (str): The target platform of the process. Either "legacy" or "windows".
        zip_path (str): The path to the zip file containing the XAML files.
        workflow_name (str): The name of the workflow to convert.

    Returns:
        tuple[bool, bytes]: A tuple containing a boolean indicating the success of the conversion and the converted zip file.
    """
    with open(zip_path, "rb") as f:
        files = {"projectZip": ("data.zip", f, "application/zip")}
        response = requests.post(_get_request_url(target_platform), files=files, data=_get_payload_data(workflow_name, multi_worklow))
        if response.status_code != 200:
            return (False, repr(response.__dict__))

        return (True, response.content)


def convert_project(project_json_path: Path, output_folder: Path, target_platform: str, tmpdir: str | None = None, jobid: str = "0"):
    if tmpdir is None:
        tmpdir = tempfile.mkdtemp()
    if output_folder.exists():
        shutil.rmtree(output_folder)
    os.makedirs(output_folder, exist_ok=False)

    shutil.copyfile(project_json_path, output_folder / "project.json")

    project_path = project_json_path.parent
    shutil.make_archive(f"{tmpdir}/input_{jobid}", "zip", project_path)
    zip_path = f"{tmpdir}/input_{jobid}.zip"
    is_success, result = send_conversion_request(target_platform, zip_path, multi_worklow=True)
    if is_success:
        with open(f"{tmpdir}/output_{jobid}.zip", "wb") as f:
            f.write(result.encode(encoding="utf-8-sig") if isinstance(result, str) else result)
        with zipfile.ZipFile(f"{tmpdir}/output_{jobid}.zip", "r") as zip_ref:
            zip_ref.extractall(output_folder)
    else:
        print(f"Conversion failed for {output_folder} with error: {result}")
    return is_success


def convert_workflow(zip_path: str, output_folder: str, target_platform: str, jobid: str):
    print(f"Starting conversion for {output_folder}")
    start_time = time.time()
    tmpdir = tempfile.mkdtemp()
    is_success, result = send_conversion_request(target_platform, zip_path, multi_worklow=True)
    duration = time.time() - start_time
    if is_success:
        with open(f"{tmpdir}/output{jobid}.zip", "wb") as f:
            f.write(result.encode(encoding="utf-8-sig") if isinstance(result, str) else result)
        with zipfile.ZipFile(f"{tmpdir}/output{jobid}.zip", "r") as zip_ref:
            zip_ref.extractall(output_folder)
        print(f"Conversion done in {duration} seconds for {output_folder}")
    else:
        print(f"Conversion failed in {duration} seconds for {output_folder} with error: {result}")
    return duration


def convert_multiple_workflows(jobs, num_workers):
    if num_workers == 0:
        print("online converter no concurency")
        duration = 0
        for job in jobs:
            # disabled the recovery mechanism until the server is stable
            # if duration > 60:
            #     print("*** waiting for endpoint to recover")
            #     time.sleep(60)
            duration = convert_workflow(job["zip_path"], job["output_folder"], job["target_platform"], job["jobid"])
    else:
        print("online converter with concurency", num_workers)
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
            max_duration = 0.0
            for i in range(0, len(jobs), num_workers):
                # disabled the recovery mechanism until the server is stable
                # if max_duration > 60:
                #     print("*** waiting for endpoint to recover")
                #     time.sleep(60)
                futures = {
                    executor.submit(convert_workflow, j["zip_path"], j["output_folder"], j["target_platform"], j["jobid"]): j for j in jobs[i : i + num_workers]
                }
                max_duration = 0.0
                for future in concurrent.futures.as_completed(futures):
                    jobdict = futures[future]
                    try:
                        duration = future.result()
                        if duration > max_duration:
                            max_duration = duration
                    except Exception as exc:
                        print(f"{jobdict} generated an exception: {exc}")


if __name__ == "__main__":
    import argparse

    ap = argparse.ArgumentParser()
    ap.add_argument("--project-path", type=str, required=True)
    ap.add_argument("--converted-path", type=str, required=True)
    ap.add_argument("--platform", default="windows", choices=CONVERTER_CONFIGS.keys())

    args, unknown = ap.parse_known_args()
    temp_dir = Path(tempfile.mkdtemp())
    temp_input_path = temp_dir / "input.zip"
    print(f"{temp_input_path=}")
    shutil.make_archive(temp_input_path.with_suffix("").as_posix(), "zip", args.project_path)
    is_success, result = send_conversion_request(args.platform, temp_input_path, multi_worklow=True)
    if not is_success:
        raise Exception(f"Conversion failed for {args.project_path} with error: {result}")
    temp_output_path = temp_dir / "output.zip"
    print(f"{temp_output_path=}")
    with temp_output_path.open("wb") as f:
        f.write(result)  # type: ignore
    # temp_output_dir_path = temp_output_path.with_suffix("")
    with zipfile.ZipFile(temp_output_path, "r") as zip_ref:
        zip_ref.extractall(args.converted_path)
