import asyncio
import os
import sys
import typing as t
from urllib.parse import quote_plus

import httpx

SCOPES = "OR.Jobs OR.Jobs.Read OR.Jobs.Write"
TOKEN_URL = "https://{env}.uipath.com/identity_/connect/token"
STAGING_TOKEN_FETCH_PROCESS_ID = "e23a8f30-3a80-4785-b7e3-56a04dd2c3e0"


async def get_studio_token(env: str = "staging", client_id: t.Optional[str] = None, client_secret: t.Optional[str] = None):
    # First, get the app token
    token = await get_app_token(env, client_id, client_secret)

    # Then, elevate by getting the Studio Automation Developer token using the app token
    print("Elevating to a Studio Automation Developer token...", file=sys.stdout)
    async with httpx.AsyncClient() as client:
        headers = {"Authorization": token}
        # TODO: We should make the process id parameterized in the future
        # This is currently calling into the Orchestrator API triggers in the default tenant of the autopilot account
        response = await client.get(
            "https://{env}.uipath.com/autopilot_templates/DefaultTenant/orchestrator_/t/{process_id}/getStudioToken".format(
                env=env, process_id=STAGING_TOKEN_FETCH_PROCESS_ID
            ),
            follow_redirects=True,
            headers=headers,
            timeout=300,
        )
        result = response.json()

    return result["token"]


async def get_app_token(env, client_id, client_secret):
    print("Retrieving Automation Cloud application token...", file=sys.stdout)

    client_id, client_secret = client_id or os.getenv("UIPATH_CLIENT_ID"), client_secret or os.getenv("UIPATH_CLIENT_SECRET")

    # loop = asyncio.get_event_loop()
    # auth_code_task = loop.run_in_executor(None, start_oauth_flow)  # Schedule on the thread pool

    data = "grant_type=client_credentials&client_id={app_id}&client_secret={app_secret}&scope={scopes}".format(
        app_id=client_id, app_secret=quote_plus(client_secret), scopes=SCOPES
    )  # type: ignore
    async with httpx.AsyncClient() as client:
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = await client.post(TOKEN_URL.format(env=env), headers=headers, data=data, timeout=30)
        authorization_result = response.json()

    return "Bearer " + authorization_result["access_token"]


# Main workflow
if __name__ == "__main__":
    code = asyncio.run(get_studio_token())  # Run the async main function
    print(code)
