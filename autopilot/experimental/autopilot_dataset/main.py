import argparse
import asyncio

import experimental.autopilot_dataset.wfgen_dataset as workflow_generation_dataset
from experimental.autopilot_dataset import dataset_conversion
from services.studio._text_to_workflow.activity_config import activity_config_dataset
from services.studio._text_to_workflow.activity_summary import activity_summary_dataset
from services.studio._text_to_workflow.common import connections_loader, typedefs
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.utils import paths, request_utils
from services.studio._text_to_workflow.utils.testing import get_testing_request_context


def build_dataset_connections():
    dataset_conversion.build_dataset_connections_json()


async def generate_queries_for_sequence_generation_datasets():
    request_context = request_utils.get_request_context()

    await workflow_generation_dataset.generate_queries_for_sequence_generation_datasets(request_context)


async def create_sequence_generation_dataset(framework: TargetFramework | None):
    request_context = request_utils.get_request_context()

    if framework is None:
        await workflow_generation_dataset.create_sequence_generation_datasets(request_context)
    else:
        await workflow_generation_dataset.create_sequence_generation_dataset(request_context, framework)


def create_workflow_summarization_datasets():
    activity_summary_dataset.create_workflow_summarization_datasets()


async def create_activity_config_dataset():
    await activity_config_dataset.init()
    activity_config_dataset.create_activity_config_datasets()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument(
        "job",
        default="test",
        choices=[
            "extract_uips",
            "build_dataset_connections",
            "convert_downloads",
            "create_workflow_generation_dataset",
            "create_sequence_generation_dataset",
            "generate_sequence_generation_queries",
            "create_activity_config_dataset",
            "create_activity_config_instruct_dataset",
            "create_workflow_summarization_dataset",
        ],
    )

    args, unknown = ap.parse_known_args()

    tenant_id, _ = connections_loader.get_connections_data()
    request_context = get_testing_request_context("en", tenant_id, "Create sequence generation dataset")
    request_utils.set_request_context(request_context)

    if args.job == "extract_uips":
        ap.add_argument("--uips-path", type=str, required=True)
        ap.add_argument("--extract-path", type=str, required=True)
        args = ap.parse_args()
        dataset_conversion.extract_uips(args.uips_path, args.extract_path)
    elif args.job == "build_dataset_connections":
        build_dataset_connections()
    elif args.job == "convert_downloads":
        ap.add_argument("--download-path", type=str, default=paths.get_downloads_dataset_path())
        ap.add_argument("--only-new", default=False, action="store_true")
        ap.add_argument("--only-errors", default=False, action="store_true")
        ap.add_argument("--contains", type=str, default=None)
        ap.add_argument("--local-converter", default=False, action="store_true")
        ap.add_argument("--num-workers", type=int, default=0)
        args = ap.parse_args()
        dataset_conversion.convert_downloads(args.download_path, args.only_new, args.contains, args.local_converter, args.num_workers, args.only_errors)
    elif args.job == "create_workflow_generation_dataset":
        ap.add_argument("--framework", default=None, choices=[None, "Portable", "Windows"])
        ap.add_argument("--inhibit-generation", "--inhibit_generation", action="store_true")
        args = ap.parse_args()
        if args.framework is None:
            asyncio.run(workflow_generation_dataset.create_workflow_generation_datasets(args.inhibit_generation))
        else:
            asyncio.run(workflow_generation_dataset.create_workflow_generation_dataset(args.framework, args.inhibit_generation))
    elif args.job == "create_sequence_generation_dataset":
        ap.add_argument("--framework", default=None, choices=[None, "Portable", "Windows"])
        args = ap.parse_args()
        typedefs.load()  # ensure typedefs are loaded; needed for activity typedef lookup for sequence creation
        asyncio.run(create_sequence_generation_dataset(args.framework))
    elif args.job == "generate_sequence_generation_queries":
        asyncio.run(generate_queries_for_sequence_generation_datasets())
    elif args.job == "create_workflow_summarization_dataset":
        create_workflow_summarization_datasets()
    elif args.job == "create_activity_config_dataset":
        asyncio.run(create_activity_config_dataset())
    else:
        print(f"Unkonw job: {args.job}")
