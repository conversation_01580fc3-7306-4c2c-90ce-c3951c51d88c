import argparse
import collections

import tqdm
import typing_extensions as t

from experimental.autopilot_dataset.wfgen_generate_metadata import are_equivalent_plans
from experimental.autopilot_dataset.wfgen_yaml_to_xaml import commit_to_xaml
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.walkers import PlanBuilder, WorkflowThoughtsModifier
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.workflow_utils import get_diff, get_display_names, get_plan_structure
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset

PlanTypeType: t.TypeAlias = t.Literal[
    "plan-original",
    "generated-plan-refined",
    "generated-plan-descriptions",
    "generated-plan-summaries",
    "plan-manual",
]

DEFAULT_PLAN_TYPE_PRECEDENCE_TO_USE: list[PlanTypeType] = [
    "plan-manual",
    "generated-plan-descriptions",
]


def apply_display_names(
    target_framework: t.Literal["Portable", "Windows"],
    subset: str | None = None,
    plan_precedence_to_use: list[PlanTypeType] | None = None,
    apply_on_yaml: bool = False,
    dry_run: bool = False,
    verbose: bool = False,
    contains: str | None = None,
):
    if plan_precedence_to_use is None:
        plan_precedence_to_use = DEFAULT_PLAN_TYPE_PRECEDENCE_TO_USE[:]
    if subset is None:
        apply_display_names(target_framework, "train", plan_precedence_to_use, apply_on_yaml, dry_run, verbose, contains)
        apply_display_names(target_framework, "test", plan_precedence_to_use, apply_on_yaml, dry_run, verbose, contains)
        return

    metadata_path = paths.get_workflow_generation_metadata_path(target_framework)
    metadata = yaml_load(metadata_path)
    dataset_path = paths.get_workflow_generation_dataset_path(target_framework, subset)

    stats = collections.Counter()
    dataset = workflow_generation_dataset.load_subset(dataset_path)
    for yaml_path, datapoint in tqdm.tqdm(dataset.items()):
        stem = yaml_path.stem

        if contains and contains not in stem:
            # print(f"Skipping {yaml_path} as it does not contain {contains}")
            continue

        if stem not in metadata:
            print(stem, "not found in metadata")
            stats["metadata-not-found"] += 1
            continue

        print(f"Applying metadata for {yaml_path}")
        workflow_object = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])
        existing_plan = datapoint["plan"]
        new_plan = existing_plan
        for plan_type in plan_precedence_to_use:
            if plan_type in metadata[stem]:
                new_plan = metadata[stem][plan_type]
                break

        if existing_plan != (built_plan := PlanBuilder().build(workflow_object)):
            print("WARNING! Inconsistent existing plan for ", stem)
            print(get_diff(existing_plan, built_plan))
            stats["inconsistent-existing-plan"] += 1
            continue

        if get_plan_structure(existing_plan) != get_plan_structure(new_plan):
            print("WARNING! Different plan structure for", stem)
            print(get_diff(get_plan_structure(existing_plan), get_plan_structure(new_plan)))
            print("-" * 30)
            print(existing_plan)
            print("-" * 30)
            print(new_plan)
            stats["different-plan-structure"] += 1
            continue

        display_names = get_display_names(new_plan)
        try:
            WorkflowThoughtsModifier(display_names).replace(workflow_object)
        except Exception as e:
            print("WARNING! Failed to apply new plan to workflow for", stem, e)
            print(existing_plan)
            print("-" * 30)
            print(new_plan)
            stats["failed-to-apply-new-plan"] += 1
            continue

        built_new_plan = PlanBuilder().build(workflow_object)
        if not are_equivalent_plans(datapoint["process"], new_plan, built_new_plan):
            print("WARNING! Inconsistent new plan for", stem)
            print(get_diff(new_plan, built_new_plan))
            stats["inconsistent-new-plan"] += 1
            continue
        new_workflow = workflow_object.to_dict()

        if apply_on_yaml:
            # datapoint["plan"] = multiline_str(new_plan)
            datapoint["plan"] = multiline_str(built_new_plan)
            datapoint["process"] = new_workflow

            # assert format consistency
            datapoint["conversion_errors"] = multiline_str(datapoint["conversion_errors"])

            yaml_dump(datapoint, yaml_path)
        else:
            try:
                commit_to_xaml(yaml_path, None, built_new_plan, dry_run=dry_run, verbose=verbose)
            except ValueError as e:  # converted path not found
                print(f"Skipping. {e}. {yaml_path}")
                stats["error-converting"] += 1
                continue
        stats["success"] += 1
    print(len(dataset))
    print(stats.most_common())


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument(
        "--target-framework",
        "--target_framework",
        "--framework",
        type=str,
        default="Portable",
        choices=t.get_args(TargetFramework),
        help="Target framework to generate for.",
    )
    ap.add_argument(
        "--subset",
        type=str,
        default=None,
        choices=["train", "test"],
        help="Subset of the dataset to process. If not provided, both train and test subsets will be processed.",
    )
    ap.add_argument(
        "--plan-keys",
        "--plan-key",  # for backward compatibility
        type=str,
        default=DEFAULT_PLAN_TYPE_PRECEDENCE_TO_USE,
        choices=t.get_args(PlanTypeType),
        help="Key in metadata to use for the plan.",
        nargs="+",
    )
    ap.add_argument("--yaml", action="store_true", help="This will apply the metadata to the yamls instead of xamls.")
    ap.add_argument("--dry", action="store_true", help="Dry run. Do not write to xaml files.")
    ap.add_argument("--verbose", action="store_true", help="Make it verbose. Prints differences.")
    ap.add_argument("--contains", type=str, default=None, help="Only apply to paths that contain the string.")
    args = ap.parse_args()
    apply_display_names(args.target_framework, args.subset, args.plan_keys, args.yaml, args.dry, args.verbose, args.contains)
