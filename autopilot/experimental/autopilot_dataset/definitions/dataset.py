from typing import Any, TypedDict

from pydantic import BaseModel, Field

from services.studio._text_to_workflow.common.schema import TargetFramework, WorkflowDict


class Variable(TypedDict):
    name: str
    type: str


class Typedefs(TypedDict):
    type: str
    Success: bool
    TypeDefinition: str
    AdditionalTypeDefinitions: str
    AdditionalTypeNamespaces: str


class SequenceGenerationDataPoint(TypedDict):
    target_framework: TargetFramework
    name: str
    description_sequence: str
    description: str
    conversion_success: bool
    conversion_errors: str
    plan_existing: str
    plan_sequence: str
    plan: str
    retrieved_triggers: list[str]
    retrieved_activities: list[str]
    process_existing: WorkflowDict
    process_sequence: list[dict[str, Any]]
    process: WorkflowDict
    typedefs: dict[str, Typedefs]


class WorkflowGenerationDataPoint(TypedDict):
    target_framework: TargetFramework
    name: str
    description: str
    plan: str
    process: WorkflowDict


class QueryPlanningResult(BaseModel):
    thinking: str = Field(description="How would you plan to solve the goal stated in the query?")
    ambiguities: str = Field(description="What are the ambiguities in the query? What is missing?")
    queryScore: int = Field(
        description="A score between -1 and 100, representing how much sense the query makes for an automation and whether it has many ambiguities. -1 is used when the query cannot even be represented as an automation."
    )
