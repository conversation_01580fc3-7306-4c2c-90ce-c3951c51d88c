import argparse

import requests

from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths, uipath_cloud_platform
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

_url = "https://staging.uipath.com/autopilot_templates/DefaultTenant/connections_/api/v1/Connections"
_headers = {
    "authorization": settings.UIPATH_TOKEN,
    "x-sessionid": "<replace this with an updated one from the browser>",
    "x-uipath-folderkey": "YOUR-FOLDER-KEY-HERE",
    "x-uipath-localization": "en",
    "x-uipath-source": "UiPath.IntegrationService.ConnectionService",
    "Cookie": "<replace this with an updated one from the browser>",
}
_folder_keys = [
    "7d5cb6c3-d160-48c6-9c98-9ad080b322c9",
    "659d5e7d-92ca-4a9b-95b8-a2052e9d77cf",
]


def run():
    token = uipath_cloud_platform.get_token_from_env()
    _headers["authorization"] = token
    connections_small = []
    for folder_key in _folder_keys:
        _headers["x-uipath-folderkey"] = folder_key
        response = requests.request("GET", _url, headers=_headers, data={})
        if response.status_code != 200:
            print(response.text)
            raise Exception("Failed to get connections.")
        connections_big: list[dict] = response.json()
        for connection_big in connections_big:
            if connection_big["state"] != "Enabled":
                continue
            connection_small = Connection(
                connectionId=connection_big["id"],
                connector=connection_big["connector"]["key"],
                friendlyName=connection_big["name"],
                state=connection_big["state"],
                isShared=not connection_big["connector"]["isPrivate"],
                isDefault=connection_big["isDefault"],
                embedding=[],
            )
            connections_small.append(connection_small)
    data_path = paths.get_autopilot_samples_dataset_path() / "Connections" / "connections.yaml"
    data: dict = yaml_load(data_path)
    data["connections"] = connections_small
    yaml_dump(data, data_path)
    return connections_small


if __name__ == "__main__":
    description = "Get latest connections from https://staging.uipath.com/autopilot_templates/DefaultTenant/connections_/main?view=connections."
    ap = argparse.ArgumentParser(description)
    run()
