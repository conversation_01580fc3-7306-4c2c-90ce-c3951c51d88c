import collections
import json
import random
import shutil
from pathlib import Path

import aiofiles
import tqdm

from experimental.autopilot_dataset.wfgen_generate_metadata import generate_query_and_plan_into_metadata, generate_summarization_into_metadata
from services.studio._text_to_workflow.common import constants, typedefs
from services.studio._text_to_workflow.common.helpers import get_sample_split, get_workflow_name
from services.studio._text_to_workflow.common.schema import TargetFramework, WorkflowDict
from services.studio._text_to_workflow.common.typedefs_parser import parse_workflow_conversion_typedefs
from services.studio._text_to_workflow.common.walkers import (
    FillMissingWorkflowActivityTypedefs,
    PlanBuilder,
    PlanBuilderWithCategories,
    SequencePlanBuilder,
    WorkflowSequenceExtractor,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.common.workflow_parser import build_yaml_object, has_old_business_entity
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.workflow_utils import get_diff
from services.studio._text_to_workflow.utils.yaml_utils import convert_to_multiline_str, multiline_str, yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.scripts import generate_from_production_data
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import (
    load_metadata_file,
    load_subsets_file,
    load_workflow_generation_dataset,
)


def create_dataset_example(
    converted_json_path: Path,
    typedef_path: Path,
    project_json_path: Path,
    dataset_output_dir_path: Path,
    split: str,
    workflow_name: str,
) -> Path | None:
    with open(project_json_path, "r", encoding="utf-8-sig") as f:
        project_json = json.load(f)
    with open(converted_json_path, "r", encoding="utf-8-sig") as f:
        conversion_result = json.load(f)
    typedefs = {}
    if typedef_path.exists():
        with open(typedef_path, "r", encoding="utf-8-sig") as f:
            typedefs = json.load(f)
    # json response from converter changed at some point.
    conversion_result["result"] = conversion_result.get("result", conversion_result.get("Model", ""))
    has_yaml = len(conversion_result["result"].strip()) > 20
    data = conversion_result
    if has_yaml:
        data = build_yaml_object(conversion_result, project_json)
        del data["activities"]
        legacy_plan = data["plan"]
        data["plan"] = multiline_str(PlanBuilder().build(Workflow("", "", data["process"])))
        if data["retrieved_triggers"][0] == "":
            data["retrieved_triggers"] = ["UiPath.Core.Activities.ManualTrigger"]
        if data["name"] not in ("RETRYSCOPE", "SWITCH", "[Jarvis] Update Jira ticket status"):
            # assert legacy_plan == data["plan"], f"Legacy plan:\n{legacy_plan}\n\nPlan:\n{data['plan']}"
            if legacy_plan != data["plan"]:
                print("WARNING! Different legacy plan")
                print(f"Legacy plan:\n{legacy_plan}\n\nPlan:\n{data['plan']}\n")
                print(get_diff(legacy_plan, data["plan"]))
    if has_yaml and len(str(data)) >= 10000:
        split = ".errors_too_long"
    elif has_yaml and "[Jarvis]" in converted_json_path.as_posix() and "fixes_20240411" in converted_json_path.as_posix():
        split = ".errors_jarvis"
    elif has_yaml and not has_old_business_entity(data) and data.get("process", {}).get("workflow") is not None:
        split = split  # use intended split
    elif has_yaml and has_old_business_entity(data) and data.get("process", {}).get("workflow") is not None:
        split = ".errors_old_business_entity"
    else:
        split = ".errors"
    if has_yaml:
        data["typedefs"] = typedefs
    split_path = dataset_output_dir_path / split
    example_path = Path(str(split_path / workflow_name) + ".yaml")
    split_path.mkdir(parents=True, exist_ok=True)
    existing = example_path.exists()
    data = {"target_framework": project_json["targetFramework"], **data}

    if data.get("Success", True):
        wf = Workflow(data["description"], data["plan"], data["process"])
        plan_with_categories = PlanBuilderWithCategories(data["target_framework"]).build(wf)
        data["plan_categories"] = multiline_str(plan_with_categories)

    yaml_dump(data, example_path)
    tqdm.tqdm.write(f"{example_path.stem}.\n\tExisting: {existing}\n\tSplit: {split}")
    return example_path


def create_dataset(dataset_converted_dir_path: Path, dataset_output_dir_path: Path):
    existing = {}
    for split_path in dataset_output_dir_path.iterdir():
        if not split_path.is_dir():
            continue
        existing[split_path.stem] = set(split_path.glob("*.yaml"))
    testsets = {name: set(testset) for name, testset in load_subsets_file(dataset_output_dir_path / "subsets.yaml").items()}
    created = collections.defaultdict(set)
    for converted_json_path in tqdm.tqdm(sorted(dataset_converted_dir_path.glob("**/*.json")), dynamic_ncols=True):
        if converted_json_path.name == "project.json":
            continue
        if converted_json_path.name.endswith(".typedef.json"):
            continue
        project_folder = converted_json_path.relative_to(dataset_converted_dir_path).parts[0]
        project_json_path = dataset_converted_dir_path / project_folder / "project.json"
        if not project_json_path.exists():
            print(f"Can't find project.json for {converted_json_path} at {project_json_path}")
            continue
        typedef_path = converted_json_path.parent / f"{converted_json_path.stem}.typedef.json"
        workflow_name = get_workflow_name(converted_json_path, relative_to=dataset_converted_dir_path)
        split = get_sample_split(workflow_name, testsets)
        # converted_dir_path = dataset_converted_dir_path / project_folder
        output_path = create_dataset_example(converted_json_path, typedef_path, project_json_path, dataset_output_dir_path, split, workflow_name)
        if output_path is None:
            continue
        split = output_path.parent.stem
        created[split].add(output_path)
    for split in sorted(set().union(existing.keys(), created.keys())):
        created_split = created.get(split, set())
        existing_split = existing.get(split, set())
        new_split = created_split.difference(existing_split)
        updated_split = created_split.intersection(existing_split)
        deleted_split = set()
        if split != "uia" and not split.startswith(("__", "prod")):
            deleted_split = existing_split.difference(created_split)
        print(f"Split {split}:")
        print(f"\tExisting: {len(existing_split)}")
        print(f"\tNew: {len(new_split)}")
        print(f"\tUpdated: {len(updated_split)}")
        print(f"\tDeleted: {len(deleted_split)}")
        if deleted_split and split != "static":
            msg = [
                "Existing workflow generation examples to be deleted:",
                "\n".join(p.stem for p in sorted(deleted_split)),
                "Confirm? (y/n):",
            ]
            confirm = input("\n".join(msg))
            if confirm.lower() == "y":
                for p in deleted_split:
                    p.unlink()


async def create_workflow_generation_dataset(target_framework: TargetFramework, inhibit_generation: bool = False) -> None:
    dataset_converted_dir_path = paths.get_converted_dataset_path(target_framework)
    dataset_workflow_generation_dir_path = paths.get_workflow_generation_dataset_path(target_framework)
    create_dataset(dataset_converted_dir_path, dataset_workflow_generation_dir_path)
    old_used_path = dataset_workflow_generation_dir_path / "__old_used"
    train_path = dataset_workflow_generation_dir_path / "train"
    if old_used_path.exists():
        for file_path in old_used_path.iterdir():
            if file_path.is_file():
                shutil.copy(file_path, train_path / file_path.name)
    if inhibit_generation:
        print("Finished creating dataset. Migrating to metadata now.")
        await generate_query_and_plan_into_metadata(target_framework)
        await generate_summarization_into_metadata(target_framework)
    # TODO: We are not yet applying the Workflow abstraction overt the workflow generation yamls
    # TODO: We could also automatically apply a metadata plan as default if we want to


async def create_sequence_generation_dataset(request_context: RequestContext, target_framework: TargetFramework) -> None:
    """To be created after the workflow generation dataset. It will build sequences based on it."""
    typedefs.load()  # ensure typedefs are loaded; needed for activity typedef lookup for sequence creation
    create_and_reconcile_metadata(target_framework)
    cut_workflow_from_metadata(target_framework)
    await generate_queries_for_sequence_generation_dataset(request_context, target_framework)

    metadata_path = paths.get_sequence_generation_metadata_path(target_framework)
    metadata = load_metadata_file(metadata_path)
    convert_to_multiline_str(metadata, "*.plan")
    convert_to_multiline_str(metadata, "*.cuts[].subplan")
    metadata_ordered = sorted(metadata.values(), key=lambda item: item["id"])
    yaml_dump(metadata_ordered, metadata_path)


def _sqgen_generate_cutting_points(datapoint_workflow: Workflow) -> list[tuple[int, int]]:
    cutting_points = WorkflowSequenceExtractor().generate_cutting_points(datapoint_workflow)
    sample_of_cutting_points = [0] + random.sample(range(1, len(cutting_points)), min(5, len(cutting_points) - 1))  # using range to maintain order
    sample_of_cutting_points = [cutting_points[i] for i in sample_of_cutting_points]
    return sample_of_cutting_points


def create_and_reconcile_metadata(target_framework: TargetFramework) -> None:
    workflow_generation_root_path = paths.get_workflow_generation_dataset_path()
    sequence_generation_root_path = paths.get_sequence_generation_dataset_path()

    metadata_path = paths.get_sequence_generation_metadata_path(target_framework)
    metadata = load_metadata_file(metadata_path)
    print(f"Found {len(metadata)} entries in metadata for {target_framework}.")

    stats = collections.defaultdict(int)
    new_metadata = {}
    workflow_generation_dataset = load_workflow_generation_dataset(
        target_framework,
        ignored_subsets={
            "prod",
            "__old_not_used",
            # "__old_used"
        },
    )
    for _subset_name, subset_data in workflow_generation_dataset.items():
        for path, datapoint in subset_data.items():
            # strip suffix
            item_id = path.relative_to(workflow_generation_root_path).with_suffix("")
            datapoint_workflow = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])

            should_generate_cuts = False
            metadata_item = {}
            if item_id in metadata:
                item_dirpath = sequence_generation_root_path / item_id
                metadata_item = metadata[item_id]
                for i, cut in enumerate(metadata_item["cuts"]):
                    start, end = cut["start"], cut["end"]
                    cut_item_path = item_dirpath / f"{i:02d}__{start}_{end}_{item_dirpath.name}.yaml"
                    if not cut_item_path.exists():
                        print(f"Warning! Missing cut item for id {item_id} at {path}")
                        should_generate_cuts = False  # assume cut is valid if it is in metadata, but not found on disk
                        break
                    sequece_data = yaml_load(cut_item_path)
                    sequence_data_workflow = Workflow(sequece_data["description"], sequece_data["plan"], sequece_data["process"])
                    sequence_data_plan = PlanBuilder().build(sequence_data_workflow)
                    datapoint_plan = PlanBuilder().build(datapoint_workflow)

                    # if datapoint_workflow != sequence_data_workflow:  # regenerate cuts on any change in the workflow
                    if datapoint_plan != sequence_data_plan:  # regenerate cuts only on plan mismatch
                        print(f"Warning! Found modified process for id {item_id} at {path}")
                        should_generate_cuts = True
                        break

                        # import difflib
                        # diff = difflib.ndiff(datapoint_workflow.lmyaml().splitlines(), sequence_data_workflow.lmyaml().splitlines())
                        # print("\n".join(diff))
            else:
                stats["new"] += 1
                should_generate_cuts = True

            if should_generate_cuts:
                stats["generated"] += 1

                new_metadata[item_id] = {
                    "id": item_id.as_posix(),
                    "query": datapoint["description"],
                    "plan": multiline_str(datapoint["plan"]),
                    "cuts": [
                        {
                            "query": "",  # will be added by generate_queries_for_sequence_generation_dataset
                            "subplan": "",  # will be added by cut_workflow_from_metadata
                            "start": start,
                            "end": end,
                        }
                        for start, end in _sqgen_generate_cutting_points(datapoint_workflow)
                    ],
                }
            else:
                stats["existing"] += 1
                new_metadata[item_id] = metadata_item

    missing_item_ids = set(metadata.keys()) - set(new_metadata.keys())
    for item_id in list(missing_item_ids):
        _, subset_name, *_ = item_id.parts
        if subset_name == "testcase":  # preserve testcase even if it does not exist in wfgen
            new_metadata[item_id] = metadata[item_id]
            missing_item_ids.remove(item_id)
    if missing_item_ids:
        stats["missing"] += len(missing_item_ids)
        print("The following will be removed from sequence generation:")
        for item_id in sorted(missing_item_ids):
            print(f"\t{item_id}")
    print(f"Finished processing. Stats: {stats}")

    print(f"Dumping new metadata with {len(new_metadata)} entries.")
    metadata_ordered = sorted(new_metadata.values(), key=lambda item: item["id"])
    yaml_dump(metadata_ordered, metadata_path)


def _seqgen_get_datapoint(datapoint: dict, start_cut: int, end_cut: int) -> dict:
    """Extracts a sequence datapoint from a workflow datapoint."""
    # datapoint = copy.deepcopy(datapoint)  # disabling this safety measure due to performance reasons
    workflow = Workflow(datapoint["description"], datapoint["plan"], datapoint["process"])

    activity_typedefs, additional_typedefs = parse_workflow_conversion_typedefs(datapoint.get("typedefs", {}))
    activity_typedefs, additional_typedefs = FillMissingWorkflowActivityTypedefs(
        workflow, activity_typedefs, additional_typedefs, force_activities=[constants.SEQUENCE_ACTIVITY_NAME], strict_mode=False
    ).fill()
    wf_remaining, wf_sequence, start_cut, end_cut = WorkflowSequenceExtractor().create_sequence(
        workflow, (start_cut, end_cut), activity_typedefs, additional_typedefs
    )
    plan_remaining = PlanBuilder().build(wf_remaining)
    plan_sequence = SequencePlanBuilder().build(wf_sequence)
    plan_sequence_with_categories = PlanBuilderWithCategories(datapoint["target_framework"], False).build(wf_sequence)

    # dump workflow and sequence
    ordered_datapoint = {}
    for key, value in datapoint.items():
        if key == "plan":
            ordered_datapoint["plan_existing"] = multiline_str(plan_remaining)
            ordered_datapoint["plan_sequence"] = multiline_str(plan_sequence)
            ordered_datapoint["plan_sequence_with_categories"] = multiline_str(plan_sequence_with_categories)
            ordered_datapoint["plan"] = multiline_str(value)
            continue
        if key == "process":
            ordered_datapoint["process_existing"] = wf_remaining.to_dict()
            ordered_datapoint["process_sequence"] = [activity.to_dict() for activity in wf_sequence.activities]
            ordered_datapoint["process"] = workflow.to_dict()
            continue
        ordered_datapoint[key] = value
    return ordered_datapoint


def cut_workflow_from_metadata(target_framework: TargetFramework) -> None:
    workflow_generation_root_path = paths.get_workflow_generation_dataset_path()
    sequence_generation_root_path = paths.get_sequence_generation_dataset_path()

    sequence_generation_framework_path = paths.get_sequence_generation_dataset_path(target_framework)
    for subset in sequence_generation_framework_path.iterdir():
        if subset.name == "prod":
            continue  # TODO: remove this after prod gets integrated
        if subset.name in {
            "testcase",
            # "static",
        }:
            continue  # these are manually added and is not reflected in the workflow generation dataset
        if subset.is_dir():
            print("Removing seqgen:", subset)
            shutil.rmtree(subset)

    metadata_path = paths.get_sequence_generation_metadata_path(target_framework)
    metadata = load_metadata_file(metadata_path)

    stats = collections.defaultdict(int)
    for item_id, item in tqdm.tqdm(list(metadata.items()), "Cutting workflows"):
        _, subset, *_ = item_id.parts
        if subset == "testcase":
            continue  # manually added
        item_source_path = workflow_generation_root_path / item_id.with_name(f"{item_id.name}.yaml")
        if not item_source_path.exists():
            print(f"Missing wfgen example for {item_source_path}")
            del metadata[item_id]
            stats["removed"] += 1
            continue
        item_destination_dirpath = sequence_generation_root_path / item_id
        item_destination_dirpath.mkdir(parents=True, exist_ok=False)

        item_datapoint = yaml_load(item_source_path) or {}
        for i, item_cut in enumerate(item["cuts"]):
            start_cut, end_cut = item_cut["start"], item_cut["end"]
            sequence_datapoint = _seqgen_get_datapoint(item_datapoint, start_cut, end_cut)

            dump_path = item_destination_dirpath / f"{i:02}__{start_cut}_{end_cut}_{item_id.name}.yaml"
            yaml_dump(sequence_datapoint, dump_path)

            # update metadata object - ensure order
            new_cut = {}
            new_cut["query"] = item_cut["query"]
            new_cut["subplan"] = sequence_datapoint["plan_sequence"]
            for k, v in item_cut.items():
                if k not in {"query", "subplan"}:
                    new_cut[k] = v
            item["cuts"][i] = new_cut

    print(f"Removed {stats['removed']} entries.")
    metadata_ordered = sorted(metadata.values(), key=lambda item: item["id"])
    yaml_dump(metadata_ordered, metadata_path)


async def generate_queries_for_sequence_generation_dataset(request_context: RequestContext, target_framework: TargetFramework) -> None:
    sequence_generation_dataset_root_path = paths.get_sequence_generation_dataset_path()
    metadata_path = paths.get_sequence_generation_metadata_path(target_framework)
    metadata_wip_path = metadata_path.with_stem("metadata.wip")
    metadata = load_metadata_file(metadata_path)
    metadata_wip = {}
    if metadata_wip_path.exists():
        metadata_wip = load_metadata_file(metadata_wip_path)
    async with aiofiles.open(metadata_wip_path, "a+") as metadata_wip_file:
        for item_id, item in tqdm.tqdm(metadata.items(), "Verify and generate queries"):
            if item_id in metadata_wip:
                continue
            item_dirpath = sequence_generation_dataset_root_path / item_id
            for i, cut in enumerate(item["cuts"]):
                query, start, end = cut["query"], cut["start"], cut["end"]
                cut_item_path = item_dirpath / f"{i:02d}__{start}_{end}_{item_dirpath.name}.yaml"
                data = yaml_load(cut_item_path)

                if item_id in metadata_wip:
                    query = metadata_wip[item_id]["query"]
                if not query:
                    print("prompting for query")
                    lmyaml: WorkflowDict = {"processName": "", "packages": data["process"]["packages"], "workflow": data["process_sequence"]}
                    workflow = Workflow("", data["plan_sequence"], lmyaml)
                    try:
                        query = await generate_from_production_data.prompt_llm_for_query(request_context, workflow)
                    except ValueError as e:
                        if "content filter" in str(e):
                            print(f"WARNING: {item_id} raised {e}")
                            query = ""
                        else:
                            raise e
                    print("prompting for query done")
                cut["query"] = query

                # insert query in the right place
                data_new = {}
                for key in ("target_framework", "name"):
                    if key in data:
                        data_new[key] = data.pop(key)
                data_new["description_sequence"] = query
                for key in tuple(data.keys()):
                    data_new[key] = data.pop(key)
                for plan_key in ("plan_existing", "plan_sequence", "plan", "plan_categories", "plan_sequence_with_categories"):
                    # categories keys might not be present in manually created examples - static + testcase
                    if data_new.get(plan_key) is None:
                        continue
                    if not isinstance(data_new[plan_key], multiline_str):
                        data_new[plan_key] = multiline_str(data_new[plan_key])
                await metadata_wip_file.write(yaml_dump(data_new, None))
            item["plan"] = multiline_str(item["plan"])
            for cut in item["cuts"]:
                cut["subplan"] = multiline_str(cut["subplan"])
            await metadata_wip_file.write(yaml_dump([item], None))
    shutil.move(metadata_wip_path, metadata_path)


async def create_workflow_generation_datasets(inhibit_generation: bool = False) -> None:
    for target_framework in ("Portable", "Windows"):
        await create_workflow_generation_dataset(target_framework, inhibit_generation)


async def create_sequence_generation_datasets(request_context: RequestContext) -> None:
    for target_framework in ("Portable", "Windows"):
        await create_sequence_generation_dataset(request_context, target_framework)


async def generate_queries_for_sequence_generation_datasets(request_context: RequestContext) -> None:
    for target_framework in ("Portable", "Windows"):
        await generate_queries_for_sequence_generation_dataset(request_context, target_framework)
