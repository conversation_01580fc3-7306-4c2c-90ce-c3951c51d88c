import asyncio
import copy
import os
import pathlib

import langchain.chains
import langchain.chains.llm
import langchain.embeddings
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
from langchain_core.prompts.chat import MessageLike

from experimental.autopilot_dataset.generators.api_workflow.query_extension import QueryExtension
from experimental.autopilot_dataset.generators.api_workflow.raw_edit_datapoint import APIWfEditDatapoint
from experimental.autopilot_dataset.helpers.datapoint_paths_utils import get_corresponding_metadata_file_path
from experimental.common.api_wfs.workflow_parser import get_activities_plan, get_workflow_plan
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.dynamic_activities import get_dynamic_activity_schemas
from services.studio._text_to_workflow.common.api_workflow.schema import (
    BASE_API_ACTIVITIES,
    ApiWorkflow,
    ApiWorkflowDataPoint,
    ConnectorIntegration,
    ForEach,
    If,
)
from services.studio._text_to_workflow.common.api_workflow.walkers import ApiActivityIdCollector, ApiWorkflowExpressionRemover
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import (
    ApiWorkflowParser,
    get_activity_types_from_do_block,
    get_workflow_activity_types,
    list_activities,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.json_utils import json_dump, json_load_from_path
from services.studio._text_to_workflow.utils.paths import (
    API_WF_DATASET_NAME,
    API_WF_EMPTY_SCOPES_WF_SUFFIX,
    API_WF_REPLACE_ACTIVITIES_SAMPLE_SUFFIX,
    API_WF_SINGLE_ACTIVITY_WF_SUFFIX,
    get_api_wf_edit_mappings_file_path,
    get_api_workflow_dataset_root_path,
    get_api_workflow_edit_dataset_root_path,
    get_downloads_dataset_path,
)
from services.studio._text_to_workflow.utils.request_utils import set_request_context
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

# Paths to ignore - these are workflows that should not be used to generate samples
IGNORE_PATHS = [
    "BrokenWFs",  # workflows that are not valid yet or have errors
    "AdditionalWorkflows",  # only used as a solution workflow for certain edit samples, not to be processed directly
]

# Paths to ignore for edit samples - these workflows should not be used for edit samples
IGNORE_EDIT_PATHS = [
    "APIWFs/Static",  # no need to add to edit samples as they only exist to be used statically in the prompts
]


class ApiWorkflowDatasetGenerator:
    def __init__(self, activities_retriever: APIActivitiesRetriever, api_workflow_parser: ApiWorkflowParser):
        self.activities_retriever = activities_retriever
        self.api_workflow_parser = api_workflow_parser

        config_path = pathlib.Path(__file__).parent / "api_wf_query_extender_config.yaml"
        self.prompt_config = yaml_load(config_path)["prompt"]
        self.model = ModelManager().get_llm_model("api_workflow_query_extension_model", ConsumingFeatureType.WORKFLOW_GENERATION)

    async def parse_api_wfs_samples(
        self,
        input_root_dir: pathlib.Path,
        workflow_generate_samples_dir: pathlib.Path,
        workflow_edit_samples_dir: pathlib.Path,
        augment: bool = False,  # augument only regenerates the properties that are not llm generated
    ) -> None:
        """Process all API.json files in the given directory and its subdirectories."""
        api_wf_files = [file_path for file_path in input_root_dir.rglob("API.json")]

        for file_path in api_wf_files:
            try:
                if any(ignore_path in str(file_path) for ignore_path in IGNORE_PATHS):
                    # skip because it is in the ignore list
                    continue

                # Load the API.json file
                api_wf = json_load_from_path(file_path)

                # Load the corresponding project.uiproj file from the same directory
                project_path = file_path.parent / "project.uiproj"
                project_json = json_load_from_path(project_path)

                # create ApiWorkflow
                api_workflow, metadata, _ = self.api_workflow_parser.parse_api_workflow(api_wf)

                activities = list_activities(api_workflow)
                dynamic_activities = [activity for activity in activities if isinstance(activity, ConnectorIntegration)]
                schemas = await get_dynamic_activity_schemas(dynamic_activities, metadata, self.activities_retriever)

                if len(metadata) != len(activities):
                    metadata = None
                if len(dynamic_activities) != len(schemas):
                    schemas = None

                language = "js" if api_wf.get("evaluate", {}).get("language", "") == "javascript" else "jq"
                serverless_workflow = ApiWorkflowDataPoint(
                    mode="workflow",
                    query=project_json["Description"] or "",
                    language=language,
                    plan="\n".join(get_workflow_plan(api_workflow.root, ignore_root_thought=True)),
                    used_activities=sorted(get_workflow_activity_types(api_workflow.root)),
                    existing_workflow=None,
                    solution_workflow=api_workflow,
                    solution_workflow_schemas=schemas,
                )

                # Get path information
                parent_path, last_folder_name = self._get_output_path_info(file_path, input_root_dir)

                # Create the output file path
                processed_samples_path = workflow_generate_samples_dir / parent_path / f"{last_folder_name}.json"
                processed_samples_path.parent.mkdir(exist_ok=True)

                # Save the converted workflow
                json_dump(serverless_workflow.model_dump(exclude_none=True, by_alias=True), pathlib.Path(processed_samples_path))

                # Save metadata to a separate file - since this can be large, we don't want to include it in the main datapoint file
                metadata_path = get_corresponding_metadata_file_path(processed_samples_path)
                os.makedirs(metadata_path.parent, exist_ok=True)
                json_dump(metadata, metadata_path)

                if any(ignore_path in str(file_path) for ignore_path in IGNORE_EDIT_PATHS):
                    # should not generate edit samples for this sample
                    continue

                # Create the output folder for edit use cases
                edit_folder_path = workflow_edit_samples_dir / parent_path
                os.makedirs(edit_folder_path, exist_ok=True)

                # Create simplified version with just the first activity - used for edit use cases
                await self._build_first_activity_only_datapoint(serverless_workflow, last_folder_name, edit_folder_path, augment)

                # Create datapoint with empty scopes by removing the then/do blocks from if/foreach activities - used for edit use cases
                await self._build_datapoints_with_empty_scopes(serverless_workflow, last_folder_name, edit_folder_path, augment)

                print(f"✅ Processed {file_path}")
            except Exception as e:
                print(f"❌ Error processing {file_path}: {e}")

    async def _build_first_activity_only_datapoint(
        self, original_datapoint: ApiWorkflowDataPoint, sample_name: str, output_path: pathlib.Path, augment: bool = False
    ):
        """
        Creates a simplified version of the workflow with only the first activity.
        This will be used to test edit use cases.
        """
        # cannot be done if the workflow has only 1 activity
        if len(original_datapoint.solution_workflow.root.do) == 1:
            return

        output_path = output_path / f"{sample_name}_{API_WF_SINGLE_ACTIVITY_WF_SUFFIX}.json"

        # Clone the original workflow to avoid modifying it
        solution_workflow = original_datapoint.solution_workflow
        existing_workflow: ApiWorkflow = copy.deepcopy(solution_workflow)
        existing_workflow.root.do = existing_workflow.root.do[:1]  # only keep the first activity

        # Get new metadata and other information for the excluded activities
        plan = "\n".join(get_activities_plan(solution_workflow.root.do[1:]))
        used_activities = sorted(get_activity_types_from_do_block(solution_workflow.root.do[1:]))

        await self._build_edit_api_wf_datapoint(
            original_datapoint=original_datapoint,
            output_path=output_path,
            existing_workflow=existing_workflow,
            plan=plan,
            used_activities=used_activities,
            focused_activity_id=existing_workflow.root.do[0].id,
            contains_no_replacements=True,
            augment=augment,
        )

    async def _build_datapoints_with_empty_scopes(
        self, original_datapoint: ApiWorkflowDataPoint, sample_name: str, output_path: pathlib.Path, augment: bool = False
    ):
        """
        Loop the existing workflow and obtain a new datapoint by setting all if/for each scopes to empty blocks.
        """
        if_index = foreach_index = 0
        for index, activity in enumerate(original_datapoint.solution_workflow.root.do):
            if isinstance(activity, If):
                if_index += 1
                output_path = output_path / f"{sample_name}_{API_WF_EMPTY_SCOPES_WF_SUFFIX}_if_{if_index}_then.json"
                existing_workflow: ApiWorkflow = copy.deepcopy(original_datapoint.solution_workflow)
                existing_if = existing_workflow.root.do[index]
                assert isinstance(existing_if, If)
                existing_if.then = []
                existing_if.thought = "If"

                # Remove references to then activities and clear them
                self._remove_activity_references_from_workflow_expressions(activity.then, existing_workflow)

                plan = "\n".join(get_activities_plan(activity.then))
                used_activities = sorted(get_activity_types_from_do_block(activity.then))

                await self._build_edit_api_wf_datapoint(
                    original_datapoint=original_datapoint,
                    output_path=output_path,
                    existing_workflow=existing_workflow,
                    plan=plan,
                    used_activities=used_activities,
                    focused_activity_id=existing_if.id,
                    contains_no_replacements=True,
                    augment=augment,
                )
                # TBD: do we want to do the same for the else block? usually they are much simpler than the then block -so it might not be needed
                continue
            if isinstance(activity, ForEach):
                foreach_index += 1
                output_path = output_path / f"{sample_name}_{API_WF_EMPTY_SCOPES_WF_SUFFIX}_foreach_{foreach_index}.json"
                existing_workflow: ApiWorkflow = copy.deepcopy(original_datapoint.solution_workflow)
                existing_for_each = existing_workflow.root.do[index]
                assert isinstance(existing_for_each, ForEach)
                existing_for_each.do = []
                existing_for_each.thought = "For each"

                # Remove references to the "do" block and clear it
                self._remove_activity_references_from_workflow_expressions(activity.do, existing_workflow)

                plan = "\n".join(get_activities_plan(activity.do))
                used_activities = sorted(get_activity_types_from_do_block(activity.do))

                await self._build_edit_api_wf_datapoint(
                    original_datapoint=original_datapoint,
                    output_path=output_path,
                    existing_workflow=existing_workflow,
                    plan=plan,
                    used_activities=used_activities,
                    focused_activity_id=existing_for_each.id,
                    contains_no_replacements=True,
                    augment=augment,
                )

    async def _build_edit_api_wf_datapoint(
        self,
        original_datapoint: ApiWorkflowDataPoint,
        output_path: pathlib.Path,
        existing_workflow: ApiWorkflow,
        plan: str,
        used_activities: list,
        focused_activity_id: str | None,
        contains_no_replacements: bool,
        augment: bool,
    ):
        """
        Creates an API workflow datapoint for edit use cases and saves it to the given path.
        """
        if augment and os.path.exists(output_path):
            # re-use the existing query if augment is true, do not generate a new one
            original_edit_datapoint = ApiWorkflowDataPoint.model_validate(json_load_from_path(output_path))
            query = original_edit_datapoint.query
        else:
            # generate a new query using LLM
            query = await self._generate_new_query(original_datapoint.query, plan, existing_workflow, original_datapoint.solution_workflow)

        api_wf_edit_datapoint = ApiWorkflowDataPoint(
            mode="edit",
            query=query,
            solution_workflow_query=original_datapoint.query,
            language=original_datapoint.language,
            plan=plan,
            used_activities=used_activities,
            existing_workflow=existing_workflow,
            solution_workflow=original_datapoint.solution_workflow,
            solution_workflow_schemas=original_datapoint.solution_workflow_schemas,
            focused_activity_id=focused_activity_id,
            contains_no_replacements=contains_no_replacements,
        )

        json_dump(api_wf_edit_datapoint.model_dump(exclude_none=True, by_alias=True), output_path)
        print(f"✅ Successfully generated edit API WF datapoint: {output_path}")

    async def _generate_new_query(self, solution_description: str, new_steps_plan: str, existing_workflow: ApiWorkflow, solution_workflow: ApiWorkflow) -> str:
        messages: list[MessageLike] = [
            langchain.prompts.SystemMessagePromptTemplate.from_template(self.prompt_config["system_msg"]),
            langchain.prompts.HumanMessagePromptTemplate.from_template(self.prompt_config["user_msg_template"]),
        ]
        prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(messages)

        structured_llm = self.model.with_structured_output(schema=QueryExtension)
        chat_chain = prompt_template | structured_llm

        inputs = {
            "target_workflow_description": solution_description,
            "initial_workflow": existing_workflow.model_dump(exclude_none=True, by_alias=True),
            "target_workflow": solution_workflow.model_dump(exclude_none=True, by_alias=True),
            "steps_to_be_added": new_steps_plan,
        }

        # Retry two times
        for retry_attempt in range(2):
            try:
                with langchain_community.callbacks.get_openai_callback():
                    result = await chat_chain.ainvoke(inputs)
                    assert isinstance(result, QueryExtension)
                    return result.new_query
            except Exception as e:
                if retry_attempt == 1:  # This is the last retry
                    print(f"❌ Error on final retry when regenerating:{solution_description}. Error: {e}")
                continue

        raise Exception("Failed to generate thinking from query")

    async def parse_api_wf_edit_samples(
        self,
        datapoints: list[APIWfEditDatapoint],
        input_root_dir: pathlib.Path,
        workflow_edit_samples_dir: pathlib.Path,
    ) -> None:
        """Combines an initial existing workflow with a solution workflow and a query to create a new edit workflow datapoint."""
        for datapoint in datapoints:
            try:
                # Load the API.json file for the existing workflow
                existing_api_wf_path = pathlib.Path(input_root_dir / datapoint.original / "API.json")
                existing_api_wf = json_load_from_path(existing_api_wf_path)
                existing_api_workflow, _, _ = self.api_workflow_parser.parse_api_workflow(existing_api_wf)

                # Load the API.json file for the solution workflow
                solution_api_wf = json_load_from_path(pathlib.Path(input_root_dir / datapoint.solution / "API.json"))
                solution_api_workflow, _, _ = self.api_workflow_parser.parse_api_workflow(solution_api_wf)

                language = "js" if solution_api_wf.get("evaluate", {}).get("language", "") == "javascript" else "jq"

                used_activities = sorted(get_workflow_activity_types(solution_api_workflow.root))

                edit_datapoint = ApiWorkflowDataPoint(
                    mode="edit",
                    query=datapoint.query,
                    language=language,
                    plan="\n".join(get_workflow_plan(solution_api_workflow.root, ignore_root_thought=True)),
                    used_activities=used_activities,
                    existing_workflow=existing_api_workflow,
                    solution_workflow=solution_api_workflow,
                    solution_workflow_schemas=None,
                )

                # Get path information
                parent_path, last_folder_name = self._get_output_path_info(existing_api_wf_path, input_root_dir)

                # Create the output file path
                edit_folder = workflow_edit_samples_dir / parent_path
                edit_datapoint_path = edit_folder / f"{last_folder_name}_{API_WF_REPLACE_ACTIVITIES_SAMPLE_SUFFIX}.json"
                os.makedirs(edit_folder, exist_ok=True)

                # Save the converted workflow
                json_dump(edit_datapoint.model_dump(exclude_none=True, by_alias=True), edit_datapoint_path)

                print(f"✅ Processed Edit API WF datapoint: {datapoint.original} - {datapoint.solution}")
            except Exception as e:
                print(f"❌ Error processing Edit API WF datapoint: {datapoint.original} - {datapoint.solution}: {e}")

    @staticmethod
    def _remove_activity_references_from_workflow_expressions(references_to_remove: list[BASE_API_ACTIVITIES], workflow: ApiWorkflow):
        """Helper method to extract activity IDs, remove their references from the workflow, and clear the list."""
        id_collector = ApiActivityIdCollector()
        activity_ids_to_remove = id_collector.collect_from_activity_list(references_to_remove)

        # Remove any expressions that reference the removed activity IDs
        ApiWorkflowExpressionRemover(activity_ids_to_remove).process_workflow(workflow)

    @staticmethod
    def _get_output_path_info(api_file_path: pathlib.Path, input_root_dir: pathlib.Path):
        """
        Extract path information from an API file path.
        """
        relative_path = api_file_path.relative_to(input_root_dir)

        # Extract parent path and last folder name
        parent_path = relative_path.parent.parent
        last_folder_name = relative_path.parent.name

        return parent_path, last_folder_name


async def main():
    # Set request context for DAP cli.
    set_request_context(get_testing_request_context("en", os.environ["UIPATH_TENANT_ID"], "Generate API WFs Dataset", os.environ["UIPATH_TOKEN"]))

    # Define the root directory to search for API.json files
    input_root_dir = get_downloads_dataset_path() / API_WF_DATASET_NAME

    # Define the output directory for the converted wf gen samples
    workflow_generate_samples_dir = get_api_workflow_dataset_root_path()

    # Define the output directory for the converted edit samples
    # For now, we plan to only use these for Eval purposes
    workflow_edit_samples_dir = get_api_workflow_edit_dataset_root_path()

    # Create the generator with an initialized activities retriever
    activities_retriever = APIActivitiesRetriever()
    api_workflow_parser = ApiWorkflowParser(activities_retriever)
    generator = ApiWorkflowDatasetGenerator(activities_retriever, api_workflow_parser)

    # Process all API.json files
    await generator.parse_api_wfs_samples(input_root_dir, workflow_generate_samples_dir, workflow_edit_samples_dir, augment=True)

    # Process all edit API WF datapoints
    edit_mappings_file_path = get_api_wf_edit_mappings_file_path()

    # Load the edit mappings file and deserialize it as a list of APIWfEditDatapoint objects
    edit_mappings = yaml_load(edit_mappings_file_path)
    edit_datapoints = [APIWfEditDatapoint(**mapping) for mapping in edit_mappings]
    await generator.parse_api_wf_edit_samples(edit_datapoints, input_root_dir, workflow_edit_samples_dir)


if __name__ == "__main__":
    asyncio.run(main())
