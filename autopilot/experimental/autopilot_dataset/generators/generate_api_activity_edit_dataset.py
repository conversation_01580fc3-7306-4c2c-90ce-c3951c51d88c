import json
import pathlib
import typing as t

import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as api_wf_edit_schema
import services.studio._text_to_workflow.common.api_workflow.schema as api_wf_schema
from experimental.autopilot_dataset.helpers.datapoint_paths_utils import get_corresponding_metadata_file_path
from services.studio._text_to_workflow.common.api_workflow import workflow_parser
from services.studio._text_to_workflow.utils import json_utils, paths

_api_wf_dataset_dir = paths.get_api_workflow_retriever_dataset_path()
_activity_edit_dataset_dir = paths.get_autopilot_samples_dataset_path() / "APIActivityEdit"


def generate_api_activity_edit_dataset() -> None:
    for name, datapoint, api_workflow_path in generate_api_activity_edit_datapoints():
        api_workflow_relative_path = api_workflow_path.relative_to(_api_wf_dataset_dir).with_suffix("")
        datapoint_path = _activity_edit_dataset_dir / api_workflow_relative_path / name
        datapoint_path.parent.mkdir(parents=True, exist_ok=True)
        json_utils.json_dump(datapoint.model_dump(), datapoint_path)


def generate_api_activity_edit_datapoints() -> t.Generator[tuple[str, api_wf_edit_schema.ApiActivityEditDataPoint, pathlib.Path], None, None]:
    for api_workflow_data, api_workflow_path in iterate_api_wf_datapoints():
        try:
            api_workflow_datapoint = api_wf_schema.ApiWorkflowDataPoint.model_validate(api_workflow_data)
            language = api_workflow_datapoint.language
            workflow = api_wf_schema.ApiWorkflowForActivityEdit(
                input=json.loads(api_workflow_datapoint.solution_workflow.input),
                root=api_workflow_datapoint.solution_workflow.root,
            )

            # Get metadata from the corresponding metadata file, since this can be large, we don't want to include it in the main datapoint file
            metadata_path = get_corresponding_metadata_file_path(api_workflow_path)
            if not metadata_path.exists():
                print(f"⚠️ Skipped {api_workflow_path} because metadata file does not exist")
                continue

            metadata = json_utils.json_load_from_path(metadata_path)
            if metadata is None:
                print(f"⚠️ Skipped {api_workflow_path} because metadata is None")
                continue

            schemas = api_workflow_datapoint.solution_workflow_schemas
            if schemas is None:
                continue

            activities = workflow_parser.list_activities(workflow)
            for i, activity in enumerate(activities):
                if isinstance(activity, api_wf_schema.Sequence):
                    continue
                input_workflow = workflow.model_copy(deep=True)
                input_activity = workflow_parser.find_activity_by_id(input_workflow, activity.id)
                output_activity = input_activity.model_copy(deep=True)
                input_activity.model_remove_configuration()
                output_activity.model_remove_children()

                datapoint = api_wf_edit_schema.ApiActivityEditDataPoint(
                    input=api_wf_edit_schema.ApiActivityEditInput(
                        messages=[{"role": "user", "content": input_activity.thought}],
                        language=language,
                        workflow=input_workflow,
                        metadata=metadata,
                        schemas=schemas,
                        activity_id=input_activity.id,
                    ),
                    expected=api_wf_edit_schema.ApiActivityEditExpected(message="", activity=output_activity.model_dump()),
                )
                name = f"{i:03d}.{datapoint.expected.activity['activity'].split('.')[-1]}.{datapoint.expected.activity['id']}.json"
                print(f"✅ Processed {api_workflow_path}")
                yield name, datapoint, api_workflow_path
        except Exception as e:
            print(f"❌ Error processing {api_workflow_path}: {e}")


def iterate_api_wf_datapoints() -> t.Generator[tuple[dict, pathlib.Path], None, None]:
    for file_path in _api_wf_dataset_dir.rglob("*/*.json"):
        if file_path.parent.name == "BrokenWFs":
            continue
        data = json_utils.json_load_from_path(file_path)
        yield data, file_path


if __name__ == "__main__":
    generate_api_activity_edit_dataset()
