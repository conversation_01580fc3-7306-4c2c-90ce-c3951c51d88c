from pydantic import BaseModel


class APIWfEditDatapoint(BaseModel):
    """
    DTO representing a blueprint for a workflow edit sample.
    Contains original workflow, solution workflow, and query for the change.
    """

    original: str  # Path to the original workflow sample, before the edit takes place
    solution: str  # Path to the solution workflow sample, after the query changes are implemented
    query: str  # Description of the changes needed to transform the original workflow into the solution
