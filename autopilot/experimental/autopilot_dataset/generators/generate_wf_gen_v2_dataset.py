import asyncio
import os
import pathlib
import sys
import typing as t

import langchain.chains
import langchain.chains.llm
import langchain.embeddings
import langchain.prompts
import langchain.schema
import langchain_community.callbacks
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts.chat import MessageLike

import services.studio._text_to_workflow.utils.request_utils as request_utils
from experimental.autopilot_dataset.definitions.dataset import QueryPlanningResult, SequenceGenerationDataPoint, WorkflowGenerationDataPoint
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, WorkflowDict
from services.studio._text_to_workflow.common.tasks import generate_tasks_and_execute
from services.studio._text_to_workflow.common.walkers import Workflow
from services.studio._text_to_workflow.models.model_manager import Model<PERSON>anager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.paths import WF_GEN_V2_DATASET_NAME, get_workdir_path
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_helpers import get_workflow_activities
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WfGenDataPointV2

ActivitiesFetcher = ActivitiesRetriever()
ActivitiesFetcher.initialize()

QueryPlanningResultParser = PydanticOutputParser(pydantic_object=QueryPlanningResult)


def get_generation_path(generation_mode: ActivitiesGenerationMode) -> str:
    workdir = get_workdir_path()
    match generation_mode:
        case "sequence":
            return os.path.join(workdir, "Autopilot.Samples", "Dataset", "SequenceGeneration")
        case "workflow":
            return os.path.join(workdir, "Autopilot.Samples", "Dataset", "WorkflowGeneration")
        case _:
            raise ValueError(f"Invalid generation mode: {generation_mode}")


def ensure_dataset_directory(workdir: str) -> str:
    dataset_path = os.path.join(workdir, "..", WF_GEN_V2_DATASET_NAME)
    if not os.path.exists(dataset_path):
        os.makedirs(dataset_path)

    return dataset_path


def get_sample_paths(generation_mode: ActivitiesGenerationMode) -> dict[str, str]:
    dataset_root_path = get_generation_path(generation_mode)
    new_dataset_path: str = ensure_dataset_directory(dataset_root_path)
    source_to_destination_map: dict[str, str] = {}

    # Recursively read all .yaml files
    for source_file_path in sorted(pathlib.Path(dataset_root_path).rglob("*.yaml")):
        destination_file_path = os.path.join(new_dataset_path, source_file_path.relative_to(dataset_root_path))
        if ".error" in destination_file_path or "__old_not_used" in destination_file_path or source_file_path.name in ["metadata.yaml", "subsets.yaml"]:
            continue

        source_to_destination_map[source_file_path.as_posix()] = destination_file_path

    return source_to_destination_map


async def generate_retrieval_dataset(augment: bool = False) -> None:
    tenant_id, _ = get_connections_data()
    request_context = get_testing_request_context("en", tenant_id, "Workflow Dataset Generation")

    request_utils.set_request_context(request_context)

    config_path = pathlib.Path(__file__).parent / "query_extender_config.yaml"
    model_run_config = yaml_load(config_path)

    model = ModelManager().get_llm_model("activity_retrieval_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)

    async def task_generator() -> t.AsyncGenerator[asyncio.Task[None], None]:
        generation_modes: list[ActivitiesGenerationMode] = ["sequence", "workflow"]

        for generation_mode in generation_modes:
            for source_file_path, destination_file_path in get_sample_paths(generation_mode).items():
                yield asyncio.create_task(
                    generate_retrieval_sample_data(destination_file_path, source_file_path, model, model_run_config, generation_mode, augment)
                )

    await generate_tasks_and_execute(task_generator(), max_concurrent_tasks=8)


async def generate_retrieval_sample_data(
    destination_file_path: str,
    source_file_path: str,
    model: BaseChatModel,
    model_run_config: dict[str, t.Any],
    generation_mode: ActivitiesGenerationMode,
    augment: bool = False,  # augument only regenerates the properties that are not llm generated
):
    # we will omit the sequence demonstrations that start with "00_", as these will be duplicated by the workflow demonstrations
    if generation_mode == "sequence" and os.path.basename(source_file_path).startswith("00_"):
        return

    # we only need the existing workflow for test samples, train demonstrations do not need it
    is_test_sample = "/test/" in os.path.dirname(source_file_path)

    for i in range(3):
        try:
            sample: WfGenDataPointV2 = WfGenDataPointV2(
                **yaml_load(pathlib.Path(destination_file_path)) if augment and os.path.exists(pathlib.Path(destination_file_path)) else {}
            )

            if generation_mode == "sequence":
                seqgen_sample_data: SequenceGenerationDataPoint = yaml_load(pathlib.Path(source_file_path))
                sample["solution_workflow"] = _get_workflow_components(seqgen_sample_data, "sequence")
                workflow = Workflow(seqgen_sample_data["description_sequence"], seqgen_sample_data["plan"], WorkflowDict(**sample["solution_workflow"]))

                sample["original_query"] = seqgen_sample_data["description"]
                sample["plan"] = seqgen_sample_data["plan_sequence"]
                sample["target_framework"] = seqgen_sample_data["target_framework"]
                sample["query"] = seqgen_sample_data["description_sequence"]
                sample["existing_workflow_sequence"] = seqgen_sample_data["process_existing"] if is_test_sample else None

            elif generation_mode == "workflow":
                workflow_sample_data: WorkflowGenerationDataPoint = yaml_load(pathlib.Path(source_file_path))
                sample["solution_workflow"] = workflow_sample_data["process"]
                workflow = Workflow(workflow_sample_data["description"], workflow_sample_data["plan"], sample["solution_workflow"])

                sample["original_query"] = workflow_sample_data["description"]
                sample["plan"] = workflow_sample_data["plan"]
                sample["target_framework"] = workflow_sample_data["target_framework"]
                sample["query"] = workflow_sample_data["description"]
                sample["existing_workflow_sequence"] = None

            else:
                print(f"⚠️ {generation_mode} not supported")
                return

            workflow_activity_details = get_workflow_activities(workflow)

            # The actual activities used in the workflow
            sample["used_activities"] = list(workflow_activity_details["activities"].keys())
            sample["mode"] = generation_mode
            sample["used_triggers"] = [workflow_activity_details["trigger_full_name"]] if workflow_activity_details["trigger_full_name"] else []

            if not augment:
                query_planning_result: QueryPlanningResult = await generate_thinking_from_query(sample["query"], workflow, model, model_run_config)
                sample["thinking"] = query_planning_result.thinking
                sample["ambiguities"] = query_planning_result.ambiguities
                sample["score"] = query_planning_result.queryScore

            sample["ambiguities"] = None if sample["ambiguities"] == "None" else sample["ambiguities"]

            # Create a new file in the ActivityRetriever folder in workdir in a similar relative path
            os.makedirs(os.path.dirname(destination_file_path), exist_ok=True)
            yaml_dump(sample, pathlib.Path(destination_file_path))

            print(f"✅ Generated sample data for {destination_file_path}")
            break
        except Exception as e:
            if i == 2:
                print(f"❌ Error generating sample data for {destination_file_path}: {e}")
                break
            continue


def _get_workflow_components(seqgen_sample_data, generation_mode: ActivitiesGenerationMode) -> WorkflowDict:
    sequence = seqgen_sample_data["process"]
    del sequence["processName"]
    del sequence["packages"]

    if generation_mode == "sequence":
        del sequence["trigger"]
        sequence["workflow"] = seqgen_sample_data["process_sequence"]

    return sequence


async def generate_thinking_from_query(query: str, workflow: Workflow, model: BaseChatModel, model_run_config: dict[str, t.Any]) -> QueryPlanningResult:
    messages: list[MessageLike] = [
        langchain.prompts.SystemMessagePromptTemplate.from_template(
            model_run_config["prompt"]["system_msg"], partial_variables={"format_instructions": QueryPlanningResultParser.get_format_instructions()}
        ).format()
    ]

    user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(model_run_config["prompt"]["user_msg_template"])
    messages.append(user_message_template)
    thinking_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(messages)

    chat_chain = thinking_prompt_template | model | QueryPlanningResultParser

    inputs = {"query": query, "workflow": workflow.lmyaml(include_dap_properties=True, use_dap_activity_name=True)}

    # Retry two times
    for _ in range(2):
        with langchain_community.callbacks.get_openai_callback():
            result: QueryPlanningResult = await chat_chain.ainvoke(inputs)
            return result

    raise Exception("Failed to generate thinking from query")


if __name__ == "__main__":
    augment = False
    if len(sys.argv) > 1 and sys.argv[1] == "--augment":
        augment = True

    print("Generating activity retrieval dataset...")
    asyncio.run(generate_retrieval_dataset(augment))
