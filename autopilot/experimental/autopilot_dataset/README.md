# Adding new data to our dataset

## Easy

### Adding new projects or modify existing projects

you need this repo and <https://github.com/UiPath/Autopilot.Samples> repo

you should add your projects to a folder in <https://github.com/UiPath/Autopilot.Samples/tree/main/Dataset/Downloads/StudioDesktop> or modify some of the existing projects.
usually we group projects in a subfolder from there if they are from the same source or of the same type.

you can commit the project folders as they are. just remember that the Downloads folder is on git lfs so commit them using that.

### Convert and create a dataset

to convert from xaml form to yaml and use them in a dataset for autopilot you need to follow these steps:

you need to have [python](https://www.python.org/downloads/) installed
then in python install [pip](https://packaging.python.org/en/latest/tutorials/installing-packages/) for python (in order to install other packages)

go to the current folder and run (to install few dependencies)

```
pip install -r requirements.build_dataset.txt
```

then you can run `build_dataset.py` from UiPath `ML/autopilot` folder. A few examples:

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples"`
this runs the build on all samples (can take a while since we have lots of samples).
you need to point it to the folder where you got the Autopilot.Samples repo

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples" --contains="BUILDCOLL"`
this adds a filter compared to the previous one

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples" --contains="BUILDCOLL" --only-new`
this adds a filter to run the build only on new samples

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples" --contains="BUILDCOLL" --only-errors`
this adds a filter to run the build only on samples that previously have given an error

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples" --contains="BUILDCOLL" --use-existing-convert`
this skips the convert step (first step which is expensive) and uses the existing converted files

an option to be able to run the above commands from anywhere is to set the PYTHONPATH environment variable to your
`ML/autopilot` folder. this will allow python to find `experimental.autopilot_dataset.build_dataset` from anywhere.

an option to run the script from the `autopilot_dataset` folder is to set PYTHONPATH like above,
then you can run the following command from the `ML/autopilot/experimental/autopilot_dataset`:

`python3 -m build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples"`

the issue with the environment variable is that you might need it for other projects and you will need to just append to it.
e.g. `PYTHONPATH=ML/autopilot;ML/computer_vision` (where you have multiple folders in there)

you can also specify multiple contains in a separate file. e.g.

`python3 -m experimental.autopilot_dataset.build_dataset --autopilot-samples "/workspace/data/Autopilot.Samples" --contains-from-file="build_dataset.txt"` where each line is used to filter

## More complicated, more power

follow <https://github.com/UiPath/Autopilot.Samples/blob/main/Dataset/README.md>
