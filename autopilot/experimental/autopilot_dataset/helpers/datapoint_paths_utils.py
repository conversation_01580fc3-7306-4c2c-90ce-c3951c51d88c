from pathlib import Path

from services.studio._text_to_workflow.utils.paths import get_api_workflow_dataset_metadata_path, get_api_workflow_dataset_root_path

ROOT_PATH = get_api_workflow_dataset_root_path()
METADATA_PATH = get_api_workflow_dataset_metadata_path()


def get_corresponding_metadata_file_path(file_path: Path) -> Path:
    """
    Returns the path to the metadata file corresponding to the given file path.
    """
    # Create the new path in the metadata directory
    return METADATA_PATH / file_path.relative_to(ROOT_PATH)
