// Author: <PERSON><PERSON>, GPT
// just use this in the console in the browser to download all uips in the page
// Function to process a single page
function processPage() {
    var parents = document.querySelectorAll('.sw-projects-actions');
    var matchingChildElements = [];

    parents.forEach(function (parent) {
        var child = parent.querySelector('.mat-mdc-button-touch-target');
        if (child) {
            matchingChildElements.push(child);
        }
    });

    matchingChildElements.forEach(function (element, index) {
        setTimeout(function () {
            element.click();

            // Simulate an Escape key press after clicking
            setTimeout(function () {
                var downloadProject = document.querySelectorAll(".download-project");
                downloadProject[0].click();
                // Simulate Escape key press to close the menu
                document.dispatchEvent(new KeyboardEvent('keydown', { 'key': 'Escape' }));
            }, 1000);

        }, index * 5000);
    });

    // Wait for the current page to be processed before moving to the next
    setTimeout(function () {
        goToNextPage();
    }, matchingChildElements.length * 5000 + 1000); // Added additional time for the last Escape action
}

// Function to go to the next page
function goToNextPage() {
    var nextPageButton = document.querySelector('.mat-mdc-paginator-navigation-next');

    // Check if the button is not disabled
    if (!nextPageButton.hasAttribute('disabled')) {
        nextPageButton.click();

        // Wait for the next page to load
        setTimeout(function () {
            processPage();
        }, 10000); // Adjust the time as necessary for page loading
    } else {
        console.log('No more pages to process');
    }
}

// Start the process
processPage();
