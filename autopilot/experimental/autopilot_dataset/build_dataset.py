"""
this is a standalone script used for the ease of adding more data.
we load the studio folder to sys.path just in case it is not in the path.
in rare cases this might not be desirable
"""

import argparse
import json
import os
import tempfile
import time
from pathlib import Path

from experimental.autopilot_dataset.online_client import convert_project
from services.studio._text_to_workflow.common import embeddingsdb, workflow_parser
from services.studio._text_to_workflow.common.helpers import filter_projects, get_sample_split, get_workflow_name, target_framework_to_target_platform
from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load


def convert_project_dry_run(project_json_path, convert_folder, target_platform, tmpdir, index):
    return len(list(convert_folder.glob("**/*.error.json"))) == 0


def convert_downloads(
    autopilot_samples: str | Path, contains: str | None = None, only_new: bool = False, only_errors: bool = False, use_existing_convert: bool = False
):
    autopilot_samples = Path(autopilot_samples)
    downloads_path = autopilot_samples / "Dataset" / "Downloads"
    studio_desktop_path = downloads_path / "StudioDesktop"
    if not studio_desktop_path.exists():
        print(f"Downloads path {studio_desktop_path} does not exist")
        return
    converted_path = autopilot_samples / "Dataset" / "Converted"
    if not converted_path.exists():
        converted_path.mkdir(parents=True)
    workflow_generation_path = autopilot_samples / "Dataset" / "WorkflowGeneration"

    print(f"Converting from {studio_desktop_path}")
    print(f"Converting to {converted_path}")

    embeddings_db_path = autopilot_samples / "Dataset" / "Embeddings" / "embeddings.db"
    activity_definitions = embeddingsdb.load_activity_definitions(embeddings_db_path)
    projects_to_convert = filter_projects(studio_desktop_path, converted_path, contains, only_new, only_errors)

    tmpdir = tempfile.mkdtemp()
    print(f"temporarily storing files in {tmpdir}")
    for i, (project_json_path, convert_folder, project_json) in enumerate(projects_to_convert):
        print(f"{i} / {len(projects_to_convert)} Converting {project_json_path}")

        target_framework = project_json["targetFramework"]  # Portable, Windows, Legacy
        target_platform = target_framework_to_target_platform(target_framework)

        start_time = time.time()
        if use_existing_convert:
            is_success = convert_project_dry_run(project_json_path, convert_folder, target_platform, tmpdir, str(i))
        else:
            is_success = convert_project(project_json_path, convert_folder, target_platform, tmpdir, str(i))
        duration = time.time() - start_time
        print(f"Conversion request {convert_folder.relative_to(converted_path)} {'success' if is_success else 'failed'}. {duration} seconds")
        if not is_success:
            continue

        for converted_json_path in convert_folder.glob("**/*.json"):
            if converted_json_path.name == "project.json":
                continue
            if converted_json_path.name.endswith(".typedef.json"):
                continue
            if converted_json_path.name.endswith(".error.json"):
                continue

            with open(converted_json_path, "r", encoding="utf-8-sig") as f:
                converted_result = json.load(f)
            conversion_success = converted_result["Success"]
            has_errors = len(converted_result["Errors"]) > 0
            if not conversion_success:
                status = "error"
            elif has_errors:
                status = "warning"
            else:
                status = "success"

            print(f"Conversion {status}: {converted_json_path}")
            if status == "error":
                os.rename(converted_json_path, converted_json_path.with_suffix(".error.json"))
                continue

            workflow_converted = yaml_load(converted_result["result"])
            yaml_dump(workflow_converted, converted_json_path.with_suffix(".yaml"))

            dataset_output_dir_path = workflow_generation_path / target_framework
            testsets = {name: set(testset) for name, testset in yaml_load(dataset_output_dir_path / "subsets.yaml").items()}
            workflow_name = get_workflow_name(converted_json_path, relative_to=converted_path / target_framework)

            has_yaml = len(converted_result["result"].strip()) > 20
            data = converted_result
            if has_yaml:
                data = workflow_parser.build_yaml_object(converted_result, project_json)

                trigger = data["retrieved_triggers"][0]
                activities = data["activities"]
                del data["activities"]
                all_activities = [trigger] + activities

                plan_categories = workflow_parser.build_plan_categories(data["plan"], all_activities, activity_definitions)
                data["plan_categories"] = multiline_str(plan_categories)

                typedef_path = converted_json_path.parent / f"{converted_json_path.stem}.typedef.json"
                if typedef_path.exists():
                    with open(typedef_path, "r", encoding="utf-8-sig") as f:
                        data["typedefs"] = json.load(f)

            if has_yaml and len(str(data["process"])) >= 10000:  # ~ 2000 tokens
                split = ".errors_too_long"
            elif has_yaml and "[Jarvis]" in converted_json_path.as_posix() and "fixes_20240411" in converted_json_path.as_posix():
                split = ".errors_jarvis"
            elif has_yaml and not workflow_parser.has_old_business_entity(data) and data.get("process", {}).get("workflow") is not None:
                split = get_sample_split(workflow_name, testsets)
            elif has_yaml and workflow_parser.has_old_business_entity(data) and data.get("process", {}).get("workflow") is not None:
                split = ".errors_old_business_entity"
            else:
                split = ".errors"

            split_path = dataset_output_dir_path / split
            example_path = Path(str(split_path / workflow_name) + ".yaml")
            yaml_dump(data, example_path)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--autopilot-samples", type=str, required=True, help="Path to the autopilot samples folder")
    contains_group = ap.add_mutually_exclusive_group()
    contains_group.add_argument("--contains", type=str, default=None, help="Only convert projects that contain this string in the path")
    contains_group.add_argument("--contains-from-file", type=str, help="Path to a file with filters, one per line")
    ap.add_argument("--only-new", default=False, action="store_true", help="Only convert projects that have not been converted yet")
    ap.add_argument("--only-errors", default=False, action="store_true", help="Only convert projects that have had errors last time")
    ap.add_argument(
        "--use-existing-convert", default=False, action="store_true", help="Use existing conversion instead of converting again and create dataset with that"
    )
    args = ap.parse_args()

    # Get filters either from command line or file
    if args.contains:
        convert_downloads(args.autopilot_samples, args.contains, args.only_new, args.only_errors, args.use_existing_convert)
    elif args.contains_from_file:
        with open(args.contains_from_file, "r") as f:
            filters = [line.strip() for line in f if line.strip()]
        for search_string in filters:
            print(f"\nProcessing projects containing: {search_string}")
            convert_downloads(args.autopilot_samples, search_string, args.only_new, args.only_errors, args.use_existing_convert)
    else:
        convert_downloads(args.autopilot_samples, None, args.only_new, args.only_errors, args.use_existing_convert)
