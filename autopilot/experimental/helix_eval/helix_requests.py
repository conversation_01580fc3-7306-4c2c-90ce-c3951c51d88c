import json

import httpx

from experimental.helix_eval import configs
from experimental.helix_eval.helix_helper import create_uuid

helix_url = configs.HELIX_URL_BASE
project_id = configs.HELIX_AUTPILOT_PROJECT_ID


async def post_eval_set_run(data_dict):
    url = helix_url + "/ingest/eval-set-run"
    headers = {"Content-Type": "application/json"}

    data_dict["project_id"] = project_id

    if "id" not in data_dict:
        id = create_uuid()
        data_dict["id"] = id

    # convert dict to json string
    data_json = json.dumps(data_dict)

    async with httpx.AsyncClient() as client:
        response = await client.post(url, data=data_json, headers=headers)

    response.raise_for_status()
    return response
