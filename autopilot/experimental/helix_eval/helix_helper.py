import asyncio
import inspect
import uuid
from datetime import datetime, timezone

import typing_extensions as t

from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.request_utils import get_request_context


class Span(t.TypedDict):
    id: str
    name: str
    parent_id: str
    start_time: int
    end_time: int
    attributes: dict
    status: int
    parent_name: str


class Trace(t.TypedDict):
    attributes: dict
    spans: list[Span]


def create_span(
    name: str,
    start_time: str,
    end_time: str,
    inputs: list,
    outputs: list,
    status: int,
    parent_name: str,
    id: str = None,
    parent_id: str = None,
) -> Trace:
    attributes = get_span_attributes(inputs, outputs)
    return {
        "id": id,
        "name": name,
        "parent_id": parent_id,
        "start_time": start_time,
        "end_time": end_time,
        "attributes": attributes,
        "status": status,
        "parent_name": parent_name,
    }


def get_span_attributes(inputs: dict, outputs: dict) -> dict:
    prefix_inputs = {f"input.{key}": str(value) for key, value in inputs.items()}
    prefix_outputs = {f"output.{key}": str(value) for key, value in outputs.items()}

    return {**prefix_inputs, **prefix_outputs}


def add_span(request_context: RequestContext, span):
    if request_context is not None:
        request_context.spans.append(span)
    else:
        pass


def get_spans(request_context: RequestContext) -> list[dict]:
    if request_context is not None:
        spans = request_context.spans

        for span in spans:
            if span["parent_name"]:
                parent_span = next((item for item in spans if item["name"] == span["parent_name"]), None)
                if parent_span:
                    span["parent_id"] = parent_span.get("id", None)
            del span["parent_name"]
        return spans
    else:
        return None


def create_uuid() -> str:
    return str(uuid.uuid4())


def get_time() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"


def log_helix(span_name, parent_name=None, input_func=lambda x: {k: v for k, v in x.items() if isinstance(v, str)}, output_func=lambda x: x):
    def span_decorator(func):
        sig = inspect.signature(func)

        def wrapper(*args, **kwargs):
            start_time_string = get_time()
            input_info, request_context = _extract_details(args, kwargs)

            try:
                result = func(*args, **kwargs)
                end_time_string = get_time()
                output_info = output_func(result)
                add_span(request_context, create_span(span_name, start_time_string, end_time_string, input_info, output_info, 1, parent_name, create_uuid()))
                return result
            except Exception as e:
                end_time_string = get_time()
                output_info = {"error": str(e)}
                add_span(request_context, create_span(span_name, start_time_string, end_time_string, input_info, output_info, 0, parent_name, create_uuid()))
                raise e

        async def awrapper(*args, **kwargs):
            start_time_string = get_time()
            input_info, request_context = _extract_details(args, kwargs)

            try:
                result = await func(*args, **kwargs)
                end_time_string = get_time()
                output_info = output_func(result)
                add_span(request_context, create_span(span_name, start_time_string, end_time_string, input_info, output_info, 1, parent_name, create_uuid()))
                return result
            except Exception as e:
                end_time_string = get_time()
                output_info = {"error": str(e)}
                add_span(request_context, create_span(span_name, start_time_string, end_time_string, input_info, output_info, 0, parent_name, create_uuid()))
                raise e

        def _extract_details(args, kwargs):
            arguments = {}
            for k, v in zip(sig.parameters, args, strict=False):
                arguments[k] = v
            arguments.update(kwargs)

            input_info = input_func(arguments)

            request_context = get_request_context()

            if request_context is None:
                raise ValueError("Request context is None. In order to use @log_helix, you need to pass request_context as an argument to the function.")
            return input_info, request_context

        return awrapper if asyncio.iscoroutinefunction(func) else wrapper

    return span_decorator
