examples = [
    {
        "userRequest": "Compute the sum of two numbers from the fields of the $input_object variable.",
        "currentExpression": "",
        "benchmarkExpression": "const sum = $input_object.a + $input_object.b;\nreturn { result: sum };",
        "availableVariables": [{"name": "input_object", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "test-workflow-id",
    },
    {
        "userRequest": "Write today's date in the format YYYY-MM-DD.",
        "currentExpression": "",
        "benchmarkExpression": "const date = new Date();\nconst isoString = date.toISOString();\nconst parts = isoString.split('T');\nreturn parts[0];",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the sum of the first 100 natural numbers without any formula.",
        "currentExpression": "",
        "benchmarkExpression": "const numbers = Array.from({ length: 100 }, (_, i) => i + 1);\nconst sum = numbers.reduce((sum, n) => sum + n, 0);\nreturn sum.toString();",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the average of the squares of the first 5 natural numbers.",
        "currentExpression": "",
        "benchmarkExpression": "const squares = Array.from({ length: 5 }, (_, i) => i * i);\nconst sum = squares.reduce((sum, n) => sum + n, 0);\nreturn sum / 5;",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the factorial of 7.",
        "currentExpression": "",
        "benchmarkExpression": "const numbers = Array.from({ length: 7 }, (_, i) => i + 1);\nconst factorial = numbers.reduce((acc, n) => acc * n, 1);\nreturn factorial;",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first 10 Fibonacci numbers.",
        "currentExpression": "",
        "benchmarkExpression": "const fib = [0, 1];\nfor (let i = 0; i < 8; i++) {\n  fib.push(fib[fib.length-1] + fib[fib.length-2]);\n}\nconst result = fib.map(String);\nreturn result;",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "Array<string>",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first day of last month in YYYY-MM-DD format.",
        "currentExpression": "",
        "benchmarkExpression": 'const d = new Date();\nconst d2 = new Date(d.getFullYear(), d.getMonth() - 1, 1);\nconst year = d2.getFullYear();\nconst month = (d2.getMonth() + 1).toString().padStart(2, "0");\nconst day = d2.getDate().toString().padStart(2, "0");\nreturn `${year}-${month}-${day}`;',
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Check if the current year is a leap year.",
        "currentExpression": "",
        "benchmarkExpression": "const y = new Date().getFullYear();\nconst isLeapYear = y % 4 === 0 && (y % 100 !== 0 || y % 400 === 0);\nreturn isLeapYear.toString();",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Output all the letters in the alphabet in a single string.",
        "currentExpression": "",
        "benchmarkExpression": 'const letters = Array.from({ length: 26 }, (_, i) => String.fromCharCode(97 + i));\nconst alphabet = letters.join("");\nreturn alphabet;',
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890ab",
    },
    {
        "userRequest": "Replace all 'a' with 'b' in 'banana'",
        "currentExpression": "",
        "benchmarkExpression": 'const original = "banana";\nconst replaced = original.replace(/a/g, "b");\nreturn replaced;',
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Reverse the order of words 'A lazy fox jumps over the fence'",
        "currentExpression": "",
        "benchmarkExpression": 'const sentence = "A lazy fox jumps over the fence";\nconst words = sentence.split(" ");\nconst reversed = words.reverse();\nconst result = reversed.join(" ");\nreturn result;',
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Compute the average age of the users.",
        "currentExpression": "",
        "benchmarkExpression": "const sum = httpResponse.users.reduce((sum, user) => sum + user.age, 0);\nconst count = httpResponse.users.length;\nreturn sum / count;",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { users: User[]; }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "interface User { name: string; country: string; age: number; }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890",
    },
    {
        "userRequest": "Compute how many employees occupy the same position for all positions.",
        "currentExpression": "",
        "benchmarkExpression": "const positionCounts = {};\nfor (const e of httpResponse.employees) {\n  positionCounts[e.position] = (positionCounts[e.position] || 0) + 1;\n}\nreturn JSON.stringify(positionCounts);",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { employees: Employee[]; }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "interface Employee { name: string; position: string; }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890a",
    },
    {
        "userRequest": "Compute the maximum number from the first half of the input list.",
        "currentExpression": "",
        "benchmarkExpression": "const halfLength = Math.floor(l.length / 2);\nconst firstHalf = l.slice(0, halfLength);\nconst max = Math.max(...firstHalf);\nreturn max;",
        "availableVariables": [{"name": "l", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Compute the sum of even numbers in the input list.",
        "currentExpression": "",
        "benchmarkExpression": "const evenNumbers = numbers.filter(n => n % 2 === 0);\nconst sum = evenNumbers.reduce((sum, n) => sum + n, 0);\nreturn sum;",
        "availableVariables": [{"name": "numbers", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Reverse the string received as input",
        "currentExpression": "",
        "benchmarkExpression": "const chars = str.split('');\nconst reversed = chars.reverse();\nconst result = reversed.join('');\nreturn result;",
        "availableVariables": [{"name": "str", "type": "string"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Find the common keys between two dictionaries.",
        "currentExpression": "",
        "benchmarkExpression": "const keys1 = Object.keys(dict1);\nconst keys2 = Object.keys(dict2);\nconst commonKeys = keys1.filter(key => keys2.includes(key));\nreturn commonKeys;",
        "availableVariables": [
            {"name": "dict1", "type": "interface Dict1 { A: number; B: number }"},
            {"name": "dict2", "type": "interface Dict2 { B: number; C: number }"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string[]",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-987654321",
    },
    {
        "userRequest": "You will receive 3 variables, a, b, c in a json. We know a>0. You need to solve e^(ax+b) = c. If there is no solution, return null.",
        "currentExpression": "",
        "benchmarkExpression": "if (input.c <= 0) {\n  return null;\n}\nconst logC = Math.log(input.c);\nconst result = (logC - input.b) / input.a;\nreturn result;",
        "availableVariables": [
            {"name": "input", "type": "interface Input { a: number; b: number; c: number }"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a string as input. You should write all email addresses present in it.",
        "currentExpression": "",
        "benchmarkExpression": "const regex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g;\nconst matches = input.match(regex);\nreturn matches || [];",
        "availableVariables": [
            {"name": "input", "type": "string"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string[]",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a list as input. Return the product of numbers on even positions.",
        "currentExpression": "",
        "benchmarkExpression": "const evenPositionNumbers = l.filter((_, i) => i % 2 === 0);\nconst product = evenPositionNumbers.reduce((prod, num) => prod * num, 1);\nreturn product;",
        "availableVariables": [
            {"name": "l", "type": "number[]"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Compute the average of the numbers in the list.",
        "currentExpression": "",
        "benchmarkExpression": "const sum = inp.list.reduce((sum, n) => sum + n, 0);\nconst avg = sum / inp.list.length;\nconst result = { result: avg };\nreturn result;",
        "availableVariables": [{"name": "inp", "type": "interface List { list: number[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-98765432",
    },
    {
        "userRequest": "Output the unique elements from the intersection of two lists, $list1 and $list2",
        "currentExpression": "",
        "benchmarkExpression": "const set1 = new Set(input.list1);\nconst uniqueList1 = [...set1];\nconst set2 = new Set(input.list2);\nconst intersection = uniqueList1.filter(x => set2.has(x));\nreturn { intersection };",
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { intersection: number[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210",
    },
    {
        "userRequest": "Compute a raised to the power of b.",
        "currentExpression": "",
        "benchmarkExpression": "const factors = Array.from({ length: input.b }, () => input.a);\nconst power = factors.reduce((acc, curr) => acc * curr, 1);\nreturn { result: power };",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210",
    },
    {
        "userRequest": "Output all numbers in the list $l that are between $a and $b, inclusive.",
        "currentExpression": "",
        "benchmarkExpression": "const filtered = inp.l.filter(n => n >= inp.a && n <= inp.b);\nreturn { result: filtered };",
        "availableVariables": [{"name": "inp", "type": "interface Input { l: number[], a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Output all strings in the list $words that contain the substring $sub.",
        "currentExpression": "",
        "benchmarkExpression": "const filtered = inp.words.filter(word => word.includes(inp.sub));\nreturn { result: filtered };",
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[], sub: string }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Convert all strings to uppercase.",
        "currentExpression": "",
        "benchmarkExpression": "const upperCaseWords = inp.words.map(word => word.toUpperCase());\nreturn { result: upperCaseWords };",
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210a",
    },
    {
        "userRequest": "Group items by category.",
        "currentExpression": "",
        "benchmarkExpression": "const grouped = {};\nfor (const item of inp.items) {\n  if (!grouped[item.category]) {\n    grouped[item.category] = [];\n  }\n  grouped[item.category].push(item);\n}\nreturn { result: grouped };",
        "availableVariables": [{"name": "inp", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: { [key: string]: { name: string, category: string }[] } }",
        "additionalTypeDefinitions": "interface Item { name: string, category: string }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210a",
    },
    {
        "userRequest": "Combine first and last names into a full name.",
        "currentExpression": "",
        "benchmarkExpression": 'const fullNames = inp.people.map(p => {\n  return { full_name: p.first_name + " " + p.last_name };\n});\nreturn { result: fullNames };',
        "availableVariables": [{"name": "inp", "type": "interface Input { people: Person[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: { full_name: string }[] }",
        "additionalTypeDefinitions": "interface Person { first_name: string, last_name: string }",
        "allVariablesIncluded": True,
        "activityTypeName": "JsInvoke",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-98765432",
    },
]
