# flake8: noqa
request_metadata = {
    "availableVariables": [
        {"name": "cellValue", "type": "System.String"},
        {
            "name": "emailList",
            "type": "System.Collections.Generic.List`1[[UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null]]",
        },
        {
            "name": "eventList",
            "type": "System.Collections.Generic.List`1[[UiPath.GSuite.Calendar.Models.GSuiteEventItem, UiPath.GSuite, Version=*******, Culture=neutral, PublicKeyToken=null]]",
        },
        {"name": "gEmail", "type": "UiPath.GSuite.Models.GmailMessage"},
        {
            "name": "generateChatCompletion",
            "type": "UiPath.IntegrationService.Activities.SWEntities.C0EF81B727E_generateChatCompletion_Create.Bundle.generateChatCompletion_Create",
        },
        {"name": "jiraIssue", "type": "UiPath.Jira.IntegrationService.Client.Curated_issue"},
        {"name": "rangeInfo", "type": "UiPath.MicrosoftOffice365.Excel.Models.RangeInformation"},
        {"name": "readRange", "type": "System.Data.DataTable"},
        {"name": "receivedEmail", "type": "UiPath.MicrosoftOffice365.Models.Office365Message"},
        {"name": "fileInfo", "type": "UiPath.IntegrationService.Activities.SWEntities.C217C31182D_files_info_GET_Retrieve.Bundle.files_info_GET_Retrieve"},
        {"name": "preHire", "type": "UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.GetPreHireByEmail_List[]"},
    ],
    "expressionTypeDefinition": "String",
    "expressionLanguage": "vbnet",
}

scenarios = [
    {"userRequest": "Last cell from the excel range", "expressionTypeDefinition": "String", "benchmarkExpression": "rangeInfo.EndCell"},
    {
        "userRequest": "One week from the end of the first event in the list",
        "expressionTypeDefinition": "String",
        "benchmarkExpression": "eventList.First().EndDate.AddDays(7).ToString()",
    },
    {
        "userRequest": "Date of background check of prehire",
        "expressionTypeDefinition": "String",
        "benchmarkExpression": "preHire(0).Background_Check_Data(0).Status_Date.ToString()",
    },
    {
        "userRequest": "email adress for the reporter of the jira issue",
        "expressionTypeDefinition": "String",
        "benchmarkExpression": "jiraIssue.Fields.Reporter.EmailAddress",
    },
    {
        "userRequest": "Concat the subject from the received emails list",
        "expressionTypeDefinition": "String",
        "benchmarkExpression": 'String.Join(", ", emailList.Select(Function(email) email.Subject))',
    },
    {
        "userRequest": "check if url of file info is valid",
        "expressionTypeDefinition": "String",
        "benchmarkExpression": "System.Uri.IsWellFormedUriString(fileInfo.url_private, System.UriKind.Absolute).ToString()",
    },
]
