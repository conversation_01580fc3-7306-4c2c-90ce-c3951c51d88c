# flake8: noqa
# typedefs for the following activities outputs:
# new outlook email received, openai chat completion, outlook email list, jira issue, gmail get email
# excel read cell, gmail get event list, workday get pre-hire by email, excel read range
additional_typedefinitions = "namespace UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle { class GetPreHireByEmail_List { string Applicant_ID; string version_Attribute; List<Background_Check_Data> Background_Check_Data; List<Document_Field_Result_Data> Document_Field_Result_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.External_Integration_ID_Data External_Integration_ID_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Personal_Data Personal_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Qualification_Data Qualification_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Recruiting_Data Recruiting_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Response_Filter Response_Filter; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Response_Group Response_Group; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Response_Results Response_Results; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Resume_Data Resume_Data; } class Background_Check_Data { string Status_Comment; DateTimeOffset? Status_Date; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Status_Reference Status_Reference; } class Document_Field_Result_Data { string Value; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Field_Reference Field_Reference; } class External_Integration_ID_Data { List<ID> ID; } class Personal_Data { bool? Tobacco_Use; string Universal_ID; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Contact_Data Contact_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Identification_Data Identification_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Name_Data Name_Data; List<Personal_Information_Data> Personal_Information_Data; } class Qualification_Data { List<Award_and_Activity> Award_and_Activity; List<Certification> Certification; List<Competency> Competency; List<Education> Education; List<External_Job_History> External_Job_History; List<Internal_Project_Experience> Internal_Project_Experience; List<Language> Language; List<Organization_Membership> Organization_Membership; List<Training> Training; List<Work_Experience> Work_Experience; } class Recruiting_Data { string Applicant_Comments; DateTimeOffset? Applicant_Entered_Date; string Eligible_for_Rehire_Comments; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Applicant_Has_Marked_as_No_Show_Reference Applicant_Has_Marked_as_No_Show_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Applicant_Source_Reference Applicant_Source_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Eligible_For_Hire_Reference Eligible_For_Hire_Reference; List<Positions_Considered_for_Reference> Positions_Considered_for_Reference; List<Referred_by_Worker_Reference> Referred_by_Worker_Reference; } class Response_Filter { DateTimeOffset? As_Of_Effective_Date; DateTimeOffset? As_Of_Entry_DateTime; } class Response_Group { bool? Show_All_Personal_Information; } class Response_Results { double? Page_Results; double? Total_Pages; double? Total_Results; } class Resume_Data { string Comments; string FileName; string File_ID; } class Status_Reference { List<ID> ID; } class Field_Reference { List<ID> ID; } class ID { string Attribute_Value; string Attribute_type; } class Contact_Data { List<Address_Data> Address_Data; List<Email_Address_Data> Email_Address_Data; List<Instant_Messenger_Data> Instant_Messenger_Data; List<Phone_Data> Phone_Data; List<Web_Address_Data> Web_Address_Data; } class Identification_Data { List<Custom_ID> Custom_ID; List<Government_ID> Government_ID; List<License_ID> License_ID; List<National_ID> National_ID; List<Passport_ID> Passport_ID; List<Visa_ID> Visa_ID; } class Name_Data { List<Additional_Name_Data> Additional_Name_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Legal_Name_Data Legal_Name_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Preferred_Name_Data Preferred_Name_Data; } class Personal_Information_Data { DateTimeOffset? Birth_Date; string City_of_Birth; DateTimeOffset? Date_of_Death; DateTimeOffset? Last_Medical_Exam_Date; DateTimeOffset? Last_Medical_Exam_Valid_To; string Medical_Exam_Notes; string Region_of_Birth_Descriptor; List<Additional_Nationalities_Reference> Additional_Nationalities_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Blood_Type_Reference Blood_Type_Reference; List<Citizenship_Status_Reference> Citizenship_Status_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.City_of_Birth_Reference City_of_Birth_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Country_Region_of_Birth_Reference Country_Region_of_Birth_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Country_of_Birth_Reference Country_of_Birth_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Gender_Identity_Reference Gender_Identity_Reference; List<Military_Service_Data> Military_Service_Data; List<Personal_Information_For_Country_Data> Personal_Information_For_Country_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Primary_Nationality_Reference Primary_Nationality_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Pronoun_Reference Pronoun_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Sexual_Orientation_Reference Sexual_Orientation_Reference; } class Award_and_Activity { UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Award_and_Activity_Data Award_and_Activity_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Award_and_Activity_Reference Award_and_Activity_Reference; } class Certification { List<Certification_Data> Certification_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Certification_Reference Certification_Reference; } class Competency { string Assessed_By_Worker_Descriptor; DateTimeOffset? Assessed_On; string Assessment_Comment; string Competency_Descriptor; string Competency_Level_Behavior_Descriptor; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Assessed_By_Person_Reference Assessed_By_Person_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Competency_Level_Behavior_Reference Competency_Level_Behavior_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Competency_Reference Competency_Reference; } class Education { List<Education_Data> Education_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Education_Reference Education_Reference; } class External_Job_History { List<Job_History_Data> Job_History_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Job_History_Reference Job_History_Reference; } class Internal_Project_Experience { List<Internal_Project_Experience_Data> Internal_Project_Experience_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Internal_Project_Experience_Reference Internal_Project_Experience_Reference; } class Language { DateTimeOffset? Assessed_On; bool? Native_Language; string Note; bool? Remove_Language; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Assessed_by_Worker_Reference Assessed_by_Worker_Reference; List<Language_Ability> Language_Ability; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Language_Reference Language_Reference; } class Organization_Membership { List<Organization_Professional_Affiliation_Data> Organization_Professional_Affiliation_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Organization_Professional_Affiliation_Reference Organization_Professional_Affiliation_Reference; } class Training { List<Training_Data> Training_Data; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Training_Reference Training_Reference; } class Work_Experience { string Experience_Comment; bool? Remove_Experience; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Experience_Rating_Reference Experience_Rating_Reference; UiPath.IntegrationService.Activities.SWEntities.C5888C27CBB_GetPreHireByEmail_List.Bundle.Experience_Reference Experience_Reference; } class Applicant_Has_Marked_as_No_Show_Reference { List<ID> ID; } class Applicant_Source_Reference { List<ID> ID; } class Eligible_For_Hire_Reference { List<ID> ID; } class Positions_Considered_for_Reference { List<ID> ID; } class Referred_by_Worker_Reference { List<ID> ID; } } namespace UiPath.MicrosoftOffice365.Models { class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; Importance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailAddressCollection Bcc; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } } namespace UiPath.IntegrationService.Activities.SWEntities.C0EF81B727E_generateChatCompletion_Create.Bundle { class generateChatCompletion_Create { long? created; string model; string id; string _object; string text; List<choices> choices; UiPath.IntegrationService.Activities.SWEntities.C0EF81B727E_generateChatCompletion_Create.Bundle.usage usage; } class choices { int? index; string finish_reason; UiPath.IntegrationService.Activities.SWEntities.C0EF81B727E_generateChatCompletion_Create.Bundle.message message; } class usage { int? total_tokens; int? prompt_tokens; long? completion_tokens; } class message { string role; string content; } } namespace UiPath.Jira.IntegrationService.Client { class Curated_issue { UiPath.Jira.IntegrationService.Client.Curated_issueChangelog Changelog; UiPath.Jira.IntegrationService.Client.Curated_issueEditmeta Editmeta; string Expand; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsToInclude FieldsToInclude; UiPath.Jira.IntegrationService.Client.Curated_issueUpdate Update; UiPath.Jira.IntegrationService.Client.Curated_issueFields Fields; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadata HistoryMetadata; string Id; string Key; UiPath.Jira.IntegrationService.Client.Curated_issueOperations Operations; UiPath.Jira.IntegrationService.Client.Curated_issueProperties Properties; UiPath.Jira.IntegrationService.Client.Curated_issueSchema Schema; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueTransition Transition; UiPath.Jira.IntegrationService.Client.Curated_issueTransitions Transitions; IDictionary<string, object> AdditionalProperties; } class Curated_issueChangelog { UiPath.Jira.IntegrationService.Client.Curated_issueChangelogHistories Histories; int? MaxResults; int? StartAt; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueEditmeta { UiPath.Jira.IntegrationService.Client.Curated_issueEditmetaFields Fields; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsToInclude { ICollection<string> ActuallyIncluded; ICollection<string> Excluded; ICollection<string> Included; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdate { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelink Issuelink; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinks Issuelinks; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateComment Comment; IDictionary<string, object> AdditionalProperties; } class Curated_issueFields { UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAggregateprogress Aggregateprogress; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAssignee Assignee; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsComponents Components; DateTimeOffset? Created; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsCreator Creator; string Description; DateTimeOffset? Duedate; string Environment; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsFixVersions FixVersions; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsIssuetype Issuetype; ICollection<string> Labels; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsParent Parent; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsPriority Priority; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProgress Progress; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProject Project; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsReporter Reporter; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsSecurity Security; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsStatus Status; DateTimeOffset? Statuscategorychangedate; string Summary; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsTimetracking Timetracking; DateTimeOffset? Updated; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsVersions Versions; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsVotes Votes; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsWatches Watches; int? Workratio; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadata { string ActivityDescription; string ActivityDescriptionKey; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataActor Actor; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataCause Cause; string Description; string DescriptionKey; string EmailDescription; string EmailDescriptionKey; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataGenerator Generator; string Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueOperations { UiPath.Jira.IntegrationService.Client.Curated_issueOperationsLinkGroups LinkGroups; IDictionary<string, object> AdditionalProperties; } class Curated_issueSchema { string Custom; long? CustomId; string Items; string System; string Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransition { UiPath.Jira.IntegrationService.Client.Curated_issueTransitionErrorCollection ErrorCollection; string Expand; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionFields Fields; bool? HasScreen; string Id; bool? IsAvailable; bool? IsConditional; bool? IsGlobal; bool? IsInitial; bool? Looped; string Name; int? Status; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionTo To; IDictionary<string, object> AdditionalProperties; } class Curated_issueEditmetaFields { ICollection<string> AllowedValues; string AutoCompleteUrl; string DefaultValue; bool? HasDefaultValue; string Key; string Name; ICollection<string> Operations; bool? Required; UiPath.Jira.IntegrationService.Client.Curated_issueEditmetaFieldsSchema Schema; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateIssuelink { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinkOutwardIssue OutwardIssue; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinkType Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateIssuelinks { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinksOutwardIssue OutwardIssue; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinksType Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateComment { string Body; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsAggregateprogress { int? Progress; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsAssignee { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAssigneeAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Id; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsCreator { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsCreatorAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsIssuetype { int? AvatarId; string Description; string EntityId; int? HierarchyLevel; string IconUrl; string Id; string Name; string Self; bool? Subtask; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsParent { string Key; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsPriority { string IconUrl; string Id; string Name; string Self; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsProgress { int? Progress; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsProject { UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProjectAvatarUrls AvatarUrls; string Id; string Key; string Name; string ProjectTypeKey; string Self; bool? Simplified; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsReporter { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsReporterAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Id; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsSecurity { string Id; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsStatus { string Description; string IconUrl; string Id; string Name; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsStatusStatusCategory StatusCategory; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsTimetracking { string OriginalEstimate; string RemainingEstimate; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsVotes { bool? HasVoted; string Self; int? Votes; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsWatches { bool? IsWatching; string Self; int? WatchCount; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataActor { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataCause { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataGenerator { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionErrorCollection { ICollection<string> ErrorMessages; int? Status; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionFields { ICollection<string> AllowedValues; string AutoCompleteUrl; string DefaultValue; bool? HasDefaultValue; string Key; string Name; ICollection<string> Operations; bool? Required; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionFieldsSchema Schema; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionTo { string Description; string IconUrl; string Id; string Name; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionToStatusCategory StatusCategory; IDictionary<string, object> AdditionalProperties; } } namespace UiPath.GSuite.Models { class GmailMessage { DateTime? ReceivedDateTime; DateTime? SentDateTime; string InternetMessageId; string WebLink; string MessageId; string ThreadId; List<string> LabelIds; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; string FolderName; string BodyAsHtml; string Body; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailAddressCollection Bcc; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.MailPriority Priority; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } } namespace UiPath.GSuite.Calendar.Models { class GSuiteEventItem { string CalendarId; string Id; DateTimeOffset? Created; DateTime CreatedDate; DateTimeOffset? Updated; DateTime UpdatedDate; string ICalUId; string Summary; string Visibility; string Transparency; string Type; UiPath.GSuite.Calendar.Models.DateTimeTimeZone Start; DateTime StartDate; UiPath.GSuite.Calendar.Models.DateTimeTimeZone End; DateTime EndDate; string Location; IList<string> Recurrence; IEnumerable<GSuiteEventAttendee> Attendees; string Description; string PreferredReturnTimezone; string WebLink; UiPath.GSuite.Calendar.Models.GSuiteOrganizer Organizer; string OrganizerDisplayName; string OrganizerEmail; } class DateTimeTimeZone { string TimeZone; DateTime DateTime; } class GSuiteEventAttendee { string DisplayName; string Email; string Comment; bool? Optional; bool? Resource; bool? Organizer; UiPath.GSuite.Calendar.Models.GSuiteEventAttendeeStatus ResponseStatus; } class GSuiteOrganizer { string DisplayName; string Email; string Id; bool? Self; } } namespace UiPath.MicrosoftOffice365.Excel.Models { class RangeInformation { string Name; string FullRangeAddress; string Worksheet; string RangeAddress; string StartCell; string EndCell; } } namespace UiPath.IntegrationService.Activities.SWEntities.C217C31182D_files_info_GET_Retrieve.Bundle { class files_info_GET_Retrieve { List<string> channels; int? comments_count; int? created; int? date_delete; bool? display_as_bot; bool? editable; string editor; string external_id; string external_type; string external_url; string filetype; List<string> groups; bool? has_rich_preview; string id; int? image_exif_rotation; List<string> ims; bool? is_external; bool? is_public; bool? is_starred; bool? is_tombstoned; string mimetype; string mode; string name; bool? non_owner_editable; int? num_stars; int? original_h; int? original_w; string permalink; string permalink_public; List<string> pinned_to; string pretty_type; string preview; bool? public_url_shared; int? size; string source_team; string state; string thumb_1024; int? thumb_1024_h; int? thumb_1024_w; string thumb_160; string thumb_360; int? thumb_360_h; int? thumb_360_w; string thumb_480; int? thumb_480_h; int? thumb_480_w; string thumb_64; string thumb_720; int? thumb_720_h; int? thumb_720_w; string thumb_80; string thumb_800; int? thumb_800_h; int? thumb_800_w; string thumb_960; int? thumb_960_h; int? thumb_960_w; string thumb_tiny; int? timestamp; string title; int? updated; string url_private; string url_private_download; string user; string username; List<reactions> reactions; } class reactions { int? count; string name; } }"
