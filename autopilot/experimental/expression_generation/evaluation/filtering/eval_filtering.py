from request_testcases import request_metadata, scenarios
from test_typedefs import additional_typedefinitions

from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.expression_generation.expression_generation_typedefs_filter import ExpressionGenerationTypedefsFilter
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


def eval_examples():
    for scenario in scenarios:
        try:
            ex = scenario
            ex["additionalTypeDefinitions"] = additional_typedefinitions
            ex.update(request_metadata)

            response = expression_generation_endpoint.generate(ex)
            result = yaml_load(response["result"])
            result_expression = result["expression"]

            print("Result for prompt: {}".format(ex["userRequest"]))
            benchmark_expression = ex["benchmarkExpression"]
            print("Expected: {}. Actual: {}".format(benchmark_expression, result_expression))
            # distance = Levenshtein.ratio(result_expression, benchmark_expression)
            # if distance < 0.85:
            #     print("Wrong answer for example: {}".format(ex["userRequest"]))
            #     print("Expected: {}. Actual: {}".format(ex["benchmarkExpression"], result_expression))
            #     print(distance)
        except Exception as e:
            print("Exception for example: {}".format(ex["userRequest"]))
            print(e)


def test_filtering():
    config = {"model_type": "sentence-embedding", "deployment_name": "all-mpnet-base-v2"}
    typedef_filter = ExpressionGenerationTypedefsFilter(config)
    typedefs = additional_typedefinitions

    for scenario in scenarios:
        typedefs_with_scores, parsed_namespaces = typedef_filter.get_typedefs_scores(scenario["userRequest"], typedefs)
        print("Scores for prompt: {}".format(scenario["userRequest"]))
        for typedef_with_score in typedefs_with_scores:
            print(f"{typedef_with_score['typedef_info']['flattened_type']} - {typedef_with_score['score']}")


if __name__ == "__main__":
    test_filtering()
    # eval_examples()
