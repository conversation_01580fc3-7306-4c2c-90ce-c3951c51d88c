16k tokens
Result for prompt: Last cell from the excel range
Expected: rangeInfo.EndCell. 
Actual: rangeInfo.EndCell



Result for prompt: One week from the end of the first event in the list
Expected: eventList.First().EndDate.AddDays(7).ToString(). 
Actual: eventList.First().End.AddDays(7).ToString()



Result for prompt: Date of background check of prehire
Expected: preHire(0).Background_Check_Data(0).Status_Date.ToString().
Actual: preHire[0].Background_Check_Data[0].Status_Date.ToString()



Result for prompt: email adress for the reporter of the jira issue
Expected: jiraIssue.Fields.Reporter.EmailAddress. 
Actual: jiraIssue.Fields.Reporter.EmailAddress



Result for prompt: Concat the subject from the received emails list
Expected: String.Join(", ", emailList.Select(Function(email) email.Subject)). 
Actual: String.Join(", ", emailList.Select(Function(e) e.Subject))   



Forced: 3.2k tokens


Trimmed typedefs. Duration: 7.34726619720459
Result for prompt: Last cell from the excel range
Expected: rangeInfo.EndCell. Actual: rangeInfo.EndCell



Trimmed typedefs. Duration: 1.473409652709961
Result for prompt: One week from the end of the first event in the list
Expected: eventList.First().EndDate.AddDays(7).ToString(). Actual: eventList.First().End.AddDays(7).ToString()



Trimmed typedefs. Duration: 1.4508130550384521
Result for prompt: Date of background check of prehire
Expected: preHire(0).Background_Check_Data(0).Status_Date.ToString(). Actual: preHire(0).Background_Check_Data(0).Status_Date.ToString()



Trimmed typedefs. Duration: 1.438835859298706
Result for prompt: email adress for the reporter of the jira issue
Expected: jiraIssue.Fields.Reporter.EmailAddress. Actual: jiraIssue.Reporter.EmailAddress



Trimmed typedefs. Duration: 1.5423848628997803
Result for prompt: Concat the subject from the received emails list
Expected: String.Join(", ", emailList.Select(Function(email) email.Subject)). Actual: String.Join(", ", receivedEmails.Select(Function(email) email.Subject))