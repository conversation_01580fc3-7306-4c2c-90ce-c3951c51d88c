# flake8: noqa

prompts = [
    "one week from the event end date",
    "get me the document file name",
    # "validate that current email has a valid sender email address",
    "get the latest file from slack",
    "format the title and comments from the issue",
    "created date from the classified document",
    "file name received from slack",
]

example_request_long_type = {
    "currentExpression": "",
    "userRequest": "email subject to lowercase",
    "allVariablesIncluded": True,
    "availableVariables": [
        {
            "name": "email",
            "type": "UiPath.MicrosoftOffice365.Models.Office365Message, UiPath.MicrosoftOffice365, Version=*******, Culture=neutral, PublicKeyToken=null",
        },
        {
            "name": "response",
            "type": "UiPath.Slack.IntegrationService.Client.GetFilesInfoResponse, UiPath.Slack.IntegrationService, Version=*******, Culture=neutral, PublicKeyToken=null",
        },
    ],
    "expressionTypeDefinition": "String",
    "expressionLanguage": "vbnet",
    "additionalTypeDefinitions": "namespace UiPath.MicrosoftOffice365.Models { class Office365Message { string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; Importance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; System.Net.Mail.MailPriority Priority; IEnumerable<string> Categories; string InternetMessageId; System.Net.Mail.AttachmentCollection Attachments; string Body; string BodyAsHtml; string Subject; string FromDisplayName; string FromAddress; string SenderDisplayName; string SenderAddress; System.Net.Mail.MailAddressCollection ReplyToList; IEnumerable<string> ReplyToAddressList; System.Net.Mail.MailAddressCollection To; IEnumerable<string> ToAddressList; System.Net.Mail.MailAddressCollection CC; IEnumerable<string> CCAddressList; System.Net.Mail.MailAddressCollection Bcc; IEnumerable<string> BccAddressList; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; int AttachmentCount; IEnumerable<string> AttachmentsNamesList; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AlternateViewCollection AlternateViews; } } namespace UiPath.Slack.IntegrationService.Client { class GetFilesInfoResponse { ICollection<string> Channels; int? Comments_count; int? Created; int? Date_delete; bool? Display_as_bot; bool? Editable; string Editor; string External_id; string External_type; string External_url; string Filetype; ICollection<string> Groups; bool? Has_rich_preview; string Id; int? Image_exif_rotation; ICollection<string> Ims; bool? Is_external; bool? Is_public; bool? Is_starred; bool? Is_tombstoned; string Mimetype; string Mode; string Name; bool? Non_owner_editable; int? Num_stars; int? Original_h; int? Original_w; string Permalink; string Permalink_public; ICollection<string> Pinned_to; string Pretty_type; string Preview; bool? Public_url_shared; UiPath.Slack.IntegrationService.Client.GetFilesInfoResponseReactions Reactions; int? Size; string Source_team; string State; string Thumb_1024; int? Thumb_1024_h; int? Thumb_1024_w; string Thumb_160; string Thumb_360; int? Thumb_360_h; int? Thumb_360_w; string Thumb_480; int? Thumb_480_h; int? Thumb_480_w; string Thumb_64; string Thumb_720; int? Thumb_720_h; int? Thumb_720_w; string Thumb_80; string Thumb_800; int? Thumb_800_h; int? Thumb_800_w; string Thumb_960; int? Thumb_960_h; int? Thumb_960_w; string Thumb_tiny; int? Timestamp; string Title; int? Updated; string Url_private; string Url_private_download; string User; string Username; IDictionary<string, object> AdditionalProperties; } }",
}

example_request = {
    "userRequest": "Do something with_out_ClassifyDocument_1__ClassificationResultsCurrentEmail_out_ConnectorActivity_1__Jit_files_info_GET_out_GetIssue_1__Response_out_NApplicationCard_1__OutUiElementcountercountersmyEmail",
    "availableVariables": [
        {"name": "_out_ClassifyDocument_1__ClassificationResults", "type": "UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.DocumentData"},
        {"name": "CurrentEmail", "type": "UiPath.MicrosoftOffice365.Models.Office365Message"},
        {
            "name": "_out_ConnectorActivity_1__Jit_files_info_GET",
            "type": "UiPath.IntegrationService.Activities.SWEntities.C6773929583_files_info_GET_Retrieve.Bundle.files_info_GET_Retrieve",
        },
        {"name": "_out_GetIssue_1__Response", "type": "UiPath.Jira.IntegrationService.Client.Curated_issue"},
        {"name": "_out_NApplicationCard_1__OutUiElement", "type": "UiPath.Core.UiElement"},
        {"name": "counter", "type": "System.Int32"},
        {"name": "counters", "type": "System.Int32[]"},
        {"name": "myEmail", "type": "System.String"},
    ],
    "expressionTypeDefinition": "String",
    "expressionLanguage": "vbnet",
    "additionalTypeDefinitions": "namespace UiPath.IntegrationService.Activities.SWEntities.C6773929583_files_info_GET_Retrieve.Bundle { class files_info_GET_Retrieve { List<string> channels; int? comments_count; int? created; int? date_delete; bool? display_as_bot; bool? editable; string editor; string external_id; string external_type; string external_url; string filetype; List<string> groups; bool? has_rich_preview; string id; int? image_exif_rotation; List<string> ims; bool? is_external; bool? is_public; bool? is_starred; bool? is_tombstoned; string mimetype; string mode; string name; bool? non_owner_editable; int? num_stars; int? original_h; int? original_w; string permalink; string permalink_public; List<string> pinned_to; string pretty_type; string preview; bool? public_url_shared; int? size; string source_team; string state; string thumb_1024; int? thumb_1024_h; int? thumb_1024_w; string thumb_160; string thumb_360; int? thumb_360_h; int? thumb_360_w; string thumb_480; int? thumb_480_h; int? thumb_480_w; string thumb_64; string thumb_720; int? thumb_720_h; int? thumb_720_w; string thumb_80; string thumb_800; int? thumb_800_h; int? thumb_800_w; string thumb_960; int? thumb_960_h; int? thumb_960_w; string thumb_tiny; int? timestamp; string title; int? updated; string url_private; string url_private_download; string user; string username; List<reactions> reactions; } class reactions { int? count; string name; } } namespace UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification { class DocumentData { string MimeType; string IconUri; string FullName; string ID; bool IsFolder; DateTime? CreationDate; DateTime? LastModifiedDate; Dictionary<string, string> Metadata; bool IsResolved; string LocalPath; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.DocumentType DocumentType; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.IDocumentData[] SubDocuments; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.FileDetails FileDetails; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.DocumentMetadata DocumentMetadata; string GetExtension(); string GetName(); long GetSizeInBytes(); } class DocumentType { string Name; float Confidence; string Url; } interface IDocumentData { UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.DocumentType DocumentType; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.IDocumentData[] SubDocuments; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.FileDetails FileDetails; UiPath.IntelligentOCR.StudioWeb.Activities.DocumentClassification.DocumentMetadata DocumentMetadata; } class FileDetails { string LocalPath; string FullName; string Extension; UiPath.DocumentProcessing.Contracts.Results.ResultsDocumentBounds PageRange; } class DocumentMetadata { string Text; UiPath.DocumentProcessing.Contracts.Dom.Document DocumentObjectModel; string Language; float SplitConfidence; List<DataTable> ResultsAsDataTables; } } namespace UiPath.Jira.IntegrationService.Client { class Curated_issue { UiPath.Jira.IntegrationService.Client.Curated_issueChangelog Changelog; UiPath.Jira.IntegrationService.Client.Curated_issueEditmeta Editmeta; string Expand; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsToInclude FieldsToInclude; UiPath.Jira.IntegrationService.Client.Curated_issueUpdate Update; UiPath.Jira.IntegrationService.Client.Curated_issueFields Fields; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadata HistoryMetadata; string Id; string Key; UiPath.Jira.IntegrationService.Client.Curated_issueOperations Operations; UiPath.Jira.IntegrationService.Client.Curated_issueProperties Properties; UiPath.Jira.IntegrationService.Client.Curated_issueSchema Schema; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueTransition Transition; UiPath.Jira.IntegrationService.Client.Curated_issueTransitions Transitions; IDictionary<string, object> AdditionalProperties; } class Curated_issueChangelog { UiPath.Jira.IntegrationService.Client.Curated_issueChangelogHistories Histories; int? MaxResults; int? StartAt; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueEditmeta { UiPath.Jira.IntegrationService.Client.Curated_issueEditmetaFields Fields; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsToInclude { ICollection<string> ActuallyIncluded; ICollection<string> Excluded; ICollection<string> Included; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdate { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelink Issuelink; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinks Issuelinks; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateComment Comment; IDictionary<string, object> AdditionalProperties; } class Curated_issueFields { UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAggregateprogress Aggregateprogress; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAssignee Assignee; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsComponents Components; DateTimeOffset? Created; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsCreator Creator; string Description; DateTimeOffset? Duedate; string Environment; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsFixVersions FixVersions; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsIssuetype Issuetype; ICollection<string> Labels; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsParent Parent; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsPriority Priority; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProgress Progress; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProject Project; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsReporter Reporter; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsSecurity Security; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsStatus Status; DateTimeOffset? Statuscategorychangedate; string Summary; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsTimetracking Timetracking; DateTimeOffset? Updated; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsVersions Versions; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsVotes Votes; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsWatches Watches; int? Workratio; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadata { string ActivityDescription; string ActivityDescriptionKey; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataActor Actor; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataCause Cause; string Description; string DescriptionKey; string EmailDescription; string EmailDescriptionKey; UiPath.Jira.IntegrationService.Client.Curated_issueHistoryMetadataGenerator Generator; string Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueOperations { UiPath.Jira.IntegrationService.Client.Curated_issueOperationsLinkGroups LinkGroups; IDictionary<string, object> AdditionalProperties; } class Curated_issueSchema { string Custom; long? CustomId; string Items; string System; string Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransition { UiPath.Jira.IntegrationService.Client.Curated_issueTransitionErrorCollection ErrorCollection; string Expand; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionFields Fields; bool? HasScreen; string Id; bool? IsAvailable; bool? IsConditional; bool? IsGlobal; bool? IsInitial; bool? Looped; string Name; int? Status; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionTo To; IDictionary<string, object> AdditionalProperties; } class Curated_issueEditmetaFields { ICollection<string> AllowedValues; string AutoCompleteUrl; string DefaultValue; bool? HasDefaultValue; string Key; string Name; ICollection<string> Operations; bool? Required; UiPath.Jira.IntegrationService.Client.Curated_issueEditmetaFieldsSchema Schema; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateIssuelink { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinkOutwardIssue OutwardIssue; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinkType Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateIssuelinks { UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinksOutwardIssue OutwardIssue; UiPath.Jira.IntegrationService.Client.Curated_issueUpdateIssuelinksType Type; IDictionary<string, object> AdditionalProperties; } class Curated_issueUpdateComment { string Body; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsAggregateprogress { int? Progress; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsAssignee { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsAssigneeAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Id; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsCreator { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsCreatorAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsIssuetype { int? AvatarId; string Description; string EntityId; int? HierarchyLevel; string IconUrl; string Id; string Name; string Self; bool? Subtask; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsParent { string Key; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsPriority { string IconUrl; string Id; string Name; string Self; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsProgress { int? Progress; int? Total; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsProject { UiPath.Jira.IntegrationService.Client.Curated_issueFieldsProjectAvatarUrls AvatarUrls; string Id; string Key; string Name; string ProjectTypeKey; string Self; bool? Simplified; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsReporter { string AccountId; string AccountType; bool? Active; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsReporterAvatarUrls AvatarUrls; string DisplayName; string EmailAddress; string Id; string Self; string TimeZone; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsSecurity { string Id; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsStatus { string Description; string IconUrl; string Id; string Name; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueFieldsStatusStatusCategory StatusCategory; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsTimetracking { string OriginalEstimate; string RemainingEstimate; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsVotes { bool? HasVoted; string Self; int? Votes; IDictionary<string, object> AdditionalProperties; } class Curated_issueFieldsWatches { bool? IsWatching; string Self; int? WatchCount; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataActor { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataCause { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueHistoryMetadataGenerator { string AvatarUrl; string DisplayName; string DisplayNameKey; string Id; string Type; string Url; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionErrorCollection { ICollection<string> ErrorMessages; int? Status; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionFields { ICollection<string> AllowedValues; string AutoCompleteUrl; string DefaultValue; bool? HasDefaultValue; string Key; string Name; ICollection<string> Operations; bool? Required; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionFieldsSchema Schema; IDictionary<string, object> AdditionalProperties; } class Curated_issueTransitionTo { string Description; string IconUrl; string Id; string Name; string Self; UiPath.Jira.IntegrationService.Client.Curated_issueTransitionToStatusCategory StatusCategory; IDictionary<string, object> AdditionalProperties; } } namespace UiPath.DocumentProcessing.Contracts.Results { class ResultsDocumentBounds { int StartPage; int PageCount; int TextStartIndex; int TextLength; } } namespace UiPath.DocumentProcessing.Contracts.Dom { class Document { string DocumentId; string ContentType; int Length; UiPath.DocumentProcessing.Contracts.Dom.Page[] Pages; List<Metadata> DocumentMetadata; } }",
}
