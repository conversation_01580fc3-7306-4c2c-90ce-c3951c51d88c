examples = [
    {
        "currentExpression": 'if (length($myList) == 0) then "Empty" end',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "error: length/1 is not defined at <top-level>, line 1:",
        "availableVariables": [{"name": "myList", "type": "array"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'if ($myList | length == 0) then "Empty" end',
    },
    {
        "currentExpression": "[range(1; 6) | map(. * .)]",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <unknown>): Cannot iterate over number (1)",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "[range(1; 6)] | map(. * .) | tostring",
    },
    {
        "currentExpression": "1^2 + 2^2 + 3^2 + 4^2 + 5^2",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "jq: error: syntax error, unexpected INVALID_CHARACTER, expecting end of file (Unix shell quoting issues?) at <top-level>, line 1:1^2 jq: 1 compile error",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "[range(1; 6)] | map(. * .) | tostring",
    },
    {
        "currentExpression": "($myList | select(max(sin))  // null) | tostring",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "jq: error: max/1 is not defined at <top-level>, line 1: [1,2,3] as $myList | ($myList | select(max(sin))  // null) | tostring jq: 1 compile error",
        "availableVariables": [
            {
                "name": "myList",
                "type": "array",
            }
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "($myList | max_by(sin) // null) | tostring",
    },
    {
        "currentExpression": '[range(97; 123)] | map(chr) | join("")',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": 'jq: error: chr/1 is not defined at <top-level>, line 1: [range(97; 123)] | map(chr) | join("") jq: 1 compile error',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '[[range(97; 123)] | implode] | join("")',
    },
    {
        "currentExpression": 'now | format("yyyy-MM-dd")',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": 'jq: error: syntax error, unexpected INVALID_CHARACTER (Unix shell quoting issues?) at <top-level>, line 1:now | format("yyyy-MM-dd") jq: 1 compile error',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'now | strftime("%Y-%m-%d")',
    },
    {
        "currentExpression": "true || false",
        "expressionTypeDefinition": "boolean",
        "allVariablesIncluded": True,
        "currentError": "jq: error: syntax error, unexpected '|' (Unix shell quoting issues?) at <top-level>, line 1: true || false jq: 1 compile error",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "true or false",
    },
    {
        "currentExpression": "$myList | avg | tostring",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "jq: error: avg/0 is not defined at <top-level>, | $myList | avg jq: 1 compile error",
        "availableVariables": [
            {
                "name": "myList",
                "type": "List<Int>",
            }
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "$myList | add/length | tostring",
    },
    {
        "currentExpression": "$myString[::-1]",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "jq: error: syntax error, unexpected ':' (Unix shell quoting issues?) at <top-level>, line 1: $myString[::-1] jq: 1 compile error",
        "availableVariables": [
            {
                "name": "myString",
                "type": "string",
            }
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "$myString | explode | reverse | implode",
    },
    {
        "currentExpression": "reduce $myList as $x (1; . * $x)",
        "expressionTypeDefinition": "number",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <unknown>): number (0) and array ([1,2,3]) cannot be added",
        "availableVariables": [
            {
                "name": "myList",
                "type": "array",
            }
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "reduce $myList.[] as $x (1; . * $x)",
    },
    {
        "currentExpression": "input | keys | select(length <= 3)",
        "expressionTypeDefinition": "array",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <stdin>:1): break",
        "availableVariables": [
            {
                "name": "input",
                "type": "interface Input {key1 : number}",
            }
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "input | keys | select(length <= 3)",
    },
    {
        "currentExpression": '$input | { "result": (.str | split(" ") | select(endswith($input.suff))) }',
        "expressionTypeDefinition": "interface Output { result: string[] }",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <unknown>): endswith() requires string inputs",
        "availableVariables": [{"name": "input", "type": "interface Input { str: string, suff: string }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "result": (.str | split(" ") | map(select(endswith($input.suff)))) }',
    },
    {
        "currentExpression": '$input | { "res": (floor(.a / .b)) }',
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": "jq: error: floor/1 is not defined at <top-level>, line 1:",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "res": ((.a / .b) | floor) }',
    },
    {
        "currentExpression": '$input | { "res": (max | .[-2]) }',
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": 'jq: error (at <unknown>): object ({"list":[1,...) and object ({"list":[1,...) cannot be iterated over',
        "availableVariables": [{"name": "input", "type": "interface Input { list: number[] }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "res": ($input.list | sort | reverse | .[1]) }',
    },
    {
        "currentExpression": '$input | { "intersect": check(.x1 <= .x2 and .y1 >= .y2) }',
        "expressionTypeDefinition": "interface Output { intersect: boolean }",
        "allVariablesIncluded": True,
        "currentError": "jq: error: check/1 is not defined at <top-level>, line 1:",
        "availableVariables": [{"name": "input", "type": "interface Input { x1: number, y1: number, x2: number, y2: number }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "intersect": (.x1 <= .y2 and .x2 <= .y1) }',
    },
    {
        "currentExpression": '$input | { "res": (.list1 + .list2 | string) }',
        "expressionTypeDefinition": "interface Output { res: string }",
        "allVariablesIncluded": True,
        "currentError": "jq: error: string/0 is not defined at <top-level>, line 1:",
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "res": ((.list1 + .list2) | map(tostring) | join("")) }',
    },
    {
        "currentExpression": '$input | { "result": (.dict.keys | match($input.str) | tostring) }',
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <unknown>): null (null) cannot be matched, as it is not a string",
        "availableVariables": [{"name": "input", "type": "interface Input { dict: UnstructuredJSON, str: string }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "result": (.dict | has($input.str) | tostring) }',
    },
    {
        "currentExpression": '$input | { "result": (2 * (.a + .b + .c) > max(.a,.b,.c) | tostring) }',
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "jq: error: max/1 is not defined at <top-level>, line 1:",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number, c: number }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '$input | { "result": ((.a + .b > .c and .a + .c > .b and .b + .c > .a) | tostring) }',
    },
    {
        "currentExpression": '$input | { "result": (.users | select(.active == true) | length | tostring) }',
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "jq: error (at <unknown>): Cannot index array with string active",
        "availableVariables": [{"name": "input", "type": "interface Input { users: User[] }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "declare namespace User {\n interface User {\n name: string,\n active: boolean \n} \n}",
        "benchmarkExpression": '$input | { "result": (.users | map(select(.active == true)) | length | tostring) }',
    },
    {
        "currentExpression": '$input | { "result": (.word == (.word | reverse)) }',
        "expressionTypeDefinition": "interface Output { result: boolean }",
        "allVariablesIncluded": True,
        "currentError": 'jq: error (at <unknown>): Cannot index array with string "word"',
        "availableVariables": [{"name": "input", "type": "interface Input { word: string }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '{"result": (((.word) | explode | reverse | implode)==.word)}',
    },
    {
        "currentExpression": '$input | { "result": (.items | sort(price) | .[-3:]) }',
        "expressionTypeDefinition": "interface Output { result: Item[] }",
        "allVariablesIncluded": True,
        "currentError": "jq: error: sort/1 is not defined at <top-level>, line 9:",
        "availableVariables": [{"name": "input", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "jq",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "declare namespace Item {\n interface Item {\n name: string,\n price: number \n} \n}",
        "benchmarkExpression": '$input | { "result": (.items | sort_by(.price) | reverse | .[:3]) }',
    },
]
