examples = [
    {
        "currentExpression": "const squares = Array.from({ 5 }, (i) => (i + 1) ** 2);\nreturn squares;",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected number at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const squares = Array.from({ length: 5 }, (_, i) => (i + 1) ** 2);\nreturn squares;",
    },
    {
        "currentExpression": "const result = 1^^2 + 2^^2 + 3^^2 + 4^^2 + 5^^2;\nreturn result;",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '^' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const squares = Array.from({length: 5}, (_, i) => (i + 1) ** 2);\nconst result = squares.toString();\nreturn result;",
    },
    {
        "currentExpression": 'if (!myList.length) {\n  return "null";\n}\n\nconst maxElement = myList.max_by(x => Math.sin(x));\nconst result = maxElement.toString();\nreturn result;',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.max_by is not a function at Object.<anonymous> (/tmp/umubDj5Xe5/main.js:6:39) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10)at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myList",
                "type": "number[]",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'if (!myList.length) {\n  return "null";\n}\n\nconst maxElement = myList.reduce((a, b) => Math.sin(b) > Math.sin(a) ? b : a);\nconst result = maxElement.toString();\nreturn result;',
    },
    {
        "currentExpression": 'const chars = Array.from({ length: 26 }, (_, i) => String.fromCharcodes(97 + i));\nconst result = chars.join("");\nreturn result;',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: String.fromCharcodes is not a function at /tmp/4KoHHDv907/main.js:3:60 at Function.from (<anonymous>) at Object.<anonymous> (/tmp/4KoHHDv907/main.js:3:22) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const charCodes = Array.from({length: 26}, (_, i) => i + 97);\nconst result = String.fromCharCode(...charCodes);\nreturn result;",
    },
    {
        "currentExpression": "const date = new Date();\nconst datePart = date.split('T')[0];\nreturn datePart;",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: (intermediate value).split is not a functionat Object.<anonymous> (/tmp/e4qjU1OQmv/main.js:1:24)at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const dateString = new Date().toISOString();\nconst datePart = dateString.split('T')[0];\nreturn datePart;",
    },
    {
        "currentExpression": "const result = eval('true || false');\nreturn result;",
        "expressionTypeDefinition": "boolean",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected identifier 'or' at Object.<anonymous> (/tmp/qd42kOR3wg/main.js:1:13) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'const result = eval("true || false");\nreturn result;',
    },
    {
        "currentExpression": "const average = myList.average();\nconst result = average.toString();\nreturn result;",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.average is not a function at Object.<anonymous> (/tmp/iVqM8QIFXZ/main.js:5:24) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "myList", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const sum = myList.reduce((a, b) => a + b, 0);\nconst average = sum / myList.length;\nconst result = average.toString();\nreturn result;",
    },
    {
        "currentExpression": "const result = myString[::-1];\nreturn result;",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token ':' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myString",
                "type": "string",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const chars = myString.split('');\nconst reversedChars = chars.reverse();\nconst result = reversedChars.join('');\nreturn result;",
    },
    {
        "currentExpression": "const product = myList.prod();\nreturn product;",
        "expressionTypeDefinition": "number",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.prod is not a function at Object.<anonymous> (/tmp/PZ4LZnnMrW/main.js:5:23) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myList",
                "type": "number[]",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const product = myList.reduce((acc, x) => acc * x, 1);\nreturn product;",
    },
    {
        "currentExpression": "const keys = Object.keys(input);\nconst shortKeys = keys.select(key => key.length <= 3);\nreturn shortKeys;",
        "expressionTypeDefinition": "any[]",
        "allVariablesIncluded": True,
        "currentError": "TypeError: Object.keys(...).select is not a function at Object.<anonymous> (/tmp/YYWJLEgWZF/main.js:11:35) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "input",
                "type": "interface Input {key1 : number}",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const keys = Object.keys(input);\nconst shortKeys = keys.filter(key => key.length <= 3);\nreturn shortKeys;",
    },
    {
        "currentExpression": "const words = input.str.split(' ');\nconst filteredWords = words.filter(endsWith(input.suff));\nconst result = { result: filteredWords };\nreturn result;",
        "expressionTypeDefinition": "interface Output { result: string[] }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: endsWith is not defined at Object.<anonymous> (/tmp/LXp9UfXLAz/main.js:8:46) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { str: string, suff: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const words = input.str.split(' ');\nconst filteredWords = words.filter(word => word.endsWith(input.suff));\nconst result = { result: filteredWords };\nreturn result;",
    },
    {
        "currentExpression": "const division = input.a / input.b;\nconst floorValue = floor(division);\nconst result = { res: floorValue };\nreturn result;",
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: floor is not defined at Object.<anonymous> (/tmp/yfqgYo97ks/main.js:5:23) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const division = input.a / input.b;\nconst floorValue = Math.floor(division);\nconst result = { res: floorValue };\nreturn result;",
    },
    {
        "currentExpression": "const sortedList = input.list.sort();\nconst secondMax = sortedList.take(-1);\nconst result = { res: secondMax };\nreturn result;",
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.list.sort(...).take is not a function at Object.<anonymous> (/tmp/Olz2Swhj4R/main.js:7:41) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { list: number[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const sortedList = input.list.slice().sort((a, b) => b - a);\nconst secondMax = sortedList[1];\nconst result = { res: secondMax };\nreturn result;",
    },
    {
        "currentExpression": "const condition1 = input.x1 <= input.y2;\nconst condition2 = input.x2 <= input.y1;\nconst doIntersect = condition1, condition2;\nconst result = { intersect: doIntersect };\nreturn result;",
        "expressionTypeDefinition": "interface Output { intersect: boolean }",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '.' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { x1: number, y1: number, x2: number, y2: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const condition1 = input.x1 <= input.y2;\nconst condition2 = input.x2 <= input.y1;\nconst doIntersect = condition1 && condition2;\nconst result = { intersect: doIntersect };\nreturn result;",
    },
    {
        "currentExpression": "const combinedList = [[_ in input.list1], [_ in input.list2]];\nconst stringValues = combinedList.map(String);\nconst joinedString = stringValues.join('');\nconst result = { res: joinedString };\nreturn result;",
        "expressionTypeDefinition": "interface Output { res: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: _ is not defined at Object.<anonymous> (/tmp/a9OZMDMpNb/main.js:8:25) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const combinedList = [...input.list1, ...input.list2];\nconst stringValues = combinedList.map(String);\nconst joinedString = stringValues.join('');\nconst result = { res: joinedString };\nreturn result;",
    },
    {
        "currentExpression": "const isKey = input.str in input.dict.keys();\nconst result = { result: isKey.toString() };\nreturn result;",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.dict.keys is not a function at Object.<anonymous> (/tmp/27e46n1yQW/main.js:8:51) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { dict: any, str: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "({ result: (input.str in input.dict).toString() })",
    },
    {
        "currentExpression": "const result = (input.a + input.b + input.c > 2 * max(input.a + input.b + input.c)).toString();\nreturn { result };",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: max is not defined at Object.<anonymous> (/tmp/05GyWKHEX6/main.js:10:30) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number, c: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const condition1 = input.a + input.b > input.c;\nconst condition2 = input.a + input.c > input.b;\nconst condition3 = input.b + input.c > input.a;\nconst isTriangle = condition1 && condition2 && condition3;\nconst result = isTriangle.toString();\nreturn { result };",
    },
    {
        "currentExpression": "const activeCount = input.users.keep(user.active).length;\nconst result = activeCount.toString();\nreturn { result };",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: user is not defined at Object.<anonymous> (/tmp/7ne1HK6w4i/main.js:11:43) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { users: User[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "interface User { name: string, active: boolean }",
        "benchmarkExpression": "const activeUsers = input.users.filter(user => user.active);\nconst activeCount = activeUsers.length;\nconst result = activeCount.toString();\nreturn { result };",
    },
    {
        "currentExpression": "const isPalindrome = input.word === input.reverse();\nconst result = isPalindrome;\nreturn { result };",
        "expressionTypeDefinition": "interface Output { result: boolean }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.reverse is not a function at Object.<anonymous> (/tmp/D1LoVDxaY4/main.js:5:47) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { word: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "const reversed = input.word.split('').reverse().join('');\nconst isPalindrome = input.word === reversed;\nconst result = isPalindrome;\nreturn { result };",
    },
    {
        "currentExpression": "const topItems = input.items.sort_by(.price).reverse().slice(0, 3);\nconst result = topItems;\nreturn { result };",
        "expressionTypeDefinition": "interface Output { result: Item[] }",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '.' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "JsInvoke",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "interface Item { name: string, price: number }",
        "benchmarkExpression": "const sortedItems = input.items.slice().sort((a, b) => b.price - a.price);\nconst topItems = sortedItems.slice(0, 3);\nconst result = topItems;\nreturn { result };",
    },
]
