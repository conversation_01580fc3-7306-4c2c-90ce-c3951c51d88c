examples = [
    {
        "currentExpression": "Array.from({ 5 }, (i) => (i + 1) ** 2)",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected number at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "Array.from({ length: 5 }, (_, i) => (i + 1) ** 2).toString()",
    },
    {
        "currentExpression": "1^^2 + 2^^2 + 3^^2 + 4^^2 + 5^^2",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '^' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "Array.from({length: 5}, (_, i) => (i + 1) ** 2).toString()",
    },
    {
        "currentExpression": '!myList.length ? "null" : myList.max_by(x => Math.sin(x)).toString()',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.max_by is not a function at Object.<anonymous> (/tmp/umubDj5Xe5/main.js:6:39) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10)at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myList",
                "type": "number[]",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '!myList.length ? "null" : myList.reduce((a, b) => Math.sin(b) > Math.sin(a) ? b : a).toString()',
    },
    {
        "currentExpression": 'Array.from({ length: 26 }, (_, i) => String.fromCharcodes(97 + i)).join("")',
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: String.fromCharcodes is not a function at /tmp/4KoHHDv907/main.js:3:60 at Function.from (<anonymous>) at Object.<anonymous> (/tmp/4KoHHDv907/main.js:3:22) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "String.fromCharCode(...Array.from({length: 26}, (_, i) => i + 97))",
    },
    {
        "currentExpression": "new Date().split('T')[0]",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: (intermediate value).split is not a functionat Object.<anonymous> (/tmp/e4qjU1OQmv/main.js:1:24)at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "new Date().toISOString().split('T')[0]",
    },
    {
        "currentExpression": "eval('true or false')",
        "expressionTypeDefinition": "boolean",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected identifier 'or' at Object.<anonymous> (/tmp/qd42kOR3wg/main.js:1:13) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'Boolean(eval("true || false"))',
    },
    {
        "currentExpression": "myList.average().toString()",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.average is not a function at Object.<anonymous> (/tmp/iVqM8QIFXZ/main.js:5:24) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "myList", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(myList.reduce((a, b) => a + b, 0) / myList.length).toString()",
    },
    {
        "currentExpression": "myString[::-1]",
        "expressionTypeDefinition": "string",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token ':' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myString",
                "type": "string",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "myString.split('').reverse().join('')",
    },
    {
        "currentExpression": "myList.prod()",
        "expressionTypeDefinition": "number",
        "allVariablesIncluded": True,
        "currentError": "TypeError: myList.prod is not a function at Object.<anonymous> (/tmp/PZ4LZnnMrW/main.js:5:23) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "myList",
                "type": "number[]",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "myList.reduce((acc, x) => acc * x, 1)",
    },
    {
        "currentExpression": "Object.keys(input).select(key => key.length <= 3)",
        "expressionTypeDefinition": "any[]",
        "allVariablesIncluded": True,
        "currentError": "TypeError: Object.keys(...).select is not a function at Object.<anonymous> (/tmp/YYWJLEgWZF/main.js:11:35) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [
            {
                "name": "input",
                "type": "interface Input {key1 : number}",
            }
        ],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "Object.keys(input).filter(key => key.length <= 3)",
    },
    {
        "currentExpression": "{ result: input.str.split(' ').filter(endsWith(input.suff)) }",
        "expressionTypeDefinition": "interface Output { result: string[] }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: endsWith is not defined at Object.<anonymous> (/tmp/LXp9UfXLAz/main.js:8:46) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { str: string, suff: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ result: input.str.split(' ').filter(word => word.endsWith(input.suff)) }",
    },
    {
        "currentExpression": "{ res: floor(input.a / input.b) }",
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: floor is not defined at Object.<anonymous> (/tmp/yfqgYo97ks/main.js:5:23) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ res: Math.floor(input.a / input.b) }",
    },
    {
        "currentExpression": "{ res: input.list.sort().take(-1) }",
        "expressionTypeDefinition": "interface Output { res: number }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.list.sort(...).take is not a function at Object.<anonymous> (/tmp/Olz2Swhj4R/main.js:7:41) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { list: number[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ res: input.list.slice().sort((a, b) => b - a)[1] }",
    },
    {
        "currentExpression": "{ intersect: ((x1, y1, x2, y2) => x1 <= y2, x2 <= y1)(input.x1, input.y1, input.x2, input.y2) }",
        "expressionTypeDefinition": "interface Output { intersect: boolean }",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '.' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { x1: number, y1: number, x2: number, y2: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ intersect: input.x1 <= input.y2 && input.x2 <= input.y1 }",
    },
    {
        "currentExpression": "{ res: [[_ in input.list1], [_ in input.list2]].map(String).join('') }",
        "expressionTypeDefinition": "interface Output { res: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: _ is not defined at Object.<anonymous> (/tmp/a9OZMDMpNb/main.js:8:25) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ res: [...input.list1, ...input.list2].map(String).join('') }",
    },
    {
        "currentExpression": "{ result: input.str in input.dict.keys() }",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.dict.keys is not a function at Object.<anonymous> (/tmp/27e46n1yQW/main.js:8:51) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { dict: any, str: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ result: (input.str in input.dict).toString() }",
    },
    {
        "currentExpression": "{ result: (input.a + input.b + input.c > 2 * max(input.a + input.b + input.c)).toString() }",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: max is not defined at Object.<anonymous> (/tmp/05GyWKHEX6/main.js:10:30) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number, c: number }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ result: (input.a + input.b > input.c && input.a + input.c > input.b && input.b + input.c > input.a).toString() }",
    },
    {
        "currentExpression": "{ result: input.users.keep(user.active).length.toString() }",
        "expressionTypeDefinition": "interface Output { result: string }",
        "allVariablesIncluded": True,
        "currentError": "ReferenceError: user is not defined at Object.<anonymous> (/tmp/7ne1HK6w4i/main.js:11:43) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { users: User[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "declare namespace User {\n interface User {\n name: string,\n active: boolean \n} \n}",
        "benchmarkExpression": "const activeUsers = input.users.filter(user => user.active);\nconst activeCount = activeUsers.length;\nconst result = activeCount.toString();\nreturn { result };",
    },
    {
        "currentExpression": "{ result: input.word === input.reverse() }",
        "expressionTypeDefinition": "interface Output { result: boolean }",
        "allVariablesIncluded": True,
        "currentError": "TypeError: input.reverse is not a function at Object.<anonymous> (/tmp/D1LoVDxaY4/main.js:5:47) at Module._compile (node:internal/modules/cjs/loader:1554:14) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { word: string }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "{ result: input.word === input.word.split('').reverse().join('') }",
    },
    {
        "currentExpression": "{ result: input.items.sort_by(.price).reverse().slice(0, 3) }",
        "expressionTypeDefinition": "interface Output { result: Item[] }",
        "allVariablesIncluded": True,
        "currentError": "SyntaxError: Unexpected token '.' at wrapSafe (node:internal/modules/cjs/loader:1486:18) at Module._compile (node:internal/modules/cjs/loader:1528:20) at Object..js (node:internal/modules/cjs/loader:1706:10) at Module.load (node:internal/modules/cjs/loader:1289:32) at Function._load (node:internal/modules/cjs/loader:1108:12) at TracingChannel.traceSync (node:diagnostics_channel:322:14) at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5) at node:internal/main/run_main_module:36:49 Node.js v22.14.0",
        "availableVariables": [{"name": "input", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "javascript",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "declare namespace Item {\n interface Item {\n name: string,\n price: number \n} \n}",
        "benchmarkExpression": "const sortedItems = input.items.slice().sort((a, b) => b.price - a.price);\nconst topItems = sortedItems.slice(0, 3);\nconst result = topItems;\nreturn { result };",
    },
]
