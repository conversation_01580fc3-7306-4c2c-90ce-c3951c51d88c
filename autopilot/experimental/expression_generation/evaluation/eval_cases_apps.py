# flake8: noqa

app_examples = [
    {
        "currentExpression": "",
        "userRequest": "get review comments concatenated with user email and user display name",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "NewPage.ReviewComments", "type": "Controls.TextAreaVb"},
            {"name": "NewPage", "type": "NewPage"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace UserContext.User { class CurrentUser { System.String DisplayName; System.String FirstName; System.String LastName; System.String Email; } } namespace Controls { class TextAreaVb { System.String HintText; System.String Tooltip; System.String Label; System.Int32 MinLength; System.Int32 MaxLength; System.String Value; System.Boolean Required; System.String RequiredErrorMessage; System.Boolean Hidden; System.Boolean Disabled; System.Boolean IsValid; System.String Id; System.String Type;  }  } class NewPage { System.String Tooltip;  }",
        "benchmarkExpression": "NewPage.ReviewComments.Value + NewPage.CurrentUser.Email + UserContext.User.CurrentUser.DisplayName",
    },
    {
        "currentExpression": "",
        "userRequest": "get rule error message from add record",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "MainPage.AddRecord", "type": "CustomControls.AddRecord"},
            {"name": "MainPage.Label", "type": "Controls.Label"},
            {"name": "MainPage", "type": "MainPage"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace Controls { class Button { System.String Tooltip; System.String Value; System.Boolean Hidden; System.Boolean Disabled; System.Boolean IsValid; System.String Id; System.String Type;  } class Label { System.String Text; System.String Tooltip; System.String Value; System.Boolean Hidden; System.Boolean Disabled; System.Boolean IsValid; System.String Id; System.String Type;  }  } namespace RuleOutputs { class CreateEntityRecordRuleOutput { System.String RecordId; Controls.Rules.RuleError Error;  }  } namespace CustomControls { class AddRecord: Controls.Button { RuleOutputs.CreateEntityRecordRuleOutput CreateEntityRecord;  }  } class MainPage { System.String Tooltip;  } ",
        "benchmarkExpression": "MainPage.AddRecord.CreateEntityRecord.Error.Message",
    },
    {
        "currentExpression": "",
        "userRequest": "get choice set value name gender at index 4 append name of company logo file name to it",
        "allVariablesIncluded": True,
        "availableVariables": [{"name": "Media.companylogo_png", "type": "AppMedia"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "class AppMedia:AppsFile { System.String mimeType } class AppsFile { AppsFile(System.String? url); System.String Name; System.String Extension; System.String FileSize; System.String NameWithoutExtension; System.String integrationType; System.String URL; } string GetChoiceSetValue(string choiceSetName, int numberId);",
        "benchmarkExpression": 'GetChoiceSetValue("gender", 4) + Media.companylogo_png.Name',
    },
    {
        "currentExpression": "",
        "userRequest": "get selected user email",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "NewPage.Users", "type": "CustomControls.Users"},
            {"name": "NewPage", "type": "NewPage"},
            {"name": "currrentRecord", "type": "entity.AlexEntity"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace entity { class SystemUser { System.Nullable(Of System.Guid) Id; System.Nullable(Of System.DateTimeOffset) CreateTime; System.String Email; System.Nullable(Of System.Boolean) IsActive; System.String Name; System.Nullable(Of System.Int32) Type; System.Nullable(Of System.DateTimeOffset) UpdateTime; } } namespace Controls { class DropdownVb { entity.SystemUser Value; System.String HintText; System.String ToolTip; System.String Label; entity.SystemUser SelectedItem; System.Byte() DataCache; Apps.Controls.ListSource(Of entity.SystemUser) DataSource; System.Boolean Required; System.String RequiredErrorMessage; System.Boolean Hidden; System.Boolean Disabled; System.Boolean IsValid; System.String Id; System.String Type; } } namespace CustomControls { class Users: Controls.DropdownVb { Apps.Controls.ListSource(Of entity.SystemUser) DataSource; entity.SystemUser SelectedItem; entity.SystemUser Value; } } class NewPage { System.String Tooltip; }",
        "benchmarkExpression": "NewPage.Users.SelectedItem.Email",
    },
    {
        "currentExpression": "",
        "userRequest": "get query param of type number with name stockCount and default value 100 , multiply this with warehouses",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "warehouses", "type": "System.Int64"},
            {"name": "MainPage", "type": "MainPage"},
        ],
        "expressionTypeDefinition": "Long",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "T App.QueryParam < T > ((string param, T ? defaultValue = default (T));",
        "benchmarkExpression": 'App.QueryParam(Of Integer)("stockCount", 100) * warehouses',
    },
    {
        "currentExpression": "",
        "userRequest": "create new app file from link https://myimg.png and get its name without extension",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "warehouses", "type": "System.Int64"},
            {"name": "MainPage", "type": "MainPage"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "class AppsFile { AppsFile(System.String ? url); System.String Name; System.String Extension; System.String FileSize; System.String NameWithoutExtension; System.String integrationType; System.String URL; }",
        "benchmarkExpression": 'New AppsFile("https://myimg.png").NameWithoutExtension',
    },
    {
        "currentExpression": "",
        "userRequest": "from employee ledger get the role of the second element in out bio",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "Processes.EmployeeLedger", "type": "Processes.EmployeeLedger"},
            {"name": "Processes.Processes", "type": "Processes.Processes"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace Processes { class EmployeeLedger { System.String() in_Roles_arr; System.String() in_Location_arr; System.Data.DataTable out_dt_Bio; System.String() out_Role_lookup; System.String() out_Location_lookup; } class Processes {} }",
        "benchmarkExpression": 'Processes.EmployeeLedger.out_dt_Bio.Rows(1)("Role").ToString()',
    },
]
