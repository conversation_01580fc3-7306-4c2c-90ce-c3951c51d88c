ExpressionGenerationInputSchema = [
    {"key": "additionalTypeDefinitions", "type": "string"},
    {"key": "availableVariables", "type": "string"},
    {"key": "expressionLanguage", "type": "string"},
    {"key": "userRequest", "type": "string"},
    {"key": "expressionTypeDefinition", "type": "string"},
    {"key": "currentExpression", "type": "string"},
]

ExpressionGenerationOutputSchema = [{"key": "result", "type": "string"}, {"key": "benchmark_expression", "type": "string"}]

FixGenerationInputSchema = [
    {"key": "additionalTypeDefinitions", "type": "string"},
    {"key": "availableVariables", "type": "string"},
    {"key": "expressionLanguage", "type": "string"},
    {"key": "expressionTypeDefinition", "type": "string"},
    {"key": "currentExpression", "type": "string"},
    {"key": "currentError", "type": "string"},
]
