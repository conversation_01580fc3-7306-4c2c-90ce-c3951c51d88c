examples = [
    {
        "userRequest": "If the input string is a palindrome, print 'Yes'",
        "currentExpression": "",
        "benchmarkExpression": '$str | split("") as $chars | ($chars | reverse | join("")) == $str | if . then "Yes" end',
        "availableVariables": [{"name": "str", "type": "string"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "namespace UiPath.Core.Activities { class CurrentJobInfo { string WorkflowName; string ProcessName; string ProcessVersion; string RobotName; string Key; string TenantName; string FolderName; string UserEmail; UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Check if at least 3 users are returned",
        "currentExpression": "",
        "benchmarkExpression": "$context.outputs.HTTP_Request_1.content.results | map(.location.country) | group_by(.) | map({(.[0]): length}) | any(.[] | . >= 3)",
        "expressionTypeDefinition": "boolean",
        "allVariablesIncluded": True,
        "availableVariables": [
            {"name": "$context", "type": "System.Object"},
            {"name": "$context.outputs", "type": "System.Object"},
            {"name": "$context.outputs.HTTP_Request_1", "type": "System.Object"},
            {"name": "$context.outputs.HTTP_Request_1.content", "type": "System.Object"},
            {"name": "$context.outputs.HTTP_Request_1.headers", "type": "Record<string, string>"},
            {"name": "$context.outputs.HTTP_Request_1.statusCode", "type": "System.Int32"},
            {"name": "$context.outputs.HTTP_Request_1.request", "type": "System.Object"},
            {"name": "$context.outputs.HTTP_Request_1.request.method", "type": "System.String"},
            {"name": "$context.outputs.HTTP_Request_1.request.url", "type": "System.String"},
            {"name": "$context.outputs.HTTP_Request_1.request.headers", "type": "Record<string, string>"},
            {"name": "$context.outputs.HTTP_Request_1.request.queryParameters", "type": "Record<string, string>"},
            {"name": "$context.outputs.HTTP_Request_1.request.body", "type": "System.Object"},
            {"name": "$workflow", "type": "System.Object"},
            {"name": "$workflow.input", "type": "{ [k: string]: any; }"},
        ],
        "expressionLanguage": "jq",
        "activityIdRef": "If_1",
        "activityTypeName": "If",
        "additionalTypeDefinitions": 'declare namespace $context { namespace outputs {\n                    /**\n### HTTP Request\n\n*/\n                    namespace HTTP_Request_1 { const content: any;\nconst headers: Record<string, string>;\nconst statusCode: number;\nconst request: {\n                    method: string;\n                    url: string;\n                    headers?: Record<string, string>;\n                    queryParameters?: Record<string, string>;\n                    body?: any;\n                }; }\n            } }\ndeclare namespace $workflow { const input: {\n[k: string]: any\n}\n }\n\ntype TimerHandler = string | Function;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/clearInterval) */\nclearInterval(id: number | undefined): void;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/clearTimeout) */\nclearTimeout(id: number | undefined): void;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/setInterval) */\ndeclare function setInterval(handler: TimerHandler, timeout?: number, ...arguments: any[]): number;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/setTimeout) */\ndeclare function setTimeout(handler: TimerHandler, timeout?: number, ...arguments: any[]): number;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/structuredClone) */\ndeclare function structuredClone<T = any>(value: T, options?: StructuredSerializeOptions): T;\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/queueMicrotask) */\nqueueMicrotask(callback: VoidFunction): void;\n\n /* Please consider I have the following variables: \n{"$workflow":{"input":{}},"$context":{},"$input":{}}\n. Please use full path for variables, like $context.output.a, instead of output.a. */',
    },
    {
        "userRequest": "Write today's date in the format YYYY-MM-DD.",
        "currentExpression": "",
        "benchmarkExpression": 'now | strftime("%Y-%m-%d")',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\nnamespace UiPath.Core.Activities { \n    class CurrentJobInfo { \n        string WorkflowName; \n        string ProcessName; \n        string ProcessVersion; \n        string RobotName; \n        string Key; \n        string TenantName; \n        string FolderName; \n        string UserEmail; \n        UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; \n    } \n    enum PictureInPictureMode { \n        NotSupported, \n        Main, \n        PictureInPictureSession, \n        PictureInPictureDesktop, \n    } \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the sum of the first 100 natural numbers without any formula.",
        "currentExpression": "",
        "benchmarkExpression": "[range(100)] | add | tostring",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\nnamespace UiPath.Core.Activities { \n    class CurrentJobInfo { \n        string WorkflowName; \n        string ProcessName; \n        string ProcessVersion; \n        string RobotName; \n        string Key; \n        string TenantName; \n        string FolderName; \n        string UserEmail; \n        UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; \n    } \n    enum PictureInPictureMode { \n        NotSupported, \n        Main, \n        PictureInPictureSession, \n        PictureInPictureDesktop, \n    } \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the average of the squares of the first 5 natural numbers.",
        "currentExpression": "",
        "benchmarkExpression": "[range(6)] | map(. * .) | add / length",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\nnamespace UiPath.Core.Activities { \n    class CurrentJobInfo { \n        string WorkflowName; \n        string ProcessName; \n        string ProcessVersion; \n        string RobotName; \n        string Key; \n        string TenantName; \n        string FolderName; \n        string UserEmail; \n        UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; \n    } \n    enum PictureInPictureMode { \n        NotSupported, \n        Main, \n        PictureInPictureSession, \n        PictureInPictureDesktop, \n    } \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the factorial of 7.",
        "currentExpression": "",
        "benchmarkExpression": "reduce range(1 ; 8) as $x (1; . * $x)",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\nnamespace UiPath.Core.Activities { \n    class CurrentJobInfo { \n        string WorkflowName; \n        string ProcessName; \n        string ProcessVersion; \n        string RobotName; \n        string Key; \n        string TenantName; \n        string FolderName; \n        string UserEmail; \n        UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; \n    } \n    enum PictureInPictureMode { \n        NotSupported, \n        Main, \n        PictureInPictureSession, \n        PictureInPictureDesktop, \n    } \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first 10 Fibonacci numbers.",
        "currentExpression": "",
        "benchmarkExpression": "reduce range(0;8) as $i ([0, 1]; . + [.[-1] + .[-2]]) | map(tostring)",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "array",
        "additionalTypeDefinitions": "\nnamespace UiPath.Core.Activities { \n    class CurrentJobInfo { \n        string WorkflowName; \n        string ProcessName; \n        string ProcessVersion; \n        string RobotName; \n        string Key; \n        string TenantName; \n        string FolderName; \n        string UserEmail; \n        UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; \n    } \n    enum PictureInPictureMode { \n        NotSupported, \n        Main, \n        PictureInPictureSession, \n        PictureInPictureDesktop, \n    } \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first day of last month in YYYY-MM-DD format.",
        "currentExpression": "",
        "benchmarkExpression": 'now | gmtime | \{year: (if .[1] == 0 then .[0] - 1 else .[0] end), month: (if .[1] == 0 then 11 else .[1] - 1 end), day: 1\} | map(.) | [ .[0], .[1]+1, .[2] ] | join("-")',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Check if the current year is a leap year.",
        "currentExpression": "",
        "benchmarkExpression": "now | gmtime | .[0] as $y | ($y % 4 == 0 and ($y % 100 != 0 or $y % 400 == 0)) | tostring",
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Output all the letters in the alphabet in a single string.",
        "currentExpression": "",
        "benchmarkExpression": '[[range(97; 123)] | implode] | join("")',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Replace all 'a' with 'b' in 'banana'",
        "currentExpression": "",
        "benchmarkExpression": '"banana" | gsub("a";"b")',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Reverse the order of words 'A lazy fox jumps over the fence'",
        "currentExpression": "",
        "benchmarkExpression": '"A lazy fox jumps over the fence" | split(" ") | reverse | join(" ")',
        "availableVariables": [],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Compute the average age of the users.",
        "currentExpression": "",
        "benchmarkExpression": "$httpResponse.users | map(.age) | add / length",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { users: User[]; }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\ninterface User { \n    name: string; \n    country: string; \n    age: number; \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890ab",
    },
    {
        "userRequest": "Compute how many employees occupy the same position for all positions.",
        "currentExpression": "",
        "benchmarkExpression": "$httpResponse.employees | group_by(.position) | map({ (.[0].position): length }) | add | tostring",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { employees: Employee[]; }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\ninterface Employee { \n    name: string; \n    position: string; \n}\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890ab",
    },
    {
        "userRequest": "Compute the maximum number from the first half of the input list.",
        "currentExpression": "",
        "benchmarkExpression": "$l [:(($l | length) / 2) | floor] | max",
        "availableVariables": [{"name": "l", "type": "array"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Compute the sum of even numbers in the input list.",
        "currentExpression": "",
        "benchmarkExpression": "$numbers | map(select(. % 2 == 0)) | add",
        "availableVariables": [{"name": "numbers", "type": "array"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Reverse the string received as input",
        "currentExpression": "",
        "benchmarkExpression": "$str | explode | reverse | implode",
        "availableVariables": [{"name": "str", "type": "string"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Find the common keys between two dictionaries.",
        "currentExpression": "",
        "benchmarkExpression": "($dict1 | keys) as $keys1 | ($dict2 | keys) as $keys2 | $keys1 | map(select(. as $key | $keys2 | index($key)))",
        "availableVariables": [
            {"name": "dict1", "type": "interface Dict1 { A: number; B: number }"},
            {"name": "dict2", "type": "interface Dict2 { B: number; C: number }"},
        ],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "array",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive 3 variables, a, b, c in a json. We know a>0. You need to solve e^(ax+b) = c. If there is no solution, return null.",
        "currentExpression": "",
        "benchmarkExpression": "if $input.c > 0 then (($input.c | log) - $input.b)/$input.a else null end",
        "availableVariables": [
            {"name": "input", "type": "interface Input { a: number; b: number; c: number }"},
        ],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a string as input. You should write all email addresses present in it.",
        "currentExpression": "",
        "benchmarkExpression": '$input | scan("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")',
        "availableVariables": [
            {"name": "input", "type": "string"},
        ],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "array",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a list as input. Return the product of numbers on even positions.",
        "currentExpression": "",
        "benchmarkExpression": "$l | reduce range(0; $l | length; 2) as $i (1; . * $l[$i])",
        "availableVariables": [
            {"name": "l", "type": "array"},
        ],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Compute the average of the numbers in the list.",
        "currentExpression": "",
        "benchmarkExpression": '{ "result": ($inp | .list | add / length) }',
        "availableVariables": [{"name": "inp", "type": "interface List { list: number[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Output the unique elements from the intersection of two lists, $list1 and $list2",
        "currentExpression": "",
        "benchmarkExpression": '{ "intersection": $input | (.list1 | unique) as $l1 | (.list2 | unique) as $l2 | ($l1 | map(select(. as $i | $l2 | index($i)))) }',
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { intersection: number[] }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Compute a raised to the power of b.",
        "currentExpression": "",
        "benchmarkExpression": '{ "result": ($input.a as $a | reduce range(0; $input.b) as $i (1; . * $a)) }',
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Output all numbers in the list $l that are between $a and $b, inclusive.",
        "currentExpression": "",
        "benchmarkExpression": '{ "result": ($inp.l | map(select(. >= $inp.a and . <= $inp.b))) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { l: number[], a: number, b: number }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: number[] }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Output all strings in the list $words that contain the substring $sub.",
        "currentExpression": "",
        "benchmarkExpression": '$inp | { "result": (.words as $words | .sub as $sub | $words | map(select(test($sub)))) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[], sub: string }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Convert all strings to uppercase.",
        "currentExpression": "",
        "benchmarkExpression": '$inp | { "result": (.words | map(ascii_upcase)) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Group items by category.",
        "currentExpression": "",
        "benchmarkExpression": '$inp | { "result": (.items | sort_by(.category) | group_by(.category) | map({(.[0].category): .})) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: { [key: string]: { name: string, category: string }[] } }",
        "additionalTypeDefinitions": "interface Item { name: string, category: string }\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Combine first and last names into a full name.",
        "currentExpression": "",
        "benchmarkExpression": '$inp | { "result": (.people | map({full_name: (.first_name + " " + .last_name)})) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { people: Person[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: { full_name: string }[] }",
        "additionalTypeDefinitions": "interface Person { first_name: string, last_name: string }\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Find the most frequent element in the list.",
        "currentExpression": "",
        "benchmarkExpression": '$inp | { "result": (.items | group_by(.) | map({key: .[0], count: length}) | max_by(.count) | .key) }',
        "availableVariables": [{"name": "inp", "type": "interface Input { items: string[] }"}],
        "expressionLanguage": "jq",
        "expressionTypeDefinition": "interface Result { result: string }",
        "additionalTypeDefinitions": "\n\n",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
]
