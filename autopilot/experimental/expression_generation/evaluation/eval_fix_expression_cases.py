examples = [
    {
        "currentExpression": "myString to SecureString()",
        "expressionTypeDefinition": "System.Security.SecureString",
        "allVariablesIncluded": True,
        "currentError": "/box/Main.vb (7,23) : error VBNC30205: End of statement expected. \n/box/Main.vb (7,23) : error VBNC90019: Expected 'End' \n/box/Main.vb (8,12) : error VBNC90019: Expected 'Module'. \n/box/Main.vb (9,11) : error VBNC30203: Identifier expected.",
        "availableVariables": [{"name": "myString", "type": "System.String"}],
        "expressionLanguage": "vbnet",
        "activityIdRef": "Assign_9",
        "activityTypeName": "System.Activities.Statements.Assign, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'New System.Net.NetworkCredential("", myString).SecurePassword',
    },
    {
        "currentExpression": "Dim secureStr As SecureString() : str.ToList().ForEach()",
        "expressionTypeDefinition": "",
        "allVariablesIncluded": True,
        "currentError": "/box/Main.vb (7,47) : error VBNC30456: 'ToList' is not a member of 'System.String'. /box/Main.vb (7,22) : warning VBNC42024: Function without an 'As' clause; Object return type assumed. There were 1 errors and 2 warnings.",
        "availableVariables": [{"name": "myString", "type": "System.String"}],
        "expressionLanguage": "vbnet",
        "activityIdRef": "Assign_9",
        "activityTypeName": "System.Activities.Statements.Assign, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'New System.Net.NetworkCredential("", myString).SecurePassword',
    },
    {
        "currentExpression": "dict1.Count >= 2",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"dict1.Count >= 2\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str2", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "Assign_9",
        "activityTypeName": "System.Activities.Statements.Assign, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(dict1.Count >= 2).ToString()",
    },
    {
        "currentExpression": "(dict1.Count >= 3) Or (dict2.Count >= 3)",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"(dict1.Count >= 3) Or (dict2.Count >= 3)\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str2", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "3f164382-a896-4f75-be6d-99e6ea59abac",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "((dict1.Count >= 3) OrElse (dict2.Count >= 3)).ToString()",
    },
    {
        "currentExpression": "myList.Contains(Math.Round(dict1.Keys.Average()))",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"myList.Contains(Math.Round(dict1.Keys.Average()))\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Double' to 'Integer'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str2", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "e17d5fc6-7751-4009-95c0-ba88855bda8a",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "myList.Contains(CInt(Math.Round(dict1.Keys.Average()))).ToString()",
    },
    {
        "currentExpression": "dict1.ContainsKey(DateTime.Now.Day) AndAlso dict2.ContainsKey(DateTime.Now.Day)",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"dict1.ContainsKey(DateTime.Now.Day) AndAlso dict2.ContainsKey(DateTime.Now.Day)\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str2", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "57d87a40-e6e0-4977-b58d-9f467723712c",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(dict1.ContainsKey(DateTime.Now.Day) AndAlso dict2.ContainsKey(DateTime.Now.Day)).ToString()",
    },
    {
        "currentExpression": "Not dict1.Keys.Any(Function(k) str1.Contains(k))",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"Not dict1.Keys.Any(Function(k) k.ToString() = str1)\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "8ba58878-d3e7-419a-9b7b-40f07777d3fc",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(Not dict1.Keys.Any(Function(k) str1.Contains(k))).ToString()",
    },
    {
        "currentExpression": "(dict1.Keys.Intersect(dict2.Keys).Count() Mod 2) = 0",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"(dict1.Keys.Intersect(dict2.Keys).Count() Mod 2) = 0\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "c222c4da-26b0-47a5-8687-4c43c630dbf6",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(dict1.Keys.Intersect(dict2.Keys).Count() Mod 2 = 0).ToString()",
    },
    {
        "currentExpression": "str1.Substring(str1.Length - 3, 1) = str1.Substring(str1.Length - 2, 1) AndAlso str1.Substring(str1.Length - 2, 1) = str1.Substring(str1.Length - 1, 1)",
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"str1.Substring(str1.Length - 3, 1) = str1.Substring(str1.Length - 2, 1) AndAlso str1.Substring(str1.Length - 2, 1) = str1.Substring(str1.Length - 1, 1)\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "toVariable", "type": "System.Double, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
            {"name": "str1", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_1",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "3b445704-ef0b-4e6d-9f77-d4a63b980049",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "(str1.Length >= 3 AndAlso str1.Substring(str1.Length - 3, 1) = str1.Substring(str1.Length - 2, 1) AndAlso str1.Substring(str1.Length - 2, 1) = str1.Substring(str1.Length - 1, 1)).ToString()",
    },
    {
        "currentExpression": 'System.Text.RegularExpressions.Regex.IsMatch(myString, "^[a-z]+$")',
        "expressionTypeDefinition": "String",
        "allVariablesIncluded": True,
        "currentError": "The selected value is incompatible with the property type.\n\nCompiler error(s) encountered processing expression \"System.Text.RegularExpressions.Regex.IsMatch(myString, \"^[a-z]+$\")\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'Boolean' to 'String'.",
        "availableVariables": [
            {"name": "myString", "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {"name": "x1", "type": "System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {"name": "x_i_", "type": "System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {"name": "x6", "type": "System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"},
            {
                "name": "_Tab_opened_successfully__",
                "type": "System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e",
            },
        ],
        "expressionLanguage": "vbnet",
        "activityIdRef": "WriteLine_2",
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=6.0.0.0, Culture=neutral, PublicKeyToken=null",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.IsMatch(myString, "^[a-z]+$").ToString()',
    },
    {
        "currentExpression": "wb",
        "currentError": "Compiler error(s) encountered processing expression \"wb\".(1,136): error CS0029: Cannot implicitly convert type 'MicrosoftOffice365.Files.Models.O365DriveRemoteItem' to 'string'\n(1,136): error CS1662: Cannot convert lambda expression to intended delegate type because some of the return types in the block are not implicitly convertible to the delegate return type",
        "availableVariables": [{"name": "wb", "type": "MicrosoftOffice365.Files.Models.O365DriveRemoteItem"}, {"name": "name", "type": "System.String"}],
        "expressionTypeDefinition": "System.String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "namespace MicrosoftOffice365.Files.Models { class O365DriveRemoteItem { long SizeInBytes; string Name; string Extension; string Uri; string Url; string MimeType; string IconUri; string FullName; string ID; bool IsFolder; DateTime? CreationDate; DateTime? LastModifiedDate; Platform.ResourceHandling.ILocalResource LocalCopy; Dictionary<string, string> Metadata; string ParentUri; MicrosoftOffice365.Files.Models.DriveItemIdentitySet CreatedBy; MicrosoftOffice365.Files.Models.DriveItemIdentitySet LastModifiedBy; string CreatedByDisplayName; string LastModifiedByDisplayName; string Summary; string GetExtension(); string GetName(); long GetSizeInBytes(); } class DriveItemIdentitySet { MicrosoftOffice365.Files.Models.DriveItemIdentity Application; MicrosoftOffice365.Files.Models.DriveItemIdentity Device; MicrosoftOffice365.Files.Models.DriveItemIdentity User; MicrosoftOffice365.Files.Models.DriveItemIdentitySetType Type; } class DriveItemIdentity { string DisplayName; string Id; } enum DriveItemIdentitySetType { Application, Device, User, } }",
        "benchmarkExpression": "wb.Name",
        "id": "80c3d54a-9ec7-4688-816f-7faa485082b7",
    },
    {
        "currentExpression": "2 to 9",
        "currentError": "Compiler error(s) encountered processing expression \"3 to 7\".(1,70): error CS1002: ; expected\n(1,73): error CS1002: ; expected\n(1,70): error CS0103: The name 'to' does not exist in the current context\n(1,73): error CS0201: Only assignment, call, increment, decrement, await, and new object expressions can be used as a statement\n(1,68): error CS0029: Cannot implicitly convert type 'int' to 'string'\n(1,68): error CS1662: Cannot convert lambda expression to intended delegate type because some of the return types in the block are not implicitly convertible to the delegate return type",
        "availableVariables": [{"name": "range", "type": "System.String"}],
        "expressionTypeDefinition": "System.String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'string.Join(", ", Enumerable.Range(2, 8))',
        "id": "cfbafda7-3ec0-4917-bdc2-4a3ddb27cc71",
    },
    {
        "currentExpression": 'fD["Submit"].ToString.Equals("Clicked")',
        "currentError": 'Compiler error(s) encountered processing expression "fD["Submit"].ToString.Equals("Clicked")".(1,116): error CS0119: \'string.ToString()\' is a method, which is not valid in the given context',
        "availableVariables": [{"name": "fD", "type": "System.Collections.DictionaryBase"}, {"name": "condition", "type": "System.Boolean"}],
        "expressionTypeDefinition": "Boolean",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'fD["Submit"].ToString().Equals("Clicked")',
        "id": "107e0d78-ecd9-4883-949f-e2974edf0d65",
    },
    {
        "currentExpression": "C:\\Users\\<USER>\\OneDrive\\work\\work1.xlsx",
        "currentError": "Compiler error(s) encountered processing expression \"\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\work\\\\work1.xlsx\"\".(1,87): error CS0029: Cannot implicitly convert type 'string' to 'Microsoft.Graph.DriveItem'\n(1,87): error CS1662: Cannot convert lambda expression to intended delegate type because some of the return types in the block are not implicitly convertible to the delegate return type",
        "availableVariables": [{"name": "workDrive", "type": "Microsoft.Graph.DriveItem"}],
        "expressionTypeDefinition": "DriveItem",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'new Microsoft.Graph.DriveItem { Name = "work1.xlsx", ParentReference = new Microsoft.Graph.ItemReference { Path = "/drive/root:/Users/<USER>/OneDrive/work" } }',
        "id": "4fc25309-aa8f-4e33-87cc-637f9e05d6d4",
    },
    {
        "currentExpression": '"Water","Juice"',
        "currentError": 'Compiler error(s) encountered processing expression ""Water", "Juice"".(1,71): error CS1002: ; expected\n(1,71): error CS7017: Member definition, statement, or end-of-file expected\n(1,73): error CS0201: Only assignment, call, increment, decrement, await, and new object expressions can be used as a statement',
        "availableVariables": [{"name": "list", "type": "System.String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": '"Water" + "," + "Juice"',
        "id": "23fa30c4-dd2c-416c-9d2e-09846609ed6c",
    },
    {
        "currentExpression": "System.IO.Directory.GetFiles(path, searchfor)",
        "currentError": "Compiler error(s) encountered processing expression \"System.IO.Directory.GetFiles(path, searchfor)\".(1,108): error CS0029: Cannot implicitly convert type 'string[]' to 'string'\n(1,108): error CS1662: Cannot convert lambda expression to intended delegate type because some of the return types in the block are not implicitly convertible to the delegate return type",
        "availableVariables": [
            {"name": "path", "type": "System.String"},
            {"name": "searchfor", "type": "System.String"},
            {"name": "file", "type": "System.String"},
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "System.IO.Directory.GetFiles(downloadpath1,searchfor).FirstOrDefault()",
        "id": "7f9a68b7-bda4-4f1f-b603-13c8eeea9b57",
    },
    {
        "currentExpression": "index",
        "currentError": "Compiler error(s) encountered processing expression \"index\".(1,78): error CS0029: Cannot implicitly convert type 'int' to 'string'\n(1,78): error CS1662: Cannot convert lambda expression to intended delegate type because some of the return types in the block are not implicitly convertible to the delegate return type",
        "availableVariables": [{"name": "userEmails", "type": "System.String[]"}, {"name": "index", "type": "System.Int32"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "userEmails[index]",
        "id": "866fe5d2-c637-4049-9c3f-bd113e9161b9",
    },
    {
        "currentExpression": 'Today.AddDays.ToString("dd MM yyyy")',
        "currentError": 'Compiler error(s) encountered processing expression "Today.AddDays.ToString("dd MM yyyy")".(1,68): error CS0103: The name \'Today\' does not exist in the current context',
        "availableVariables": [{"name": "yesterday", "type": "System.String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": 'DateTime.Today.AddDays(-1).ToString("dd MM yyyy")',
        "id": "93873bf0-dda4-44c5-b4da-d4945c6833dc",
    },
    {
        "currentExpression": 'sourceFiles.SelectedSheet.Name is not "ExcelSheet',
        "currentError": "Compiler error(s) encountered processing expression \"sourceFiles.SelectedSheet.Name is not \"ExcelSheet\".(1) : error BC30027: 'End Function' expected.\n(2) : error BC30648: String constants must end with a double quote.\n(3) : error BC30198: ')' expected.\n(2) : error BC30512: Option Strict On disallows implicit conversions from 'String' to 'Long'.\n(2) : error BC30020: 'Is' operator does not accept operands of type 'Long'. Operands must be reference or nullable types. The selected value is incompatible with the property type.",
        "availableVariables": [{"name": "condition", "type": "System.Boolean"}, {"name": "sourceFiles", "type": "Excel.IWorkbookQuickHandle"}],
        "expressionTypeDefinition": "Boolean",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace Excel { interface IWorkbookQuickHandle { Excel.RangeValue SelectedRange; Excel.ExcelValue SelectedCell; Excel.WorksheetQuickHandle SelectedSheet; Excel.IWorksheetIndexer Sheet; Excel.ITableIndexer Table; string FilePath; Excel.IPivotTableRef AllPivotTables; } class RangeValue { string Worksheet; string Address; string FullRangeName; string FirstCell; IEnumerable<string> Columns; string TableName; Excel.ExcelRangeType RangeType; System.Data.DataTable DataTableValue; System.Data.DataTable DataTableOutValue; int RowCount; } class ExcelValue { System.Drawing.Color Color; string Formula; object RawValue; Type RawValueType; string Worksheet; string Address; int Int32Value; string StringValue; bool BooleanValue; double DoubleValue; decimal DecimalValue; DateTime DateTimeValue; TimeSpan TimeSpanValue; } class WorksheetQuickHandle { Excel.ExcelRangeType RangeType; string Name; Excel.IRangeIndexer Range; Excel.ICellIndexer Cell; Excel.IPivotIndexer PivotTable; Excel.IChartIndexer Chart; string Worksheet; string Address; string FirstCell; string FullRangeName; System.Data.DataTable DataTableValue; System.Data.DataTable DataTableOutValue; int RowCount; } interface IWorksheetIndexer { Excel.WorksheetQuickHandle Item; } interface ITableIndexer { Excel.RangeValue Item; } interface IPivotTableRef { string Worksheet; string Address; string FirstCell; string FullRangeName; int RowCount; string TableName; } enum ExcelRangeType { Range, Table, PivotTable, Sheet, DataTable, } interface IRangeIndexer { Excel.RangeValue Item; } interface ICellIndexer { Excel.ExcelValue Item; Excel.ExcelValue Item; } interface IPivotIndexer { Excel.RangeValue Item; } interface IChartIndexer { Excel.ChartQuickHandle Item; } }",
        "benchmarkExpression": 'sourceFiles.SelectedSheet.Name <> "ExcelSheet"',
        "name": "check excel sheet name",
        "id": "50c29e42-07cf-4b45-a5c9-7608d2186d82",
    },
    {
        "currentExpression": "this year",
        "currentError": "Compiler error(s) encountered processing expression \"\"this year\"\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'String' to 'Integer'. The selected value is incompatible with the property type.",
        "availableVariables": [{"name": "year", "type": "System.Int32"}],
        "expressionTypeDefinition": "System.Int32",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "DateTime.Now.Year",
        "id": "53a7992c-1649-4b2b-bcf7-f89717ae8ae6",
    },
    {
        "currentExpression": "Gmail.SelectedMails()",
        "currentError": "Compiler error(s) encountered processing expression \"Gmail.SelectedMails()\".(2) : error BC30512: Option Strict On disallows implicit conversions from 'IEnumerable(Of MailMessage)' to 'String()'. The selected value is incompatible with the property type.",
        "availableVariables": [{"name": "emails", "type": "System.String[]"}, {"name": "Gmail", "type": "Mail.IMailQuickHandle"}],
        "expressionTypeDefinition": "System.Int32",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace Mail { interface IMailQuickHandle { System.Net.Mail.MailMessage SelectedMail; IEnumerable<MailMessage> SelectedMails; string SelectedAccount; } } namespace System.Net.Mail { class MailMessage { System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection Bcc; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailPriority Priority; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; string Subject; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; string Body; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } class MailAddress { string DisplayName; string User; string Host; string Address; } enum MailPriority { Normal, Low, High, } enum DeliveryNotificationOptions { None, OnSuccess, OnFailure, Delay, Never, } }",
        "benchmarkExpression": "Gmail.SelectedMails.Select(Function(mail) mail.Subject).ToArray()",
        "id": "d19ac8bd-3709-4079-a725-154b6e1569a4",
    },
]
