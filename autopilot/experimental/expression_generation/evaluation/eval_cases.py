# flake8: noqa

examples = [
    {
        "userRequest": "Verify that the variable myString only contains lower-case letters.",
        "currentExpression": "",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.IsMatch(myString, "^[a-z]+$").ToString()',
        "availableVariables": [
            {"name": "myString", "type": "System.String"},
            {"name": "x1", "type": "System.Int32"},
            {"name": "x_i_", "type": "System.Int32"},
            {"name": "x6", "type": "System.Object"},
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "jobData", "type": "UiPath.Core.Activities.CurrentJobInfo"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "namespace UiPath.Core.Activities { class CurrentJobInfo { string WorkflowName; string ProcessName; string ProcessVersion; string RobotName; string Key; string TenantName; string FolderName; string UserEmail; UiPath.Core.Activities.PictureInPictureMode PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
        "outputType": "json",
    },
    {
        "userRequest": "Use myPassword to create a secure string",
        "currentExpression": "",
        "benchmarkExpression": 'New System.Net.NetworkCredential("", myPassword).SecurePassword',
        "availableVariables": [
            {"name": "myPassword", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "System.Security.SecureString",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
        "outputType": "json",
    },
    {
        "userRequest": "Check if the sum of elements on even positions is bigger than 10",
        "currentExpression": "",
        "benchmarkExpression": "(myList.Where(Function(i, index) index Mod 2 = 0).Sum() > 10).ToString()",
        "availableVariables": [
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            }
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "00e1144e-2ca2-4a00-937e-3f115814b299",
        "outputType": "json",
    },
    {
        "userRequest": '"Verify that str1 has at least one word with more than 3 characters"',
        "benchmarkExpression": 'str1.Split(" "c).Any(Function(word) word.Length > 3).ToString()',
        "currentExpression": "",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "c545f7ea-8e18-49e1-b8af-e510e87b9083",
        "outputType": "json",
    },
    {
        "userRequest": '"Verify that the last 3 characters of str1 are equal to each other "',
        "currentExpression": "",
        "benchmarkExpression": "str1.Substring(str1.Length - 3, 3).All(Function(c) c = str1.Substring(str1.Length - 3, 3).First())",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "3b445704-ef0b-4e6d-9f77-d4a63b980049",
        "outputType": "json",
    },
    {
        "userRequest": '"Verify that the intersection between dict1 and dict2 has even length"',
        "currentExpression": "",
        "benchmarkExpression": "(dict1.Keys.Intersect(dict2.Keys).Count() Mod 2 = 0).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "c222c4da-26b0-47a5-8687-4c43c630dbf6",
    },
    {
        "userRequest": '" Verify that no key in dict1 appears in str1 "',
        "currentExpression": "",
        "benchmarkExpression": "(Not dict1.Keys.Any(Function(k) str1.Contains(k.ToString()))).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "8ba58878-d3e7-419a-9b7b-40f07777d3fc",
    },
    {
        "userRequest": '"Verify that the current day appears as a key in both dict1 and dict2"',
        "currentExpression": "",
        "benchmarkExpression": "(dict1.ContainsKey(DateTime.Now.Day) AndAlso dict2.ContainsKey(DateTime.Now.Day)).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str2", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "57d87a40-e6e0-4977-b58d-9f467723712c",
    },
    {
        "userRequest": '"verify that the mean of keys in dict1, rounded to the nearest integer appears in myList"',
        "currentExpression": "",
        "benchmarkExpression": "myList.Contains(CInt(Math.Round(dict1.Keys.Average()))).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str2", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "e17d5fc6-7751-4009-95c0-ba88855bda8a",
    },
    {
        "userRequest": '"Check if either dict1 or dict2 have at least 3 elements"',
        "currentExpression": "",
        "benchmarkExpression": "(dict1.Count >= 3 OrElse dict2.Count >= 3).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str2", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_1",
        "workflowId": "3f164382-a896-4f75-be6d-99e6ea59abac",
    },
    {
        "userRequest": '"The truth value of dict1 having at least 2 elements"',
        "currentExpression": "",
        "benchmarkExpression": "(dict1.Count >= 2).ToString()",
        "availableVariables": [
            {
                "name": "dict1",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "_Tab_opened_successfully__", "type": "System.String"},
            {"name": "toVariable", "type": "System.Double"},
            {
                "name": "myList",
                "type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str1", "type": "System.String"},
            {
                "name": "dict2",
                "type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]",
            },
            {"name": "str2", "type": "System.String"},
        ],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.Assign, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "Assign_9",
        "workflowId": "d6d80172-f40e-4239-9b7b-084d6c700f05",
    },
    {
        "currentExpression": "",
        "userRequest": "check if today's date is greater than the 20th of november 2021",
        "availableVariables": [],
        "expressionTypeDefinition": "Boolean",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "348d5f6d-87b2-4d8b-bbef-81a009f0d9cd",
        "benchmarkExpression": "DateTime.Today > New DateTime(2021, 11, 20)",
    },
    {
        "currentExpression": "",
        "userRequest": "find the outputRecords with largest income",
        "availableVariables": [
            {
                "name": "outputRecords",
                "type": "System.Collections.Generic.IList`1[[DataService.Entities.Student, DataService.gbTssLKtC3Ag, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]",
            }
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace DataService.Entities { class Student { string BirthPlace; DateTimeOffset? BirthDate; string LastName; decimal? Income; bool? NewField; decimal? ANumber; string Name; decimal? Grade; string Major; ISet<Materii> Materie; DateTimeOffset CreateTime; Guid Id; DateTimeOffset UpdateTime; DataService.Entities.SystemUser CreatedBy; DataService.Entities.SystemUser UpdatedBy; } enum Materii { Chimie, Fizica, Romana, } class SystemUser { DateTimeOffset CreateTime; Guid Id; string Email; DateTimeOffset UpdateTime; DataService.Entities.UserType Type; string Name; bool IsActive; } enum UserType { User, Group, Robot, Application, } }",
        "id": "ac6336f6-d34a-4ccf-94e9-2b97fb58881f",
        "benchmarkExpression": "outputRecords.OrderByDescending(Function(s) s.Income).FirstOrDefault()",
    },
    {
        "currentExpression": "",
        "userRequest": "check if today is the third Monday of the month",
        "availableVariables": [],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "id": "3fbe9948-1691-413c-a582-7e245eb08aed",
        "benchmarkExpression": "DateTime.Today.DayOfWeek = DayOfWeek.Monday AndAlso DateTime.Today.Day / 7 = 2",
    },
    {
        "userRequest": "Substract 3 from it",
        "availableVariables": [{"name": "num1", "type": "Integer"}, {"name": "num2", "type": "Integer"}, {"name": "num3", "type": "Integer"}],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "Integer",
        "additionalTypeDefinitions": "",
        "benchmarkExpression": "num2 - 3",
        "id": "8894f3d7-d8ab-4752-a261-da1e9be39668",
        "currentExpression": "num2",
    },
    {
        "currentExpression": "",
        "userRequest": 'Received date of _out_GetNewestEmail_1__Result in "12/14/2020" format',
        "availableVariables": [{"name": "_out_GetNewestEmail_1__Result", "type": "UiPath.MicrosoftOffice365.Models.Office365Message"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace UiPath.MicrosoftOffice365.Models { interface Office365Message { string BodyAsHtml; string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; Importance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; IEnumerable<string> Categories; string InternetMessageId; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection Bcc; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailPriority Priority; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; string Subject; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; string Body; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } }",  # noqa
        "id": "c81cd1d5-ebcf-49e5-b9e0-96165541a37e",
        "benchmarkExpression": '_out_GetNewestEmail_1__Result.ReceivedDateTime.Value.ToString("MM/dd/yyyy")',
    },
    {
        "currentExpression": "",
        "userRequest": "From address of _out_GetNewestEmail_1__Result",
        "availableVariables": [{"name": "_out_GetNewestEmail_1__Result", "type": "UiPath.MicrosoftOffice365.Models.Office365Message"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace UiPath.MicrosoftOffice365.Models { interface Office365Message { System.Net.Mail.MailAddress From; string BodyAsHtml; string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; Importance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; IEnumerable<string> Categories; string InternetMessageId; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection Bcc; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailPriority Priority; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; string Subject; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; string Body; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } }",  # noqa
        "id": "cfaa6016-befc-43c0-953b-2c6435a8020b",
        "benchmarkExpression": "_out_GetNewestEmail_1__Result.From.Address",
    },
    {
        "currentExpression": "",
        "userRequest": 'If Email is a valid email "true" otherwise "false"',
        "availableVariables": [{"name": "Email", "type": "System.String"}],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "id": "bb53122f-9f7d-4ec1-9f1d-57e4012f18cc",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.IsMatch(Email, "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$")',
    },
    {
        "currentExpression": "",
        "userRequest": "LLM prompt to generate a description for VendorName",
        "availableVariables": [{"name": "VendorName", "type": "System.String"}],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "id": "2be9f354-494f-4cf2-9f9c-4cf12dbd2a66",
        "benchmarkExpression": "LLM.GenerateDescription(VendorName)",
    },
    {
        "currentExpression": "",
        "userRequest": "last monday from this month",
        "availableVariables": [],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "",
        "id": "b60a707d-5e8e-42da-a53b-95eb582c0a41",
        "benchmarkExpression": 'DateTime.Today.AddDays(-(DateTime.Today.Day - 1)).AddDays(-1).AddDays(-CInt(DateTime.Today.DayOfWeek)).ToString("yyyy-MM-dd")',
    },
    {
        "currentExpression": "",
        "userRequest": "_out_GetEmailByIdConnections_4__Result.Sender is part of the UiPath organization",
        "availableVariables": [{"name": "_out_GetEmailByIdConnections_4__Result.Sender", "type": "System.Net.Mail.MailAddress"}],
        "expressionLanguage": "vbnet",
        "expressionTypeDefinition": "String",
        "additionalTypeDefinitions": "namespace UiPath.MicrosoftOffice365.Models { interface Office365Message { string BodyAsHtml; string MessageId; string BodyPreview; Microsoft.Graph.FollowupFlag Flag; Importance? Importance; bool IsRead; DateTime? LastModifiedDateTime; string ParentFolderId; DateTime? ReceivedDateTime; DateTime? SentDateTime; string WebLink; IEnumerable<string> Categories; string InternetMessageId; int StandardAttachmentCount; IEnumerable<string> StandardAttachmentNames; int InlineAttachmentCount; IEnumerable<string> InlineAttachmentNames; System.Net.Mail.MailAddress From; System.Net.Mail.MailAddress Sender; System.Net.Mail.MailAddress ReplyTo; System.Net.Mail.MailAddressCollection ReplyToList; System.Net.Mail.MailAddressCollection To; System.Net.Mail.MailAddressCollection Bcc; System.Net.Mail.MailAddressCollection CC; System.Net.Mail.MailPriority Priority; System.Net.Mail.DeliveryNotificationOptions DeliveryNotificationOptions; string Subject; System.Text.Encoding SubjectEncoding; NameValueCollection Headers; System.Text.Encoding HeadersEncoding; string Body; System.Text.Encoding BodyEncoding; System.Net.Mime.TransferEncoding BodyTransferEncoding; bool IsBodyHtml; System.Net.Mail.AttachmentCollection Attachments; System.Net.Mail.AlternateViewCollection AlternateViews; } }",  # noqa
        "id": "eb218af0-395a-4624-8044-5b6943d6816b",
        "benchmarkExpression": '_out_GetEmailByIdConnections_4__Result.Sender.Host.EndsWith("uipath.com")',
    },
    {
        "currentExpression": "",
        "userRequest": "Two weeks from now at 12:30",
        "availableVariables": [],
        "expressionTypeDefinition": "DateTimeOffset",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "9c9bf386-7d15-4ccc-8923-dbc4cdf8535d",
        "benchmarkExpression": "DateTimeOffset.Now.AddDays(14).Date.AddHours(12).AddMinutes(30)",
    },
    {
        "currentExpression": "",
        "userRequest": "Validate myEmail is a valid email",
        "availableVariables": [{"name": "myEmail", "type": "System.String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "e47cbb71-e665-4a19-8b05-237f7171167e",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.IsMatch(myEmail, "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$")',  # noqa
    },
    {
        "currentExpression": "",
        "userRequest": "return noOfTests if is bellow 30 otherwise return 100",
        "availableVariables": [{"name": "noOfTests", "type": "System.Int32"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "4810b266-9661-4d32-b94a-8b64e3ea397f",
        "benchmarkExpression": 'If(noOfTests < 30, noOfTests.ToString(), "100")',
    },
    {
        "currentExpression": "",
        "userRequest": "A string that says hello",
        "availableVariables": [],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "15f56fc3-9e4c-4b4e-b7d4-ae593f1546fa",
        "benchmarkExpression": '"Hello"',
    },
    {
        "currentExpression": "",
        "userRequest": "considering ExtractedItems I want the cheapeast item considering top 3 ratings",
        "availableVariables": [{"name": "ExtractedItems", "type": "System.Data.DataTable2"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "b63cf712-5ded-43ad-9514-ceaf8487dcda",
        "benchmarkExpression": 'ExtractedItems.AsEnumerable().OrderBy(Function(row) Convert.ToDecimal(row("Price"))).ThenByDescending(Function(row) Convert.ToInt32(row("Rating"))).Take(3).Min(Function(row) row("ItemName").ToString())',  # noqa
    },
    {
        "currentExpression": "",
        "userRequest": 'format mail subject into uppercase and prepend "Subject" to it',
        "availableVariables": [{"name": "mail", "type": "Mail"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "Class Mail\n\tPublic Property Subject As String\n\tPublic Property ToAddress As String\n\tPublic Property Cc As String\n\tPublic Property Content As String\nEnd Class",  # noqa
        "id": "634d9157-26b5-4aef-9d8b-a83792153903",
        "benchmarkExpression": '"Subject: " & mail.Subject.ToUpper()',
    },
    {
        "currentExpression": "",
        "userRequest": "Extract with regex the domain from emailAdress, given firstname.lastname@domain format",
        "availableVariables": [{"name": "emailAdress", "type": "System.String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "b28afea3-ab0b-4bd1-90a2-78adeb6b9b6f",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.Match(emailAdress, "@(.*)$").Groups(1).Value',
    },
    {
        "currentExpression": "",
        "userRequest": "Sum of all elements in $_numberArray",
        "availableVariables": [{"name": "numberArray", "type": "List(Of Integer)"}],
        "expressionTypeDefinition": "Integer",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "392bc330-4bd3-431f-b9fb-b67ae1f7432e",
        "benchmarkExpression": "numberArray.Sum()",
    },
    {
        "currentExpression": "",
        "userRequest": "important information to generate an email to contant new leads from _out_GetLeadByID_1__curated_lead",
        "availableVariables": [
            {
                "name": "_out_GetLeadByID_1__curated_lead",
                "type": "UiPath.Salesforce.IntegrationService.Activities.SWEntities.Ca6b82d6d49634704b014ae6e8e398feb_curated_lead.Bundle.curated_lead",
            }
        ],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "namespace UiPath.Salesforce.IntegrationService.Activities.SWEntities.Ca6b82d6d49634704b014ae6e8e398feb_curated_lead.Bundle { interface curated_lead { string City; string Company; string Country; string Description; string Email; string FirstName; string Id; string LastName; string LeadSource; string MobilePhone; string OwnerId; string Phone; string PostalCode; string Rating; string Status; string Street; string Title; string Address_city; string Address_country; float? Address_geocodeAccuracy; double? Address_latitude; double? Address_longitude; string Address_postalCode; string Address_state; string Address_street; float? AnnualRevenue; string CleanStatus; string CompanyDunsNumber; string ConvertedAccountId; string ConvertedContactId; DateTimeOffset? ConvertedDate; string ConvertedOpportunityId; string CreatedById; DateTimeOffset? CreatedDate; string CurrentGenerators__c; string DandbCompanyId; DateTimeOffset? EmailBouncedDate; string EmailBouncedReason; string Fax; string GeocodeAccuracy; string IndividualId; string Industry; bool? IsConverted; bool? IsDeleted; bool? IsUnreadByOwner; string Jigsaw; string JigsawContactId; DateTimeOffset? LastActivityDate; string LastModifiedById; DateTimeOffset? LastModifiedDate; DateTimeOffset? LastReferencedDate; DateTimeOffset? LastViewedDate; float? Latitude; float? Longitude; bool? MarketingSystemSync__c; string MasterRecordId; string Name; long? NumberOfEmployees; float? NumberofLocations__c; string PhotoUrl; string Primary__c; string ProductInterest__c; string SICCode__c; string Salutation; string State; DateTimeOffset? SystemModstamp; string Website; string customLocale__c; } }",  # noqa
        "id": "f71a965a-49c8-41fd-b0fe-7f797e09ba60",
        "benchmarkExpression": '$"New lead: {_out_GetLeadByID_1__curated_lead.FirstName}, {_out_GetLeadByID_1__curated_lead.LastName}, {_out_GetLeadByID_1__curated_lead.Company}, {_out_GetLeadByID_1__curated_lead.Email}"',  # noqa
    },
    {
        "currentExpression": "",
        "userRequest": 'Date for next Monday in "June 24" format',
        "availableVariables": [],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "0773cff4-8248-4034-b7a1-3e03e88bb609",
        "benchmarkExpression": 'DateTime.Today.AddDays((8 - CInt(DateTime.Today.DayOfWeek)) Mod 7).ToString("MMMM dd")',
    },
    {
        "currentExpression": "",
        "userRequest": "Extract with Regex from userMail first name considering firstName.lastName@domain format",
        "availableVariables": [{"name": "userMail", "type": "String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "f6359515-b312-4c68-8181-19a1a9976815",
        "benchmarkExpression": 'System.Text.RegularExpressions.Regex.Match(userMail, "^[a-zA-Z0-9._%+-]+").Value',
    },
    {
        "currentExpression": "",
        "userRequest": "Get the items from MyList greater than 4",
        "availableVariables": [{"name": "ItemsList", "type": "List<int>"}],
        "expressionTypeDefinition": "List<int>",
        "expressionLanguage": "csharp",
        "additionalTypeDefinitions": "",
        "id": "13c624c4-8638-4bc1-b760-161b4b2ad0e3",
        "benchmarkExpression": "ItemsList.Where(i => i > 4).ToList()",
    },
    {
        "currentExpression": "",
        "userRequest": "Get the first $_primele largest from $_MyList",
        "availableVariables": [{"name": "primele", "type": "Integer"}, {"name": "MyList", "type": "List(Of Double)"}],
        "expressionTypeDefinition": "Short",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "ec7a7251-8fb3-4e87-aa6b-ee57b5e59797",
        "benchmarkExpression": "MyList.OrderByDescending(Function(x) x).Take(primele).Last().ToShort()",
    },
    {
        "currentExpression": "",
        "userRequest": "regex expression to get email address",
        "availableVariables": [{"name": "inputString", "type": "String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "caaec92d-cb9a-43e6-9840-2ff4ecad260d",
        "benchmarkExpression": "System.Text.RegularExpressions.Regex.Match(inputString, "
        "\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"  # noqa
        ").Value",
    },
    {
        "currentExpression": "",
        "userRequest": "largest number from $_MyList",
        "availableVariables": [{"name": "MyList", "type": "List(Of Double)"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "b11861d2-0465-4d34-ab48-88b599faaebe",
        "benchmarkExpression": "MyList.Max().ToString()",
    },
    {
        "currentExpression": "",
        "userRequest": "Check if $_stringToCheck contains the word apple",
        "availableVariables": [{"name": "stringToCheck", "type": "String"}],
        "expressionTypeDefinition": "Boolean",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "6e4b341c-a5c3-4db7-9699-a31bbb74c8a4",
        "benchmarkExpression": "stringToCheck.Contains(apple)",
    },
    {
        "currentExpression": "",
        "userRequest": "Date for the first Monday of the previous year",
        "availableVariables": [],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "59e57d0d-555f-4933-960d-fd8412683035",
        "benchmarkExpression": 'New DateTime(DateTime.Now.Year - 1, 1, 1).AddDays((DayOfWeek.Monday + 7 - New DateTime(DateTime.Now.Year - 1, 1, 1).DayOfWeek) Mod 7).ToString("yyyy-MM-dd")'
        ")",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the area of a rectangle with length $_length and width $_width",
        "availableVariables": [{"name": "length", "type": "Double"}, {"name": "width", "type": "Double"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "acf1eec8-79c2-46c2-8b25-f9b32c55b74c",
        "benchmarkExpression": "length * width",
    },
    {
        "currentExpression": "",
        "userRequest": "Concatenate the strings in $_stringList with a comma as a separator",
        "availableVariables": [{"name": "stringList", "type": "List(Of String)"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "886869c1-86a8-4efe-86f0-5b521346b014",
        "benchmarkExpression": "String.Join(" """, """ ", stringList)",
    },
    {
        "currentExpression": "",
        "userRequest": "Count the number of even numbers in $_numberList",
        "availableVariables": [{"name": "numberList", "type": "List(Of Integer)"}],
        "expressionTypeDefinition": "Integer",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "3fb6d8fb-4fdc-424e-bfc0-8e74e376f4e0",
        "benchmarkExpression": "numberList.Count(Function(n) n Mod 2 = 0)",
    },
    {
        "currentExpression": "",
        "userRequest": "Get the last 5 characters of $_inputString",
        "availableVariables": [{"name": "inputString", "type": "String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "281b6bdf-2535-4863-95b8-adb11eb77906",
        "benchmarkExpression": "inputString.Substring(Math.Max(0, inputString.Length - 5))",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the average value of $_valueList",
        "availableVariables": [{"name": "valueList", "type": "List(Of Double)"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "94cfe739-c449-4061-9333-e1ae040c3a8a",
        "benchmarkExpression": "valueList.Average()",
    },
    {
        "currentExpression": "",
        "userRequest": "Replace all spaces with underscores in $_text",
        "availableVariables": [{"name": "text", "type": "String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "49c65928-85d0-4a5a-97eb-36e47c772251",
        "benchmarkExpression": "text.Replace( , _)",
    },
    {
        "currentExpression": "",
        "userRequest": 'Get the current date and time as a string in the format "yyyy-MM-dd HH:mm:ss"',
        "availableVariables": [{"name": "", "type": ""}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "f8ae9482-e7e2-4144-9c60-f8ca5c6bc25c",
        "benchmarkExpression": "DateTime.Now.ToString(yyyy-MM-dd HH:mm:ss)",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the square root of $_number",
        "availableVariables": [{"name": "number", "type": "Double"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "8b0e1cbd-9d72-484f-8832-9d7f3269dcf4",
        "benchmarkExpression": "Math.Sqrt(number)",
    },
    {
        "currentExpression": "",
        "userRequest": """Concatenate the strings in $_stringList with a custom separator $_separator,
                        make each string uppercase, and return the result as a string.""",
        "availableVariables": [{"name": "stringList", "type": "List(Of String"}, {"name": "separator", "type": "String"}],
        "expressionTypeDefinition": "String",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "fe5caa49-3c04-47c7-867e-8b70c12b28a8",
        "benchmarkExpression": "String.Join(separator, stringList.Select(Function(s) s.ToUpper()))",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the sum of the squares of even numbers in $_numberList.",
        "availableVariables": [{"name": "numberList", "type": "List(Of Integer)"}],
        "expressionTypeDefinition": "Integer",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "5156099c-8c11-4825-958b-4f0e4e2e206f",
        "benchmarkExpression": "numberList.Where(Function(n) n Mod 2 = 0).Sum(Function(n) n * n)",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the product of the values in $_decimalList up to the index $_lastIndex (inclusive).",
        "availableVariables": [{"name": "decimalList", "type": "List(Of Decimal)"}, {"name": "lastIndex", "type": "Integer"}],
        "expressionTypeDefinition": "Decimal",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "f7c75b7d-6021-4361-8b9f-a876a700525b",
        "benchmarkExpression": "decimalList.Take(lastIndex + 1).Aggregate(Function(x, y) x * y)",
    },
    {
        "currentExpression": "",
        "userRequest": "Filter the strings in $_stringList that contain the substring $_substring and return the filtered list.",
        "availableVariables": [{"name": "stringList", "type": "List(Of String)"}, {"name": "substring", "type": "String"}],
        "expressionTypeDefinition": "List(Of String)",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "9a788797-14db-493c-a3f3-ceb9ebe07607",
        "benchmarkExpression": "stringList.Where(Function(s) s.Contains(substring)).ToList()",
    },
    {
        "currentExpression": "",
        "userRequest": "Calculate the average of the absolute values of the numbers in $_numberList and return the result as a double.",
        "availableVariables": [{"name": "numberList", "type": "List(Of Double)"}],
        "expressionTypeDefinition": "Double",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "218e5e5a-fb0b-4879-93d8-a326309f02cc",
        "benchmarkExpression": "numberList.Select(Function(n) Math.Abs(n)).Average()",
    },
    {
        "currentExpression": "",
        "userRequest": "Check if today's date is the third Monday of the month.",
        "availableVariables": [],
        "expressionTypeDefinition": "",
        "expressionLanguage": "vbnet",
        "additionalTypeDefinitions": "",
        "id": "4d439842-81f1-4fb5-8cbe-886bf58edff7",
        "benchmarkExpression": "DateTime.Today.DayOfWeek = DayOfWeek.Monday AndAlso DateTime.Today.Day / 7 = 2",
    },
]
