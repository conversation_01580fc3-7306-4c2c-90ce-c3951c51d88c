examples = [
    {
        "userRequest": "Write today's date in the format YYYY-MM-DD.",
        "currentExpression": "",
        "benchmarkExpression": "new Date().toISOString().split('T')[0]",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the sum of the first 100 natural numbers without any formula.",
        "currentExpression": "",
        "benchmarkExpression": "Array.from({ length: 100 }, (_, i) => i + 1).reduce((sum, n) => sum + n, 0).toString()",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the average of the squares of the first 5 natural numbers.",
        "currentExpression": "",
        "benchmarkExpression": "Array.from({ length: 5 }, (_, i) => i * i).reduce((sum, n) => sum + n, 0) / 5",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the factorial of 7.",
        "currentExpression": "",
        "benchmarkExpression": "Array.from({ length: 7 }, (_, i) => i + 1).reduce((acc, n) => acc * n, 1)",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first 10 Fibonacci numbers.",
        "currentExpression": "",
        "benchmarkExpression": "[0, 1].concat(Array.from({ length: 8 }, (_, i, arr) => arr[i] + arr[i + 1])).map(String)",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "Array<string>",
        "additionalTypeDefinitions": "declare namespace UiPath.Core.Activities { interface CurrentJobInfo { WorkflowName: string; ProcessName: string; ProcessVersion: string; RobotName: string; Key: string; TenantName: string; FolderName: string; UserEmail: string; PictureInPictureMode: UiPath.Core.Activities.PictureInPictureMode; } enum PictureInPictureMode { NotSupported, Main, PictureInPictureSession, PictureInPictureDesktop, } }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_2",
        "workflowId": "58c95a95-39c3-4320-9c84-5870f766b325",
    },
    {
        "userRequest": "Compute the first day of last month in YYYY-MM-DD format.",
        "currentExpression": "",
        "benchmarkExpression": "new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toISOString().split('T')[0]",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Check if the current year is a leap year.",
        "currentExpression": "",
        "benchmarkExpression": "((y = new Date().getFullYear()) => y % 4 === 0 && (y % 100 !== 0 || y % 400 === 0))().toString()",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Output all the letters in the alphabet in a single string.",
        "currentExpression": "",
        "benchmarkExpression": "Array.from({ length: 26 }, (_, i) => String.fromCharCode(97 + i)).join('')",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890ab",
    },
    {
        "userRequest": "Replace all 'a' with 'b' in 'banana'",
        "currentExpression": "",
        "benchmarkExpression": "'banana'.replace(/a/g, 'b')",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Reverse the order of words 'A lazy fox jumps over the fence'",
        "currentExpression": "",
        "benchmarkExpression": "'A lazy fox jumps over the fence'.split(' ').reverse().join(' ')",
        "availableVariables": [],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_3",
        "workflowId": "12345678-90ab-cdef-1234-567890abcdef",
    },
    {
        "userRequest": "Compute the average age of the users.",
        "currentExpression": "",
        "benchmarkExpression": "httpResponse.users.reduce((sum, user) => sum + user.age, 0) / httpResponse.users.length",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { users: User[]; }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "interface User { name: string; country: string; age: number; }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890",
    },
    {
        "userRequest": "Compute how many employees occupy the same position for all positions.",
        "currentExpression": "",
        "benchmarkExpression": "JSON.stringify(httpResponse.employees.reduce((acc, e) => ({ ...acc, [e.position]: (acc[e.position] || 0) + 1 }), {}))",
        "availableVariables": [{"name": "httpResponse", "type": "interface HttpResponse { employees: Employee[]; }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "interface Employee { name: string; position: string; }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_13",
        "workflowId": "890abcdef-1234-5678-90ab-cdef567890a",
    },
    {
        "userRequest": "Compute the maximum number from the first half of the input list.",
        "currentExpression": "",
        "benchmarkExpression": "Math.max(...l.slice(0, Math.floor(l.length / 2)))",
        "availableVariables": [{"name": "l", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Compute the sum of even numbers in the input list.",
        "currentExpression": "",
        "benchmarkExpression": "numbers.filter(n => n % 2 === 0).reduce((sum, n) => sum + n, 0)",
        "availableVariables": [{"name": "numbers", "type": "number[]"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Reverse the string received as input",
        "currentExpression": "",
        "benchmarkExpression": "str.split('').reverse().join('')",
        "availableVariables": [{"name": "str", "type": "string"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_6",
        "workflowId": "7890abcd-1234-5678-90ab-cdef12345678",
    },
    {
        "userRequest": "Find the common keys between two dictionaries.",
        "currentExpression": "",
        "benchmarkExpression": "Object.keys(dict1).filter(key => Object.keys(dict2).includes(key))",
        "availableVariables": [
            {"name": "dict1", "type": "interface Dict1 { A: number; B: number }"},
            {"name": "dict2", "type": "interface Dict2 { B: number; C: number }"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string[]",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-987654321",
    },
    {
        "userRequest": "You will receive 3 variables, a, b, c in a json. We know a>0. You need to solve e^(ax+b) = c. If there is no solution, return null.",
        "currentExpression": "",
        "benchmarkExpression": "input.c <= 0 ? null : (Math.log(input.c) - input.b) / input.a",
        "availableVariables": [
            {"name": "input", "type": "interface Input { a: number; b: number; c: number }"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a string as input. You should write all email addresses present in it.",
        "currentExpression": "",
        "benchmarkExpression": "input.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g) || []",
        "availableVariables": [
            {"name": "input", "type": "string"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "string[]",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "You will receive a list as input. Return the product of numbers on even positions.",
        "currentExpression": "",
        "benchmarkExpression": "l.filter((_, i) => i % 2 === 0).reduce((prod, num) => prod * num, 1)",
        "availableVariables": [
            {"name": "l", "type": "number[]"},
        ],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "number",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Compute the average of the numbers in the list.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.list.reduce((sum, n) => sum + n, 0) / inp.list.length }",
        "availableVariables": [{"name": "inp", "type": "interface List { list: number[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-98765432",
    },
    {
        "userRequest": "Output the unique elements from the intersection of two lists, $list1 and $list2",
        "currentExpression": "",
        "benchmarkExpression": "{ intersection: [...new Set(input.list1)].filter(x => new Set(input.list2).has(x)) }",
        "availableVariables": [{"name": "input", "type": "interface Input { list1: number[], list2: number[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { intersection: number[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210",
    },
    {
        "userRequest": "Compute a raised to the power of b.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: Array.from({ length: input.b }, () => input.a).reduce((acc, curr) => acc * curr, 1) }",
        "availableVariables": [{"name": "input", "type": "interface Input { a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210",
    },
    {
        "userRequest": "Output all numbers in the list $l that are between $a and $b, inclusive.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.l.filter(n => n >= inp.a && n <= inp.b) }",
        "availableVariables": [{"name": "inp", "type": "interface Input { l: number[], a: number, b: number }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: number[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Output all strings in the list $words that contain the substring $sub.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.words.filter(word => word.includes(inp.sub)) }",
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[], sub: string }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210ab",
    },
    {
        "userRequest": "Convert all strings to uppercase.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.words.map(word => word.toUpperCase()) }",
        "availableVariables": [{"name": "inp", "type": "interface Input { words: string[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: string[] }",
        "additionalTypeDefinitions": "",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210a",
    },
    {
        "userRequest": "Group items by category.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.items.reduce((acc, item) => ({ ...acc, [item.category]: [...(acc[item.category] || []), item] }), {}) }",
        "availableVariables": [{"name": "inp", "type": "interface Input { items: Item[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: { [key: string]: { name: string, category: string }[] } }",
        "additionalTypeDefinitions": "interface Item { name: string, category: string }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-9876543210a",
    },
    {
        "userRequest": "Combine first and last names into a full name.",
        "currentExpression": "",
        "benchmarkExpression": "{ result: inp.people.map(p => ({ full_name: p.first_name + ' ' + p.last_name })) }",
        "availableVariables": [{"name": "inp", "type": "interface Input { people: Person[] }"}],
        "expressionLanguage": "javascript",
        "expressionTypeDefinition": "interface Result { result: { full_name: string }[] }",
        "additionalTypeDefinitions": "interface Person { first_name: string, last_name: string }",
        "allVariablesIncluded": True,
        "activityTypeName": "System.Activities.Statements.WriteLine, System.Activities, Version=*******, Culture=neutral, PublicKeyToken=null",
        "activityIdRef": "WriteLine_7",
        "workflowId": "1234abcd-5678-90ef-ghij-98765432",
    },
]
