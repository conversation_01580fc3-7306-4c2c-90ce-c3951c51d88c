import os

import numpy as np
import torch
import torch.nn as nn
import transformers
from datasets import load_dataset
from peft.mapping import get_peft_model

# C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\peft\tuners\lora\config.py
from peft.tuners.lora.config import LoraConfig
from peft.utils.other import prepare_model_for_kbit_training

# from transformers import LlamaTokenizer, LlamaForCausalLM
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig

from experimental.workflow_generation.training import utils
from services.studio._text_to_workflow.utils import paths

# from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training


os.environ["WANDB_PROJECT"] = "wingman@plan"

# BASE_MODEL = "meta-llama/Meta-Llama-3-8B-Instruct"
# BASE_MODEL = "meta-llama/Llama-3.2-1b"
BASE_MODEL = "meta-llama/Meta-Llama-3.1-8B-Instruct"
tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)
tokenizer.pad_token_id = 0  # unk. we want this to be different from the eos token
tokenizer.padding_side = "left"


def generate_and_tokenize_prompt(data_point):
    input_ids = tokenizer.apply_chat_template(data_point["instruction"], tokenize=True)
    n_input_tokens = len(input_ids)

    input_output_ids = tokenizer.apply_chat_template(data_point["instruction"] + data_point["output"], tokenize=True)

    result = {}
    result["input_ids"] = input_output_ids
    result["labels"] = result["input_ids"].copy()

    # -100 is used for inhibiting the loss function on those tokens (the prompt)
    result["labels"][:n_input_tokens] = [-100] * n_input_tokens
    return result


def keep_only_transform(batch):
    keys_out = ["input_ids", "labels", "attention_mask"]
    for k in list(batch.keys()):
        if k not in keys_out:
            del batch[k]
    return batch


if __name__ == "__main__":
    print("Torch cuda available:", torch.cuda.is_available())
    # BASE_MODEL = "microsoft/Phi-3-medium-4k-instruct"
    print("BASE MODEL:", BASE_MODEL)
    CUTOFF_LEN = 4096
    MAX_STEPS = 50000
    USE_LORA = True
    BATCH_SIZE = 2
    MICRO_BATCH_SIZE = 1
    GRADIENT_ACCUMULATION_STEPS = BATCH_SIZE // MICRO_BATCH_SIZE
    # Quantization Config
    quantize = False
    if quantize:
        quant_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=False,
        )
        model = AutoModelForCausalLM.from_pretrained(
            BASE_MODEL,
            quantization_config=quant_config,
            # device_map="auto",
        )
    else:
        model = AutoModelForCausalLM.from_pretrained(
            BASE_MODEL,
            # device_map="auto",
        )

    print("model.device:", model.device)
    use_demo = True
    if not use_demo:
        test_folders = ["Portable_test_nodemo_datapoints.json"]
        train_folders = [
            "Portable_train_nodemo_datapoints.json",
            "Windows_prod2_nodemo_datapoints.json",
        ]
    else:
        test_folders = {
            "Portable": ["Portable_test_datapoints.json"],
            "Windows": ["Windows_test_datapoints.json"],
        }
        train_folders = [
            "Portable_train_datapoints.json",
            "Windows_train_datapoints.json",
        ]
    model_name = "plan-3.1-8B-pw-train-withdemo"

    is_debug = os.environ.get("DEBUG", None) is not None
    if is_debug:
        model_name = "plan_debug_2"
    is_sequence_generation = True

    OUTPUT_DIR = os.path.join(paths.get_workdir_path(), "experiments", model_name)
    test_dataset_paths = {
        platform: [os.path.join(utils.get_dataset_basefolder(is_sequence_generation), "plan_data", f) for f in test_folders[platform]]
        for platform in test_folders
    }
    if is_debug:
        test_dataset_raw = {platform: load_dataset("json", data_files=test_dataset_paths[platform], split="train[:4]") for platform in test_dataset_paths}
    else:
        test_dataset_raw = {platform: load_dataset("json", data_files=test_dataset_paths[platform], split="train") for platform in test_dataset_paths}
    test_dataset = {platform: test_dataset_raw[platform].map(generate_and_tokenize_prompt) for platform in test_dataset_raw}
    test_dataset = {platform: test_dataset[platform].map(keep_only_transform) for platform in test_dataset}

    train_dataset_paths = [os.path.join(utils.get_dataset_basefolder(is_sequence_generation), "plan_data", f) for f in train_folders]
    if is_debug:
        train_dataset_raw = load_dataset("json", data_files=train_dataset_paths, split="train[:10]")
    else:
        train_dataset_raw = load_dataset("json", data_files=train_dataset_paths, split="train")
    train_dataset = train_dataset_raw.map(generate_and_tokenize_prompt)
    train_dataset.set_transform(keep_only_transform)

    if USE_LORA:
        print("Use_lora")
        if quantize:
            model = prepare_model_for_kbit_training(model)
        config = LoraConfig(
            # r=64,
            # lora_alpha=128,
            # target_modules=["q_proj", "v_proj"],  # lora target modules
            r=8,
            lora_alpha=16,
            target_modules=["k_proj", "v_proj", "q_proj", "o_proj"],
            # r=128,
            # lora_alpha=32,
            # target_modules=["q_proj", "k_proj", "v_proj", "up_proj", "down_proj", "o_proj", "gate_proj"],
            # target_modules=["o_proj", "qkv_proj"],
            lora_dropout=0.05,
            bias="none",
            task_type="CAUSAL_LM",
        )
        model = get_peft_model(model, config)
        model.print_trainable_parameters()

    from services.studio._text_to_workflow.utils.embedding_model import RerankingModel
    from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import planning_edit_score

    cross_encoder_model = RerankingModel(utils.config["rerank_model"]).model
    loss_fn = nn.CrossEntropyLoss()

    def compute_metrics(eval_pred):
        # Unpack the eval_pred to include input_ids along with predictions and labels
        predictions, labels, input_ids = (
            eval_pred.predictions,
            eval_pred.label_ids,
            eval_pred.inputs,
        )
        # Convert predictions and labels to tensors (if not already)
        labels = torch.tensor(labels)
        input_ids = torch.tensor(input_ids)
        predictions = torch.tensor(predictions)

        # Prepare lists to store generated texts and reference texts
        scores = []

        # Loop over each example in the batch
        for i in range(labels.shape[0]):
            # Get the current batch element's labels and input_ids
            current_labels = labels[i]
            current_input_ids = input_ids[i]

            # Mask to find where labels == -100 (the part we want to generate from)
            mask_for_generation = current_labels == -100
            mask_for_generation = mask_for_generation & (current_input_ids != 0)
            # Stop at the first valid token (non -100)
            for j in range(1, len(mask_for_generation)):
                if current_labels[j] != -100:
                    mask_for_generation[j:] = 0  # Stop the mask after the first valid token
                    break
            mask_for_comparison = current_labels != -100

            # Extract the part of the input where labels are masked (-100)
            inputs_for_generation = torch.masked_select(current_input_ids, mask_for_generation).unsqueeze(0).cuda()
            # input_prompt = tokenizer.decode(inputs_for_generation[0], skip_special_tokens=False)

            # Generate text based on this prompt
            generated_tokens = model.generate(
                input_ids=inputs_for_generation,
                max_length=1024,  # Set maximum length for generated text
                min_length=12,  # Set minimum length for generated text
                do_sample=False,
                num_beams=1,  # Use beam search for better quality
                # num_beams=5,                  # Use beam search for better quality
                # early_stopping=True,          # Stop when the model predicts the end token
                # no_repeat_ngram_size=3,       # Prevent repetition of n-grams
                # length_penalty=1.2,           # Encourage longer sequences
                pad_token_id=tokenizer.pad_token_id,
            )

            generated_text = tokenizer.decode(generated_tokens[0], skip_special_tokens=True)

            # Extract the reference labels (actual expected text) where labels != -100
            reference_labels = torch.masked_select(current_labels, mask_for_comparison)

            # Decode the reference labels
            decoded_reference_labels = tokenizer.decode(reference_labels, skip_special_tokens=True)
            label_plan = decoded_reference_labels[len("assistant\n\n") :]
            predicted_plan = generated_text[generated_text.rfind("assistant\n\n") :][len("assistant\n\n") :]

            print("label_plan:\n", label_plan)
            print("predicted_plan:\n", predicted_plan)

            try:
                score = planning_edit_score(label_plan, predicted_plan, cross_encoder_model=cross_encoder_model)
            except Exception:
                score = 0.0
            scores.append(score)
        return {"plan_edit_score": np.mean(scores)}

    training_arguments = transformers.Seq2SeqTrainingArguments(
        per_device_train_batch_size=MICRO_BATCH_SIZE,
        gradient_accumulation_steps=GRADIENT_ACCUMULATION_STEPS,
        warmup_steps=100,
        max_steps=MAX_STEPS,
        learning_rate=3e-4,  # find value for re-finetune
        fp16=False,
        logging_first_step=True,
        logging_steps=20,
        optim="adamw_torch",
        # optim="sgd",
        # optim="adafactor",
        # optim="adamw_bnb_8bit", # not enough memory to run on single A100 for llama 13B
        # optimizers ['adamw_hf', 'adamw_torch', 'adamw_torch_fused', 'adamw_torch_xla', 'adamw_apex_fused', 'adafactor', 'adamw_anyprecision', 'sgd', 'adagrad', 'adamw_bnb_8bit', 'adamw_8bit', 'lion_8bit', 'lion_32bit', 'paged_adamw_32bit', 'paged_adamw_8bit', 'paged_lion_32bit', 'paged_lion_8bit']
        # do_eval=False,  # does not seem to work?
        weight_decay=1e-5,
        # adam_beta2=0.95,
        # adam_epsilon=1e-5,
        lr_scheduler_type="cosine",
        evaluation_strategy="steps",
        eval_steps=5 if is_debug else 500,
        save_strategy="steps",
        save_steps=5 if is_debug else 500,
        output_dir=OUTPUT_DIR,
        save_total_limit=3,
        load_best_model_at_end=True,
        remove_unused_columns=False,
        eval_accumulation_steps=1,
        per_device_eval_batch_size=MICRO_BATCH_SIZE,
        report_to="wandb",
        run_name=model_name,
        include_inputs_for_metrics=True,
        # predict_with_generate=True,
        metric_for_best_model="Portable_plan_edit_score",
    )

    data_collator = transformers.DataCollatorForSeq2Seq(tokenizer, pad_to_multiple_of=8, return_tensors="pt", padding=True)

    trainer = transformers.Seq2SeqTrainer(
        model=model,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        args=training_arguments,
        data_collator=data_collator,
        compute_metrics=compute_metrics,
    )
    model.config.use_cache = False
    trainer.train(resume_from_checkpoint=False)
    model.save_pretrained(OUTPUT_DIR)
