import os

import yaml

from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.workflow_generation import workflow_generation_task

text2workflow = workflow_generation_task.WorkflowGenerationTask(config_name="prompt.yaml").load()
config = text2workflow.config


def get_yaml_files(directory: str):
    yaml_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".yaml") or file.endswith(".yml"):
                file_path = os.path.join(root, file)
                yaml_files.append(file_path)
    return yaml_files


def get_dataset_basefolder(generation_mode: ActivitiesGenerationMode = "workflow"):
    datafolder = "WorkflowGeneration"
    if generation_mode == "sequence":
        datafolder = "SequenceGeneration"

    folder = os.path.join(paths.get_autopilot_samples_dataset_path(), datafolder)
    return folder


def get_workflow(file_path: str):
    with open(file_path, "r") as f:
        wf = yaml.load(f, Loader=yaml.FullLoader)
    return wf
