import json
import os
from typing import List

import numpy as np
import tqdm
import yaml

from experimental.workflow_generation.training import utils
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, SubsetName, TargetFramework
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths


def json_serialize(obj):
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    if isinstance(obj, np.floating):
        return float(obj)
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    return obj


def build_demonstrations_for_query(
    query,
    target_framework: TargetFramework,
    demonstrations_count: int = 5,
    generation_mode: ActivitiesGenerationMode = "workflow",
):
    print("build demo...")
    text2workflow = utils.text2workflow
    if generation_mode == "sequence":
        demo_retriever = text2workflow.sequence_demo_retriever
    else:
        demo_retriever = text2workflow.workflow_demo_retriever

    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    query_embedding = embedding_model.encode(query, instruction_set="icl", instruction_type="query")
    connection_embeddings = np.mean([query_embedding], axis=0)
    ignored_namespaces = set()
    ignored_identifiers = set()

    demonstrations_count_dict: dict[SubsetName, int] = {
        "train": demonstrations_count,
        "static": 1,
        "uia": 1,
    }

    demonstrations = demo_retriever.get_relevant(
        query,
        query_embedding,
        connection_embeddings,
        demonstrations_count_dict,
        target_framework,
        ignored_namespaces=ignored_namespaces,
        ignored_identifiers=ignored_identifiers,
    )
    demonstrations["train"] = [demo for demo in demonstrations["train"] if demo["description"] != query]
    return demonstrations


def build_demonstrations(
    file_path: str,
    split: str,
    demonstrations_count: int = 5,
    generation_mode: ActivitiesGenerationMode = "workflow",
):
    with open(file_path, "r") as f:
        wf = yaml.load(f, Loader=yaml.FullLoader)

    query = wf["description"]
    if "target_framework" in wf:
        target_framework = wf["target_framework"]
    else:
        if "Windows" in file_path:
            target_framework = "Windows"
        else:
            target_framework = "Portable"

    if split == "train":
        demonstrations_count += 1

    demonstrations = build_demonstrations_for_query(
        query,
        target_framework,
        demonstrations_count=demonstrations_count,
        generation_mode=generation_mode,
    )
    return demonstrations


def save_demonstrations_to_json(directory: str, split: str, generation_mode: ActivitiesGenerationMode):
    """
    Save demonstrations from YAML files to JSON files in a new directory.

    :param directory: The directory containing the YAML files.
    """
    yaml_files = utils.get_yaml_files(directory)
    output_directory = directory + "_demos"

    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    for yaml_file in tqdm.tqdm(yaml_files):
        json_file_name = os.path.splitext(os.path.basename(yaml_file))[0] + ".json"
        json_file_path = os.path.join(output_directory, json_file_name)

        if os.path.exists(json_file_path):
            print(json_file_name, " exists, continue")
            continue
        demonstrations = build_demonstrations(
            yaml_file,
            split=split,
            generation_mode=generation_mode,
        )

        with open(json_file_path, "w") as json_file:
            json.dump(demonstrations, json_file, indent=2, default=json_serialize)


metadatas = {}
for target in ["Portable", "Windows"]:
    with open(
        os.path.join(paths.get_workflow_generation_dataset_path(), target, "metadata.yaml"),
        "r",
    ) as f:
        metadatas[target] = yaml.load(f, Loader=yaml.FullLoader)


def get_save_demonstration(
    filepath: str,
    target: TargetFramework,
    split: str,
    generation_mode: ActivitiesGenerationMode = "workflow",
):
    filename = os.path.basename(filepath)
    base_folder = utils.get_dataset_basefolder(generation_mode)
    demo_path = os.path.join(base_folder, target, split + "_demos", os.path.splitext(filename)[0] + ".json")

    print(demo_path)
    with open(demo_path, "r") as f:
        demos = json.load(f)
    for demo in demos["train"]:
        # demo_name = os.path.splitext(demo["identifier"])[0]
        demo_name = demo["identifier"].split("/")[-1]
        metadata = metadatas[demo["target_framework"]]
        if demo_name in metadata:
            demo["metadata"] = metadata[demo_name]
        else:
            if split != "prod":
                print(target, split, demo_name, "not found in metadata")

    for demo in demos["static"] + demos["uia"]:
        # TODO: remove this after the metadata is updated in static and uia demos
        demo["metadata"] = {"query-current": demo["description"]}

    return demos


if __name__ == "__main__":
    use_prod = False
    generation_modes: List[ActivitiesGenerationMode] = ["workflow", "sequence"]
    supported_targets: list[TargetFramework] = ["Portable", "Windows"]

    for generation_mode in generation_modes:
        for target in supported_targets:
            # target = "Windows"
            splits = ["test", "train"]
            if target == "Windows" and generation_mode == "sequence" and use_prod:
                splits = splits + ["prod"]

            # splits = ["test", "train"]

            for split in splits:
                folder = os.path.join(
                    utils.get_dataset_basefolder(generation_mode),
                    target,
                    split,
                )
                print("Build and save demonstrations for:", folder)
                save_demonstrations_to_json(folder, split, generation_mode=generation_mode)
