import os
import re
import sqlite3

import pandas as pd

from services.studio._text_to_workflow.common import connections_loader
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.workflow_generation import workflow_generation_task
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import planning_edit_score

text2workflow = workflow_generation_task.WorkflowGenerationTask(config_name="prompt.yaml").load()
tenant_id, connections = connections_loader.get_connections_data()

con = sqlite3.connect(os.path.join(paths.get_embeddings_path(), "embeddings.db"))
# Read sqlite query results into a pandas DataFrame
activity_df = pd.read_sql_query("SELECT * from ActivityEmbeddings", con)


def get_connector_activity(activityTypeId):
    # from the UiPathActivityTypeId, get the activity class name which is more representative
    # for example, for UiPathActivityTypeId = ..., the activity class name is "SendOutlookMail"
    try:
        typedf = str(activity_df[activity_df.UiPathActivityTypeId == activityTypeId].TypeDefinition.values[0])
        # print(typedf)
        match = re.search(r".*class (\w+): Activity.*", typedf)
        if match:
            function_name = match.group(1)
            return function_name
        else:
            raise ValueError("can not parse typedf")
    except Exception:
        print("type id not found:", activityTypeId)
        return "None"


def get_activity_fullname(a):
    try:
        if "ConnectorTriggerActivity" in a["activity"]:
            activity_type_id = a["params"]["UiPathActivityTypeId"]
            return a["activity"].split()[0] + ":" + get_connector_activity(activity_type_id)
        elif "ConnectorActivity" in a["activity"]:
            activity_type_id = a["params"]["UiPathActivityTypeId"]
            return a["activity"].split()[0] + ":" + get_connector_activity(activity_type_id)
        else:
            activity = a["activity"]
            if activity.endswith("`1"):
                activity = activity[:-2]
                return activity
            return a["activity"]
    except Exception as err:
        raise ValueError("exception") from err


def traverse_activities(activities, prefix="", start_number=1):
    # from a workflow process, get the activities in a tree format
    result = []
    for i, activity in enumerate(activities, start=start_number):
        current_prefix = f"{prefix}{i}."
        result.append(f"{current_prefix} {get_activity_fullname(activity)}")

        # Check for sub-steps in 'Handler'
        if "params" in activity:
            if "Body" in activity["params"] and "Handler" in activity["params"]["Body"]:
                result.extend(
                    traverse_activities(
                        activity["params"]["Body"]["Handler"],
                        prefix=" " + current_prefix,
                    )
                )
            if "Handler" in activity["params"]:
                result.extend(traverse_activities(activity["params"]["Handler"], prefix=" " + current_prefix))
            if "Then" in activity["params"]:
                result.extend(traverse_activities(activity["params"]["Then"], prefix=" " + current_prefix))
            if "Else" in activity["params"]:
                current_prefix = f"{prefix}{i + 1}."
                result.append(f"{current_prefix} Else")
                result.extend(traverse_activities(activity["params"]["Else"], prefix=" " + current_prefix))

    return result


def get_workflow_activities_tree(workflow, mode):
    result = []
    if mode == "workflow":
        trigger = workflow["trigger"]
        trigger_activity = get_activity_fullname(trigger)

        # Add the trigger
        result.append(f"1. {trigger_activity}")

        # Add the workflow activities
        result.extend(traverse_activities(workflow["workflow"], prefix="", start_number=2))
    else:
        # print("workflow:", workflow)
        result.extend(traverse_activities(workflow, prefix="", start_number=1))

    return "\n".join(result)


def get_plan_activities_tree(
    plan,
    target_framework,
    text2workflow=text2workflow,
    connections=connections,
    k=5,
    mode="workflow",
):
    embedding_model = ModelManager().get_embedding_model("activities_embedding_model")
    ignored_namespaces = set()
    steps, retrieval_usage = text2workflow.embed_workflow_plan(plan, embedding_model)
    pred_activities = []
    ignored_activities = set()
    step_start_idx = 0
    if mode == "workflow":
        step_start_idx = 1
        triggers = text2workflow.activities_retriever.search(
            steps[0],
            connections,
            target_framework,
            "trigger",
            ignored_namespaces,
            ignored_activities,
            k=k,
        )
        trigger_names = [extract_trigger_name(trigger) for trigger in triggers]
        pred_activities.append(trigger_names)
    for step in steps[step_start_idx:]:
        relevant_activities = text2workflow.activities_retriever.search(
            step,
            connections,
            target_framework,
            "activity",
            ignored_namespaces,
            ignored_activities,
            k=k,
        )
        activitie_names = [extract_activity_name(activity) for activity in relevant_activities]
        pred_activities.append(activitie_names)
    return replace_plan_with_activities(plan, pred_activities)


def extract_trigger_name(trigger):
    if "ConnectorTrigger" in trigger["fullActivityId"]:
        trigger_name = trigger["fullActivityName"] + ":" + trigger["className"]
    else:
        trigger_name = trigger["fullActivityId"]
    return trigger_name


def extract_activity_name(activity):
    activity_name = activity["fullActivityName"]
    if "ConnectorActivity" in activity["fullActivityName"]:
        activity_name = activity_name + ":" + activity["className"]
    return activity_name


def get_activities_list_str(relevant_activities):
    return ",".join(relevant_activities)


def replace_plan_with_activities(plan: str, list_relevant_activities: list[list]) -> str:
    # Split the plan into lines
    plan_lines = plan.strip().split("\n")

    # Initialize the result list to store the new plan
    updated_plan = []

    # Keep a count of activities replaced
    activity_index = 0

    # Iterate over each line in the plan
    for line in plan_lines:
        line = line.strip()

        # Check if the line starts with a number (indicating it's a step in the plan)
        if line.strip()[0].isdigit() and activity_index < len(list_relevant_activities):
            # Replace the step with the corresponding activity
            number, text = line.split(" ", 1)
            if text.lower() == "else":
                updated_plan.append(line)
            else:
                updated_plan.append(f"{number} {get_activities_list_str(list_relevant_activities[activity_index])}")
            activity_index += 1
        else:
            # If the line is not a numbered step, keep it as it is
            updated_plan.append(line)

    # Join the updated plan lines back into a single string
    return "\n".join(updated_plan)


def plan_score(plan, workflow, generation_mode):
    target_framework = workflow["target_framework"]
    plan = plan.strip()
    pred_act_tree = get_plan_activities_tree(plan, target_framework, k=5, mode=generation_mode)
    process = workflow["process_sequence"] if generation_mode == "sequence" else workflow["process"]
    wf_tree = get_workflow_activities_tree(process, mode=generation_mode)
    # print("pred_act_tree:", pred_act_tree)
    # print("wf_tree:", wf_tree)
    score = planning_edit_score(wf_tree, pred_act_tree)
    # print("score:", score)
    return score


def plan_vs_label_score(label_plan, pred_plan, target_framework):
    label_plan = label_plan.strip()
    pred_plan = pred_plan.strip()
    pred_act_tree = get_plan_activities_tree(pred_plan, target_framework, k=1)
    label_act_tree = get_plan_activities_tree(label_plan, target_framework, k=1)
    print("pred_act_tree:", pred_act_tree)
    print("label_act_tree:", label_act_tree)
    return planning_edit_score(label_act_tree, pred_act_tree)
