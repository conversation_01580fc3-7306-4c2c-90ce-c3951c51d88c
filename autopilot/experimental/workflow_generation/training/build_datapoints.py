import glob
import itertools
import json
import os

import tqdm
import yaml

from experimental.workflow_generation.training import utils
from experimental.workflow_generation.training.build_demonstrations import (
    get_save_demonstration,
    metadatas,
)
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, TargetFramework
from services.studio._text_to_workflow.common.walkers import PlanBuilderWithCategories
from services.studio._text_to_workflow.common.workflow import Workflow


def get_plan_with_category(workflow, target_framework):
    wf = Workflow(description="", plan="", lmyaml=workflow)
    plan_builder = PlanBuilderWithCategories(target_framework=target_framework, force_trigger_if_missing=False)
    return plan_builder.build(wf)


METADATA_PLAN_TYPE = "plan-current"


def build_datapoint(
    filepath: str,
    target: TargetFramework,
    split: str,
    config: dict,
    has_demo: bool,
    generation_mode: ActivitiesGenerationMode,
    use_categories: bool = False,
):
    with open(filepath, "r") as f:
        wf = yaml.load(f, Loader=yaml.FullLoader)
    filename = os.path.splitext(os.path.basename(filepath))[0]

    metadata = metadatas[target]

    # SYSTEM MESSAGE
    if generation_mode == "sequence":
        system_message = config["plan_system_template"]["sequence"].format(additional_instructions="")
    else:
        system_message = config["plan_system_template"]["workflow"]

    messages = [
        {"role": "system", "content": system_message},
    ]

    # demonstration part
    if has_demo:
        demos = get_save_demonstration(filepath, target, split, generation_mode)
        for demo in itertools.chain(*demos.values()):
            if generation_mode == "sequence":
                demo_user_query = demo["description_sequence"]
                if use_categories:
                    demo_existing_plan = get_plan_with_category(demo["process_existing"], target)
                    demo_sequence_plan = get_plan_with_category({"workflow": demo["process_sequence"]}, target)
                else:
                    demo_existing_plan = demo["plan_existing"]
                    demo_sequence_plan = demo["plan_sequence"]
                demo_user_message = config["plan_user_template"]["sequence"].format(existing_plan=demo_existing_plan, query=demo_user_query)
                demo_assistant_message = demo_sequence_plan
            else:
                # demo coming from train and test must have metadata
                if use_categories:
                    demo_plan = get_plan_with_category(demo["process"], target)
                else:
                    assert "metadata" in demo
                    demo_plan = demo["metadata"][METADATA_PLAN_TYPE]
                demo_user_query = demo["metadata"]["query-current"]

                demo_user_message = config["plan_user_template"]["workflow"].format(query=demo_user_query)
                demo_assistant_message = demo_plan

            messages.append({"role": "user", "content": demo_user_message})
            messages.append({"role": "assistant", "content": demo_assistant_message})

    # user message part
    if generation_mode == "sequence":
        if use_categories:
            existing_plan = get_plan_with_category(wf["process_existing"], target)
        else:
            existing_plan = wf["plan_existing"]
        user_query = wf["description_sequence"]
        user_message = config["plan_user_template"]["sequence"].format(existing_plan=existing_plan, query=user_query)
        if use_categories:
            assistant_message = get_plan_with_category({"workflow": wf["process_sequence"]}, target)
        else:
            assistant_message = wf["plan_sequence"]
    else:
        if split in ("train", "test"):
            assert filename in metadata
            if use_categories:
                assistant_message = get_plan_with_category(wf["process"], target)
            else:
                assistant_message = metadata[filename][METADATA_PLAN_TYPE]
            # use query-current for all train, test split
            user_query = metadata[filename]["query-current"]
        else:
            assert split == "prod"
            raise NotImplementedError("Worflow Prod not supported yet")

        user_message = config["plan_user_template"]["workflow"].format(query=user_query)

    messages.append({"role": "user", "content": user_message})

    return {
        "instruction": messages,
        "output": [{"role": "assistant", "content": assistant_message}],
        "query": user_query,
        "filepath": filepath,
    }


def build_finetune_dataset(
    target: TargetFramework,
    split: str,
    config: dict,
    has_demo: bool = True,
    generation_mode: ActivitiesGenerationMode = "workflow",
    use_categories: bool = False,
):
    dataset_basefolder = utils.get_dataset_basefolder(generation_mode)
    dataset_path = os.path.join(dataset_basefolder, target, split)
    print("build_finetune_dataset:", dataset_path)
    yaml_files = utils.get_yaml_files(dataset_path)
    if has_demo:
        all_demo_files = glob.glob(os.path.join(dataset_basefolder, target, split + "_demos", "*.json"))
        all_demo_files = set([os.path.splitext(os.path.basename(f))[0] for f in all_demo_files])
        yaml_files = [f for f in yaml_files if os.path.splitext(os.path.basename(f))[0] in all_demo_files]
    print("number of files has demo:", len(yaml_files))
    data = []
    import random

    random.shuffle(yaml_files)
    for filepath in tqdm.tqdm(yaml_files):
        datapoint = build_datapoint(filepath, target, split, config, has_demo, generation_mode, use_categories)
        data.append(datapoint)

    if use_categories:
        if has_demo:
            datapoints_filepath = f"{target}_{split}_withcat_datapoints.json"
        else:
            datapoints_filepath = f"{target}_{split}_withcat_nodemo_datapoints.json"
    else:
        if has_demo:
            datapoints_filepath = f"{target}_{split}_datapoints.json"
        else:
            datapoints_filepath = f"{target}_{split}_nodemo_datapoints.json"

    datapoints_folder = os.path.join(dataset_basefolder, "plan_data")
    if not os.path.exists(datapoints_folder):
        os.makedirs(datapoints_folder, exist_ok=True)

    datapoints_path = os.path.join(datapoints_folder, datapoints_filepath)
    print("datapoints_path:", datapoints_path)
    with open(datapoints_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2)
    print("Done")


if __name__ == "__main__":
    include_prod = True
    generation_modes: list[ActivitiesGenerationMode] = ["workflow", "sequence"]
    supported_targets: list[TargetFramework] = ["Windows", "Portable"]
    use_categories = True
    for generation_mode in generation_modes:
        for target in supported_targets:
            splits = ["test", "train"]
            if target == "Windows" and generation_mode == "sequence" and include_prod:
                splits = splits + ["prod"]

            # splits = ["prod"]
            config = utils.config
            for split in splits:
                print("Build plan dataset for:", target, split)
                build_finetune_dataset(
                    target,
                    split,
                    config["prompt"],
                    has_demo=True,
                    generation_mode=generation_mode,
                    use_categories=use_categories,
                )
