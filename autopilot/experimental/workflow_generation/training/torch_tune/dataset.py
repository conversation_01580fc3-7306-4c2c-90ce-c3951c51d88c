import json
import os
from dataclasses import dataclass

import numpy as np
import torch
from torch.nn.utils.rnn import pad_sequence
from torch.utils.data import DataLoader, Dataset, DistributedSampler
from torchtune import data, datasets
from torchtune.data import CROSS_ENTROPY_IGNORE_IDX
from torchtune.models.llama3 import llama3_tokenizer
from torchtune.modules import tokenizers


@dataclass
class DatasetFileItem:
    filename: str
    category: str = ""
    weight: int = 1


class PlanningChatDataset(Dataset):
    def __init__(
        self,
        dataset_files: list[DatasetFileItem],
        tokenizer: tokenizers.ModelTokenizer,
        max_seq_len: int,
        tokenize_messages_kwargs: dict | None = None,
    ) -> None:
        self.tokenizer = tokenizer
        self.max_seq_len = max_seq_len
        self.dataset_files = dataset_files
        self.datapoints = []
        self.tokenize_messages_kwargs = tokenize_messages_kwargs or {}
        self.load_datapoints()

    def __len__(self):
        return len(self.datapoints)

    def __getitem__(self, index: int) -> dict[str, list[int]]:
        messages = self._convert_to_messages(self.datapoints[index])
        return self._prepare_messages(messages, self.datapoints[index])

    def load_datapoints(self):
        for dataset_file_item in self.dataset_files:
            datapoints = json.load(open(dataset_file_item.filename, "r")) * dataset_file_item.weight
            if os.environ.get("DEBUG", False):
                datapoints = datapoints[:10]
            for datapoint in datapoints:
                datapoint["category"] = dataset_file_item.category
            self.datapoints.extend(datapoints)

    def _convert_to_messages(self, sample: dict) -> list[data.Message]:
        messages: list[data.Message] = []

        for message_item in sample["instruction"]:
            role = message_item["role"]
            content = message_item["content"]
            messages.append(data.Message(role=role, content=content, masked=True))

        for message_item in sample["output"]:
            messages.append(
                data.Message(
                    role=message_item["role"],
                    content=message_item["content"],
                    masked=message_item["role"] != "assistant",
                )
            )
        return messages

    def _prepare_messages(self, messages: list[data.Message], datapoint: dict) -> dict[str, list[int]]:
        data.validate_messages(messages)
        # add_end_tokens
        tokens, mask = self.tokenizer.tokenize_messages(messages, **self.tokenize_messages_kwargs)  # type: ignore
        # labels = list(np.where(mask, data.CROSS_ENTROPY_IGNORE_IDX, tokens))  # Wherever mask == True, set to CROSS_ENTROPY_IGNORE_IDX. Otherwise keep as tokens
        labels = np.copy(tokens)
        masking_index = np.argmin(mask)
        labels[:masking_index] = data.CROSS_ENTROPY_IGNORE_IDX
        labels = labels.tolist()
        assert len(tokens) == len(labels)
        return {
            "tokens": tokens,
            "labels": labels,
            "filepath": datapoint["filepath"],
            "category": datapoint["category"],
        }


def planning_chat_dataset(
    dataset_files: list[DatasetFileItem],
    tokenizer: tokenizers.ModelTokenizer,
    max_seq_len: int,
    packed: bool,
    limit: int | None = None,
    repeat: int | None = None,
    train_on_input: bool = False,
    **tokenize_messages_kwargs,
):
    ds = PlanningChatDataset(
        dataset_files,
        tokenizer,
        max_seq_len,
        **tokenize_messages_kwargs,
    )
    if packed:
        ds = datasets.PackedDataset(ds, max_seq_len=max_seq_len, padding_idx=tokenizer.pad_id)  # type: ignore
    return ds


def get_dataset_item(filename: str, category: str = "", weight: int = 1):
    return DatasetFileItem(filename=filename, category=category, weight=weight)


def padded_collate(
    batch: list[dict[str, torch.Tensor]],
    padding_idx: int = 0,
    ignore_idx: int = CROSS_ENTROPY_IGNORE_IDX,
) -> dict[str, torch.Tensor]:
    input_ids = pad_sequence(
        [torch.tensor(x["tokens"]) for x in batch],
        batch_first=True,
        padding_value=padding_idx,
    )
    labels = pad_sequence(
        [torch.tensor(x["labels"]) for x in batch],
        batch_first=True,
        padding_value=ignore_idx,
    )

    # set other batch information for evaluation
    out = {"tokens": input_ids, "labels": labels}
    for key in batch[0]:
        if key not in list(out.keys()):
            out[key] = [x[key] for x in batch]
    return out


def test_dataset():
    llama_tokenizer = llama3_tokenizer("/workspace/data/Llama-3.1-8B/original/tokenizer.model")
    train_files = [
        DatasetFileItem(filename="/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Portable_test_datapoints.json"),
        DatasetFileItem(filename="/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Portable_train_datapoints.json"),
        DatasetFileItem(filename="/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_prod_datapoints.json"),
        DatasetFileItem(filename="/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_test_datapoints.json"),
        DatasetFileItem(filename="/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_train_datapoints.json"),
    ]

    llama_tokenizer = llama3_tokenizer("/workspace/data/Llama-3.1-8B/original/tokenizer.model")
    # PlanningChatDataset(tokenizer=llama_tokenizer, max_seq_len=16000,dataset_files= train_files)
    ds = planning_chat_dataset(train_files, llama_tokenizer, max_seq_len=16000, packed=False)
    sampler = DistributedSampler(ds, num_replicas=1, rank=0, shuffle=True, seed=42)
    dataloader = DataLoader(dataset=ds, batch_size=2, sampler=sampler, collate_fn=padded_collate)
    for _, batch in enumerate(dataloader):
        print(batch)


if __name__ == "__main__":
    test_dataset()
