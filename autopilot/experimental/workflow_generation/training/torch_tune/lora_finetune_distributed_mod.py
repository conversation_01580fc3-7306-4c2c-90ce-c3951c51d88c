# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the BSD-style license found in the
# LICENSE file in the root directory of this source tree.

import functools
import os
import sys
import time
import warnings
from typing import Any, Dict, Optional

import numpy as np
import torch
import torch.distributed
import torch.distributed.elastic.multiprocessing.errors
import torch.nn
from omegaconf import DictConfig
from torch import nn
from torch.distributed.fsdp import FullyShardedDataParallel
from torch.optim import Optimizer
from torch.utils.data import DataLoader, DistributedSampler
from torchtune import config, modules, training, utils
from torchtune.modules.peft import (
    DoRALinear,
    LoRALinear,
    get_adapter_params,
    get_lora_module_names,
    get_merged_lora_ckpt,
    set_trainable_params,
    validate_missing_and_unexpected_for_lora,
)
from tqdm import tqdm

from services.studio._text_to_workflow.workflow_generation.training.torch_tune.dataset import (
    padded_collate,
)
from services.studio._text_to_workflow.workflow_generation.training.torch_tune.debug_utils import (
    setup_debugger,
)
from services.studio._text_to_workflow.workflow_generation.training.torch_tune.metrics import (
    compute_batch_ted_metric,
)

# from services.studio._text_to_workflow.workflow_generation.training.torch_tune.kv_caching import local_kv_cache

if os.environ.get("DEBUG", False):
    setup_debugger()


log = utils.get_logger("DEBUG")


class LoRAFinetuneRecipeDistributed:
    """
    Distributed LoRA finetuning recipe for dense transformer-based LLMs such as Llama2. This recipe supports
    distributed training and can be run on a single node (1 to 8 GPUs).

    Features:
        - FSDP. Supported using PyTorch's FSDP APIs. DDP is currently not supported. Traning on CPU is not
            supported.

        - Activation Checkpointing. This can be controlled using the ``activation_checkpoin
        ting``
            flag. Activation checkpointing helps reduce the memory footprint since we no longer keep
            activations in memory and instead recompute them during the backward pass. This is especially
            helpful for larger batch sizes when you're memory constrained. But these savings in memory
            come at the cost of training performance. In most cases training can slow-down quite a bit as
            a result of this activation recomputation.

        - Precision. Full fp32 and bf16 training are supported. Precision is controlled using the ``dtype``
            flag. When ``dtype=bf16``, all activations, gradients and optimizer states are in bfloat16. In
            most cases this should halve the memory footprint of full precision (fp32) training, without
            loss in model quality (will depend on the model, training data and other settings). For
            GPUs which do not support bfloat16, we fall back to fp32. Mixed precision training and fp16
            precision are currently not supported.

        - Gradient Accumulation. You can simulate larger batch sizes by accumulating gradients. This is
            controlled using the ``gradient_accumulation_steps`` flag.

                Total Batch Size = batch_size * number of GPUs * gradient accumulation steps.

            For example: with batch_size=1, nproc_per_node=2 and gradient_accumulation_steps=32 we get a
            total batch size of 64.

            Gradient accumulation is especially useful when you are memory constrained. In this case,
            accumulating gradients might give you better training speed than enabling activation
            checkpointing.

        - Checkpointing. Model weights are checkpointed both at the end of each epoch and at the end of
            training. Currently we checkpoint both the adapter weights (trainable params only) and the
            complete merged weights (adapter weights added back to the base model). For more details
            please take a look at our LoRA tutorial
            (https://pytorch.org/torchtune/main/tutorials/lora_finetune.html).

            Optimizer State and recipe state (seed, total_epochs, number of epochs run etc) are
            only saved at the end of a given epoch and used in case of resuming training. Resuming
            training is controlled by the ``resume_from_checkpoint`` flag. Mid-epoch checkpointing is
            currently not supported.

            For more details on the checkpointer, please take a look at
            our checkpointer deepdive (https://pytorch.org/torchtune/main/tutorials/checkpointer.html).

        - Logging. Terminal, Disk, WandB and TensorBoard are all supported.

    For a full list of example configs for this recipe, run ``tune ls`` on the command line. Each config
    has example commands for how to kick-off training.

    Args:
        cfg (DictConfig): OmegaConf object parsed from yaml file

    Raises:
        ValueError: If ``dtype`` is set to fp16.
        ValueError: If world_size is 1
        RuntimeError: If ``dtype`` is set to bf16 and the hardware does not support bf16.
    """

    def __init__(self, cfg: DictConfig) -> None:
        self._cfg = cfg
        if os.environ.get("DEBUG", False):
            self._cfg["experiment_name"] = "debug"
        self._device = utils.get_device(device=cfg.device)
        self._dtype = training.get_dtype(cfg.dtype, device=self._device)
        if self._dtype == torch.float16:
            raise ValueError("full fp16 training is not supported with this recipe. Please use bf16 or fp32 instead.")
        # logging attributes
        self._output_dir = cfg.output_dir
        self._log_every_n_steps = cfg.log_every_n_steps
        # ``_is_rank_zero`` is used primarily for logging. In the future, the logger should directly take care of this
        _, rank = training.get_world_size_and_rank()
        self._is_rank_zero = rank == 0
        # training attributes
        self._enable_activation_checkpointing = cfg.enable_activation_checkpointing
        self._gradient_accumulation_steps = cfg.gradient_accumulation_steps
        # These attributes constitute the recipe state and are updated by ``load_checkpoint`` when ``resume_from_checkpoint`` is ``True``
        self.seed = training.set_seed(seed=cfg.seed)
        self.epochs_run = 0
        self.total_epochs = cfg.epochs
        self.global_step = 0
        self._resume_from_checkpoint = cfg.resume_from_checkpoint
        self.max_steps_per_epoch = cfg.max_steps_per_epoch
        # self.is_debug = False

    def load_checkpoint(self, cfg_checkpointer: DictConfig) -> dict:
        """
        Extract the checkpoint state from file and validate.
        This includes the base model weights. If resume_from_checkpoint is True, this also includes the adapter weights and recipe state.
        """
        self._checkpointer = config.instantiate(cfg_checkpointer, resume_from_checkpoint=self._resume_from_checkpoint)
        checkpoint_dict = self._checkpointer.load_checkpoint()
        # When resuming from checkpoint for LoRA, the recipe expects the adapter weights and recipe state to be present.
        # The keys should match up with what ``save_checkpoint`` used to create these intermediate checkpoints.
        if self._resume_from_checkpoint:
            if training.ADAPTER_KEY not in checkpoint_dict:
                raise ValueError("Adapter weights not found. Please ensure a valid adapter checkpoint is provided.")
            self._update_recipe_state(checkpoint_dict)
        return checkpoint_dict

    def _update_recipe_state(self, ckpt_dict: dict) -> None:
        """
        Updates the recipe state from checkpoint.
        """
        try:
            self.epochs_run = ckpt_dict[training.EPOCHS_KEY]
            # on mismatch, warn the user and prevent the override
            if self.seed != ckpt_dict[training.SEED_KEY]:
                msg = f"Config value for seed does not match the checkpoint value, using the checkpoint value: {ckpt_dict[training.SEED_KEY]}"
                warnings.warn(msg, stacklevel=2)
                self.seed = ckpt_dict[training.SEED_KEY]
            # on mismatch, warn the user but allow the override
            if self.total_epochs != ckpt_dict[training.TOTAL_EPOCHS_KEY]:
                msg = f"Config value for total_epochs does not match the checkpoint value, using the config value: {self.total_epochs}"
                warnings.warn(message=msg, stacklevel=2)
        except KeyError as e:
            msg = "Checkpoint does not contain the required keys needed for updating recipe state. Are you sure you passed in the right recipe checkpoint?"
            raise KeyError(msg) from e

    def setup(self) -> None:
        """
        Setup the recipe state. This includes recipe state (if resume_from_checkpoint is True),
        model, tokenizer, loss, optimizer, learning rate scheduler, sampler, and dataloader.
        """
        if self._is_rank_zero:
            print("Logging")
            if os.environ.get("DEBUG", False):
                self._metric_logger = config.instantiate(self._cfg.debug_metric_logger)
            else:
                tags = self._cfg.metric_logger.pop("tags", []) + ["lora", "distributed"]
                self._metric_logger = config.instantiate(self._cfg.metric_logger, tags=tags)
                # log config with parameter override
                self._metric_logger.log_config(self._cfg)
        checkpoint_dict = self.load_checkpoint(cfg_checkpointer=self._cfg.checkpointer)
        self._compile = self._cfg.get("compile", False)
        self._model = self._setup_model(
            cfg_model=self._cfg.model,
            enable_activation_checkpointing=self._cfg.enable_activation_checkpointing,
            fsdp_cpu_offload=self._cfg.get("fsdp_cpu_offload", False),
            reshard_after_forward=self._cfg.get("fsdp_reshard_after_forward", True),
            base_model_state_dict=checkpoint_dict[training.MODEL_KEY],
            lora_weights_state_dict=(checkpoint_dict[training.ADAPTER_KEY] if self._resume_from_checkpoint else None),
        )
        self._tokenizer = config.instantiate(self._cfg.tokenizer)
        # _setup_optimizer should take in ckpt_dict only if training is resumed from checkpoint. Transforming the opt state dict is handled by this method
        self._optimizer = self._setup_optimizer(
            cfg_optimizer=self._cfg.optimizer,
            opt_state_dict=(checkpoint_dict[training.OPT_KEY] if self._resume_from_checkpoint else None),
        )
        self._loss_fn = config.instantiate(self._cfg.loss)
        # sampler and dataloader depend on the tokenizer and loss_fn and should be setup after all of these are setup
        self._train_sampler, self._train_dataloader = self._setup_data(self._cfg.dataset.train, **self._cfg.get("tokenizer_specific_params", {}))
        self._test_sampler, self._test_dataloader = self._setup_data(self._cfg.dataset.test, **self._cfg.get("tokenizer_specific_params", {}))

        # Finally update the recipe state which can only be correctly set after all of the other components have been initialized and updated.
        # Number of training steps in each epoch depends on the number of batches produced by the dataloader and the gradient_accumulation_steps param.
        # for logging and tracking training state. This should be computed after the dataloader has been setup
        self._steps_per_epoch = len(self._train_dataloader) // self._gradient_accumulation_steps
        self.global_step = self.epochs_run * self._steps_per_epoch

        # Learning rate scheduler can only be set up after number of steps has been computed
        self._lr_scheduler = self._setup_lr_scheduler(
            cfg_lr_scheduler=self._cfg.lr_scheduler,
            num_training_steps=self.total_epochs * self._steps_per_epoch,
            last_epoch=self.global_step - 1,
        )
        # Set up profiler. DummyProfiler (nullcontext object with no-op `step` method) if cfg is missing profiler key or if `self._cfg.profiler.enabled = False`
        self._profiler = self._setup_profiler(self._cfg.get(training.PROFILER_KEY, None))

    def _setup_profiler(self, cfg_profiler: DictConfig | None) -> torch.profiler.profile | training.DummyProfiler:
        """
        Parses the `profiler` section of top-level `cfg` and sets up profiler

        Args:
            cfg_profiler: DictConfig - `profiler` section of the top-level `cfg` (the main config passed to `recipe.main`)

        Returns:
            profiler: Union[torch.profiler.profile, DummyProfiler] - DummyProfiler is a nullcontext with no-op methods
            for `start`, `stop`, and `step` that can be used in place of `torch.profiler.profile` if profiler is not enabled such
            that the instrumented training loop does not need to be changed profiling is disabled.

        The profiler config can be provided in configs under the `profiler` key with the following layout:

        .. code-block:: yaml
            profiler:
                enabled: bool

                #Output directory of trace artifacts
                output_dir: str

            #`torch.profiler.ProfilerActivity` types to trace
            cpu: bool
            cuda: bool

                #Trace options
                profile_memory: bool
                with_stack: bool
                record_shapes: bool
                with_flops: bool

            # `torch.profiler.schedule` options:
            # wait_steps -> wait, warmup_steps -> warmup, active_steps -> active, num_cycles -> repeat
            wait_steps: int
            warmup_steps: int
            active_steps: int
            num_cycles: int
        """
        # Missing profiler section in config, assume disabled
        if cfg_profiler is None:
            cfg_profiler = DictConfig({"enabled": False})
        # Check that component is included and set correctly
        if cfg_profiler.get("_component_", None) is None:
            cfg_profiler["_component_"] = "torchtune.training.setup_torch_profiler"
        else:
            msg = "Only torch profiler supported currently: component must be `torchtune.training.setup_torch_profiler`"
            assert cfg_profiler.get("_component_") == "torchtune.training.setup_torch_profiler", msg
        profiler, profiler_cfg = config.instantiate(cfg_profiler)
        if self._is_rank_zero:
            log.info(f" Profiler config after instantiation: {profiler_cfg}")
        return profiler

    def _setup_model(
        self,
        cfg_model: DictConfig,
        enable_activation_checkpointing: bool,
        fsdp_cpu_offload: bool,
        reshard_after_forward: bool,
        base_model_state_dict: Dict[str, Any],
        lora_weights_state_dict: Optional[Dict[str, Any]] = None,
    ) -> nn.Module:
        """
        Model initialization has some important considerations:
           a. To minimize GPU peak memory, we initialize the model on meta device with
              the right dtype
           b. All ranks calls ``load_state_dict`` without peaking CPU RAMs since
              full state dicts are loaded with ``torch.load(mmap=True)``
           c. We register (pre-)forward hooks with ``fully_shard`` instead of wrapping `nn.Module`
        """

        self._lora_rank = cfg_model.lora_rank
        self._lora_alpha = cfg_model.lora_alpha
        self._lora_attn_modules = list(cfg_model.lora_attn_modules)
        self._apply_lora_to_mlp = cfg_model.apply_lora_to_mlp
        self._apply_lora_to_output = getattr(cfg_model, "apply_lora_to_output", False)
        is_distributed = training.is_distributed()

        if self._is_rank_zero:
            if is_distributed:
                log.info("FDSP enabled")
            log.info("Instantiating model and loading checkpoint on Rank 0 ...")
            init_start = time.perf_counter()

        default_device = torch.device("meta") if is_distributed else self._device

        with training.set_default_dtype(self._dtype), default_device:
            model = config.instantiate(cfg_model)

        self.adapter_params = get_adapter_params(model)
        set_trainable_params(model, self.adapter_params)

        if self._compile:
            training.compile_model(model, verbose=self._is_rank_zero)

        if enable_activation_checkpointing:
            training.set_activation_checkpointing(model, auto_wrap_policy={modules.TransformerSelfAttentionLayer})

        if is_distributed:
            print("training.shard_model")

            # For FSDP sharding, we can condition on either the module or its name
            def _is_layer_name(name: str, module: nn.Module) -> bool:
                """
                Return True for layers.i and False for all other module names
                Covers sharding for both AC-wrapped and non-AC-wrapped modules in one shot
                """
                name_list = name.split(".")
                return len(name_list) == 2 and name_list[0] == "layers" and str.isdigit(name_list[1])

            training.shard_model(
                model=model,
                shard_conditions=[_is_layer_name],
                cpu_offload=fsdp_cpu_offload,
                reshard_after_forward=reshard_after_forward,
            )
        else:
            base_missing, base_unexpected = model.load_state_dict(base_model_state_dict, strict=False)

        if lora_weights_state_dict:
            if is_distributed:
                lora_missing, lora_unexpected = training.load_from_full_model_state_dict(
                    model,
                    lora_weights_state_dict,
                    self._device,
                    self._is_rank_zero,
                    cpu_offload=fsdp_cpu_offload,
                )
            else:
                lora_missing, lora_unexpected = model.load_state_dict(lora_weights_state_dict, strict=False)
        else:
            lora_missing, lora_unexpected = None, None

        if is_distributed:
            # Initialize LoRA params and RoPE buffers
            with training.set_default_dtype(self._dtype), self._device:
                lora_device = "cpu" if fsdp_cpu_offload else self._device
                for m in model.modules():
                    if (isinstance(m, LoRALinear) or isinstance(m, DoRALinear)) and not lora_weights_state_dict:
                        # lora may not be covered in state dict
                        # if finetune for the 1st time
                        m.lora_a.to_empty(device=lora_device)
                        m.lora_b.to_empty(device=lora_device)
                        m.initialize_parameters()
                    # RoPE is not covered in state dict
                    if hasattr(m, "rope_init"):
                        m.rope_init()
            base_missing, base_unexpected = training.load_from_full_model_state_dict(
                model,
                base_model_state_dict,
                self._device,
                self._is_rank_zero,
                cpu_offload=fsdp_cpu_offload,
            )

        validate_missing_and_unexpected_for_lora(
            lora_attn_modules=self._lora_attn_modules,
            apply_lora_to_mlp=self._apply_lora_to_mlp,
            apply_lora_to_output=self._apply_lora_to_output,
            base_missing=base_missing,
            base_unexpected=base_unexpected,
            lora_missing=lora_missing,
            lora_unexpected=lora_unexpected,
        )
        # Ensure no params and buffers are on meta device
        training.validate_no_params_on_meta_device(model)

        if self._is_rank_zero:
            log.info(f"Instantiating model and loading checkpoint took {time.perf_counter() - init_start:.2f} secs")
            memory_stats = training.get_memory_stats(device=self._device)
            training.log_memory_stats(memory_stats)

        if is_distributed:  # synchronize before training begins
            torch.distributed.barrier()

        return model

    def _setup_optimizer(self, cfg_optimizer: DictConfig, opt_state_dict: dict | None = None) -> Optimizer:
        optimizer = config.instantiate(cfg_optimizer, self._model.parameters())  # type: ignore
        if opt_state_dict:
            # Note: technically we should check _contains_fsdp for just the state dict of the adapter cfg, but should be equivalent
            opt_state_dict = FullyShardedDataParallel.optim_state_dict_to_load(self._model, optimizer, opt_state_dict)
            optimizer.load_state_dict(opt_state_dict)
        if self._is_rank_zero:
            log.info("Optimizer and loss are initialized.")
        return optimizer

    def _setup_lr_scheduler(self, cfg_lr_scheduler: DictConfig, num_training_steps: int, last_epoch: int) -> Optimizer:
        lr_scheduler = config.instantiate(
            cfg_lr_scheduler,
            optimizer=self._optimizer,  # type: ignore
            num_training_steps=num_training_steps,  # type: ignore
            last_epoch=last_epoch,  # type: ignore
        )
        if self._is_rank_zero:
            log.info("Learning rate scheduler is initialized.")
        return lr_scheduler

    def _setup_data(self, cfg_dataset: DictConfig, **tokenizer_specific_params) -> tuple[DistributedSampler, DataLoader]:
        """
        All data related setup happens here. Currently this recipe only supports the DistributedSamplers with Map-style Datasets which fit into memory.
        Other samplers, iterable datasets and streaming datasets are not supported.
        """
        batch_size = cfg_dataset.pop("batch_size")
        shuffle = cfg_dataset.pop("shuffle")
        limit = cfg_dataset.pop("limit", None)
        repeat = cfg_dataset.pop("repeat", None)
        world_size, rank = training.get_world_size_and_rank()
        dataset_files = [config.instantiate(item) for item in cfg_dataset.pop("dataset_files")]

        ds = config.instantiate(
            cfg_dataset,
            dataset_files=dataset_files,
            tokenizer=self._tokenizer,
            limit=limit,
            repeat=repeat,
        )
        collate_fn = padded_collate
        if not cfg_dataset["packed"]:
            collate_fn = functools.partial(
                collate_fn,
                padding_idx=self._tokenizer.pad_id,
                ignore_idx=self._loss_fn.ignore_index,
            )

        sampler = DistributedSampler(ds, num_replicas=world_size, rank=rank, shuffle=shuffle, seed=self.seed)
        dataloader = DataLoader(dataset=ds, batch_size=batch_size, sampler=sampler, collate_fn=collate_fn)
        if self._is_rank_zero:
            log.info("Dataset and Sampler are initialized.")
        return sampler, dataloader

    def save_checkpoint(
        self,
        prefix: str,
        intermediate_checkpoint: bool,
        epoch: int,
    ) -> None:
        """
        Checkpoint the state of the recipe. The constructed checkpoint state dict
        contains the following information:
        - Merged weights with key MODEL_KEY
        - Adapter weights with key ADAPTER_KEY
        - Relevant recipe state if training is not complete
        - If the `self._save_adapter_weights_only` option is True, the checkpointer will save only the adapter weights

        Checkpointer will save the merged weights, adapter weights and recipe state in
        different checkpoint files. To correctly resume from training, the adapter weights
        and recipe state must be provided along with the base model weights.
        """
        # final dict passed onto the checkpointer
        checkpoint_dict = {}

        # intermediate_checkpoint = epoch + 1 < self.total_epochs
        # To prevent GPU memory from spiking during checkpoint save,
        # we consolidate the full model and optim state dicts on CPU for rank 0
        cpu_state_dict = training.get_full_model_state_dict(
            self._model,
            self._is_rank_zero,
            device=self._device,
            trainable_only=False,
        )

        if intermediate_checkpoint:
            opt_state_dict = training.get_full_optimizer_state_dict(
                self._optimizer,
                self._is_rank_zero,
                device=self._device,
            )
        else:
            opt_state_dict = None

        # Now that we have the model and opt state dict, create the actual checkpoint dict
        # to be sent to the checkpointer and ultimately written to file
        if self._is_rank_zero:
            # Filter out the adapter keys and weights from the model state dict. These will
            # be saved separately
            def adapter_key_filter(x):
                return x in self.adapter_params

            adapter_state_dict = {k: v for k, v in cpu_state_dict.items() if adapter_key_filter(k)}
            checkpoint_dict.update({training.ADAPTER_KEY: adapter_state_dict})

            # merge the adapter weights and base weights to create the model checkpoint
            merged_state_dict = get_merged_lora_ckpt(
                cpu_state_dict,
                rank=self._lora_rank,
                alpha=self._lora_alpha,
            )
            checkpoint_dict.update({training.MODEL_KEY: merged_state_dict})

            # if training is in-progress, checkpoint the optimizer state and recipe state
            # as well.
            if intermediate_checkpoint:
                checkpoint_dict.update(
                    {
                        training.OPT_KEY: opt_state_dict,
                        training.SEED_KEY: self.seed,
                        training.EPOCHS_KEY: self.epochs_run,
                        training.TOTAL_EPOCHS_KEY: self.total_epochs,
                        # training.MAX_STEPS_KEY: self.max_steps_per_epoch,
                    }
                )

            adapter_config = {
                "r": self._lora_rank,
                "lora_alpha": self._lora_alpha,
                "target_modules": get_lora_module_names(
                    self._lora_attn_modules,
                    self._apply_lora_to_mlp,
                    self._apply_lora_to_output,
                ),
                "peft_type": "LORA",
            }
            checkpoint_dict.update({training.ADAPTER_CONFIG: adapter_config})
            print("saving checkpoint")
            self._checkpointer.save_checkpoint(
                checkpoint_dict,
                epoch=epoch,
                intermediate_checkpoint=intermediate_checkpoint,
                adapter_only=False,
            )

            if prefix == "latest":
                # remove the previous last epoch checkpoint
                import glob

                for f in glob.glob(os.path.join(self._output_dir, f"*{epoch - 1}.pt")):
                    os.remove(f)

    def train(self) -> None:
        """
        The core training loop.
        """
        # clean up before training begins
        training.cleanup_before_training()
        _, rank = training.get_world_size_and_rank()
        # zero out the gradients before starting training
        self._optimizer.zero_grad()
        with self._profiler as prof:
            # self._model.train()
            step_num_tokens, step_loss, step_start_t, best_eval_loss = 0, 0, time.perf_counter(), float("inf")
            # self.epochs_run should be non-zero when we're resuming from a checkpoint
            for curr_epoch in range(self.epochs_run, self.total_epochs):
                # Update the sampler to ensure data is correctly shuffled across epochs in case shuffle is True
                self._train_sampler.set_epoch(curr_epoch)
                pbar = tqdm(
                    total=self._steps_per_epoch,
                    dynamic_ncols=True,
                    disable=not self._is_rank_zero,
                )
                (
                    train_epoch_num_tokens,
                    train_epoch_loss,
                    train_epoch_batches,
                    train_epoch_start_t,
                ) = (0, 0, 0, time.perf_counter())

                for idx, batch in enumerate(self._train_dataloader):
                    if self.max_steps_per_epoch is not None and (idx // self._gradient_accumulation_steps) == self.max_steps_per_epoch:
                        break
                    train_epoch_num_tokens += batch["tokens"].numel()
                    loss, logits = self.forward(batch)
                    del logits
                    loss = loss / self._gradient_accumulation_steps
                    step_loss += loss
                    loss.backward()
                    # Step with optimizer
                    if (idx + 1) % self._gradient_accumulation_steps == 0:
                        self._optimizer.step()
                        self._optimizer.zero_grad(set_to_none=True)
                        self._lr_scheduler.step()
                        # Update the number of steps when the weights are updated
                        self.global_step += 1
                        # Log step
                        step_loss = step_loss.item()
                        pbar.update(1)
                        pbar.set_description(f"{curr_epoch + 1:03d}|{self.global_step:06d}|Loss: {step_loss:.4f}")
                        # Track epoch loss
                        train_epoch_loss += step_loss
                        train_epoch_batches += 1
                        # Log per-step metrics
                        time_per_step = time.perf_counter() - step_start_t
                        if self.global_step % self._log_every_n_steps == 0 and self._is_rank_zero:
                            log_dict = {
                                "step": {
                                    "loss": step_loss,
                                    "tps": step_num_tokens / time_per_step,
                                    "lr": self._optimizer.param_groups[0]["lr"],  # type: ignore
                                    **training.get_memory_stats(device=self._device),
                                }
                            }
                            self._metric_logger.log_dict(log_dict, step=self.global_step)
                        # Reset running stats for the next step
                        step_loss, step_num_tokens, step_start_t = (
                            0,
                            0,
                            time.perf_counter(),
                        )
                        # Step profiler Note that this is called within gradient accumulation block.
                        # Hence will include multiple forward / backward passes if gradient accumulation > 1
                        prof.step()
                train_epoch_time = time.perf_counter() - train_epoch_start_t
                train_epoch_loss = train_epoch_loss / train_epoch_batches
                self.save_checkpoint(
                    prefix="latest",
                    intermediate_checkpoint=curr_epoch + 1 < self.total_epochs,
                    epoch=curr_epoch + 1,
                )
                out_eval_dict = self.eval()
                self._model.train()
                eval_epoch_loss = out_eval_dict["eval_loss"]
                # eval_epoch_score = out_eval_dict["ted_score"]  # F841: Unused variable
                if self._is_rank_zero:
                    log_dict = {
                        "train": {
                            "loss": train_epoch_loss,
                            "tps": train_epoch_num_tokens / train_epoch_time,
                        },
                        "eval": {
                            "loss": eval_epoch_loss,
                            "tps": out_eval_dict["eval_num_tokens"] / out_eval_dict["eval_time"],
                        },
                    }
                    if "ted_score" in out_eval_dict:
                        log_dict["eval"]["ted_score"] = out_eval_dict["ted_score"]

                    for key in out_eval_dict:
                        if key not in [
                            "eval_num_tokens",
                            "loss",
                            "eval_num_tokens",
                            "ted_score",
                            "eval_time",
                        ]:
                            log_dict["eval"][key] = out_eval_dict[key]

                    self._metric_logger.log_dict(log_dict, step=self.global_step)
                self.epochs_run += 1
                best_eval_loss = float("inf")  # Initialize to avoid F821: Undefined name
                if eval_epoch_loss < best_eval_loss:
                    # best_eval_loss = eval_epoch_loss  # F841: Unused variable
                    self.save_checkpoint(
                        prefix="best",
                        intermediate_checkpoint=False,
                        epoch=(self.total_epochs + 1),
                    )

    @torch.inference_mode()
    def eval(self) -> dict[str, float]:
        # self._model.eval()
        """The core evaluation loop."""
        eval_num_tokens, eval_loss, eval_batches, t0 = 0, 0, 0, time.perf_counter()
        # ted_scores = []
        max_ted_scores_evals = -1
        per_categories_eval: dict[str, dict[str, list]] = {}

        compute_ted = max_ted_scores_evals >= 0
        all_metrics_scores = {}
        if compute_ted:
            all_metrics_scores = {"ted_scores": []}

        for _, batch in enumerate(self._test_dataloader):
            eval_num_tokens += batch["tokens"].numel()
            batch_categories = batch["category"]
            generation_modes = ["sequence" if "sequence" in cat else "workflow" for cat in batch_categories]

            loss, _ = self.forward(batch)
            batch_ted_scores = []

            loss = loss.item()
            eval_loss += loss
            eval_batches += 1

            if compute_ted and len(all_metrics_scores["ted_scores"]) < max_ted_scores_evals:
                batch_ted_scores = compute_batch_ted_metric(
                    self._model,
                    self._tokenizer,
                    batch,
                    self._device,
                    self._dtype,
                    generation_modes,
                )
                all_metrics_scores["ted_scores"].extend(batch_ted_scores)
            # split metric per category
            for index in range(len(batch_categories)):
                category = batch_categories[index]
                if category not in per_categories_eval:  # init
                    per_categories_eval[category] = {}
                    if compute_ted:
                        per_categories_eval[category]["ted_scores"] = []

                if len(batch_ted_scores) > 0:
                    per_categories_eval[category]["ted_scores"].append(batch_ted_scores[index])
        # if training.is_distributed():
        #     torch.distributed.barrier()

        eval_time = time.perf_counter() - t0
        eval_loss = eval_loss / eval_batches
        node_out_dict = {
            "eval_loss": {"value": eval_loss, "no_items": eval_batches},
            "eval_num_tokens": {"value": eval_num_tokens, "no_items": eval_batches},
            "eval_time": {"value": eval_time, "no_items": eval_batches},
        }
        if compute_ted:
            node_out_dict["ted_score"] = (
                {
                    "value": np.mean(all_metrics_scores["ted_scores"]),
                    "no_items": len(all_metrics_scores["ted_scores"]),
                },
            )

        for category, category_eval_dict in per_categories_eval.items():
            for metric, metric_values in category_eval_dict.items():
                metric_name = f"{category}_{metric}"
                node_out_dict[metric_name] = {
                    "value": np.mean(metric_values),
                    "no_items": len(metric_values),
                }

        out_dict: dict[str, float] = {}
        if training.is_distributed():
            all_metrics = [None] * torch.distributed.get_world_size()
            torch.distributed.all_gather_object(all_metrics, node_out_dict)
        else:
            all_metrics = [node_out_dict]

        for key in node_out_dict.keys():
            out_dict[key] = np.average(
                [(node_metric[key]["value"] if key in node_metric else 0) for node_metric in all_metrics],
                weights=[(node_metric[key]["no_items"] if key in node_metric else 0) for node_metric in all_metrics],
            )

        return out_dict

    def forward(self, batch: dict[str, torch.Tensor]) -> tuple[torch.Tensor, torch.Tensor]:
        # Both are shape [b, s]
        # Get the attention mask and position ids from the dataset if they exist. Currently, only sample packing in PackedDataset returns these
        tokens, labels = batch["tokens"], batch["labels"]
        mask = batch.get("mask", None)  # shape [b, s, s]
        input_pos = batch.get("input_pos", None)  # shape [b, s]
        tokens = tokens.to(self._device)
        labels = labels.to(self._device)
        mask = mask.to(self._device) if mask is not None else None
        input_pos = input_pos.to(self._device) if input_pos is not None else None
        logits = self._model(tokens, mask=mask, input_pos=input_pos)
        # Shift so that tokens < n predict n
        logits = logits[..., :-1, :].contiguous()
        labels = labels[..., 1:].contiguous()
        logits = logits.transpose(1, 2)
        # Compute loss
        loss = self._loss_fn(logits, labels)
        return loss, logits

    def cleanup(self) -> None:
        if self._is_rank_zero:
            self._metric_logger.close()
        torch.distributed.destroy_process_group()


@config.parse
def recipe_main(cfg: DictConfig) -> None:
    """
    Entry point for the recipe.

    Configurable parameters are read in the following order:
        - Parameters specified in config (see available configs through ``tune ls``)
        - Overwritten by arguments from the command-line
    """
    if training.is_distributed():
        # "Distributed finetune recipe should be run via a distributed launcher. If using tune CLI, please specify --nnodes 1 and --nproc_per_node [gpus]"
        os.environ["TORCH_NCCL_AVOID_RECORD_STREAMS"] = "1"  # https://github.com/pytorch/pytorch/pull/76861
        torch.distributed.init_process_group(backend="gloo" if cfg.device == "cpu" else "nccl")
        config.log_config(recipe_name="LoRAFinetuneRecipeDistributed", cfg=cfg)
    recipe = LoRAFinetuneRecipeDistributed(cfg=cfg)
    recipe.setup()
    recipe.train()
    recipe.cleanup()


def run_debug():
    import argparse

    from torchtune.config._parse import TuneRecipeArgumentParser
    from torchtune.config._utils import _merge_yaml_and_cli_args

    parser = TuneRecipeArgumentParser(
        description=recipe_main.__doc__,
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    # Get user-specified args from config and CLI and create params for recipe

    # cfg_path = r"/workspace/src/studio/text_to_workflow/workflow_generation/training/torch_tune/llama3_1_8b_lora.yaml"
    cfg_path = r"/mldata/ML2/studio/text_to_workflow/workflow_generation/training/torch_tune/llama3_8b.lora.distributed.yaml"
    yaml_args, cli_args = parser.parse_known_args(["--config", cfg_path])
    conf = _merge_yaml_and_cli_args(yaml_args, cli_args)

    recipe = LoRAFinetuneRecipeDistributed(cfg=conf)
    # recipe.is_debug = True
    recipe.setup()
    recipe.train()
    recipe.cleanup()


if __name__ == "__main__":
    # run_debug()
    # Usage: python finetune_lora_distributed.py --config configs/phi3/finetune_lora.phi3_mini.distributed.yaml
    if len(sys.argv) == 1:
        # sys.argv.extend(["--config", "configs/debug.lora.distributed.yaml"])
        # sys.argv.extend(["--config", "configs/debug.lora.distributed.yaml"])
        sys.argv.extend(
            [
                "--config",
                "/mldata/ML2/studio/text_to_workflow/workflow_generation/training/torch_tune/llama3_8b.lora.distributed.yaml",
            ]
        )
    error_handler = torch.distributed.elastic.multiprocessing.errors.ErrorHandler()
    error_handler.initialize()
    try:
        sys.exit(recipe_main())  # type: ignore
    except torch.distributed.elastic.multiprocessing.errors.ChildFailedError as e:
        _, failure = e.get_first_failure()
        error_handler.dump_error_file(failure.error_file, failure.exitcode)
        raise
