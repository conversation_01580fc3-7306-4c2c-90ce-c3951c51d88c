# Config for multi-device full finetuning in full_finetune_distributed.py
# using a Phi3 Mini 4K Instruct
#
# This config assumes that you've run the following command before launching
# this run:
#   tune download microsoft/Phi-3-mini-4k-instruct --output-dir /tmp/Phi-3-mini-4k-instruct --hf-token <HF_TOKEN> --ignore-patterns ""
#
# Run this config on 4 GPUs using the following:
#  tune run --nproc_per_node 4 full_finetune_distributed --config phi3/mini_full
#
# You can add specific overrides through the command line. For example
# to override the checkpointer directory while launching training
# you can run:
#   tune run --nproc_per_node 4 full_finetune_distributed --config phi3/mini_full checkpointer.checkpoint_dir=<YOUR_CHECKPOINT_DIR>
#
# This config works best when the model is being fine-tuned on 2+ GPUs.
# Single device full finetuning requires more memory optimizations. It's
# best to use mini_low_memory.yaml for those cases

# NOTE: Modify these paths to point to your own data and model directories
workdir: /workspace
data_dir: /workspace/data
# base_dir: /workspace/data/Llama-3.1-8B-Instruct
base_dir: /workspace/data/Llama-3.1-70B-Instruct
experiment_name: llama3.170b_full_dataset_categories_eval
output_dir: /workspace/data/experiments/${experiment_name}
log_dir: ${output_dir}/Logs
seed: 42
# Tokenizer
tokenizer:
  _component_: torchtune.models.llama3.llama3_tokenizer
  path: ${base_dir}/original/tokenizer.model
  max_seq_len: 8192
# Dataset
dataset:
  train:
    _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.planning_chat_dataset
    dataset_files:
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Portable_train_withcat_datapoints.json
        category: sequence_portable
        weight: 20
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_train_withcat_datapoints.json
        category: sequence_windows
        weight: 20
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_prod_withcat_datapoints.json
        category: sequence_windows
        weight: 1
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/WorkflowGeneration/plan_data/Windows_train_withcat_datapoints.json
        category: workflow_generation_windows
        weight: 40
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/WorkflowGeneration/plan_data/Portable_train_withcat_datapoints.json
        category: workflow_generation_portable
        weight: 80
    max_seq_len: 8192
    packed: False
    batch_size: 2
    shuffle: True
    tokenize_messages_kwargs:
      add_end_tokens: True
  test:
    _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.planning_chat_dataset
    dataset_files:
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Portable_test_withcat_datapoints.json
        category: sequence_portable
        weight: 1
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/SequenceGeneration/plan_data/Windows_test_withcat_datapoints.json
        category: sequence_windows
        weight: 1
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/WorkflowGeneration/plan_data/Portable_test_withcat_datapoints.json
        category: workflow_generation_portable
        weight: 1
      - _component_: _text_to_workflow.workflow_generation.training.torch_tune.dataset.get_dataset_item
        filename : ${data_dir}/Autopilot.Samples/Dataset/WorkflowGeneration/plan_data/Windows_test_withcat_datapoints.json
        category: workflow_generation_windows
        weight: 1
    max_seq_len: 8192
    packed: False
    batch_size: 2
    shuffle: False
    tokenize_messages_kwargs: ${dataset.train.tokenize_messages_kwargs}
# Model
compile: True
model:
  #_component_: torchtune.models.llama3_1.lora_llama3_1_8b
  _component_: torchtune.models.llama3_1.lora_llama3_1_70b
  lora_attn_modules: ['q_proj', 'v_proj']
  apply_lora_to_mlp: False
  apply_lora_to_output: False
  lora_rank: 8
  lora_alpha: 16
checkpointer:
  _component_: torchtune.training.FullModelHFCheckpointer
  checkpoint_dir: ${base_dir}
  # checkpoint_files: [
  #   model-00001-of-00004.safetensors,
  #   model-00002-of-00004.safetensors,
  #   model-00003-of-00004.safetensors,
  #   model-00004-of-00004.safetensors
  # ]
  checkpoint_files: [
    model-00001-of-00030.safetensors,
    model-00002-of-00030.safetensors,
    model-00003-of-00030.safetensors,
    model-00004-of-00030.safetensors,
    model-00005-of-00030.safetensors,
    model-00006-of-00030.safetensors,
    model-00007-of-00030.safetensors,
    model-00008-of-00030.safetensors,
    model-00009-of-00030.safetensors,
    model-00010-of-00030.safetensors,
    model-00011-of-00030.safetensors,
    model-00012-of-00030.safetensors,
    model-00013-of-00030.safetensors,
    model-00014-of-00030.safetensors,
    model-00015-of-00030.safetensors,
    model-00016-of-00030.safetensors,
    model-00017-of-00030.safetensors,
    model-00018-of-00030.safetensors,
    model-00019-of-00030.safetensors,
    model-00020-of-00030.safetensors,
    model-00021-of-00030.safetensors,
    model-00022-of-00030.safetensors,
    model-00023-of-00030.safetensors,
    model-00024-of-00030.safetensors,
    model-00025-of-00030.safetensors,
    model-00026-of-00030.safetensors,
    model-00027-of-00030.safetensors,
    model-00028-of-00030.safetensors,
    model-00029-of-00030.safetensors,
    model-00030-of-00030.safetensors,
  ]
  recipe_checkpoint: null
  output_dir: ${output_dir}
  model_type: LLAMA3
resume_from_checkpoint: False
# Optimizer
optimizer:
  _component_: torch.optim.AdamW
  fused: True
  lr: 3e-4
# Scheduler
lr_scheduler:
  _component_: torchtune.modules.get_cosine_schedule_with_warmup
  num_warmup_steps: 10
# Loss
loss:
  _component_: torch.nn.CrossEntropyLoss
# Training
max_steps_per_epoch: 1000
epochs: 20
gradient_accumulation_steps: 8
device: cuda
# Reduced precision
dtype: bf16
# Memory management
enable_activation_checkpointing: True
# Showcase the usage of pytorch profiler. Set enabled to False as it's only needed for debugging training
profiler:
  enabled: False
  _component_: torchtune.training.setup_torch_profiler
  #Output directory of trace artifacts
  output_dir: ${output_dir}/profiling_outputs
  #`torch.profiler.ProfilerActivity` types to trace
  cpu: True
  cuda: True
  #trace options passed to `torch.profiler.profile`
  profile_memory: False
  with_stack: False
  record_shapes: True
  with_flops: False
  # `torch.profiler.schedule` options:
  # wait_steps -> wait, warmup_steps -> warmup, active_steps -> active, num_cycles -> repeat
  wait_steps: 5
  warmup_steps: 5
  active_steps: 2
  num_cycles: 1
# Logging
log_every_n_steps: 10
metric_logger:
  _component_: torchtune.training.metric_logging.WandBLogger
  project: wingman@workflow_generation_planning
  entity: uipath
  group: finetuning
  log_dir: ${log_dir}
  tags:
  - finetuning
  # _component_: torchtune.utils.metric_logging.DiskLogger
  # filename: metrics.log
  # log_dir: ${output_dir}
