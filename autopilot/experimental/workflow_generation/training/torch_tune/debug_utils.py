import os

import debugpy


def setup_debugger():
    debug_port = 5678  # You can choose any available port
    os.environ["DEBUG"] = "1"
    rank = int(os.environ.get("RANK", "0"))
    if rank == 0:
        # Only the process with rank 0 waits for the debugger to attach
        print(f"Rank {rank}: Waiting for debugger attach on port {debug_port}...")
        debugpy.listen(("0.0.0.0", debug_port))
        debugpy.wait_for_client()
        print(f"Rank {rank}: Debugger is attached.")
    else:
        # Other ranks also listen for debugger but do not wait
        debugpy_port = debug_port + rank
        print(f"Rank {rank}: Listening for debugger attach on port {debugpy_port}...")
        debugpy.listen(("0.0.0.0", debugpy_port))
