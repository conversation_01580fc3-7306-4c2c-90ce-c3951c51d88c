import contextlib
import datetime
import os
import pathlib
import traceback
from typing import Generator, Optional

import torch
import torch.nn as nn
from torch import Tensor
from torchtune.generation import generate
from torchtune.modules import tokenizers
from torchtune.modules.transformer import TransformerDecoder

from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.training import eval_plan


@contextlib.contextmanager
def local_kv_cache(
    model: nn.Module,
    *,
    batch_size: int,
    device: torch.device,
    dtype: torch.dtype,
    encoder_max_seq_len: Optional[int] = None,
    decoder_max_seq_len: Optional[int] = None,
) -> Generator[None, None, None]:
    # ensure caches are setup on the same device as the model
    with device:
        model.setup_caches(
            batch_size,
            dtype,
            encoder_max_seq_len=encoder_max_seq_len,
            decoder_max_seq_len=decoder_max_seq_len,
        )
    try:
        yield
    finally:
        delete_kv_caches(model)


def delete_kv_caches(model: nn.Module):
    for module in model.modules():
        if hasattr(module, "kv_cache") and callable(module.kv_cache):
            module.cache_enabled = False
            module.kv_cache = None


def print0(*args, **kwargs):
    # modified print that only prints from the master process
    # if this is not a distributed run, it's just a print
    if int(os.environ.get("RANK", 0)) == 0:
        print(*args, **kwargs)


def compute_batch_ted_metric(
    model: TransformerDecoder,
    tokenizer: tokenizers.BaseTokenizer,
    batch: dict[str, Tensor],
    device: torch.device,
    dtype,
    generation_modes: list[str],
):
    # Prepare lists to store generated texts and reference texts
    model.eval()
    generated_texts = []
    reference_texts = []
    batch_scores = []

    tokens, labels = batch["tokens"], batch["labels"]

    # Loop over each example in the batch
    for i in range(labels.shape[0]):
        # Get the current batch element's labels and input_ids
        current_labels = labels[i]
        current_input_ids = tokens[i]
        generation_mode = generation_modes[i]

        # Mask to find where labels == -100 (the part we want to generate from)
        mask_for_generation = current_labels == -100
        mask_for_generation = mask_for_generation & (current_input_ids != tokenizer.pad_id)
        index = torch.argmin(mask_for_generation.long())
        mask_for_generation[index:] = 0

        mask_for_comparison = current_labels != -100

        # Extract the part of the input where labels are masked (-100)
        inputs_for_generation = torch.masked_select(current_input_ids, mask_for_generation).to(device)

        input_prompt = tokenizer.decode(inputs_for_generation.cpu().detach().tolist(), skip_special_tokens=True)
        usr_prompt = input_prompt[input_prompt.rfind("user\n\n") :][len("user\n\n") :]

        max_generated_tokens = 256
        # Generate text based on this prompt
        use_local_cache = False

        if use_local_cache:
            with local_kv_cache(
                model,
                batch_size=1,
                device=device,
                dtype=dtype,
                decoder_max_seq_len=inputs_for_generation.numel() + max_generated_tokens,
            ):
                start_generation = datetime.datetime.now()
                generated_tokens, _ = generate(
                    model, prompt=inputs_for_generation, max_generated_tokens=max_generated_tokens, pad_id=tokenizer.pad_id, stop_tokens=tokenizer.eot_id
                )
                end_generation = datetime.datetime.now()
                print0("Time taken for generation:", end_generation - start_generation)
        else:
            start_generation = datetime.datetime.now()
            generated_tokens, _ = generate(
                model, prompt=inputs_for_generation, max_generated_tokens=max_generated_tokens, pad_id=tokenizer.pad_id, stop_tokens=tokenizer.eot_id
            )
            end_generation = datetime.datetime.now()
            print0("Time taken for generation:", end_generation - start_generation)

        generated_text = tokenizer.decode(
            generated_tokens[0][len(inputs_for_generation) :].cpu().detach().tolist(),
            skip_special_tokens=True,
        )

        # Extract the reference labels (actual expected text) where labels != -100
        reference_labels = torch.masked_select(current_labels, mask_for_comparison)

        # Decode the reference labels
        decoded_reference_labels = tokenizer.decode(reference_labels.cpu().detach().tolist(), skip_special_tokens=True)
        label_plan = decoded_reference_labels[len("assistant\n\n") :]
        predicted_plan = generated_text[generated_text.rfind("assistant\n\n") :][len("assistant\n\n") :]
        generated_texts.append(predicted_plan)
        reference_texts.append(label_plan)

        print0("***********************************************")
        print0("input_prompt:\n", usr_prompt)
        print0("label_plan:\n", label_plan)
        print0("predicted_plan:\n", predicted_plan)
        print0("***********************************************")

        workflow = yaml_load(pathlib.Path(batch["filepath"][i]))
        try:
            # score = planning_edit_score(label_plan, predicted_plan, cross_encoder_model=cross_encoder_model)
            score = eval_plan.plan_score(predicted_plan, workflow, generation_mode)
        except Exception as e:
            print("Exception in computing score:", e)
            traceback.print_exc()
            score = 0.0
        batch_scores.append(score)
    return batch_scores
