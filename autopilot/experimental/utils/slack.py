import argparse
import os

import requests


def send_slack_notification(args: argparse.Namespace):
    url = os.getenv("SLACK_WEBHOOK_URL")
    if url is None:
        print("SLACK_WEBHOOK_URL is not set")
    else:
        # print args as a dict
        args_printable = "\n".join([f"{k}: {v}" for k, v in vars(args).items()])
        message = "Workflow Generation job finished."
        response = requests.post(url, json={"message": message, "block": args_printable})
        if response.status_code != 200:
            print(f"Failed to send slack notification: {response.status_code} {response.text}")
