{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import itertools\n", "import json\n", "import pathlib\n", "\n", "import yaml\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "\n", "class multiline_str(str):\n", "    pass\n", "\n", "\n", "def multiline_str_presenter(dumper, data):\n", "    data = data.replace(\"\\t\", \"  \")\n", "    return dumper.represent_scalar(\"tag:yaml.org,2002:str\", str(data), style=\"|\")\n", "\n", "\n", "yaml.add_representer(multiline_str, multiline_str_presenter)\n", "yaml.representer.SafeRepresenter.add_representer(multiline_str, multiline_str_presenter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "indexing_re = re.compile(r\"^\\s*(\\d+\\.)*\\s\")\n", "\n", "\n", "def strip_plan(plan):\n", "    lines = plan.split(\"\\n\")\n", "    if lines and lines[-1] == \"\":\n", "        lines = lines[:-1]\n", "    return \"\\n\".join(\n", "        m.group(0).replace(\"  \", \"\\t\")  # this replacement for dealing with \\t vs \"  \" confusion\n", "        if (m := indexing_re.search(line))\n", "        else \"None\"\n", "        for line in lines\n", "    )\n", "\n", "\n", "def postprocess_summary(newplan):\n", "    if newplan[0] == \"```pseudocode\" and newplan[-1] == \"```\":\n", "        newplan = newplan[1:-1]\n", "    newplan = [i.strip() for i in newplan]\n", "    for i in range(len(newplan)):\n", "        result = re.search(\"^(\\d+\\.)+(.*)$\", newplan[i])\n", "        if result is not None:\n", "            newplan[i] = result[2]\n", "        if newplan[i].startswith(\"- \"):\n", "            newplan[i] = newplan[i][2:]\n", "    newplan = [i.strip() for i in newplan]\n", "    return newplan"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Lo<PERSON>'s plan proposals"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plans_andrei_train_path = pathlib.Path(\"/workspace/data/plans-proposals/from-andrei/plans_train_v2\")\n", "plans_andrei_test_path = pathlib.Path(\"/workspace/data/plans-proposals/from-andrei/plans_test_v2\")\n", "\n", "metadata_object = {}\n", "for path in [plans_andrei_train_path, plans_andrei_test_path]:\n", "    for file in path.iterdir():\n", "        with open(file, \"r\") as f:\n", "            data = json.load(f)\n", "            workflow = Workflow(\"\", \"\", data[\"workflow\"])\n", "            plan = PlanBuilder().build(workflow)\n", "            proposed_plan = \"\\n\".join(postprocess_summary(data[\"summary\"]))\n", "            # if strip_plan(plan) != strip_plan(proposed_plan):\n", "            #     print(f\"Plan structure mismatch for {file.stem}\")\n", "            #     continue\n", "            # if plan != proposed_plan:\n", "            #     print(f\"Plan mismatch for {file.stem}\")\n", "            #     # print(plan)\n", "            #     # print(proposed_plan)\n", "            #     continue\n", "            if len(plan.split(\"\\n\")) != len(proposed_plan.split(\"\\n\")):\n", "                print(f\"Plan length mismatch for {file.stem}\")\n", "                print(plan)\n", "                print(proposed_plan)\n", "                continue\n", "            stem = file.stem\n", "            if stem.endswith(\".yaml\"):\n", "                stem = stem[:-5]\n", "            metadata_object[stem] = {\n", "                # \"workflow\": data[\"workflow\"],\n", "                \"plan-original\": multiline_str(plan),\n", "                \"proposed-plan-v2\": multiline_str(proposed_plan),\n", "            }\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "with open(metadata_path, \"w\") as f:\n", "    # metadata_object = dict(sorted(metadata_object.items(), key=lambda x: x[0]))\n", "    yaml.dump(metadata_object, f, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Temporary commit of the metadata plans"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.walkers import WorkflowThoughtsModifier\n", "\n", "# train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\")\n", "\n", "for datapoint_path in train_path.iterdir():\n", "    if not datapoint_path.suffix == \".yaml\":\n", "        print(\"WARNING! Invalid file in train data\")\n", "        continue\n", "    datapoint_identifier = datapoint_path.stem\n", "    if datapoint_identifier not in metadata_object:\n", "        print(f\"WARNING! Missing metadata for {datapoint_identifier}\")\n", "        continue\n", "    with open(datapoint_path, \"r\") as f:\n", "        datapoint = yaml.load(f, Loader=yaml.CSafeLoader)\n", "\n", "    metadatapoint = metadata_object[datapoint_identifier]\n", "    if datapoint[\"plan\"] != metadatapoint[\"plan-original\"]:\n", "        # print(f\"Different for {datapoint_identifier}\")\n", "        continue\n", "\n", "    if \"proposed-plan-v2\" not in metadatapoint:\n", "        print(\"Skipping, no metadata\")\n", "        continue\n", "\n", "    proposed_plan = metadatapoint[\"proposed-plan-v2\"]\n", "\n", "    workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "    try:\n", "        WorkflowThoughtsModifier(proposed_plan.split(\"\\n\")).replace(workflow)\n", "    except:\n", "        print(datapoint[\"plan\"])\n", "        print(proposed_plan)\n", "        raise\n", "\n", "    datapoint[\"plan\"] = multiline_str(PlanBuilder().build(workflow))\n", "    datapoint[\"process\"] = workflow.to_dict()\n", "\n", "    # continue\n", "    with open(datapoint_path, \"w\") as f:\n", "        yaml.dump(datapoint, f, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Simple application of workflow abstraction on the dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_paths = [\n", "    pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\"),\n", "    pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\"),\n", "]\n", "datapoints = itertools.chain(*[root_path.glob(\"*.yaml\") for root_path in root_paths])\n", "for datapoint_path in datapoints:\n", "    datapoint = yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "    workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "    datapoint[\"conversion_errors\"] = multiline_str(datapoint[\"conversion_errors\"])  # to avoid unnecessary diffs\n", "    datapoint[\"plan\"] = multiline_str(PlanBuilder().build(workflow))\n", "    datapoint[\"process\"] = workflow.to_dict()\n", "    yaml.dump(datapoint, datapoint_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate plan refinement from train data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import prompt_llm_for_plans_refinement_and_iterate\n", "\n", "# train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\")\n", "dataset, queries, plans, loaded_dataset = [{} for _ in range(4)]\n", "for datapoint_path in train_path.iterdir():\n", "    # if len(dataset) > 3:\n", "    #     break\n", "    datapoint = yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "    identifier = datapoint_path.as_posix()\n", "\n", "    dataset[identifier] = yaml.dump(datapoint[\"process\"], sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n", "    queries[identifier] = datapoint[\"description\"]\n", "    plans[identifier] = datapoint[\"plan\"]\n", "    loaded_dataset[identifier] = datapoint[\"process\"]\n", "\n", "refined_plans = {}\n", "# for identifier, refined_plan in prompt_llm_for_plans_refinement_and_iterate(dataset, queries, plans, loaded_dataset, True):\n", "for identifier, refined_plan in prompt_llm_for_plans_refinement_and_iterate(dataset, queries, plans, loaded_dataset):\n", "    identifier = pathlib.Path(identifier)\n", "    refined_plans[identifier.stem] = multiline_str(refined_plan)\n", "\n", "# with open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_plans_train.yaml\", \"w\") as f:\n", "with open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_plans_test.yaml\", \"w\") as f:\n", "    yaml.dump(refined_plans, f, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "for identifier, refined_plan in refined_plans.items():\n", "    refined_plan = refined_plan.strip()\n", "    if refined_plan.startswith(\"```yaml\"):\n", "        refined_plan = refined_plan[7:]\n", "    if refined_plan.endswith(\"```\"):\n", "        refined_plan = refined_plan[:-3]\n", "    refined_plan = refined_plan.strip()\n", "    if identifier not in metadata_object:\n", "        print(f\"WARNING! Missing metadata for {identifier}\")\n", "        datapoint_path = pathlib.Path(f\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/{identifier}.yaml\")\n", "        metadata_object[identifier] = {\"plan-original\": yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)[\"plan\"]}\n", "    metadata_object[identifier][\"generated-plan-refined\"] = refined_plan\n", "yaml.dump(metadata_object, metadata_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Apply thoughts from metadata to the yamls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "from services.studio._text_to_workflow.common.walkers import WorkflowThoughtsModifier\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "\n", "# subset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "subset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\")\n", "\n", "for datapoint_path in subset_path.iterdir():\n", "    datapoint_identifier = datapoint_path.stem\n", "    if datapoint_identifier not in metadata_object:\n", "        print(f\"WARNING! Missing metadata for {datapoint_identifier}\")\n", "        continue\n", "    with open(datapoint_path, \"r\") as f:\n", "        datapoint = yaml.load(f, Loader=yaml.CSafeLoader)\n", "    metadatapoint = metadata_object[datapoint_identifier]\n", "    if datapoint[\"plan\"] != metadatapoint[\"plan-original\"]:\n", "        print(f\"Different for {datapoint_identifier}\")\n", "        continue\n", "\n", "    # proposed_plan = metadatapoint[\"generated-plan-refined\"]\n", "    proposed_plan = metadatapoint[\"generated-plan-descriptions\"]\n", "    # proposed_plan = metadatapoint[\"generated-plan-summaries\"]\n", "    # proposed_plan = metadatapoint[\"generated-plan-refined\"]\n", "    proposed_plan_thoughts = [re.sub(r\"^\\s*(\\d+\\.)+\\s*\", \"\", step) for step in proposed_plan.split(\"\\n\")]\n", "    workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "    try:\n", "        WorkflowThoughtsModifier(proposed_plan_thoughts).replace(workflow)\n", "    except:\n", "        print(\"-- ERROR ---\")\n", "        print(datapoint[\"plan\"])\n", "        print(proposed_plan)\n", "        print(\"-\" * 30)\n", "        print()\n", "        continue\n", "\n", "    datapoint[\"plan\"] = multiline_str(PlanBuilder().build(workflow))\n", "    datapoint[\"process\"] = workflow.to_dict()\n", "\n", "    # continue\n", "    with open(datapoint_path, \"w\") as f:\n", "        yaml.dump(datapoint, f, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Add query to metadata.yaml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_paths = [\n", "    pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\"),\n", "    pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\"),\n", "]\n", "datapoints = itertools.chain(*[root_path.glob(\"*.yaml\") for root_path in root_paths])\n", "\n", "stem_to_query = {}\n", "for datapoint_path in datapoints:\n", "    datapoint = yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "    stem = datapoint_path.stem\n", "    if stem.endswith(\".yaml\"):\n", "        stem = stem[:-5]\n", "    query = datapoint[\"description\"]\n", "    stem_to_query[stem] = query\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "for stem, datapoint in metadata_object.items():\n", "    if stem not in stem_to_query:\n", "        print(f\"WARNING! Missing query for {stem}\")\n", "        continue\n", "    new_datapoint = {}\n", "    new_datapoint[\"query-original\"] = stem_to_query[stem]\n", "    for k, v in datapoint.items():\n", "        if \"plan\" in k:\n", "            new_datapoint[k] = multiline_str(v)\n", "        else:\n", "            new_datapoint[k] = v\n", "    metadata_object[stem] = new_datapoint\n", "yaml.dump(metadata_object, metadata_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import prompt_llm_for_queries_and_iterate\n", "\n", "train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "# train_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\")\n", "dataset, queries, plans, loaded_dataset = [{} for _ in range(4)]\n", "for datapoint_path in train_path.iterdir():\n", "    datapoint = yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "    identifier = datapoint_path.as_posix()\n", "    dataset[identifier] = yaml.dump(datapoint[\"process\"], sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n", "    loaded_dataset[identifier] = datapoint[\"process\"]\n", "\n", "refined_queries = {}\n", "for identifier, refined_query in prompt_llm_for_queries_and_iterate(dataset, loaded_dataset):\n", "    identifier = pathlib.Path(identifier)\n", "    refined_queries[identifier.stem] = multiline_str(refined_query)\n", "\n", "with open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_queries_train.yaml\", \"w\") as f:\n", "    # with open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_queries_test.yaml\", \"w\") as f:\n", "    yaml.dump(refined_queries, f, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["refined_queries = yaml.load(open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_queries_train.yaml\"), Loader=yaml.CSafeLoader)\n", "refined_queries.update(\n", "    yaml.load(open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/refined_queries_test.yaml\"), Loader=yaml.CSafeLoader)\n", ")\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "for stem, datapoint in metadata_object.items():\n", "    if stem not in refined_queries:\n", "        print(f\"WARNING! Missing query for {stem}\")\n", "        continue\n", "    new_datapoint = {}\n", "    new_datapoint[\"query-original\"] = datapoint[\"query\"]\n", "    new_datapoint[\"query-refined\"] = refined_queries[stem]\n", "    for k, v in datapoint.items():\n", "        if k == \"query\":\n", "            continue  # got renamed\n", "        if k == \"proposed-plan-v2\":\n", "            continue  # delete these\n", "        if \"plan\" in k:\n", "            new_datapoint[k] = multiline_str(v)\n", "        else:\n", "            new_datapoint[k] = v\n", "    metadata_object[stem] = new_datapoint\n", "yaml.dump(metadata_object, metadata_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Gather summary plans to metadata.yaml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_summary.activity_summary_schema import ActivitySummaryRequest\n", "from services.studio._text_to_workflow.activity_summary.activity_summary_task import ActivitySummaryTask\n", "from services.studio._text_to_workflow.utils.llm_gateway_model import LLMGatewayModel\n", "from services.studio._text_to_workflow.utils.llm_schema import ConsumingFeatureType\n", "\n", "ACTIVITY_SUMMARY_TASK = ActivitySummaryTask(\"config.yaml\").build(True)\n", "\n", "config_path = pathlib.Path(\"/workspace/src/studio/text_to_workflow/activity_summary/config.yaml\")\n", "with open(config_path, \"r\") as f:\n", "    config = yaml.load(f, Loader=yaml.FullLoader)\n", "model_run_config = copy.deepcopy(config)\n", "model = LLMGatewayModel(ConsumingFeatureType.DEFAULT, **model_run_config[\"model\"])\n", "\n", "\n", "def get_summary(workflow: str):\n", "    summary_request = ActivitySummaryRequest(activity=workflow)\n", "    return ACTIVITY_SUMMARY_TASK.run(model, summary_request)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder, WorkflowThoughtsModifier\n", "\n", "# subset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "subset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test\")\n", "\n", "errors = []\n", "metadata_supplement = {}\n", "listing = list(subset_path.iterdir())\n", "# listing = errors\n", "for datapoint_path in tqdm.tqdm(listing):\n", "    try:\n", "        datapoint = yaml.load(datapoint_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "        workflow_object = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "        workflow_str = workflow_object.lmyaml(include_ids=True)\n", "        summary_result = get_summary(workflow_str)[\"result\"]\n", "        # print(yaml.dump(summary_result, sort_keys=False))\n", "\n", "        workflow_with_descriptions = copy.deepcopy(workflow_object)\n", "        id_to_descriptions = {item[\"id\"]: item[\"description\"] for item in summary_result[\"activityDescriptions\"]}\n", "        WorkflowThoughtsModifier(id_to_descriptions).replace(workflow_with_descriptions)\n", "        workflow_description = summary_result[\"workflowShortDescription\"]\n", "        plan_descriptions = PlanBuilder().build(workflow_with_descriptions)\n", "\n", "        workflow_with_summaries = copy.deepcopy(workflow_object)\n", "        id_to_summaries = {item[\"id\"]: item[\"summary\"] for item in summary_result[\"activitySummaries\"]}\n", "        WorkflowThoughtsModifier(id_to_summaries).replace(workflow_with_summaries)\n", "        workflow_summary = summary_result[\"workflowSummary\"]\n", "        plan_summaries = PlanBuilder().build(workflow_with_summaries)\n", "\n", "        stem = datapoint_path.stem\n", "        if stem.endswith(\".yaml\"):\n", "            stem = stem[:-5]\n", "\n", "        metadata_supplement[stem] = {\n", "            \"query-description\": workflow_description,\n", "            \"query-summary\": workflow_summary,\n", "            \"generated-plan-descriptions\": multiline_str(plan_descriptions),\n", "            \"generated-plan-summaries\": multiline_str(plan_summaries),\n", "        }\n", "        # print('=' * 30)\n", "        # print(yaml.dump(metadata_supplement, sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper))\n", "    except Exception:\n", "        import traceback\n", "\n", "        traceback.print_exc()\n", "        errors.append(datapoint_path)\n", "\n", "# yaml.dump(metadata_supplement, open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/summary_results_train.yaml\", \"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n", "# yaml.dump(metadata_supplement, open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/summary_results_test.yaml\", \"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)\n", "\n", "print(len(errors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metadata_supplement = yaml.load(\n", "    open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/summary_results_train.yaml\"), Loader=yaml.CSafeLoader\n", ")\n", "metadata_supplement.update(\n", "    yaml.load(open(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/summary_results_test.yaml\"), Loader=yaml.CSafeLoader)\n", ")\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "\n", "for stem, datapoint in metadata_object.items():\n", "    if stem not in metadata_supplement:\n", "        print(f\"WARNING! Missing summary for {stem}\")\n", "        continue\n", "    datapoint.update(metadata_supplement[stem])\n", "    new_datapoint = {}\n", "    for k, v in datapoint.items():\n", "        if \"query\" in k:\n", "            new_datapoint[k] = v\n", "    for k, v in datapoint.items():\n", "        if \"plan\" in k:\n", "            new_datapoint[k] = multiline_str(v)\n", "    for k, v in datapoint.items():\n", "        if \"query\" not in k and \"plan\" not in k:\n", "            new_datapoint[k] = v\n", "    metadata_object[stem] = new_datapoint\n", "yaml.dump(metadata_object, metadata_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Identify empty thoughts plans and repair if necessary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "from services.studio._text_to_workflow.common.walkers import WorkflowThoughtsModifier\n", "\n", "metadata_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/metadata.yaml\")\n", "metadata_object = yaml.load(metadata_path.open(\"r\"), Loader=yaml.CSafeLoader)\n", "\n", "counter = 0\n", "for identifier, metadatapoint in metadata_object.items():\n", "    proposed_plan = metadatapoint[\"generated-plan-summaries\"]\n", "    # proposed_plan = metadatapoint[\"generated-plan-summaries\"]\n", "    proposed_plan_thoughts = [re.sub(r\"^\\s*(\\d+\\.)+\\s*\", \"\", step) for step in proposed_plan.split(\"\\n\")]\n", "\n", "    original_plan_thoughts = [re.sub(r\"^\\s*(\\d+\\.)+\\s*\", \"\", step) for step in metadatapoint[\"plan-original\"].split(\"\\n\")]\n", "\n", "    # replace only trigger\n", "    # if proposed_plan_thoughts and not proposed_plan_thoughts[0] and \"trigger\" in original_plan_thoughts[0].lower():\n", "    #     proposed_plan_thoughts[0] = original_plan_thoughts[0]\n", "    # replace all\n", "\n", "    for i in range(len(proposed_plan_thoughts)):\n", "        if not proposed_plan_thoughts[i]:\n", "            proposed_plan_thoughts[i] = original_plan_thoughts[i]\n", "    reconstructed_proposed_plan = [\n", "        re.sub(r\"(?P<prefix>^\\s*(\\d+\\.)+\\s*)(?P<thought>.*)$\", rf\"\\g<prefix>{step}\", og_step)\n", "        for og_step, step in zip(proposed_plan.split(\"\\n\"), proposed_plan_thoughts, strict=False)\n", "    ]\n", "    reconstructed_proposed_plan = \"\\n\".join(reconstructed_proposed_plan)\n", "    metadatapoint[\"generated-plan-summaries\"] = multiline_str(reconstructed_proposed_plan)\n", "\n", "    for k, v in metadatapoint.items():  # preserve multiline\n", "        if \"plan\" in k:\n", "            metadatapoint[k] = multiline_str(v)\n", "\n", "    original_plan = metadatapoint[\"plan-original\"]\n", "    if not all(step for step in proposed_plan_thoughts):\n", "        print(identifier)\n", "        print(metadatapoint[\"plan-original\"])\n", "        print(proposed_plan)\n", "        print(reconstructed_proposed_plan)\n", "        print()\n", "        counter += 1\n", "print(counter)\n", "\n", "yaml.dump(metadata_object, metadata_path.open(\"w\"), sort_keys=False, width=1e5, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Apply to xaml directly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Apply on everything"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.autopilot_dataset.wfgen_yaml_to_xaml import yaml_to_xaml\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "for path in paths.get_workflow_generation_dataset_path(\"Portable\", \"train\").glob(\"*.yaml\"):\n", "    try:\n", "        yaml_to_xaml(path, dry_run=False)\n", "    except Exception as e:\n", "        print(path)\n", "        print(e)\n", "for path in paths.get_workflow_generation_dataset_path(\"Portable\", \"test\").glob(\"*.yaml\"):\n", "    yaml_to_xaml(path, dry_run=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import workflow_utils\n", "\n", "a = \"\\n\".join(str(i) for i in range(30))\n", "b = \"\\n\".join(str(i) if not 12 < i < 15 else \"A\" for i in range(30))\n", "print(workflow_utils.get_diff(a, b, 1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Apply on a single example and test cycle consistency"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.autopilot_dataset.wfgen_generate_metadata import generate_query_and_plan_inline\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/StudioDesktop_complex_examples_20240517_SortEmails.yaml\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Migrating plan to yaml\")\n", "assert \"description-refined\" not in yaml_load(path)\n", "assert \"plan-refined\" not in yaml_load(path)\n", "generate_query_and_plan_inline(path)\n", "assert \"description-refined\" in yaml_load(path)\n", "assert \"plan-refined\" in yaml_load(path)\n", "print(yaml_load(path)[\"description-refined\"])\n", "print(yaml_load(path)[\"plan-refined\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.autopilot_dataset.online_client import convert_and_return_workflow\n", "from services.studio._text_to_workflow.autopilot_dataset.wfgen_yaml_to_xaml import yaml_to_xaml\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "\n", "reconverted_original_workflow = yaml_load(convert_and_return_workflow(path))\n", "yaml_to_xaml(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reconverted_modified_workflow = yaml_load(convert_and_return_workflow(path))\n", "current_workflow = yaml_load(path)[\"process\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "\n", "def show_diff(wf1, wf2, workflow_normalization=False):\n", "    wf1 = Workflow(\"\", \"\", wf1).to_dict()\n", "    wf2 = Workflow(\"\", \"\", wf2).to_dict()\n", "    print(get_diff(yaml_dump(wf1), yaml_dump(wf2)))\n", "\n", "\n", "# show_diff(reconverted_original_workflow, reconverted_modified_workflow)\n", "# show_diff(current_workflow, reconverted_modified_workflow)\n", "show_diff(current_workflow, reconverted_original_workflow)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import zipfile\n", "\n", "tmpdir = pathlib.Path(\"/tmp/tmpumlvavzj\")\n", "display(list(tmpdir.iterdir()))\n", "\n", "with zipfile.ZipFile(f\"{tmpdir}/output.zip\", \"r\") as zip_ref:\n", "    output_path = tmpdir / \"output\"\n", "    output_path.mkdir(exist_ok=True)\n", "    zip_ref.extractall(output_path)\n", "with zipfile.ZipFile(f\"{tmpdir}/input.zip\", \"r\") as zip_ref:\n", "    input_path = tmpdir / \"input\"\n", "    input_path.mkdir(exist_ok=True)\n", "    zip_ref.extractall(input_path)\n", "display(list((tmpdir / \"input\").iterdir()))\n", "display(list((tmpdir / \"output\").iterdir()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test new implementation for plan builder and ifelseifv2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.common.walkers import ActivityIdCollectorIncludingSyntheticSteps, PlanBuilder, SyntheticStepsWalker\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/StudioDesktop_IFWhileElse_IFELSE.yaml\")\n", "# path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/StudioDesktop_fixes_20240411_[<PERSON><PERSON>] Upload email attachments to OneDrive or SharePoint.yaml\")\n", "# path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/StudioDesktop_IFWhileElse_RETRYSCOPE.yaml\")\n", "datapoint = yaml_load(path)\n", "existing_plan = datapoint[\"plan\"]\n", "print(existing_plan)\n", "print(\"-\" * 30)\n", "workflow_object = Workflow(datapoint[\"description\"], existing_plan, datapoint[\"process\"])\n", "built_plan = PlanBuilder().build(workflow_object)\n", "print(built_plan)\n", "\n", "\n", "class CustomWalker(SyntheticStepsWalker):\n", "    def process_activity(self, activity):\n", "        print(activity.fqn, activity.display_name)\n", "\n", "\n", "CustomWalker().walk_workflow(workflow_object)\n", "\n", "display(ActivityIdCollectorIncludingSyntheticSteps().get_ids(workflow_object))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import load_workflow_generation_dataset\n", "\n", "wfgen_dataset = load_workflow_generation_dataset(\"Portable\", ignored_subsets={\"prod\"})\n", "print(wfgen_dataset.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path, datapoint in wfgen_dataset[\"train\"].items():\n", "    existing_plan = datapoint[\"plan\"]\n", "    workflow_object = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "    rebuilt_plan = PlanBuilder().build(workflow_object)\n", "    if existing_plan != rebuilt_plan:\n", "        print(path)\n", "        print(existing_plan)\n", "        print(\"-\" * 30)\n", "        print(rebuilt_plan)\n", "        raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mark these dataset items as having manual annotated query or plan"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "# mark these current queries as manual:\n", "manual_queries_entries = {\n", "    # # from PR148\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Gmail to <PERSON><PERSON> task\",\n", "    # \"StudioDesktop_confluence_20240422_Invoice is older than 90 days throw a business exception\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Search for Gmails on  invoices or payment confirmations and upload this to Gdrive\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] New SAP Concur Expense when Outlook email received\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Backup Important Gmail email to GDrive\",\n", "    # # from PR151\n", "    # \"StudioDesktop_fixes_20240412_[Client] Create Slack Channel From Email\",\n", "    # \"StudioDesktop_fixes_20240412_[Client] Save Outlook Emails To Excel On OneDrive\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Copy files from Google Drive to OneDrive\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Search for emails and make a list of my emails subscription\",\n", "    # # from PR152\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Search in my Gmails and make a list of my subscription emails\",\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Get Outlook meetings from last week\",\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Search for emails on invoices or payment confirmations and upload this to OneDrive\",\n", "}\n", "\n", "# mark these current plans as manual\n", "manual_plan_entries = {\n", "    # # from PR148\n", "    # \"StudioDesktop_confluence_20240422_Invoice is older than 90 days throw a business exception\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Search for Gmails on  invoices or payment confirmations and upload this to Gdrive\",\n", "    # \"StudioDesktop_fixes_20240412_[Client] Read emails from Outlook folder\",\n", "    # \"StudioDesktop_JSONSTUFF_DEJSON3\",\n", "    # # from PR151\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Search for Gmails on  invoices or payment confirmations and upload this to Gdrive\",\n", "    # \"StudioDesktop_fixes_20240412_[Client] Create Slack Channel From Email\",\n", "    # \"StudioDesktop_fixes_20240412_[Client] Save Outlook Emails To Excel On OneDrive\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Copy files from Google Drive to OneDrive\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] On every weekday at lunch send me a message on Slack\",\n", "    # \"StudioDesktop_fixes_20240408_[Confluence] Search for emails and make a list of my emails subscription\",\n", "    # # from PR152\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Search in my Gmails and make a list of my subscription emails\",\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Get Outlook meetings from last week\",\n", "    \"StudioDesktop_fixes_20240408_[Confluence] When a high priority ticket is created in Freshdesk send a message on Slack\",\n", "    \"StudioDesktop_fixes_20240408_[Confluence] Search for emails on invoices or payment confirmations and upload this to OneDrive\",\n", "    \"StudioDesktop_fixes_20240412_[Client] Generate Outlook Email And Send To A List From Google Spreadsheet\",\n", "}\n", "\n", "metadata_path = paths.get_workflow_generation_metadata_path(\"Portable\")\n", "metadata = yaml_load(metadata_path, load_multiline=True)\n", "for identifier in manual_queries_entries | manual_plan_entries:\n", "    if identifier not in metadata:\n", "        print(f\"WARNING! Missing query for {identifier}\")\n", "        continue\n", "    metadatapoint = metadata[identifier]\n", "    # we do this in order to enforce ordering of metadatapoint\n", "    new_metadatapoint = {}\n", "    for k, v in metadatapoint.items():\n", "        new_metadatapoint[k] = v\n", "        if identifier in manual_queries_entries and k == \"query-summary\":  # add manual query after summary query (last one)\n", "            if \"query-manual\" in metadatapoint:\n", "                print(f\"WARNING! Overwriting manual query for {identifier}\")\n", "            new_metadatapoint[\"query-manual\"] = copy.deepcopy(metadatapoint[\"query-current\"])\n", "        if identifier in manual_plan_entries and k == \"plan-current\":  # add manual plan after current plan (last one not generated)\n", "            if \"plan-manual\" in metadatapoint:\n", "                print(f\"WARNING! Overwriting manual query for {identifier}\")\n", "            new_metadatapoint[\"plan-manual\"] = copy.deepcopy(metadatapoint[\"plan-current\"])\n", "    metadata[identifier] = new_metadatapoint\n", "yaml_dump(metadata, metadata_path)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}