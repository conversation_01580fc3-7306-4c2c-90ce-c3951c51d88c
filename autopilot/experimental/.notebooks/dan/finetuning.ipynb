{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Create a .jsonl from a .html evaluation file"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# html_path = pathlib.Path(\"/workspace/data/workdir/output-v48-wfgen-train-gpt4o-gpt4omini.html\")  # train\n", "html_path = pathlib.Path(\"/workspace/data/workdir/output-v47-wfgen-gpt4o-gpt4omini-control-after-renames.html\")  # test\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import parse_html\n", "\n", "parsed_datapoints, parsed_aggregates = parse_html(html_path)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "\n", "finetune_dataset = []\n", "for dp in parsed_datapoints:\n", "    _, *chunks = re.split(r\"(System:\\s?|Human:\\s?|AI:\\s?)\", dp[\"planning_prompt\"])\n", "    chunks.append(\"AI: \")\n", "    chunks.append(dp[\"ideal_plan\"])\n", "    if not chunks[0].startswith(\"System:\"):\n", "        print(dp[\"query\"])\n", "        continue\n", "    assert len(chunks) % 4 == 2  # pairs of role and content, there should be odd number of pairs\n", "\n", "    messages = []\n", "    for i in range(0, len(chunks), 2):\n", "        role, content = chunks[i:i+2]\n", "        role = role.strip(\": \")\n", "        role_mapping = {\n", "            \"System\": \"system\",\n", "            \"Human\": \"user\",\n", "            \"AI\": \"assistant\",\n", "        }\n", "        messages.append({\"role\": role_mapping[role], \"content\": content})\n", "    finetune_datapoint = {\"messages\": messages}\n", "    finetune_dataset.append(finetune_datapoint)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "timestamp = datetime.datetime.now().strftime(\"%Y%m%d\")\n", "# finetune_dataset_path = paths.get_workdir_path() / \"finetuning\" / f\"oai-plan-ft-train-data-{timestamp}.jsonl\"\n", "finetune_dataset_path = paths.get_workdir_path() / \"finetuning\" / f\"oai-plan-ft-test-data-{timestamp}.jsonl\"\n", "finetune_dataset_path.parent.mkdir(parents=True, exist_ok=True)\n", "with open(finetune_dataset_path, \"w\") as f:\n", "    for dp in finetune_dataset:\n", "        f.write(json.dumps(dp))\n", "        f.write(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}