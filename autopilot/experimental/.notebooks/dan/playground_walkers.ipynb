{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import load_dataset, load_workflow_generation_dataset\n", "\n", "dataset = load_workflow_generation_dataset(\"Portable\")\n", "# dataset = load_workflow_generation_dataset(\"Windows\")\n", "# dataset = load_dataset(paths.get_workflow_generation_dataset_path(\"Windows\"), ignored_subsets=[\"prod\", \"prod-filtered\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs\n", "\n", "await typedefs.build()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.common import typedefs\n", "from services.studio._text_to_workflow.common.helpers import Tee\n", "from services.studio._text_to_workflow.common.walkers import SynopsisBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.typedefs_parser import parse_workflow_conversion_typedefs\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "typedefs.load()\n", "\n", "with <PERSON><PERSON>(pathlib.Path(\"synopsis_diffs_v3.txt\")):\n", "    for path, datapoint in (dataset[\"train\"] | dataset[\"test\"]).items():\n", "    # for path, datapoint in dataset[\"test\"].items():\n", "        # if path != pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test/StudioDesktop_GmailAct_Archive.yaml\"):\n", "        #     continue\n", "\n", "        # path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train/StudioDesktop_fixes_20240411_[<PERSON><PERSON>] Upload email attachments to OneDrive or SharePoint.yaml\")\n", "        # datapoint = yaml_load(path)\n", "\n", "        workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "\n", "        workflow_activity_typedefs, _ = parse_workflow_conversion_typedefs(datapoint[\"typedefs\"])\n", "        # workflow_activity_typedefs, _ = FillMissingWorkflowActivityTypedefs(workflow, workflow_activity_typedefs, {}).fill()\n", "        # workflow_variables = CollectWorkflowVariables(workflow, workflow_activity_typedefs, {}).collect()\n", "        # available_variables = set(itertools.chain.from_iterable(workflow_variables.values()))  # type: ignore\n", "        # available_variables = set(itertools.chain(workflow.variables, workflow.arguments))\n", "        # synopsis1 = SynopsisBuilder(workflow_activity_typedefs, parse_expressions=False).build(workflow)\n", "        print(path)\n", "        synopsis = SynopsisBuilder(workflow_activity_typedefs).build(workflow)\n", "        print(datapoint[\"description\"])\n", "        # print(get_diff(synopsis1, synopsis))\n", "        # print(synopsis1)\n", "        print(synopsis)\n", "        print()\n", "        # break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate the differences after connections cache update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever\n", "\n", "retriever = ActivitiesRetriever().ensure_loaded()\n", "\n", "def find_all_warnings(path):\n", "    warnings = []\n", "    for line in path.open():\n", "        if line.startswith(\"WARNING! No global typedef found for \"):\n", "            warnings.append(line.strip().split()[-1])\n", "    return warnings\n", "\n", "w1 = find_all_warnings(pathlib.Path(\"synopsis_diffs_v1.txt\"))\n", "# w2 = find_all_warnings(pathlib.Path(\"synopsis_diffs_v2.txt\"))\n", "w2 = find_all_warnings(pathlib.Path(\"synopsis_diffs_v3.txt\"))\n", "print(\"Fixed\")\n", "for w in sorted(set(w1) - set(w2)):\n", "    info = retriever.get(w, \"Windows\")\n", "    if info is None:\n", "        print(w, \"unretrievable\")\n", "        continue\n", "    print(f'{info[\"connectorKey\"]:30}', w, info[\"className\"])\n", "print(\"Introduced\")\n", "for w in sorted(set(w2) - set(w1)):\n", "    info = retriever.get(w, \"Windows\")\n", "    if info is None:\n", "        print(w, \"unretrievable\")\n", "        continue\n", "    print(f'{info[\"connectorKey\"]:30}', w, info[\"className\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever\n", "from services.studio._text_to_workflow.common.typedefs import _activity_typedefs\n", "\n", "ret = ActivitiesRetriever()\n", "act_id = 'UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity@58d96648-3237-3496-a5eb-dbf633a98f2a'\n", "# ret.get(act_id, \"Windows\")\n", "# _activity_typedefs[act_id]\n", "# _additional_typedefs[act_id]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs\n", "\n", "await typedefs.build()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted(_activity_typedefs.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Map missing activities to their workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "dataset = load_workflow_generation_dataset(\"Portable\")\n", "_special_activities = {  # No activity definition for these\n", "    # \"UiPath.Core.Activities.IfElseIfV2\": \"IfElseIfV2\",\n", "    # \"UiPath.Core.Activities.IfElseIfV2.ElseIfClause\": \"ElseIf\",  # legit\n", "    # \"System.Activities.Statements.InvokeMethod\": \"InvokeMethod\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity@b99b521b-c81b-3490-8553-18b9f1bdd964\": \"UnknownGithubIssueCreate\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorTriggerActivity@ffeb32e1-3337-350d-b358-ea5a657e9d67\": \"UnknownPullRequestCreated\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@67c3bebc-9c43-36e2-87bd-57bcbec902a8\": \"UnknownOpenAI1\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@2d06467e-5c0a-3e14-b145-96a314116d8d\": \"UnknownOpenAI2\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@be8ce9db-240e-3839-88c6-3f611c01df17\": \"UnknownSlackListAllChannels\",\n", "    # \"UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity@a166e307-e42b-3adb-97ab-cef01e60c768\": \"UnknownGoogleVertex\",\n", "    # from Windows\n", "    \"System.Activities.Statements.Flowchart\": \"Flowchart\",\n", "    \"UiPath.Core.Activities.TakeScreenshot\": \"TakeScreenshot\",\n", "    \"UiPath.Database.Activities.DatabaseTransaction\": \"DatabaseTransaction\",\n", "    \"UiPath.Excel.Activities.Business.ChartModifications.ChangeDataRangeModification\": \"ChangeDataRangeModification\",\n", "    \"UiPath.Excel.Activities.Business.ExecuteMacroArgumentX\": \"ExecuteMacroArgumentX\",\n", "    \"UiPath.Excel.Activities.Business.InvokeVBAArgumentX\": \"InvokeVBAArgumentX\",\n", "    \"UiPath.Excel.Activities.Business.PivotTableFieldX\": \"PivotTableFieldX\",\n", "    \"UiPath.Excel.Activities.Business.SortColumnX\": \"SortColumnX\",\n", "    \"UiPath.GSuite.Activities.AddNewSheet\": \"AddNewSheet\",\n", "    \"UiPath.GSuite.Activities.DownloadSpreadsheet\": \"DownloadSpreadsheet\",\n", "    \"UiPath.GSuite.Activities.ReadRange\": \"ReadRange\",\n", "    \"UiPath.GSuite.Activities.Sheets.GoogleSheetsApplicationScope\": \"GoogleSheetsApplicationScope\",\n", "    \"UiPath.GSuite.Activities.WriteRange\": \"WriteRange\",\n", "    \"UiPath.Presentations.Activities.ChangeShapeNameSlideContentModification\": \"ChangeShapeNameSlideContentModification\",\n", "    \"UiPath.Presentations.Activities.RunMacroArgument\": \"RunMacroArgument\",\n", "    \"UiPath.Testing.Activities.ArgumentsBridge\": \"ArgumentsBridge\",\n", "    \"UiPath.Testing.Activities.VerifyControlAttribute\": \"VerifyControlAttribute\",\n", "    \"UiPath.UIAutomationNext.Activities.NExtractData\": \"NExtractData\",\n", "}\n", "\n", "windows_dataset = load_dataset(paths.get_workflow_generation_dataset_path(\"Windows\"), ignored_subsets=[\"prod\", \"prod-filtered\"])\n", "\n", "activity_to_workflows = collections.defaultdict(list)\n", "for path, datapoint in (dataset[\"train\"] | dataset[\"test\"] | windows_dataset[\"train\"] | windows_dataset[\"test\"]).items():\n", "    wf_obj = Workflow(\"\", \"\", datapoint[\"process\"])\n", "    act_and_t = ActivitiesAndTriggersCollector().collect(wf_obj)\n", "    act_and_t = act_and_t[\"activity\"] + act_and_t[\"trigger\"]\n", "    \n", "    for act in act_and_t:\n", "        if act.activity_id in _special_activities:\n", "            activity_to_workflows[act.activity_id].append(path)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for activity_id, workflow_paths in activity_to_workflows.items():\n", "    print(activity_id)\n", "    for path in workflow_paths:\n", "        print(path)\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Translate daps walker experimentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs\n", "from services.studio._text_to_workflow.common.helpers import Tee\n", "from services.studio._text_to_workflow.common.walkers import DAPClassNameTranslator, PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.typedefs_parser import parse_workflow_conversion_typedefs\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump\n", "\n", "\n", "class ActivityIdTreeBuilder(PlanBuilder):\n", "    def process_activity(self, activity):\n", "        self.add_plan_step(activity.activity_id)\n", "\n", "typedefs.load()\n", "\n", "for path, datapoint in (dataset[\"train\"] | dataset[\"test\"]).items():\n", "    if \"Connector\" not in yaml_dump(datapoint[\"process\"]):\n", "        continue\n", "    workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "    # workflow_activity_typedefs, _ = parse_workflow_conversion_typedefs(datapoint[\"typedefs\"])\n", "    # synopsis = SynopsisBuilder(workflow_activity_typedefs).build(workflow)\n", "\n", "    translated_workflow = DAPClassNameTranslator().translate(workflow)\n", "    translated_workflow = Workflow(\"\", \"\", translated_workflow.to_dict())\n", "    print(path)\n", "    print(datapoint[\"description\"])\n", "    print(ActivityIdTreeBuilder().build(workflow))\n", "    print()\n", "    print(ActivityIdTreeBuilder().build(translated_workflow))\n", "    print()\n", "    print(workflow.lmyaml())\n", "    print()\n", "    print(translated_workflow.lmyaml())\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["activity_dict = yaml_load(\"\"\"\\\n", "- thought: Create Freshdesk ticket with event details\n", "  activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity\n", "  params:\n", "    priority: '1'\n", "    status: '2'\n", "    UiPathActivityTypeId: 489bd332-d516-3ad9-b23f-b21bb4fd1a2b\n", "    description: '[[googleEvent.Description]]'\n", "    email: '[[googleEvent.OrganizerEmail]]'\n", "    Jit_tickets: '[[FreshDeskTicket]]'\n", "    out_id: '[[FreshDesk_out_Ticket_id]]'\n", "    subject: '[[googleEvent.Summary]]'\"\"\")[0]\n", "\n", "activity_dict = yaml_load(\"\"\"\\\n", "- thought: Create Freshdesk ticket with event details\n", "  activity: UiPath.IntegrationService.Activities.Runtime.Activities.FreshdeskCreate_Ticket\n", "  uiPathActivityTypeId: 489bd332-d516-3ad9-b23f-b21bb4fd1a2b\n", "  params:\n", "    priority: '1'\n", "    status: '2'\n", "    description: '[[googleEvent.Description]]'\n", "    email: '[[googleEvent.OrganizerEmail]]'\n", "    Jit_tickets: '[[FreshDeskTicket]]'\n", "    out_id: '[[FreshDesk_out_Ticket_id]]'\n", "    subject: '[[googleEvent.Summary]]'\"\"\")[0]\n", "\n", "from services.studio._text_to_workflow.common.workflow import Activity\n", "\n", "act = Activity(activity_dict, [], [], None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(act.lmyaml(include_dap_properties=False))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}