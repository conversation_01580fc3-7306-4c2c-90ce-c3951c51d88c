{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clean stale summarization datapoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# verify stale activity_summarization datapoints\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "for dataset_path in [\n", "    paths.get_workflow_summarization_dataset_path(\"Portable\", \"train\"),\n", "    paths.get_workflow_summarization_dataset_path(\"Portable\", \"test\"),\n", "    # paths.get_workflow_summarization_dataset_path(\"Portable\", \"static\"),\n", "    # paths.get_workflow_summarization_dataset_path(\"Portable\", \"uia\"),\n", "]:\n", "    *_, framework, subset = dataset_path.parts\n", "    dataset_generation_counterpart_path = paths.get_workflow_generation_dataset_path(framework, subset)\n", "    print(dataset_path)\n", "    import collections\n", "    counter = collections.Counter()\n", "    for filepath in sorted(dataset_path.rglob(\"*.yaml\")):\n", "        if (dataset_generation_counterpart_path / filepath.name).exists():\n", "            # print(\"Yey\")\n", "            counter['good'] += 1\n", "            continue\n", "        else:\n", "            # print(\"Ney\", filepath)\n", "            counter['old'] += 1\n", "            for attempt_subset in dataset_generation_counterpart_path.parent.iterdir():\n", "                if (dataset_generation_counterpart_path.parent / attempt_subset / filepath.name).exists():\n", "                    counter[attempt_subset.name] += 1\n", "            else:\n", "                counter['not_found'] += 1\n", "            filepath.unlink()\n", "    print(counter)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Aggregate representations for activities in embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_summary.activity_summary_helpers import get_unique_activity_names\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "all_activity_formats = set()\n", "\n", "# framework = \"Portable\"\n", "framework = \"Windows\"\n", "\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for dataset_path in [\n", "        paths.get_workflow_generation_dataset_path(framework, \"static\"),\n", "        paths.get_workflow_generation_dataset_path(framework, \"uia\"),\n", "        paths.get_workflow_generation_dataset_path(framework, \"train\"),\n", "        paths.get_workflow_generation_dataset_path(framework, \"test\"),\n", "    ]:\n", "        *_, framework, subset = dataset_path.parts\n", "        for filepath in dataset_path.rglob(\"*.yaml\"):\n", "            all_activity_formats |= get_unique_activity_names({\"input\": yaml_load(filepath)[\"process\"]}, framework)\n", "print(\"\\n\".join(sorted(all_activity_formats)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "from services.studio._text_to_workflow.autopilot_dataset.wfgen_yaml_to_xaml import wfgen_path_to_converted_path\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "stats = collections.Counter()\n", "\n", "for dataset_path in [\n", "    paths.get_workflow_generation_dataset_path(\"Windows\", \"static\"),\n", "    paths.get_workflow_generation_dataset_path(\"Windows\", \"uia\"),\n", "    paths.get_workflow_generation_dataset_path(\"Windows\", \"train\"),\n", "    paths.get_workflow_generation_dataset_path(\"Windows\", \"test\"),\n", "]:\n", "    *_, framework, subset = dataset_path.parts\n", "    for filepath in sorted(dataset_path.rglob(\"*.yaml\")):\n", "        try:\n", "            if not wfgen_path_to_converted_path(filepath).exists():\n", "                print(filepath)\n", "            stats['valid'] += 1\n", "        except:\n", "            print(filepath)\n", "            stats[\"invalid\"] += 1\n", "stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "yaml_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/Converted/Portable/StudioDesktop_IFWhileElse_IFELSE/Main.yaml\")\n", "workflow = yaml_load(yaml_path)\n", "json_path = yaml_path.with_suffix(\".json\")\n", "json_data = json.load(json_path.open())\n", "json_data[\"result\"] = yaml_dump(workflow)\n", "json.dump(json_data, json_path.open(\"w\"), indent=2)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}