{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Workflow Edit Dataset Experimentation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.models.model_manager import ModelManager\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "embedding_model = ModelManager().get_embeddings_model()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "datapoints = {}\n", "# wfgen_dataset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WfGenV2/Portable\")\n", "wfgen_dataset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WfGenV2/Windows\")\n", "for subset in [\"train\", \"test\"]:\n", "    wfgen_subset_path = wfgen_dataset_path / subset\n", "    for file in wfgen_subset_path.rglob(\"*.yaml\"):\n", "        datapoints[file] = yaml_load(file.read_text())\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["for path, datapoint in datapoints.items():\n", "    datapoint[\"path\"] = path\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["for datapoint in datapoints.values():\n", "    datapoint[\"query-embedding\"] = embedding_model.encode(datapoint[\"query\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import itertools\n", "import numpy as np\n", "\n", "similarities = []\n", "# datapoints_to_similarities = {k: v for k, v in datapoints.items() if v[\"mode\"] == \"workflow\"}\n", "datapoints_to_similarities = {k: v for k, v in datapoints.items()}\n", "for dp1, dp2 in itertools.combinations(datapoints_to_similarities.values(), 2):\n", "    # cosine = dp1[\"query-embedding\"].dot(dp2[\"query-embedding\"]) / (\n", "    #     np.linalg.norm(dp1[\"query-embedding\"]) * np.linalg.norm(dp2[\"query-embedding\"])\n", "    # )\n", "    cosine = dp1[\"query-embedding\"].dot(dp2[\"query-embedding\"])  # already normalized\n", "    similarities.append({\n", "        \"score\": cosine,\n", "        \"dp1\": dp1,\n", "        \"dp2\": dp2,\n", "    })\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "plt.hist([s[\"score\"] for s in similarities], bins=100)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["similarities.sort(key=lambda x: x['score'], reverse=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get top 1%\n", "# top_n = int(len(similarities) * 0.01)\n", "from services.studio._text_to_workflow.utils.unidiff_utils import get_unidiff\n", "\n", "top_n = 500\n", "similarity_threshold = 0.7\n", "top_pairs = similarities[:top_n]\n", "\n", "pairs = []\n", "# Print the most similar pairs\n", "print(f\"Top {top_n} most similar pairs:\\n\")\n", "for pair in top_pairs:\n", "    if pair[\"score\"] >= 0.99:\n", "        continue\n", "    if pair[\"score\"] < similarity_threshold:\n", "        continue\n", "    # if pair[\"dp1\"][\"mode\"] != \"workflow\" or pair[\"dp2\"][\"mode\"] != \"workflow\":\n", "    #     continue\n", "    print(f\"Score: {pair['score']:.3f}\")\n", "    print(f\"Query 1: {pair['dp1']['query']}\")\n", "    print(f\"Query 2: {pair['dp2']['query']}\")\n", "    print(f\"Query 1: {pair['dp1']['path']}\")\n", "    print(f\"Query 2: {pair['dp2']['path']}\")\n", "    print(get_unidiff(yaml_dump(pair[\"dp1\"][\"solution_workflow\"]), yaml_dump(pair[\"dp2\"][\"solution_workflow\"])))\n", "    pairs.append(pair)\n", "\n", "print(f\"Found {len(pairs)} pairs with score < 1.0 among top {top_n} pairs\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump\n", "from services.studio._text_to_workflow.utils.unidiff_utils import get_unidiff\n", "\n", "\n", "system_prompt = \"\"\"\n", "You are a helpful assistant that generates semantic queries for editing workflows.\n", "The user will provide you with two workflows, and you will generate a query that provides a semantic description of how to edit the first workflow into the second.\n", "The queries you generate should be concise short and imperative.\n", "The queries should be able to be answered by a LLM, and should be no more than 10 words.\n", "Output queries that you would be able to resolve.\n", "In case you see no proper way to edit the first workflow into the second, just return \"NO EDIT\".\n", "\"\"\"\n", "\n", "user_prompt = \"\"\"\n", "Create a prompt that would succintly specify how to edit the first workflow into the second. Provide a semantic query, do not list verbatim the changes.\n", "\n", "Here are some type definitions for the available activities in the workflows:\n", "{activity_type_definitions}\n", "\n", "Here are some additional type definitions for the available activities in the workflows:\n", "{additional_type_definitions}\n", "\n", "Original query: {query1}\n", "Original workflow:\n", "```yaml\n", "{workflow1}\n", "```\n", "\n", "Please modify this workflow to instead: {query2}\n", "Target workflow:\n", "```yaml\n", "{workflow2}\n", "```\n", "\n", "Here's also a unidiff between the two workflows:\n", "```diff\n", "{diff}\n", "```\n", "\n", "Remember to answer very broadly what you would like the edit to be, and not to list verbatim the changes.\n", "\"\"\"\n", "\n", "# Test it on the first few similar pairs\n", "print(\"<PERSON><PERSON> edit prompts:\")\n", "for pair in pairs[:3]:  # Look at first 3 pairs\n", "# for pair in pairs:\n", "    print(\"\\n\" + \"=\"*80)\n", "    def prepare_workflow(workflow):\n", "        workflow = copy.deepcopy(workflow)\n", "        workflow.pop(\"processName\", None)\n", "        workflow.pop(\"packages\", None)\n", "        return yaml_dump(workflow)\n", "\n", "    workflow1 = prepare_workflow(pair[\"dp1\"][\"solution_workflow\"])\n", "    workflow2 = prepare_workflow(pair[\"dp2\"][\"solution_workflow\"])\n", "    diff = get_unidiff(workflow1, workflow2)\n", "    activity_typedefs, additional_typedefs = await get_activity_definitions(pair)\n", "    print(user_prompt.format(\n", "        query1=pair[\"dp1\"][\"query\"],\n", "        query2=pair[\"dp2\"][\"query\"],\n", "        activity_type_definitions=activity_typedefs,\n", "        additional_type_definitions=additional_typedefs,\n", "        workflow1=workflow1,\n", "        workflow2=workflow2,\n", "        diff=diff,\n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs_parser\n", "from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever\n", "from services.studio._text_to_workflow.common.connections_loader import get_connections_data\n", "from services.studio._text_to_workflow.common.schema import Connection\n", "from services.studio._text_to_workflow.common.walkers import ActivityTypeCollector\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "from services.studio._text_to_workflow.utils.activity_utils import build_activity_type_definitions, build_additional_type_definitions\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "\n", "workflow_fix_task_handle = WorkflowFixTask()\n", "retriever = ActivitiesRetriever().ensure_loaded()\n", "\n", "async def get_activity_definitions(pair):\n", "    wf1 = Workflow(\"\", \"\", pair[\"dp1\"][\"solution_workflow\"])\n", "    collector1 = ActivityTypeCollector()\n", "    collector1.walk_workflow(wf1)\n", "\n", "    wf2 = Workflow(\"\", \"\", pair[\"dp2\"][\"solution_workflow\"])\n", "    collector2 = ActivityTypeCollector()\n", "    collector2.walk_workflow(wf2)\n", "\n", "    merged_activities_types = copy.deepcopy(collector1.typed_activity_instances)\n", "    for k, v in collector2.typed_activity_instances.items():\n", "        merged_activities_types[k].extend(v)\n", "\n", "    # these parts were taken from workflow fix - update as neeeded\n", "    activity_ids = sorted(collector1.get_activity_types() | collector2.get_activity_types())\n", "    activity_definitions = []\n", "    # augmented_definitions = []\n", "    for activity_id, activity_examples in merged_activities_types.items():\n", "        # activity_def = retriever.get(activity_id, \"Portable\")\n", "        activity_def = retriever.get(activity_id, \"Windows\")\n", "        if not activity_def:\n", "            continue\n", "        \n", "        curr_activity_def = activity_def\n", "        for activity in activity_examples:\n", "            if activity.is_dynamic:\n", "                curr_activity_def = copy.deepcopy(activity_def)\n", "                curr_activity_def[\"uuid\"] = activity.id\n", "                curr_activity_def[\"defaultConfiguration\"] = curr_activity_def[\"activityConfiguration\"]\n", "                curr_activity_def[\"activityIsConfigured\"] = False\n", "                if activity.dap_is_configured:\n", "                    curr_activity_def[\"activityConfiguration\"] = activity.dap_config\n", "                    curr_activity_def[\"activityIsConfigured\"] = True\n", "                    curr_activity_def[\"dynamicActivityDetails\"] = activity.dynamic_activity_details\n", "\n", "        # augmented_definitions.append(curr_activity_def)\n", "        activity_definitions.append(curr_activity_def)\n", "    _tenant_id, connections = get_connections_data()\n", "    connections_by_connector: dict[str, Connection] = {}\n", "    for connection in connections:\n", "        if not (connector := connection[\"connector\"]):\n", "            continue\n", "        if connector not in connections_by_connector or connection.get(\"isDefault\", True):\n", "            connections_by_connector[connector] = connection\n", "    augmented_definitions, _ = await workflow_fix_task_handle.augment_dynamic_activities_type_definitions(activity_definitions, connections_by_connector)\n", "    activity_typedef_str = build_activity_type_definitions(augmented_definitions)\n", "    additional_typedef_str = build_additional_type_definitions(augmented_definitions, \"\")\n", "    return activity_typedef_str, additional_typedef_str\n", "\n", "a, b = await get_activity_definitions(pairs[2])\n", "# print(a)\n", "# print(b)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import langchain_core\n", "import tqdm\n", "from services.studio._text_to_workflow.common.schema import WorkflowDict\n", "from services.studio._text_to_workflow.common.walkers import ActivityTypeCollector, DAPClassNameTranslator\n", "from services.studio._text_to_workflow.common.workflow_parser import remove_fields\n", "from services.studio._text_to_workflow.utils.inference import llm_schema\n", "from services.studio._text_to_workflow.models.model_manager import ModelManager\n", "\n", "\n", "# model = ModelManager().get_llm_model(\"workflow_draft_generation_model\", llm_schema.ConsumingFeatureType.WORKFLOW_EDIT)\n", "# model = ModelManager().get_llm_model(\"workflow_generation_long_reasoning_model\", llm_schema.ConsumingFeatureType.WORKFLOW_EDIT)\n", "model = ModelManager().get_llm_model(\"workflow_generation_reasoning_model\", llm_schema.ConsumingFeatureType.WORKFLOW_EDIT)\n", "# model.max_model_tokens = 500\n", "\n", "print(\"Generating edit descriptions for similar pairs:\")\n", "for pair in tqdm.tqdm(pairs):\n", "    if \"edit_query\" in pair and len(pair[\"edit_query\"]) > 3:\n", "        continue\n", "    # if \"edit_query\" in pair and isinstance(pair[\"edit_query\"], str):\n", "    #     pair[\"edit_query\"] = [{\n", "    #         \"query\": pair[\"edit_query\"],\n", "    #         \"response\": pair[\"edit_response_metadata\"],\n", "    #     }]\n", "    #     del pair[\"edit_response_metadata\"]\n", "    def prepare_workflow(workflow_dict: WorkflowDict):\n", "        # workflow = copy.deepcopy(workflow)\n", "        # workflow.pop(\"processName\", None)\n", "        # workflow.pop(\"packages\", None)\n", "        # remove_fields(workflow, [\"thought\"])\n", "        # return yaml_dump(workflow)\n", "        workflow_object = Workflow(\"\", \"\", workflow_dict)\n", "        DAPClassNameTranslator().translate(workflow_object)\n", "        new_workflow_dict = workflow_object.to_dict(include_packages=False, include_name=False)\n", "        remove_fields(new_workflow_dict, [\"thought\"])\n", "        return yaml_dump(new_workflow_dict)\n", "        \n", "\n", "    workflow1 = prepare_workflow(pair[\"dp1\"][\"solution_workflow\"])\n", "    workflow2 = prepare_workflow(pair[\"dp2\"][\"solution_workflow\"])\n", "    diff = get_unidiff(workflow1, workflow2)\n", "\n", "    messages = [\n", "        langchain_core.prompts.SystemMessagePromptTemplate.from_template(system_prompt),\n", "        langchain_core.prompts.HumanMessagePromptTemplate.from_template(user_prompt),\n", "    ]\n", "    chat_template = langchain_core.prompts.ChatPromptTemplate.from_messages(messages)\n", "    chain = chat_template | model\n", "\n", "    # response = model.generate(messages)\n", "\n", "    # # Generate the edit description\n", "    activity_typedefs, additional_typedefs = await get_activity_definitions(pair)\n", "    try:\n", "        response = chain.invoke(dict(\n", "            query1=pair[\"dp1\"][\"query\"],\n", "            query2=pair[\"dp2\"][\"query\"],\n", "            activity_type_definitions=activity_typedefs,\n", "            additional_type_definitions=additional_typedefs,\n", "            workflow1=workflow1,\n", "            workflow2=workflow2,\n", "            diff=diff\n", "        ))\n", "    except Exception as e:\n", "        import traceback\n", "        traceback.print_exc()\n", "        continue\n", "\n", "    if \"edit_query\" not in pair:\n", "        pair[\"edit_query\"] = []\n", "    pair[\"edit_query\"].append({\n", "        \"query\": response.content,\n", "        \"response\": response.response_metadata,\n", "    })\n", "\n", "    print(f\"Original Query: {pair['dp1']['query']}\")\n", "    print(f\"Target Query: {pair['dp2']['query']}\")\n", "    print(f\"Edit Query: {response.content}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["for pair in pairs:\n", "    if \"edit_query\" not in pair:\n", "        continue\n", "    print(pair[\"edit_query\"])\n", "    print(pair[\"dp1\"][\"query\"])\n", "    print(pair[\"dp2\"][\"query\"])\n", "    print(pair[\"dp1\"][\"path\"])\n", "    print(pair[\"dp2\"][\"path\"])\n", "    print(pair[\"score\"])\n", "    print(\"-\"*80)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v1\")\n", "# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v2\")\n", "# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v3\")\n", "# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v4-dap-translation\")\n", "# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v5-windows\")\n", "path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v5-windows-including-sequence\")\n", "path_dump.mkdir(parents=True, exist_ok=True)\n", "\n", "for i, pair in enumerate(pairs):\n", "    path = path_dump / f\"pair-{i:04d}.yaml\"\n", "    to_dump_pair = copy.deepcopy(pair)\n", "    to_dump_pair[\"score\"] = float(pair[\"score\"])\n", "    if isinstance(pair[\"dp1\"][\"path\"], pathlib.Path):\n", "        to_dump_pair[\"dp1\"][\"path\"] = pair[\"dp1\"][\"path\"].as_posix()\n", "    if isinstance(pair[\"dp2\"][\"path\"], pathlib.Path):\n", "        to_dump_pair[\"dp2\"][\"path\"] = pair[\"dp2\"][\"path\"].as_posix()\n", "    if \"query-embedding\" in to_dump_pair[\"dp1\"]:\n", "        del to_dump_pair[\"dp1\"][\"query-embedding\"]\n", "    if \"query-embedding\" in to_dump_pair[\"dp2\"]:\n", "        del to_dump_pair[\"dp2\"][\"query-embedding\"]\n", "    yaml_dump(to_dump_pair, path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "import pathlib\n", "\n", "# path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v3\")\n", "path_dump = pathlib.Path(\"/workspace/data/workdir/workflow-edit/pairs-v4-dap-translation\")\n", "pairs = []\n", "for path in path_dump.glob(\"*.yaml\"):\n", "    pairs.append(yaml_load(path))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for pair in sorted(pairs, key=lambda p: p[\"score\"], reverse=True):\n", "    _response_<PERSON><PERSON><PERSON>, _response_o3mini, _response_minimal, response_minimal = pair[\"edit_query\"]\n", "    # print(_response_o3mini[\"query\"])\n", "    # print(_response_4omini[\"query\"])\n", "    print(_response_minimal[\"query\"])\n", "    print(response_minimal[\"query\"])\n", "    print(pair[\"score\"])\n", "    print(pair[\"dp1\"][\"query\"])\n", "    print(pair[\"dp2\"][\"query\"])\n", "    print(pair[\"dp1\"][\"path\"])\n", "    print(pair[\"dp2\"][\"path\"])\n", "    print(\"-\" * 80)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.cluster import DBSCAN\n", "import numpy as np\n", "import collections\n", "\n", "filtered_datapoints = {k: v for k, v in datapoints.items() if v[\"mode\"] == \"workflow\"}\n", "\n", "# Collect all embeddings and corresponding datapoints\n", "embeddings = np.array([dp[\"query-embedding\"] for dp in filtered_datapoints.values()])\n", "dp_list = list(filtered_datapoints.values())\n", "\n", "# Perform DBSCAN clustering\n", "# eps=0.15 means points within this cosine distance are considered neighbors\n", "# min_samples=2 means at least 2 points needed to form a cluster\n", "# clustering = DBSCAN(eps=0.15, min_samples=2, metric='cosine').fit(embeddings)\n", "clustering = DBSCAN(eps=0.15, min_samples=2, metric='cosine').fit(embeddings)\n", "\n", "# Group datapoints by cluster\n", "clusters = collections.defaultdict(list)\n", "for idx, label in enumerate(clustering.labels_):\n", "    if label != -1:  # Skip noise points\n", "        clusters[label].append(dp_list[idx])\n", "\n", "# Print clusters\n", "print(f\"Found {len(clusters)} clusters\")\n", "for label, cluster_dps in sorted(clusters.items(), key=lambda x: len(x[1]), reverse=True):\n", "    print(f\"\\nCluster {label} with {len(cluster_dps)} datapoints:\")\n", "    for dp in cluster_dps:\n", "        print(f\"Query: {dp['query']}\")\n", "    for dp in cluster_dps:\n", "        print(f\"Path: {dp['path']}\")\n", "    print(\"-\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Workflow Edit Draft Generation Tests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}