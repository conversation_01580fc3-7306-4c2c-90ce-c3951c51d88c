{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "eval_root_path = pathlib.Path(\"/workspace/data/Evaluations/activity_config/\")\n", "default_eval_path = eval_root_path / \"default\"\n", "production_eval_path = eval_root_path / \"prod_demo_windows_too\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "tokenizer = tiktoken.get_encoding('cl100k_base')\n", "completion_usage, prompt_usage = [], []\n", "for eval_path in default_eval_path.rglob(\"**/test/*/*.yaml\"):\n", "    eval_data = yaml_load(eval_path)\n", "    completion = eval_data[\"completion\"]\n", "    prompt = eval_data[\"prompt\"]\n", "    completion_usage.append(len(tokenizer.encode(completion)))\n", "    prompt_usage.append(len(tokenizer.encode(prompt)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "usage_overall = [x + y for x, y in zip(completion_usage, prompt_usage)]\n", "\n", "with pd.option_context('display.float_format', '{:.0f}'.format):\n", "    print(pd.DataFrame({\"total\": usage_overall, \"prompt\": prompt_usage, \"completion\": completion_usage}).describe())\n", "\n", "print(f\"Total usage: {sum(usage_overall)} {(sum(prompt_usage))} {(sum(completion_usage))}\")\n", "\n", "# Create a figure and three subplots\n", "fig, (ax1, ax2, ax3) = plt.subplots(3)\n", "\n", "# Plot the first histogram\n", "ax1.hist(usage_overall, bins=50)\n", "ax1.set_title('Overall Usage')\n", "\n", "# Plot the second histogram\n", "ax2.hist(completion_usage, bins=50)\n", "ax2.set_title('Completion Usage')\n", "\n", "# Plot the third histogram\n", "ax3.hist(prompt_usage, bins=50)\n", "ax3.set_title('Prompt Usage')\n", "\n", "# Display the figure\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparing completions before and after"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump, multiline_str\n", "from services.studio._text_to_workflow.utils.yaml_utils from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump, multiline_str_load\n", "import collections\n", "import difflib\n", "\n", "def get_diff_lines(a, b):\n", "    return \"\".join(difflib.ndiff(a.splitlines(keepends=True), b.splitlines(keepends=True)))\n", "\n", "def load_completion(s):\n", "    if s.startswith('```yaml'):\n", "        s = s[7:]\n", "    if s.endswith('```'):\n", "        s = s[:-3]\n", "    return yaml_load(s)\n", "\n", "\n", "activity_to_demos1 = collections.defaultdict(collections.Counter)\n", "activity_to_demos2 = collections.defaultdict(collections.Counter)\n", "\n", "stats = collections.Counter()\n", "mask = \"**/test/*/*.yaml\"\n", "for eval1, eval2 in zip(default_eval_path.rglob(mask), production_eval_path.rglob(mask)):\n", "    assert eval1.name == eval2.name and eval1.parent.name == eval2.parent.name, \"Mismatch evaluation jobs\"\n", "    name = eval1.name\n", "\n", "    eval_dict1 = yaml_load(eval1)\n", "    eval_dict2 = yaml_load(eval2)\n", "\n", "    completion1_string = eval_dict1[\"completion\"]\n", "    completion2_string = eval_dict2[\"completion\"]\n", "\n", "    prompt1 = eval_dict1[\"prompt\"]\n", "    prompt2 = eval_dict2[\"prompt\"]\n", "\n", "    import re\n", "    demo_pattern = re.compile(r\"\"\"Workflow name: (?P<workflow_name>[^\\n]+)\n", "Workflow description: (?P<workflow_description>[^\\n]+)\n", "\n", "Arguments:\n", "(?P<arguments>.*?)\n", "\n", "Variables:\n", "(?P<variables>.*?)\n", "\n", "Pseudocode of the steps taken so far:\n", "(?P<plan>.*?)\n", "\n", "Activity:\n", "(?P<activity>.*?)\n", "AI: ```yaml\n", "(?P<truth>.*?)\n", "```\n", "\"\"\", re.DOTALL)\n", "\n", "    # *demonstrations1, query = [match.groupdict() for match in demo_pattern.finditer(prompt1)]  # the last one is the current query\n", "    # *demonstrations2, query = [match.groupdict() for match in demo_pattern.finditer(prompt2)]  # the last one is the current query\n", "    demonstrations1 = [match.groupdict() for match in demo_pattern.finditer(prompt1)]  # the last one is the current query\n", "    demonstrations2 = [match.groupdict() for match in demo_pattern.finditer(prompt2)]  # the last one is the current query\n", "    \n", "    # eval_dict1[\"predicted_params\"]\n", "    # eval_dict1[\"cleaned_params\"]\n", "    assert (target := eval_dict1[\"target_params\"]) == eval_dict2[\"target_params\"]\n", "\n", "    activity_to_demos1[eval_dict1[\"activity_id\"]].update([d[\"activity\"] for d in demonstrations1])\n", "    activity_to_demos2[eval_dict1[\"activity_id\"]].update([d[\"activity\"] for d in demonstrations2])\n", "    # activity_to_demos[eval_dict1[\"activity_fqn\"]].update([d[\"activity\"] for d in demonstrations1])\n", "\n", "    # same response\n", "    stats['total'] += 1\n", "    if completion1_string == completion2_string:\n", "        stats['same_string'] += 1\n", "        continue\n", "\n", "    # loading attempts\n", "    completion1_loaded = True\n", "    try:\n", "        completion1 = load_completion(completion1_string)\n", "    except:\n", "        completion1_loaded = False\n", "    completion2_loaded = True\n", "    try:\n", "        completion2 = load_completion(completion2_string)\n", "    except:\n", "        completion2_loaded = False\n", "    if not completion1_loaded and not completion2_loaded:\n", "        stats['failed_to_load'] += 1\n", "        continue\n", "    if not completion1_loaded:\n", "        stats['failed_to_load_old'] += 1\n", "        continue\n", "    if not completion2_loaded:\n", "        stats['failed_to_load_new'] += 1\n", "        continue\n", "\n", "    # same loaded response\n", "    if completion1 == completion2:\n", "        stats['same_dict'] += 1\n", "        continue\n", "    stats['different'] += 1\n", "\n", "    # prompt comparison\n", "    if demonstrations1 == demonstrations2:\n", "        stats['same_demonstrations'] += 1\n", "        continue\n", "\n", "    demonstrations1, demonstrations2 = \\\n", "        [demo for demo in demonstrations1 if all(demo != demo2 for demo2 in demonstrations2)],\\\n", "        [demo for demo in demonstrations2 if all(demo != demo1 for demo1 in demonstrations1)]\n", "\n", "    # dump for sorting the keys\n", "    completion_dump1 = yaml_dump(completion1)\n", "    completion_dump2 = yaml_dump(completion2)\n", "    target_dump = yaml_dump(target)\n", "\n", "    # continue\n", "\n", "    query = {\n", "        \"workflow_name\": eval_dict1[\"workflow_name\"],\n", "        \"workflow_description\": eval_dict1[\"workflow_description\"],\n", "        \"arguments\": yaml_dump(eval_dict1[\"arguments\"]),\n", "        \"variables\": yaml_dump(eval_dict1[\"global_variables\"] + eval_dict1[\"local_variables\"]),\n", "        \"plan\": eval_dict1[\"plan\"],\n", "        \"activity\": eval_dict1[\"activity_id\"],\n", "        \"truth\": \"\"\n", "    }\n", "\n", "    def present_demo(demo, title):\n", "        print()\n", "        print(f\" --- {title} ---\")\n", "        print(\"Activity:\", demo[\"activity\"])\n", "        print()\n", "        print(\"Workflow name:\", demo[\"workflow_name\"])\n", "        print(\"Workflow desc:\", demo[\"workflow_description\"])\n", "        print()\n", "        print(demo[\"plan\"])\n", "        print()\n", "        # print(demo[\"arguments\"])\n", "        # print(demo[\"variables\"])\n", "        print(\"\\n\".join(v[\"direction\"] + \": \" + v[\"name\"] + \": \" + v[\"type\"] for v in yaml_load(demo[\"arguments\"])))\n", "        print()\n", "        print(\"\\n\".join(v[\"name\"] + \": \" + v[\"type\"] for v in yaml_load(demo[\"variables\"])))\n", "        print()\n", "        print(demo[\"truth\"])\n", "\n", "    print(name)\n", "    print('-' * 50)\n", "    present_demo(query, \"Query\")\n", "    print('-' * 50 + \" Ground Truth \" + '-' * 50)\n", "    print(target_dump)\n", "    print('-' * 50 + \" Prediction before \" + '-' * 50)\n", "    print(completion_dump1)\n", "    print('-' * 50 + \" Prediction before against ground truth \" + '-' * 50)\n", "    print(get_diff_lines(target_dump, completion_dump1))\n", "    print('-' * 50 + \" Prediction after \" + '-' * 50)\n", "    print(completion_dump2)\n", "    print('-' * 50 + \" Prediction after against ground truth \" + '-' * 50)\n", "    print(get_diff_lines(target_dump, completion_dump2))\n", "    print('-' * 50 + \" Prediction before vs prediction after \" + '-' * 50)\n", "    print(get_diff_lines(completion_dump1, completion_dump2))\n", "    print('-' * 100)\n", "    # print()\n", "    # print(prompt1)\n", "    # print('-' * 100)\n", "    # print()\n", "    # print('-' * 100)\n", "    # print(prompt2)\n", "    # print('-' * 100)\n", "\n", "    # present_demo(query, \"Query\")\n", "    print('-' * 50, \"Default\" ,'-' * 50)\n", "    for i, demo in enumerate(demonstrations1, 1):\n", "        present_demo(demo, f\"Demonstration {i}\")\n", "    print('-' * 50, \"Production\" ,'-' * 50)\n", "    for i, demo in enumerate(demonstrations2, 1):\n", "        present_demo(demo, f\"Demonstration {i}\")\n", "    \n", "    print('\\n' * 20)\n", "    # for _ in range(10):\n", "    #     print('#' * 100)\n", "\n", "stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["activities_with_same_demos = set()\n", "\n", "for activity, demos_counter1 in sorted(activity_to_demos1.items(), key=lambda x: -len(x[-1])):\n", "# for activity, demos_counter1 in sorted(activity_to_demos.items(), key=lambda x: -sum(x[-1].values())):\n", "    total_count = sum(demos_counter1.values()) / 3\n", "    print(f\"{total_count:3} {activity}\")\n", "    print('-' * 50, \"default\")\n", "    for demo_activity, c in demos_counter1.most_common():\n", "        print(f\"  {c:3} {demo_activity}\")\n", "    print('-' * 50, \"production\")\n", "    for demo_activity, c in activity_to_demos2[activity].most_common():\n", "        print(f\"  {c:3} {demo_activity}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparing metrics before and after"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "default_df = pd.read_feather(default_eval_path / \"param_level.test.feather\")\n", "prod_df = pd.read_feather(production_eval_path / \"param_level.test.feather\")\n", "default_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_config.activity_config_evaluation import calculate_evaluation_metrics\n", "\n", "print(default_df.columns)\n", "# groupby then add a new column\n", "\n", "def preprocess(df):\n", "    a_value_was_predicted = df[\"param_predicted_value\"].notna()\n", "    a_truth_exists = df[\"param_target_value\"].notna()\n", "    the_predicted_value_is_correct = df[\"predicted_correct\"]\n", "\n", "    df[\"tp_precision\"] = a_value_was_predicted & the_predicted_value_is_correct\n", "    df[\"tp_recall\"] = a_truth_exists & df[\"tp_precision\"]\n", "    df[\"fp\"] = a_value_was_predicted & (~the_predicted_value_is_correct)\n", "    df[\"fn\"] = a_truth_exists & ~df[\"tp_recall\"]\n", "    # df[\"fn\"] = a_truth_exists & (~a_value_was_predicted | a_value_was_predicted & (~the_predicted_value_is_correct))  # equivalent to (a_truth_exists & ~df[\"tp\"])\n", "    # df[\"fn\"] = (~a_value_was_predicted)\n", "    # df[\"fn\"] = (~a_value_was_predicted) & a_truth_exists\n", "    # df[\"fn\"] = ~df[\"tp\"]\n", "    # print((~a_truth_exists & df[\"tp\"]).sum())\n", "    # print('tp', df[\"tp\"].sum(), 'fp', df[\"fp\"].sum(), 'fn', df[\"fn\"].sum())\n", "\n", "    # df[\"recall\"] = df[\"tp\"] / (df[\"tp\"] + df[\"fn\"])\n", "    # df[\"precision\"] = df[\"tp\"] / (df[\"tp\"] + df[\"fp\"])\n", "    # df[\"f1\"] = 2 * df[\"precision\"] * df[\"recall\"] / (df[\"precision\"] + df[\"recall\"])\n", "\n", "    a_value_was_predicted = df[\"param_predicted_clean_value\"].notna()\n", "    the_predicted_value_is_correct = df[\"predicted_clean_correct\"]\n", "    df[\"tp_precision_clean\"] = a_value_was_predicted & the_predicted_value_is_correct\n", "    df[\"tp_recall_clean\"] = a_truth_exists & df[\"tp_precision_clean\"]\n", "    df[\"fp_clean\"] = a_value_was_predicted & (~the_predicted_value_is_correct)\n", "    df[\"fn_clean\"] = a_truth_exists & ~df[\"tp_recall_clean\"]\n", "    # df[\"fn_clean\"] = (~a_value_was_predicted)\n", "    # df[\"fn_clean\"] = (~a_value_was_predicted) & a_truth_exists\n", "    # df[\"fn_clean\"] = ~df[\"tp_clean\"]\n", "\n", "    # df[\"recall_clean\"] = df[\"tp_clean\"] / (df[\"tp_clean\"] + df[\"fn_clean\"])\n", "    # df[\"precision_clean\"] = df[\"tp_clean\"] / (df[\"tp_clean\"] + df[\"fp_clean\"])\n", "    # df[\"f1_clean\"] = 2 * df[\"precision_clean\"] * df[\"recall_clean\"] / (df[\"precision_clean\"] + df[\"recall_clean\"])\n", "\n", "    return df\n", "\n", "# def fbeta(tp, fp, fn, beta=1):\n", "#     precision = tp / (tp + fp) if tp else 0\n", "#     recall = tp / (tp + fn) if tp else 0\n", "#     return (1 + beta ** 2) * precision * recall / (beta ** 2 * precision + recall) if precision + recall else 0\n", "\n", "def fbeta(recall, precision, beta=1):\n", "    return (1 + beta ** 2) * precision * recall / (beta ** 2 * precision + recall) if precision + recall else 0\n", "\n", "def aggregate_functions(df):\n", "    tpp, tpr, fp, fn = df[\"tp_precision\"].sum(), df[\"tp_recall\"].sum(), df[\"fp\"].sum(), df[\"fn\"].sum()\n", "    tpp_clean, tpr_clean, fp_clean, fn_clean = df[\"tp_precision_clean\"].sum(), df[\"tp_recall_clean\"].sum(), df[\"fp_clean\"].sum(), df[\"fn_clean\"].sum()\n", "    # n = df[\"tp\"].count()\n", "    n = len(df)\n", "\n", "    metrics = {\n", "        \"count\": n,\n", "        \"targets\": tpr + fn,\n", "        \"predictions\": tpp + fp,\n", "        \"precision\": tpp / (tpp + fp) if tpp else 0,\n", "        \"recall\": tpr / (tpr + fn) if tpr else 0,\n", "        \"precision_clean\": tpp_clean / (tpp_clean + fp_clean) if tpp_clean else 0,\n", "        \"recall_clean\": tpr_clean / (tpr_clean + fn_clean) if tpr_clean else 0,\n", "    }\n", "\n", "    metrics[\"f1\"] = fbeta(metrics[\"recall\"], metrics[\"precision\"])\n", "    metrics[\"f0.5\"] = fbeta(metrics[\"recall\"], metrics[\"precision\"], 0.5)\n", "    metrics[\"f0.25\"] = fbeta(metrics[\"recall\"], metrics[\"precision\"], 0.25)\n", "\n", "    metrics[\"f1_clean\"] = fbeta(metrics[\"recall_clean\"], metrics[\"precision_clean\"])\n", "    metrics[\"f0.5_clean\"] = fbeta(metrics[\"recall_clean\"], metrics[\"precision_clean\"], 0.5)\n", "    metrics[\"f0.25_clean\"] = fbeta(metrics[\"recall_clean\"], metrics[\"precision_clean\"], 0.25)\n", "\n", "    metrics[\"tpr\"] = tpr\n", "    metrics[\"tpp\"] = tpp\n", "    metrics[\"fn\"] = fn\n", "    metrics[\"fp\"] = fp\n", "    metrics[\"tpp_clean\"] = tpp_clean\n", "    metrics[\"tpr_clean\"] = tpr_clean\n", "    metrics[\"fp_clean\"] = fp_clean\n", "    metrics[\"fn_clean\"] = fn_clean\n", "    \n", "    return pd.DataFrame({k: [v] for k, v in metrics.items()})\n", "\n", "df = preprocess(default_df)\n", "overall_metrics = aggregate_functions(df)\n", "# print(overall_metrics)\n", "# print(calculate_evaluation_metrics(df))\n", "# raise\n", "\n", "grouped_by_param_type_category = df.groupby(\"param_type_category\").apply(aggregate_functions).sort_values(\"precision_clean\", ascending=False)\n", "grouped_by_activity = df.groupby(\"activity_id\").apply(aggregate_functions).sort_values(\"precision_clean\", ascending=False)\n", "\n", "df = preprocess(prod_df)\n", "# update overall metrics with the ones from prod, but change the name of the columns to start with prod_\n", "prod_overall_metrics = aggregate_functions(df)\n", "prod_overall_metrics.columns = [f\"prod_{c}\" for c in prod_overall_metrics.columns]\n", "overall_metrics = pd.concat([overall_metrics, prod_overall_metrics], axis=1)\n", "\n", "prod_grouped_by_param_type_category = df.groupby(\"param_type_category\").apply(aggregate_functions).sort_values(\"precision_clean\", ascending=False)\n", "prod_grouped_by_param_type_category.columns = [f\"prod_{c}\" for c in prod_grouped_by_param_type_category.columns]\n", "grouped_by_param_type_category = pd.concat([grouped_by_param_type_category, prod_grouped_by_param_type_category], axis=1)\n", "\n", "prod_grouped_by_activity = df.groupby(\"activity_id\").apply(aggregate_functions).sort_values(\"precision_clean\", ascending=False)\n", "prod_grouped_by_activity.columns = [f\"prod_{c}\" for c in prod_grouped_by_activity.columns]\n", "grouped_by_activity = pd.concat([grouped_by_activity, prod_grouped_by_activity], axis=1)\n", "\n", "grouped_by_activity[\"precision_diff\"] = grouped_by_activity[\"prod_precision\"] - grouped_by_activity[\"precision\"]\n", "grouped_by_activity[\"recall_diff\"] = grouped_by_activity[\"prod_recall\"] - grouped_by_activity[\"recall\"]\n", "grouped_by_activity[\"precision_clean_diff\"] = grouped_by_activity[\"prod_precision_clean\"] - grouped_by_activity[\"precision_clean\"]\n", "grouped_by_activity[\"recall_clean_diff\"] = grouped_by_activity[\"prod_recall_clean\"] - grouped_by_activity[\"recall_clean\"]\n", "\n", "grouped_by_param_type_category[\"precision_diff\"] = grouped_by_param_type_category[\"prod_precision\"] - grouped_by_param_type_category[\"precision\"]\n", "grouped_by_param_type_category[\"recall_diff\"] = grouped_by_param_type_category[\"prod_recall\"] - grouped_by_param_type_category[\"recall\"]\n", "grouped_by_param_type_category[\"precision_clean_diff\"] = grouped_by_param_type_category[\"prod_precision_clean\"] - grouped_by_param_type_category[\"precision_clean\"]\n", "grouped_by_param_type_category[\"recall_clean_diff\"] = grouped_by_param_type_category[\"prod_recall_clean\"] - grouped_by_param_type_category[\"recall_clean\"]\n", "\n", "with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.max_colwidth', 1000,'display.width', 100000,'display.float_format', lambda x: f'{x:.2f}'):\n", "    metrics_order = [\n", "        \"count\",\n", "        \"prod_precision\", \"precision_diff\",\n", "        \"prod_recall\", \"recall_diff\",\n", "        \"prod_precision_clean\", \"precision_clean_diff\",\n", "        \"prod_recall_clean\", \"recall_clean_diff\",\n", "        # \"precision\", \"prod_precision\",\n", "        # \"recall\", \"prod_recall\",\n", "        # \"precision_clean\", \"prod_precision_clean\",\n", "        # \"recall_clean\", \"prod_recall_clean\"\n", "    ]\n", "    # display(overall_metrics[metrics_order])\n", "    diff_df = grouped_by_activity[metrics_order][\n", "        (grouped_by_activity[\"precision_clean\"] != grouped_by_activity[\"prod_precision_clean\"]) |\n", "        (grouped_by_activity[\"recall_clean\"] != grouped_by_activity[\"prod_recall_clean\"])\n", "    ]\n", "    # diff_df = grouped_by_param_type_category[metrics_order][\n", "    #     (grouped_by_param_type_category[\"precision_clean\"] != grouped_by_param_type_category[\"prod_precision_clean\"]) |\n", "    #     (grouped_by_param_type_category[\"recall_clean\"] != grouped_by_param_type_category[\"prod_recall_clean\"])\n", "    # ]\n", "    # display(diff_df.sort_values(\"precision_clean_diff\", ascending=False))\n", "    # display(diff_df.sort_values(\"recall_clean_diff\", ascending=False))\n", "    display(diff_df[[\"count\", \"prod_precision\", \"precision_diff\", \"prod_precision_clean\", \"precision_clean_diff\"]].sort_values(\"precision_clean_diff\", ascending=False))\n", "    display(diff_df[[\"count\", \"prod_recall\", \"recall_diff\", \"prod_recall_clean\", \"recall_clean_diff\"]].sort_values(\"recall_clean_diff\", ascending=False))\n", "    print(diff_df[[\"count\", \"prod_precision\", \"precision_diff\", \"prod_precision_clean\", \"precision_clean_diff\"]].sort_values(\"precision_clean_diff\", ascending=False))\n", "    print(diff_df[[\"count\", \"prod_recall\", \"recall_diff\", \"prod_recall_clean\", \"recall_clean_diff\"]].sort_values(\"recall_clean_diff\", ascending=False))\n", "    # display(grouped_by_activity)\n", "\n", "# simplified = df[[\"param_name\", \"param_target_value\", \"param_predicted_value\", \"tp\", \"fp\", \"fn\", \"predicted_correct\"]]\n", "# simplified\n", "\n", "# df.to_csv(\"/workspace/data/workdir/activity-configuration/metrics.csv\")\n", "# .agg(\n", "#     count=(\"param_type_category\", \"count\"),\n", "#     precision=(\"precision\", \"mean\"),\n", "# )\n", "# .assign(\n", "#     accuracy=lambda x: x[\"predicted_correct\"] / x[\"count\"]\n", "# )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}