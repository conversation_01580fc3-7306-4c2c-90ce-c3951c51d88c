{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "path = pathlib.Path('/workspace/data/workflows')\n", "portable_path = path / 'parsed_portable1'\n", "windows_path = path / 'parsed_windows1'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explore data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "\n", "def print_workflow(node, indent=0):\n", "    if isinstance(node, list):\n", "        for child in node:\n", "            print_workflow(child, indent=indent)\n", "        return\n", "    if not isinstance(node, dict):\n", "        return\n", "    thought = node.get(\"thought\")\n", "    activity = node.get('activity')\n", "    params = [f\"{k}: {v if len(v) < 100 else v[:100] + ' ...'}\"\n", "              for k, v in node.get('params', {}).items() if not isinstance(v, (list, dict))]\n", "    if activity is not None:\n", "        print(f\"{indent * '  '}{activity} ({thought or 'no thought'}) | {' | ' .join(params)}\")\n", "\n", "    for child in node.values():\n", "        print_workflow(child, indent=indent+int(activity is not None))\n", "    if not activity:\n", "        return\n", "\n", "    # def iter_child(container):\n", "    #     if isinstance(container, list):\n", "    #         for child in container:\n", "    #             print_workflow(child, indent=indent+1)\n", "    #     else:\n", "    #         print_workflow(container, indent=indent+1)\n", "\n", "    # if activity == \"System.Activities.Statements.TryCatch\":\n", "    #     iter_child(node[\"params\"][\"Try\"])\n", "    # elif activity == \"System.Activities.Statements.Sequence\":\n", "    #     iter_child(node[\"params\"][\"Activities\"])\n", "    # elif activity == \"System.Activities.Statements.If\":\n", "    #     print(\"  \" * indent + \"Then:\")\n", "    #     iter_child(node[\"params\"][\"Then\"])\n", "    #     print(\"  \" * indent + \"Else:\")\n", "    #     iter_child(node[\"params\"].get(\"<PERSON><PERSON>\", []))\n", "    # elif activity == \"System.Activities.Statements.Parallel\":\n", "    #     iter_child(node[\"params\"][\"Branches\"])\n", "    # elif activity == \"System.Activities.Statements.While\":\n", "    #     iter_child(node[\"params\"][\"Body\"])\n", "    # elif activity == \"System.Activities.Statements.DoWhile\":\n", "    #     iter_child(node[\"params\"][\"Body\"])\n", "    # elif activity == \"System.Activities.Statements.ForEach\":\n", "    #     iter_child(node[\"params\"][\"Body\"])\n", "    # elif activity == \"System.Activities.Statements.ForEach`1\":\n", "    #     iter_child(node[\"params\"][\"Body\"])\n", "    # elif activity == \"System.Activities.Statements.FlowDecision\":\n", "    #     iter_child(node[\"params\"][\"Cases\"])\n", "    # elif activity == \"System.Activities.Statements.FlowSwitch`1\":\n", "    #     iter_child(node[\"params\"][\"Cases\"])\n", "    # elif activity == \"System.Activities.Statements.Flowchart\":\n", "    #     iter_child(node[\"params\"][\"Nodes\"])\n", "    # elif activity == \"System.Activities.Statements.StateMachine\":\n", "    #     iter_child(node[\"params\"][\"States\"])\n", "    # elif activity == \"System.Activities.Statements.State\":\n", "    #     print(\"  \" * indent + \"Entry:\")\n", "    #     iter_child(node[\"params\"][\"Entry\"])\n", "    #     print(\"  \" * indent + \"Exit:\")\n", "    #     iter_child(node[\"params\"][\"Exit\"])\n", "    #     print(\"  \" * indent + \"Transition:\")\n", "    #     iter_child(node[\"params\"][\"Transitions\"])\n", "\n", "paths = [p for p in windows_path.iterdir() if p.name.endswith('.yaml')]\n", "for i, path in enumerate(paths[:], 1):\n", "# for path in list(portable_path.iterdir())[:10]:\n", "    print(f\"{i}/{len(paths)} {path}\")\n", "    print('-' * 50)\n", "    with open(path, encoding='utf-8-sig') as fd:\n", "    # with open(path, encoding='utf-8') as fd:\n", "        try:\n", "            data = yaml_load(fd)\n", "        except Exception:\n", "            print(\"Error: failed to parse file\")\n", "            print()\n", "            continue\n", "    if not data:\n", "        print(\"Warning: empty file\")\n", "        print()\n", "        continue\n", "    process_name = data[\"processName\"]\n", "    packages = data.get(\"packages\", [])\n", "\n", "    trigger = data.get(\"trigger\")\n", "    trigger_thought = trigger[\"thought\"] if trigger else \"no trigger\"\n", "    trigger_activity = trigger[\"activity\"] if trigger else None\n", "\n", "    workflow = data.get(\"workflow\", [])\n", "\n", "    print(f\"T: {trigger_thought}\")\n", "    print_workflow(workflow)\n", "    print()\n", "\n", "    # print(json.dumps(data, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "# packages = collections.Counter()\n", "package_names_counter = collections.Counter()\n", "versions_counter = collections.Counter()\n", "packages = collections.defaultdict(collections.Counter)\n", "for path in windows_path.iterdir():\n", "    if not path.name.endswith('.yaml'):\n", "        continue\n", "    with open(path, encoding='utf-8-sig') as fd:\n", "        try:\n", "            data = yaml_load(fd)\n", "        except Exception:\n", "            continue\n", "    if not data:\n", "        continue\n", "    for package in data.get(\"packages\", []):\n", "        package_name, package_version = package.split(',')\n", "        package_names_counter[package_name] += 1\n", "        packages[package_name].update([package_version])\n", "        versions_counter.update([package_version])\n", "\n", "# for package_name, count in package_names_counter.most_common():\n", "#     print(f\"{count:3} {package_name}\")\n", "#     for version, count in packages[package_name].most_common():\n", "#         print(f\"  {count:3} {version}\")\n", "\n", "# for package_name in sorted(package_names_counter):\n", "#     print(package_name)\n", "\n", "# versions_counter.most_common()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import collections, json, yaml_load\n", "\n", "\n", "def iterate_nodes(node, indent=0):\n", "    if isinstance(node, list):\n", "        for child in node:\n", "            yield from iterate_nodes(child, indent=indent)\n", "        return\n", "    if not isinstance(node, dict):\n", "        return\n", "    yield node, indent\n", "    has_activity = 'activity' in node\n", "    for child in node.values():\n", "        yield from iterate_nodes(child, indent=indent + int(has_activity))\n", "\n", "\n", "def iterate_and_apply(workflow, func):\n", "    for node, indent in iterate_nodes(workflow):\n", "        func(node, indent)\n", "\n", "\n", "def get_workflow_summary(workflow):\n", "    summary = []\n", "    for node, indent in iterate_nodes(workflow):\n", "        thought = node.get(\"thought\")\n", "        activity = node.get('activity')\n", "        params = [f\"{k}: {v if len(v) < 100 else v[:100] + ' ...'}\"\n", "                for k, v in node.get('params', {}).items() if not isinstance(v, (list, dict))]\n", "        if activity is not None:\n", "            summary.append(f\"{indent * '  '}{activity} ({thought or 'no thought'}) | {' | ' .join(params)}\")\n", "    return '\\n'.join(summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# thoughts = collections.Counter()\n", "activities = collections.Counter()\n", "activities_params = collections.defaultdict(collections.Counter)\n", "paths = [p for p in windows_path.iterdir() if p.name.endswith('.yaml')]\n", "for i, path in enumerate(paths, 1):\n", "    print(f\"{i}/{len(paths)} {path}\")\n", "    print('-' * 50)\n", "    with open(path, encoding='utf-8-sig') as fd:\n", "        try:\n", "            data = yaml_load(fd)\n", "        except Exception:\n", "            continue\n", "    if not data:\n", "        continue\n", "\n", "    print(get_workflow_summary(data.get(\"workflow\", [])))\n", "    print()\n", "    continue\n", "\n", "    def print_summary(node, indent):\n", "        thought = node.get(\"thought\")\n", "        activity = node.get('activity')\n", "        params = [f\"{k}: {v if len(v) < 100 else v[:100] + ' ...'}\"\n", "                for k, v in node.get('params', {}).items() if not isinstance(v, (list, dict))]\n", "        if activity is not None:\n", "            print(f\"{indent * '  '}{activity} ({thought or 'no thought'}) | {' | ' .join(params)}\")\n", "\n", "    # def process_node(node, indent):\n", "    #     thoughts.update([node.get(\"thought\", \"None\")])\n", "    #     activities.update([node.get(\"activity\", \"None\")])\n", "\n", "    # def gather_activities_params(node, indent):\n", "    #     activity = node.get(\"activity\")\n", "    #     if activity == 'System.Activities.Statements.If' and 'Then' not in node.get(\"params\", {}) and 'Else' not in node.get(\"params\", {}):\n", "    #         print(path)\n", "    #         print(yaml_dump(node))\n", "    #     if activity is None:\n", "    #         return\n", "    #     activities.update([activity])\n", "    #     activities_params[activity].update(node.get(\"params\", {}).keys())\n", "\n", "    # iterate_and_apply(data.get(\"workflow\", []), gather_summary)\n", "    # iterate_and_apply(data.get(\"workflow\", []), gather_summary)\n", "\n", "\n", "activities.most_common()\n", "sorted(activities)\n", "# for activity, count in activities.most_common():\n", "for activity, count in sorted(activities.most_common(), key=lambda x: x[0]):\n", "    print(f\"{activity}\")\n", "    for param, c in activities_params[activity].most_common():\n", "        print(f\"  {c/count * 100:3.0f}% {param}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "paths = list(windows_path.iterdir())\n", "paths = [p for p in paths if p.suffix == '.yaml']\n", "# paths = list(portable_path.iterdir())\n", "dataset = {}\n", "for i, path in enumerate(paths, 1):\n", "    # print(f\"{i}/{len(paths)} {path}\")\n", "    # print('-' * 50)\n", "    with open(path, encoding='utf-8-sig') as fd:\n", "        # dataset.append(fd.read())\n", "        workflow = fd.read()\n", "        # print(workflow)\n", "        try:\n", "            yaml_load(workflow)\n", "        except Exception as e:\n", "            print(f\"Failed ({e}) to load workflow at {path}\")\n", "            continue\n", "        dataset[path.name] = workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "sizes = [*map(len, dataset.values())]\n", "print(f\"There are {sizes.count(0)} with 0 size\")\n", "log_sizes = [*map(math.log10, [s for s in sizes if s > 0])]\n", "fig, axs = plt.subplots(1, 2, figsize=(10, 5))\n", "\n", "# Plot the first histogram\n", "axs[0].hist(sizes, bins=50)\n", "axs[0].set_title('Sizes Histogram')\n", "axs[0].set_xlabel('Sizes')\n", "axs[0].set_ylabel('Frequency')\n", "\n", "# Plot the second histogram\n", "axs[1].hist(log_sizes, bins=50)\n", "axs[1].set_title('Log Sizes Histogram')\n", "axs[1].set_xlabel('Log <PERSON> (powers of 10)')\n", "axs[1].set_ylabel('Frequency')\n", "axs[1].xaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: f'$10^{{{int(x)}}}$'))\n", "\n", "# Display the plots\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path, workflow_raw in dataset.items():\n", "    workflow = yaml_load(workflow_raw)\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LLM augmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import dotenv\n", "import langchain.chat_models\n", "\n", "dotenv.load_dotenv(override=True)\n", "\n", "class LLMGatewayModel(langchain.chat_models.AzureChatOpenAI):\n", "    def __init__(self, **kwargs):\n", "        super().__init__(\n", "            openai_api_base=\"https://staging.uipath.com/550aad85-fa72-48df-8495-8ad751cade29/ff481ed9-5261-4d0b-9f4a-8c60f58d379e/llmgateway_/\",\n", "            model_kwargs={\n", "                \"headers\": {\n", "                    \"Authorization\": os.getenv(\"UIPATH_TOKEN\"),\n", "                    \"X-UiPath-LlmGateway-TimeoutSeconds\": \"60\",\n", "                    \"X-UiPath-LlmGateway-RequestingProduct\": \"Wingman\",\n", "                    \"X-UiPath-LlmGateway-RequestingFeature\": \"workflow-generation\",\n", "                    \"X-UIPATH-STREAMING-ENABLED\": \"false\",\n", "                },\n", "                **kwargs.pop(\"model_kwargs\", {})\n", "            },\n", "            **kwargs,\n", "        )\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate query from the workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import langchain.chat_models\n", "import langchain.prompts\n", "import langchain.schema\n", "\n", "# from services.studio._text_to_workflow.utils.llm_gateway_model import LLMGatewayModel\n", "\n", "system_prompt = \"\"\"You are an RPA expert working with UiPath Studio.\n", "You can read workflow yaml proficiently and can reverse engineer what is happening inside one.\n", "You are also able to summarize the explanation into only one sentence, using knowledge that you've gathered over the years.\n", "Although not obvious at first, some workflows are more complex than others. Instead of explaining verbatim, try to offer a concise summary.\n", "You must avoid generic formulations and stick to specific information that you can infer from the workflow.\n", "Your answer should be imperative, as you would be demanding for the specific workflow to be created.\n", "Avoid formulations starting with \"Create a UiPath workflow that ...\" or \"Implement a workflow that ...\", as the user is already familiar.\n", "You can start directly with what \"...\" would have contained.\n", "\"\"\"\n", "# You are able to assess how concise and specific your answer is with a number between 0 and 10.\n", "\n", "user_prompt = \"\"\"Here is a workflow\n", "```yaml\n", "{workflow}\n", "```\n", "\n", "Try to figure out the main idea of the workflow in one sentence as if you were asking someone to create it.\n", "\"\"\"\n", "\n", "# Your output should be a json like this one:\n", "# ```json\n", "# {{\n", "#     \"summary\": \"\",\n", "#     \"conciseness\": 0\n", "#     \"confidence\": 0\n", "# }}\n", "# ```\n", "# \"\"\"\n", "\n", "system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(system_prompt)\n", "user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(user_prompt)\n", "\n", "chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages([system_message_template, user_message_template])\n", "\n", "for k, v in os.environ.items():\n", "    if k.startswith('OPENAI'):\n", "        print(k, v)\n", "\n", "# model = LLMGatewayModel(\n", "model = langchain.chat_models.AzureChatOpenAI(\n", "    # deployment_name=\"gpt-35-turbo-1106\",\n", "    # deployment_name=\"gpt-4-1106-Preview\",\n", "    deployment_name='gpt-4',\n", "    # deployment_name='gpt-4-turbo',\n", "    temperature=0,\n", "    max_tokens=200,\n", "    max_retries=10\n", ")\n", "\n", "chain = chat_prompt | model\n", "chain.invoke({\n", "    \"workflow\": \"Hello world\"\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Here's a workflow\n", "{workflow}\n", "\n", "Here's the query\n", "{query}\n", "\n", "Heres's a summary\n", "{summary}\n", "\n", "Generate a planning for creating the workflow.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_dataset = {k: v for k, v in dataset.items() if 10 < len(v) < 100_000}\n", "print(len(filtered_dataset), len(dataset))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "i = random.randint(0, len(filtered_dataset))\n", "print(i)\n", "values = list(filtered_dataset.values())\n", "print(values[i])\n", "response = chain.invoke({\"workflow\": values[i]})\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "responses = {}\n", "for name, workflow in tqdm.tqdm(filtered_dataset.items()):\n", "    response = chain.invoke({\"workflow\": workflow})\n", "    responses[name] = response.content\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dump response\n", "import json\n", "\n", "with open(windows_path / \"_queries.json\", \"w\") as f:\n", "    json.dump(responses, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(windows_path / \"_queries.txt\", \"w\") as fd:\n", "    for name, query in responses.items():\n", "        fd.write(f\"{windows_path / name}\\n{query}\\n\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate plan from query and workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import langchain.chat_models\n", "import langchain.prompts\n", "import langchain.schema\n", "\n", "# from services.studio._text_to_workflow.utils.llm_gateway_model import LLMGatewayModel\n", "\n", "system_prompt = \"\"\"You are an RPA expert working with UiPath Studio.\n", "You can read workflow yaml proficiently and can reverse engineer what is happening inside one.\n", "You are also able to devise a plan that would summarize the steps taken by the workflow in brief natural language.\n", "Although not obvious at first, some workflows are more complex than others. Instead of explaining verbatim, try to offer concise statements.\n", "Try to avoid generic formulations and stick to specific information that you can infer from the workflow.\n", "If there are assignment operations, avoid mentioning them in the plan such as \"Assign the current directory path\". It should only contain concise steps.\n", "Please maintain the most important steps in the plan. Logs, assignemnts, try catches are not necessary.\n", "Please follow the format from the provided demonstrations.\n", "\"\"\"\n", "# Avoid formulations starting with \"Create a UiPath workflow that ...\" or \"Implement a workflow that ...\", as the user is already familiar.\n", "# You can start directly with what \"...\" would have contained.\n", "# Your answer should be imperative, as you would be demanding for the specific workflow to be created.\n", "# You are able to assess how concise and specific your answer is with a number between 0 and 10.\n", "\n", "user_prompt = \"\"\"\n", "Here is the workflow\n", "```yaml\n", "{workflow}\n", "```\n", "\n", "The query: {query}\n", "\n", "A verbose summarization of the workflow\n", "```plaintext\n", "{summary}\n", "```\n", "\n", "Try to figure out the main ideas of the workflow and devise a plan that would aid someone else to recreate the workflow.\n", "\"\"\"\n", "\n", "# Your output should be a json like this one:\n", "# ```json\n", "# {{\n", "#     \"summary\": \"\",\n", "#     \"conciseness\": 0\n", "#     \"confidence\": 0\n", "# }}\n", "# ```\n", "# \"\"\"\n", "\n", "\"\"\"\n", "Here are some examples of queries and their respective plans:\n", "\n", "Query:\n", "Plan to devise:\n", "\n", "\n", "Query:\n", "Plan to devise:\n", "\n", "\"\"\"\n", "\n", "system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(system_prompt)\n", "user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(user_prompt)\n", "\n", "demonstrations = [\n", "    user_message_template.format(\n", "        query=\"Use OpenAI to create a new email message for a customer interested in UiPath Test Suite and send it over to me for review\",\n", "        workflow=\"<too_verbose_to_include_in_demonstration>\",\n", "        summary=\"<too_verbose_to_include_in_demonstration>\"\n", "    ),\n", "    langchain.prompts.AIMessagePromptTemplate.from_template(\"\"\"\n", "  1. <PERSON> Trigger\n", "  2. Use OpenAI to generate text for a customer interested in UiPath Test Suite.\n", "  3. Send the email to the user for review\n", "\"\"\"),\n", "    user_message_template.format(\n", "        query=\"Whenever I receive a JPG with a receipt on Gmail, extract the document data and notify my team on the receipts channel in Slack\",\n", "        workflow=\"<too_verbose_to_include_in_demonstration>\",\n", "        summary=\"<too_verbose_to_include_in_demonstration>\"\n", "    ),\n", "    langchain.prompts.AIMessagePromptTemplate.from_template(\"\"\"\n", "  1. <PERSON><PERSON> received with JPG attachment\n", "  2. Download JPG Attachments\n", "  3. For each JPG Attachment\n", "    3.1. Extract Document Data as Receipt\n", "    3.2. Notify team on receipts channel in Slack\n", "\"\"\"),\n", "]\n", "\n", "chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages([\n", "    system_message_template,\n", "    *demonstrations,\n", "    user_message_template\n", "])\n", "\n", "# model = LLMGatewayModel(\n", "model = langchain.chat_models.AzureChatOpenAI(\n", "    # deployment_name=\"gpt-35-turbo-1106\",\n", "    # deployment_name=\"gpt-4-1106-Preview\",\n", "    # deployment_name='gpt-4',\n", "    deployment_name='gpt-4-turbo',\n", "    temperature=0,\n", "    max_tokens=200,\n", "    max_retries=10\n", ")\n", "\n", "chain = chat_prompt | model\n", "inputs = {\n", "    \"workflow\": \"Hello world\",\n", "    \"query\": \"Hello world\",\n", "    \"summary\": \"print(\\\"Hello world\\\")\"\n", "}\n", "print(chat_prompt.format(**inputs))\n", "# chain.invoke(inputs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "filtered_dataset = {k: v for k, v in dataset.items() if 10 < len(v) < 100_000 and k.endswith('.yaml')}\n", "print(len(filtered_dataset), len(dataset))\n", "name_to_query = json.load(open(\"/workspace/data/workflows/parsed_windows1/_queries.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "i = random.randint(0, len(filtered_dataset))\n", "# i = 102\n", "# i = 194\n", "print(i)\n", "keys = list(filtered_dataset.keys())\n", "values = list(filtered_dataset.values())\n", "query = name_to_query[keys[i]]\n", "summary = get_workflow_summary(yaml_load(values[i]))\n", "# print(values[i])\n", "print(keys[i])\n", "print(query)\n", "print(summary)\n", "response = chain.invoke({\n", "    \"workflow\": values[i],\n", "    \"summary\": summary,\n", "    \"query\": query\n", "})\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "responses = {}\n", "for name, workflow in tqdm.tqdm(filtered_dataset.items()):\n", "    try:\n", "        query = name_to_query[name]\n", "        summary = get_workflow_summary(yaml_load(workflow))\n", "        # print(values[i])\n", "        response = chain.invoke({\n", "            \"workflow\": workflow,\n", "            \"summary\": summary,\n", "            \"query\": query\n", "        })\n", "        responses[name] = response.content\n", "    except Exception:\n", "        print(f\"Exception for \\\"{name}\\\"\")\n", "        import traceback\n", "        traceback.print_exc()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#  \"demonstrations\": [d.to_json() for d in demonstrations]\n", "# to_dump = {\"system_prompt\": system_message_template.to_json(), \"user_prompt\": user_message_template.to_json(), \"responses\": responses}\n", "to_dump = responses\n", "with open(windows_path / \"_plans.json\", \"w\") as f:\n", "    json.dump(to_dump, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for name, query in name_to_query.items():\n", "    print((windows_path / name).absolute().as_posix())\n", "    try:\n", "        summary = get_workflow_summary(yaml_load(filtered_dataset[name]))\n", "    except:\n", "        print('Exception loading workflow!')\n", "        print()\n", "        continue\n", "    plan = responses.get(name, \"no_plan\")\n", "    print('-' * 50)\n", "    print(f'Query: {query}')\n", "    print('Plan')\n", "    print(plan)\n", "    print('Summary:')\n", "    print(summary)\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import load_data\n", "\n", "base_path = pathlib.Path('/workspace/data/workflows')\n", "# root_path = base_path / \"parsed_portable1\"\n", "root_path = base_path / \"parsed_portable2\"\n", "# root_path = base_path / \"parsed_windows1\"\n", "# root_path = base_path / \"parsed_windows2\"\n", "\n", "dataset, dataset_raw, invalid_workflows = load_data(root_path, verbose=False, bounds=(10, 100_000))\n", "print(f\"Loaded {len(dataset)} workflows. {len(invalid_workflows)} invalid workflows.\")\n", "queries = json.load(open(root_path / \"_queries.json\"))\n", "assert all(k in queries for k in dataset), f\"Missing queries {set(dataset) - set(queries)}\"\n", "plans = json.load(open(root_path / \"_plans.json\"))\n", "# assert all(k in plans for k in dataset), f\"Missing plans {set(dataset) - set(plans)}\"\n", "missing_plans = [k for k in dataset if k not in plans]\n", "assert len(missing_plans) < 10, f\"Missing plans {missing_plans}\"\n", "\n", "if missing_plans:\n", "    missing_plans = set(missing_plans)\n", "    dataset = {k: v for k, v in dataset.items() if k not in missing_plans}\n", "    dataset_raw = {k: v for k, v in dataset_raw.items() if k not in missing_plans}\n", "    queries = {k: v for k, v in queries.items() if k not in missing_plans}\n", "    print(f\"Filtered out missing plans {len(missing_plans)}. Having {len(dataset)} remaining.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Configure activity dumping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# queries = {}\n", "# with open(root_path / \"_queries_v1.txt\") as fd:\n", "#     lines = fd.readlines()\n", "#     for start in range(0, len(lines), 3):\n", "#         path = lines[start].strip()\n", "#         query = lines[start + 1].strip()\n", "#         queries[path] = query\n", "#         # print(path)\n", "#         # print(query)\n", "# # print(root_path)\n", "# with open(root_path / \"_queries.json\", \"w\") as fd:\n", "#     json.dump(queries, fd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.workflow_utils import has_inner_activity, iterate_activities\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "\n", "def custom_iteration(datapoint):\n", "    identifier, workflow = datapoint\n", "    # for node, ancestry in iterate_nodes(workflow):\n", "    for node, ancestry, variables in iterate_activities(workflow):\n", "        thought = node.get(\"thought\", \"no thought\")\n", "        activity = node.get(\"activity\")\n", "        params = node.get(\"params\", {})\n", "        params = {k: v for k, v in params.items() if not has_inner_activity(v)}\n", "\n", "        # indent = \"  \" * len(ancestry)\n", "        # params = [f\"{k}: {v if len(v) < 100 else v[:100] + ' ...'}\" for k, v in params.items()]\n", "        # print(f\"{indent}{activity} ({thought}) | {' | ' .join(params)}\")\n", "        # print(f\"other path {ancestry}              {sorted(node.keys())}\")\n", "\n", "        if 'variables' in node:\n", "            raise\n", "\n", "\n", "# datapoint = list(dataset.items())[0]\n", "# print(custom_iteration(datapoint))\n", "# for datapoint in dataset.items():\n", "#     custom_iteration(datapoint)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dump activity configs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.config import constants\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesTriggersIndex\n", "\n", "config = yaml_load(open('/workspace/src/studio/text_to_workflow/workflow_generation/config/config.yaml'))\n", "index_path = pathlib.Path('/workspace/src/studio/text_to_workflow/embeddings/activities')\n", "activities_index = ActivitiesTriggersIndex('activities', config, index_path, constants.BASIC_ACTIVITIES)\n", "activities_index.build(True)\n", "\n", "def iter_activity_config(datapoint):\n", "    identifier, workflow = datapoint\n", "    # for node, ancestry in iterate_nodes(workflow):\n", "    global_variables = workflow.get(\"variables\")\n", "    arguments = workflow.get(\"arguments\", [])\n", "\n", "    for node, ancestry, local_variables in iterate_activities(workflow.get(\"workflow\", [])):\n", "        thought = node.get(\"thought\", \"no thought\")\n", "        activity = node.get(\"activity\")\n", "        params = node.get(\"params\", {})\n", "        params = {k: v for k, v in params.items() if not has_inner_activity(v)}\n", "\n", "        # indent = \"  \" * len(ancestry)\n", "        # params = [f\"{k}: {v if len(v) < 100 else v[:100] + ' ...'}\" for k, v in params.items()]\n", "        # print(f\"{indent}{activity} ({thought}) | {' | ' .join(params)}\")\n", "        # print(f\"other path {ancestry}              {sorted(node.keys())}\")\n", "        activity_data = activities_index.get_by_name(activity)\n", "        if activity_data is None:\n", "            print(activity)\n", "            continue\n", "\n", "        activity_configuration_datapoint = {\n", "            \"workflow_name\": pathlib.Path(identifier).name,\n", "            \"workflow_description\": queries[identifier],\n", "            \"activity_id\": activity,\n", "            \"activity_fqn\": activity,\n", "            \"activity_display_name\": activity_data[\"displayName\"],\n", "            \"plan\": plans[identifier],\n", "            \"arguments\": arguments,\n", "            \"global_variables\": global_variables,\n", "            \"local_variables\": local_variables,\n", "            \"params\": params,\n", "            \"activity_typedef\": activity_data[\"typeDefinition\"],\n", "            \"additional_typedefs\": activity_data[\"additionalTypeDefinitions\"],\n", "        }\n", "        yield activity_configuration_datapoint\n", "\n", "datapoint = list(dataset.items())[0]\n", "\n", "activity_path = root_path.with_name(root_path.name + \"_activity_configuration\")\n", "activity_path.mkdir(exist_ok=True)\n", "for datapoint in dataset.items():\n", "    workflow_path, workflow = datapoint\n", "    workflow_name = pathlib.Path(workflow_path).name\n", "    workflow_dir = activity_path / f\"[Production] {workflow_name}\"\n", "    workflow_dir.mkdir(exist_ok=True)\n", "\n", "    activity_configs = list(iter_activity_config(datapoint))\n", "\n", "    for i, activity_config in enumerate(activity_configs):\n", "        activity_config[\"workflow_path\"] = (root_path / activity_config[\"workflow_name\"]).as_posix()\n", "        activity_config[\"workflow_number_of_activities\"] = len(activity_configs)\n", "        activity_name = activity_config[\"activity_id\"]\n", "        with open(workflow_dir / f\"{i:02}___{activity_name}.yaml\", \"w\") as fd:\n", "            yaml_dump(activity_config, fd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Retrieve additional information about activities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.config import constants\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesTriggersIndex\n", "\n", "config = yaml_load(open('/workspace/src/studio/text_to_workflow/workflow_generation/config/config.yaml'))\n", "index_path = pathlib.Path('/workspace/src/studio/text_to_workflow/embeddings/activities')\n", "activities_index = ActivitiesTriggersIndex('activities', config, index_path, constants.BASIC_ACTIVITIES)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["activities_index.build(True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["activity_data = activities_index.get_by_name(\"UiPath.Core.Activities.InvokeCode\")\n", "display(activity_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze activity configs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["configs = {}\n", "activities_path = root_path.with_name(root_path.name + \"_activity_configuration\")\n", "for activity_group in activities_path.iterdir():\n", "    for act_path in activity_group.iterdir():\n", "        with open(act_path) as fd:\n", "            raw_config = fd.read()\n", "            # loaded_config = yaml_load(raw_config)\n", "            configs[act_path.as_posix()] = raw_config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "sizes = [*map(len, configs.values())]\n", "print(f\"There are {sizes.count(0)} with 0 size\")\n", "log_sizes = [*map(math.log10, [s for s in sizes if s > 0])]\n", "fig, axs = plt.subplots(1, 2, figsize=(10, 5))\n", "\n", "# Plot the first histogram\n", "axs[0].hist(sizes, bins=50)\n", "axs[0].set_title('Sizes Histogram')\n", "axs[0].set_xlabel('Sizes')\n", "axs[0].set_ylabel('Frequency')\n", "\n", "# Plot the second histogram\n", "axs[1].hist(log_sizes, bins=50)\n", "axs[1].set_title('Log Sizes Histogram')\n", "axs[1].set_xlabel('Log <PERSON> (powers of 10)')\n", "axs[1].set_ylabel('Frequency')\n", "axs[1].xaxis.set_major_formatter(ticker.FuncFormatter(lambda x, pos: f'$10^{{{int(x)}}}$'))\n", "\n", "# Display the plots\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_to_sample = 100\n", "stride = len(configs) // n_to_sample\n", "\n", "for path, config in sorted(configs.items(), key=lambda x: len(x[1]))[::stride]:\n", "    print()\n", "    print('#' * 100)\n", "    print(path)\n", "    print()\n", "    print(config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "all_variables = collections.Counter()\n", "variable_types = collections.Counter()\n", "\n", "def gather_vars(workflow):\n", "    workflow_variables = set()\n", "    for node, ancestry, variables in iterate_activities(workflow):\n", "        workflow_variables |= set(variables)\n", "    all_variables.update(workflow_variables)\n", "    variable_types.update([v.split(\":\")[1] for v in workflow_variables])\n", "\n", "for workflow in dataset.values():\n", "    gather_vars(workflow)\n", "with open(root_path / \"_variables_most_common.txt\", \"w\") as fd:\n", "    for var, count in all_variables.most_common():\n", "        fd.write(f\"{count:3} {var}\\n\")\n", "with open(root_path / \"_variables.txt\", \"w\") as fd:\n", "    for var in sorted(all_variables.keys()):\n", "        fd.write(f\"{var}\\n\")\n", "with open(root_path / \"_variable_types_most_common.txt\", \"w\") as fd:\n", "    for var, count in variable_types.most_common():\n", "        fd.write(f\"{count:3} {var}\\n\")\n", "with open(root_path / \"_variable_types.txt\", \"w\") as fd:\n", "    for var in sorted(variable_types.keys()):\n", "        fd.write(f\"{var}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use existing abstraction for activity configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import load_data\n", "\n", "root_path = portable_path\n", "# root_path = windows_path\n", "print(root_path)\n", "dataset, dataset_raw, invalid = load_data(root_path, verbose=True, bounds=(10, 100_000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import workflow\n", "\n", "for _path, datapoint in dataset.items():\n", "    wf = workflow.Workflow(None, None, datapoint)\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "\n", "class Visitor(workflow.Visitor):\n", "    def visit_activity(self, activity):\n", "        # print(activity.fqn)\n", "        if activity.unknown_params:\n", "            print(activity.fqn)\n", "            print(activity.unknown_params)\n", "            raise\n", "\n", "        for activity_container in [activity.activities, activity.activity_delegates]:\n", "            for sequence in activity_container.values():\n", "                for child_activity in sequence:\n", "                    child_activity.accept(self)\n", "\n", "    def visit_workflow(self, workflow: Workflow) -> None:\n", "        if wf.trigger is not None:\n", "            wf.trigger.accept(self)\n", "        for activity in wf.activities:\n", "            activity.accept(self)\n", "\n", "\n", "for _path, datapoint in dataset.items():\n", "    try:\n", "        wf = workflow.Workflow(None, None, datapoint)\n", "    except Exception as e:\n", "        print('Exception loading workflow! To look into', _path, e)\n", "    try:\n", "        wf.accept(Visitor())\n", "    except:\n", "        print(_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# workflow.Workflow(None, None, dataset[\"/workspace/data/workflows/parsed_portable1/parsed_f2ea9403-825b-732d-5fab-5e6cb4b24cf4_Main.xaml.yaml\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_config.activity_config_dataset import WorkflowToActivityConfigConverter\n", "\n", "converter = WorkflowToActivityConfigConverter(True)\n", "configs = {}\n", "\n", "for _path, datapoint in dataset.items():\n", "    try:\n", "        wf = workflow.Workflow(queries[_path], plans[_path], datapoint)\n", "    except Exception as e:\n", "        print('Exception loading workflow! To look into', _path, e)\n", "        continue\n", "    try:\n", "        configs[_path] = converter.convert_all(wf)\n", "    except Exception as e:\n", "        print('Exception conversion!', e, path)\n", "        print(_path)\n", "print(len(configs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum(map(len, configs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path, activity_configs in configs.items():\n", "    print(len([*iterate_activities(dataset[path])]), path, len(activity_configs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use existing abstraction for generating the plan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Activity, Visitor\n", "\n", "\n", "def summarize_activity(activity: Activity):\n", "    print(str(activity))\n", "    # print(child_activity.fqn)\n", "    # print(len(child_activity.activities), len(child_activity.activity_delegates))\n", "    # print(len(child_activity.primitive_properties), len(child_activity.list_properties), len(child_activity.dict_properties))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "from services.studio._text_to_workflow.common.walkers import WorkflowPruner\n", "\n", "for path, datapoint in dataset.items():\n", "    dp = copy.deepcopy(datapoint)\n", "    wf = Workflow(None, None, dp)\n", "    # WorkflowPruner().prune(wf)\n", "    if WorkflowPruner().prune(wf):\n", "        print(wf.lmyaml2())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "# builder = None  # e stateful si nu merge pastrat\n", "\n", "# for _ in range(1):\n", "#     path = \"/workspace/data/workflows/parsed_portable1/parsed_9ec00359-5cb1-4be9-66a9-b67ff1e17951_FV_CostCenter.xaml.yaml\"\n", "#     datapoint = dataset[path]\n", "for path, datapoint in dataset.items():\n", "    try:\n", "        wf = Workflow(queries[path], plans[path], datapoint)\n", "    except Exception as e:\n", "        print('Exception loading workflow! To look into', path, e)\n", "        continue\n", "    # if not wf.trigger:\n", "    #     continue\n", "    try:\n", "        plan = PlanBuilder().build(wf)\n", "        print('-' * 50)\n", "        print(path)\n", "        print('-' * 50)\n", "        print(plan)\n", "    except Exception as e:\n", "        print('Exception building plan!', e, path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare lmyaml with lmyaml2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import difflib\n", "\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "\n", "def get_diff_lines(a, b):\n", "    # a = pprint.pformat(a)\n", "    # b = pprint.pformat(b)\n", "    a_lines = a.splitlines(keepends=True)\n", "    b_lines = b.splitlines(keepends=True)\n", "    return \"\".join(difflib.ndiff(a_lines, b_lines))\n", "\n", "stats = collections.Counter()\n", "for path, datapoint in dataset.items():\n", "    print(path)\n", "    wf = Workflow(queries[path], plans[path], datapoint)\n", "    yaml_v1 = wf.lmyaml()\n", "    yaml_v2 = wf.lmyaml2()\n", "\n", "    stats['total'] += 1\n", "    if yaml_v1 == yaml_v2:\n", "        stats['same'] += 1\n", "        continue\n", "    stats['different'] += 1\n", "\n", "    print(get_diff_lines(yaml_v1, yaml_v2))\n", "    print('\\n' * 20)\n", "display(stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.workflow_utils import iterate_activities\n", "\n", "inversed_ifs = []\n", "for path, datapoint in dataset.items():\n", "    for activity, ancestry, variables in iterate_activities(datapoint):\n", "        activity_name = activity['activity']\n", "        if activity_name == 'System.Activities.Statements.If':\n", "            order = list(activity['params'].keys())\n", "            if order == ['Condition', 'Else', 'Then']:\n", "                inversed_ifs.append(path)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Use the new data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_sources = [\n", "    \"/workspace/data/workflows/parsed_portable1_activity_configuration_v2\",\n", "    \"/workspace/data/workflows/parsed_portable2_activity_configuration_v2\",\n", "    \"/workspace/data/workflows/parsed_windows1_activity_configuration_v2\",\n", "    \"/workspace/data/workflows/parsed_windows2_activity_configuration_v2\",\n", "]\n", "data_destination = \"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import shutil\n", "\n", "import tqdm\n", "\n", "for source in data_sources:\n", "    listing = list(pathlib.Path(source).iterdir())\n", "    for path in tqdm.tqdm(listing, desc=f\"Copying from {source}\"):\n", "        if path.is_dir():\n", "            destination_path = pathlib.Path(data_destination) / path.name\n", "            if destination_path.exists():\n", "                print(\"Destination already exists\", destination_path)\n", "                continue\n", "                # shutil.rmtree(destination_path)\n", "            shutil.copytree(path, destination_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare activities: train vs prod"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "dataset_portable_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable\")\n", "dataset_windows_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows\")\n", "# prod_path = dataset_path / \"prod\"\n", "# train_path = dataset_path / \"train\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "\n", "def load_activities(subset_path):\n", "    activities = []\n", "    paths = [p for p in subset_path.rglob(\"*.yaml\") if p.is_file()]\n", "    progbar = tqdm.tqdm(paths, desc=f\"Loading activities from {subset_path.name}\")\n", "    for activity_path in progbar:\n", "        # with open(activity_path) as fd:\n", "        #     content = fd.read()\n", "            # data = yaml_load(fd)  # 47 it / s cu load-ul\n", "        # activities[activity_path] = data['activity_fqn']\n", "        activities.append(activity_path.stem.rsplit('___', 1)[-1])\n", "    return activities\n", "\n", "prod_activities = [*load_activities(dataset_portable_path / \"prod-all\"), *load_activities(dataset_windows_path / \"prod-all\")]\n", "train_activities = [*load_activities(dataset_portable_path / \"train\"), *load_activities(dataset_windows_path / \"train\")]\n", "prod_counter = collections.Counter(prod_activities)\n", "train_counter = collections.Counter(train_activities)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "from services.studio._text_to_workflow.activity_config.activity_config_schema import ActivityConfigDemoRetrieverState\n", "\n", "# @deprecated\n", "# def load_activities_retriever_counter(path):\n", "#     bin_path = path / \"retriever.bin\"\n", "#     with open(bin_path, \"rb\") as f:\n", "#         bin = pickle.load(f)\n", "#         fname2id = bin[\"fname2id\"]\n", "#         id2fname = bin[\"id2fname\"]\n", "#         aname2ids = bin[\"aname2ids\"]\n", "#         exampleid2id = bin[\"exampleid2id\"]\n", "#     return {k: len(v) for k, v in aname2ids.items()}\n", "\n", "def load_activities_retriever_counter(path):\n", "    bin_path = path / \"state.pkl\"\n", "    with open(bin_path, \"rb\") as f:\n", "        state : ActivityConfigDemoRetrieverState = pickle.load(f)\n", "    return {k: len(v) for k, v in state[\"lookup\"].items()}\n", "\n", "retriever_root_path = pathlib.Path(\"/workspace/src/studio/text_to_workflow/embeddings/activity_config\")\n", "retriever_root_path = pathlib.Path(\"/workspace/data/Retrievers\")\n", "\n", "# train_retr_counter = load_activities_retriever_counter(retriever_root_path / \"retriever-original-portable\")\n", "# prod_retr_counter = load_activities_retriever_counter(retriever_root_path / \"retriever-prod-portable\")\n", "\n", "train_retr_counter = load_activities_retriever_counter(retriever_root_path / \"_archive\" / \"retriever-original\")\n", "prod_retr_counter = load_activities_retriever_counter(retriever_root_path / \"_archive\" / \"retriever-prod\")\n", "\n", "# state : ActivityConfigDemoRetrieverState = pickle.load(open(retriever_root_path / \"retriever-original\" / \"state.pkl\", \"rb\"))\n", "# display(sorted(state[\"lookup\"].keys()))\n", "# other_keys = {\"Portable\", \"Windows\", \"train\"}\n", "# state[\"lookup\"][\"System.Activities.Statements.AddToCollection\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.config import constants\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesRetriever\n", "\n", "# config = yaml_load(open(\"/workspace/src/studio/text_to_workflow/workflow_generation/config/config.yaml\"))\n", "# index_path = pathlib.Path(\"/workspace/src/studio/text_to_workflow/embeddings/activities\")\n", "# activities_index = ActivitiesTriggersIndex(\"activities\", config, index_path, constants.BASIC_ACTIVITIES)\n", "# activities_index = ActivitiesTriggersIndex(\"activities\", config, index_path)  # pentru triggers\n", "# activities_index.build(True)\n", "# activities_index.load()\n", "\n", "retr = ActivitiesRetriever(\"config.yaml\").load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retr.get(\"UiPath.Core.Activities.InvokeCode\", \"Portable\", None)\n", "# retr.get(\"UiPath.Core.Activities.InvokeCode\", \"Windows\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_activities = sorted(set(prod_counter) | set(train_counter))\n", "# key_func = lambda act: prod_retr_counter.get(act, 0) - train_retr_counter.get(act, 0)\n", "# key_func = lambda act: train_retr_counter.get(act, 0)\n", "\n", "# activities_additional_details = {a: activities_index.get_by_name(a) for a in all_activities}\n", "activities_additional_details = {a: retr.get(a, \"Portable\", None) or retr.get(a, \"Windows\", None) for a in all_activities}\n", "\n", "import pandas as pd\n", "\n", "df = pd.DataFrame({\n", "    \"train_ret\": [train_retr_counter.get(a, 0) for a in all_activities],\n", "    \"train\": [train_counter.get(a, 0) for a in all_activities],\n", "    \"prod_ret\": [prod_retr_counter.get(a, 0) - train_retr_counter.get(a, 0) for a in all_activities],\n", "    \"prod\": [prod_counter.get(a, 0) for a in all_activities],\n", "    \"activity\": all_activities,\n", "})\n", "\n", "for k, v in activities_additional_details.items():\n", "    if v is None:\n", "        v = {}\n", "    for key, value in v.items():\n", "        df.loc[df.activity == k, key] = value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for activity, packageName in df[[\"activity\", \"packageName\"]].sort_values(\"activity\").itertuples(index=False):\n", "    if isinstance(packageName, float):  # idk why but pandas uses nan for these (float nan)\n", "        packageName = \"No package\"\n", "    print(f\"{packageName:50} {activity}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for activity in sorted(common_activities, key=key_func, reverse=True):\n", "ordering = [\"train_ret\", \"train\", \"prod_ret\", \"prod\", \"activity\"]\n", "# ordering = [\"prod_ret\", \"prod\", \"train_ret\", \"train\", \"activity\"]\n", "# ordering = [\"activity\"]\n", "for train_c_retr, train_c, prod_c_retr, prod_c, activity in df[ordering].sort_values(ordering, ascending=False).itertuples(index=False):\n", "    train_c = train_counter.get(activity, 0)\n", "    train_c_retr = train_retr_counter.get(activity, 0)\n", "    prod_c = prod_counter.get(activity, 0)\n", "    prod_c_retr = prod_retr_counter.get(activity, 0) - train_c_retr\n", "    # print(f\"{train_c_retr:5}/{train_c:5}     {prod_c_retr:5}/{prod_c:5} {activity}\")  # daca nu avem typedefes pentru toate\n", "    # print(f\"{train_c_retr:5}  {prod_c_retr:5} {activity}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_add = {}\n", "for activity in df[\"activity\"]:\n", "    train_c_retr = train_retr_counter.get(activity, 0)\n", "    prod_c_retr = prod_retr_counter.get(activity, 0) - train_c_retr\n", "    if train_c_retr < 10 and prod_c_retr > 0:\n", "        print(f\"{train_c_retr:5}  {prod_c_retr:5} {activity}\")\n", "        to_add[activity] = min(10 - train_c_retr, prod_c_retr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sum(to_add.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Sample activities for creating a smaller dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_portable_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod-all\")\n", "dataset_windows_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows/prod-all\")\n", "\n", "import collections\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "\n", "def get_activity_index(subset_path):\n", "    activities = collections.defaultdict(list)\n", "    paths = [p for p in subset_path.rglob(\"*.yaml\") if p.is_file()]\n", "    progbar = tqdm.tqdm(paths, desc=f\"Loading activities from {subset_path.name}\")\n", "    for activity_path in progbar:\n", "        activity_name = activity_path.stem.rsplit('___', 1)[-1]\n", "        # loaded_data = yaml_load(open(activity_path))\n", "        # if activity_name != loaded_data[\"activity_id\"]:\n", "        #     print(activity_name)\n", "        #     print(loaded_data[\"activity_id\"])\n", "        activities[activity_name].append(activity_path)\n", "    return activities\n", "\n", "portable_activities = get_activity_index(dataset_portable_path)\n", "windows_activities = get_activity_index(dataset_windows_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "portable_sample = []\n", "windows_sample = []\n", "\n", "to_add_from_windows = {}\n", "for activity, n_needed in to_add.items():  # sample precedence: portable > windows\n", "    portable_matches = portable_activities.get(activity, [])\n", "    windows_matches = windows_activities.get(activity, [])\n", "\n", "    n_portable_sample = min(len(portable_matches), n_needed)\n", "    random.seed(42)\n", "    portable_sample += random.sample(portable_matches, n_portable_sample)\n", "\n", "    n_windows_to_sample = 0\n", "    if n_portable_sample < n_needed:\n", "        remaining_count = n_needed - n_portable_sample\n", "        n_windows_to_sample = min(len(windows_matches), remaining_count)\n", "        random.seed(42)\n", "        windows_sample += random.sample(windows_matches, n_windows_to_sample)\n", "    print(f\"{activity:70} having {train_retr_counter.get(activity, 0)} to add: {n_needed:3} from portable: {n_portable_sample:3}/{len(portable_matches):<5} from windows: {n_windows_to_sample:3}/{len(windows_matches):<5}\")\n", "print(f\"Portable: {len(portable_sample)} Windows: {len(windows_sample)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "output_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod-sample-upto-10\")\n", "for path in portable_sample:\n", "    new_path = output_path / path.relative_to(dataset_portable_path)\n", "    new_path.parent.mkdir(parents=True, exist_ok=True)\n", "    shutil.copy2(path, new_path)\n", "\n", "output_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows/prod-sample-upto-10\")\n", "for path in windows_sample:\n", "    new_path = output_path / path.relative_to(dataset_windows_path)\n", "    new_path.parent.mkdir(parents=True, exist_ok=True)\n", "    shutil.copy2(path, new_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Process 2024 08 05 data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Group workflows into subdirectories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# root_path = pathlib.Path(\"/workspace/data/workflows/windows_converted_05082024\")\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_converted_05082024\")\n", "listing = list(root_path.iterdir())\n", "len(listing)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import re\n", "import shutil\n", "\n", "prefix_counter = collections.Counter()\n", "for p in listing:\n", "    if re.match(r\"\\d{2}_\", p.name):\n", "        continue\n", "    _, process_name, workflow_name = p.name.split('_', 2)\n", "    prefix = process_name[:2]\n", "    subdir = root_path / prefix\n", "    if not subdir.exists():\n", "        subdir.mkdir()\n", "\n", "    prefix_counter[prefix] += 1\n", "    new_filepath = subdir / f\"{process_name}_{workflow_name}\"\n", "    print(f\"Moving {p} to {new_filepath}\")  # for dry run\n", "    # shutil.move(p, new_filepath)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ungroup - only for portable\n", "\n", "for p in listing:\n", "    for subp in p.iterdir():\n", "        print(f\"Moving {subp} to {root_path / subp.name}\")\n", "        # shutil.move(subp, p.parent / subp.name)\n", "for p in listing:\n", "    if p.is_dir() and not list(p.iterdir()):\n", "        pass\n", "        # shutil.rmtree(p)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract .yamls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workflows/windows_converted_05082024\")\n", "# root_path = pathlib.Path(\"/workspace/data/workflows/portable_converted_05082024\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import json\n", "import zipfile\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import build_yaml_object\n", "\n", "output_path = pathlib.Path(\"/workspace/data/workflows/\") / \"windows_20240805\"\n", "# output_path = pathlib.Path(\"/workspace/data/workflows/\") / \"portable_20240805\"\n", "\n", "drop_reason = collections.defaultdict(list)\n", "\n", "workflows = {}\n", "listing = [*root_path.rglob(\"*.zip\")]\n", "for p in tqdm.tqdm(listing):\n", "    process_name, workflow_name = p.stem.split('_', 1)\n", "    assert len(process_name) == 36, \"Invalid process name\"\n", "\n", "    with zipfile.ZipFile(p, \"r\") as zf:\n", "        conversion_result_names = []\n", "        typedefs = set()\n", "        for name in sorted(zf.namelist()):\n", "            if name.endswith(\".typedef.json\"):\n", "                typedefs.add(name)\n", "                continue\n", "            conversion_result_names.append(name)\n", "\n", "        for name in conversion_result_names:\n", "            typedef_name = name.replace(\".json\", \".typedef.json\")\n", "\n", "            with zf.open(name) as fd:\n", "                data = json.load(fd)\n", "                # \"jitCommands\"\n", "\n", "            if not data[\"Success\"]:\n", "                drop_reason[\"unsuccessful\"].append(p)\n", "                continue\n", "            if len(data[\"Errors\"]):\n", "                drop_reason[\"with_errors\"].append(p)\n", "                continue\n", "            try:\n", "                workflow_string = data[\"result\"]\n", "                wf = yaml_load(workflow_string)\n", "            except Exception as e:\n", "                print(\"Error loading yaml\", e)\n", "                drop_reason[\"workflow_exception\"].append(p)\n", "                continue\n", "            if \"workflow\" not in wf:\n", "                drop_reason[\"no_workflow\"].append(p)\n", "                continue\n", "            if \"processName\" not in wf:\n", "                print(f\"Warning! No process name {p}\")\n", "\n", "            try:\n", "                datapoint = build_yaml_object(data, {\"name\": wf.get(\"processName\", workflow_name)})\n", "            except Exception as e:\n", "                print(\"Error building yaml object\", e)\n", "                drop_reason[\"build_exception\"].append(p)\n", "                continue\n", "\n", "            if typedef_name in typedefs:\n", "                with zf.open(typedef_name) as fd:\n", "                    typedef_data = json.load(fd)\n", "                    datapoint[\"typedefs\"] = typedef_data\n", "            if jit_commands := data.get(\"jitCommands\"):\n", "                datapoint[\"jitCommands\"] = jit_commands\n", "\n", "            # print(f\"{process_name=}\")\n", "            # print(f\"{workflow_name=}\")\n", "            # print(f\"{name=} {typedef_name in typedefs}\")\n", "            # print(data[\"jitCommands\"])\n", "            # print(yaml_dump(datapoint))\n", "            # print()\n", "\n", "            p_out = output_path / p.relative_to(root_path).with_suffix('.yaml')\n", "            p_out.parent.mkdir(exist_ok=True, parents=True)\n", "            yaml_dump(datapoint, p_out)\n", "\n", "stats_dir = output_path.with_name(output_path.name + \"_statistics\")\n", "stats_dir.mkdir(exist_ok=True)\n", "for reason, paths in drop_reason.items():\n", "    print(reason, len(paths))\n", "    with open(stats_dir / f\"_1preprocessing_dropped_{reason}.txt\", \"w\") as fd:\n", "        for p in paths:\n", "            fd.write(f\"{p.as_posix()}\\n\")\n", "print(\"Success: \", len(list(output_path.rglob(\"*.yaml\"))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Attempt to use minHash"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Toy example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import document_understanding.t5.dataset.minhash as minhash\n", "# this import did not work due to other dependencies in the document understanding projects\n", "from services.studio._text_to_workflow.utils import minhash\n", "\n", "shingles = minhash.create_shingles([\"Hello World\", \"Hello world!\"], [1, 2])\n", "print(shingles)\n", "print(minhash.find_duplicates([\"Hello World\", \"Hello world!\"], [1, 2], shingles, 100, 0.5))\n", "# with 10 hashes, works with 0.4 threshold but not 0.5\n", "def jaccard_distance(a, b):\n", "    a = set(a)\n", "    b = set(b)\n", "    return 1 - len(a & b) / len(a | b)\n", "print(jaccard_distance(*shingles.values()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Experiment with workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workflows/windows_20240805\")\n", "listing = list(root_path.rglob(\"*.yaml\"))\n", "print(len(listing))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "datapoints = {}\n", "workflows = []\n", "paths = []\n", "\n", "def func(path):\n", "    return yaml_load(path), path\n", "\n", "# 1m48s loading 100K workflows with 8 processes\n", "# 1m14s loading 100K workflows with 32 processes\n", "with multiprocessing.Pool(32) as pool:\n", "    for datapoint, path in tqdm.tqdm(pool.imap_unordered(func, listing), total=len(listing)):\n", "        datapoints[path] = datapoint\n", "        paths.append(path)\n", "        workflows.append(datapoint[\"process\"])\n", "\n", "# 7 min loading 100K workflows single process\n", "# for path in tqdm.tqdm(listing):\n", "#     datapoint = yaml_load(path)\n", "#     datapoints[path] = datapoint\n", "#     workflow = datapoint[\"process\"]\n", "#     workflows.append(workflow)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from multiprocessing import Pool\n", "\n", "workflows = workflows[:2_000]\n", "# workflows_as_str = [yaml_dump(wf) for wf in tqdm.tqdm(workflows)]\n", "# this took 1 minute for 20K\n", "\n", "with Pool(32) as p:\n", "    workflows_as_str = list(tqdm.tqdm(p.imap(yaml_dump, workflows), total=len(workflows)))\n", "# this took 9 seconds for either 8 processes or 32 processes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import minhash\n", "\n", "# k = 2000\n", "k = 10\n", "shingles = minhash.create_shingles(workflows_as_str[:k], paths[:k])\n", "# 1k docs in 4s\n", "# 2K in 8.6s\n", "# estimatino 100K in 7m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dup_pairs, sim_scores = minhash.find_duplicates(\n", "    workflows_as_str[:k], paths[:k], shingles, 100, 0.1)\n", "print(dup_pairs)\n", "print(sim_scores)\n", "# tested 2K docs in 1m24s ~ 4M comparisons\n", "# estimation 110K docs ~ 12B comparisons ~ 70h ~ 3 days\n", "# if grouped in buckets up to 2K workflows, then we can do it in ~1h, even faster if we parallelize buckets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deduplicate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workflows/windows_20240805\")\n", "listing = list(root_path.rglob(\"*.yaml\"))\n", "print(len(listing))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "datapoints = {}\n", "with multiprocessing.Pool(32) as pool:\n", "    def func(path):\n", "        return path, yaml_load(path)\n", "    for path, datapoint in tqdm.tqdm(pool.imap_unordered(func, listing), total=len(listing)):\n", "        datapoints[path] = datapoint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### First hash the workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Exact hash"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "exact_matches = collections.defaultdict(list)\n", "for path, datapoint in tqdm.tqdm(datapoints.items()):\n", "    serialization = str(datapoint[\"process\"])\n", "    exact_matches[serialization].append(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths_to_remove = []\n", "for i, paths in enumerate(sorted(exact_matches.values(), key=len, reverse=True)):\n", "    if not len(paths) > 1:\n", "        continue\n", "    print(f\"Group {i} ({len(paths)}):\")\n", "    for path in paths:\n", "        print(f\"  {path}\")\n", "    paths_to_remove.extend(sorted(paths)[1:])\n", "print(len(paths_to_remove))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in paths_to_remove:\n", "    del datapoints[path]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Partial hashing - no process name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "\n", "def get_hash(datapoint):\n", "    obj = copy.deepcopy(datapoint[\"process\"])\n", "    del obj['processName']\n", "    return str(obj)\n", "\n", "matches = collections.defaultdict(list)\n", "# for path, datapoint in tqdm.tqdm(datapoints.items()):\n", "#     matches[get_hash(datapoint)].append(path)\n", "with multiprocessing.Pool(32) as pool:\n", "    for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(get_hash, datapoints.values()), total=len(datapoints)), strict=False):\n", "        matches[h].append(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths_to_remove = []\n", "for i, paths in enumerate(sorted(matches.values(), key=len, reverse=True)):\n", "    if not len(paths) > 1:\n", "        continue\n", "    print(f\"Group {i} ({len(paths)}):\")\n", "    for path in paths:\n", "        print(f\"  {path}\")\n", "    paths_to_remove.extend(sorted(paths)[1:])\n", "print(len(paths_to_remove))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in paths_to_remove:\n", "    del datapoints[path]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### More partial hashing - no thoughts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "\n", "def get_hash(datapoint):\n", "    obj = copy.deepcopy(datapoint[\"process\"])\n", "    del obj['processName']\n", "\n", "    def strip_thoughts(node):\n", "        if isinstance(node, dict):\n", "            if \"thought\" in node:\n", "                del node[\"thought\"]\n", "            for v in node.values():\n", "                strip_thoughts(v)\n", "        if isinstance(node, list):\n", "            for v in node:\n", "                strip_thoughts(v)\n", "    strip_thoughts(obj)\n", "\n", "    return str(obj)\n", "\n", "matches = collections.defaultdict(list)\n", "# for path, datapoint in tqdm.tqdm(datapoints.items()):\n", "#     matches[get_hash(datapoint)].append(path)\n", "with multiprocessing.Pool(32) as pool:\n", "    for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(get_hash, datapoints.values()), total=len(datapoints)), strict=False):\n", "        matches[h].append(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths_to_remove = []\n", "for i, paths in enumerate(sorted(matches.values(), key=len, reverse=True)):\n", "    if not len(paths) > 1:\n", "        continue\n", "    print(f\"Group {i} ({len(paths)}):\")\n", "    for path in paths:\n", "        print(f\"  {path}\")\n", "    paths_to_remove.extend(sorted(paths)[1:])\n", "print(len(paths_to_remove))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in paths_to_remove:\n", "    del datapoints[path]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### More partial hashing - no variable names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "\n", "def get_hash(datapoint):\n", "    obj = copy.deepcopy(datapoint[\"process\"])\n", "    del obj['processName']\n", "\n", "    def strip_thoughts(node):\n", "        if isinstance(node, dict):\n", "            if \"thought\" in node:\n", "                del node[\"thought\"]\n", "            for v in node.values():\n", "                strip_thoughts(v)\n", "        if isinstance(node, list):\n", "            for v in node:\n", "                strip_thoughts(v)\n", "    strip_thoughts(obj)\n", "\n", "    obj_as_str = str(obj)\n", "\n", "    var_names = [str(var[\"name\"]) for var in obj.get(\"variables\", [])]\n", "    var_names = sorted(var_names, key=lambda name: obj_as_str.index(name))\n", "\n", "    arg_names = [str(var[\"name\"]) for var in obj.get(\"arguments\", [])]\n", "    arg_names = sorted(arg_names, key=lambda name: obj_as_str.index(name))\n", "\n", "    for i, var_name in enumerate(var_names):\n", "        obj_as_str = obj_as_str.replace(var_name, f\"var_{i}\")\n", "    for i, arg_name in enumerate(arg_names):\n", "        obj_as_str = obj_as_str.replace(arg_name, f\"arg_{i}\")\n", "    return obj_as_str\n", "\n", "matches = collections.defaultdict(list)\n", "# for path, datapoint in list(tqdm.tqdm(datapoints.items()))[:5]:\n", "#     matches[get_hash(datapoint)].append(path)\n", "with multiprocessing.Pool(32) as pool:\n", "    for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(get_hash, datapoints.values()), total=len(datapoints)), strict=False):\n", "        matches[h].append(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths_to_remove = []\n", "for i, paths in enumerate(sorted(matches.values(), key=len, reverse=True)):\n", "    if not len(paths) > 1:\n", "        continue\n", "    print(f\"Group {i} ({len(paths)}):\")\n", "    for path in paths:\n", "        print(f\"  {path}\")\n", "    paths_to_remove.extend(sorted(paths)[1:])\n", "print(len(paths_to_remove))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in paths_to_remove:\n", "    del datapoints[path]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### More partial hashing - no packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.deduplicate import filter_partial_no_packages\n", "\n", "filter_partial_no_packages(datapoints)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Commit without aggressive hashing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "new_root = root_path.with_name(root_path.name + \"_deduplicated_part1\")\n", "new_root.mkdir(exist_ok=True)\n", "for path, datapoint in datapoints.items():\n", "    new_path = new_root / path.relative_to(root_path)\n", "    # print(f\"Copying {path} to {new_path}\")\n", "    new_path.parent.mkdir(exist_ok=True, parents=True)\n", "    shutil.copy2(path, new_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Group by same activity tree and then minhash"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "root_path = pathlib.Path(\n", "    \"/workspace/data/workflows/windows_20240805_deduplicated_part1\"\n", ")\n", "listing = list(root_path.rglob(\"*.yaml\"))\n", "print(len(listing))\n", "\n", "import multiprocessing\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "datapoints = {}\n", "with multiprocessing.Pool(32) as pool:\n", "    def func(path):\n", "        return path, yaml_load(path)\n", "    for path, datapoint in tqdm.tqdm(pool.imap_unordered(func, listing), total=len(listing)):\n", "        datapoints[path] = datapoint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Aggressive hashing - activity tree"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import copy\n", "\n", "\n", "def get_hash(datapoint):\n", "    obj = copy.deepcopy(datapoint[\"process\"])\n", "    tree = []\n", "    def traverse(node, level=0):\n", "        if isinstance(node, dict):\n", "            if \"activity\" in node:\n", "                tree.append(f\"{level * '  '}{node['activity']}\")\n", "                level += 1\n", "            for k, v in node.items():\n", "                traverse(v, level)\n", "        if isinstance(node, list):\n", "            for v in node:\n", "                traverse(v, level)\n", "    traverse(obj)\n", "    return \"\\n\".join(tree)\n", "\n", "activity_tree_matches = collections.defaultdict(list)\n", "# for path, datapoint in list(tqdm.tqdm(datapoints.items()))[:5]:\n", "#     matches[get_hash(datapoint)].append(path)\n", "with multiprocessing.Pool(32) as pool:\n", "    for path, h in zip(datapoints.keys(), tqdm.tqdm(pool.imap(get_hash, datapoints.values()), total=len(datapoints)), strict=False):\n", "        activity_tree_matches[h].append(path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Minhash by groups"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "from services.studio._text_to_workflow.utils import minhash\n", "\n", "n_hashes = 25\n", "# threshold = 0.75\n", "# threshold = 0.9\n", "threshold = 0.95\n", "\n", "def datapoint_to_minhash_representation(datapoint):\n", "    representation = {}\n", "    if \"trigger\" in datapoint[\"process\"]:\n", "        representation[\"trigger\"] = copy.deepcopy(datapoint[\"process\"][\"trigger\"])\n", "    representation[\"workflow\"] = copy.deepcopy(datapoint[\"process\"][\"workflow\"])\n", "    return yaml_dump(representation)\n", "\n", "def find_duplicates(args):\n", "    _hash, datapoints_subset = args\n", "    paths = list(datapoints_subset.keys())\n", "    if len(datapoints_subset) < 2:\n", "        return _hash, paths, {}\n", "    dp_representation = [*map(datapoint_to_minhash_representation, datapoints_subset.values())]\n", "    shingles = minhash.create_shingles(dp_representation, paths)\n", "    _, dupes_to_scores = minhash.find_duplicates(dp_representation, paths, shingles, n_hashes, threshold)\n", "    return _hash, paths, dupes_to_scores\n", "\n", "def iterator():\n", "    for _h, paths in activity_tree_matches.items():\n", "        if len(paths) < 2:\n", "            continue\n", "        yield _h, {path: datapoints[path] for path in paths}\n", "\n", "duplicates = {}\n", "with (\n", "    multiprocessing.Pool(32) as pool,\n", "    tqdm.tqdm(total=len(datapoints)) as pbar,\n", "):\n", "    for _hash, paths, dupes_to_scores in pool.imap_unordered(find_duplicates, iterator()):\n", "        pbar.update(len(paths))\n", "        duplicates[_hash] = dupes_to_scores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.paths import get_workdir_path\n", "\n", "pickle.dump(duplicates, open(get_workdir_path() / \"minhash\" / \"duplicates.pkl\", \"wb\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "from services.studio._text_to_workflow.utils.paths import get_workdir_path\n", "\n", "# duplicates = pickle.load(open(\"duplicates.pkl\", \"rb\"))\n", "duplicates = pickle.load(open(get_workdir_path() / \"minhash\" / \"duplicates_minhash_25_0.9.pkl\", \"rb\"))\n", "\n", "duplicates = {k: v for k, v in duplicates.items() if len(activity_tree_matches[k]) > 1}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filter by datapoints\n", "duplicates = {k: {frozenset(p for p in paths if p in datapoints): score for paths, score in v.items()} for k, v in duplicates.items()}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# maybe there are no more duplicates in some cases\n", "duplicates = {k: {paths: score for paths, score in v.items() if len(paths) > 1} for k, v in duplicates.items()}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(duplicates)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_dupes = {h: sum(len(paths) for paths, score in dupes.items()) for h, dupes in duplicates.items()}\n", "total_unique_dupes = {h: len(set.union(set(), *(set(paths) for paths, score in dupes.items()))) for h, dupes in duplicates.items()}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sum(total_unique_dupes.values()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check for least disimilar similar pairs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, (_hash, dupes) in enumerate(sorted(duplicates.items(), key=lambda k: total_dupes[k[0]], reverse=True)):\n", "    print(f\"Group {i} ({total_unique_dupes[_hash]} / {len(activity_tree_matches[_hash])}):\")\n", "    # for j, (paths, score) in enumerate(sorted(dupes.items(), key=lambda k: k[1], reverse=True)):\n", "    for j, (paths, score) in enumerate(sorted(dupes.items(), key=lambda k: k[1])):\n", "        print(f\"  Sugroup {i}.{j} ({score:.2f}) ({len(paths)}):\")\n", "        if j >= 10:\n", "            break\n", "        for path in sorted(paths):\n", "            print(f\"    {path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Perform filtration by minhash deduplication"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, (_hash, dupes) in enumerate(sorted(duplicates.items(), key=lambda k: total_unique_dupes[k[0]], reverse=True)):\n", "    print(f\"Group {i} ({total_unique_dupes[_hash]} / {len(activity_tree_matches[_hash])}):\")\n", "    remaining_paths = set(activity_tree_matches[_hash])\n", "    for j, (paths, score) in enumerate(sorted(dupes.items(), key=lambda k: k[1], reverse=True)):\n", "        remaining_paths -= set(sorted(paths)[1:])\n", "    remaining_paths = sorted(remaining_paths)\n", "    print(f\"Remaining ({len(remaining_paths)}):\")\n", "    for path in remaining_paths:\n", "        print(f\"  {path}\")\n", "    print(f\"Deduped ({len(activity_tree_matches[_hash]) - len(remaining_paths)}):\")\n", "    for path in sorted(set(activity_tree_matches[_hash]) - set(remaining_paths)):\n", "        print(f\"  {path}\")\n", "    for path in set(activity_tree_matches[_hash]) - set(remaining_paths):\n", "        del datapoints[path]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Dump new filtered dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(datapoints)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.deduplicate import flush_datapoints\n", "\n", "# flush_datapoints(root_path, datapoints, \"deduplicated_part2_v1_minhash_25_0.9\")\n", "flush_datapoints(root_path, datapoints, \"deduplicated_part2_v2_minhash_25_0.9\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Group by UIA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.deduplicate import load_datapoints\n", "\n", "# root_path = pathlib.Path(\"/workspace/data/workflows/windows_20240805_deduplicated_part2_v2_minhash_25_0.9\")\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_20240805_deduplicated_part2\")\n", "datapoints = load_datapoints(root_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check how many have UI Automation activities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump\n", "\n", "datapoints_uia = {}\n", "datapoints_nonuia = {}\n", "\n", "def _is_uia(args):\n", "    path, datapoint = args\n", "    yaml_serialization = yaml_dump(datapoint)\n", "    return path, datapoint, \"activity: UiPath.UIAutomation\".lower() in yaml_serialization.lower()\n", "\n", "with multiprocessing.Pool(32) as pool:\n", "    for path, datapoint, is_uia in tqdm.tqdm(\n", "        pool.imap_unordered(_is_uia, list(datapoints.items())),\n", "        total=len(datapoints)\n", "    ):\n", "        if is_uia:\n", "            datapoints_uia[path] = datapoint\n", "        else:\n", "            datapoints_nonuia[path] = datapoint\n", "\n", "print(len(datapoints))\n", "print(len(datapoints_uia))\n", "print(len(datapoints_nonuia))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "path = pathlib.Path(\"/workspace/data/workflows/windows_20240805_deduplicated_part2_v2_minhash_25_0.9_api/00/00ffac21-abe1-c43a-3680-bad05784a905_DemandTools_ImportRecords.yaml\")\n", "is_uia((path, yaml_load(path)))[-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.deduplicate import flush_datapoints\n", "\n", "flush_datapoints(root_path, datapoints_uia, suffix=\"uia\")\n", "flush_datapoints(root_path, datapoints_nonuia, suffix=\"api\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deduplicate for portable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts import deduplicate\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_20240805\")\n", "deduplicate.hard_deduplicate(root_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_path = pathlib.Path(\"/workspace/data/workflows/portable_20240805_deduplicated_part1\")\n", "new_path = deduplicate.soft_deduplicate(root_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts import deduplicate\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_20240805_deduplicated_part2\")\n", "deduplicate.group_uia(root_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Use summarization to generate a query and plan of the workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "\n", "dotenv.load_dotenv(override=True)\n", "\n", "import pathlib\n", "\n", "from services.studio._text_to_workflow.activity_summary import activity_summary_endpoint\n", "from services.studio._text_to_workflow.autopilot_dataset.wfgen_generate_metadata import _infer_summarization_for_metadata\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "activity_summary_endpoint.init()\n", "\n", "datapoint_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test/StudioDesktop_fixes_20240408_[Confluence] Copy files from Google Drive to OneDrive.yaml\")\n", "\n", "# this does not work if called from the notebook\n", "await _infer_summarization_for_metadata(\"\", {}, yaml_load(datapoint_path))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Include prod filtered data from labelstudio to the dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load prod dataset index for reverse lookup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_index = {}\n", "for data_path in [\n", "    # pathlib.Path(\"/workspace/data/workflows/portable_20240805_dedup_grouped\"),\n", "    # pathlib.Path(\"/workspace/data/workflows/windows_20240805_dedup_grouped\"),\n", "    pathlib.Path(\"/workspace/data/workflows/from_canh/windows_prod_with_summarization\"),\n", "]:\n", "    for path in data_path.rglob(\"*.yaml\"):\n", "        # name = path.name.split(\"_\", 1)[0]\n", "        name = path.name\n", "        if name in dataset_index:\n", "            print(f\"Duplicate key {name}\")\n", "        dataset_index[name] = path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load labelstudio data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "labelstudio_data_path = pathlib.Path(\"/workspace/data/workflows/pii-labelstudio/project-7-at-2024-11-11-10-09-33d165a2.json\")\n", "\n", "labelstudio_data = json.load(labelstudio_data_path.open())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(json.dumps(labelstudio_data[0], indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify depth of converted folder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["converted_dataset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/Converted\")\n", "for path in converted_dataset_path.rglob(\"*\"):\n", "    if path.is_file():\n", "        if len(path.relative_to(converted_dataset_path).parts) != 3:\n", "            print(path.relative_to(converted_dataset_path))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dump datapoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.walkers import WorkflowThoughtsModifier\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.paths import get_converted_dataset_path\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_display_names\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "# converted_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/Converted/Windows/_prod_masked\")\n", "# converted_path.mkdir(exist_ok=True)\n", "converted_path = get_converted_dataset_path(\"Windows\")\n", "\n", "for datapoint in labelstudio_data:\n", "    yaml_data = yaml_load(datapoint[\"data\"][\"text\"])\n", "    name = datapoint[\"data\"][\"name\"]\n", "\n", "    assert len(datapoint[\"annotations\"]) == 1, \"Multiple annotations\"\n", "    annotation = datapoint[\"annotations\"][0]\n", "    no_pii = annotation[\"result\"][0][\"value\"][\"choices\"] == [\"NO (NO PII)\"]\n", "    if not no_pii:\n", "        print(f\"Skipping. Has PII. {name}\")\n", "        continue\n", "\n", "    # filename = \"{}_{}.yaml\".format(name.split(\"_\", 1)[0], yaml_data['name'])\n", "    filename = name\n", "    yaml_data = yaml_load(dataset_index[filename])\n", "    # yaml_data[\"original-filename\"]\n", "\n", "    workflow_obj = Workflow(\"\", \"\", yaml_data[\"process\"])\n", "    WorkflowThoughtsModifier(get_display_names(yaml_data[\"generated-plan-descriptions\"])).replace(workflow_obj)\n", "    workflow_lmyaml = workflow_obj.lmyaml()\n", "\n", "    # mock for converted dataset\n", "    out_dir: pathlib.Path = converted_path / f\"_Prod_filtered_{name}\"\n", "    out_dir.mkdir()\n", "    (out_dir / \"Main.yaml\").open(\"w\").write(workflow_lmyaml)\n", "    json.dump(\n", "        {\n", "            \"Success\": True,\n", "            \"result\": workflow_lmyaml,\n", "            \"Errors\": [],\n", "            \"jitCommands\": None\n", "        },\n", "        (out_dir / \"Main.json\").open(\"w\"),\n", "        indent=2,\n", "    )\n", "    json.dump(\n", "        {\n", "            \"targetFramework\": \"Windows\",\n", "            \"description\": yaml_data[\"query_refine\"],\n", "            \"name\": name.split(\"_\", 1)[1],\n", "        },\n", "        (out_dir / \"project.json\").open(\"w\"),\n", "        indent=2,\n", "    )\n", "\n", "# json.dump({\"targetFramework\": \"Windows\"}, (converted_path / \"project.json\").open(\"w\"), indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}