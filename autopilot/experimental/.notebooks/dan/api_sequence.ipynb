{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# dataset_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/train\")\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset\n", "# dataset = workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"train\")\n", "# dataset = workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"static\")\n", "# dataset = workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"uia\")\n", "# dataset = workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"test\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Cut workflow plan for sequence generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import re\n", "\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "from services.studio._text_to_workflow.workflow_generation.config import constants\n", "\n", "random.seed(42)\n", "\n", "for dataset in [\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"train\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"static\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"uia\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"test\"),\n", "]:\n", "    for path, datapoint in dataset.items():\n", "        # if path != pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/train/StudioDesktop_IFWhileElse_WHILE.yaml\"):\n", "        #     continue\n", "        # if path != pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/train/StudioDesktop_fixes_20240411_[<PERSON>] Send Slack direct message via UserName.yaml\"):\n", "        #     continue\n", "\n", "        lmyaml = datapoint[\"process\"]\n", "        plan = datapoint[\"plan\"]\n", "        description = datapoint[\"description\"]\n", "\n", "        wf = Workflow(description, plan, lmyaml)\n", "        plan2 = PlanBuilder().build(wf)\n", "        if plan != plan2:\n", "            print(f\"Plan mismatch for {path}\")\n", "            print(f\"Original: {plan}\")\n", "            print(f\"Rebuilt: {plan2}\")\n", "            print()\n", "\n", "        for _ in range(10):\n", "            plan_steps = plan.splitlines()\n", "            # print(plan)\n", "            starting_step = random.choice(plan.splitlines())\n", "            starting_step_idx = plan_steps.index(starting_step)\n", "\n", "            \"\"\"for step 3.1.2 it's 3.1. and for 2. it is empty\"\"\"\n", "            starting_step_prefix_category_match = re.search(r\"(?P<prefix>(?P<indentation>\\s*)(\\d+\\.)*)\\d+\\.\\s\", starting_step)\n", "            starting_step_prefix_category = starting_step_prefix_category_match.group(\"prefix\")\n", "            starting_step_indentation = starting_step_prefix_category_match.group(\"indentation\")\n", "            # print(f\"|{starting_step_prefix_category}|\")\n", "\n", "            \"\"\"find suitable candidates for the ending step for cutting\"\"\"\n", "            if not starting_step_prefix_category:\n", "                following_steps = [step for step in plan_steps[starting_step_idx:] if not step.startswith(\" \")]  # root indentation level\n", "            else:\n", "                following_steps = [\n", "                    step for step in plan_steps[starting_step_idx:] if step.startswith(starting_step_prefix_category)\n", "                ]  # same category i.e. for 3.2.1, all steps with 3.2.X\n", "            ending_step = random.choice(following_steps)\n", "            ending_step_idx = plan_steps.index(ending_step)\n", "            # display(following_steps)\n", "\n", "            # print(f\"{starting_step=}\")\n", "            # print(f\"{ending_step=}\")\n", "\n", "            \"\"\"do the cutting\"\"\"\n", "            starting_step_prefix = re.search(r\"\\s*(?P<prefix>(\\d+\\.)*)\\s\", starting_step).group(\"prefix\")\n", "            ending_step_prefix = re.search(r\"\\s*(?P<prefix>(\\d+\\.)*)\\s\", ending_step).group(\"prefix\")\n", "            while ending_step_idx + 1 < len(plan_steps) and plan_steps[ending_step_idx + 1].lstrip().startswith(ending_step_prefix):\n", "                ending_step_idx += 1\n", "\n", "            # print(starting_step_idx, ending_step_idx)\n", "            # print(plan_steps[starting_step_idx: ending_step_idx + 1])\n", "\n", "            prefix_re = re.compile(r\"(?P<indentation>\\s*)(?P<prefix>(\\d+\\.)+)\\s\")\n", "\n", "            numbering_index = starting_step_indentation.count(\"  \")\n", "            # print(f\"{numbering_index=}\")\n", "            prefixes = [prefix_re.search(step).group(\"prefix\") for step in plan_steps[starting_step_idx : ending_step_idx + 1]]\n", "            # print(prefixes)\n", "            numbering_values = [int(prefix[:-1].split(\".\")[numbering_index]) for prefix in prefixes]\n", "            # print(numbering_values)\n", "            starting_numbering = min(numbering_values)\n", "            ending_numbering = max(numbering_values)\n", "\n", "            # print(f\"{starting_step_prefix=}\")\n", "            # print(f\"{ending_step_prefix=}\")\n", "            new_plan_steps = plan_steps[:]\n", "\n", "            # renumbering\n", "            to_modify_steps, index = [], ending_step_idx + 1\n", "            while index < len(plan_steps) and plan_steps[index].startswith(starting_step_indentation):  # has at least the same indentation\n", "                to_modify_steps.append(plan_steps[index])\n", "                index += 1\n", "\n", "            # display(to_modify_steps)\n", "\n", "            def reorder(steps, delta, pivot_indentation):\n", "                n_indentation = pivot_indentation.count(\"  \")\n", "\n", "                def subtract_prefix(match):\n", "                    indentation = match.group(\"indentation\")\n", "                    prefix = match.group(\"prefix\")\n", "                    # print(step)\n", "                    # print(f\"{prefix=}\")\n", "                    prefix_numbers = [*map(int, prefix[:-1].split(\".\"))]\n", "                    prefix_numbers[n_indentation] -= delta\n", "                    return f\"{indentation}{'.'.join(map(str, prefix_numbers))}. \"\n", "\n", "                reordered_steps = []\n", "                for i, step in enumerate(steps):\n", "                    new_step = re.sub(r\"(?P<indentation>\\s*)(?P<prefix>(\\d+\\.)+)\\s\", subtract_prefix, step)\n", "                    reordered_steps.append(new_step)\n", "                return reordered_steps\n", "\n", "            new_plan_steps[ending_step_idx + 1 : index] = reorder(\n", "                to_modify_steps, delta=ending_numbering - starting_numbering + 1, pivot_indentation=starting_step_indentation\n", "            )\n", "\n", "            plan_cutout_original = new_plan_steps[starting_step_idx : ending_step_idx + 1]\n", "            # print(\"Original\")\n", "            # print(\"\\n\".join(plan_cutout_original))\n", "            # print(f\"{starting_step_idx=}, {ending_step_idx=}\")\n", "\n", "            # print(plan_cutout_original)\n", "            # print('aa')\n", "            plan_cutout_prefixes = [prefix_re.search(step).group(\"prefix\") for step in plan_cutout_original]\n", "            plan_cutout_numbering_index = prefix_re.search(plan_cutout_original[0]).group(\"indentation\").count(\"  \")\n", "            plan_cutout_numbering_values = [int(prefix[:-1].split(\".\")[plan_cutout_numbering_index]) for prefix in plan_cutout_prefixes]\n", "\n", "            plan_cutout_without_indentation = [step[plan_cutout_numbering_index * 2 :] for step in plan_cutout_original]\n", "\n", "            def remove_lead(match):\n", "                prefix = \".\".join(match.group(\"prefix\")[:-1].split(\".\")[plan_cutout_numbering_index:])\n", "                return f\"{match.group('indentation')}{prefix}. \"\n", "\n", "            plan_cutout_without_leading = [prefix_re.sub(remove_lead, step) for step in plan_cutout_without_indentation]  # \"  3.2.1. \" -> \"  2.1.\"\n", "            plan_cutout_steps = reorder(plan_cutout_without_leading, delta=min(plan_cutout_numbering_values) - 1, pivot_indentation=\"\")  # \"  2.1\" -> \"  1.1\"\n", "            plan_cutout = \"\\n\".join(plan_cutout_steps)\n", "\n", "            new_plan_steps[starting_step_idx : ending_step_idx + 1] = [f\"{starting_step_indentation}{constants.SEQUENCE_GENERATION_INSERTION_PHRASE}\"]\n", "            new_plan = \"\\n\".join(new_plan_steps)\n", "            # print(new_plan)\n", "\n", "            if len(plan_steps) < 2 or len(plan_steps) > len(plan_cutout_steps) >= 0.5 * len(plan_steps):\n", "                break\n", "            # print(new_plan)\n", "            # print('-' * 10)\n", "            # print(plan_cutout)\n", "            # print(\"Trying again\")\n", "            # print()\n", "\n", "        print(plan)\n", "        print(\"-\" * 10)\n", "        print(new_plan)\n", "        print(\"-\" * 10)\n", "        print(plan_cutout)\n", "        print(\"\\n\" * 3)\n", "\n", "        ordered_datapoint = {}\n", "        for key, value in datapoint.items():\n", "            if key in {\"start_cut\", \"end_cut\", \"plan_cut\", \"plan_cutout\"}:\n", "                continue\n", "            if key == \"plan\":\n", "                ordered_datapoint[\"start_cut\"] = starting_step_prefix\n", "                ordered_datapoint[\"end_cut\"] = ending_step_prefix\n", "                ordered_datapoint[\"plan_cut\"] = multiline_str(new_plan)\n", "                ordered_datapoint[\"plan_cutout\"] = multiline_str(plan_cutout)\n", "                ordered_datapoint[\"plan\"] = multiline_str(value)\n", "                continue\n", "            ordered_datapoint[key] = value\n", "\n", "        # continue\n", "        # raise\n", "        # break\n", "\n", "        # print(yaml_dump(ordered_datapoint))\n", "\n", "        yaml_dump(ordered_datapoint, path)\n", "\n", "        # break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Postprocessing method for integrating the partial plan generation into the cut plan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "from services.studio._text_to_workflow.workflow_generation.config import constants\n", "\n", "\n", "def postprocess_sequence_generation(result: str, existing_plan: str) -> str:\n", "    indentation_re = re.compile(r\"^\\s*\")\n", "\n", "    # strip result of global indentation\n", "    result_indentations = [indentation_re.search(line).group() for line in result.splitlines()]\n", "    result_global_indentation = min(result_indentations, key=len)\n", "    result_steps = [line[len(result_global_indentation) :] for line in result.splitlines()]\n", "\n", "    # find the insertion point\n", "    plan_steps = existing_plan.splitlines()\n", "    # for insertion_index, step in enumerate(plan_steps):\n", "    #     if step.lstrip() == constants.SEQUENCE_GENERATION_INSERTION_PHRASE:\n", "    #         break\n", "    # else:\n", "    #     raise ValueError(\"No split locus found\")\n", "    insertion_index = [i for i, step in enumerate(plan_steps) if step.lstrip() == constants.SEQUENCE_GENERATION_INSERTION_PHRASE][0]\n", "\n", "    # insert the new steps\n", "    insertion_index_indentation = indentation_re.search(plan_steps[insertion_index]).group()\n", "    plan_steps[insertion_index : insertion_index + 1] = [f\"{insertion_index_indentation}{step}\" for step in result_steps]\n", "\n", "    # renumber the plan\n", "    new_plan_steps = []\n", "    step_content_re = re.compile(r\"\\s*(\\d+\\.)+\\s(?P<content>.*)\")\n", "    counters, previous_indentation = [0], \"\"\n", "    for step in plan_steps:\n", "        indentation = indentation_re.search(step).group()\n", "        if len(indentation) > len(previous_indentation):\n", "            counters.append(1)\n", "        elif len(indentation) < len(previous_indentation):\n", "            counters.pop()\n", "\n", "        counters[-1] += 1\n", "        content = step_content_re.search(step).group(\"content\")\n", "        prefix = \".\".join(map(str, counters))\n", "\n", "        print(counters)\n", "        new_plan_steps.append(f\"{indentation}{prefix}. {content}\")\n", "        previous_indentation = indentation\n", "\n", "    # print(\"\\n\".join(plan_steps))\n", "    # print('-' * 30)\n", "    # print(\"\\n\".join(new_plan_steps))\n", "\n", "    return \"\\n\".join(new_plan_steps)\n", "\n", "\n", "dummy_result = \"\"\"\\\n", "1. If the file extension is PDF\n", "  1.1. Read text from PDF\n", "  1.2. Print Text Found in PDF Message\"\"\"\n", "\n", "dummy_plan = f\"\"\"\\\n", "1. For each email from Outlook folder\n", "  1.1. Download attachments from Outlook email\n", "  1.2. For each downloaded attachment\n", "    1.2.1. Check the attachment file extension\n", "      {constants.SEQUENCE_GENERATION_INSERTION_PHRASE}\"\"\"\n", "\n", "dummy_result = \"\"\"\\\n", "1. Get all files in Google Drive folder\"\"\"\n", "\n", "dummy_plan = f\"\"\"\\\n", "1. Find Google Drive folder\n", "{constants.SEQUENCE_GENERATION_INSERTION_PHRASE}\n", "2. For Each File in Google Drive folder\n", "  2.1. If the current item is a file\n", "    2.1.1. Upload the file to OneDrive folder\"\"\"\n", "\n", "postprocess_sequence_generation(dummy_result, dummy_plan)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Splitting the actual workflow using a visitor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import copy\n", "import random\n", "\n", "import typing_extensions as t\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Activity, Workflow\n", "\n", "from services.studio._text_to_workflow.activity_config import activity_config_dataset\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset\n", "from services.studio._text_to_workflow.workflow_generation.config import constants\n", "\n", "raise DeprecationWarning\n", "\n", "\n", "class WorkflowSequenceExtractor:\n", "    \"\"\"Sequencer for workflows. It will traverse the activity tree\"\"\"\n", "\n", "    def __init__(self):\n", "        self.placeholder_activity_lmyaml = {\"thought\": constants.SEQUENCE_GENERATION_INSERTION_PHRASE, \"activity\": \"System.Activities.Statements.Sequence\"}\n", "        self.counter = None\n", "\n", "    def _iterate_activities(self, activity: Activity, parent: list[Activity]) -> t.Iterator[tuple[Activity, list[Activity]]]:\n", "        \"\"\"This should go in the visitor\"\"\"\n", "        yield activity, parent\n", "        for sequence in activity.activities.values():\n", "            for child_activity in sequence:\n", "                yield from self._iterate_activities(child_activity, sequence)\n", "        for sequence in activity.activity_delegates.values():\n", "            for child_activity in sequence:\n", "                yield from self._iterate_activities(child_activity, sequence)\n", "\n", "    def iterate_activites(self, workflow: Workflow) -> t.Iterator[tuple[Activity, list[Activity]]]:\n", "        # do not iterate on the trigger for this extraction\n", "        for activity in workflow.activities:\n", "            yield from self._iterate_activities(activity, workflow.activities)\n", "\n", "    def _extract_sequence_from_parent_index_range(self, start: int, end: int, parent: list[Activity], activity_for_metadata: Activity):\n", "        sequence = copy.deepcopy(parent[start : end + 1])\n", "        placeholder_activity = Activity(self.placeholder_activity_lmyaml, activity_for_metadata.workflow_arguments, activity_for_metadata.workflow_variables)\n", "        placeholder_activity.is_current_activity = True  # this is necessary to obtain variables up to this point\n", "        parent[start : end + 1] = [placeholder_activity]\n", "        return sequence\n", "\n", "    def _extract_sequence_including_activity(self, activity: Activity, parent: list[Activity]):\n", "        \"\"\"This modifies the parent\"\"\"\n", "        index = [i for i, a in enumerate(parent) if a is activity][0]\n", "        start = random.randint(0, index)\n", "        end = random.randint(index, len(parent) - 1)\n", "        start_activity, end_activity = parent[start], parent[end]\n", "\n", "        sequence = self._extract_sequence_from_parent_index_range(start, end, parent, activity)\n", "        return sequence, start_activity, end_activity\n", "\n", "    def _extract_sequence_from_activity_range(self, start_activity: Activity, end_activity: Activity, parent: list[Activity]):\n", "        \"\"\"This modifies the parent\"\"\"\n", "        start = [i for i, a in enumerate(parent) if a is start_activity][0]\n", "        end = [i for i, a in enumerate(parent) if a is end_activity][0]\n", "        sequence = self._extract_sequence_from_parent_index_range(start, end, parent, start_activity)\n", "        return sequence\n", "\n", "    def create_sequence(self, workflow: Workflow, cut_indices: tuple[int, int] | None = None):\n", "        \"\"\"Generate a random cut or cut a specific range from the workflow\"\"\"\n", "        cut_workflow = copy.deepcopy(workflow)\n", "        activities_and_parents = list(self.iterate_activites(cut_workflow))\n", "        if cut_indices is None:\n", "            activity_to_remove, activity_parent = random.choice(activities_and_parents)\n", "            sequence, start_activity, end_activity = self._extract_sequence_including_activity(activity_to_remove, activity_parent)\n", "            start_cut = [i for i, (a, p) in enumerate(activities_and_parents) if a is start_activity][0]\n", "            end_cut = [i for i, (a, p) in enumerate(activities_and_parents) if a is end_activity][0]\n", "        else:\n", "            start_cut, end_cut = cut_indices\n", "            start_activity, parent = activities_and_parents[start_cut]\n", "            end_activity, parent2 = activities_and_parents[end_cut]\n", "            assert parent is parent2\n", "            sequence = self._extract_sequence_from_activity_range(start_activity, end_activity, parent)\n", "\n", "        # use only variables up to the placeholder activity\n", "        placeholder_activity_config = activity_config_dataset.WorkflowToActivityConfigConverter(False, cut_workflow.name, {}, {}).convert_current(cut_workflow)\n", "        cut_workflow.variables = {\n", "            variable[\"name\"]: variable\n", "            for variable in placeholder_activity_config[\"local_variables\"] + placeholder_activity_config[\"global_variables\"]\n", "            if variable[\"name\"] not in cut_workflow.arguments\n", "        }\n", "\n", "        sequence_workflow = copy.deepcopy(workflow)\n", "        sequence_workflow.trigger = None\n", "        sequence_workflow.activities = sequence\n", "        sequence_workflow.variables = copy.deepcopy(cut_workflow.variables)\n", "        # print(workflow.variables)\n", "        # print(cut_workflow.variables)\n", "        # raise\n", "        return cut_workflow, sequence_workflow, start_cut, end_cut\n", "\n", "    def _create_cutting_points_tree_from_sequence(self, activity_sequence: list[Activity]):\n", "        \"\"\"Helper function to create the cutting points tree from a sequence. Mutual recurrence with activites.\"\"\"\n", "        sequence_cutting_points = []\n", "        inner_cutting_points = []\n", "        for activity in activity_sequence:\n", "            sequence_cutting_points.append(self.counter)\n", "            inner_cutting_points.extend(self._create_cutting_points_tree_from_activity(activity))\n", "        return {\"cutting_points\": sequence_cutting_points, \"inner_cutting_points\": inner_cutting_points}\n", "\n", "    def _create_cutting_points_tree_from_activity(self, activity: Activity):\n", "        \"\"\"Helper function to create the cutting points tree from an activity. Mutual recurrence with sequences.\"\"\"\n", "        activity_cutting_points = []\n", "        self.counter += 1\n", "        for sequence in activity.activities.values():\n", "            activity_cutting_points.append(self._create_cutting_points_tree_from_sequence(sequence))\n", "        for sequence in activity.activity_delegates.values():\n", "            activity_cutting_points.append(self._create_cutting_points_tree_from_sequence(sequence))\n", "        return activity_cutting_points\n", "\n", "    def generate_cutting_points(self, workflow):\n", "        \"\"\"Generates the comprehensive list of cutting points for the workflow\"\"\"\n", "        self.counter = 0\n", "        cutting_points_tree = self._create_cutting_points_tree_from_sequence(workflow.activities)\n", "\n", "        queue = collections.deque([cutting_points_tree])\n", "        all_cutting_points = []\n", "        while queue:\n", "            node = queue.popleft()\n", "            cutting_points = node[\"cutting_points\"]\n", "            if not cutting_points:  # leaf node\n", "                continue\n", "            for i in range(len(cutting_points)):\n", "                for j in range(i, len(cutting_points))[::-1]:\n", "                    all_cutting_points.append((cutting_points[i], cutting_points[j]))\n", "            for child_node in node[\"inner_cutting_points\"]:\n", "                queue.append(child_node)\n", "        return all_cutting_points\n", "\n", "\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "workflow_generation_root_path = paths.get_autopilot_samples_dataset_path()\n", "temp_path = paths.get_autopilot_samples_dataset_path() / \"Experimental\" / \"SequenceGenerationWithTriggers\"\n", "\n", "# random.seed(42)\n", "metadata_index = []\n", "for dataset in [\n", "    # workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"train\"),\n", "    # workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"static\"),\n", "    # workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"uia\"),\n", "    # workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"test\"),\n", "    workflow_generation_dataset.load_workflow_generation_subset(\"Portable\", \"test\"),\n", "]:\n", "    for path, datapoint in dataset.items():\n", "        lmyaml = datapoint[\"process\"]\n", "        plan = datapoint[\"plan\"]\n", "        description = datapoint[\"description\"]\n", "        metadata_info = {\n", "            \"path\": path.relative_to(workflow_generation_root_path).as_posix(),\n", "            \"id\": path.stem,\n", "            \"query\": description,\n", "            \"plan\": multiline_str(plan),\n", "        }\n", "\n", "        wf = Workflow(description, plan, lmyaml)\n", "        plan2 = PlanBuilder().build(wf)\n", "        if plan != plan2:\n", "            print(f\"Different plans for {path}\")\n", "            print(f\"Original:\\n{plan}\\n\")\n", "            print(f\"Rebuilt:\\n{plan2}\\n\")\n", "            # continue\n", "        # continue\n", "\n", "        print(path)\n", "        print(plan)\n", "        cutting_points = WorkflowSequenceExtractor().generate_cutting_points(wf)\n", "        print(cutting_points)\n", "        # print(len(cutting_points))\n", "        # continue\n", "\n", "        sample_of_cutting_points = [0] + random.sample(range(1, len(cutting_points)), min(5, len(cutting_points) - 1))  # using range to maintain order\n", "        sample_of_cutting_points = [cutting_points[i] for i in sample_of_cutting_points]\n", "\n", "        # datapoint_dirpath = (path.parent / path.stem)\n", "        datapoint_dirpath = temp_path / path.stem\n", "        if datapoint_dirpath.exists():\n", "            import shutil\n", "\n", "            shutil.rmtree(datapoint_dirpath)\n", "        # continue\n", "        datapoint_dirpath.mkdir(exist_ok=False, parents=True)\n", "\n", "        metadata_info[\"cuts\"] = []\n", "        for i, cutting_point in enumerate(sample_of_cutting_points):\n", "            print(cutting_point)\n", "            wf_remaining, wf_sequence, start_cut, end_cut = WorkflowSequenceExtractor().create_sequence(wf, cutting_point)\n", "            plan_remaining = PlanBuilder().build(wf_remaining)\n", "            plan_sequence = PlanBuilder().build(wf_sequence)\n", "\n", "            print(plan)\n", "            print(\"-\" * 30)\n", "            print(plan_remaining)\n", "            print(\"-\" * 30)\n", "            print(plan_sequence)\n", "            print(\"\\n\" * 4)\n", "\n", "            ordered_datapoint = {}\n", "            for key, value in datapoint.items():\n", "                if key in {\"plan_existing\", \"plan_sequence\", \"process_existing\", \"process_sequence\"}:\n", "                    continue\n", "                if key == \"plan\":\n", "                    ordered_datapoint[\"plan_existing\"] = multiline_str(plan_remaining)\n", "                    ordered_datapoint[\"plan_sequence\"] = multiline_str(plan_sequence)\n", "                    ordered_datapoint[\"plan\"] = multiline_str(value)\n", "                    continue\n", "                if key == \"process\":\n", "                    ordered_datapoint[\"process_existing\"] = wf_remaining.to_dict()\n", "                    ordered_datapoint[\"process_sequence\"] = [activity.lmyaml() for activity in wf_sequence.activities]\n", "                    ordered_datapoint[\"process\"] = wf.to_dict()\n", "                    continue\n", "                ordered_datapoint[key] = value\n", "\n", "            start_cut, end_cut = cutting_point\n", "            dump_path = datapoint_dirpath / f\"{i:02}__{start_cut}_{end_cut}_{path.stem}.yaml\"\n", "            # dump_path = datapoint_dirpath / f\"{i:02}_{path.stem}.yaml\"\n", "            yaml_dump(ordered_datapoint, dump_path)\n", "\n", "            metadata_info[\"cuts\"].append(\n", "                {\n", "                    # \"path\": dump_path.as_posix(),\n", "                    # \"id\": dump_path.stem,\n", "                    \"query\": \"\",\n", "                    \"subplan\": multiline_str(plan_sequence),\n", "                    \"start\": start_cut,\n", "                    \"end\": end_cut,\n", "                }\n", "            )\n", "        metadata_index.append(metadata_info)\n", "\n", "        # for _ in range(10):\n", "        #     wf_remaining, wf_sequence, start_cut, end_cut = WorkflowSequenceExtractor().create_sequence(wf)\n", "        #     plan_remaining = PlanBuilder().build(wf_remaining)\n", "        #     plan_sequence = PlanBuilder().build(wf_sequence)\n", "\n", "        #     print(plan)\n", "        #     print('-' * 30)\n", "        #     print(plan_remaining)\n", "        #     print('-' * 30)\n", "        #     print(plan_sequence)\n", "        #     print('\\n' * 4)\n", "\n", "        #     n_steps_plan, n_steps_sequence = len(plan.splitlines()), len(plan_sequence.splitlines())\n", "        #     if n_steps_plan < 2 or 0.5 * n_steps_plan <= n_steps_sequence < n_steps_plan:\n", "        #         break\n", "        #     print('reattempting')\n", "\n", "        # ordered_datapoint = {}\n", "        # for key, value in datapoint.items():\n", "        #     if key in {\"plan_cut\", \"plan_cutout\", \"process_original\", \"process_cut\", \"process_cutout\"}:\n", "        #         continue\n", "        #     if key == \"plan\":\n", "        #         ordered_datapoint[\"plan_cut\"] = multiline_str(plan_remaining)\n", "        #         ordered_datapoint[\"plan_cutout\"] = multiline_str(plan_sequence)\n", "        #         ordered_datapoint[\"plan\"] = multiline_str(value)\n", "        #         continue\n", "        #     if key == \"process\":\n", "        #         ordered_datapoint[\"process_cut\"] = wf_remaining.to_dict()\n", "        #         ordered_datapoint[\"process_cutout\"] = [activity.lmyaml() for activity in wf_sequence.activities]\n", "        #         ordered_datapoint[\"process\"] = wf.to_dict()\n", "        #         continue\n", "        #     ordered_datapoint[key] = value\n", "\n", "    #     yaml_dump(ordered_datapoint, path)\n", "\n", "from services.studio._text_to_workflow.utils.paths import get_sequence_generation_dataset_path\n", "\n", "root_path = get_sequence_generation_dataset_path(\"Portable\")\n", "# metadata_file_path = root_path / \"metadata.yaml\"\n", "metadata_file_path = temp_path / \"metadata.yaml\"\n", "\n", "yaml_dump(metadata_index, metadata_file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# workflow_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/test/StudioDesktop_fixes_20240408_[Confluence] Extract table from PDF and save to Excel.yaml\"\n", "# workflow_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/train/StudioDesktop_DocumentUnderstanding_CreateClassificationValidationAction.yaml\"\n", "from services.studio._text_to_workflow.common.walkers import WorkflowSequenceExtractor\n", "\n", "workflow_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/test/StudioDesktop_fixes_20240408_[Confluence] Schedule Zoom Meeting in the first available Google Calendar slot next week.yaml\"\n", "lmyaml = yaml_load(open(workflow_path))\n", "wf = Workflow(\"\", \"\", lmyaml[\"process\"])\n", "print(PlanBuilder().build(wf))\n", "\n", "for i, (activity, parent) in enumerate(list(WorkflowSequenceExtractor().iterate_activites(wf))):\n", "    print(i, activity.display_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean root .yamls from sequence generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset\n", "\n", "while \"yes\" != input(\"Guarded deletion. Type 'yes' to proceed:\"):\n", "    continue\n", "\n", "for root_path in [\n", "    paths.get_sequence_generation_dataset_path(\"Portable\", \"train\"),\n", "    paths.get_sequence_generation_dataset_path(\"Portable\", \"static\"),\n", "    paths.get_sequence_generation_dataset_path(\"Portable\", \"uia\"),\n", "    paths.get_sequence_generation_dataset_path(\"Portable\", \"test\"),\n", "]:\n", "    for root_yaml_path in root_path.glob(\"*.yaml\"):\n", "        root_yaml_path.unlink()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Fix metadata file (sort + make paths relative)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "metadata_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/metadata.yaml\"\n", "metadata = yaml_load(open(metadata_path))\n", "for item in metadata:\n", "    item[\"path\"] = item[\"path\"].replace(\"Autopilot.Samples/Dataset/SequenceGeneration/Portable\", \"SequenceGeneration/Portable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add verification field for metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "# metadata_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/metadata.yaml\"\n", "metadata_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/metadata.yaml\"\n", "metadata = yaml_load(open(metadata_path))\n", "for item in metadata:\n", "    item[\"plan\"] = multiline_str(item[\"plan\"])\n", "    for i, cut in enumerate(item[\"cuts\"]):\n", "        new_cut = {}\n", "        new_cut[\"query\"] = cut[\"query\"]\n", "        new_cut[\"verified\"] = False\n", "        new_cut[\"subplan\"] = multiline_str(cut[\"subplan\"])\n", "        for k, v in cut.items():\n", "            if k not in {\"query\", \"subplan\"}:\n", "                new_cut[k] = v\n", "        item[\"cuts\"][i] = new_cut\n", "\n", "yaml_dump(metadata, open(metadata_path, \"w\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Testing workflow sequence generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# json_path = \"/workspace/data/workdir/sequence-generation/problematic_payload.json\"\n", "json_path = \"/workspace/data/workdir/sequence-generation/out_of_ctx_v3.json\"\n", "# json_path = \"/workspace/data/workdir/sequence-generation/out_of_ctx_v2.json\"\n", "# json_path = \"/workspace/data/workdir/sequence-generation/out_of_ctx.json\"\n", "request_data = json.load(open(json_path))\n", "\n", "localhost_server = \"http://localhost:5123/\"\n", "\n", "import requests\n", "\n", "result = requests.post(localhost_server + \"/generate-sequence\", json=request_data).json()\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(request_data[\"currentWorkflow\"])\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "yaml_dump(\n", "    {\n", "        \"description\": request_data[\"userRequest\"],\n", "        \"workflow\": yaml_load(request_data[\"currentWorkflow\"]),\n", "    },\n", "    open(\"/workspace/src/studio/text_to_workflow/tests/fixtures/ai_sequence_fixtures/missing_workflow.yaml\", \"w\"),\n", ")\n", "request_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add more synthetic activities to trigger the sequence generation limit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(request_data.keys())\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "workflow = yaml_load(request_data[\"currentWorkflow\"])\n", "workflow[\"workflow\"][1:2] = [copy.deepcopy(workflow[\"workflow\"][1]) for _ in range(5)]\n", "request_data[\"currentWorkflow\"] = yaml_dump(workflow)\n", "with open(\"/workspace/data/workdir/sequence-generation/out_of_ctx_v2.json\", \"w\") as fd:\n", "    json.dump(request_data, fd, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate some statistics for sequence generation metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "metadata_path = \"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Portable/metadata.yaml\"\n", "metadata = yaml_load(open(metadata_path))\n", "\n", "\n", "cuts_steps = collections.Counter()\n", "n_steps_percentage = []\n", "for item in metadata:\n", "    plan_steps = len(item[\"plan\"].splitlines()) - 1\n", "    # print(plan_steps)\n", "    # print(item[\"plan\"])\n", "    # print()\n", "    for cut in item[\"cuts\"]:\n", "        n_steps = len(cut[\"subplan\"].splitlines())\n", "        # print(n_steps, n_steps / plan_steps)\n", "        # print(cut[\"subplan\"])\n", "        # print()\n", "        # print(item[\"plan\"])\n", "        cuts_steps[n_steps] += 1\n", "        n_steps_percentage.append(n_steps / plan_steps)\n", "    # print('\\n' * 3)\n", "print(cuts_steps.most_common())\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "plt.hist(n_steps_percentage, bins=30)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prune workflow if too large"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datapoints = {}\n", "\n", "for dataset in [\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"train\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"static\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"uia\"),\n", "    workflow_generation_dataset.load_sequence_generation_subset(\"Portable\", \"test\"),\n", "]:\n", "    datapoints.update(dataset)\n", "    # dataset = list(itertools.chain(*(dataset.items() for dataset in datasets)))\n", "original_datapoints = datapoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "paths = [\n", "    # \"/workspace/data/workdir/sequence-generation/problematic_payload.json\",\n", "    # \"/workspace/data/workdir/sequence-generation/out_of_ctx_v3.json\",\n", "    # \"/workspace/data/workdir/sequence-generation/out_of_ctx_v4-left.json\",\n", "    \"/workspace/data/workdir/sequence-generation/out_of_ctx_v4-middle.json\",\n", "]\n", "\n", "\n", "def custom_load(path):\n", "    if path.endswith(\".json\"):\n", "        json_data = json.load(open(path, encoding=\"utf-8-sig\"))\n", "        # print(yaml_load(json_data[\"currentWorkflow\"]).keys())\n", "        datapoint = {\"process_existing\": yaml_load(json_data[\"currentWorkflow\"]), \"description\": json_data[\"userRequest\"]}\n", "        datapoint[\"plan_existing\"] = PlanBuilder().build(Workflow(\"\", \"\", datapoint[\"process_existing\"]))\n", "        return datapoint\n", "    return yaml_load(open(path))\n", "\n", "\n", "datapoints = {path: custom_load(path) for path in paths}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import copy\n", "import random\n", "\n", "import typing_extensions as t\n", "from services.studio._text_to_workflow.common.walkers import CollapseWorkflowSubsequences<PERSON><PERSON>er, PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Activity, Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset\n", "\n", "times = []\n", "\n", "# random.seed(42)\n", "for path, datapoint in datapoints.items():\n", "    if path.name == \"02__2_2_StudioDesktop_IFWhileElse_RETRYSCOPE.yaml\":\n", "        continue\n", "    lmyaml = datapoint[\"process_existing\"]\n", "    plan = datapoint[\"plan_existing\"]\n", "    description = datapoint[\"description\"]\n", "\n", "    wf = Workflow(description, plan, lmyaml)\n", "    plan2 = PlanBuilder().build(wf)\n", "    if plan != plan2 and path.parts[-2] != \"StudioDesktop_IFWhileElse_RETRYSCOPE\":\n", "        print(f\"Different plans for {path}\")\n", "        print(f\"Original:\\n{plan}\\n\")\n", "        print(f\"Rebuilt:\\n{plan2}\\n\")\n", "        raise\n", "\n", "    original_wf = copy.deepcopy(wf)\n", "\n", "    print(path)\n", "\n", "    import time\n", "\n", "    start = time.time()\n", "    for pruning_level in CollapseWorkflowSubsequencesPruner().iteratively_prune_workflow(wf):\n", "        x = wf.lmyaml()\n", "        # current_wf = copy.deepcopy(wf)\n", "\n", "        # print(pruning_level)\n", "        # print(PlanBuilder().build(current_wf))\n", "        # print(wf.l<PERSON>aml())\n", "        # print('\\n' * 4)\n", "    times.append(time.time() - start)\n", "    continue\n", "    # break\n", "\n", "print(times)\n", "import pandas as pd\n", "\n", "pd.Series(times).describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.walkers import WorkflowSequenceExtractor, WorkflowToActivityConfigConverter\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "datapoint_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/test/StudioDesktop_JSONSTUFF_DEJSON3.yaml\")\n", "datapoint = yaml_load(datapoint_path)\n", "plan = datapoint[\"plan\"]\n", "workflow = Workflow(datapoint[\"description\"], plan, datapoint[\"process\"])\n", "print(workflow.lmyaml())\n", "\n", "print(WorkflowToActivityConfigConverter(False, workflow.name, {}, {}).convert_current(workflow))\n", "\n", "print(\"\\n\" * 3)\n", "main_cut = WorkflowSequenceExtractor().generate_cutting_points(workflow)[0]\n", "cut_workflow, sequence_workflow, start_cut, end_cut = WorkflowSequenceExtractor().create_sequence(workflow, main_cut)\n", "print(cut_workflow.lmyaml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate invalid cut"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder, WorkflowSequenceExtractor\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "# datapoint_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/train/StudioDesktop_windows_templates_movefileandfolders_1.0.1_contentFiles_any_any_pt0_VisualBasic_GlobalHandlerX.yaml\")\n", "datapoint_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/train/StudioDesktop_ExceluriLipsa_CReatPivot.yaml\")\n", "datapoint = yaml_load(datapoint_path)\n", "\n", "print(datapoint[\"plan\"])\n", "\n", "workflow_object = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "cutting_points = WorkflowSequenceExtractor().generate_cutting_points(workflow_object)\n", "print(cutting_points)\n", "for i, j in cutting_points:\n", "    cut_workflow, sequence_workflow, start_cut, end_cut = WorkflowSequenceExtractor().create_sequence(workflow_object, (i, j))\n", "    plan_sequence = PlanBuilder(force_trigger_if_missing=False).build(cut_workflow)\n", "    print(plan_sequence, end=\"\\n\\n\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test various model options for api automation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.helpers import get_connections_data\n", "\n", "from services.studio._text_to_workflow.core.config import settings\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import GenerateWorkflowRequest\n", "\n", "req: GenerateWorkflowRequest = {\n", "    \"userRequest\": \"Output a Hello world message\",\n", "    \"connections\": [],\n", "    \"targetFramework\": \"Portable\",\n", "}\n", "workflow_generation_endpoint.init()\n", "\n", "# workflow_generation_endpoint.generate_workflow(req)\n", "# workflow_generation_endpoint.generate_workflow(req, planning_model_name='gpt35')\n", "# workflow_generation_endpoint.generate_workflow(req, planning_model_name='gpt4o')\n", "# workflow_generation_endpoint.generate_workflow(req, generation_model_name='gpt4o')\n", "# workflow_generation_endpoint.generate_workflow(req, planning_model_name='gpt35', generation_model_name='gpt4o')\n", "# workflow_generation_endpoint.generate_workflow(req, planning_model_name='sonnet')\n", "# workflow_generation_endpoint.generate_workflow(req, planning_model_name='haiku')\n", "# from services.studio._text_to_workflow.utils.request_utils import with_request_context\n", "\n", "# context = RequestContext(\n", "#     email=\"<EMAIL>\",\n", "#     first_name=\"UiPath\",\n", "#     last_name=\"Support\",\n", "#     planning_model=\"llm-gateway-claude-35-sonnet\",\n", "#     raw_jwt=settings.UIPATH_TOKEN,\n", "# )\n", "from services.studio._text_to_workflow.api.deps import get_context\n", "\n", "context = get_context(\n", "    raw_jwt=settings.UIPATH_TOKEN or \"\",\n", "    tenant_id=get_connections_data()[0],\n", "    planning_model=\"llm-gateway-claude-35-sonnet\",\n", ")\n", "# print(context)\n", "await workflow_generation_endpoint.generate_workflow(req, context=context)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fix current activity inside switch case"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.walkers import PlanBuilder\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "wf_string = \"\"\"workflow:\n", "- thought: \"Switch\"\n", "  displayName: \"Switch\"\n", "  activity: System.Activities.Statements.Switch<System.Int32>\n", "  params:\n", "    Expression: \"[[variable1]]\"\n", "    Cases:\n", "      1:\n", "      - thought: \"Sequence\"\n", "        displayName: \"Sequence\"\n", "        activity: System.Activities.Statements.Sequence\n", "        currentActivity: True\n", "      2: null\n", "    Default:\n", "    - thought: \"Log Message\"\n", "      displayName: \"Log Message\"\n", "      activity: UiPath.Core.Activities.LogMessage\n", "      params:\n", "        Level: \"Info\"\n", "        Message: \"[[variable1]]\"\n", "\"\"\"\n", "wf_object = Workflow(\"\", \"\", yaml_load(wf_string))\n", "print(wf_object.lmyaml(include_unknown_properties=False))\n", "print(PlanBuilder().build(wf_object))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sequence generation sampling for big workflows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The focus is to have sequences that we can control:\n", "- in the number of steps\n", "- in the activities that are present"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs\n", "\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset\n", "\n", "typedefs.load()\n", "dataset = workflow_generation_dataset.load_workflow_generation_dataset(\"Windows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import pathlib\n", "\n", "import numpy as np\n", "from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector, PlanBuilder, TestWorkflowParenthoodValidity, WorkflowPruner, WorkflowSequenceExtractor\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "# datapoints = dataset[\"train\"]\n", "paths = [\n", "    pathlib.Path(\"/workspace/src-visitors/studio/text_to_workflow/.notebooks/dan/013cdf52-8441-50c5-cbc3-cbf3e8594606_Manual_Trigger_Email_Transaction.yaml\")\n", "]\n", "datapoints = {path: yaml_load(open(path)) for path in paths}\n", "\n", "# for path, datapoint in :\n", "for path, datapoint in datapoints.items():\n", "    print(path)\n", "    print(datapoint[\"description\"])\n", "    print(datapoint[\"plan\"])\n", "    print()\n", "\n", "    workflow = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "\n", "    # prune workflow (default: remove comment out and collapse try catches)\n", "    WorkflowPruner().prune(workflow)\n", "    assert TestWorkflowParenthoodValidity().validate(workflow), f\"Invalid workflow {path}\"\n", "    # print(\"post pruning\")\n", "    # print(PlanBuilder().build(workflow))\n", "    # print()\n", "    # print(workflow.lmyaml())\n", "    # print()\n", "\n", "    # create all possible sequences from workflow\n", "    cutting_points = WorkflowSequenceExtractor().generate_cutting_points(workflow)\n", "    sequences_datapoints = [workflow_generation_dataset._seqgen_get_datapoint(datapoint, start, end, workflow) for start, end in cutting_points]\n", "    n_steps_sequence = [len(dp[\"plan_sequence\"].split(\"\\n\")) for dp in sequences_datapoints]\n", "    print(n_steps_sequence)\n", "\n", "    # filter by sequences that are between 1 and 10 steps long\n", "    sequences_datapoints = [dp for dp in sequences_datapoints if 1 <= len(dp[\"plan_sequence\"].split(\"\\n\")) <= 5]\n", "\n", "    # filter by ratio of sequence steps to original steps between 20% and 80%\n", "    # sequences_datapoints = [dp for dp in sequences_datapoints if 0.2 <= len(dp[\"plan_sequence\"].split(\"\\n\")) / len(dp[\"plan\"].split(\"\\n\")) <= 0.8]\n", "\n", "    # filter by heuristics regarding activities inside the sequence\n", "    filtered_sequences_datapoints = []\n", "    for dp in sequences_datapoints:\n", "        n_steps = len(dp[\"plan_sequence\"].split(\"\\n\"))\n", "        sequence = Workflow(dp[\"description\"], dp[\"plan_sequence\"], {\"workflow\": dp[\"process_sequence\"]})\n", "        sequence_activities = ActivitiesAndTriggersCollector().collect(sequence)[\"activity\"]\n", "        if n_steps > 1 and len(set(act.activity_id for act in sequence_activities)) < 2:\n", "            continue  # same activity\n", "        if sum(\"assign\" in act.activity_id.lower() for act in sequence_activities) / n_steps > 0.5:\n", "            continue  # > 50% assigns\n", "        if sum(\"logmessage\" in act.activity_id.lower() for act in sequence_activities) / n_steps > 0.5:\n", "            continue  # > 50% log messages\n", "        if sum(any(keyword in act.activity_id.lower() for keyword in [\"logmessage\", \"assign\"]) for act in sequence_activities) / n_steps > 0.7:\n", "            continue  # > 70% log messages or assigns\n", "        filtered_sequences_datapoints.append(dp)\n", "    sequences_datapoints = filtered_sequences_datapoints\n", "\n", "    # print sequences\n", "    TO_SAMPLE = 5\n", "    n_steps_sequence = [len(dp[\"plan_sequence\"].split(\"\\n\")) for dp in sequences_datapoints]\n", "    _probs = [n_steps / sum(n_steps_sequence) for n_steps in n_steps_sequence]\n", "    _probs = [p**0.8 for p in _probs]  # flatten the distribution a little bit\n", "    _probs = [p / sum(_probs) for p in _probs]  # normalize\n", "    _probs[-1] = 1 - sum(_probs[:-1])  # make sure they add up to 1\n", "\n", "    sequences_datapoints = [\n", "        sequences_datapoints[i]\n", "        for i in np.random.choice(  # need to use numpy here for sampling without replacement and weighting\n", "            len(sequences_datapoints),\n", "            min(TO_SAMPLE, len(sequences_datapoints)),\n", "            p=_probs,\n", "            replace=False,\n", "        )\n", "    ]\n", "\n", "    # generate query for the considered sequences\n", "    # for dp in sequences_datapoints:\n", "    #     lmyaml = {\"processName\": dp[\"process\"][\"processName\"], \"packages\": dp[\"process\"][\"packages\"], \"workflow\": dp[\"process_sequence\"]}\n", "    #     workflow = Workflow(dp[\"description\"], dp[\"plan_sequence\"], lmyaml)\n", "    #     query = await generate_from_production_data.prompt_llm_for_query(build_testing_request_context(), workflow.lmyaml(), workflow.to_dict(), False)\n", "    #     dp[\"description_sequence\"] = query\n", "\n", "    for i, dp in enumerate(sequences_datapoints):\n", "        print(dp[\"description\"])\n", "        print(dp[\"plan_sequence\"])\n", "        print()\n", "        # yaml_dump(dp, pathlib.Path(path.with_name(f\"{path.stem}-sequence-{i}.yaml\")))\n", "\n", "    print(\"\\n\" * 3)\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.sample_sequence_generation import sample\n", "\n", "await sample(\n", "    [pathlib.Path(\"/workspace/src-visitors/studio/text_to_workflow/.notebooks/dan/013cdf52-8441-50c5-cbc3-cbf3e8594606_Manual_Trigger_Email_Transaction.yaml\")]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reset the queries for the sequence generation dataset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "for target_framework in [\"Portable\", \"Windows\"]:\n", "    metadata_path = paths.get_sequence_generation_metadata_path(target_framework)\n", "    metadata_obj = yaml_load(metadata_path, load_multiline=True)\n", "    for metadatapoint in metadata_obj:\n", "        for cut_object in metadatapoint[\"cuts\"]:\n", "            cut_object[\"query\"] = \"\"\n", "    yaml_dump(metadata_obj, metadata_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import load_metadata_file\n", "\n", "sequence_generation_dataset_root_path = paths.get_sequence_generation_dataset_path()\n", "metadata_path = paths.get_sequence_generation_metadata_path(\"Portable\")\n", "metadata_wip_path = metadata_path.with_stem(\"metadata.wip\")\n", "metadata = load_metadata_file(metadata_path)\n", "i = 0\n", "for item_id, item in tqdm.tqdm(metadata.items(), \"Verify and generate queries\"):\n", "    if i == 597:\n", "        print(item_id)\n", "    item_dirpath = sequence_generation_dataset_root_path / item_id\n", "    i += 1\n", "\n", "metadata = yaml_load(metadata_wip_path, load_multiline=True)\n", "metadata = {item[\"id\"]: item for item in metadata}\n", "yaml_dump(list(metadata.values()), metadata_wip_path)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}