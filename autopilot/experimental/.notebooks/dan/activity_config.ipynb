{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "import requests\n", "\n", "from services.studio._text_to_workflow.core import settings\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workdir/_investigations/SRE-325538-trigger-current-activity\")\n", "\n", "for path in root_path.glob(\"*.json\"):\n", "    data = json.load(path.open())\n", "    if \"Internal Server Error\" not in data[\"response\"][\"error\"][\"message\"]:\n", "        continue\n", "    headers = {\n", "        \"Authorization\": settings.UIPATH_TOKEN,\n", "        \"X-UIPATH-Localization\": \"en, en\",\n", "    }\n", "    response = requests.post(\"http://localhost:5123/v2/configure-activity\", headers=headers, json=data[\"request\"])\n", "    print(response)\n", "    break\n", "    # print(data[\"response\"])\n", "    # print(data[\"request\"][\"targetFramework\"])\n", "    # print(data[\"request\"][\"workflow\"])\n", "    # print('-' * 80)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}