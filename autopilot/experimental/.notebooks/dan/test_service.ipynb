{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "problematic_request = json.loads(r\"\"\"{}\"\"\")\n", "\n", "request_pair_path = pathlib.Path(\"/workspace/src-visitors/studio/text_to_workflow/workflow_generation/PILOT-unk-seqgen-coroutine-exception/311f2304-9925-42f6-8b95-ba581ca7b294.json\")\n", "problematic_request = json.load(request_pair_path.open())[\"request\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "from services.studio._text_to_workflow.core import settings\n", "\n", "# tenant_id, connections = helpers.get_connections_data()\n", "# request_context = get_testing_request_context(localization=None, tenant_id=tenant_id)\n", "\n", "for _ in range(10):\n", "    response = requests.post(\n", "        \"http://localhost:5123/v2/generate-sequence\",\n", "        headers={\n", "            # \"X-UIPATH-Localization\": \"en, en\",\n", "            # \"X-UiPath-ClientName\": \"Studio\",\n", "            # \"X-UiPath-ClientVersion\": \"*********\",\n", "            # \"X-UiPath-License\": \"UiPathers\",\n", "            # \"X-UIPATH-TenantId\": tenant_id,\n", "            # \"X-UiPath-CorrelationId\": \"<correlation_id>\",\n", "            # \"X-UiPath-StudioProjectId\": \"<studio_project_id>\",\n", "            \"Authorization\": settings.UIPATH_TOKEN,\n", "        },\n", "        json=problematic_request\n", "    )\n", "    response.raise_for_status()\n", "    response.json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# the same but async\n", "import asyncio\n", "\n", "import aiohttp\n", "\n", "\n", "async def main():\n", "    async with aiohttp.ClientSession() as session:\n", "        tasks = []\n", "        for _ in range(10):\n", "            tasks.append(\n", "                session.post(\n", "                    \"http://localhost:5123/v2/generate-sequence\",\n", "                    headers={\n", "                        \"Authorization\": settings.UIPATH_TOKEN,\n", "                    },\n", "                    json=problematic_request\n", "                )\n", "            )\n", "        responses = await asyncio.gather(*tasks)\n", "        for response in responses:\n", "            response.raise_for_status()\n", "            await response.json()\n", "\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.json()['result'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(problematic_request['userRequest'])\n", "# print(problematic_request['currentWorkflow'])\n", "display(problematic_request[\"availableVariables\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test workflow generation endpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.helpers import get_connections_data\n", "\n", "from services.studio._text_to_workflow.api.deps import get_context\n", "from services.studio._text_to_workflow.core.config import settings\n", "from services.studio._text_to_workflow.utils.request_utils import set_request_context\n", "from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import GenerateWorkflowRequest\n", "\n", "req: GenerateWorkflowRequest = {\n", "    \"userRequest\": \"Output a Hello world message\",\n", "    \"connections\": [],\n", "    \"targetFramework\": \"Portable\",\n", "}\n", "workflow_generation_endpoint.init()\n", "\n", "context = get_context(\n", "    raw_jwt=settings.UIPATH_TOKEN or \"\",\n", "    tenant_id=get_connections_data()[0],\n", "    planning_model=\"llama3.1-8B-planning-finetuned\",\n", "    planning_model_custom_url=\"http://************:8008/v1/chat/completions\"\n", ")\n", "# print(context)\n", "set_request_context(context)\n", "await workflow_generation_endpoint.generate_workflow(req)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from services.studio._text_to_workflow.common.helpers import get_connections_data\n", "\n", "from services.studio._text_to_workflow.core.config import settings\n", "\n", "response = requests.post(\n", "    \"http://localhost:5123/v2/generate-workflow\",\n", "    # \"http://localhost:5123/v2/generate-workflow\",\n", "    # \"http://localhost:5123/v2/generate-workflow?planning_model=llama3.1-8B-planning-finetuned\",\n", "    headers={\n", "        # \"X-UIPATH-Localization\": \"en, en\",\n", "        # \"X-UiPath-ClientName\": \"Studio\",\n", "        # \"X-UiPath-ClientVersion\": \"*********\",\n", "        # \"X-UiPath-License\": \"UiPathers\",\n", "        \"X-UIPATH-TenantId\": get_connections_data()[0],\n", "        # \"X-UiPath-CorrelationId\": \"<correlation_id>\",\n", "        # \"X-UiPath-StudioProjectId\": \"<studio_project_id>\",\n", "        \"Authorization\": settings.UIPATH_TOKEN,\n", "        ### Custom headers ###\n", "        # \"planning_model\": \"llama3.1-8B-planning-finetuned\",\n", "        # \"planning_model\": \"llm-gateway-claude-35-sonnet-v2\",\n", "        # \"planning_model_custom_url\": \"http://************:8008/v1/chat/completions\"\n", "        # \"planning_model_custom_url\": \"http://************:8008/v1/chat/completions\"\n", "    },\n", "    json={\n", "        \"userRequest\": \"Output a Hello world message\",  # mandatory\n", "        \"targetFramework\": \"Portable\",  # mandatory\n", "        \"connections\": [],  # mandatory\n", "    }\n", ")\n", "response.raise_for_status()\n", "# response.json()\n", "print(response.json()[\"result\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test fix workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import requests\n", "from services.studio._text_to_workflow.common.helpers import get_connections_data\n", "\n", "from services.studio._text_to_workflow.core.config import settings\n", "\n", "response = requests.post(\n", "    \"http://localhost:5123/v2/fix-workflow\",\n", "    headers={\n", "        \"X-UIPATH-TenantId\": get_connections_data()[0],\n", "        \"Authorization\": settings.UIPATH_TOKEN,\n", "    },\n", "    json=json.load(open(\"test_fix_workflow.json\"))\n", ")\n", "response.raise_for_status()\n", "# response.json()\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test summarization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import requests\n", "from services.studio._text_to_workflow.common.helpers import get_connections_data\n", "\n", "from services.studio._text_to_workflow.core.config import settings\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "response = requests.post(\n", "    \"http://localhost:5123/v2/summarize-workflow\",\n", "    headers={\n", "        \"X-UIPATH-TenantId\": get_connections_data()[0],\n", "        \"Authorization\": settings.UIPATH_TOKEN,\n", "    },\n", "    json={\n", "        \"activity\": yaml_dump(\n", "            {\n", "                \"workflow\": yaml_load(\"\"\"\\\n", "- thought: ''\n", "  activity: UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity\n", "  uiPathActivityTypeId: 489bd332-d516-3ad9-b23f-b21bb4fd1a2b\n", "  params:\n", "    priority: '1'\n", "    status: '2'\n", "    description: '[[description]]'\n", "    email: '[[email]]'\n", "    Jit_tickets: '[[FreshDeskTicket]]'\n", "    out_id: '[[ticket_id]]'\n", "    subject: '[[summary]]'\"\"\")\n", "            }\n", "        )\n", "    },\n", ")\n", "response.raise_for_status()\n", "response.json()\n", "# print(response.json())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'result': {'activityDescriptions': [{'id': 'ConnectorActivity_6E2DE0',\n", "    'description': 'Create Freshdesk ticket'}],\n", "  'activitySummaries': [{'id': 'ConnectorActivity_6E2DE0',\n", "    'summary': 'Create a Freshdesk ticket with provided details'}],\n", "  'workflowSummary': 'Create a Freshdesk ticket with specified details',\n", "  'workflowFileName': 'Create_Freshdesk_Ticket',\n", "  'workflowShortDescription': 'Create Freshdesk Ticket with Details'},\n", " 'usage': {'model': 'gpt-4o-mini-2024-07-18',\n", "  'promptTokens': 3009,\n", "  'completionTokens': 84,\n", "  'totalTokens': 3093}}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}