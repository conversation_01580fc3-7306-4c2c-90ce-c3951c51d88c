{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# cwd = pathlib.Path('./output_planning_only').absolute()\n", "cwd = pathlib.Path(\"./output_full\").absolute()\n", "paths = list(cwd.iterdir())\n", "paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exclude = [\"gpt35\"]\n", "paths = [p for p in paths if all(k not in p.name for k in exclude)]\n", "paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "dataset = []\n", "for p in paths:\n", "    model_name = p.name\n", "    content = p.read_text()\n", "    preamble, *examples = re.split(r\"\\*\\*\\*\", content)\n", "    examples[-1], footer = re.split(r\"(?=<pre>\\nFULLY VALID COUNT)\", examples[-1], maxsplit=1)\n", "    for example in examples:\n", "        # print(example)\n", "\n", "        datapoint = {}\n", "\n", "        lines = example.splitlines()\n", "        query = lines[1]\n", "        # print(query)\n", "\n", "        def find_section(name):\n", "            regpat = r\"(?<=>{}</button>\\n<pre style=\\\"display:none\\\">).*?(?=</pre)\"\n", "            m = re.search(regpat.format(\"plan\"), example, re.DOTALL)\n", "            return m.group(0) if m is not None else None\n", "\n", "        def get_sections():\n", "            section_re = re.compile(r\">(?P<section>[^<]*?)</button>\\n<pre style=\\\"display:none\\\">(?P<content>[^<]*?)(?=</pre)\", re.DOTALL)\n", "            sections = {}\n", "            for match in section_re.finditer(example):\n", "                d = match.groupdict()\n", "                sections[d[\"section\"]] = d[\"content\"]\n", "            return sections\n", "\n", "        def get_scores():\n", "            score_re = re.compile(r\"^(?P<metric>\\w[\\w ]+)\\s(?P<value>\\d+\\.?\\d*)\", re.MULTILINE)\n", "            return {metric: float(value) for metric, value in score_re.findall(example)}\n", "\n", "        sections = get_sections()\n", "        scores = get_scores()\n", "        # print(query)\n", "        # import json\n", "        # print(json.dumps(scores, indent=2))\n", "\n", "        # planning_score = float(re.search(r'PLAN SCORE (\\d+\\.?\\d+)', example).group(1))\n", "\n", "        # print(sections.keys())\n", "        datapoint[\"plan\"] = sections[\"plan\"]\n", "        datapoint[\"name\"] = model_name\n", "        datapoint[\"query\"] = query\n", "        datapoint[\"ideal\"] = sections[\"ideal_plan\"]\n", "        datapoint.update(scores)\n", "        dataset.append(datapoint)\n", "\n", "        # print(find_section(\"plan\"))\n", "        # print(find_section(\"ideal_plan\"))\n", "        # print(example)\n", "\n", "        # break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for dp in dataset:\n", "    if \"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\" not in dp:\n", "        print(dp[\"name\"])\n", "        print(dp[\"query\"])\n", "    print(dp[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame(dataset)\n", "# print columns that have nan values\n", "# print(df.columns[df.isnull().any()])\n", "# drop these columns\n", "df = df.dropna(axis=1)\n", "df[\"name\"] = df[\"name\"].apply(lambda x: x.replace(\"output-full-\", \"\").replace(\".html\", \"\"))\n", "\n", "import collections\n", "\n", "for_df = collections.defaultdict(dict)\n", "for name, agg in df.groupby(\"name\"):\n", "    for_df[\"pted\"][name] = agg[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"].describe().drop(\"count\")\n", "    for_df[\"lev\"][name] = agg[\"PLAN SCORE\"].describe().drop(\"count\")\n", "    for_df[\"time\"][name] = agg[\"elapsed time\"].describe().drop(\"count\")\n", "\n", "print(\"pted\")\n", "pted_df = pd.DataFrame(for_df[\"pted\"])\n", "print(pted_df)\n", "print(\"lev\")\n", "lev_df = pd.DataFrame(for_df[\"lev\"])\n", "print(lev_df)\n", "print(\"elapsed time\")\n", "print(pd.DataFrame(for_df[\"time\"]))\n", "\n", "print(\"\\n\" * 10)\n", "\n", "\n", "# print(df['TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE'].tolist()[2])\n", "# print(df['name'][2])\n", "# print(df['query'][2])\n", "# raise\n", "# aggregate by \"query\" and print all the plans, with the name of the model\n", "for i, (query, agg) in enumerate(df.groupby(\"query\"), 1):\n", "    print(\"=\" * 50)\n", "    print(f\"{i} / 30. {query}\")\n", "    print(\"=\" * 50)\n", "    # print(agg)\n", "    print()\n", "    # iterate lines\n", "\n", "    name_to_data = dict(zip(agg[\"name\"].tolist(), agg.to_dict(\"records\"), strict=False))\n", "    model_order = [\n", "        # 'output-full-gpt35.html',\n", "        # 'output_gpt4.html',\n", "        # 'output_claude1.html',\n", "        # 'output_claude2.html',\n", "        # 'output_claude2.1.html',\n", "        # 'output_claude-3-haiku-20240307.html',\n", "        # 'output_claude-3-sonnet-20240229.html',\n", "        # 'output_claude-3-opus-20240229.html',\n", "        \"gpt4\",\n", "        \"claude-3-opus\",\n", "    ]\n", "    for model_name in model_order:\n", "        display_name = model_name\n", "        lev = float(datapoint[\"PLAN SCORE\"])\n", "        pted = float(datapoint[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"])\n", "\n", "        print(f\"{display_name} lev {lev:.3f} pted {pted:.3f}\")\n", "        print(\"-\" * 30)\n", "        datapoint = name_to_data[model_name]\n", "        # print(datapoint.keys())\n", "        print(datapoint[\"plan\"])\n", "        print()\n", "\n", "    print(\"Ideal\")\n", "    print(\"-\" * 30)\n", "    print(datapoint[\"ideal\"])\n", "    print()\n", "\n", "    print(\"\\n\" * 5)\n", "# df.groupby('query').agg({'plan': 'unique'}).to_csv('plans.csv')\n", "# df.groupby('query')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analyze scores after multiple runs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import pandas as pd\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import load_data\n", "\n", "# paths_to_categories = [*map(pathlib.Path, [\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-1-seq-baseline-before-emb.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-2-coarse-orig-after-gpt4o-pr.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-3-coarse-orig-coarse-demos-gpt4o-before-embeddings-change.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-4-coarse-orig-coarse-demos-after-embeddings.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-5-baseline-seq-new-emb.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-6-baseline-wf-new-emb.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-7-coarse-all.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-8-fine-all-coarse-demos.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-9-fine-all-fine-demos.html\",\n", "#     \"/workspace/src/studio/text_to_workflow/workflow_generation/output-v8-10-fine-orig-fine-demos.html\",\n", "# ])]\n", "\n", "# fmt: off\n", "paths_to_categories = [*map(pathlib.Path, [\n", "    # \"/workspace/data/workdir/output-v20-wf-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v20-wf-baseline-abstraction.html\",\n", "    # \"/workspace/data/workdir/output-v20-wf-proposals-andrei.html\",\n", "    # \"/workspace/data/workdir/output-v20-wf-proposals-refined.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v21-wf-ideal-baseline-abstraction.html\",\n", "    # \"/workspace/data/workdir/output-v21-wf-ideal-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v21-wf-ideal-proposal-v2.html\",\n", "    # \"/workspace/data/workdir/output-v21-wf-ideal-refined.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v23-wf-ideal-refined-test-only.html\",\n", "    # \"/workspace/data/workdir/output-v23-wf-ideal-refined-train-only.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v25-wf-ideal-abstraction-rebuilt.html\",\n", "    # \"/workspace/data/workdir/output-v25-wf-ideal-refined-train-only.html\",\n", "    # \"/workspace/data/workdir/output-v25-wf-ideal-refined-all.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v26-wf-refined-test-only.html\",\n", "    # \"/workspace/data/workdir/output-v26-wf-refined-all.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v27-wf-allmpnet-ideal-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v27-wf-allmpnet-ideal-refined.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v27-wf-allmpnet-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v27-wf-allmpnet-refined.html\",\n", "\n", "    # COMPARISON OF ALTERNATE THOUGHTS\n", "\n", "    # \"/workspace/data/workdir/output-v25-wf-ideal-baseline-inc-test.html\",\n", "    # \"/workspace/data/workdir/output-v25-wf-ideal-refined-test-only.html\",\n", "    # \"/workspace/data/workdir/output-v29-wf-desc-ideal-test.html\",\n", "    # \"/workspace/data/workdir/output-v29-wf-summaries-ideal-test.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v26-wf-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v26-wf-refined-train-only.html\",\n", "    # \"/workspace/data/workdir/output-v29-wf-desc-train.html\",\n", "    # \"/workspace/data/workdir/output-v29-wf-summaries-train.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-previous-thoughts.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-improvement-wo-build.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-gpt4o-thoughts.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-baseline-original-thoughts.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-improvement.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summarization-gpt4o-thoughts-2.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summ-new-dataset-and-retriever.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summ-ndata-leaveoneout.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summ-retriever-change-only.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summ-old-retriever-ablation.html\",\n", "    # \"/workspace/data/workdir/output-v33-wf-summ-old-way-no-LOO.html\"\n", "\n", "    # \"/workspace/data/workdir/output-v35-wfgen-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v34-wfgen-activities-retriever-move.html\",\n", "    # \"/workspace/data/workdir/output-v34-wfgen-activities-retriever-moce-2.html\",\n", "    # \"/workspace/data/workdir/output-v35-wfgen-new-retriever.html\",\n", "    # \"/workspace/data/workdir/output-v35-wfgen-new-summ-dataset.html\",\n", "    # \"/workspace/data/workdir/output-v37-wfgen-summarization-ted-demos.html\"\n", "\n", "    # \"/workspace/data/workdir/output-v39-wfgen-before-PR.html\",\n", "    # \"/workspace/data/workdir/output-v39-wfgen-after-PR.html\",\n", "    # \"/workspace/data/workdir/output-v39-wfgen-after-PR-rerun.html\",\n", "    # \"/workspace/data/workdir/output-v40-wfgen-decreasing-demos.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v39-sqgen-baseline-before-PR.html\",\n", "    # \"/workspace/data/workdir/output-v39-sqgen-ablation-reordering-only.html\",\n", "    # \"/workspace/data/workdir/output-v39-sqgen-demox2-after-PR.html\",\n", "    # \"/workspace/data/workdir/output-v39-sqgen-demox2-after-PR-rerun.html\",\n", "    # \"/workspace/data/workdir/output-v40-sqgen-decreasing-demos.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v41-sqgen-fine-before-PR-baseline.html\",\n", "    # \"/workspace/data/workdir/output-v41-sqgen-fine-after-PR.html\",\n", "\n", "    # \"/workspace/data/workdir/output-v43-wfgen-inc-pruning.html\",\n", "    # \"/workspace/data/workdir/output-v43-wfgen-without-demo-pruning.html\",\n", "    # \"/workspace/data/workdir/output-v44-wfgen-newdataset-inc-pruning.html\",\n", "    # \"/workspace/data/workdir/output-v44-wfgen-newdataset-without-pruning.html\",\n", "\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-baseline.html\",\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-baseline-gpt4o-august-planning.html\",\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-plan-gpt4o-gen-sonnet35.html\",\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-plan-gpt4o-august-gen-sonnet-35.html\",\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-plan-sonnet35-gen-gpt4o-mini.html\",\n", "    \"/workspace/data/workdir/output-v64-wfgen-portable-new-sonnet35.html\",\n", "    \"/workspace/data/workdir/output-v65-sqgen-windows-baseline.html\",\n", "    \"/workspace/data/workdir/output-v65-sqgen-windows-baseline-gpt4o-august.html\",\n", "    \"/workspace/data/workdir/output-v65-sqgen-windows-plan-sonnet35.html\",\n", "])]\n", "# fmt: on\n", "\n", "loaded_datapoints, loaded_aggregates = load_data(paths_to_categories)\n", "df = pd.DataFrame(loaded_aggregates).dropna(axis=1)\n", "# from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import print_summary\n", "# print_summary(pd.DataFrame(loaded_datapoints).dropna(axis=1))\n", "\n", "# put basename of identifier path in the name column\n", "df[\"name\"] = df[\"identifier\"].apply(lambda x: pathlib.Path(x).name)\n", "df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# columns_mapping = {\n", "#     'name': 'name',\n", "#     'average ted_scores': 'TED',\n", "#     'average pled_scores': 'PLED',\n", "#     'average plan_scores': 'plan SCORE',\n", "#     'average elapsed_times': 'TIME',\n", "#     'AVERAGE DURATION OF STEP planning': 'TIME plan',\n", "#     'AVERAGE DURATION OF STEP generation': 'TIME gen',\n", "#     'average prompt tokens': 'prompt',\n", "#     'average completion tokens': 'completion',\n", "# }\n", "# df = df.rename(columns=columns_mapping)[columns_mapping.values()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = df.set_index(\"name\").drop(columns=[\"identifier\"]).T\n", "_names = m.columns.tolist()\n", "print(m.rename(columns=lambda x: str(_names.index(x) + 1)).applymap(lambda x: str(x)[:6].format(x)).to_string())  # noqa: B023\n", "print(\"Legend:\")\n", "print(\"\\n\".join(f\"{i}: {name}\" for i, name in enumerate(_names, 1)))\n", "print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metadata = {\n", "    \"output-v8-4-coarse-orig-coarse-demos-after-embeddings.html\": (\"coarse\", \"orig\", \"coarse\", \"pr\"),\n", "    \"output-v8-5-baseline-seq-new-emb.html\": (\"coarse\", \"orig\", \"coarse\", \"baseline\"),\n", "    \"output-v8-6-baseline-wf-new-emb.html\": (\"coarse\", \"all\", \"coarse\", \"baseline\"),\n", "    \"output-v8-7-coarse-all.html\": (\"coarse\", \"all\", \"coarse\", \"pr\"),\n", "    # \"output-v8-8-fine-all-coarse-demos.html\": (\"fine\", \"all\", \"coarse\", \"pr\"),\n", "    \"output-v8-9-fine-all-fine-demos.html\": (\"fine\", \"all\", \"fine\", \"pr\"),\n", "    \"output-v8-10-fine-orig-fine-demos.html\": (\"fine\", \"orig\", \"fine\", \"pr\"),\n", "}\n", "# add metadata values to dataframe where key is \"name\"\n", "df[\"granularity\"] = df[\"name\"].apply(lambda x: metadata[x][0])\n", "df[\"quantity\"] = df[\"name\"].apply(lambda x: metadata[x][1])\n", "df[\"demonstrations\"] = df[\"name\"].apply(lambda x: metadata[x][2])\n", "df[\"baseline\"] = df[\"name\"].apply(lambda x: metadata[x][3])\n", "\n", "# add new column based on rows\n", "df[\"category\"] = df[\"granularity\"] + \" \" + df[\"demonstrations\"] + \" \" + df[\"baseline\"]\n", "mapping = {\n", "    \"coarse coarse baseline\": \"baseline\",\n", "    \"coarse coarse pr\": \"coarse\",\n", "    \"fine fine pr\": \"fine\",\n", "}\n", "df[\"category\"] = df[\"category\"].apply(lambda x: mapping[x])\n", "\n", "# print the dataframe with metadata\n", "# df.set_index('name')\n", "\n", "# print a table for each value of \"TED\" for each value of \"granularity\", \"quantity\" (index and column)\n", "# use .2f precision\n", "# print(df.pivot_table(index='granularity', columns='quantity', values='TED').to_string(float_format='%.2f'))\n", "\n", "for metric in [\"TED\", \"PLED\", \"TIME plan\", \"TIME gen\"]:\n", "    print(metric)\n", "    print(df.pivot_table(index=\"category\", columns=\"quantity\", values=metric).to_string(float_format=\"%.2f\", index=True, header=True))\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "statistics = collections.defaultdict(dict)\n", "for name, agg in df.groupby(\"name\"):\n", "    statistics[\"pted\"][name] = agg[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"].describe().drop(\"count\")\n", "    statistics[\"lev\"][name] = agg[\"PLAN SCORE\"].describe().drop(\"count\")\n", "    statistics[\"time\"][name] = agg[\"elapsed time\"].describe().drop(\"count\")\n", "\n", "for metric in [\"pted\", \"lev\", \"time\"]:\n", "    print(metric)\n", "    m = pd.DataFrame(statistics[metric])\n", "    _names = m.columns.tolist()\n", "    print(m.rename(columns=lambda x: str(_names.index(x) + 1)).to_string())  # noqa: B023\n", "    print(\"Legend:\")\n", "    print(\"\\n\".join(f\"{i}: {name}\" for i, name in enumerate(_names, 1)))\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analyze scores for ideal planning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "import yaml\n", "\n", "path = \"/workspace/data/Autopilot.Samples/Dataset/Converted/Portable/StudioDesktop_fixes_20240412_[Client] Create Workday Prehire When New ID Is Uploaded/Main.yaml\"\n", "workflow = yaml.load(open(path), Loader=yaml.SafeLoader)\n", "yaml.safe_dump(workflow, open(path + \".test1\", \"w\"), width=sys.maxsize, sort_keys=False)\n", "yaml.dump(workflow, open(path + \".test2\", \"w\"), width=sys.maxsize, sort_keys=False, Dumper=yaml.SafeDumper)\n", "yaml.dump(workflow, open(path + \".test3\", \"w\"), width=1e5, sort_keys=False, Dumper=yaml.SafeDumper)\n", "yaml.dump(workflow, open(path + \".test4\", \"w\"), width=1e5, sort_keys=False, Dumper=yaml.CSafeDumper)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare two or more results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import pandas as pd\n", "\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import load_data\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesRetriever\n", "\n", "retriever = ActivitiesRetriever().load()\n", "\n", "paths_to_categories = [\n", "    *map(\n", "        pathlib.Path,\n", "        [\n", "            \"/workspace/data/workdir/output-v80-sqgen-windows-test-baseline-retry2.html\",\n", "            \"/workspace/data/workdir/output-v80-sqgen-windows-test-prod-quality-demos-retry2.html\",\n", "            # \"/workspace/data/workdir/output-v33-wf-summarization-gpt4o-thoughts-2.html\",\n", "            # \"/workspace/data/workdir/output-v33-wf-summ-new-dataset-and-retriever.html\",\n", "            # \"/workspace/data/workdir/output-v33-wf-summ-ndata-leaveoneout.html\"\n", "            # \"/workspace/data/workdir/output-v35-wfgen-baseline.html\",\n", "            # \"/workspace/data/workdir/output-v36-wfgen-gpt4o-mini-generation.html\",\n", "            # \"/workspace/data/workdir/output-v43-wfgen-inc-pruning.html\",\n", "            # \"/workspace/data/workdir/output-v43-wfgen-without-demo-pruning.html\",\n", "        ],\n", "    )\n", "]\n", "loaded_datapoints, loaded_aggregates = load_data(paths_to_categories)\n", "\n", "df = pd.DataFrame(loaded_datapoints).dropna(axis=1)\n", "# group by query\n", "display(sorted(df.keys()))\n", "groups = df.groupby(\"index\")  # group by all the datapoints\n", "\n", "def ted_diff(grouped_element):\n", "    key, group = grouped_element\n", "    ted_scores = group[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"].tolist()\n", "    return ted_scores[1] - ted_scores[0]\n", "\n", "# for index, group in sorted(groups, key=lambda x: x[0]):  # sort by datapoints\n", "for index, group in sorted(groups, key=ted_diff):  # sort by TED diff\n", "    queries = group[\"query\"].tolist()\n", "    expected_plans = group[\"ideal_plan\"].tolist()\n", "    expected_activities_group = group[\"expected activities\"].tolist()\n", "    assert len(set(queries)) == 1 and len(set(expected_plans)) == 1 and len(set(expected_activities_group)) == 1\n", "    query = queries[0]\n", "    expected_plan = expected_plans[0]\n", "    expected_activities = expected_activities_group[0]\n", "    # end of preamble\n", "\n", "    experiments = group[\"identifier\"].tolist()\n", "\n", "    param_lev = group[\"LEVENSHTEIN PARAMS SCORE\"].tolist()\n", "    param_exact = group[\"EXACT PARAMS SCORE\"].tolist()\n", "    workflows = group[\"workflow_cleaned\"].tolist()\n", "\n", "    retriever_scores = group[\"activities recall\"].tolist()\n", "    ted_scores = group[\"TREE EDIT DISTANCE WITH EXACT PARAMS SCORE\"].tolist()\n", "    plan_scores = group[\"PLAN SCORE\"].tolist()\n", "\n", "    ideal_workflow_synopsis = group[\"ideal_workflow_synopsis\"].tolist()[0]\n", "    generated_workflow_synopsi = group[\"generated_workflow_synopsis\"].tolist()\n", "\n", "    # # Log different planning prompts\n", "    # planning_prompts = group[\"planning_prompt\"].tolist()\n", "    # if len(set(planning_prompts)) > 1:\n", "    #     print(f'*** {index} / {len(groups)} - \"{query}\"')\n", "    #     print(get_diff(planning_prompts[0], planning_prompts[1], 5))\n", "    #     continue\n", "    # continue\n", "\n", "    if ted_scores[0] != ted_scores[1]:\n", "        print(experiments)\n", "        print(f'*** {index} / {len(groups)} - \"{query}\"')\n", "        print(f\"ted diff {ted_scores[1] - ted_scores[0]:.2f} {ted_scores[0]:.2f} -> {ted_scores[1]:.2f}\")\n", "\n", "        print(\"Ideal workflow synopsis\")\n", "        print(ideal_workflow_synopsis)\n", "        print(\"Generated Workfow synopsis 1\")\n", "        print(generated_workflow_synopsi[0])\n", "        print(\"Generated Workfow synopsis 2\")\n", "        print(generated_workflow_synopsi[1])\n", "        print(\"Generated Workflo diffs\")\n", "        print(get_diff(*generated_workflow_synopsi))\n", "\n", "        print(\"Workflow differences\")\n", "        print(get_diff(workflows[0], workflows[1], 3))\n", "        print(\"\\n\" * 4)\n", "    continue\n", "\n", "    retrieved_activities = group[\"retrieved_activities\"].tolist()\n", "\n", "    if param_lev[0] != param_lev[1]:\n", "        # print(experiments)\n", "        print(f'*** {index} / {len(groups)} - \"{query}\"')\n", "        print(f\"param leven<PERSON>tein diff {param_lev[1] - param_lev[0]:.2f} {param_lev[0]:.2f} -> {param_lev[1]:.2f}\")\n", "        print(f\"ted diff {ted_scores[1] - ted_scores[0]:.2f} {ted_scores[0]:.2f} -> {ted_scores[1]:.2f}\")\n", "        print(get_diff(workflows[0], workflows[1], 3))\n", "        print(\"\\n\" * 4)\n", "    continue\n", "\n", "    def filter_acts(activities):\n", "        import collections\n", "\n", "        by_steps = collections.defaultdict(list)\n", "        for act in activities.split(\"\\n\"):\n", "            short_name, score, step = [x.strip() for x in act.split(\":\")]\n", "            by_steps[step].append(f\"{short_name}({int(float(score)*100)})\")\n", "        return \"\\n\".join(f\"{step}: {' '.join(acts)}\" for step, acts in by_steps.items())\n", "\n", "    def map_activity_ids(activities):\n", "        return \"\\n\".join(retriever.get(activity, \"Portable\", None)[\"fullClassName\"] for activity in activities.split(\"\\n\"))\n", "\n", "    if len(set(retriever_scores)) > 1:\n", "        print(\"=\" * 50)\n", "        print(f'*** {index} / {len(groups)} - \"{query}\"')\n", "        print(\"-\" * 10 + \" expected plan \" + \"-\" * 10)\n", "        print(expected_plan)\n", "        print(\"-\" * 10 + \" expected activities \" + \"-\" * 10)\n", "        print(map_activity_ids(expected_activities))\n", "        print(\"=\" * 50)\n", "        print(\"retriever scores\", retriever_scores)\n", "        print(\"ted scores\", ted_scores)\n", "        id1, id2 = experiments\n", "        plan1, plan2 = group[\"plan\"].tolist()\n", "        act1, act2 = retrieved_activities\n", "        print(\"-\" * 10 + f\" {id1} \" + \"-\" * 10)\n", "        print(\"-\" * 30)\n", "        print(plan1)\n", "        print(filter_acts(act1))\n", "        print(\"-\" * 10 + f\" {id2} \" + \"-\" * 10)\n", "        print(plan2)\n", "        print(filter_acts(act2))\n", "        print(\"=\" * 50)\n", "        print(\"\\n\" * 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Dump html outputs for fix workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import html\n", "import pathlib\n", "import re\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import load_data\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workdir/\")\n", "\n", "html_paths = sorted(root_path.rglob(\"output*.html\"))\n", "# display(html_paths)\n", "print(f\"Found {len(html_paths)} html files.\")\n", "loaded_datapoints, loaded_aggregates = load_data(html_paths)\n", "dataset = collections.defaultdict(list)\n", "for element in loaded_datapoints:\n", "    # if \"plan_sequence_ideal\" in element:\n", "    #     continue  # means it's sequence, skip it\n", "    html_identifier = element[\"identifier\"]\n", "\n", "    remaining = html.unescape(element[\"_remaining\"])\n", "    error_group_re = re.compile(\n", "        r\"\"\"partially_deserializable: (?P<partially_deserializable>[^\\n]*)\n", "fully_deserializable: (?P<fully_deserializable>[^\\n]*)\n", "invalid: (?P<invalid>[^\\n]*)\n", "output: '(?P<output>[^\\n]*)'\n", "errors:\\s?(?P<errors>.*?)\\n\\n\"\"\",\n", "        re.DOTALL,\n", "    )\n", "    matches = list(error_group_re.finditer(remaining))\n", "    if not matches:\n", "        print(f\"Could not find section {html_identifier}\")\n", "        continue\n", "\n", "    info = matches[0].groupdict()\n", "    info[\"errors\"] = [] if info[\"errors\"] == \"[]\" else [msg[2:] for msg in info[\"errors\"].splitlines()]\n", "    if not info[\"errors\"]:\n", "        # print(\"No errors\")\n", "        continue\n", "\n", "    filepath_match = re.search(r\"Ground Truth Filename:\\s*(?P<filepath>.*)\", remaining)\n", "    if not filepath_match:\n", "        print(f\"Could not find filename {html_identifier}\")\n", "        continue\n", "    wfgen_filepath = pathlib.Path(filepath_match.group(\"filepath\"))\n", "    # print(f\"Before {wfgen_filepath}\")\n", "    # get rid of sequence generation prefix\n", "    wfgen_filepath = wfgen_filepath.with_stem(re.sub(r\"^\\d{2,}__\\d+_\\d+_\", \"\", wfgen_filepath.stem))\n", "    # print(f\"After  {wfgen_filepath}\")\n", "\n", "    if \"workflow_valid\" not in element:\n", "        print(f\"Could not find valid workflow {html_identifier}\")\n", "        continue\n", "    workflow_identifier = wfgen_filepath.stem\n", "    workflow_generated = element[\"workflow_valid\"]\n", "    datapoint = {\n", "        \"errors\": info,\n", "        \"workflow\": yaml_load(workflow_generated),\n", "    }\n", "    dataset[workflow_identifier].append(datapoint)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Starting with:\")\n", "print(len(dataset))\n", "print(sum(map(len, dataset.values())))\n", "\n", "# dump_path = pathlib.Path(\"/workspace/data/workdir/Autopilot.Samples/Dataset\")\n", "dump_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Dataset\")\n", "dump_path.mkdir(exist_ok=True)\n", "\n", "for identifier, datapoints in list(dataset.items()):\n", "    # datapoints = {yaml_dump(dp[\"workflow\"]): dp for dp in datapoints}.values()  # make unique by workflow\n", "    datapoints = {tuple(sorted(dp[\"errors\"][\"errors\"])): dp for dp in datapoints}.values()  # make unique by errors\n", "    dataset[identifier] = datapoints\n", "\n", "print(\"Deduplicated\")\n", "print(len(dataset))\n", "print(sum(map(len, dataset.values())))\n", "\n", "for identifier, datapoints in dataset.items():\n", "    parent_dir = dump_path / identifier\n", "    parent_dir.mkdir(exist_ok=True)\n", "    for i, datapoint in enumerate(datapoints, 1):\n", "        # print(parent_dir / f\"Fix{i}__{identifier}.yaml\")\n", "        yaml_dump(datapoint, parent_dir / f\"Fix{i}__{identifier}.yaml\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted(dataset.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Error analysis of various html outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import pandas as pd\n", "\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import load_data\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_retrievers import ActivitiesRetriever\n", "\n", "retriever = ActivitiesRetriever().load()\n", "paths_to_categories = [*map(pathlib.Path, [\n", "    # \"/workspace/data/workdir/output-v55-wfgen-test.html\",\n", "    # \"/workspace/data/workdir/output-v55-wfgen-train.html\",\n", "    # \"/workspace/data/workdir/output-v61-wfgen-test-eval-improvement.html\",\n", "    \"/workspace/data/workdir/output-v63-wfgen-error-analysis.html\",\n", "])]\n", "loaded_datapoints, loaded_aggregates = load_data(paths_to_categories)\n", "\n", "df = pd.DataFrame(loaded_datapoints).dropna(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted(df.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import html\n", "\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import prefix, suffix\n", "\n", "\n", "def htmlprint(*args, **kwargs):\n", "    print(*args, **kwargs, end=\"<br>\\n\")\n", "\n", "\n", "print(prefix)\n", "# for section in df.sort_values(\"ACTIVITY TREE EDIT DISTANCE SCORE\")[\"html\"]:\n", "for i, (index, row) in enumerate(df.sort_values(\"ACTIVITY TREE EDIT DISTANCE SCORE\").iterrows()):\n", "    htmlprint(\"-\" * 200)\n", "    # print(f\"\"\"<h2 class=\"heading\">{index}. {row[\"query\"]}</h2>\"\"\")\n", "    htmlprint(f\"*** {i} / {len(df)} ***\")\n", "    htmlprint(\"TED\", row[\"ACTIVITY TREE EDIT DISTANCE SCORE\"])\n", "    htmlprint(\"PLED\", row[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"])\n", "    htmlprint()\n", "    htmlprint(\"IDEAL\")\n", "    htmlprint(f'<pre>{html.escape(row[\"ideal_workflow_synopsis\"])}</pre>')\n", "    htmlprint()\n", "    htmlprint(\"GENERATED\")\n", "    htmlprint(f'<pre>{html.escape(row[\"generated_workflow_synopsis\"])}</pre>')\n", "    htmlprint()\n", "    htmlprint(row[\"html\"])\n", "\n", "    # if 'Exception!' in row['html']:\n", "    #     print(row['html'])\n", "    #     break\n", "    # break\n", "print(suffix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Create subplots\n", "fig, axs = plt.subplots(2, 2, figsize=(10, 8))\n", "\n", "# Plot the histograms\n", "axs[0, 0].hist(df[\"ACTIVITY TREE EDIT DISTANCE SCORE\"], bins=30)\n", "axs[0, 0].set_xlabel(\"TED\")\n", "axs[0, 0].set_ylabel(\"Frequency\")\n", "axs[0, 0].set_title(\"TED\")\n", "\n", "axs[0, 1].hist(df[\"TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE\"], bins=30)\n", "axs[0, 1].set_xlabel(\"PLED\")\n", "axs[0, 1].set_ylabel(\"Frequency\")\n", "axs[0, 1].set_title(\"PLED\")\n", "\n", "axs[1, 0].hist(df[\"PLAN Tree Edit Score\"], bins=30)\n", "axs[1, 0].set_xlabel(\"PLAN TED\")\n", "axs[1, 0].set_ylabel(\"Frequency\")\n", "axs[1, 0].set_title(\"PLAN TED\")\n", "\n", "axs[1, 1].hist(df[\"activities recall\"], bins=30)\n", "axs[1, 1].set_xlabel(\"Activities Recall\")\n", "axs[1, 1].set_ylabel(\"Frequency\")\n", "axs[1, 1].set_title(\"Activities Recall\")\n", "\n", "# Adjust spacing between subplots\n", "plt.tight_layout()\n", "\n", "# Show the figure\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}