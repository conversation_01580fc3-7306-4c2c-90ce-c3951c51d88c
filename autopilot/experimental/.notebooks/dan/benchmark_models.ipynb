{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "from services.studio._text_to_workflow.workflow_generation import main\n", "\n", "\n", "class RedirectStdStreams(object):\n", "    def __init__(self, stdout=None, stderr=None):\n", "        self._stdout = stdout or sys.stdout\n", "        self._stderr = stderr or sys.stderr\n", "\n", "    def __enter__(self):\n", "        self.old_stdout, self.old_stderr = sys.stdout, sys.stderr\n", "        self.old_stdout.flush(); self.old_stderr.flush()\n", "        sys.stdout, sys.stderr = self._stdout, self._stderr\n", "\n", "    def __exit__(self, exc_type, exc_value, traceback):\n", "        self._stdout.flush(); self._stderr.flush()\n", "        sys.stdout = self.old_stdout\n", "        sys.stderr = self.old_stderr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main.run_workflow_generation_build()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import itertools\n", "import pathlib\n", "\n", "output_path_fmt = \"/workspace/data/workdir/output-v54-benchmark-{}-{}.html\"\n", "\n", "if False:\n", "    variants = list(itertools.product([\"gpt35\", \"gpt4o-mini\", \"gpt4o\"], repeat=2))\n", "    variants = list(itertools.product([\"gpt35\", \"gpt4o-mini\", \"gpt4o\", \"llm-gateway-claude-35-sonnet\"], [\"llm-gateway-claude-35-sonnet\"]))\n", "    variants = list(itertools.product([\"gpt35\", \"gpt4o-mini\", \"gpt4o\", \"llm-gateway-gemini-1.5-pro-001\", \"llm-gateway-claude-35-sonnet\"], [\"llm-gateway-gemini-1.5-pro-001\"]))\n", "    variants = list(itertools.product([\"llm-gateway-gemini-1.5-pro-001\", \"llm-gateway-claude-35-sonnet\"], [\"llama3.1-70B\", \"llama3.1-8B\"]))\n", "    variants += [(b, a) for a, b in variants if a != b]\n", "    display(variants)\n", "    raise\n", "\n", "variants = [\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'gpt4o-mini'),\n", "    # ('gpt4o-mini', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'gpt4o'),\n", "    # ('gpt4o', 'llm-gateway-gemini-1.5-pro-001'),\n", "\n", "    # ('llama3.1-70B', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llama3.1-70B', 'llm-gateway-claude-35-sonnet'),\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'llama3.1-70B'),\n", "\n", "    # ('llm-gateway-claude-35-sonnet', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'llm-gateway-claude-35-sonnet'),\n", "\n", "    # postponed\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'gpt35'),\n", "    # ('gpt35', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llm-gateway-claude-35-sonnet', 'llama3.1-70B'),\n", "    # ('llm-gateway-gemini-1.5-pro-001', 'llama3.1-8B'),\n", "    # ('llm-gateway-claude-35-sonnet', 'llama3.1-8B'),\n", "    # ('llama3.1-8B', 'llm-gateway-gemini-1.5-pro-001'),\n", "    # ('llama3.1-8B', 'llm-gateway-claude-35-sonnet'),\n", "\n", "    # reflection\n", "    ('llama-reflection-70B', 'gpt4o-mini'),\n", "    # ('llama-reflection-70B', 'llama-reflection-70B'),\n", "    # ('llama-reflection-70B', 'gpt4o'),\n", "\n", "    # make sure these were done ok\n", "    # ('gpt4o', 'gpt4o-mini'),\n", "    # ('gpt4o-mini', 'gpt4o-mini'),\n", "    # ('gpt4o-mini', 'gpt4o'),\n", "]\n", "\n", "remaining_index = 0\n", "for planning_model, generation_model in variants[remaining_index:]:\n", "    print('-' * 30)\n", "    print(f\"Planning {planning_model}, Generation {generation_model}\")\n", "    print('-' * 30)\n", "    output_path = pathlib.Path(output_path_fmt.format(planning_model, generation_model))\n", "    # assert not output_path.exists(), output_path\n", "\n", "    with open(output_path, \"w\") as fd:\n", "        with RedirectStdStreams(stdout=fd, stderr=fd):\n", "        # with RedirectStdStreams():\n", "            main.run_workflow_generation_test(\n", "                query=None,\n", "                target_framework=\"Portable\",\n", "                subset=\"test\",\n", "                planning_model=planning_model,\n", "                generation_model=generation_model,\n", "                mode=\"workflow\",\n", "                log_file=None,\n", "                # kwargs\n", "                seed=42,\n", "                # limit=1,\n", "                # use_ideal_plan=True,\n", "                # localization=None,\n", "                # run_generation=True,\n", "                # force_sequence_generation_entire_sequences=False,\n", "            )\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}