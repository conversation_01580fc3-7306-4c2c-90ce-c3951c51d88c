{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create an index of the workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "path = pathlib.Path('/workspace/data/workflows')\n", "portable_path = path / 'parsed_portable1'\n", "windows_path = path / 'parsed_windows1'\n", "\n", "# Dump an index of the workflows\n", "index = {}\n", "for root_path in [portable_path, windows_path]:\n", "    for p in root_path.iterdir():\n", "        if p.suffix == '.yaml':\n", "            index[p.name] = p.as_posix()\n", "import json\n", "\n", "with open(portable_path.parent / '_index.json', 'w') as fd:\n", "    json.dump(index, fd, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Experiment timing for slice vs pop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# .pop() vs slice performance\n", "import random\n", "import time\n", "\n", "\n", "def variant_pop(lst, to_pop):\n", "    for _ in range(to_pop):\n", "        lst.pop()\n", "\n", "def variant_slice(lst, to_pop):\n", "    return lst[:-to_pop]\n", "\n", "timings = []\n", "for _ in range(100):\n", "    n = 130\n", "    a = [i for i in range(random.randint(n, n))]\n", "    to_pop = random.randint(10, 10)\n", "\n", "    b = a[:]\n", "    start = time.perf_counter()\n", "    variant_slice(b, to_pop)\n", "    timing_slice = time.perf_counter() - start\n", "\n", "    b = a[:]\n", "    start = time.perf_counter()\n", "    variant_pop(b, to_pop)\n", "    timing_pops = time.perf_counter() - start\n", "\n", "    timings.append({\n", "        \"slice\": timing_slice,\n", "        \"pop\": timing_pops\n", "    })\n", "\n", "import pandas as pd\n", "\n", "pd.DataFrame(timings).describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Group workflows into subdirectories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# root_path = pathlib.Path(\"/workspace/data/workflows/windows_converted_unzipped\")\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_converted_unzipped\")\n", "\n", "listing = list(root_path.iterdir())\n", "len(listing)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import shutil\n", "\n", "prefix_counter = collections.Counter()\n", "for p in listing:\n", "    _, process_name, workflow_name = p.name.split('_', 2)\n", "    prefix = process_name[:2]\n", "    subdir = root_path / prefix\n", "    if not subdir.exists():\n", "        subdir.mkdir()\n", "\n", "    prefix_counter[prefix] += 1\n", "    # print(f\"Moving {p} to {subdir / p.name}\")  # for dry run\n", "    shutil.move(p, subdir / p.name)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Extract .yamls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "# root_path = pathlib.Path(\"/workspace/data/workflows/windows_converted_unzipped\")\n", "root_path = pathlib.Path(\"/workspace/data/workflows/portable_converted_unzipped\")\n", "\n", "listing = list(root_path.iterdir())\n", "len(listing)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import json\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "# output_path = pathlib.Path(\"/workspace/data/workflows/\") / \"parsed_portable2_test\"\n", "output_path = pathlib.Path(\"/workspace/data/workflows/\") / \"parsed_windows2_test\"\n", "\n", "drop_reason = collections.Counter()\n", "\n", "workflows = {}\n", "listing = [*root_path.rglob(\"output.yaml\")]\n", "for p in tqdm.tqdm(listing):\n", "    # print(p)\n", "    with open(p) as fd:\n", "        data = json.load(fd)\n", "\n", "    if not data[\"Success\"]:\n", "        drop_reason[\"unsuccessful\"] += 1\n", "        continue\n", "    if len(data[\"Errors\"]):\n", "        drop_reason[\"with_errors\"] += 1\n", "        continue\n", "    try:\n", "        workflow_string = data[\"result\"]\n", "        wf = yaml_load(workflow_string)\n", "    except Exception as e:\n", "        print(\"Error loading yaml\", e)\n", "        drop_reason[\"workflow_exception\"] += 1\n", "        continue\n", "    if \"workflow\" not in wf:\n", "        drop_reason[\"no_workflow\"] += 1\n", "        continue\n", "    drop_reason[\"success\"] += 1\n", "    # # workflows[p] = data\n", "    p_out = output_path / p.parent.relative_to(root_path)\n", "    p_out.parent.mkdir(exist_ok=True, parents=True)\n", "    with open(p_out.with_suffix('.yaml'), \"w\") as fd:\n", "        fd.write(workflow_string)\n", "\n", "for reason, count in drop_reason.items():\n", "    print(reason, count)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check the errors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["listing = [*root_path.rglob(\"output.yaml\")]\n", "\n", "errors = collections.Counter()\n", "for p in tqdm.tqdm(listing):\n", "    with open(p) as fd:\n", "        data = json.load(fd)\n", "    errors.update(data[\"Errors\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for error, count in errors.most_common():\n", "    print(f\"{count:4} {error}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["drop_reason.most_common()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path, data in workflows.items():\n", "    wf = data[\"result\"]\n", "    if len(wf) < 300:\n", "        print(data)\n", "        print(data[\"result\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Perform post-convertion cleanup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import add_manual_trigger, find_trigger, remove_field_with_value, remove_fields\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    \"/workspace/data/workflows/parsed_portable1\",\n", "    \"/workspace/data/workflows/parsed_portable2\",\n", "    \"/workspace/data/workflows/parsed_windows1\",\n", "    \"/workspace/data/workflows/parsed_windows2\",\n", "])]\n", "\n", "def workflow_cleanup(workflow):\n", "    \"\"\"this method contains the cleanup adjusted parts of build_yaml_object from workflow_generation_dataset\"\"\"\n", "    trigger = find_trigger(workflow)\n", "    if trigger is None or trigger == \"\":\n", "        workflow = add_manual_trigger(workflow)\n", "    remove_fields(\n", "        workflow,\n", "        [\n", "            \"displayName\",\n", "            # \"Api<PERSON>ey\",  # this can be redacted further on\n", "            \"AllFields\",\n", "            \"AllLabels\",\n", "            \"id\",\n", "            \"Guid\",\n", "            \"Id\",\n", "            \"dynamicActivityDetails\",\n", "            \"isDynamic\",\n", "            \"isConfigured\",\n", "            \"Configuration\",\n", "            \"configuration\",\n", "        ],\n", "    )\n", "    remove_field_with_value(workflow, \"thought\", \"Then\")  # we should rather replace these, they cause problems down the line\n", "    remove_field_with_value(workflow, \"thought\", \"Body\")\n", "    return workflow\n", "\n", "for source_path in data_sources:\n", "    listing = list(source_path.rglob(\"*.yaml\"))\n", "    for p in tqdm.tqdm(listing):\n", "        # print(p)\n", "        try:\n", "            wf = yaml_load(p)\n", "        except Exception as e:\n", "            tqdm.tqdm.write(f\"Error loading yaml {p} {e}\")\n", "            continue\n", "        if wf is None:\n", "            tqdm.tqdm.write(f\"Error loading yaml {p}. Deleting.\")\n", "            p.unlink()\n", "            continue\n", "        # print(yaml_dump(wf))\n", "        # print('\\n' * 30)\n", "        wf = workflow_cleanup(wf)\n", "        # print(yaml_dump(wf))\n", "\n", "        yaml_dump(wf, p)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Count token usage for prompts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import load_data\n", "\n", "data_path = pathlib.Path(\"/workspace/data/workflows/parsed_windows2\")\n", "dataset, dataset_raw, invalid_workflows = load_data(data_path, bounds=(10, 100_000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workflows_too_small = [k for k, wf_str in dataset_raw.items() if len(wf_str) < 10]\n", "workflows_too_big = [k for k, wf_str in dataset_raw.items() if len(wf_str) > 10_000]\n", "print(len(workflows_too_small), len(workflows_too_big))\n", "# for k in workflows_too_big:\n", "#     del dataset[k]\n", "#     del dataset_raw[k]\n", "#     invalid_workflows.append(k)\n", "len(dataset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remaining_dataset_raw = {k: v for k, v in dataset_raw.items() if k not in n_tokens}\n", "remaining_dataset = {k: v for k, v in dataset.items() if k not in n_tokens}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import prompt_llm_for_queries_and_iterate\n", "\n", "tokenizer = tiktoken.encoding_for_model(\"gpt-4-turbo\")\n", "n_tokens = {}\n", "for identifier, query in prompt_llm_for_queries_and_iterate(dataset_raw, dataset, dry_run=True):\n", "# for identifier, query in prompt_llm_for_queries_and_iterate(remaining_dataset_raw, remaining_dataset, dry_run=True, verbose=True):\n", "    # n_tokens[identifier] = len(query)\n", "    # print(identifier, flush=True)\n", "    n_tokens[identifier] = len(tokenizer.encode(query))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{sum(n_tokens.values()):,}\")\n", "import pandas as pd\n", "\n", "pd.Series(n_tokens.values()).describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.hist(n_tokens.values(), bins=100, log=True)\n", "plt.xlabel('Number of Tokens')\n", "plt.ylabel('Frequency (Log Scale)')\n", "plt.title('Distribution of Token Counts')\n", "plt.savefig(\"/workspace/data/workflows/parsed_windows2_statistics/query_full_prompt_tokens_histogram.png\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import (\n", "    dump_activities,\n", "    dump_length_histogram,\n", "    dump_packages,\n", "    dump_summaries,\n", "    dump_variables,\n", ")\n", "\n", "data_path = pathlib.Path(\"/workspace/data/workflows/parsed_windows2\")\n", "output_path = data_path.with_name(data_path.name + \"_statistics\")\n", "output_path.mkdir(exist_ok=True)\n", "with open(output_path / \"_invalid_workflows.txt\", \"w\") as fd:\n", "    for path in invalid_workflows:\n", "        fd.write(f\"{path.as_posix()}\\n\")\n", "dump_packages(dataset, output_path)\n", "dump_activities(dataset, output_path)\n", "dump_length_histogram(dataset_raw, output_path)\n", "dump_summaries(dataset, output_path)\n", "dump_variables(dataset, output_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clear the empty prod directories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "prod_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod\")\n", "for wf_path in prod_path.iterdir():\n", "    if not any(wf_path.iterdir()):\n", "        # print(wf_path)\n", "        wf_path.rmdir()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "cleanup_path = pathlib.Path(\"/workspace/data/workflows/parsed_windows1\")\n", "empty = []\n", "for file_path in cleanup_path.glob(\"*.yaml\"):\n", "    with open(file_path, encoding=\"utf-8-sig\") as fd:\n", "        try:\n", "            if set(yaml_load(fd).keys()) == {'trigger'}:\n", "                empty.append(file_path)\n", "        except:\n", "            continue\n", "sorted(empty)\n", "\n", "for p in empty:\n", "    print(p)\n", "    print(open(p, encoding=\"utf-8-sig\").read())\n", "    print('\\n' * 2)\n", "    # p.unlink()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate planning spacing for existing workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "workflow_gen_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/train\")\n", "for wf_path in workflow_gen_path.iterdir():\n", "    wf_dict = yaml_load(wf_path)\n", "    print(wf_dict['plan'])\n", "    print('-' * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate production data windows2 stat dump problems"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import load_data\n", "\n", "data_path = pathlib.Path(\"/workspace/data/workflows/parsed_windows2\")\n", "dataset, dataset_raw, invalid = load_data(data_path, verbose=1, limit=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import dump_activities_dataset\n", "\n", "\n", "def exclude(workflow: Workflow, plan):\n", "    n_steps_estimation = plan.count('\\n')  # faster this way\n", "    if n_steps_estimation > 10:\n", "        return \"workflow_too_big\"\n", "\n", "dump_activities_dataset(data_path, dataset, output_name=\"_under10\", prune=True, exclude_function=exclude)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p = pathlib.Path(data_path)\n", "output_path = p.with_name(p.name + \"_statistics\")\n", "output_path.mkdir(exist_ok=True)\n", "with open(output_path / \"_invalid_workflows.txt\", \"w\") as fd:\n", "    for path in invalid:\n", "        fd.write(f\"{path.as_posix()}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.scripts.dump_statistics_production_data import (\n", "    dump_activities,\n", "    dump_length_histogram,\n", "    dump_packages,\n", "    dump_summaries,\n", "    dump_variables,\n", ")\n", "\n", "# dump_packages(dataset, output_path)\n", "# dump_activities(dataset, output_path)  # a crapat - fixed -\n", "# dump_length_histogram(dataset_raw, output_path)\n", "# dump_summaries(dataset, output_path)\n", "dump_variables(dataset, output_path)  # a crapat"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mass dump and aggregate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    \"/workspace/data/workflows/parsed_portable1\",\n", "    \"/workspace/data/workflows/parsed_portable2\",\n", "    \"/workspace/data/workflows/parsed_windows1\",\n", "    \"/workspace/data/workflows/parsed_windows2\",\n", "])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import dump_activities_dataset, load_data\n", "\n", "# if input() != \"yes\":\n", "#     print(\"Type yes to run\")\n", "#     raise\n", "\n", "def exclude(workflow: Workflow, plan):\n", "    n_steps_estimation = plan.count('\\n')  # faster this way\n", "    if n_steps_estimation > 10:\n", "        return \"workflow_too_big\"\n", "\n", "for data_path in data_sources:\n", "    print(data_path)\n", "    dataset, dataset_raw, invalid = load_data(data_path, verbose=1, limit=None)\n", "    # dump_activities_dataset(data_path, dataset, output_name=\"under10\", prune=True, exclude_function=exclude)\n", "    dump_activities_dataset(data_path, dataset, output_name=\"refined\", prune=True, custom_plan_filename=\"_plans_refined.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    \"/workspace/data/workflows/parsed_portable1_activity_configuration_refined\",\n", "    \"/workspace/data/workflows/parsed_portable2_activity_configuration_refined\",\n", "    \"/workspace/data/workflows/parsed_windows1_activity_configuration_refined\",\n", "    \"/workspace/data/workflows/parsed_windows2_activity_configuration_refined\",\n", "])]\n", "# list all yamls inside these files and concatenate the results\n", "paths = []\n", "for data_path in data_sources:\n", "    print(data_path)\n", "    listing = list(data_path.rglob(\"*.yaml\"))\n", "    for p in listing:\n", "        paths.append(p)\n", "\n", "# sort paths by modified time and plot the modifciation times in a histogram\n", "import datetime\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "paths.sort(key=lambda p: p.stat().st_mtime)\n", "timestamps = [datetime.datetime.fromtimestamp(p.stat().st_mtime) for p in paths]\n", "paths_to_remove = [p for p, ts in zip(paths, timestamps, strict=False) if ts > datetime.datetime(2024, 5, 14, 13, 35)]\n", "timestamps = [ts for ts in timestamps if ts > datetime.datetime(2024, 5, 14, 13, 35)]\n", "timestamps = pd.Series(timestamps)\n", "timestamps.hist(bins=100)\n", "plt.xticks(rotation=45)\n", "plt.title(\"Modification Times of Activity Configuration Files\")\n", "plt.ylabel(\"Frequency\")\n", "plt.xlabel(\"Modification Time\")\n", "plt.show()\n", "# plt.savefig(\"/workspace/data/workflows/activity_configuration_modification_times.png\")\n", "\n", "print(len(paths_to_remove), len(paths))\n", "\n", "# import tqdm\n", "# for p in tqdm.tqdm(paths_to_remove):\n", "#     if not p.is_file():\n", "#         continue\n", "#     p.unlink()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/workspace/data/workflows/parsed_portable2/_plans.json\") as fd:\n", "    print(len(json.load(fd)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = pathlib.Path(\"/workspace/data/workflows/parsed_portable2/processed_f05fece7-dfae-39f3-214f-3ffae28a1084_Check Standard Loan.yaml\")\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "workflow = yaml_load(path)\n", "Workflow(\"\", \"\", workflow)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import shutil\n", "\n", "import tqdm\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    \"/workspace/data/workflows/parsed_portable1\",\n", "    \"/workspace/data/workflows/parsed_portable2\",\n", "    # \"/workspace/data/workflows/parsed_windows1\",\n", "    # \"/workspace/data/workflows/parsed_windows2\",\n", "])]\n", "\n", "# activity_config_data_sources = [src.with_name(src.name + \"_activity_configuration_under10\") for src in data_sources]\n", "# activity_config_data_sources = [src.with_name(src.name + \"_activity_configuration_v2\") for src in data_sources]\n", "activity_config_data_sources = [src.with_name(src.name + \"_activity_configuration_refined\") for src in data_sources]\n", "\n", "activity_config_destination = \"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod\"\n", "# activity_config_destination = \"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows/prod\"\n", "\n", "for source in activity_config_data_sources:\n", "    listing = list(pathlib.Path(source).iterdir())\n", "    for path in tqdm.tqdm(listing, desc=f\"Copying from {source}\"):\n", "        if path.is_dir():\n", "            destination_path = pathlib.Path(activity_config_destination) / path.name\n", "            if destination_path.exists():\n", "                print(\"Destination already exists\", destination_path)\n", "                continue\n", "                # shutil.rmtree(destination_path)\n", "            shutil.copytree(path, destination_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Dump planning data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "indexing_re = re.compile(r'^\\s*(\\d+\\.)*\\s')\n", "\n", "def strip_plan(plan):\n", "    lines = plan.split('\\n')\n", "    if lines and lines[-1] == \"\":\n", "        lines = lines[:-1]\n", "    return '\\n'.join(m.group(0).replace('  ', '\\t')  # this replacement for dealing with \\t vs \"  \" confusion\n", "                     if (m:=indexing_re.search(line)) else \"None\" for line in lines)\n", "\n", "import difflib\n", "\n", "\n", "def get_diff_lines(a, b):\n", "    return \"\".join(difflib.ndiff(a.splitlines(keepends=True), b.splitlines(keepends=True)))\n", "\n", "import pathlib\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    \"/workspace/data/workflows/parsed_portable1\",\n", "    # \"/workspace/data/workflows/parsed_portable2\",\n", "    # \"/workspace/data/workflows/parsed_windows1\",\n", "    # \"/workspace/data/workflows/parsed_windows2\",\n", "])]\n", "\n", "import json\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.generate_from_production_data import Workflow, WorkflowPruner, load_data, load_queries_and_plans\n", "from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import find_activities\n", "\n", "for root_path in data_sources:\n", "    print(f'Dataset {root_path}')\n", "    print('-' * 50)\n", "\n", "    dataset, dataset_raw, invalid = load_data(root_path, verbose=1, limit=None)\n", "    queries, plans, filtered_dataset = load_queries_and_plans(root_path, dataset)\n", "    print(f'Dataset has {len(dataset)}. Loaded {len(queries)} queries and {len(plans)} plans. Filtered down to {len(filtered_dataset)}.')\n", "    \n", "    refined_plans = json.load(open(root_path / \"_plans_refined.json\"))\n", "    identifiers = set(dataset.keys()) & set(refined_plans.keys())\n", "    print(f'Refined plans for {len(identifiers)}.')\n", "\n", "    mismatch_plans = set()\n", "    for identifier in identifiers:\n", "        print(identifier)\n", "        plan = plans[identifier]\n", "        refined_plan = refined_plans[identifier]\n", "        if strip_plan(plan) != strip_plan(refined_plan):\n", "            print('-' * 50)\n", "            print(get_diff_lines(strip_plan(plan), strip_plan(refined_plan)))\n", "            print('-' * 50)\n", "            print(plan)\n", "            print('-' * 50)\n", "            print(refined_plan)\n", "            print('\\n' * 5)\n", "            mismatch_plans.add(identifier)\n", "    identifiers -= mismatch_plans\n", "    print(f'Found {len(mismatch_plans)} mismatched plans. Keeping only {len(identifiers)}.')\n", "\n", "    output_root_path = root_path.with_name(root_path.name + \"_generation\")\n", "    for identifier in identifiers:\n", "        query = queries[identifier]\n", "        refined_plan = refined_plans[identifier]\n", "        workflow_lmyaml = dataset[identifier]\n", "\n", "        workflow = Workflow(query, refined_plan, workflow_lmyaml)\n", "        WorkflowPruner().prune(workflow)\n", "\n", "        # replace in the plan start of lines \"  \" with \\t but not the inner \"  \"\n", "        # refined_plan = re.sub(r'^(\\s+)', lambda m: m.group(0).replace('  ', '\\t'), refined_plan, flags=re.MULTILINE)\n", "        \n", "        workflow_datapoint_wrap = {\n", "            # \"name\": workflow_lmyaml[\"processName\"],b\n", "            \"name\": workflow.name,\n", "            \"description\": query,\n", "            \"conversion_success\": True,  # because only the ones that were successfull were selected so far\n", "            \"conversion_errors\": [],  # same as above\n", "            \"plan\": multiline_str(refined_plan),\n", "            \"retrieved_triggers\": [find_trigger(workflow_lmyaml)],\n", "            \"retrieved_activities\": find_activities(workflow_lmyaml),\n", "            \"process\": workflow.to_dict()\n", "        }\n", "        # common path starting from root_path \n", "        relative_path = pathlib.Path(identifier).relative_to(root_path)\n", "        output_path = output_root_path / relative_path\n", "        output_path.parent.mkdir(exist_ok=True, parents=True)\n", "\n", "        # assert not output_path.exists(), f\"Exists {output_path}.\"\n", "        yaml_dump(workflow_datapoint_wrap, output_path)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Annotate the generation dataset with additional typedefs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import json\n", "import pathlib\n", "\n", "# workflow_aggregation_location = pathlib.Path(\"/workspace/data/workflows/parsed_portable2\")\n", "# original_data_location = pathlib.Path(\"/workspace/data/workflows/portable_converted_unzipped\")\n", "\n", "workflow_aggregation_location = pathlib.Path(\"/workspace/data/workflows/parsed_windows2\")\n", "original_data_location = pathlib.Path(\"/workspace/data/workflows/windows_converted_unzipped\")\n", "\n", "stats = collections.Counter()\n", "typedef_paths = list(original_data_location.rglob(\"**/typeDef.json\"))\n", "typedefs = {}\n", "print(len(typedef_paths))\n", "for path in typedef_paths:\n", "    with open(path) as fd:\n", "        typedef_object = json.load(fd)\n", "    if not typedef_object:\n", "        stats[\"1-empty\"] += 1\n", "        continue\n", "\n", "    workflow_identifier = workflow_aggregation_location / path.parent.relative_to(original_data_location).with_suffix('.yaml')\n", "    if not workflow_identifier.exists():\n", "        stats[\"2-not-found\"] += 1\n", "        continue\n", "\n", "    stats['3-included'] += 1\n", "    typedefs[workflow_identifier.as_posix()] = typedef_object\n", "\n", "    # print(json.dumps(typedef_object, indent=2))\n", "\n", "with open(workflow_aggregation_location / \"_typedefs.json\", \"w\") as fd:\n", "    json.dump(typedefs, fd, indent=2)\n", "print(stats.most_common())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Move the generated plans to a common directory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "data_sources = [*map(pathlib.Path, [\n", "    # \"/workspace/data/workflows/parsed_portable1\",\n", "    # \"/workspace/data/workflows/parsed_portable2\",\n", "    \"/workspace/data/workflows/parsed_windows1\",\n", "    \"/workspace/data/workflows/parsed_windows2\",\n", "])]\n", "\n", "import pathlib\n", "import shutil\n", "\n", "import tqdm\n", "\n", "planning_data_sources = [src.with_name(src.name + \"_generation\") for src in data_sources]\n", "# planning_destination = \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod/\"\n", "planning_destination = \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/prod/\"\n", "pathlib.Path(planning_destination).mkdir(exist_ok=True)\n", "\n", "for source in planning_data_sources:\n", "    # listing = list(source.iterdir())\n", "    listing = list(source.rglob(\"*.yaml\"))\n", "    for path in tqdm.tqdm(listing, desc=f\"Copying from {source}\"):\n", "        # use a relative path? balanced into directories?\n", "        # new_path = pathlib.Path(planning_destination) / path.relative_to(source)\n", "        shutil.copy2(path, planning_destination)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify credentials"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "import Levenshtein\n", "import tqdm\n", "\n", "sensitive_fields = [\n", "    \"password\", \"passwd\", \"pwd\", \"secret\", \"api_key\", \"apikey\", \"access_token\", \"auth_token\", \"bearer_token\", \"token\", \"access_key\", \"secret_key\",\n", "    \"private_key\", \"encryption_key\", \"credential\", \"credentials\", \"auth\", \"authentication\", \"username\", \"user_name\", \"user\", \"login\", \"user_id\",\n", "    \"client_id\", \"client_secret\", \"consumer_key\", \"consumer_secret\", \"db_password\", \"db_user\", \"database_password\", \"database_user\", \"db_name\",\n", "    \"database\", \"ssh_key\", \"ssh_private_key\", \"ssh_key_private\", \"ssh_password\", \"ftp_password\", \"proxy_password\", \"api_password\", \"api_secret\",\n", "    \"api_authentication\", \"certificate\", \"cert\", \"private_cert\", \"public_key\", \"ssl_cert\", \"ssl_key\", \"aws_secret_access_key\", \"azure_storage_key\",\n", "    \"gcp_key\", \"s3_access_key\", \"s3_secret_key\", \"facebook_secret\", \"twitter_secret\", \"google_client_secret\", \"linkedin_client_secret\", \"config\",\n", "    \"configuration\", \"settings\", \"setup\", \"license_key\", \"license\", \"api\", \"session_token\", \"session_id\", \"app_key\", \"app_secret\", \"secret_token\",\n", "    \"refresh_token\"\n", "]\n", "\n", "extended_sensitive_fields = [\n", "    \"admin_password\", \"admin_user\", \"root_password\", \"service_account\", \"service_account_password\", \"api_login\", \"api_password\", \"api_url\", \"endpoint\",\n", "    \"uri\", \"base_url\", \"webhook_url\", \"network_password\", \"router_password\", \"wifi_password\", \"connection_string\", \"connect_uri\", \"data_source\",\n", "    \"database_url\", \"jdbc_url\", \"symmetric_key\", \"asymmetric_key\", \"encryption_algorithm\", \"signature\", \"tls_cert\", \"tls_key\", \"pem_file\", \"aws_access_key_id\",\n", "    \"azure_key\", \"gcp_service_account_key\", \"digitalocean_access_token\", \"heroku_api_key\", \"oauth_token\", \"oauth_consumer_key\", \"oauth_consumer_secret\",\n", "    \"oauth_access_token\", \"oauth_access_secret\", \"stripe_key\", \"paypal_secret\", \"callback_url\", \"redirect_uri\", \"auth_url\", \"logout_url\", \"notify_url\",\n", "    \"status_callback_url\", \"storage_bucket_url\", \"file_path\", \"master_key\", \"backup_code\", \"recovery_code\", \"pin\", \"security_question\", \"security_answer\",\n", "    \"license_number\", \"registration_id\", \"customer_id\", \"order_id\", \"payment_id\", \"invoice_id\", \"credit_card_number\", \"cvv\", \"expiry_date\",\n", "    \"ssn\", \"passport_number\", \"driver_license_number\", \"address\", \"phone_number\", \"email\", \"date_of_birth\", \"url\"\n", "]\n", "sensitive_fields += extended_sensitive_fields\n", "# display(sorted(sensitive_fields + extended_sensitive_fields, key=lambda s: len(s)))\n", "# raise\n", "\n", "false_positive_fields = [\n", "    \"text\", \"then\", \"ispassword\", \"cell\", \"port\", \"lookin\", \"left\", \"authenticationtype\", \"Encryption\",\n", "    \"EncryptionAlgorithm\", \"Min\", \"ConfigLocation\", \"PageNumber\", \"AuthenticationMode\", \"ParentId\",\n", "    \"Expiry Date\"\n", "]\n", "false_positive_fields = [x.lower() for x in false_positive_fields]\n", "\n", "var_re = re.compile(\"\\[\\[.*\\]\\]( -> .*)?\")\n", "\n", "def redact(workflow):\n", "    if isinstance(workflow, dict):\n", "        for k, v in workflow.items():\n", "            if any(Levenshtein.distance(k.lower(), sf) <= len(sf) // 3 for sf in sensitive_fields):  # tolerance: 1 alteration for each 3 chars in ground truth\n", "                if isinstance(v, str) and var_re.match(v):  # if it's a variable\n", "                    continue\n", "                if k.lower() in false_positive_fields:\n", "                    continue\n", "                yield k, v\n", "                # workflow[k] = \"REDACTED\"\n", "            yield from redact(v)\n", "    if isinstance(workflow, list):\n", "        for v in workflow:\n", "            yield from redact(v)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Redact plans and generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["planning_destination = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod/\")\n", "# planning_destination = pathlib.Path(\"/workspace/data/workflows/parsed_windows2_generation\")\n", "for datapoint_path in tqdm.tqdm(list(planning_destination.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(datapoint_path)\n", "\n", "    to_redact = list(redact(datapoint))\n", "\n", "    if to_redact:\n", "        replacements = {}\n", "        print(datapoint_path)\n", "        for k, v in to_redact:\n", "            print(k, v)\n", "            replacements[f\"{k}: {v}\"] = f\"{k}: REDACTED\"\n", "        with open(datapoint_path, \"r\") as fd:\n", "            content = fd.read()\n", "        with open(datapoint_path, \"w\") as fd:\n", "            for k, v in replacements.items():\n", "                content = content.replace(k, v)\n", "            fd.write(content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Redact activity configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "seen_so_far = {'Encryption', 'EndPoint', 'FilePath', 'FolderPath', 'Password', 'StorageBucketName', 'Url', 'Username', 'token'}\n", "seen_so_far |= {'ApiKey', 'AuthenticationMode', 'Authorization', 'Client', 'ClientId', 'ClientSecret', 'ConfigLocation', 'ConnectionString', 'ConsumerKey', 'CredentialID', 'Customer', 'Email', 'Encryption', 'EncryptionAlgorithm', 'EndPoint', 'Endpoint', 'Expiry Date', 'FilePath', 'FolderPath', 'Min', 'PASSWORD', 'PageNumber', 'ParentId', 'Password', 'ProcessNumber', 'StorageBucketName', 'Surname', 'USERNAME', 'Url', 'UserName', 'Username', 'api-key', 'client_id', 'client_secret', 'email', 'filePath', 'orderid', 'password', 'redirect_uri', 'token', 'username'}\n", "\n", "# activity_config_data_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod\")\n", "# activity_config_data_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows/prod\")\n", "\n", "# planning_data_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod\")\n", "planning_data_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/prod\")\n", "\n", "# for datapoint_path in tqdm.tqdm(list(activity_config_data_path.rglob(\"*.yaml\"))):\n", "for datapoint_path in tqdm.tqdm(list(planning_data_path.rglob(\"*.yaml\"))):\n", "    if datapoint_path.is_dir():\n", "        continue\n", "    datapoint = yaml_load(datapoint_path)\n", "\n", "    to_redact = list(redact(datapoint))\n", "\n", "    if to_redact:\n", "        replacements = {}\n", "        # print(datapoint_path)\n", "        for k, v in to_redact:\n", "            if k not in seen_so_far:\n", "                tqdm.tqdm.write(f\"{k}, {v}\")\n", "                seen_so_far.add(k)\n", "            replacements[f\"{k}: {v}\"] = f\"{k}: REDACTED\"\n", "        with open(datapoint_path, \"r\") as fd:\n", "            content = fd.read()\n", "        with open(datapoint_path, \"w\") as fd:\n", "            for k, v in replacements.items():\n", "                content = content.replace(k, v)\n", "            fd.write(content)  # doing it like this so we don't have to go through the yaml redumping\n", "display(set(sorted(seen_so_far)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Rename directories without extension \".yaml\" and \".xaml\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "import pathlib\n", "import shutil\n", "\n", "import tqdm\n", "\n", "# root_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Portable/prod\")\n", "root_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/ActivityConfiguration/Windows/prod\")\n", "\n", "for path in tqdm.tqdm(list(root_path.rglob(\"*\"))):\n", "    if path.is_dir() and path.suffix in {\".yaml\", \".xaml\"}:\n", "        new_path = path\n", "        while new_path.suffix in {\".yaml\", \".xaml\"}:\n", "            new_path = new_path.with_suffix(\"\")\n", "        # print(path)\n", "        # print(new_path)\n", "        # print()\n", "        shutil.move(path, new_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Modify the worfklow thoughts with the ones from the plan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import re\n", "\n", "from services.studio._text_to_workflow.common.walkers import PlanBuilder, WorkflowThoughtsModifier\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import multiline_str, yaml_dump, yaml_load\n", "\n", "indexing_re = re.compile(r'^\\s*(\\d+\\.)*\\s')\n", "\n", "def strip_plan(plan):\n", "    lines = plan.split('\\n')\n", "    if lines and lines[-1] == \"\":\n", "        lines = lines[:-1]\n", "    return '\\n'.join(m.group(0).replace('  ', '\\t')  # this replacement for dealing with \\t vs \"  \" confusion\n", "                     if (m:=indexing_re.search(line)) else \"None\" for line in lines)\n", "\n", "def get_diff_lines(a, b):\n", "    return \"\".join(difflib.ndiff(a.splitlines(keepends=True), b.splitlines(keepends=True)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["portable_prod_path = \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod\"\n", "windows_prod_path = \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Windows/prod\"\n", "all_workflows_paths = list(pathlib.Path(portable_prod_path).rglob(\"*.yaml\")) + list(pathlib.Path(windows_prod_path).rglob(\"*.yaml\"))\n", "\n", "# allowlist = {\n", "#     \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod/processed_9ec00359-5cb1-4be9-66a9-b67ff1e17951_CE_DoubleValues.yaml\",\n", "#     \"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod/processed_5fb5c240-beba-e27b-b457-feb4501900b4_GetDataForPerformerExample.yaml\",\n", "# }\n", "\n", "import collections\n", "\n", "counter = collections.Counter()\n", "for path in all_workflows_paths:\n", "    content = path.open().read()\n", "    if \"UiPath.Core.Activities.InterruptibleWhile\" in content:\n", "        continue  # temporary skip until we reconvert production data\n", "    datapoint = yaml_load(content)\n", "    # fix packages having list of lists\n", "    if \"packages\" in datapoint[\"process\"]:\n", "        packages = datapoint[\"process\"][\"packages\"]\n", "        if isinstance(packages, list) and packages and isinstance(packages[0], list):\n", "            assert len(packages) == 1, f\"Missed case {packages}\"\n", "            datapoint[\"process\"][\"packages\"] = packages[0]\n", "    workflow = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "    original_plan = PlanBuilder().build(workflow)\n", "\n", "    refined_plan = datapoint[\"plan\"].strip()\n", "    if strip_plan(original_plan) != strip_plan(refined_plan):\n", "        counter[\"failed-structure\"] += 1\n", "        path.unlink()\n", "        # print(path)\n", "        # print(get_diff_lines(strip_plan(original_plan), strip_plan(refined_plan)))\n", "        # print(original_plan)\n", "        # print(refined_plan)\n", "        # print('\\n' * 5)\n", "        continue\n", "\n", "    refined_plan_thoughts = [re.sub(r\"^\\s*(\\d+\\.)+\\s*\", \"\", step) for step in refined_plan.split(\"\\n\")]\n", "    try:\n", "        WorkflowThoughtsModifier(refined_plan_thoughts, suppress_warning=True).replace(workflow)\n", "    except Exception as e:\n", "        print(path)\n", "        counter[\"failed-thoughts\"] += 1\n", "        print(f\"Error in {path}\")\n", "        print(e)\n", "        print(datapoint[\"plan\"], end='\\n\\n')\n", "        print(original_plan, end='\\n\\n')\n", "        continue\n", "    counter[\"success\"] += 1\n", "    # print(PlanBuilder().build(workflow), end='\\n\\n\\n\\n\\n')\n", "\n", "    datapoint[\"process\"] = workflow.to_dict()\n", "    new_datapoint = {}\n", "    for k, v in datapoint.items():\n", "        if k == \"plan\":\n", "            new_datapoint[\"plan-original\"] = multiline_str(original_plan)\n", "            new_datapoint[\"plan\"] = multiline_str(refined_plan)\n", "            continue\n", "        new_datapoint[k] = v\n", "    \n", "    # new_path = path.with_stem(path.stem + \"__test\")\n", "    # print(new_path)\n", "    # yaml_dump(new_datapoint, new_path)\n", "    # yaml_dump(new_datapoint, path)\n", "\n", "counter.most_common()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/prod/processed_9ec00359-5cb1-4be9-66a9-b67ff1e17951_queueClean.yaml\")\n", "path = pathlib.Path(\"/workspace/data/workflows/parsed_portable2/processed_c1910ee7-1791-f365-f58a-426f940f0fd4_Main.yaml\")\n", "datapoint = yaml_load(path)\n", "# datapoint[\"process\"][\"packages\"] = datapoint[\"process\"][\"packages\"][0]\n", "# workflow = Workflow(\"\", \"\", datapoint[\"process\"])\n", "workflow = Workflow(\"\", \"\", datapoint)\n", "print(workflow.lmyaml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clean stale dataset summarization examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import itertools\n", "\n", "from services.studio._text_to_workflow.common.walkers import ActivityIdCollector\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "train_path = paths.get_workflow_summarization_dataset_path(\"Portable\", \"train\")\n", "test_path = paths.get_workflow_summarization_dataset_path(\"Portable\", \"test\")\n", "# uia_path = paths.get_workflow_summarization_dataset_path(\"Portable\", \"uia\")\n", "\n", "for example_path in itertools.chain(train_path.rglob(\"*.yaml\"), test_path.rglob(\"*.yaml\")):\n", "    example = yaml_load(example_path)\n", "    workflow = Workflow(\"\", \"\", example[\"input\"])\n", "    ids = ActivityIdCollector().collect(workflow)\n", "    if any(id_.endswith(\"_1\") for id_ in ids):\n", "        # print(f\"Removing {example_path}\")\n", "        # example_path.unlink()\n", "        print(ids)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate variable name that comes as bool workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_path = \"/workspace/data/workflows/windows_20240805\"\n", "\n", "from services.studio._text_to_workflow.workflow_generation.scripts.deduplicate import load_datapoints\n", "\n", "datapoints = load_datapoints(root_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path, dp in datapoints.items():\n", "    for var in dp[\"process\"].get(\"variables\", []):\n", "        if not isinstance(var[\"name\"], str):\n", "            print(path, type(var[\"name\"]))\n", "            print(var)\n", "    for var in dp[\"process\"].get(\"arguments\", []):\n", "        if not isinstance(var[\"name\"], str):\n", "            print(path, type(var[\"name\"]))\n", "            print(var)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate issues with metadata.yaml"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# There are some keys that are duplicated\n", "from services.studio._text_to_workflow.utils import paths, workflow_utils, yaml_utils\n", "\n", "yaml_paths = list(paths.get_workflow_generation_dataset_path(\"Portable\", \"train\").rglob(\"*.yaml\"))\n", "yaml_paths += list(paths.get_workflow_generation_dataset_path(\"Portable\", \"test\").rglob(\"*.yaml\"))\n", "wfgen_stems = set(path.stem for path in yaml_paths)\n", "# sorted(yaml_paths)\n", "\n", "metadata_path = paths.get_workflow_generation_metadata_path(\"Portable\")\n", "\n", "metadata = yaml_utils.yaml_load(metadata_path)\n", "for identifier, metadatapoint in list(metadata.items()):\n", "    if identifier not in wfgen_stems:\n", "        print(identifier)\n", "        del metadata[identifier]\n", "        continue\n", "\n", "        # Invesetigate contents\n", "        _, _, _, remaining = identifier.split(\"_\", 3)  # remove \"Tag_Tag_Tag_\"\n", "        while remaining and remaining[0] == \"[\":  # remove \"[Tag] ...\"\n", "            _, remaining = remaining.split(\"]\", 1)\n", "            remaining = remaining.strip()\n", "        parts = remaining.split('_')\n", "        core = \"_\".join(parts[:len(parts) // 2])  # \"<core>_<core>\"\n", "        prefix = identifier[:identifier.index(core)]\n", "\n", "        original = prefix + core\n", "        if original not in wfgen_stems:\n", "            print(\"Not found\", identifier)\n", "            continue\n", "        if metadata[original] == metadatapoint:\n", "            print(\"Duplicate\", identifier)\n", "        else:\n", "            print(\"Different\", identifier)\n", "            print(workflow_utils.get_diff(yaml_utils.yaml_dump(metadata[original]), yaml_utils.yaml_dump(metadatapoint), 5))\n", "\n", "\n", "for identifier, metadatapoint in list(metadata.items()):\n", "    metadata[identifier] = {k: yaml_utils.multiline_str(v) if \"plan\" in k else v for k, v in metadatapoint.items()}\n", "yaml_utils.yaml_dump(metadata, metadata_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Manually convert a workflow xaml to yaml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import shutil\n", "import tempfile\n", "\n", "from services.studio._text_to_workflow.autopilot_dataset.online_client import convert_workflow\n", "\n", "# import dotenv\n", "# dotenv.load_dotenv()\n", "\n", "# input_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/static/StudioDesktop_LocalPath\")\n", "# input_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/static/BlankProcess404\")\n", "input_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/SequenceGeneration/Windows/static/LogFilesAndExcelRows\")\n", "# output_path = input_path / f\"converted\"\n", "output_path = input_path\n", "\n", "with tempfile.TemporaryDirectory() as tempdir:\n", "    tempdir = pathlib.Path(tempdir)\n", "    input_zip = tempdir / f\"{input_path.stem}.zip\"\n", "    shutil.make_archive(input_zip.with_suffix(\"\").as_posix(), \"zip\", input_path.as_posix())\n", "    assert input_zip.exists()\n", "    convert_workflow(\n", "        zip_path=input_zip.as_posix(),\n", "        output_folder=output_path.as_posix(),\n", "        target_platform=\"windows\",\n", "        jobid=\"\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load\n", "\n", "for path in output_path.glob(\"*.json\"):\n", "    if \".typedef.\" in path.name:\n", "        continue\n", "    with open(path) as fd:\n", "        conversion_result = json.load(fd)\n", "    \n", "    conversion_success = conversion_result[\"Success\"]\n", "    if not conversion_success:\n", "        print(f\"Warning! Conversion failed for {path}.\")\n", "        continue\n", "    \n", "    has_errors = len(conversion_result[\"Errors\"]) > 0\n", "    if has_errors:\n", "        print(f\"Warning! Conversion has errors for {path}.\")\n", "        print(json.dumps(conversion_result[\"Errors\"], indent=2))\n", "        continue\n", "\n", "    workflow = yaml_load(conversion_result[\"result\"])\n", "    yaml_dump(workflow, path.with_suffix(\".yaml\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.workflow_generation_dataset import _sqgen_generate_cutting_points, create_dataset\n", "\n", "converted_source = output_path\n", "worklow_generation_destination = output_path\n", "\n", "with tempfile.TemporaryDirectory() as tempdir:\n", "    tempdir = pathlib.Path(tempdir)\n", "\n", "    dataset_mock_converted_dir = tempdir / \"converted_input\"\n", "    shutil.copytree(converted_source, dataset_mock_converted_dir / output_path.name)\n", "\n", "    print(\"Listing of input conversion dataset dir\")\n", "    for path in dataset_mock_converted_dir.rglob(\"*\"):\n", "        print(path)\n", "\n", "    dataset_mock_wfgen_output = tempdir / \"wfgen_output\"\n", "    dataset_mock_wfgen_output.mkdir()\n", "    with open(dataset_mock_wfgen_output / \"subsets.yaml\", \"w\") as fd:\n", "        json.dump({}, fd)\n", "    create_dataset(dataset_mock_converted_dir, dataset_mock_wfgen_output)\n", "\n", "    print(\"Listing of output dataset dir\")\n", "    yaml_paths = []\n", "    for path in dataset_mock_wfgen_output.rglob(\"*.yaml\"):\n", "        print(path)\n", "        if path.name == \"subsets.yaml\":\n", "            continue\n", "        yaml_paths.append(path)\n", "        shutil.copy2(path, worklow_generation_destination / path.name)\n", "\n", "    # seqgen extraction\n", "    for path in yaml_paths:\n", "        datapoint = yaml_load(path)\n", "        workflow = Workflow(datapoint[\"description\"], datapoint[\"plan\"], datapoint[\"process\"])\n", "        cutting_points = _sqgen_generate_cutting_points(workflow)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}