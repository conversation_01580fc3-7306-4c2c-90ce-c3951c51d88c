{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "\n", "def get_fix_workflow_dataset_path() -> pathlib.Path:\n", "    return pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create dataset by gatheing .html evaluations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from services.studio._text_to_workflow.workflow_generation.scripts.aggregate_htmls import load_data\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "import re\n", "import html\n", "import collections\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workdir/\")\n", "\n", "# take all\n", "html_paths = sorted(\n", "    [\n", "        *root_path.glob(\"output*.html\"),\n", "        *(root_path / \"_archive_evaluations\").glob(\"output*.html\"),\n", "    ]\n", ")\n", "\n", "# take a subset\n", "index_file = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only.txt\")\n", "filenames = set(index_file.read_text().splitlines())\n", "print(f\"Looked for {len(filenames)} files.\")\n", "\n", "html_paths = [p for p in html_paths if p.name in filenames]\n", "\n", "\n", "# display(html_paths)\n", "print(f\"Found {len(html_paths)} html files.\")\n", "loaded_datapoints, loaded_aggregates = load_data(html_paths)\n", "dataset = collections.defaultdict(list)\n", "for element in loaded_datapoints:\n", "    # if \"plan_sequence_ideal\" in element:\n", "    #     continue  # means it's sequence, skip it\n", "    html_identifier = element[\"identifier\"]\n", "\n", "    remaining = html.unescape(element[\"_remaining\"])\n", "    error_group_re = re.compile(\n", "        r\"\"\"partially_deserializable: (?P<partially_deserializable>[^\\n]*)\n", "fully_deserializable: (?P<fully_deserializable>[^\\n]*)\n", "invalid: (?P<invalid>[^\\n]*)\n", "output: '(?P<output>[^\\n]*)'\n", "errors:\\s?(?P<errors>.*?)\\n\\n\"\"\",\n", "        re.DOTALL,\n", "    )\n", "    matches = list(error_group_re.finditer(remaining))\n", "    if not matches:\n", "        print(f\"Could not find section {html_identifier}\")\n", "        continue\n", "\n", "    info = matches[0].groupdict()\n", "    info[\"errors\"] = [] if info[\"errors\"] == \"[]\" else [msg[2:] for msg in info[\"errors\"].splitlines()]\n", "    if not info[\"errors\"]:\n", "        # print(\"No errors\")\n", "        continue\n", "\n", "    filepath_match = re.search(r\"Ground Truth Filename:\\s*(?P<filepath>.*)\", remaining)\n", "    if not filepath_match:\n", "        print(f\"Could not find filename {html_identifier}\")\n", "        continue\n", "    wfgen_filepath = pathlib.Path(filepath_match.group(\"filepath\"))\n", "    # print(f\"Before {wfgen_filepath}\")\n", "    # get rid of sequence generation prefix\n", "    wfgen_filepath = wfgen_filepath.with_stem(re.sub(r\"^\\d{2,}__\\d+_\\d+_\", \"\", wfgen_filepath.stem))\n", "    # print(f\"After  {wfgen_filepath}\")\n", "\n", "    if \"workflow_valid\" not in element:\n", "        print(f\"Could not find valid workflow {html_identifier}\")\n", "        continue\n", "    workflow_identifier = wfgen_filepath.stem\n", "    workflow_generated = element[\"workflow_valid\"]\n", "    if not workflow_generated:\n", "        print(f\"Valid workflow is invalid {html_identifier}\")\n", "        continue\n", "\n", "    datapoint = {\n", "        \"errors\": info,\n", "        \"workflow\": yaml_load(workflow_generated),\n", "    }\n", "    dataset[workflow_identifier].append(datapoint)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(dataset))\n", "print(sum(len(v) for v in dataset.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Starting with:\")\n", "print(len(dataset))\n", "print(sum(map(len, dataset.values())))\n", "\n", "# dump_path = pathlib.Path(\"/workspace/data/workdir/Autopilot.Samples/Dataset\")\n", "# dump_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "dump_path = get_fix_workflow_dataset_path()\n", "dump_path.mkdir(exist_ok=True)\n", "\n", "for identifier, datapoints in list(dataset.items()):\n", "    # datapoints = {yaml_dump(dp[\"workflow\"]): dp for dp in datapoints}.values()  # make unique by workflow\n", "    datapoints = {tuple(sorted(dp[\"errors\"][\"errors\"])): dp for dp in datapoints}.values()  # make unique by errors\n", "    dataset[identifier] = datapoints\n", "\n", "print(\"Deduplicated\")\n", "print(len(dataset))\n", "print(sum(map(len, dataset.values())))\n", "\n", "# assert False  # stop here\n", "\n", "for identifier, datapoints in dataset.items():\n", "    parent_dir = dump_path / identifier\n", "    parent_dir.mkdir(exist_ok=True)\n", "    for i, datapoint in enumerate(datapoints, 1):\n", "        # print(parent_dir / f\"Fix{i}__{identifier}.yaml\")\n", "        yaml_dump(datapoint, parent_dir / f\"Fix{i}__{identifier}.yaml\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate ground truth by using the fix-activity endpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "# for path in fix_workflow_dataset_path.rglob(\"*.yaml\"):\n", "#     datapoint = yaml_load(path)\n", "#     if \"predictions\" in datapoint:\n", "#         del datapoint[\"predictions\"]\n", "#     yaml_dump(datapoint, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "\n", "wfgen_identifier_to_framework = {}\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for path in paths.get_workflow_generation_dataset_path(framework).rglob(\"*.yaml\"):\n", "        if path.stem in {\"metadata\", \"subsets\"}:\n", "            continue\n", "        wfgen_identifier_to_framework[path.stem] = framework\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "for path in fix_workflow_dataset_path.rglob(\"*.yaml\"):\n", "    datapoint = yaml_load(path)\n", "    wfgen_identifier = path.parent.name\n", "    if wfgen_identifier not in wfgen_identifier_to_framework:\n", "        print(f\"Could not find framework for {wfgen_identifier}\")\n", "        path.unlink()\n", "        continue\n", "    datapoint[\"target_framework\"] = wfgen_identifier_to_framework[wfgen_identifier]\n", "    yaml_dump(datapoint, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(path)\n", "    workflow = datapoint[\"workflow\"]\n", "    if not workflow:\n", "        print(\"Removing empty\", path)\n", "        path.unlink()\n", "    if not datapoint[\"target_framework\"]:\n", "        print(\"Removing tf\", path)\n", "        path.unlink()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_identifiers = len([*fix_workflow_dataset_path.glob(\"*\")])\n", "n_generations = len([*fix_workflow_dataset_path.rglob('*.yaml')])\n", "print(f\"There are {n_generations} workflows from {n_identifiers} identifiers.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "errors = collections.Counter()\n", "for path in fix_workflow_dataset_path.rglob(\"*.yaml\"):\n", "    datapoint = yaml_load(path)\n", "    datapoint[\"errors\"]\n", "    errors.update(datapoint[\"errors\"][\"errors\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, v in errors.most_common():\n", "    print(f\"{v:4} {k}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.connections_loader import get_connections_data\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "from services.studio._text_to_workflow.utils import request_schema\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "import pathlib\n", "import tqdm\n", "import datetime\n", "\n", "model_options: request_schema.ModelOptions = {\n", "    \"model_name\": None,\n", "    \"seed\": \"42\",\n", "}\n", "\n", "workflow_fix_task = await WorkflowFixTask(\"config.yaml\").load()\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Dataset\")\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "print(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))[0])\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(path)\n", "    workflow = datapoint[\"workflow\"]\n", "    workflow_as_str = yaml_dump(workflow)\n", "    errors = datapoint[\"errors\"][\"errors\"]\n", "    wfgen_identifier = path.parent.name\n", "    if wfgen_identifier not in wfgen_identifier_to_framework:  # TODO: check the one missing\n", "        print(f\"Could not find framework for {wfgen_identifier}\")\n", "        continue\n", "    if datapoint.get(\"predictions\"):  # already predicted\n", "        print(f\"Already processed {path}\")\n", "        continue\n", "    target_framework = wfgen_identifier_to_framework[wfgen_identifier]\n", "    try:\n", "        tenant_id, connections = get_connections_data()\n", "        result = await workflow_fix_task.run_fix_workflow(workflow_as_str, errors, \"\", connections, target_framework, model_options)\n", "    except Exception as e:\n", "        print(f\"Failed to fix {path}: {e}\")\n", "        continue\n", "    result[\"timestamp\"] = datetime.datetime.now().isoformat()\n", "    result[\"usage\"] = result[\"usage\"]\n", "    datapoint[\"predictions\"] = datapoint.get(\"predictions\", []) + [result]\n", "    yaml_dump(datapoint, path)\n", "    # break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Validate first set of predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import validate_workflow_format\n", "\n", "async def validate(workflow_str):\n", "    validation_input = {\n", "        \"result\": workflow_str,\n", "        \"jitCommands\": [],  # not used for the moment\n", "        \"connectors\": [],  # not used for the moment\n", "    }\n", "    response = await validate_workflow_format(validation_input, oneline_errors=True)\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "import tqdm\n", "import pathlib\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "\n", "\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    print(path)\n", "    datapoint = yaml_load(path)\n", "    if not datapoint:\n", "        print(f\"Removing empty {path}\")\n", "        path.unlink()\n", "        continue\n", "    if \"predictions\" not in datapoint:\n", "        print(f\"No predictions for {path}\")\n", "        continue  # no predictions\n", "    _validations_done = 0\n", "    for prediction in datapoint[\"predictions\"]:\n", "        if \"validation\" in prediction:\n", "            continue  # already validated\n", "        _validations_done += 1\n", "        if \"fixedWorkflow\" not in prediction:\n", "            print(f\"No fixedWorkflow for {path}\")\n", "            continue\n", "        prediction[\"validation\"] = await validate(prediction[\"fixedWorkflow\"])\n", "    if _validations_done:\n", "        yaml_dump(datapoint, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import pathlib\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "\n", "all_errors_mitigated = set()\n", "all_errors_remaining = set()\n", "all_errors_introduced = set()\n", "\n", "stats = collections.Counter()\n", "count_errors_mitigated = collections.Counter()\n", "count_errors_introduced = collections.Counter()\n", "count_errors_remaining = collections.Counter()\n", "errors_mitigated = collections.Counter()\n", "errors_introduced = collections.Counter()\n", "errors_remaining = collections.Counter()\n", "\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    stats[\"total\"] += 1\n", "    datapoint = yaml_load(path)\n", "    if \"predictions\" not in datapoint:\n", "        print(f\"No predictions for {path}\")\n", "        stats[\"without_prediction\"] += 1\n", "        continue  # no predictions\n", "    errors = set(datapoint[\"errors\"][\"errors\"])\n", "\n", "    if len(predictions := datapoint[\"predictions\"]) == 2:\n", "        continue\n", "    stats[\"total_batch2\"] += 1\n", "\n", "    prediction = datapoint[\"predictions\"][-1]\n", "    # for prediction in :\n", "    if \"validation\" not in prediction:\n", "        print(f\"No validation for {path}\")\n", "        stats[\"without_validation\"] += 1\n", "        continue\n", "\n", "    current_errors = set(prediction[\"validation\"][\"errors\"])\n", "    mitigated_errors = errors - current_errors\n", "    introduced_errors = current_errors - errors\n", "    if current_errors:\n", "        stats[\"still_with_errors\"] += 1\n", "    else:\n", "        stats[\"fixed\"] += 1\n", "\n", "    stats[\"mitigated_errors\"] += len(mitigated_errors)\n", "    stats[\"introduced_errors\"] += len(introduced_errors)\n", "    stats[\"remaining_errors\"] += len(current_errors)\n", "\n", "    all_errors_mitigated.update(mitigated_errors)\n", "    all_errors_introduced.update(introduced_errors)\n", "    all_errors_remaining.update(current_errors)\n", "\n", "    count_errors_mitigated[len(mitigated_errors)] += 1\n", "    count_errors_introduced[len(introduced_errors)] += 1\n", "    count_errors_remaining[len(current_errors)] += 1\n", "\n", "    errors_mitigated.update(mitigated_errors)\n", "    errors_introduced.update(introduced_errors)\n", "    errors_remaining.update(current_errors)\n", "\n", "print(\"Stats:\")\n", "for _stat, count in stats.most_common():\n", "    print(f\"{count:4} {_stat}\")\n", "print('\\n' * 3)\n", "\n", "print(\"Distribution of workflows with #errors mitigated\")\n", "for count, count_ in count_errors_mitigated.most_common():\n", "    print(f\"{count:4} errors mitigated: {count_:3} workflows\")\n", "\n", "print(\"Distribution of workflows with #errors introduced\")\n", "for count, count_ in count_errors_introduced.most_common():\n", "    print(f\"{count:4} errors introduced: {count_:3} workflows\")\n", "\n", "print(\"Distribution of workflows with #errors remaining\")\n", "for count, count_ in count_errors_remaining.most_common():\n", "    print(f\"{count:4} errors remaining: {count_:3} workflows\")\n", "print('\\n' * 3)\n", "\n", "print(\"Unique errors\")\n", "print(f\"Mitigated {len(all_errors_mitigated)}\")\n", "print(f\"Remaining {len(all_errors_remaining)}\")\n", "print(f\"Introduced {len(all_errors_introduced)}\")\n", "print('\\n' * 3)\n", "\n", "print(\"Errors stats\")\n", "print(f\"Mitigated {sum(errors_mitigated.values())}\")\n", "print(f\"Remaining {sum(errors_remaining.values())}\")\n", "print(f\"Introduced {sum(errors_introduced.values())}\")\n", "print('\\n' * 3)\n", "\n", "print(\"# region: ######## Errors mitigated ########\")\n", "for error, count in errors_mitigated.most_common():\n", "    print(f\"{count:4} {error}\")\n", "print('\\n' * 3)\n", "\n", "print(\"# region: ######## Errors introduced ########\")\n", "for error, count in errors_introduced.most_common():\n", "    print(f\"{count:4} {error}\")\n", "print('\\n' * 3)\n", "\n", "print(\"# region: ######## Errors remaining ########\")\n", "for error, count in errors_remaining.most_common():\n", "    print(f\"{count:4} {error}\")\n", "print('\\n' * 3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Attempt multiple times predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openai\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixResponse\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "from services.studio._text_to_workflow.utils import request_schema\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.workflow_fix.dataset_creation.create_from_wfgen import refresh_token\n", "import pathlib\n", "import tqdm\n", "import datetime\n", "\n", "model_options: request_schema.ModelOptions = {\n", "    \"model_name\": None,\n", "    \"seed\": \"42\",\n", "}\n", "\n", "PREDICTIONS_AT = 1  # predict only for thos that have 1 prediction already, which still has errors\n", "\n", "async def wrapped_predict(workflow_as_str, errors, target_framework) -> WorkflowFixResponse | None:\n", "    for _ in range(3):\n", "        try:\n", "            tenant_id, connections = get_connections_data()\n", "            return await workflow_fix_task.run_fix_workflow(workflow_as_str, errors, \"\", connections, target_framework, model_options)\n", "        except openai.AuthenticationError:\n", "            print(\"Attempting to refresh token\")\n", "            await refresh_token()\n", "            continue\n", "        except Exception as e:\n", "            print(f\"Failed to fix {e} {path}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            break\n", "\n", "\n", "workflow_fix_task = await WorkflowFixTask(\"config.yaml\").load()\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "print(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))[0])\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(path)\n", "    if \"target_framework\" not in datapoint:\n", "        continue\n", "    target_framework = datapoint[\"target_framework\"]\n", "\n", "    if len(datapoint.get(\"predictions\", [])) != PREDICTIONS_AT:\n", "        continue\n", "    prediction = datapoint[\"predictions\"][-1]\n", "\n", "    if \"fixedWorkflow\" not in prediction:\n", "        continue\n", "    workflow_as_str = prediction[\"fixedWorkflow\"] if isinstance(prediction[\"fixedWorkflow\"], str) else yaml_dump(prediction[\"fixedWorkflow\"])\n", "\n", "    if \"validation\" not in prediction:\n", "        print(f\"Warning! validation not in {path}\")\n", "        continue\n", "    errors = prediction[\"validation\"][\"errors\"]\n", "    if not errors:\n", "        continue  # was fixed\n", "\n", "    try:\n", "        result = await wrapped_predict(workflow_as_str, errors, target_framework)\n", "    except Exception as e:\n", "        print(f\"Failed to fix {path}: {e}\")\n", "        continue\n", "    if result is None:\n", "        print(f\"Failed to fix {path}\")\n", "        continue\n", "\n", "    result[\"timestamp\"] = datetime.datetime.now().isoformat()\n", "    result[\"usage\"] = result[\"usage\"]\n", "    datapoint[\"predictions\"].append(result)\n", "    # print(yaml_dump(datapoint))\n", "    # break\n", "    yaml_dump(datapoint, path)\n", "    # break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prettify dataset by loading the yaml strings in fixedWorkflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump, multiline_str\n", "import pathlib\n", "import tqdm\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Dataset\")\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(path)\n", "    if \"predictions\" not in datapoint:\n", "        print(f\"No predictions for {path}\")\n", "        continue  # no predictions\n", "    for prediction in datapoint[\"predictions\"]:\n", "        # for k, v in prediction[\"prompts\"].items():\n", "        #     prediction[\"prompts\"][k] = multiline_str(v)]\n", "        if \"fixedWorkflow\" in prediction and isinstance(prediction[\"fixedWorkflow\"], str):\n", "            prediction[\"fixedWorkflow\"] = yaml_load(prediction[\"fixedWorkflow\"])\n", "        # prediction[\"fixedWorkflow\"] = yaml_load(prediction[\"fixedWorkflow\"])\n", "    yaml_dump(datapoint, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import unescape_quotes\n", "import yaml\n", "\n", "class multiline_str(str):\n", "    pass\n", "\n", "\n", "def multiline_str_presenter(dumper, data):\n", "    return dumper.represent_scalar(\"tag:yaml.org,2002:str\", str(data), style=\"|\")\n", "\n", "\n", "yaml.add_representer(multiline_str, multiline_str_presenter)\n", "yaml.representer.SafeRepresenter.add_representer(multiline_str, multiline_str_presenter)\n", "\n", "\n", "prediction[\"prompts\"][\"fix\"] = multiline_str(unescape_quotes(prediction[\"prompts\"][\"fix\"]))\n", "print(yaml_dump(prediction[\"prompts\"][\"fix\"][:5897], default_style=\"|\"))\n", "# print(prediction[\"prompts\"][\"fix\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Design some test cases"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import validate_workflow_format\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "import pathlib\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Dataset\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "workflow_fix_task = WorkflowFixTask(\"config.yaml\").load()\n", "\n", "def validate(workflow_str):\n", "    validation_input = {\n", "        \"result\": workflow_str,\n", "        \"jitCommands\": [],  # not used for the moment\n", "        \"connectors\": [],  # not used for the moment\n", "    }\n", "    response = validate_workflow_format(validation_input, oneline_errors=True)\n", "    return response\n", "\n", "\n", "def get_errors(workflow_as_str):\n", "    return validate(workflow_as_str)[\"errors\"]\n", "\n", "\n", "def fix_workflow(workflow_str, errors, target_framework=\"Portable\"):\n", "    tenant_id, connections = get_connections_data()\n", "    return workflow_fix_task.run_fix_workflow(workflow_str, errors, \"\", target_framework, connections, None)\n", "\n", "\n", "test_fixture_path = pathlib.Path(\"/workspace/src-visitors/studio/text_to_workflow/tests/fixtures/workflow_fix_fixtures/send_email_on_onedrive_upload.yaml\")\n", "workflow_dict = yaml_load(test_fixture_path)\n", "workflow_as_str = yaml_dump(workflow_dict)\n", "\n", "errors = get_errors(yaml_dump(workflow_dict))\n", "print(\"Errors before:\", errors)\n", "\n", "response = fix_workflow(workflow_as_str, errors)\n", "repaired_workflow_as_str = response[\"fixedWorkflow\"]\n", "print(\"Reasoning:\", response[\"reasoning\"])\n", "print(\"Errors after:\", get_errors(repaired_workflow_as_str))\n", "print(get_diff(workflow_as_str, repaired_workflow_as_str))\n", "\n", "# workflow_altered = copy.deepcopy(workflow_dict)\n", "# workflow_altered[\"\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analyze the completion tokens distribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "import pathlib\n", "import tqdm\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "encoder = tiktoken.get_encoding(tiktoken.encoding_name_for_model(\"gpt-4o\"))\n", "\n", "tokens_mapping = []\n", "\n", "# fix_workflow_dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Dataset\")\n", "fix_workflow_dataset_path = get_fix_workflow_dataset_path()\n", "for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob(\"*.yaml\"))):\n", "    datapoint = yaml_load(path)\n", "    datapoint['workflow']\n", "    original_workflow_tokens = len(encoder.encode(yaml_dump(datapoint['workflow'])))\n", "    if \"predictions\" not in datapoint:\n", "        continue  # no predictions\n", "    for prediction in datapoint[\"predictions\"]:\n", "        if prediction['validation']['errors']:\n", "            continue\n", "        completion_tokens = prediction['usage']['completion_tokens']\n", "        tokens_mapping.append((original_workflow_tokens, completion_tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plot histograms in the same figure, original and completion tokens, but also the difference (absolute and percentage)\n", "import matplotlib.pyplot as plt\n", "\n", "original_tokens, completion_tokens = zip(*tokens_mapping)\n", "difference_tokens = [c - o for o, c in tokens_mapping]\n", "percentage_tokens = [d / o * 100 for o, d in zip(original_tokens, difference_tokens) if d / o * 100 < 100]\n", "\n", "fig, axs = plt.subplots(3, 1, figsize=(10, 10))\n", "axs[0].hist([original_tokens, completion_tokens], bins=100, label=[\"Original\", \"Completion\"])\n", "axs[0].legend()\n", "axs[0].set_title(\"Original vs Completion tokens\")\n", "axs[0].set_xlabel(\"Tokens\")\n", "axs[0].set_ylabel(\"Frequency\")\n", "\n", "axs[1].hist(difference_tokens, bins=100)\n", "axs[1].set_title(\"Difference in tokens\")\n", "axs[1].set_xlabel(\"Tokens Difference\")\n", "axs[1].set_ylabel(\"Frequency\")\n", "\n", "axs[2].hist(percentage_tokens, bins=100)\n", "axs[2].set_title(\"Percentage difference in tokens\")\n", "axs[2].set_xlabel(\"Percentage difference\")\n", "axs[2].set_ylabel(\"Frequency\")\n", "plt.tight_layout()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted(percentage_tokens)[-10:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Investigate issues from alpha"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import time\n", "from autopilot.services.studio._text_to_workflow.common.connections_loader import get_connections_data\n", "from autopilot.services.studio._text_to_workflow.core.config import settings\n", "import json\n", "import requests\n", "from autopilot.services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from autopilot.services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from autopilot.services.studio._text_to_workflow.workflow_generation.workflow_generation_test import validate_workflow_format\n", "\n", "\n", "async def validate(workflow_str):\n", "    validation_input = {\n", "        \"result\": workflow_str,\n", "        \"jitCommands\": [],  # not used for the moment\n", "        \"connectors\": [],  # not used for the moment\n", "    }\n", "    response = await validate_workflow_format(validation_input, oneline_errors=True)\n", "    return response\n", "\n", "\n", "dump_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha\")\n", "for path in dump_path.glob(\"*.json\"):\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/7fefc364-7c74-4364-9226-0f2c36f6b2b7.json\")\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/301_Request_emptyReasoning_alpha.txt\")\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/1507_NoApplyButton.txt\")\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/877_FixWorkflow_strangeResponse.txt\")\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/6147_FixWorkFlow_Slow.txt\")\n", "    path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/2558_Request_fix_wf.txt\")\n", "    # path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/Investigate-alpha/217_Fix_wf_Request.txt\")\n", "    data = {\"request\": json.loads(path.read_text().split(\"\\n\\n\")[-1])}\n", "    # data = json.load(path.open())\n", "\n", "    tenant_id, connections = get_connections_data()\n", "    headers = {\n", "        \"Authorization\": settings.UIPATH_TOKEN,\n", "        \"X-UIPATH-Localization\": \"en, en\",\n", "        \"X-UIPATH-TenantId\": tenant_id,\n", "    }\n", "    # times = []\n", "    start = time.time()\n", "    response = requests.post(\"http://localhost:5123/v2/fix-workflow\", headers=headers, json=data[\"request\"])\n", "    print(\"Time:\", time.time() - start)\n", "    # times.append(time.time() - start)\n", "    # print(\"Time:\", times[-1])\n", "\n", "    response_as_json = response.json()\n", "    validation = await validate(response_as_json[\"fixedWorkflow\"])\n", "    # validation = {}\n", "\n", "    print(path)\n", "    print(\"Errors: \")\n", "    print(\"\\n\".join(sorted(data[\"request\"][\"errors\"])))\n", "    print()\n", "    print(\"Reasoning: \", response_as_json[\"reasoning\"])\n", "    print()\n", "    print(\"Remaining errors: \")\n", "    if validation:\n", "        print(\"\\n\".join(validation[\"errors\"]))\n", "    else:\n", "        print(\"Validation not run\")\n", "    print()\n", "    print(\"Differences in workflow:\")\n", "    original_workflow = yaml_dump(yaml_load(data[\"request\"][\"currentWorkflow\"]))\n", "    fixed_workflow = response_as_json[\"fixedWorkflow\"]\n", "    print(get_diff(original_workflow, fixed_workflow))\n", "    print()\n", "    print()\n", "    break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[\"request\"][\"errors\"])\n", "print(yaml_dump(yaml_load(data[\"request\"][\"currentWorkflow\"])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explore corrections made by fix workflow"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "wfgen_identifier_to_datapoint = {}\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for subset in [\"train\", \"test\"]:  # other subsets could be considered\n", "        for path in paths.get_workflow_generation_dataset_path(framework, subset).rglob(\"*.yaml\"):\n", "            # print(path)\n", "            if path.stem in {\"metadata\", \"subsets\"}:\n", "                continue\n", "            wfgen_identifier_to_datapoint[path.stem] = yaml_load(path)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import collections\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "stats = collections.Counter()\n", "sizes_no_workflow = collections.Counter()\n", "sizes_with_workflow = collections.Counter()\n", "# dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only\")\n", "dataset_path = get_fix_workflow_dataset_path()\n", "\n", "wf_name_to_datapoints = collections.defaultdict(dict)\n", "for datapoint_path in dataset_path.rglob(\"*.yaml\"):\n", "    *_, wf_name, fix_id = datapoint_path.parts\n", "\n", "    datapoint = yaml_load(datapoint_path)\n", "\n", "    # special cases i found afterwards\n", "    if all(error.endswith(\"couldn't be resolved, so System.Activities.Statements.Sequence was used instead.\") for error in datapoint[\"errors\"][\"errors\"]):\n", "        continue  # skip these ones, no fix\n", "\n", "    stats[\"total\"] += 1\n", "    if \"predictions\" not in datapoint:\n", "        # print(f\"No predictions for {datapoint_path}\")\n", "        stats[\"without_prediction\"] += 1\n", "        continue\n", "    for prediction in datapoint[\"predictions\"][:1]:\n", "        if \"fixedWorkflow\" not in prediction:\n", "            # print(f\"No fixedWorkflow for {datapoint_path}\")\n", "            stats[\"without_fixedWorkflow\"] += 1\n", "            sizes_no_workflow[len(yaml_dump(datapoint[\"workflow\"]))] += 1\n", "            continue\n", "        sizes_with_workflow[len(yaml_dump(datapoint[\"workflow\"]))] += 1\n", "        if \"validation\" not in prediction:\n", "            # print(f\"No validation for {datapoint_path}\")\n", "            stats[\"without_validation\"] += 1\n", "            continue\n", "        # if not prediction[\"validation\"][\"errors\"]:\n", "        if prediction[\"validation\"][\"errors\"]:\n", "            # print(f\"Still errors for {datapoint_path}\")\n", "            stats[\"still_with_errors\"] += 1\n", "            continue\n", "        stats[\"fixed\"] += 1\n", "        datapoint[\"prediction\"] = prediction\n", "        wf_name_to_datapoints[wf_name][fix_id] = datapoint\n", "        # print(datapoint_path)\n", "        # print(prediction[\"fixedWorkflow\"])\n", "        # print()\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats.most_common()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(min(sizes_no_workflow), max(sizes_no_workflow))\n", "print(sum(sizes_no_workflow) / len(sizes_no_workflow))\n", "print(sum(sizes_with_workflow) / len(sizes_with_workflow))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump\n", "\n", "\n", "def reorder(workflow_dict: dict):\n", "    workflow_dict = workflow_dict.copy()\n", "    if \"processName\" in workflow_dict:\n", "        process_name = workflow_dict.pop(\"processName\")\n", "        workflow_dict[\"processName\"] = process_name\n", "    if \"packages\" in workflow_dict:\n", "        packages = workflow_dict.pop(\"packages\")\n", "        workflow_dict[\"packages\"] = packages\n", "    return workflow_dict\n", "\n", "def print_diff_wffix_datapoint(datapoint):\n", "    original_workflow = datapoint[\"workflow\"]\n", "    fixed_workflow = datapoint[\"prediction\"][\"fixedWorkflow\"]\n", "    print(\"Errors:\")\n", "    for error in datapoint[\"errors\"][\"errors\"]:\n", "        print(\"  - \", error)\n", "    print(\"Fix:\")\n", "    print(\"```\")\n", "    print(get_diff(yaml_dump(reorder(original_workflow)), yaml_dump(reorder(fixed_workflow)), 3))\n", "    print(\"```\")\n", "    print(\"Errors afterwards:\")\n", "    for error in datapoint[\"prediction\"][\"validation\"][\"errors\"]:\n", "        print(\"  - \", error)\n", "    print()\n", "\n", "for wf_name, datapoints in wf_name_to_datapoints.items():\n", "    print(f\"# {len(datapoints):3} {wf_name}\")\n", "    for fix_id, datapoint in datapoints.items():\n", "        fixed_workflow = datapoint[\"prediction\"][\"fixedWorkflow\"]\n", "        if not fixed_workflow:\n", "            continue  # no fix, just a false positive\n", "        print_diff_wffix_datapoint(datapoint)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Order by TED / PLED with ground truth"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Compute ted\n", "\n", "from services.studio._text_to_workflow.common.schema import WorkflowDict\n", "from services.studio._text_to_workflow.utils.workflow_utils import cleanup_workflow\n", "from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import workflow_edit_score\n", "\n", "\n", "def get_ted(workflow1: WorkflowDict, workflow2: WorkflowDict):\n", "    cleaned_workflow1 = cleanup_workflow(workflow1)\n", "    cleaned_workflow2 = cleanup_workflow(workflow2)\n", "    ted_score, _, _, _ = workflow_edit_score(cleaned_workflow1, cleaned_workflow2)\n", "    return ted_score\n", "\n", "\n", "def get_pled(workflow1: WorkflowDict, workflow2: WorkflowDict):\n", "    cleaned_workflow1 = cleanup_workflow(workflow1)\n", "    cleaned_workflow2 = cleanup_workflow(workflow2)\n", "    pled_score, _, _, _ = workflow_edit_score(cleaned_workflow1, cleaned_workflow2, \"levenshtein\", None)\n", "    return pled_score\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for wf_name, datapoints in wf_name_to_datapoints.items():\n", "    wfgen_datapoint = wfgen_identifier_to_datapoint[wf_name]\n", "    workflow_ground_truth = wfgen_datapoint[\"process\"]\n", "    for fix_id, datapoint in datapoints.items():\n", "        datapoint[\"workflow_ground_truth\"] = workflow_ground_truth\n", "\n", "        workflow_fixed = datapoint[\"prediction\"][\"fixedWorkflow\"]\n", "        # print(get_diff(\n", "        #     yaml_dump(cleanup_workflow(workflow_ground_truth)),\n", "        #     yaml_dump(cleanup_workflow(workflow_fixed))\n", "        # ))\n", "        ted = get_ted(workflow_ground_truth, workflow_fixed)\n", "        pled = get_pled(workflow_ground_truth, workflow_fixed)\n", "\n", "        datapoint[\"ted\"] = ted\n", "        datapoint[\"pled\"] = pled"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_consider = []\n", "\n", "fixed_errors = collections.Counter()\n", "for wf_name, datapoints in wf_name_to_datapoints.items():\n", "    print(f\"# {len(datapoints):3} {wf_name}\")\n", "    for fix_id, datapoint in sorted(datapoints.items(), key=lambda a: a[1][\"ted\"], reverse=True):\n", "        # if datapoint[\"ted\"] < 0.8:\n", "        #     continue\n", "        if datapoint[\"pled\"] < 0.8:\n", "            continue\n", "        print(\"TED:\", datapoint[\"ted\"])\n", "        print(\"PLED:\", datapoint[\"pled\"])\n", "        print_diff_wffix_datapoint(datapoint)\n", "        print()\n", "        fixed_errors.update(datapoint[\"errors\"][\"errors\"])\n", "        to_consider.append((wf_name, fix_id))\n", "for error, count in fixed_errors.most_common():\n", "    print(f\"{count:4} {error}\")\n", "print(len(to_consider))\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "plt.hist([wf_name_to_datapoints[wf_name][fix_id][\"pled\"] for wf_name, fix_id in to_consider], bins=100)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_path = get_fix_workflow_dataset_path()\n", "demos_path = dataset_path.with_name(dataset_path.name + \"-demos\")\n", "demos_path.mkdir(exist_ok=True)\n", "\n", "for wf_name, fix_id in to_consider:\n", "    datapoint = wf_name_to_datapoints[wf_name][fix_id]\n", "    print(f\"# {wf_name} {fix_id}\")\n", "    demo = {\n", "        \"process_existing\": datapoint[\"workflow\"],\n", "        \"errors\": datapoint[\"errors\"][\"errors\"],\n", "        \"reasoning\": datapoint[\"prediction\"][\"reasoning\"],\n", "        \"process_fixed\": datapoint[\"prediction\"][\"fixedWorkflow\"],\n", "    }\n", "    fix_id_only = fix_id.split(\"__\")[0]\n", "    yaml_dump(demo, demos_path / f\"{wf_name} {fix_id_only}.yaml\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# New venture: breaking workflows"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "wfgen_identifier_to_datapoint = {}\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for subset in [\"train\", \"test\"]:  # other subsets could be considered\n", "        for path in paths.get_workflow_generation_dataset_path(framework, subset).rglob(\"*.yaml\"):\n", "            # print(path)\n", "            if path.stem in {\"metadata\", \"subsets\"}:\n", "                continue\n", "            wfgen_identifier_to_datapoint[path.stem] = yaml_load(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import csv\n", "import pathlib\n", "\n", "def load_errors():\n", "    errors_list_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/csv_of_errors.csv\")\n", "    # format is RFC 4180-compliant CSV: items are enclosed in \" and \" inside is escaped by double \"\"\n", "    prod_errors = [line[0] for line in csv.reader(errors_list_path.open())][1:]\n", "    return prod_errors\n", "\n", "prod_errors = load_errors()\n", "len(prod_errors)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "sample = random.sample(prod_errors, 10)\n", "sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workflow_sample = random.sample(list(wfgen_identifier_to_datapoint.values()), 1)[0]\n", "workflow_sample[\"process\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "\n", "workflow_fix_task = await WorkflowFixTask(\"config.yaml\").load()\n", "\n", "tenant_id, connections = get_connections_data()\n", "result = await workflow_fix_task.run_fix_workflow(yaml_dump(workflow_sample[\"process\"]), sample, \"\", connections, workflow_sample[\"target_framework\"], None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_connections_data()\n", "result = await workflow_fix_task.run_fix_workflow(yaml_dump(workflow_sample[\"process\"]), sample, \"\", connections, workflow_sample[\"target_framework\"], None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result[\"reasoning\"])\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "\n", "# print(result[\"fixedWorkflow\"])\n", "\n", "print(get_diff(yaml_dump(workflow_sample[\"process\"]), result[\"fixedWorkflow\"], 5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await validate(result[\"fixedWorkflow\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Start a playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from uuid import uuid4\n", "import random\n", "import pathlib\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "import tqdm\n", "\n", "workflow_fix_task = await WorkflowFixTask(\"config.yaml\").load()\n", "\n", "playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground\")\n", "# playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-one-error\")\n", "# playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-experiments-testing-breaking-workflows\")\n", "playground_path.mkdir(exist_ok=True)\n", "\n", "dummy_fix_workflow = yaml_load(\"\"\"\\\n", "- thought: ''\n", "  activity: System.Activities.Statements.Sequence\n", "  currentActivity: true\"\"\")\n", "\n", "# for index in tqdm.tqdm(range(2000)):\n", "for index in range(len(prod_errors)):\n", "    wfname, sample_workflow = random.sample(list(wfgen_identifier_to_datapoint.items()), 1)[0]\n", "    sample_errors = prod_errors[index: index+1]\n", "\n", "    tenant_id, connections = get_connections_data()\n", "    result = await workflow_fix_task.run_fix_workflow(yaml_dump(sample_workflow[\"process\"]), sample_errors, \"\", connections, sample_workflow[\"target_framework\"], None)\n", "    if not result.get(\"fixedWorkflow\"):\n", "        continue\n", "    fixed_workflow = yaml_load(result[\"fixedWorkflow\"])\n", "    if \"workflow\" not in fixed_workflow:\n", "        print(\"WARNING! Missing workflow key\")\n", "        print(result[\"fixedWorkflow\"])\n", "        continue\n", "    if fixed_workflow == dummy_fix_workflow:\n", "        print(\"WARNING! Got empty sequence workflow back.\")\n", "        continue\n", "    info = {\n", "        \"wfname\": wfname,\n", "        \"reasoning\": result[\"reasoning\"],\n", "        \"errors\": sample_errors,\n", "        \"workflow\": sample_workflow[\"process\"],\n", "        \"fixedWorkflow\": fixed_workflow,\n", "    }\n", "    uuid = str(uuid4())\n", "    yaml_dump(info, playground_path / f\"experiment-{wfname}-{uuid}.yaml\")"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["dummy_fix_workflow = yaml_load(\"\"\"\\\n", "packages: []\n", "workflow:\n", "- thought: ''\n", "  activity: System.Activities.Statements.Sequence\n", "  currentActivity: true\"\"\")\n", "\n", "for path in playground_path.rglob(\"*.yaml\"):\n", "    info = yaml_load(path)\n", "    if info[\"fixedWorkflow\"] == dummy_fix_workflow:\n", "        path.unlink()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Validate workflows and verify the errors are consistent"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-cummulative\")\n", "# playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground\")\n", "# playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-one-error\")\n", "# playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-experiments-testing-breaking-workflows\")\n", "\n", "listing = [*playground_path.rglob(\"*.yaml\")]\n", "for path in tqdm.tqdm(listing, desc=\"Validating workflows with errors\"):\n", "    info = yaml_load(path)\n", "    if \"validation\" in info:\n", "        # continue\n", "        del info[\"validation\"]\n", "    validation = await validate(yaml_dump(info[\"fixedWorkflow\"]))\n", "    info[\"validation\"] = validation\n", "    yaml_dump(info, path)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "errors = collections.Counter()\n", "\n", "playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-one-error\")\n", "for path in playground_path.rglob(\"*.yaml\"):\n", "    info = yaml_load(path)\n", "    errors.update(info[\"errors\"])\n", "    # errors.update(info[\"validation\"][\"errors\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for error, count in sorted(errors.most_common()):\n", "    print(f\"{count:4} {error}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.workflow import Workflow\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "\n", "\n", "playground_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-one-error\")\n", "\n", "for path in playground_path.rglob(\"*.yaml\"):\n", "    info = yaml_load(path)\n", "\n", "    intended_error = info[\"errors\"][0]\n", "    actual_errors = info[\"validation\"][\"errors\"]\n", "    original_workflow = info[\"workflow\"]\n", "    original_workflow = Workflow(\"\", \"\", original_workflow).to_dict(include_name=False)  # to normalize the uipathactivityid (in params / root level)\n", "    altered_workflow = info[\"fixedWorkflow\"]\n", "    altered_workflow = Workflow(\"\", \"\", altered_workflow).to_dict(include_name=False)\n", "\n", "    print(info[\"reasoning\"])\n", "    print(path)\n", "    print(intended_error)\n", "    print(\"\\n\".join(f\" - {error}\" for error in actual_errors))\n", "    # print(get_diff(yaml_dump(original_workflow), yaml_dump(original_workflow_abstracted)))\n", "    print(get_diff(yaml_dump(original_workflow), yaml_dump(altered_workflow)))\n", "    validation_result = await validate(yaml_dump(altered_workflow))\n", "    print(\"\\n\".join(f\" - {error}\" for error in validation_result[\"errors\"]))\n", "    print()\n", "    break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Invetigate DAP translation issue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "wfgen_identifier_to_datapoint = {}\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for subset in [\"train\", \"test\"]:  # other subsets could be considered\n", "        for path in paths.get_workflow_generation_dataset_path(framework, subset).rglob(\"*.yaml\"):\n", "            # print(path)\n", "            if path.stem in {\"metadata\", \"subsets\"}:\n", "                continue\n", "            wfgen_identifier_to_datapoint[path.stem] = yaml_load(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common.connections_loader import get_connections_data\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "import pathlib\n", "\n", "# path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-one-error/experiment-StudioDesktop_fixes_20240403_Get argument gmail summary on slack channel-dbd2b32f-bdb4-43b0-860c-91f7d381bf6c.yaml\")\n", "# path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_complex_examples_20240517_EscaleEmail/Fix1__StudioDesktop_complex_examples_20240517_EscaleEmail.yaml\")\n", "# path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_ExceluriLipsa_Format/Fix1__StudioDesktop_ExceluriLipsa_Format.yaml\")\n", "path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-cummulative/experiment-StudioDesktop_fixes_20240419_[Generated] Track new OneDrive for Business files in a Google Sheet-66c57746-0566-4390-902c-3c207abab9e3.yaml\")\n", "sample = yaml_load(path)\n", "\n", "# identifier = path.stem.split(\"__\", 1)[-1]\n", "# identifier = path.stem.split('-', 1)[-1][:-36-1]\n", "identifier = sample[\"wfname\"]\n", "datapoint = wfgen_identifier_to_datapoint[identifier]\n", "target_framework = datapoint[\"target_framework\"]\n", "errors = sample[\"errors\"]\n", "# errors = sample[\"errors\"][\"errors\"]\n", "workflow = yaml_dump(sample[\"workflow\"])\n", "\n", "workflow_fix_task = await WorkflowFixTask(\"config.yaml\").load()\n", "tenant_id, connections = get_connections_data()\n", "# connections = []\n", "result = await workflow_fix_task.run_fix_workflow(workflow, errors, \"\", connections, target_framework, None)\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "print(get_diff(workflow, result[\"fixedWorkflow\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(workflow)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result['fixedWorkflow'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Restore dataset entries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever\n", "from services.studio._text_to_workflow.common.schema import Connection\n", "from services.studio._text_to_workflow.common.walkers import IntegrationServiceActivityRestorer\n", "from services.studio._text_to_workflow.utils.errors import ExistingWorkflowInvalidError\n", "from services.studio._text_to_workflow.utils.workflow_utils import load_workflow_instance, get_diff\n", "\n", "retriever = ActivitiesRetriever().ensure_loaded()\n", "\n", "connections_by_connector: dict[str, Connection] = {}\n", "for connection in connections:\n", "    if not (connector := connection[\"connector\"]):\n", "        continue\n", "    if connector not in connections_by_connector or connection.get(\"isDefault\", True):\n", "        connections_by_connector[connector] = connection\n", "\n", "# try to fix these offline\n", "# root_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o\")\n", "root_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-cummulative\")\n", "\n", "# paths = [\n", "#     # \"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_complex_examples_20240517_EscaleEmail/Fix1__StudioDesktop_complex_examples_20240517_EscaleEmail.yaml\",\n", "#     # \"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_complex_examples_20240517_EscaleEmail/Fix1__StudioDesktop_complex_examples_20240517_EscaleEmail.yaml\",\n", "#     # \"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_ExceluriLipsa_Format/Fix1__StudioDesktop_ExceluriLipsa_Format.yaml\",\n", "#     \"/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o/StudioDesktop_jarvis_fixed_[Jarvis] Retrieve Pending Concur Expense Reports/Fix2__StudioDesktop_jarvis_fixed_[<PERSON>] Retrieve Pending Concur Expense Reports.yaml\",\n", "# ]\n", "counter = 0\n", "invalid_paths = []\n", "for path in [*root_path.rglob(\"*.yaml\")]:\n", "# for path in map(pathlib.Path, paths):\n", "    info = yaml_load(path)\n", "    try:\n", "        original_workflow = load_workflow_instance(\"\", yaml_dump(info[\"workflow\"]))\n", "    except ExistingWorkflowInvalidError as e:\n", "        invalid_paths.append(path)\n", "        continue\n", "    # identifier = path.parts[-2]\n", "    identifier = info[\"wfname\"]\n", "    target_framework = wfgen_identifier_to_datapoint[identifier][\"target_framework\"]\n", "\n", "    activity_defs, collector, existing_workflow_object = workflow_fix_task._get_activity_defs(original_workflow, target_framework)\n", "    augmented_type_defs, dap_restore_info = await workflow_fix_task.augment_dynamic_activities_type_definitions(\n", "        activity_defs,\n", "        connections_by_connector,\n", "    )\n", "    new_dap_restore_info = {}\n", "    for identifier, restore_dict in dap_restore_info.items():\n", "        activity_name, identifier_code = identifier.split(\"_\", 1)\n", "        class_name = restore_dict[\"className\"]\n", "        new_dap_restore_info[f\"{class_name}_{identifier_code}\"] = restore_dict\n", "    dap_restore_info = new_dap_restore_info\n", "    # display(dap_restore_info)\n", "\n", "    # if \"predictions\" not in info:\n", "    #     continue\n", "\n", "    for prediction in [info]:\n", "    # for prediction in info[\"predictions\"]:\n", "        if \"fixedWorkflow\" not in prediction:\n", "            continue\n", "        fixed_workflow_serialization = yaml_dump(prediction[\"fixedWorkflow\"])\n", "        fixed_workflow = load_workflow_instance(\"\", fixed_workflow_serialization)\n", "        updated_fixed_workflow = copy.deepcopy(fixed_workflow)\n", "        IntegrationServiceActivityRestorer(dap_restore_info).walk(updated_fixed_workflow)\n", "        # fixed_workflow.namespace_imports = existing_workflow_object.namespace_imports\n", "        updated_fixed_workflow_serialization = updated_fixed_workflow.lmyaml(include_namespaces=True, include_dap_properties=True)\n", "\n", "        # because for diffing there are still some white spaces inconsistent, we do this\n", "        updated_fixed_workflow_serialization = yaml_dump(yaml_load(updated_fixed_workflow_serialization))\n", "\n", "        # print(updated_fixed_workflow_serialization)\n", "        if updated_fixed_workflow_serialization != fixed_workflow_serialization:\n", "            print(path)\n", "            counter += 1\n", "            # print('Different')\n", "            # print(get_diff(fixed_workflow_serialization, updated_fixed_workflow_serialization, 5))\n", "\n", "            prediction[\"fixedWorkflow\"] = yaml_load(updated_fixed_workflow_serialization)\n", "    yaml_dump(info, path)\n", "        \n", "print(counter)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# for p in invalid_paths:\n", "#     p.unlink()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup existing workflows with empty sequences"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["empty_sequence = yaml_load(\"\"\"\n", "- thought: ''\n", "  activity: System.Activities.Statements.Sequence\n", "  currentActivity: true\n", "\"\"\")\n", "\n", "root_paths = [*map(pathlib.Path, [\n", "    \"/workspace/data/workdir/fix_workflow/playground\",\n", "    \"/workspace/data/workdir/fix_workflow/playground-one-error\",\n", "    \"/workspace/data/workdir/fix_workflow/playground-experiments-testing-breaking-workflows\",\n", "])]\n", "paths = []\n", "for path in root_paths:\n", "    listing = [*path.rglob(\"*.yaml\")]\n", "    print(len(listing), path)\n", "    paths.extend(listing)\n", "print(len(paths))\n", "\n", "# empty_workflow_paths = []\n", "counter = 0\n", "for path in paths:\n", "    info = yaml_load(path)\n", "    if info[\"fixedWorkflow\"][\"workflow\"] == empty_sequence:\n", "        # empty_workflow_paths.append(path)\n", "        # path.unlink()\n", "        counter += 1\n", "print(counter)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Rerun the empty workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "dummy_fix_workflow = yaml_load(\"\"\"\\\n", "- thought: ''\n", "  activity: System.Activities.Statements.Sequence\n", "  currentActivity: true\"\"\")\n", "\n", "for path in tqdm.tqdm(empty_workflow_paths):\n", "    # print(path)\n", "    info = yaml_load(path)\n", "\n", "    target_framework = wfgen_identifier_to_datapoint[info[\"wfname\"]][\"target_framework\"]\n", "    result = await workflow_fix_task.run_fix_workflow(yaml_dump(info[\"workflow\"]), info[\"errors\"], \"\", connections, target_framework, None)\n", "    if not result.get(\"fixedWorkflow\"):\n", "        continue\n", "    fixed_workflow = yaml_load(result[\"fixedWorkflow\"])\n", "    if \"workflow\" not in fixed_workflow:\n", "        print(\"WARNING! Missing workflow key\")\n", "        print(result[\"fixedWorkflow\"])\n", "        continue\n", "    if fixed_workflow == dummy_fix_workflow:\n", "        print(\"WARNING! Got empty sequence workflow back.\")\n", "        continue\n", "\n", "    info[\"reasoning\"] = result[\"reasoning\"]\n", "    info[\"fixedWorkflow\"] = fixed_workflow\n", "    del info[\"validation\"]\n", "\n", "    yaml_dump(info, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in tqdm.tqdm(empty_workflow_paths, desc=\"Validating\"):\n", "    info = yaml_load(path)\n", "    if \"validation\" in info:\n", "        continue\n", "    validation = await validate(yaml_dump(info[\"fixedWorkflow\"]))\n", "    info[\"validation\"] = validation\n", "    yaml_dump(info, path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Better parsing of errors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import collections\n", "\n", "error_code_re = re.compile(r\"(BC\\d{5}|CS\\d{4})\")\n", "errors = load_errors()\n", "\n", "errors_without_code = collections.defaultdict(list)\n", "errors_by_code = collections.defaultdict(list)\n", "for error in errors:\n", "    if match_object := error_code_re.match(error):\n", "        errors_by_code[match_object.group()].append(error)\n", "    else:\n", "        error_key = re.sub(r\"\\'(.*?)\\'\", \"'x'\", error)\n", "        errors_without_code[error_key].append(error)\n", "\n", "# for code, errors in sorted(errors_by_code.items(), key=lambda a: len(a[1]), reverse=True):\n", "#     candidate = re.sub(r\"\\'(.*?)\\'\", \"'x'\", errors[0])\n", "#     print(f\"{len(errors)} {candidate}\")\n", "#     for error in errors:\n", "#         print(f\" - {error}\")\n", "# print()\n", "# for code, errors in sorted(errors_without_code.items(), key=lambda a: len(a[1]), reverse=True):\n", "#     print(f\"{len(errors)} {code}\")\n", "#     for error in errors:\n", "#         print(f\" - {error}\")\n", "\n", "mixed_errors = errors_without_code | {re.sub(r\"\\'(.*?)\\'\", \"'x'\", errors[0]): errors for _, errors in errors_by_code.items()}\n", "for code, errors in sorted(mixed_errors.items(), key=lambda a: len(a[1]), reverse=True):\n", "    print(f\"{len(errors):3} {code}\")\n", "\n", "    for error, count in collections.Counter(errors).most_common():\n", "        print(f\"    - {count:3} {error}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_paths = [*map(pathlib.Path, [\n", "    \"/workspace/data/workdir/fix_workflow/playground-cummulative\",\n", "    # \"/workspace/data/workdir/fix_workflow/playground\",\n", "    # \"/workspace/data/workdir/fix_workflow/playground-one-error\",\n", "    # \"/workspace/data/workdir/fix_workflow/playground-experiments-testing-breaking-workflows\",\n", "])]\n", "paths = []\n", "for path in root_paths:\n", "    listing = [*path.rglob(\"*.yaml\")]\n", "    print(len(listing), path)\n", "    paths.extend(listing)\n", "print(len(paths))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import shutil\n", "\n", "# cummulative_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-cummulative\")\n", "# cummulative_path.mkdir()\n", "# for path in paths:\n", "#     # print(\"Copying\")\n", "#     # print(path)\n", "#     # print(cummulative_path / f\"{path.stem}.yaml\")\n", "#     shutil.copy2(path, cummulative_path / f\"{path.stem}.yaml\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "by_error_code = collections.defaultdict(list)\n", "by_error = collections.defaultdict(list)\n", "for path in paths:\n", "    info = yaml_load(path)\n", "    print(path)\n", "\n", "    reasoning = info[\"reasoning\"]\n", "    errors = info[\"errors\"]\n", "    error_used = \"\"\n", "    if match := error_code_re.search(reasoning):\n", "        print(\"found code for\", path.name)\n", "        error_used = match.group()\n", "        by_error_code[error_used].append(path)\n", "    else:\n", "        # print(\"code not found for \", path.name)\n", "        error_used = \"\"\n", "        for error in errors:\n", "            if error in reasoning:\n", "                error_used = error\n", "                print(\"found by exact match\", path.name)\n", "                break\n", "        else:\n", "            reasoning_words = \" \".join(re.findall(r\"\\w+\", reasoning))\n", "            errors_words = [\" \".join(re.findall(r\"\\w+\", error)) for error in errors]\n", "            for error, error_words in zip(errors, errors_words):\n", "                if error_words in reasoning_words:\n", "                    error_used = error\n", "                    print(\"found by word match\", path.name)\n", "                    break\n", "            else:\n", "                if len(errors) == 1:\n", "                    print(\"single error for\", path.name)\n", "                    error_used = errors[0]\n", "                else:\n", "                    print(\"heuristic for\", path.name)\n", "                    if match := re.search(r\"\\\"(.*)\\\"\", reasoning):\n", "                        error_used = match.group(1)\n", "                    else:\n", "                        print(\"not found by heuristic\", path.name)\n", "                        print(reasoning)\n", "                        for error in sorted(info[\"errors\"]):\n", "                            print(\"- \", error)\n", "                # for error_words in errors_words:\n", "                #     print(\"  - \", error_words)\n", "                # print()\n", "        error_key = re.sub(r\"\\'(.*)\\'\", \"'x'\", error_used)\n", "        by_error[error_key].append(path)\n", "    info[\"error_introduced\"] = error_used\n", "    yaml_dump(info, path)"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [], "source": ["for error in errors_by_code:\n", "    if error not in by_error_code:\n", "        by_error_code[error] = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for error, paths in sorted(by_error_code.items(), key=lambda a: len(a[1]), reverse=True):\n", "    prod_n = len(errors_by_code[error])\n", "    error_example = errors_by_code[error][0] if errors_by_code[error] else \"\"\n", "    print(f\"{len(paths):3} / {prod_n} {error} {error_example}\")\n", "    # for path in paths:\n", "    #     print(\"  - \", path.name)\n", "for code, paths in sorted(by_error.items(), key=lambda a: len(a[1]), reverse=True):\n", "    print(f\"{len(paths):3} {code}\")\n", "    # print()\n", "    # for path in paths:\n", "    #     print(\"  - \", path.name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rename some of the generations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from uuid import uuid4\n", "import pathlib\n", "\n", "root_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-experiments-testing-breaking-workflows\")\n", "for path in root_path.rglob(\"*.yaml\"):\n", "    info = yaml_load(path)\n", "    wfname = info[\"wfname\"]\n", "    uuid = uuid4()\n", "    print(f\"Renaming {path}\")\n", "    print(f'      to {path.with_name(f\"experiment-{wfname}-{uuid}.yaml\")}')\n", "    # path.rename(path.with_name(f\"experiment-{wfname}-{uuid}.yaml\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Set up a labelling interface using streamlit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import streamlit as st\n", "from st_diff_viewer import diff_viewer\n", "\n", "# Sample text diffs\n", "original_text = \"\"\"\n", "def hello_world():\n", "    print(\"Hello, world!\")\n", "\"\"\"\n", "\n", "modified_text = \"\"\"\n", "def hello_world():\n", "    print(\"Hello, <PERSON><PERSON>!\")\n", "\"\"\"\n", "\n", "# Display the diff\n", "st.title(\"Text Diff Viewer\")\n", "diff_viewer(original_text, modified_text)\n", "\n", "# Labeling interface\n", "st.header(\"Label the Diff\")\n", "label = st.radio(\"Select the label for the diff:\", [\"Correct\", \"Incorrect\", \"Needs Review\"])\n", "\n", "if st.button(\"Submit\"):\n", "    st.write(f\"Label submitted: {label}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Post tagging dataset aggregation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "wfgen_identifier_to_datapoint = {}\n", "for framework in [\"Portable\", \"Windows\"]:\n", "    for subset in [\"train\", \"test\"]:  # other subsets could be considered\n", "        for path in paths.get_workflow_generation_dataset_path(framework, subset).rglob(\"*.yaml\"):\n", "            # print(path)\n", "            if path.stem in {\"metadata\", \"subsets\"}:\n", "                continue\n", "            wfgen_identifier_to_datapoint[path.stem] = yaml_load(path)\n", "\n", "dataset_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/playground-cummulative\")\n", "dataset_annotations = dataset_path.with_name(dataset_path.stem + \"-tagging-results.yaml\")\n", "\n", "datapoints = []\n", "annotations = yaml_load(dataset_annotations)\n", "for name, annotation_datum in annotations.items():\n", "    # print(name)\n", "    # print(annotation_datum[\"path\"])\n", "    label = annotation_datum[\"label\"]\n", "    if label not in {\"Very Good\", \"Good\"}:\n", "        continue\n", "\n", "    playground_datum = yaml_load(pathlib.Path(annotation_datum[\"path\"]))\n", "    datapoint = {\n", "        \"process_existing\": playground_datum[\"fixedWorkflow\"],\n", "        \"errors\": [playground_datum[\"error_introduced\"]],\n", "        \"reasoning\": playground_datum[\"reasoning\"],\n", "        \"process_fixed\": playground_datum[\"workflow\"],\n", "        \"validation_errors\": playground_datum[\"validation\"][\"errors\"],\n", "        \"wfname\": playground_datum[\"wfname\"],\n", "        \"target_framework\": wfgen_identifier_to_datapoint[playground_datum[\"wfname\"]][\"target_framework\"]\n", "    }\n", "    datapoint[\"label\"] = label\n", "    datapoint[\"annotation-hash\"] = name.rsplit(\".\", 1)[0][-36:]\n", "    datapoints.append(datapoint)\n", "print(f\"Loaded {len(datapoints)} datapoints\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "\n", "evaluation_dataset_path = paths.get_workflow_fix_dataset_path() / \"eval\"\n", "evaluation_dataset_path.mkdir(exist_ok=True)\n", "for datapoint in datapoints:\n", "    filename = f'{datapoint[\"wfname\"]}-{datapoint[\"annotation-hash\"]}.yaml'\n", "    # print(evaluation_dataset_path / filename)\n", "    yaml_dump(datapoint, evaluation_dataset_path / filename)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Add error messages to errors that have only the code"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["error_code_to_message = {code: re.sub(r\"\\'(.*?)\\'\", \"'x'\", collections.Counter(errors).most_common(1)[0][0]) for code, errors in errors_by_code.items()}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "evaluation_dataset_path = paths.get_workflow_fix_dataset_path() / \"eval\"\n", "datapoints = {path: yaml_load(path) for path in evaluation_dataset_path.rglob(\"*.yaml\")}\n", "for path, datapoint in datapoints.items():\n", "    reasoning = datapoint[\"reasoning\"]\n", "    error_message = datapoint[\"errors\"][0]\n", "    if not re.match(r\"^(BC\\d{5}|CS{4})$\", error_message):\n", "        continue\n", "\n", "    error_message = error_code_to_message[error_message]\n", "    # messages = re.findall(r\"\\\"(.*)\\\"\", reasoning)\n", "    # if len(messages) == 0:\n", "    #     error_message = error_code_to_message[error_message]\n", "    # elif len(messages) == 1:\n", "    #     error_message = messages[0]\n", "    # else:\n", "    #     for message in messages:\n", "    #         if message in error_message:\n", "    #             error_message = message\n", "    #             break\n", "    #     else:\n", "    #         error_message = error_code_to_message[error_message]\n", "    print(error_message)\n", "    \n", "    datapoint[\"errors\"] = [error_message]\n", "    yaml_dump(datapoint, path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Evaluate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gather predictions"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "import os\n", "dotenv.load_dotenv()\n", "print(os.environ[\"GOOGLE_APPLICATION_CREDENTIALS\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from services.studio._text_to_workflow.common.connections_loader import get_connections_data\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.request_utils import get_request_context, set_request_context\n", "from services.studio._text_to_workflow.utils.testing import build_testing_request_context\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixFlairType, WorkflowFixResponse\n", "from services.studio._text_to_workflow.workflow_fix.dataset_creation.create_from_wfgen import refresh_token\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "import openai\n", "import tqdm\n", "\n", "\n", "await refresh_token()\n", "set_request_context(build_testing_request_context())\n", "\n", "workflow_fix_task = await WorkflowFixTask().load()\n", "datapoints = {p: yaml_load(p) for p in (paths.get_workflow_fix_dataset_path() / \"train\" / \"handcrafted\").rglob(\"*.yaml\")}\n", "\n", "\n", "async def wrapped_predict(workflow_as_str, errors, target_framework, fixed_workflow: str | None = None) -> WorkflowFixResponse | None:\n", "    response = {\"exceptions\": []}\n", "    for _ in range(3):\n", "        try:\n", "            tenant_id, connections = get_connections_data()\n", "            start = time.time()\n", "            endpoint_response = await workflow_fix_task.run_fix_workflow(\n", "                workflow_as_str,\n", "                errors,\n", "                \"\",\n", "                connections,\n", "                target_framework,\n", "                None,\n", "                # WorkflowFixFlairType.Default,\n", "                # WorkflowFixFlairType.UniDiff,\n", "                # WorkflowFixFlairType.SoftUniDiff,\n", "                # WorkflowFixFlairType.MergeConflict,\n", "                # WorkflowFixFlairType.MergeConflictWithLines,\n", "                WorkflowFixFlairType.MergeHybrid,\n", "                # WorkflowFixFlairType.GenerateReasoning,\n", "                # WorkflowFixFlairType.SkipToTheGoodBit,\n", "                # fixed_workflow_gt=fixed_workflow,\n", "            )\n", "            response.update(endpoint_response)\n", "            response[\"latency\"] = time.time() - start\n", "            return response\n", "        except openai.AuthenticationError:\n", "            print(\"Attempting to refresh token\")\n", "            await refresh_token()\n", "            continue\n", "        except Exception as e:\n", "            print(f\"Failed to fix {e} {path}\")\n", "            import traceback\n", "\n", "            traceback.print_exc()\n", "            response[\"exceptions\"].append({\n", "                \"error\": str(e),\n", "                \"traceback\": traceback.format_exc(),\n", "                \"latency\": time.time() - start\n", "            })\n", "            continue\n", "    print(\"No output\")\n", "    return response\n", "\n", "\n", "for path, datapoint in tqdm.tqdm(datapoints.items(), desc=\"Evaluation\"):\n", "    if \"prediction\" in datapoint:\n", "        continue\n", "    workflow_as_str = yaml_dump(datapoint[\"process_existing\"])\n", "    fixed_workflow = yaml_dump(datapoint[\"process_fixed\"])\n", "    start = time.time()\n", "    prediction_result = await wrapped_predict(workflow_as_str, datapoint[\"errors\"], datapoint[\"target_framework\"], fixed_workflow)\n", "    prediction_result[\"latency\"] = time.time() - start\n", "    if \"fixedWorkflow\" in prediction_result:\n", "        prediction_result[\"fixedWorkflow\"] = yaml_load(prediction_result[\"fixedWorkflow\"])\n", "\n", "    # datapoint[\"prediction-old-gpt4o\"] = prediction_result\n", "    # datapoint[\"prediction-old-gpt4o-mini\"] = prediction_result\n", "    # datapoint[\"prediction-old-gpt4o-mini-speculative-decoding\"] = prediction_result\n", "    # datapoint[\"prediction-old-gpt4o-mini-control-no-speculative-decoding\"] = prediction_result\n", "    # datapoint[\"prediction-gpt4o-mini-unidiff\"] = prediction_result\n", "    # datapoint[\"prediction-gpt4o-mini-custom-softunidiff\"] = prediction_result\n", "    # datapoint[\"prediction-gpt4o-mini-custom-softunidiff-no-speculative\"] = prediction_result\n", "    # datapoint[\"reasoning-prediction-gpt4o\"] = prediction_result\n", "    # datapoint[\"prediction-o3-mini\"] = prediction_result\n", "    # datapoint[\"prediction-gpt4o-merge-conflict-style\"] = prediction_result\n", "    print(prediction_result)\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prediction_result[\"model_raw_prediction\"])\n", "print(yaml_dump(prediction_result[\"fixedWorkflow\"]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "# flush\n", "for path, datapoint in datapoints.items():\n", "    # if \"fixedWorkflow\" in datapoint[\"prediction-old-gpt4o\"] and isinstance(datapoint[\"prediction-old-gpt4o\"][\"fixedWorkflow\"], str):\n", "    #     datapoint[\"prediction-old-gpt4o\"][\"fixedWorkflow\"] = yaml_load(datapoint[\"prediction-old-gpt4o\"][\"fixedWorkflow\"])\n", "    # if \"fixedWorkflow\" in datapoint[\"prediction-old-gpt4o-mini\"] and isinstance(datapoint[\"prediction-old-gpt4o-mini\"][\"fixedWorkflow\"], str):\n", "    #     datapoint[\"prediction-old-gpt4o-mini\"][\"fixedWorkflow\"] = yaml_load(datapoint[\"prediction-old-gpt4o-mini\"][\"fixedWorkflow\"])\n", "    new_datapoint = copy.deepcopy(datapoint)\n", "    del new_datapoint[\"reasoning-prediction-gpt4o\"]\n", "    new_datapoint[\"reasoning\"] = datapoint[\"reasoning-prediction-gpt4o\"][\"reasoning\"]\n", "    yaml_dump(new_datapoint, path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "stats = collections.defaultdict(list)\n", "\n", "for datapoint in datapoints.values():\n", "    for model_name in [\n", "        # \"prediction-old-gpt4o-mini-control-no-speculative-decoding\",\n", "        # \"prediction-old-gpt4o-mini-speculative-decoding\",\n", "        \"prediction-gpt4o-mini-custom-softunidiff\",\n", "        \"prediction-gpt4o-mini-custom-softunidiff-no-speculative\",\n", "    ]:\n", "        pred = datapoint[model_name]\n", "        stats[f\"{model_name}-latency\"].append(pred[\"latency\"])\n", "        stats[f\"{model_name}-completion_tokens\"].append(pred[\"usage\"][\"completionTokens\"])\n", "\n", "normalized_stats = {}\n", "for k, v in stats.items():\n", "    # normalized_stats[k] = sum(v) / len(v)\n", "    normalized_stats[k] = sorted(v)[len(v) // 2]\n", "\n", "for k, v in normalized_stats.items():\n", "    print(f\"{k}: {v:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate predictions"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "datapoints = {p: yaml_load(p) for p in (paths.get_workflow_fix_dataset_path() / \"eval\").rglob(\"*.yaml\")}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "scores = collections.defaultdict(list)\n", "\n", "for path, datapoint in datapoints.items():\n", "    ground_truth = datapoint[\"process_fixed\"]\n", "    for prediction_key in [\n", "        # \"prediction-old-gpt4o\",\n", "        \"prediction-old-gpt4o-mini\",\n", "        # \"prediction-gpt4o-mini-unidiff\",\n", "        # \"prediction-old-gpt4o-mini-control-no-speculative-decoding\",\n", "        # \"prediction-old-gpt4o-mini-speculative-decoding\",\n", "        \"prediction-gpt4o-mini-custom-softunidiff\",\n", "        \"prediction-gpt4o-mini-custom-softunidiff-no-speculative\",\n", "    ]:\n", "        if datapoint[prediction_key] is not None and \"fixedWorkflow\" in datapoint[prediction_key]:\n", "            fixed_workflow = datapoint[prediction_key][\"fixedWorkflow\"]\n", "            scores[prediction_key].append(get_pled(ground_truth, fixed_workflow))\n", "        else:\n", "            scores[prediction_key].append(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key, score_list in scores.items():\n", "    print(key, sum(score_list) / len(score_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# error analysis\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "\n", "for path, datapoint in datapoints.items():\n", "    for model_key in [\"prediction-old-gpt4o\", \"prediction-old-gpt4o-mini\"]:\n", "        if \"fixedWorkflow\" not in datapoint[model_key]:\n", "            continue\n", "        original_workflow = datapoint[\"process_fixed\"]\n", "        fixed_workflow = datapoint[model_key][\"fixedWorkflow\"]\n", "        error_to_fix = datapoint[\"errors\"][0]\n", "        reasoning = datapoint[\"reasoning\"]\n", "\n", "        pled = get_pled(original_workflow, fixed_workflow)\n", "        if pled > 0.8:\n", "            continue\n", "\n", "        # fixed_workflow = Workflow(\"\", \"\", fixed_workflow).to_dict(include_name=False, include_dap_properties=False)\n", "\n", "        print(path)\n", "        print(model_key)\n", "        print(reasoning)\n", "        print(error_to_fix)\n", "        print(f\"PLED {pled}\")\n", "\n", "        to_compare_1 = yaml_dump(cleanup_workflow(original_workflow))\n", "        # to_compare_1 = yaml_dump(original_workflow)\n", "        to_compare_2 = yaml_dump(cleanup_workflow(fixed_workflow))\n", "        # to_compare_2 = yaml_dump(fixed_workflow)\n", "\n", "        print(get_diff(to_compare_1, to_compare_2))\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Handle handcrafted datapoints for workflow fixing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["option 1: use the existing dataset creation script on an autopilot like dataset structure\n", "```\n", "python3 -m services.studio._text_to_workflow.autopilot_dataset.build_dataset --autopilot-samples /workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure --only-new\n", "```\n", "option 2: use the following implementations for the yaml conversions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "dotenv.load_dotenv()\n", "# This is for loading of the conversion secretes from .env"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Isolate project xamls into separate projects to avoid connection reset on conversion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "# xamls_paths = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "xamls_paths = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/StudioDesktop\")\n", "xamls_paths_grouped = xamls_paths.parent / (xamls_paths.name + \"_individual\")\n", "xamls_paths_grouped = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "xamls_paths_grouped.mkdir(exist_ok=True)\n", "\n", "for project_dir in xamls_paths.iterdir():\n", "    if not project_dir.is_dir():\n", "        print(f\"Skipping {project_dir}. Not a directory\")\n", "        continue\n", "    listing = list(project_dir.iterdir())\n", "    broken_xaml_paths = [x for x in listing if x.name.lower().endswith(\"broken.xaml\")]\n", "    other_files_and_dirs = [x for x in listing if not x.name.lower().endswith(\"broken.xaml\") and not x.name.lower().endswith(\"fixed.xaml\")]\n", "    \n", "    for i, xaml_path in enumerate(sorted(broken_xaml_paths, key=lambda p: int(p.name.split('_')[0])), 1):\n", "        project_dir_mirrored = xamls_paths_grouped / project_dir.relative_to(xamls_paths)\n", "        project_dir_mirrored = project_dir_mirrored.with_name(project_dir_mirrored.name + \" \" + str(i))\n", "        project_dir_mirrored.mkdir(exist_ok=True)\n", "        for other_file in other_files_and_dirs:\n", "            print(other_file)\n", "            if other_file.is_dir():\n", "                shutil.copytree(other_file, project_dir_mirrored / other_file.name)\n", "            else:\n", "                shutil.copy2(other_file, project_dir_mirrored / other_file.name)\n", "        shutil.copy2(xaml_path, project_dir_mirrored / xaml_path.name)\n", "        fixed_path = xaml_path.with_name(xaml_path.name.replace(\"broken\", \"fixed\"))\n", "        if not fixed_path.exists():\n", "            fixed_path = fixed_path.with_name(fixed_path.name.replace(\"fixed\", \"Fixed\"))\n", "        shutil.copy2(fixed_path, project_dir_mirrored / fixed_path.name)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom conversion code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import json\n", "import tempfile\n", "\n", "from services.studio._text_to_workflow.autopilot_dataset.online_client import convert_project\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "\n", "# xamls_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_mihai/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "# xamls_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/Autopilot Workflow Fix\")\n", "xamls_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "output_dir = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/output\")\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "tmpdir = tempfile.mkdtemp()\n", "for i, project_json_path in enumerate(xamls_path.rglob(\"project.json\")):\n", "\n", "    # if \"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop/Reference required to assembly x containing the type x Add one to your project\" not in project_json_path.as_posix():\n", "    #     continue\n", "    print(i, project_json_path)\n", "    # name = project_json_path.relative_to(xamls_path).parent.as_posix().replace(\"/\", \"_\")\n", "    project_data = json.load(project_json_path.open())\n", "    name = project_json_path.parent.name\n", "    converted_dir = output_dir / name\n", "    convert_project(project_json_path, converted_dir, \"windows\", tmpdir, str(i))\n", "    \n", "    for converted_json_path in converted_dir.glob(\"**/*.json\"):\n", "        if converted_json_path.name == \"project.json\":\n", "            continue\n", "        if converted_json_path.name.endswith(\".typedef.json\"):\n", "            continue\n", "        if converted_json_path.name.endswith(\".error.json\"):\n", "            continue\n", "\n", "        converted_result = json.load(converted_json_path.open(encoding=\"utf-8-sig\"))\n", "        conversion_success = converted_result[\"Success\"]\n", "        has_errors = len(converted_result[\"Errors\"]) > 0\n", "        if not conversion_success:\n", "            print(\"No success for \", converted_json_path)\n", "            continue\n", "\n", "        workflow_converted = yaml_load(converted_result[\"result\"])\n", "        yaml_dump(workflow_converted, converted_json_path.with_suffix(\".yaml\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract error messages from xamls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from lxml import etree\n", "import pathlib\n", "\n", "xamls_path = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "\n", "prefix_map = {\n", "    \"\": \"http://schemas.microsoft.com/netfx/2009/xaml/activities\",\n", "    \"isactr\": \"http://schemas.uipath.com/workflow/integration-service-activities/isactr\",\n", "    \"mc\": \"http://schemas.openxmlformats.org/markup-compatibility/2006\",\n", "    \"sap\": \"http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation\",\n", "    \"sap2010\": \"http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation\",\n", "    \"scg\": \"clr-namespace:System.Collections.Generic;assembly=System.Private.CoreLib\",\n", "    \"uasf\": \"clr-namespace:UiPath.Activities.System.FileOperations;assembly=UiPath.System.Activities\",\n", "    \"ui\": \"http://schemas.uipath.com/workflow/activities\",\n", "    \"uisape\": \"clr-namespace:UiPath.IntelligentOCR.StudioWeb.Activities.PDF.ExtractPDFText;assembly=UiPath.IntelligentOCR.StudioWeb.Activities\",\n", "    \"upr\": \"clr-namespace:UiPath.Platform.ResourceHandling;assembly=UiPath.Platform\",\n", "    \"x\": \"http://schemas.microsoft.com/winfx/2006/xaml\",\n", "    \"uiascb\": \"clr-namespace:UiPath.IntegrationService.Activities.SWEntities.C3A01EAE649_generateChatCompletion_Create.Bundle;\"\n", "    \"assembly=C3A01EAE649_generate.XEw3O3vyE8K1tvYuZ2hXvFi\",\n", "}\n", "\n", "def extract_error_annotation_from_xaml(xaml_path: pathlib.Path) -> str | None:\n", "    root = etree.parse(xaml_path)  # type: ignore\n", "    if not root:\n", "        return\n", "    main_sequence_element = root.find('.//Sequence', prefix_map)\n", "    if not main_sequence_element:\n", "        print(\"Warning! No main sequence found\")\n", "        return\n", "    return main_sequence_element.attrib.get(\"{http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation}Annotation.AnnotationText\")\n", "\n", "for xaml_path in sorted(xamls_path.rglob(\"*.xaml\")):\n", "    error_annotation = extract_error_annotation_from_xaml(xaml_path)\n", "    if not error_annotation:\n", "        continue\n", "    errors = error_annotation.split(\"\\n\") if error_annotation else []\n", "    # if error_annotation:\n", "    #     print(xaml_path.parts[-2:])\n", "    #     print(error_annotation)\n", "    #     print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Aggregate into a dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixDatapoint\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "\n", "# input\n", "conversion_dir = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows\")\n", "xamls_dir = pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Downloads/StudioDesktop\")\n", "\n", "# output\n", "demos_path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted\")\n", "demos_path.mkdir(exist_ok=True)\n", "\n", "for workflow_broken_yaml_path in conversion_dir.rglob(\"*_broken.yaml\"):\n", "    workflow_fixed_yaml_path = workflow_broken_yaml_path.with_name(workflow_broken_yaml_path.name.replace(\"_broken.yaml\", \"_fixed.yaml\"))\n", "    if not workflow_fixed_yaml_path.exists():\n", "        workflow_fixed_yaml_path = workflow_broken_yaml_path.with_name(workflow_broken_yaml_path.name.replace(\"_broken.yaml\", \"_Fixed.yaml\"))\n", "    assert workflow_fixed_yaml_path.exists(), workflow_fixed_yaml_path\n", "\n", "    name, _ = workflow_broken_yaml_path.parts[-2:]\n", "    if name.startswith(_prefix := \"StudioDesktop_\"):\n", "        name = name[len(_prefix):]\n", "\n", "    xaml_path = xamls_dir / name / (workflow_broken_yaml_path.stem + \".xaml\")\n", "    assert xaml_path.exists(), xaml_path\n", "    error_annotation = extract_error_annotation_from_xaml(xaml_path)\n", "    errors = error_annotation.split(\"\\n\") if error_annotation else []\n", "\n", "    datapoint: WorkflowFixDatapoint = {\n", "        \"target_framework\": \"Windows\",\n", "        \"name\": name,\n", "        \"errors\": errors,\n", "        \"reasoning\": \"<TBD>\",\n", "        \"process_existing\": yaml_load(workflow_broken_yaml_path),\n", "        \"process_fixed\": yaml_load(workflow_fixed_yaml_path),\n", "    },\n", "\n", "    yaml_dump(datapoint, demos_path / f\"{name}.yaml\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["path-uri <PERSON>e\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Catch or Finally expected for TryCatch activity x 4\n", "- \"Cannot create unknown type '{http://schemas.uipath.com/workflow/activities/intelligentocr}DigitizeDocument'.\"\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_StateMachine x must have an initial state 1\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_StateMachine x must have an initial state 2\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_StateMachine x must have an initial state 3\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 1\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 2\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 3\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 4\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 5\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 6\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 7\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 8\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Trigger-less transition x of state x must contain a condition 9\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Value of type x cannot be converted to x 3\n", "- \"Cannot create unknown type '{http://schemas.microsoft.com/netfx/2009/xaml/activities}Variable({clr-namespace:System.Collections.Generic;assembly=System.Private.CoreLib}List({clr-namespace:System.Net.Mail;assembly=System.Net.Mail}MailMessage))'.\"\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Value of type x cannot be converted to x 7\n", "/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_x is not declared It may be inaccessible due to its protection level 5\n", "- \"Unable to cast object of type 'UiPath.LanguageModel.Workflow.Models.ActivityModel' to type 'UiPath.LanguageModel.Workflow.Models.ObjectModel'.\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "print(json.load(pathlib.Path(\"/workspace/data/workdir/fix_workflow/_from_dragos/_autopilot_like_structure/Dataset/Converted/Windows/StudioDesktop_Target or Input UI Element must be set 1/1_fixed.json\").open())[\"result\"])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Unidiff approach"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# experimentation with 3rd party libraries\n", "\n", "# Parse the diff from a string\n", "# patch = unidiff.PatchSet(io.StringIO(diff))\n", "\n", "# Iterate over modifications\n", "# for patched_file in patch:\n", "#     print(f\"File: {patched_file.path}\")\n", "#     for hunk in patched_file:\n", "#         print(f\"Hunk: starts at {hunk.source_start}\")\n", "#         for line in hunk:\n", "#             print(f\"  {line.value.strip()} ({line.line_type})\")\n", "\n", "# import patch\n", "# import io\n", "# patch_set = patch.PatchSet(diff)\n", "# print(patch_set.parse())\n", "# patch_set = patch.fromstring(diff.encode(\"utf-8-sig\"))\n", "# print(patch_set)\n", "# if patch_set:\n", "#     patched_content = io.StringIO(a)\n", "#     if patch_set.apply(patched_content):\n", "#         print(patched_content.getvalue())\n", "#     else:\n", "#         print(\"Failed to apply patch\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load, yaml_dump\n", "from services.studio._text_to_workflow.utils.unidiff_utils import apply_merge_conflict, apply_skip_to_the_good_bit, apply_soft_unidiff, apply_standard_unidiff, get_merge_conflict, get_skip_to_the_good_bit, get_soft_unidiff, get_unidiff\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixFlairType\n", "import difflib\n", "\n", "a = \"\"\"\\\n", "Line0\n", "\n", "Line1\n", "Line2\n", "Line77\n", "Line3\n", "Line4\n", "Line5\n", "Line6\n", "Line7\n", "\"\"\"\n", "b = \"\"\"\\\n", "Line1\n", "Line2\n", "Line99\n", "Line100\n", "Line3\n", "Line80\n", "Line81\n", "Line4\n", "\n", "Line5\n", "Line6\n", "\"\"\"\n", "\n", "# a = \"\"\"\\\n", "# 123\n", "# abc\n", "# \"\"\"\n", "\n", "# b = \"\"\"\\\n", "# 123\n", "# abc\n", "# def\n", "# \"\"\"\n", "\n", "# flair_to_use = WorkflowFixFlairType.MergeConflict\n", "# flair_to_use = WorkflowFixFlairType.MergeConflictWithLines\n", "flair_to_use = WorkflowFixFlairType.MergeHybrid\n", "\n", "# diff = get_diff(yaml_dump(demo[\"process_existing\"]), yaml_dump(demo[\"process_fixed\"]), 5)\n", "# diff = \"\\n\".join(difflib.unified_diff(a.splitlines(), b.splitlines(), lineterm=\"\", n=0))\n", "\n", "# diff = get_unidiff(a, b, 1)\n", "# diff = get_soft_unidiff(a, b, 1)\n", "# diff = get_merge_conflict(a, b)\n", "# diff = get_merge_conflict(a, b, flair=flair_to_use)\n", "diff = get_merge_conflict(a, b, flair=flair_to_use, use_ellipsis=True)\n", "# diff = get_skip_to_the_good_bit(a, b)\n", "\n", "print(diff)\n", "\n", "# altered_a = apply_standard_unidiff(a, diff)\n", "# altered_a = apply_soft_unidiff(a, diff)\n", "# altered_a = apply_merge_conflict(a, diff)\n", "altered_a = apply_merge_conflict(a, diff, flair=flair_to_use)\n", "# altered_a = apply_skip_to_the_good_bit(a, diff)\n", "print(\"-\" * 30, \"The source is:\")\n", "print(a)\n", "print(\"-\" * 30, \"The target should have been:\")\n", "print(b)\n", "print(\"-\" * 30, \"The diff application on the source is:\")\n", "print(altered_a)\n", "print(\"-\" * 30, \"The deviations from the target are:\")\n", "print(get_diff(altered_a, b))\n", "\n", "# Consistency check\n", "check_diff = \"\\n\".join(difflib.unified_diff(b.splitlines(), altered_a.splitlines(), lineterm=\"\", fromfile=\"diff.txt\", tofile=\"diff.txt\"))\n", "if not check_diff:\n", "    print(\"Consistent\")\n", "else:\n", "    print(\"Inconsistent\")\n", "    print(f\"```diff\\n{check_diff}\\n```\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths\n", "\n", "demo_paths = [*paths.get_workflow_fix_dataset_path().rglob(\"*.yaml\")]\n", "demo_paths = [p for p in demo_paths if p.parent.name != \"train_hil\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "from services.studio._text_to_workflow.utils.unidiff_utils import prepend_line_numbers\n", "\n", "problematic_paths = list(map(pathlib.Path, [  # problematic examples\n", "    # \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/eval/StudioDesktop_DateAndTextManipulation_SPLIT-d92ceeb7-5481-485f-b8f5-c0160c6e7c6b.yaml\",\n", "    # \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Value for a required activity argument x was not supplied 7.yaml\"\n", "    # \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/x is not declared It may be inaccessible due to its protection level 1.yaml\",\n", "    # \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Value for a required activity argument x was not supplied 13.yaml\",\n", "    # \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Target or Input UI Element must be set 1.yaml\",\n", "    \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Option Strict On disallows implicit conversions from x to x 6.yaml\",\n", "]))\n", "\n", "for demo_path in demo_paths:\n", "# for demo_path in problematic_paths:\n", "    # print(demo_path)\n", "    demo = yaml_load(demo_path)\n", "    a = yaml_dump(demo[\"process_existing\"])\n", "    b = yaml_dump(demo[\"process_fixed\"])\n", "\n", "    # diff = get_unidiff(a, b, span=0)\n", "    # diff = get_soft_unidiff(a, b, span=1)\n", "    # diff = get_merge_conflict(a, b, flair=flair_to_use)\n", "    diff = get_merge_conflict(a, b, flair=flair_to_use, use_ellipsis=True)\n", "    # diff = get_skip_to_the_good_bit(a, b)\n", "    # print(f\"```diff\\n{diff}\\n```\")\n", "\n", "    # altered_a = apply_soft_unidiff(a, diff)\n", "    altered_a = apply_merge_conflict(a, diff, flair=flair_to_use)\n", "    # altered_a = apply_skip_to_the_good_bit(a, diff)\n", "\n", "    check_diff = get_unidiff(b, altered_a, 0)\n", "    # check_diff = get_diff(altered_a, b, 0)\n", "    if check_diff:\n", "        print(\"Inconsistent\")\n", "        print(demo_path)\n", "        print('>' * 30 + \" The diff is \" + '>' * 30)\n", "        print(diff)\n", "        print('>' * 30 + \" The unidiff is \" + '>' * 30)\n", "        print(a.endswith(\"\\n\"), b.endswith(\"\\n\"))\n", "        print(get_unidiff(a + \"\\n\", b + \"\\n\", 2))\n", "        print('>' * 30 + \" The deviation from the target is \" + '>' * 30)\n", "        print(check_diff)\n", "        print('>' * 30 + \" The original a is \" + '>' * 30)\n", "        print(prepend_line_numbers(a))\n", "        print('>' * 30 + \" The altered a is \" + '>' * 30)\n", "        print(altered_a)\n", "        print('>' * 30 + \" The target b is \" + '>' * 30)\n", "        print(b)\n", "        print('>' * 30)\n", "        break\n", "    else:\n", "        pass\n", "        # print(\"Consistent\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datapoints = {p: yaml_load(p) for p in (paths.get_workflow_fix_dataset_path() / \"eval\").rglob(\"*.yaml\")}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Demo example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "demo_paths = list(map(pathlib.Path, [  # problematic examples\n", "    \"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Option Strict On disallows implicit conversions from x to x 6.yaml\",\n", "]))\n", "\n", "# for demo_path in demo_paths:\n", "for demo_path in demo_paths:\n", "    # print(demo_path)\n", "    demo = yaml_load(demo_path)\n", "    del demo[\"process_existing\"][\"packages\"]\n", "    del demo[\"process_existing\"][\"processName\"]\n", "    del demo[\"process_fixed\"][\"packages\"]\n", "    del demo[\"process_fixed\"][\"processName\"]\n", "    a = yaml_dump(demo[\"process_existing\"])\n", "    b = yaml_dump(demo[\"process_fixed\"])\n", "    b = b.replace(\"currentText\", \"currentItem\")\n", "\n", "    print(prepend_line_numbers(a))\n", "    print()\n", "    print(get_unidiff(a, b, span=0))\n", "    print()\n", "    print(get_soft_unidiff(a, b, span=1))\n", "    print()\n", "    print(get_merge_conflict(a, b).replace(\"=======\", \">>>>>>>\").replace(\"-------\", \"=======\"))\n", "    print()\n", "    print(get_merge_conflict(a, b, use_line_numbers=True).replace(\"=======\", \">>>>>>>\").replace(\"-------\", \"=======\"))\n", "    print()\n", "    print(get_skip_to_the_good_bit(a, b))\n", "    continue"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Error analysis of predictions and dataset exploration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dataset exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "from services.studio._text_to_workflow.utils import paths\n", "from services.studio._text_to_workflow.utils.unidiff_utils import get_soft_unidiff\n", "\n", "path_to_examples = paths.get_workflow_fix_dataset_path() / \"train\" / \"handcrafted\"\n", "for example_path in path_to_examples.rglob(\"*.yaml\"):\n", "    datapoint = yaml_load(example_path)\n", "    diff = get_soft_unidiff(yaml_dump(datapoint['process_existing']), yaml_dump(datapoint['process_fixed']), 1)\n", "    if diff:\n", "        continue\n", "\n", "    print(example_path)\n", "    # example_path.unlink()\n", "\n", "    # print(datapoint['errors'])\n", "    # print(yaml_dump(datapoint['process_existing']))\n", "    # print(yaml_dump(datapoint['process_fixed']))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problematic_names = [  # problematic examples\n", "    \"x is not declared It may be inaccessible due to its protection level 7.yaml\",\n", "    \"x expected 3.yaml\",\n", "    \"x is not declared It may be inaccessible due to its protection level 8.yaml\",\n", "    \"x is not declared It may be inaccessible due to its protection level 4.yaml\",\n", "    \"x is not declared It may be inaccessible due to its protection level 9.yaml\",\n", "    \"x expected 6.yaml\",\n", "    \"x is not declared It may be inaccessible due to its protection level 3.yaml\",\n", "]\n", "predictions_path = paths.get_workflow_fix_dataset_path() / \"predictions\"\n", "for path in predictions_path.rglob(\"*.yaml\"):\n", "    if path.name in problematic_names:\n", "        print(path)\n", "        # path.unlink()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Predictions exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import WorkflowFixPrediction, WorkflowFixDatapoint\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_test import get_triangle_lev, get_pled, get_ted\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "from services.studio._text_to_workflow.utils import paths\n", "\n", "\n", "# path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"handcrafted-gpt4o-mini-speculative-decoding-with-demos\"\n", "# path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"handcrafted-o3-mini\"\n", "# path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"handcrafted-o3-mini-softunidiff\"\n", "# path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"handcrafted-gpt4o-mini\"\n", "# path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"v6_handcrafted-gemini-2-flash-001-merge-fixyaml\"\n", "path_to_predictions = paths.get_workflow_fix_dataset_path() / \"predictions\" / \"v16-merge-hybrid-gemini\"\n", "\n", "no_prediction = []\n", "predictions: dict[str, WorkflowFixDatapoint] = {p.stem: yaml_load(p) for p in path_to_predictions.rglob(\"*.yaml\")}\n", "for name, datapoint in list(predictions.items()):\n", "    prediction : WorkflowFixPrediction = datapoint[\"prediction\"]  # type: ignore\n", "    if 'fixedWorkflow' not in prediction:\n", "        print(f\"No prediction for {name}\")\n", "        no_prediction.append(name)\n", "        del predictions[name]\n", "        continue\n", "\n", "    fixed_workflow = prediction['fixedWorkflow']\n", "    existing_workflow = datapoint['process_existing']\n", "    ground_truth = datapoint['process_fixed']\n", "\n", "    prediction[\"metrics\"] = {\n", "        \"PLED\": get_pled(existing_workflow, fixed_workflow),\n", "        \"TLD\": get_triangle_lev(existing_workflow, ground_truth, fixed_workflow),\n", "        \"TED\": get_ted(existing_workflow, fixed_workflow),\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get average and median of metrics\n", "metrics = {}\n", "for metric in [\"PLED\", \"TLD\", \"TED\"]:\n", "    for prediction in predictions.values():\n", "        metrics[metric] = metrics.get(metric, []) + [prediction[\"prediction\"][\"metrics\"][metric]]\n", "for metric in metrics:\n", "    print(f\"{metric}: {sum(metrics[metric]) / len(metrics[metric])}\")\n", "    print(f\"{metric}: {sorted(metrics[metric])[len(metrics[metric]) // 2]}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump\n", "from services.studio._text_to_workflow.utils.unidiff_utils import get_soft_unidiff\n", "from services.studio._text_to_workflow.utils.workflow_utils import cleanup_workflow\n", "from services.studio._text_to_workflow.workflow_fix.workflow_fix_test import prepare_workflow_for_metric\n", "\n", "path_to_examples = paths.get_workflow_fix_dataset_path() / \"train\" / \"handcrafted\"\n", "\n", "for name, datapoint in sorted(predictions.items(), key=lambda x: x[1][\"prediction\"][\"metrics\"][\"TLD\"], reverse=True):\n", "    prediction = datapoint[\"prediction\"]\n", "    if 'fixedWorkflow' not in prediction:\n", "        print(f\"No prediction for {name}\")\n", "        continue\n", "\n", "    fixed_workflow = prediction['fixedWorkflow']\n", "    existing_workflow = datapoint['process_existing']\n", "    ground_truth = datapoint['process_fixed']\n", "\n", "    PLED = round(prediction[\"metrics\"][\"PLED\"], 2)\n", "    TLD = round(prediction[\"metrics\"][\"TLD\"], 2)\n", "    TED = round(prediction[\"metrics\"][\"TED\"], 2)\n", "\n", "    print(path_to_examples / f\"{name}.yaml\")\n", "    print(name)\n", "    print(datapoint['errors'])\n", "    print(f\"TLD: {TLD}, PLED: {PLED}, TED: {TED}\")\n", "\n", "    print(\"Expected changes:\")\n", "    # print(get_soft_unidiff(prepare_workflow_for_metric(existing_workflow), prepare_workflow_for_metric(ground_truth), 1))\n", "    print(get_diff(prepare_workflow_for_metric(existing_workflow), prepare_workflow_for_metric(ground_truth)))\n", "    print('-' * 80)\n", "    print(\"Actual changes:\")\n", "    print(get_soft_unidiff(prepare_workflow_for_metric(existing_workflow), prepare_workflow_for_metric(fixed_workflow), 1))\n", "    print('-' * 80)\n", "    print(\"Raw prediction:\")\n", "    print(datapoint[\"prediction\"][\"model_raw_prediction\"])\n", "    print('-' * 80)\n", "    # print(yaml_dump(prediction))\n", "    # print(prediction['reasoning'])\n", "    # print(\"=\" * 80)\n", "    # print()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test word/excel scope workflow abstraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "path = pathlib.Path(\"/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/handcrafted/Activity is valid only inside WordApplicationScope 3.yaml\")\n", "workflow_dict = yaml_load(path)[\"process_fixed\"]\n", "workflow_object = Workflow(\"\", \"\", workflow_dict)\n", "workflow_serialization = workflow_object.lmyaml(include_packages=True, include_name=True, include_ids=True)\n", "\n", "print(get_soft_unidiff(yaml_dump(workflow_dict), workflow_serialization, 1))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}