{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["# Investigate missing activity for some examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "from services.studio._text_to_workflow.common import typedefs\n", "from services.studio._text_to_workflow.common.workflow import Workflow\n", "\n", "from services.studio._text_to_workflow.utils.workflow_utils import get_diff\n", "from services.studio._text_to_workflow.utils.yaml_utils import yaml_load\n", "\n", "typedefs.load()\n", "\n", "filepaths = [*pathlib.Path('./missing_current_activity').rglob(\"*.json\")]\n", "for p in filepaths:\n", "# for p in filepaths[-2:]:\n", "    print(p)\n", "    data = json.load(p.open())\n", "    print(data[\"request\"].keys())\n", "\n", "    # print(data[\"request\"][\"activityTypeDefinition\"])\n", "    # print(data[\"request\"][\"additionalTypeDefinitions\"])\n", "\n", "    workflow_str = data[\"request\"][\"workflow\"]\n", "    # print(workflow_str)\n", "\n", "    # how to convert these typedefs?\n", "    # workflow_activity_typedefs, _ = parse_workflow_conversion_typedefs(data[\"request\"][\"activityTypeDefinition\"])\n", "\n", "    workflow_dict = yaml_load(workflow_str)\n", "    workflow_obj = Workflow(\"\", \"\", workflow_dict)\n", "\n", "    print(get_diff(workflow_obj.lmyaml(), workflow_obj.lmyaml(include_unknown_properties=False)))\n", "\n", "    # print()\n", "\n", "    # synopsis = SynopsisBuilder().build(workflow_obj)\n", "    # print(synopsis)\n", "    print('\\n' * 5)\n", "\n", "    # break"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}