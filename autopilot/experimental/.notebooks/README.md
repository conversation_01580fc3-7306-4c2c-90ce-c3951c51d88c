# Notebooks and personal scripts

## Scope

Here we could include notebooks and scripts that we use to experiment for things as proof of concept.

## Notebooks considerations

You can use various headings to organize your notebook file. This will help others to more easily understand the content of the notebook.

Please clear all the output cells before commiting your notebooks, as they can often be verbose.

## Other considerations

Please be aware that these files should not induce collaboration, and should be considered as experimental. It's hard to track collaborative notebooks.

If any functionality is worth sharing and prone to collaboration, it should be isolated to a separate script file and included in the project's `scripts` directory or exported as a module in the `utils` directory. Functionalities could also be integrated within the existing modules.
