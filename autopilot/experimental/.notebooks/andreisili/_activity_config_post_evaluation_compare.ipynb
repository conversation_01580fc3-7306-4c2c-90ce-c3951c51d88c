{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_config import activity_config_evaluation\n", "from services.studio._text_to_workflow.common import schema\n", "\n", "\n", "def build_evaluation_df(evaluation_name: str) -> pd.DataFrame:\n", "    param_columns = [\n", "        \"id\",\n", "        \"activity_eval_item_id\",\n", "        \"activity_id\",\n", "        \"param_target_value\",\n", "        \"param_target_clean_value\",\n", "        \"param_pred_value\",\n", "        \"param_pred_clean_value\",\n", "        \"pred_correct\",\n", "        \"pred_clean_correct\",\n", "        \"param_name\",\n", "        \"param_type\",\n", "        \"param_type_category\",\n", "        \"param_target_value_category\",\n", "        \"param_pred_value_category\",\n", "        \"duration\",\n", "        \"framework\",\n", "        \"subset\",\n", "    ]\n", "    activity_columns = [\"id\", \"arguments\", \"global_variables\", \"local_variables\", \"activity_typedef\", \"additional_typedefs\"]\n", "    activity_df = activity_config_evaluation.read_evaluation_data(evaluation_name, \"activity_eval.feather\")[activity_columns]\n", "    param_df = activity_config_evaluation.read_evaluation_data(evaluation_name, \"param_eval.feather\")[param_columns]\n", "    df = param_df.merge(activity_df, left_on=\"activity_eval_item_id\", right_on=\"id\", validate=\"many_to_one\", suffixes=(None, \"_from_activity_df\"))\n", "    df = df.drop(\"id_from_activity_df\", axis=1, inplace=False)\n", "    return df\n", "\n", "\n", "def compare_predictions(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>, allowed_param_type_category: set[schema.ParamTypeCategory] | None, allowed_value_category: set[schema.ParamValueCategory] | None\n", ") -> tuple[pd.DataFrame, dict]:\n", "    df = df.copy()\n", "    preds_clean_correct, preds_correct, param_target_clean_values, param_pred_clean_values = [], [], [], []\n", "    for row in df.itertuples():\n", "        arguments, global_varialbes, local_variables = list(row.arguments), list(row.global_variables), list(row.local_variables)  # type: ignore\n", "        variables = arguments + global_varialbes + local_variables\n", "        activity_typedef = typedefs.parse_activity_typedef(row.activity_id, row.activity_typedef)  # type: ignore\n", "        additional_typedefs = typedefs.parse_additional_typedefs(row.additional_typedefs)  # type: ignore\n", "        param_name = row.param_name\n", "        param_type_category = row.param_type_category\n", "        param_target_value = row.param_target_value\n", "        param_pred_value = row.param_pred_value\n", "        param_target_clean_value = activity_config_params.clean_param_hard(param_target_value, param_name, activity_typedef, variables, allowed_param_type_category, allowed_value_category)  # type:  ignore\n", "        param_pred_clean_value = activity_config_params.clean_param_hard(param_pred_value, param_name, activity_typedef, variables, allowed_param_type_category, allowed_value_category)  # type:  ignore\n", "        pred_correct = activity_config_evaluation.compare_params(\n", "            param_target_value,\n", "            param_pred_value,\n", "            param_type_category,  # type: ignore\n", "            arguments,  # type: ignore\n", "            global_varialbes,  # type: ignore\n", "            local_variables,  # type: ignore\n", "        )\n", "        if pred_correct != row.pred_correct:\n", "            print(\"pred_correct\", pred_correct, row.pred_correct)\n", "            print(\"param_target_value\", param_target_value)\n", "            print(\"param_pred_value\", param_pred_value)\n", "            print(\"param_target_value\", param_target_value)\n", "            print(\"param_pred_value\", param_pred_value)\n", "            print(\"param_name\", param_name)\n", "            print(\"param_type_category\", param_type_category)\n", "            print(row.id)\n", "        pred_clean_correct = activity_config_evaluation.compare_params(\n", "            param_target_value,\n", "            param_pred_clean_value,\n", "            param_type_category,  # type: ignore\n", "            arguments,  # type: ignore\n", "            global_varialbes,  # type: ignore\n", "            local_variables,  # type: ignore\n", "        )\n", "        param_target_clean_values.append(param_target_clean_value)\n", "        param_pred_clean_values.append(param_pred_clean_value)\n", "        preds_correct.append(pred_correct)\n", "        preds_clean_correct.append(pred_clean_correct)\n", "    df[\"param_clean_target_value\"], df[\"param_pred_clean_value\"], df[\"pred_correct\"], df[\"pred_clean_correct\"] = param_target_clean_values, param_pred_clean_values, preds_correct, preds_clean_correct\n", "    score = activity_config_evaluation.calculate_score(df)\n", "    return df, score\n", "\n", "\n", "def compare_all_predictions(df: pd.DataFrame) -> tuple[pd.DataFrame, dict]:\n", "    return compare_predictions(df, allowed_param_type_category=None, allowed_value_category=None)\n", "\n", "\n", "def compare_all_output_predictions(df: pd.DataFrame) -> tuple[pd.DataFrame, dict]:\n", "    return compare_predictions(df, allowed_param_type_category={\"OutArgument\"}, allowed_value_category=None)\n", "\n", "\n", "def compare_all_input_predictions(df: pd.DataFrame) -> tuple[pd.DataFrame, dict]:\n", "    return compare_predictions(df, allowed_param_type_category={\"InArgument\", \"InOutArgument\"}, allowed_value_category=None)\n", "\n", "\n", "def compare_simple_expression_predictions(df) -> tuple[pd.DataFrame, dict]:\n", "    return compare_predictions(df, allowed_param_type_category={\"InArgument\", \"OutArgument\", \"InOutArgument\", \"ActivityAction\"}, allowed_value_category={\"variable_reference\", \"simple_expression\"})"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}