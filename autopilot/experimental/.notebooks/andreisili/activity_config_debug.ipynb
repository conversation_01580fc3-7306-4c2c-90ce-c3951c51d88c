{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.common import typedefs\n", "\n", "from services.studio._text_to_workflow.activity_config import activity_config_config, activity_config_evaluation, activity_config_params\n", "from services.studio._text_to_workflow.utils import yaml_utils"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Empty target data inconsistency\n", "\n", "Some examples do not have target parameters. This is because the activity does not have any, or it has outputs that are not serailized. There's only 50 examples\n", "in the dataset, so will add some mitigation to skip these during dataset creation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_activity = activity_config_evaluation.read_evaluation_data(\"remove-activity-action.240904\", \"activity_eval.feather\")\n", "df_param = activity_config_evaluation.read_evaluation_data(\"remove-activity-action.240904\", \"param_eval.feather\")\n", "\n", "print(len(df_activity[df_activity[\"target_params\"] == \"{}\\n\"][\"activity_id\"]))\n", "print(list(df_activity[df_activity[\"target_params\"] == \"{}\\n\"][\"activity_id\"].unique()))\n", "typedefs.get_activity_typedef(\"UiPath.GSuite.Activities.TurnOffAutomaticRepliesConnections\")\n", "\n", "list(df_param[(df_param[\"param_type_category\"] == \"OutArgument\") & (df_param[\"param_target_value\"].isna())][\"activity_id\"].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## clean_pred_params bug\n", "Somehow we do not completely clear some values that are predicted as properties. We should never return properties in cleaned values."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not typedefs.exists():\n", "    typedefs.build()\n", "typedefs.load()\n", "_config = activity_config_config.get()\n", "df = activity_config_evaluation.read_evaluation_data(\"master.240904\", \"param_eval.feather\")\n", "df[(df[\"param_type_category\"] != \"ActivityAction\") & (df[\"param_pred_clean_value_category\"] == \"property\")]\n", "for id_ in df[(df[\"param_type_category\"] != \"ActivityAction\") & (df[\"param_pred_clean_value_category\"] == \"property\")][\"id\"]:\n", "    print(id_)\n", "\n", "df_activity = activity_config_evaluation.read_evaluation_data(_evaluation_name, \"activity_eval.feather\")\n", "df_activity.head()\n", "\n", "EXAMPLE_BUG = df_activity[\n", "    df_activity[\"id\"]\n", "    == \"Portable/test/StudioDesktop_fixes_20240408_[Confluence] Backup Important Gmail email to GDrive/03___UiPath.GSuite.Activities.UploadFilesConnections.yaml\"\n", "].iloc[0]\n", "print(EXAMPLE_BUG[\"target_params\"])\n", "print(\"---------------------------------\")\n", "print(EXAMPLE_BUG[\"pred_params\"])\n", "print(\"---------------------------------\")\n", "print(EXAMPLE_BUG[\"pred_clean_params\"])\n", "\n", "examples = df_activity[df_activity[\"activity_id\"] == \"UiPath.GSuite.Activities.UploadFilesConnections\"]\n", "examples.head()\n", "\n", "print(examples.iloc[0][\"activity_typedef\"])\n", "for example in examples.itertuples():\n", "    print(example.target_params)\n", "    print(\"--------------------------------\")\n", "    print(example.pred_params)\n", "    print(\"--------------------------------\")\n", "    print(example.pred_clean_params)\n", "    print(\"================================\")\n", "\n", "activity_config_params.clean_pred_params(\n", "    yaml_utils.yaml_load(EXAMPLE_BUG[\"pred_params\"]),\n", "    list(EXAMPLE_BUG[\"global_variables\"]) + list(EXAMPLE_BUG[\"local_variables\"]) + list(EXAMPLE_BUG[\"arguments\"]),\n", "    typedefs.parse_activity_typedef(EXAMPLE_BUG[\"activity_id\"], EXAMPLE_BUG[\"activity_typedef\"]),\n", "    set(_config[\"params\"][\"allowed_types\"]),\n", "    set(_config[\"params\"][\"allowed_values\"]),\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([3107, 3108, 3109, 3110, 3111, 3112, 3113, 3114, 3115, 3116, 3117,\n", "       3118, 3119, 3120, 3121, 3122, 3123, 3124, 3125, 3126, 3127, 3128,\n", "       3129, 3130, 3131, 3132, 3133, 3134, 3135, 3136, 3137, 3138, 3139,\n", "       3140, 3141, 3142, 3143, 3144, 3145, 3146, 3147, 3148, 3149, 3150,\n", "       3151, 3152, 3153, 3154, 3155, 3156, 3157, 3158, 3159, 3160, 3161,\n", "       3162, 3163, 3164, 3165, 3166, 3167, 3168, 3169, 3170, 3171, 3172,\n", "       3173, 3174, 3175, 3176, 3177, 3178, 3179, 3180, 3181, 3182, 3183,\n", "       3184, 3185, 3186, 3187, 3188, 3189, 3190, 3191, 3192, 3193, 3194,\n", "       3195, 3196, 3197, 3198, 3199, 3200, 3201, 3202, 3203, 3204, 3205,\n", "       3206, 3207, 3208, 3209, 3210, 3211, 3212, 3213, 3214, 3215, 3216,\n", "       3217, 3218, 3219, 3220, 3221, 3222, 3223, 3224, 3225, 3226, 3227,\n", "       3228, 3229, 3230, 3231, 3232, 3233, 3234, 3235, 3236, 3237, 3238,\n", "       3239, 3240, 3241, 3242, 3243, 3244, 3245, 3246, 3247, 3248, 3249,\n", "       3250, 3251, 3252, 3253, 3254, 3255, 3256, 3257, 3258, 3259, 3260,\n", "       3261, 3262, 3263, 3264, 3265, 3266, 3267, 3268, 3269, 3270, 3271,\n", "       3272, 3273, 3274, 3275, 3276, 3277, 3278, 3279, 3280, 3281, 3282,\n", "       3283, 3284, 3285, 3286, 3287, 3288, 3289, 3290, 3291, 3292, 3293,\n", "       3294, 3295, 3296, 3297, 3298, 3299, 3300, 3301, 3302, 3303, 3304,\n", "       3305, 3306, 3307, 3308, 3309, 3310, 3311, 3312, 3313, 3314, 3315,\n", "       3316, 3317, 3318, 3319, 3320, 3321, 3322, 3323, 3324, 3325, 3326,\n", "       3327, 3328, 3329, 3330, 3331, 3332, 3333, 3334, 3335, 3336, 3337,\n", "       3338, 3339, 3340, 3341, 3342, 3343, 3344, 3345, 3346, 3347, 3348,\n", "       3349, 3350, 3351, 3352, 3353, 3354, 3355, 3356, 3357, 3358, 3359,\n", "       3360, 3361, 3362, 3363, 3364, 3365, 3366, 3367, 3368, 3369, 3370,\n", "       3371, 3372, 3373, 3374, 3375, 3376, 3377, 3378, 3379, 3380, 3381,\n", "       3382, 3383, 3384, 3385, 3386, 3387, 3388, 3389, 3390, 3391, 3392,\n", "       3393, 3394, 3395, 3396, 3397, 3398, 3399, 3400, 3401, 3402, 3403,\n", "       3404, 3405, 3406, 3407, 3408, 3409])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pickle\n", "\n", "with open(\"/workspace/data/Retrievers/ActivityConfigRetriever/state.pkl\", \"rb\") as f:\n", "    state = pickle.load(f)\n", "\n", "state[\"lookup\"][\"test\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}