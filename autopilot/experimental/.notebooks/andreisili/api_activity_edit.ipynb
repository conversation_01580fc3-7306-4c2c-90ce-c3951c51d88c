{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# add magic to autoreload the notebook\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import pprint\n", "import yaml\n", "\n", "from experimental.api_activity_edit import chat as aec\n", "from services.studio._text_to_workflow.api_activity_edit import api_activity_edit_endpoint as aee\n", "from services.studio._text_to_workflow.api_activity_edit import api_activity_edit_schema as aes\n", "\n", "import services.studio._text_to_workflow.utils.testing as ut\n", "import services.studio._text_to_workflow.utils.request_utils as ru\n", "\n", "request_context = ut.get_testing_request_context(\"en\", \"7751aee1-3ac2-4280-8d9f-e5075c7cde28\", \"API Activity Edit\")\n", "ru.set_request_context(request_context)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Chat"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["async def bootstrap_chat(datapoint_path: str) -> aec.ActivityEditChatClient:\n", "    with open(datapoint_path) as f:\n", "        datapoint = json.load(f)\n", "    chat = aec.ActivityEditChatClient(aes.BASIC_MODEL, datapoint)\n", "    user_message = datapoint[\"input\"][\"messages\"][0]\n", "    user_message: aes.Message = {\"role\": \"user\", \"content\": user_message[\"content\"]}\n", "    await chat.ask(user_message)\n", "    return chat\n", "# path = \"/data/andreisili/ml.autopilot/autopilot/.data/Autopilot.Samples/Dataset/APIActivityEdit/SmallComplexity/Cancel Latest Time Off Request in BambooHR/BambooHRChange_Time_off_Request_Status.c4ae3bbf-7dea-4e78-b59e-73530c3a8c66.json\",\n", "# chat = await bootstrap_chat(path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Endpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/data/andreisili/ml.autopilot/autopilot/.data/Autopilot.Samples/Dataset/Downloads/APIWFs/JQ/SmallComplexity/Cancel Latest Time Off Request in BambooHR/API.json\") as f:\n", "    workflow = json.load(f)\n", "idx = 0\n", "activity_ids = [\"c581e4b3-7bfb-4f8e-bec2-68504a57a734\", \"c4ae3bbf-7dea-4e78-b59e-73530c3a8c66\"]\n", "messages = [\"Configure this activity from the inputs.\", \"Configure this activity from the context\"]\n", "request = aes.EndpointRequest(\n", "    messages=[{\"role\": \"user\", \"content\": messages[idx]}],\n", "    expressionLanguage=\"jq\",\n", "    activityId=activity_ids[idx],\n", "    workflow=workflow,\n", "    schemas=None,\n", ")\n", "\n", "endpoint = aee.get()\n", "response, trace = await endpoint.edit(request)\n", "\n", "pprint.pprint(response.message)\n", "for change in response.argumentsChanges.data:\n", "    pprint.pprint(change)\n", "for change in response.propertiesChanges.data:\n", "    pprint.pprint(change)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Test Case"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "import pprint\n", "import yaml\n", "\n", "from experimental.api_activity_edit import chat as aec\n", "from services.studio._text_to_workflow.api_activity_edit import api_activity_edit_endpoint as aee\n", "from services.studio._text_to_workflow.api_activity_edit import api_activity_edit_schema as aes\n", "\n", "import services.studio._text_to_workflow.utils.testing as ut\n", "import services.studio._text_to_workflow.utils.request_utils as ru\n", "\n", "request_context = ut.get_testing_request_context(\"en\", \"7751aee1-3ac2-4280-8d9f-e5075c7cde28\", \"API Activity Edit\")\n", "ru.set_request_context(request_context)\n", "\n", "with open(\"/data/andreisili/ml.autopilot/autopilot/.data/Autopilot.Samples/Dataset/APIActivityEdit/JQ/SmallComplexity/Cancel Latest Time Off Request in BambooHR/BambooHRChange_Time_off_Request_Status.update_time_off_status.json\") as f:\n", "    datapoint = json.load(f)\n", "with open(\"/data/andreisili/ml.autopilot/autopilot/experimental/.notebooks/andreisili/API.json\") as f:\n", "    workflow = json.load(f)\n", "\n", "# Configure this activity from the inputs.\n", "# Set the status to cancelled and the id, start and end from that of the last time off request.\n", "messages = [{\n", "    \"role\": \"user\",\n", "    \"content\": \"Extra the endpoint to an input argument.\"\n", "}]\n", "language=\"jq\"\n", "\n", "schemas = {\n", "    # \"c581e4b3-7bfb-4f8e-bec2-68504a57a734\": datapoint[\"input\"][\"schemas\"][list(datapoint[\"input\"][\"schemas\"].keys())[0]],\n", "    # \"c4ae3bbf-7dea-4e78-b59e-73530c3a8c66\": datapoint[\"input\"][\"schemas\"][list(datapoint[\"input\"][\"schemas\"].keys())[1]],\n", "}\n", "# activity_idx, activity_id = 0, \"c581e4b3-7bfb-4f8e-bec2-68504a57a734\"\n", "# activity_idx, activity_id = 1, \"c4ae3bbf-7dea-4e78-b59e-73530c3a8c66\"\n", "activity_id = \"HTTP_5\"\n", "\n", "# workflow[\"do\"][0][\"Sequence_1\"][\"do\"][0][\"For_Each_1\"][\"do\"][0][\"For_Each_1#Body\"][\"do\"][1][\"If_1\"][\"do\"][1][\"If_1#Then\"][\"do\"][0][\"HTTP_5\"][\"with\"]\n", "\n", "\n", "request = aes.EndpointRequest(\n", "    messages=messages,\n", "    activityId=activity_id,\n", "    expressionLanguage=language,\n", "    workflow=workflow,\n", "    schemas=schemas,\n", ")\n", "\n", "endpoint = aee.get()\n", "response, trace = await endpoint.edit(request)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["case = {\n", "    \"request\": {\n", "        \"messages\": messages,\n", "        \"activityId\": activity_id,\n", "        \"expressionLanguage\": \"jq\",\n", "        \"workflow\": workflow,\n", "        \"schemas\": schemas,\n", "    },\n", "    \"expected\": {\n", "        \"message\": response.message,\n", "        \"argumentsChanges\": response.argumentsChanges.model_dump(),\n", "        \"propertiesChanges\": response.propertiesChanges.model_dump(),\n", "    },\n", "}\n", "\n", "with open(\"case.yaml\", \"w\") as f:\n", "    yaml.dump(case, f, sort_keys=False, width=float('inf'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}