{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.utils import paths, yaml_utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["group = \"smaller-context\"\n", "job = \"evaluation\"\n", "baseline = \"flowing-meadows-53\"\n", "challenger = \"feasible-fire-56\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["runs_path = paths.get_activity_config_runs_path()\n", "baseline_run_path = runs_path / group / job / baseline\n", "challenger_run_path = runs_path / group / \"evaluation\" / challenger\n", "baseline_datapoint_paths = sorted(baseline_run_path.rglob(\"**/*.yaml\"))\n", "challenger_datapoint_paths = sorted(challenger_run_path.rglob(\"**/*.yaml\"))\n", "assert len(baseline_datapoint_paths) == len(challenger_datapoint_paths)\n", "for baseline_datapoint_path, challenger_datapoint_path in zip(baseline_datapoint_paths, challenger_datapoint_paths, strict=False):\n", "    baseline_datapoint = yaml_utils.yaml_load(baseline_datapoint_path)\n", "    challenger_datapoint = yaml_utils.yaml_load(challenger_datapoint_path)\n", "    assert baseline_datapoint[\"id\"] == challenger_datapoint[\"id\"]\n", "\n", "    if baseline_datapoint[\"inputs_precision\"] != challenger_datapoint[\"inputs_precision\"]:\n", "        print(\"***\")\n", "        print(f\"Baseline: {baseline_datapoint_path}\")\n", "        print(f\"Challenger: {challenger_datapoint_path}\")\n", "        print(f\"Baseline={baseline_datapoint['inputs_precision']} vs. Challenger={challenger_datapoint['inputs_precision']}\")\n", "        print(\"Target\")\n", "        print(baseline_datapoint[\"target_configuration\"])\n", "        print(\"Baseline\")\n", "        print(baseline_datapoint[\"pred_configuration\"])\n", "        print(\"Challenger\")\n", "        print(challenger_datapoint[\"pred_configuration\"])\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}