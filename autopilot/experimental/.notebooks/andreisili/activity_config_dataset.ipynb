{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from services.studio._text_to_workflow.activity_config import activity_config_evaluation\n", "from services.studio._text_to_workflow.utils import paths"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["group, job, name = \"improve-whitelist\", \"evaluation\", \"ethereal-resonance-85\"\n", "run_path = paths.get_activity_config_runs_path() / group / job / name\n", "usage_path = paths.get_workdir_path() / \"Miscellaneous\" / \"activity_usage.csv\"\n", "df_usage = pd.read_csv(usage_path, index_col=\"activity_id\").rename(index={\"System.Activities.Statements.Assign\": \"UiPath.Core.Activities.Assign\"})\n", "df_eval = pd.read_feather(run_path / \"activity_eval.feather\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_eval_activity_id, _ = await activity_config_evaluation.evaluate_by_activity_id(df_eval, df_usage)\n", "df_eval_activity_id = df_eval_activity_id.sort_values(\"usage\", ascending=False)\n", "df_eval_activity_id_whitelist = df_eval_activity_id.loc[df_eval_activity_id[\"accepted\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_eval_activity_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_eval_activity_id_whitelist"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df_activity_coverage = df_eval_activity_id_whitelist[[\"package_id\", \"usage\", \"num_examples\"]]\n", "df_eval_package_id = (\n", "    df_eval_activity_id.reset_index(\"activity_id\")\n", "    .groupby(\"package_id\")\n", "    .agg(\n", "        usage=(\"usage\", \"sum\"),\n", "        num_activities=(\"activity_id\", \"count\"),\n", "        num_examples=(\"num_examples\", \"sum\"),\n", "    )\n", "    .sort_values(\"usage\", ascending=False)\n", ")\n", "df_eval_package_id_whitelist = (\n", "    df_eval_activity_id_whitelist.reset_index(\"activity_id\")\n", "    .groupby(\"package_id\")\n", "    .agg(\n", "        usage=(\"usage\", \"sum\"),\n", "        num_activities=(\"activity_id\", \"count\"),\n", "        num_examples=(\"num_examples\", \"sum\"),\n", "    )\n", "    .sort_values(\"usage\", ascending=False)\n", ")\n", "\n", "df_package_coverage = df_eval_package_id.join(df_eval_package_id_whitelist, lsuffix=\"_all\", rsuffix=\"_whitelist\", how=\"inner\")\n", "df_package_coverage[\"usage_coverage\"] = df_package_coverage[\"usage_whitelist\"] / df_package_coverage[\"usage_all\"]\n", "df_package_coverage[\"num_activities_coverage\"] = df_package_coverage[\"num_activities_whitelist\"] / df_package_coverage[\"num_activities_all\"]\n", "df_package_coverage = df_package_coverage[\n", "    [\n", "        \"usage_all\",\n", "        \"usage_whitelist\",\n", "        \"usage_coverage\",\n", "        \"num_activities_all\",\n", "        \"num_activities_whitelist\",\n", "        \"num_activities_coverage\",\n", "        \"num_examples_all\",\n", "        \"num_examples_whitelist\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_activity_coverage.to_markdown())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_package_coverage.to_markdown())"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}