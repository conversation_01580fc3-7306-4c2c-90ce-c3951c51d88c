{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from services.studio._text_to_workflow.activity_config import activity_config_config, activity_config_evaluation\n", "\n", "_config = activity_config_config.get()\n", "# _evaluation_name = \"frosty-dew-135\"\n", "# _evaluation_name = \"scarlet-wind-136\"\n", "# _evaluation_name = \"fluent-flower-141\"\n", "# _evaluation_name = \"stellar-snowflake-143\"\n", "# _evaluation_name = \"cosmic-breeze-145\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_baseline_name = \"fluent-flower-141\"\n", "_challenger_name = \"neat-valley-147\"\n", "df_param_baseline = activity_config_evaluation.read_evaluation_data(_baseline_name, \"param_eval.feather\")\n", "df_param_challenger = activity_config_evaluation.read_evaluation_data(_challenger_name, \"param_eval.feather\")\n", "print(len(df_param_baseline), len(df_param_challenger))\n", "\n", "diff = df_param_baseline[~df_param_baseline[\"id\"].isin(df_param_challenger[\"id\"])]\n", "diff\n", "# diff = df_param_challenger[~df_param_challenger[\"id\"].isin(df_param_baseline[\"id\"])]\n", "# diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import clear_output\n", "\n", "_baseline_name = \"fluent-flower-141\"\n", "_challenger_name = \"neat-valley-147\"\n", "df_activity_baseline = activity_config_evaluation.read_evaluation_data(_baseline_name, \"activity_eval.feather\")\n", "df_activity_challenger = activity_config_evaluation.read_evaluation_data(_challenger_name, \"activity_eval.feather\")\n", "join = df_activity_baseline.set_index(\"id\").join(df_activity_challenger.set_index(\"id\"), on=\"id\", lsuffix=\"_baseline\", rsuffix=\"_challenger\")\n", "diff = join[join[\"target_clean_params_baseline\"] != join[\"target_clean_params_challenger\"]]\n", "for row in diff.itertuples():\n", "    # a = yaml_utils.yaml_load(row.target_params_baseline)\n", "    # b = yaml_utils.yaml_load(row.target_params_challenger)\n", "    # if a != b:\n", "    print(row.Index)\n", "    print(row.target_clean_params_baseline)\n", "    print(row.target_clean_params_challenger, flush=True)\n", "    # print(row.target_clean_params_baseline.encode(\"utf-8\"))\n", "    # print(row.target_clean_params_challenger.encode(\"utf-8\"))\n", "    # input()\n", "    # clear_output(wait=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_param = activity_config_evaluation.read_evaluation_data(_evaluation_name, \"param_eval.feather\")\n", "df_param.head()\n", "len(df_param)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_activity = activity_config_evaluation.read_evaluation_data(_evaluation_name, \"activity_eval.feather\")\n", "df_activity.head()\n", "len(df_activity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scores_by_framework = activity_config_evaluation.calculate_scores_by_column(df_param, \"framework\")\n", "scores_by_framework = pd.DataFrame([{\"name\": name, **scores} for name, scores in scores_by_framework.items()])\n", "scores_by_framework"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scores_by_param_type_category = activity_config_evaluation.calculate_scores_by_column(df_param, \"param_type_category\")\n", "scores_by_param_type_category = pd.DataFrame([{\"name\": name, **scores} for name, scores in scores_by_param_type_category.items()]).dropna()\n", "scores_by_param_type_category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scores_by_param_value_category = activity_config_evaluation.calculate_scores_by_column(df_param, \"param_pred_clean_value_category\")\n", "scores_by_param_value_category = pd.DataFrame([{\"name\": name, **scores} for name, scores in scores_by_param_value_category.items()]).dropna()\n", "scores_by_param_value_category"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_scores = activity_config_evaluation.calculate_inputs_scores_by_pred_value_category(df_param)\n", "input_scores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_scores = activity_config_evaluation.calculate_outputs_scores(df_param)\n", "output_scores = pd.DataFrame([output_scores])\n", "output_scores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_input_false = activity_config_evaluation.get_input_false_predictions(df_param)\n", "# columns = [\"activity_id\", \"param_name\", \"param_type\", \"param_target_clean_value\", \"param_pred_clean_value\", \"param_target_value\", \"param_pred_value\", \"framework\", \"pred_clean_correct\"]\n", "columns = df_input_false.columns\n", "df_input_false = df_input_false[columns]\n", "df_input_false"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = activity_config_evaluation.filter_df_by_column(df_param, \"param_type_category\", [\"InArgument\", \"InOutArgument\"])\n", "df = df[(df[\"param_pred_clean_value_category\"] == \"simple_expression\") | (df[\"param_target_clean_value_category\"] == \"simple_expression\")]\n", "df = df[[\"id\", \"param_target_value\", \"param_target_clean_value\", \"param_pred_clean_value\", \"pred_clean_correct\"]]\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.studio._text_to_workflow.activity_config.activity_config_evaluation import paths, yaml_load\n", "\n", "errors = set()\n", "for row in df_input_false.itertuples():\n", "    path = paths.get_activity_config_evaluation_path() / _evaluation_name / str(row.activity_eval_item_id)\n", "    errors.add(path)\n", "for path in errors:\n", "    eval_item = yaml_load(path)\n", "\n", "\n", "    print(path.as_posix())\n", "    print(\"*** Inputs Precision\")\n", "    print(eval_item[\"inputs_precision\"])\n", "    print(\"*** Activity\")\n", "    print(eval_item[\"activity_id\"])\n", "    print(\"*** Target\")\n", "    print(eval_item[\"target_params\"])\n", "    print(\"*** Pred\")\n", "    print(eval_item[\"pred_params\"])\n", "    print(\"*** Target Clean\")\n", "    print(eval_item[\"target_clean_params\"])\n", "    print(\"*** Pred Clean\")\n", "    print(eval_item[\"pred_clean_params\"])\n", "    print(\"*** Workflow\")\n", "    print(eval_item[\"workflow\"], flush=True)\n", "\n", "    input(\"Press Enter to continue...\")\n", "    clear_output(wait=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}