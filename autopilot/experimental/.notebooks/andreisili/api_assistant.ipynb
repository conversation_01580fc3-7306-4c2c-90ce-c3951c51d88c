{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import json\n", "import copy\n", "\n", "from services.studio._text_to_workflow.common.api_workflow.schema import ActivityAddChange\n", "from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import WorkflowAssistantChangeResponse\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': {'type': 'object', 'properties': {}},\n", " 'root': {'thought': 'Sequence',\n", "  'activity': 'Sequence',\n", "  'id': 'Sequence_1',\n", "  'do': [{'thought': 'HTTP GET request to httpbin.org/get',\n", "    'activity': 'HttpRequest',\n", "    'id': 'HttpRequest_1',\n", "    'with': {'method': 'GET', 'endpoint': '${\"http://httpbin.org/get\"}'}},\n", "   {'thought': 'HTTP POST request to httpbin.org/post',\n", "    'activity': 'HttpRequest',\n", "    'id': 'HttpRequest_2',\n", "    'with': {'method': 'POST', 'endpoint': '${\"http://httpbin.org/post\"}'}}]}}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(\"/data/andreisili/ml.autopilot/autopilot/rrparis/edit-workflow/response.json\") as f:\n", "    response = json.load(f)\n", "\n", "new_workflow = json.loads(response[\"newWorkflowContent\"])\n", "new_workflow"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["change_1 = ActivityAddChange(\n", "    parentId=\"Sequence_1\",\n", "    containerId=\"do\",\n", "    previousSiblingId=None,\n", "    data=new_workflow[\"root\"][\"do\"][0]\n", ")\n", "change_2 = ActivityAddChange(\n", "    parentId=\"Sequence_1\",\n", "    containerId=\"do\",\n", "    previousSiblingId=new_workflow[\"root\"][\"do\"][0][\"id\"],\n", "    data=new_workflow[\"root\"][\"do\"][1]\n", ")\n", "changes = [change_1.model_dump(mode=\"json\"), change_2.model_dump(mode=\"json\")]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["response_with_changes = copy.deepcopy(response)\n", "response_with_changes[\"changes\"] = changes\n", "response_model = WorkflowAssistantChangeResponse.model_validate(response_with_changes)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'prompts': None,\n", " 'path': None,\n", " 'scenario': 'edit-workflow',\n", " 'newWorkflowContent': '{\"input\":{\"type\":\"object\",\"properties\":{}},\"root\":{\"thought\":\"Sequence\",\"activity\":\"Sequence\",\"id\":\"Sequence_1\",\"do\":[{\"thought\":\"HTTP GET request to httpbin.org/get\",\"activity\":\"HttpRequest\",\"id\":\"HttpRequest_1\",\"with\":{\"method\":\"GET\",\"endpoint\":\"${\\\\\"http://httpbin.org/get\\\\\"}\"}},{\"thought\":\"HTTP POST request to httpbin.org/post\",\"activity\":\"HttpRequest\",\"id\":\"HttpRequest_2\",\"with\":{\"method\":\"POST\",\"endpoint\":\"${\\\\\"http://httpbin.org/post\\\\\"}\"}}]}}',\n", " 'previousWorkflowContent': '',\n", " 'changes': [{'type': 'activityAddChange',\n", "   'parentId': 'Sequence_1',\n", "   'containerId': 'do',\n", "   'previousSiblingId': None,\n", "   'data': {'thought': 'HTTP GET request to httpbin.org/get',\n", "    'activity': 'HttpRequest',\n", "    'id': 'HttpRequest_1',\n", "    'with': {'method': 'GET', 'endpoint': '${\"http://httpbin.org/get\"}'}}},\n", "  {'type': 'activityAddChange',\n", "   'parentId': 'Sequence_1',\n", "   'containerId': 'do',\n", "   'previousSiblingId': 'HttpRequest_1',\n", "   'data': {'thought': 'HTTP POST request to httpbin.org/post',\n", "    'activity': 'HttpRequest',\n", "    'id': 'HttpRequest_2',\n", "    'with': {'method': 'POST', 'endpoint': '${\"http://httpbin.org/post\"}'}}}],\n", " 'message': \"I've edited the workflow based on your request.\",\n", " 'jitCommands': [],\n", " 'testCase': False}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(\"response_with_changes.json\", \"w\") as f:\n", "    json.dump(response_model.model_dump(mode=\"json\"), f, indent=True)\n", "response_model.model_dump(mode=\"json\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}