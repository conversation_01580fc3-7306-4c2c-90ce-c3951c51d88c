{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib as pl\n", "\n", "from services.studio._text_to_workflow.common import workflow as wf\n", "from services.studio._text_to_workflow.utils import yaml_utils as yu\n", "\n", "workflow = wf.Workflow(\"\", \"\", yu.yaml_load(pl.Path(\"/home/<USER>/code/ml.studio/data/Autopilot.Samples/Dataset/WorkflowGeneration/Portable/static/DivisibleByFourChecker.yaml\"))[\"process\"])\n", "workflow.to_dict()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workflow_clone = workflow.clone()\n", "workflow_clone.to_dict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib as pl\n", "\n", "import tqdm\n", "\n", "from services.studio._text_to_workflow.common import workflow as wf\n", "from services.studio._text_to_workflow.utils import yaml_utils as yu\n", "\n", "path = \"/home/<USER>/code/ml.studio/data/Autopilot.Samples.Prod/Sanitized/Windows\"\n", "for path in tqdm.tqdm(sorted(pl.Path(path).glob(\"**/*.yaml\"))):\n", "    example = yu.yaml_load(path)\n", "    workflow = wf.Workflow(example[\"description\"], example[\"plan\"], example[\"process\"])\n", "    workflow.to_dict()\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}