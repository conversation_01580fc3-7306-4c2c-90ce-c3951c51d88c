{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import wandb\n", "import weave\n", "from weave.trace.weave_client import CallsFilter\n", "\n", "from services.studio._text_to_workflow.utils import paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = weave.init(\"<EMAIL>\", settings={\"print_call_link\": False})\n", "calls_iter = client.calls(filter=CallsFilter(op_names=[\"weave:///uipath/<EMAIL>/op/clean_pred_params:twA7ji1joo187F0HY9vUpAhNVgYBcUrOXMpYDSD0eG4\"]))\n", "for call in calls_iter:\n", "    call.delete()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mwandb\u001b[0m: Using wandb-core as the SDK backend. Please refer to https://wandb.me/wandb-core for more information.\n", "\u001b[34m\u001b[1mwandb\u001b[0m: Currently logged in as: \u001b[33mandreilazauipath\u001b[0m (\u001b[33muipath\u001b[0m). Use \u001b[1m`wandb login --relogin`\u001b[0m to force relogin\n"]}, {"data": {"text/html": ["Tracking run with wandb version 0.18.0"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Run data is saved locally in <code>/workspace/data/Logs/wandb/run-20240913_193222-_assets</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Syncing run <strong><a href='https://wandb.ai/uipath/wingman-activity-configuration/runs/_assets' target=\"_blank\">_assets</a></strong> to <a href='https://wandb.ai/uipath/wingman-activity-configuration' target=\"_blank\">Weights & Biases</a> (<a href='https://wandb.me/run' target=\"_blank\">docs</a>)<br/>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View project at <a href='https://wandb.ai/uipath/wingman-activity-configuration' target=\"_blank\">https://wandb.ai/uipath/wingman-activity-configuration</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run at <a href='https://wandb.ai/uipath/wingman-activity-configuration/runs/_assets' target=\"_blank\">https://wandb.ai/uipath/wingman-activity-configuration/runs/_assets</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["client = wandb.init(project=\"wingman-activity-configuration\", entity=\"uipath\", job_type=\"_assets\",dir=paths.get_logs_path(), name=\"_assets\", id=\"_assets\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["table = wandb.Table(\n", "    columns=[\"model\", \"provider\", \"input cost ($/1000T)\", \"output cost ($/1000T)\"],\n", "    data=[\n", "        [\"gpt-35\", \"azure\", 0.0005, 0.0015],\n", "        [\"gpt-4o\", \"azure\", 0.005, 0.015],\n", "        [\"gpt-4o-mini\", \"azure\", 0.00015, 0.0006],\n", "        [\"gpt-4-turbo\", \"azure\", 0.03, 0.06],\n", "        [\"llama3-8b\", \"azure\", 0.0003, 0.00061],\n", "        [\"llama3-70b\", \"azure\", 0.00268, 0.00354],\n", "        [\"llama3-405b\", \"azure\", 0.00533, 0.016],\n", "    ]\n", ")\n", "client.log({\"inference_costs_240912\": table})\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "faec23c8a22d4d65bf41ad8178433dbe", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Label(value='0.010 MB of 0.010 MB uploaded\\r'), FloatProgress(value=1.0, max=1.0)))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run <strong style=\"color:#cdcd00\">_assets</strong> at: <a href='https://wandb.ai/uipath/wingman-activity-configuration/runs/_assets' target=\"_blank\">https://wandb.ai/uipath/wingman-activity-configuration/runs/_assets</a><br/> View project at: <a href='https://wandb.ai/uipath/wingman-activity-configuration' target=\"_blank\">https://wandb.ai/uipath/wingman-activity-configuration</a><br/>Synced 5 W&B file(s), 0 media file(s), 4 artifact file(s) and 1 other file(s)"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Find logs at: <code>/workspace/data/Logs/wandb/run-20240913_193222-_assets/logs</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["client.finish()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}