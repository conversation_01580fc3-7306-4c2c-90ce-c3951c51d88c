import functools
import pathlib
import shutil

import omegaconf
import pandas as pd
import torch
import tqdm
import wandb
import wandb.wandb_run
from torch.utils.data import DataLoader, SequentialSampler
from torchtune import data, training, utils

from experimental.activity_config.finetuning import common, dataset
from experimental.activity_config.finetuning.inference import InferenceRecipe
from services.studio._text_to_workflow.activity_config import activity_config_evaluation, activity_config_messages
from services.studio._text_to_workflow.activity_config.activity_config_schema import (
    ActivityConfigConfiguration,
    ActivityConfigContext,
    ActivityConfigEvaluationScores,
)
from services.studio._text_to_workflow.common import typedefs
from services.studio._text_to_workflow.utils import yaml_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage

_logger = utils.get_logger("DEBUG")


class FinetuneEvaluationRecipe:
    def __init__(self, cfg: omegaconf.DictConfig) -> None:
        self._device = utils.get_device(device=cfg.device)
        self._dtype = training.get_dtype(cfg.dtype, device=self._device)
        self._persist = cfg.persist
        self._inference_kv_cache_batch_size = cfg.inference.kv_cache_batch_size
        self._evaluation_kv_cache_batch_size = cfg.dataset.loader.batch_size

    def setup(self, cfg: omegaconf.DictConfig) -> None:
        self._run_dir = pathlib.Path(cfg.inference.checkpointer.checkpoint_dir)
        self._inference_recipe = self._setup_inference_recipe(cfg.inference)
        self._dataset, self._dataloader = self._setup_data(cfg.dataset)
        self._wandb = self._setup_wandb()
        self.inference_cfg = cfg.inference

    @torch.inference_mode()
    def evaluate(self) -> None:
        # Set the kv cache batch size to the same as the data loader batch size
        self.inference_cfg.kv_cache_batch_size = self._evaluation_kv_cache_batch_size

        checkpoint_files = self._get_checkpoint_files_by_epoch()
        latest_epoch, best_epoch, best_precision, best_recall = 0, 0, 0, 0
        desc = f"Evaluation Best: {0:03d}|{0:.4f}|{0:.4f}"
        pbar = tqdm.tqdm(total=len(checkpoint_files), desc=desc, dynamic_ncols=True, unit="Epoch")
        for epoch in sorted(checkpoint_files.keys()):
            self._setup_inference_recipe_for_epoch(checkpoint_files[epoch])
            epoch_summary = self._evaluate_epoch(epoch)
            precision = epoch_summary["overall"]["overall"]["precision"] or 0.0
            recall = epoch_summary["overall"]["overall"]["recall"] or 0.0
            latest_epoch = epoch
            if precision > best_precision:
                best_epoch = epoch
                best_precision = precision
                best_recall = recall
            log_dict = {
                "Evaluation/test.precision": epoch_summary["overall"]["overall"]["precision"],
                "Evaluation/test.recall": epoch_summary["overall"]["overall"]["recall"],
                "Evaluation/test.inputs.precision": epoch_summary["inputs"]["overall"]["precision"],
                "Evaluation/test.inputs.recall": epoch_summary["inputs"]["overall"]["recall"],
                "Evaluation/test.outputs.precision": epoch_summary["outputs"]["overall"]["precision"],
                "Evaluation/test.outputs.recall": epoch_summary["outputs"]["overall"]["recall"],
                "Epoch": epoch,
            }
            self._wandb.log(log_dict)
            pbar.update(1)
            pbar.set_description(f"Evaluation. Best: {best_epoch:03d}|{best_precision:.4f}|{best_recall:.4f}")
        # Set the kv cache batch size to back to the original inference value
        self.inference_cfg.kv_cache_batch_size = self._inference_kv_cache_batch_size
        if self._persist:
            self.select_model(checkpoint_files, latest_epoch, best_epoch)

    def select_model(self, checkpoint_files: dict[int, list[str]], latest_epoch: int, best_epoch: int):
        best_checkpoint_files = []
        # Copy the best checkpoint files. This will be used for inference.
        for checkpoint_file in checkpoint_files[best_epoch]:
            name, suffix = checkpoint_file.split("_")
            index, extension = suffix.split(".")
            best_checkpoint_file = f"{name}.best.{extension}"
            best_checkpoint_files.append(best_checkpoint_file)
            from_, to_ = self._run_dir / checkpoint_file, self._run_dir / best_checkpoint_file
            shutil.copy(from_, to_)
        # Remove training checkpoint files.
        for epoch_checkpoint_files in checkpoint_files.values():
            for checkpoint_file in epoch_checkpoint_files:
                checkpoint_file_path = self._run_dir / checkpoint_file
                checkpoint_file_path.unlink()
        recipe_state_path = self._run_dir / "recipe_state.pt"
        if recipe_state_path.exists():
            recipe_state_path.unlink()
        # Set the best checkpoint files in the config
        self.inference_cfg.checkpointer.checkpoint_files = best_checkpoint_files

    def _setup_inference_recipe(self, cfg: omegaconf.DictConfig) -> InferenceRecipe:
        recipe = InferenceRecipe(cfg)
        recipe.setup(cfg)
        return recipe

    def _setup_inference_recipe_for_epoch(self, checkpoint_files: list[str]) -> None:
        cfg_current_epoch = omegaconf.OmegaConf.create({"checkpointer": {"checkpoint_files": checkpoint_files}})
        cfg_current_epoch = omegaconf.OmegaConf.merge(self.inference_cfg, cfg_current_epoch)
        assert isinstance(cfg_current_epoch, omegaconf.DictConfig)
        self._inference_recipe.setup(cfg_current_epoch)

    def _setup_data(self, cfg_dataset: omegaconf.DictConfig) -> tuple[dataset.ActivityConfigChatDataset, torch.utils.data.DataLoader]:
        _tokenizer = self._inference_recipe._tokenizer
        ds = dataset.activity_config_chat_dataset(tokenizer=_tokenizer, inference=True, **omegaconf.OmegaConf.to_object(cfg_dataset.data))  # type: ignore
        collate_fn = functools.partial(data.padded_collate, padding_idx=_tokenizer.pad_id, pad_direction="right", keys_to_pad=["tokens", "labels"])
        dl = DataLoader(dataset=ds, sampler=SequentialSampler(ds), collate_fn=collate_fn, batch_size=cfg_dataset.loader.batch_size, drop_last=False)
        _logger.info(f"Dataset and Sampler are initialized for {cfg_dataset.data.subsets}.")
        return ds, dl

    def _setup_wandb(self) -> wandb.wandb_run.Run:
        """
        Initialize wandb run and define metrics for training.
        """
        assert wandb.run is not None, "Wandb run is not initialized."
        run = wandb.run
        epoch_metric = run.define_metric("Epoch", hidden=True)
        run.define_metric("Evaluation/*", step_metric=epoch_metric, summary="max", step_sync=True)
        return run

    def _get_checkpoint_files_by_epoch(self) -> dict[int, list[str]]:
        checkpoint_files = {}
        pt_paths = sorted(self._run_dir.glob("model-*.safetensors"))
        for pt_path in pt_paths:
            name, epoch = pt_path.stem.split("_")
            epoch = int(epoch)
            if epoch not in checkpoint_files:
                checkpoint_files[epoch] = []
            checkpoint_files[epoch].append(pt_path.name)
        return checkpoint_files

    def _evaluate_epoch(self, epoch: int) -> ActivityConfigEvaluationScores:
        predictions: list[ActivityConfigConfiguration] = []
        targets: list[ActivityConfigConfiguration] = []
        contexts: list[ActivityConfigContext] = []
        usages: list[TokenUsage] = []
        durations: list[float] = []
        ppbar = tqdm.tqdm(self._dataloader, total=len(self._dataloader), desc=f"{epoch:03d}", dynamic_ncols=True, unit="Batch", leave=False)
        for batch in ppbar:
            for index in batch["index"].tolist():
                interaction = self._dataset.get_item_interaction(index)
                contexts.append(interaction["context"])
                targets.append(interaction["configuration"])
            utils.batch_to_device(batch, self._device)
            batch_predictions, batch_usages, batch_duration = self._inference_recipe.generate(batch["tokens"])
            pred_configurations = [activity_config_messages.load_completion(prediction) for prediction in batch_predictions]
            predictions.extend(pred_configurations)
            usages.extend(batch_usages)
            durations.extend([batch_duration / len(batch_predictions)] * len(batch_predictions))
        param_eval_items = []
        for i, (prediction, target, context, usage, duration) in enumerate(zip(predictions, targets, contexts, usages, durations, strict=False)):
            activity_eval_item = activity_config_evaluation.get_activity_eval_item(
                prediction=prediction,
                target=target,
                context=context,
                duration=duration,
                # doesn't matter from here
                framework=context["target_framework"],
                usage=usage.to_json(),
                completion=yaml_utils.yaml_dump(prediction),
                subset="test",
                activity_eval_item_id=f"{i}",
            )
            param_eval_items.extend(activity_config_evaluation.get_param_eval_items(activity_eval_item))
        param_eval_df = pd.DataFrame([item for item in param_eval_items])
        summary = activity_config_evaluation.calculate_evaluation_scores(param_eval_df)
        return summary


def init() -> omegaconf.DictConfig:
    typedefs.load()
    run_config = pathlib.Path(common.init_config_from_base().finetuned_model_path) / "config.yaml"
    cfg = common.init_config_from_run(run_config)
    common.init_debugger(cfg.debugger, 0)
    common.init_logging(cfg.logging, 0)
    common.resume_run(cfg, 0)
    return cfg


def main() -> None:
    """Usage: tune run --nnodes 1 --nproc_per_node 1 finetune_full_evaluation --config /full/path/to/training/run/config.yaml"""
    cfg = init()
    recipe = FinetuneEvaluationRecipe(cfg.evaluation)
    recipe.setup(cfg.evaluation)
    recipe.evaluate()
    common.log_config(cfg, 0)


if __name__ == "__main__":
    main()
