# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the BSD-style license found in the
# LICENSE file in the root directory of this source tree.
import itertools
import time

import omegaconf
import torch
from torchtune import config, data, generation, modules, training, utils
from torchtune.models import llama3

from services.studio._text_to_workflow.common import schema as cs
from services.studio._text_to_workflow.utils.inference import llm_schema as ls

logger = utils.get_logger("DEBUG")


class InferenceRecipe:
    """
    Recipe for generating tokens from a dense Transformer-based LLM.

    Currently this recipe supports single-GPU generation only. Speculative
    decoding is not supported.

    For more details on how to use this recipe for generation, please see our
    tutorial: https://pytorch.org/torchtune/main/tutorials/e2e_flow.html#generation

    For using this recipe with a quantized model, please the following section of
    the above tutorial:
    https://pytorch.org/torchtune/main/tutorials/e2e_flow.html#speeding-up-generation-using-quantization
    """

    def __init__(self, cfg: omegaconf.DictConfig) -> None:
        self._device = utils.get_device(device=cfg.device)
        self._dtype = training.get_dtype(dtype=cfg.dtype, device=self._device)
        self._compiled = False
        training.set_seed(seed=cfg.seed)

    def setup(self, cfg: omegaconf.DictConfig) -> None:
        # Parameter weights_only needs to be False when loading a quantized model and checkpoint should be FullModelTorchTuneCheckpointer.
        checkpoint = self._load_checkpoint(cfg)
        self._tokenizer = self._setup_tokenizer(cfg.tokenizer)
        self._model = self._setup_model(cfg, model_state_dict=checkpoint[training.MODEL_KEY])
        self._model_type = cfg.checkpointer.model_type
        self._max_new_tokens = cfg.max_new_tokens
        self._temperature = cfg.temperature
        self._top_k = cfg.top_k
        self._compile = cfg.compile
        self._kv_cache_batch_size = cfg.kv_cache_batch_size
        # Setup kv-cache for faster generation
        self._setup_kv_cache(cfg)
        # Compile the model for faster generation.
        self.generate_next_token_fn = generation.generate_next_token
        if self._compile and not self._compiled:
            self.generate_next_token_fn = self._compile_model()
            self._compiled = True

    def _load_checkpoint(self, cfg: omegaconf.DictConfig) -> dict:
        checkpointer = training.FullModelHFCheckpointer(**omegaconf.OmegaConf.to_object(cfg.checkpointer))  # type: ignore
        return checkpointer.load_checkpoint()

    def _setup_tokenizer(self, cfg_tokenizer: omegaconf.DictConfig) -> llama3.Llama3Tokenizer:
        return llama3.llama3_tokenizer(**omegaconf.OmegaConf.to_object(cfg_tokenizer))  # type: ignore

    def _setup_model(self, cfg, model_state_dict: dict) -> modules.TransformerDecoder:
        with training.set_default_dtype(self._dtype), self._device:
            model = config.instantiate(cfg.model)
        model.load_state_dict(model_state_dict)
        # Validate model was loaded in with the expected dtype.
        training.validate_expected_param_dtype(model.named_parameters(), dtype=self._dtype)
        # Set model to eval mode.
        model.eval()
        logger.info(f"Model is initialized with precision {self._dtype}.")
        return model

    def _setup_kv_cache(self, cfg: omegaconf.DictConfig) -> None:
        with self._device:
            self._model.setup_caches(batch_size=self._kv_cache_batch_size, dtype=self._dtype, decoder_max_seq_len=cfg.tokenizer.max_seq_len)

    def convert_prompt_to_tokens(self, prompt: list[cs.Message] | str) -> torch.Tensor:
        """
        Convert the prompt string to a user message with optional system messages and tokenize using the prompt template defined on the tokenizer.
        """
        if isinstance(prompt, str):
            # If the prompt is a string, we need to convert it to a list of default system message and user message.
            prompt = [cs.Message(role="user", content=prompt)]
        messages = [data.Message(**message) for message in prompt] + [data.Message(role="assistant", content="")]
        tokens_list = self._tokenizer({"messages": messages}, inference=True)["tokens"]
        tokens_tensor = torch.tensor(tokens_list, dtype=torch.int, device=self._device)
        return tokens_tensor

    def _compile_model(self) -> None:
        logger.info("Starting compilation to improve generation performance ...")
        t0 = time.perf_counter()
        self.generate_next_token_fn = torch.compile(generation.generate_next_token, dynamic=True, fullgraph=True)
        prompt_tokens = self.convert_prompt_to_tokens("This is a warmup run.")
        self.generate(prompt_tokens)
        t = time.perf_counter() - t0
        logger.info(f"Warmup run for compiled model takes: {t:.02f} sec")

    def log_stats(self, completion: str, usage: ls.TokenUsage, elapsed_time: float) -> None:
        model_size = sum([p.numel() * p.dtype.itemsize for p in itertools.chain(self._model.parameters(), self._model.buffers())])
        tokens_sec = usage.completion_tokens / elapsed_time
        logger.info(f"Time for inference: {elapsed_time:.02f} sec total, {tokens_sec:.02f} tokens/sec")
        logger.info(f"Bandwidth achieved: {model_size * tokens_sec / 1e9:.02f} GB/s")
        logger.info(f"Memory used: {torch.cuda.max_memory_allocated() / 1e9:.02f} GB")
        logger.info(f"Prompt|Completion|Total tokens: {usage.prompt_tokens}|{usage.completion_tokens}|{usage.total_tokens}")
        logger.info(f"Completion:\n{completion}")

    @torch.inference_mode()
    def generate(self, prompt_tokens: torch.Tensor) -> tuple[list[str], list[ls.TokenUsage], float]:
        t0 = time.perf_counter()
        prompt_tokens = prompt_tokens.unsqueeze(0) if prompt_tokens.ndim == 1 else prompt_tokens
        batch_size, sequence_length = prompt_tokens.shape
        num_missing_samples_in_batch = self._kv_cache_batch_size - batch_size
        if num_missing_samples_in_batch > 0:
            dtype, device, num_missing_samples = prompt_tokens.dtype, prompt_tokens.device, self._kv_cache_batch_size - batch_size
            pad_sequence = torch.tensor(self._tokenizer.pad_id, dtype=dtype, device=device).repeat(num_missing_samples, sequence_length)
            prompt_tokens = torch.cat((prompt_tokens, pad_sequence), dim=0)
        all_tokens = generation.generate(
            model=self._model,
            prompt=prompt_tokens,
            max_generated_tokens=self._max_new_tokens,
            temperature=self._temperature,
            top_k=self._top_k,
            stop_tokens=self._tokenizer.stop_tokens,
            pad_id=self._tokenizer.pad_id,
            custom_generate_next_token=self.generate_next_token_fn,
        )[0]
        if num_missing_samples_in_batch:
            prompt_tokens = prompt_tokens[:batch_size]
            all_tokens = all_tokens[:batch_size]
        self._model.reset_caches()
        completions, usages = [], []
        for i in range(batch_size):
            prompt_tokens_sample, all_tokens_sample = prompt_tokens[i], all_tokens[i]
            completion_tokens_sample = all_tokens_sample[prompt_tokens_sample.shape[0] :]
            completion = self._tokenizer.decode(completion_tokens_sample.tolist())
            usage = ls.TokenUsage(
                model=self._model_type,
                prompt_tokens=prompt_tokens_sample.shape[0],
                completion_tokens=completion_tokens_sample.shape[0],
                total_tokens=all_tokens_sample.shape[0],
            )
            completions.append(completion)
            usages.append(usage)
        elapsed_time = time.perf_counter() - t0
        return completions, usages, elapsed_time

    def completion(self, prompt: list[cs.Message] | str, verbose: bool = False) -> tuple[str, ls.TokenUsage]:
        prompt_tokens = self.convert_prompt_to_tokens(prompt)
        completions, usages, elapsed_time = self.generate(prompt_tokens)
        completion, usage = completions[0], usages[0]
        if verbose:
            self.log_stats(completion, usage, elapsed_time)
        return completion, usage
