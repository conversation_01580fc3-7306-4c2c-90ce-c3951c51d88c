# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the BSD-style license found in the
# LICENSE file in the root directory of this source tree.

import functools
import time
import warnings

import omegaconf
import torch
import torch.distributed
import tqdm
import wandb
import wandb.wandb_run
from omegaconf import DictConfig
from torch.utils.data import DataLoader, DistributedSampler
from torchtune import config, data, modules, training, utils
from torchtune.models import llama3
from torchtune.training import activations

from experimental.activity_config.finetuning import common, dataset
from services.studio._text_to_workflow.common import typedefs

_logger = utils.get_logger("DEBUG")


class FinetuneFullRecipe:
    """
    Full finetuning recipe for dense transformer-based LLMs such as Llama2. This recipe supports
    distributed training and can be run on a single node (1 to 8 GPUs).

    Features:
        - FSDP. Supported using PyTorch's FSDP APIs. CPU offload of parameters, gradients, and optimizer states
            is supported via ``fsdp_cpu_offload``. Resharding of parameters after the forward pass is
            done by default (corresponding to FULL_SHARD sharding strategy), but can be disabled by setting the config
            ``fsdp_reshard_after_forward`` to False (this corresponds to SHARD_GRAD_OP sharding strategy).
            DDP is currently not supported. Training on CPU is not supported.

        - Activation Checkpointing. This can be controlled using the ``activation_checkpointing``
            flag. Activation checkpointing helps reduce the memory footprint since we no longer keep
            activations in memory and instead recompute them during the backward pass. This is especially
            helpful for larger batch sizes when you're memory constrained. But these savings in memory
            come at the cost of training performance. In most cases training can slow-down quite a bit as
            a result of this activation recomputation.

        - Precision. Full fp32 and bf16 training are supported. Precision is controlled using the ``dtype``
            flag. When ``dtype=bf16``, all activations, gradients and optimizer states are in bfloat16. In
            most cases this should halve the memory footprint of full precision (fp32) training, without
            loss in model quality (will depend on the model, training data and other settings). For
            GPUs which do not support bfloat16, we fall back to fp32. Mixed precision training and fp16
            precision are currently not supported.

        - Gradient Accumulation. You can simulate larger batch sizes by accumulating gradients. This is
            controlled using the ``gradient_accumulation_steps`` flag.

                Total Batch Size = batch_size * number of GPUs * gradient accumulation steps.

            For example: with batch_size=1, nproc_per_node=2 and gradient_accumulation_steps=32 we get a
            total batch size of 64.

            Gradient accumulation is especially useful when you are memory constrained. In this case,
            accumulating gradients might give you better training speed than enabling activation
            checkpointing.

        - Checkpointing. Model weights are checkpointed both at the end of each epoch and at the end of
            training. Optimizer state and recipe state (seed, total_epochs, number of epochs run etc) are
            only saved at the end of a given epoch and used in case of resuming training.

            Resuming training is controlled by the ``resume_from_checkpoint`` flag. Mid-epoch checkpointing is
            currently not supported.

            For more details on the checkpointer, please take a look at
            our checkpointer deepdive (https://pytorch.org/torchtune/main/deep_dives/checkpointer.html).

        - Logging. Terminal, Disk, WandB and TensorBoard are all supported.

        - Gradient Clipping. Gradient clipping is supported using the ``clip_grad_norm`` flag. By default,
            ``clip_grad_norm`` is set to ``None``. If you only want to log the grad norm, you can set
            ``clip_grad_norm='inf'``.

    For a full list of example configs for this recipe, run ``tune ls`` on the command line. Each config
    has example commands for how to kick-off training.

    Args:
        cfg (DictConfig): OmegaConf object parsed from yaml file

    Raises:
        ValueError: If ``dtype`` is set to fp16.
        RuntimeError: If ``dtype`` is set to bf16 and the hardware does not support bf16.
    """

    def __init__(self, cfg: DictConfig) -> None:
        self._device = utils.get_device(device=cfg.device)
        self._dtype = training.get_dtype(cfg.dtype, device=self._device)

        if self._dtype == torch.float16:
            raise ValueError("full fp16 training is not supported with this recipe. Please use bf16 or fp32 instead.")

        # Utilize all available CPU cores for intra-op parallelism. This provides ~2x speed up when benchmarking fused AdamW on CPU.
        if cfg.fsdp_cpu_offload:
            training.set_torch_num_threads()
        if cfg.fsdp_cpu_offload and cfg.optimizer.fused and not utils.torch_version_ge("2.4.0"):
            raise RuntimeError("Using fused optimizer on CPU is only supported in PyTorch nightly.")
        # logging attributes
        self._log_every_n_steps = cfg.log_every_n_steps
        # ``_is_rank_zero`` is used primarily for logging. In the future, the logger should directly take care of this
        _, rank = training.get_world_size_and_rank()
        self._is_rank_zero = rank == 0
        # Training attributes
        self._compile = cfg.compile
        self._resume_from_checkpoint = cfg.checkpointer.resume_from_checkpoint
        self._gradient_accumulation_steps = cfg.gradient_accumulation_steps
        # These are public properties which are updated by the checkpoint loader when ``resume_from_checkpoint`` is `True` or validated in tests
        self.seed = training.set_seed(seed=cfg.seed)
        self.total_epochs = cfg.epochs
        self._clip_grad_norm = cfg.clip_grad_norm
        self.epochs_run = 0
        self.global_step = 0

    def setup(self, cfg: DictConfig) -> None:
        """
        Setup the recipe. This includes training state (if resume_from_checkpoint is True), model, tokenizer, loss, optimizer, sampler, and dataloader.
        """
        self._checkpointer, checkpoint = self._setup_checkpointer(cfg.checkpointer)
        self._tokenizer = self._setup_tokenizer(cfg.tokenizer)
        # ``_setup_model`` handles initialization and loading the state dict.
        # This method should be called before ``_setup_optimizer`` since transforming the optimizer state dict requires the model
        self._model = self._setup_model(
            cfg_model=cfg.model,
            enable_activation_checkpointing=cfg.enable_activation_checkpointing,
            custom_sharded_layers=cfg.custom_sharded_layers,
            fsdp_cpu_offload=cfg.fsdp_cpu_offload,
            reshard_after_forward=cfg.fsdp_reshard_after_forward,
            model_state_dict=checkpoint[training.MODEL_KEY],
            ac_mode=cfg.ac_mode,
            ac_option=cfg.ac_option,
        )
        # Initialize loss
        self._loss_fn = self._setup_loss(cfg.loss)
        # _setup_optimizer should take in ckpt_dict only if training is resumed from checkpoint. Transforming the opt state dict is handled by this method
        opt_state_dict = checkpoint[training.OPT_KEY] if self._resume_from_checkpoint else None
        self._optimizer = self._setup_optimizer(cfg_optimizer=cfg.optimizer, opt_state_dict=opt_state_dict)
        # Sampler and dataloader depend on the tokenizer and loss_fn and should be setup after both of these are initialized
        # ``ignore_labels_cache`` used to ignore labels for loss computation
        self._train_sampler, self._train_dataloader, self.train_ignore_labels_cache = self._setup_data(cfg.dataset.train)
        self._test_sampler, self._test_dataloader, self.test_ignore_labels_cache = self._setup_data(cfg.dataset.test)
        # Finally update the recipe state which can only be correctly set after all of the other components have been initialized and updated.
        # Number of training steps in each epoch depends on the number of batches produced by the dataloader and the gradient_accumulation_steps param.
        # This value is used for logging and tracking training state. The computation should happen after the dataloader has been setup.
        self._steps_per_epoch = len(self._train_dataloader) // self._gradient_accumulation_steps
        self.global_step = self.epochs_run * self._steps_per_epoch
        # Set up wandb with training metrics.
        self._wandb = self._setup_wandb()
        # Set up profiler, returns DummyProfiler (nullcontext object with no-op `step` method) if cfg is missing profiler key or `cfg.profiler.enabled = False`.
        self._profiler = self._setup_profiler(cfg.profiler)

    def _setup_wandb(self) -> wandb.wandb_run.Run:
        """
        Initialize wandb run and define metrics for training.
        """
        assert wandb.run is not None, "Wandb run is not initialized."
        run = wandb.run
        run.define_metric("Training/*", summary="min", step_sync=True)
        return run

    def _setup_checkpointer(self, cfg_checkpointer: DictConfig) -> tuple[training.FullModelHFCheckpointer, dict]:
        """
        Extract the checkpoint state from file and validate. If resume_from_checkpoint is True, this also includes the recipe state.
        """
        cfg_checkpoiner_dict = omegaconf.OmegaConf.to_object(cfg_checkpointer)
        assert isinstance(cfg_checkpoiner_dict, dict)
        if not self._is_rank_zero:
            # Hack. Non-zero ranks do not have a run name because they do not log to wandb. This is a temporary fix.
            cfg_checkpoiner_dict["output_dir"] = "/tmp"
        checkpointer = training.FullModelHFCheckpointer(**cfg_checkpoiner_dict)  # type: ignore
        checkpoint_dict = checkpointer.load_checkpoint()
        if self._resume_from_checkpoint:
            self._update_recipe_state(checkpoint_dict)
        return checkpointer, checkpoint_dict

    def _setup_tokenizer(self, cfg_tokenizer: DictConfig) -> llama3.Llama3Tokenizer:
        """
        Load llama3 tokenizer. Used for encoding and decoding text data.
        """
        _tokenizer = llama3.llama3_tokenizer(**omegaconf.OmegaConf.to_object(cfg_tokenizer))  # type: ignore
        _logger.info("Tokenizer is initialized.")
        return _tokenizer

    def _update_recipe_state(self, ckpt_dict: dict) -> None:
        """
        Updates the recipe state from checkpoint.
        """
        try:
            self.epochs_run = ckpt_dict[training.EPOCHS_KEY]
            # on mismatch, warn the user and prevent the override
            if self.seed != ckpt_dict[training.SEED_KEY]:
                msg = f"Config value for seed does not match the checkpoint value, using the checkpoint value: {ckpt_dict[training.SEED_KEY]}"
                warnings.warn(message=msg, stacklevel=2)
                self.seed = ckpt_dict[training.SEED_KEY]
            # on mismatch, warn the user but allow the override
            if self.total_epochs != ckpt_dict[training.TOTAL_EPOCHS_KEY]:
                msg = f"Config value for total_epochs does not match the checkpoint value, using the config value: {self.total_epochs}"
                warnings.warn(message=msg, stacklevel=2)
        except KeyError as e:
            msg = "Checkpoint does not contain the required keys needed for updating recipe state. Are you sure you passed in the right recipe checkpoint?"
            raise KeyError(msg) from e

    def _setup_model(
        self,
        cfg_model: DictConfig,
        enable_activation_checkpointing: bool,
        custom_sharded_layers: list[str] | None,
        fsdp_cpu_offload: bool,
        reshard_after_forward: bool,
        model_state_dict: dict,
        ac_mode: str | None = None,
        ac_option: int | None = None,
    ) -> torch.nn.Module:
        """
        Model initialization has some important considerations:
           a. To minimize GPU peak memory, we initialize the model on meta device with
              the right dtype
           b. All ranks calls ``load_state_dict`` without peaking CPU RAMs since
              full state dicts are loaded with ``torch.load(mmap=True)``
        """
        _logger.info("FSDP is enabled. Instantiating model and loading checkpoint on Rank 0 ...")
        init_start = time.perf_counter()
        with training.set_default_dtype(self._dtype), torch.device("meta"):
            _model = config.instantiate(cfg_model)
        if self._compile:
            training.compile_model(_model, verbose=self._is_rank_zero)
        # We currently have two versions of activation checkpointing in this recipe for testing and BC purposes.
        # ``enable_activation_checkpointing`` controls the older version of AC and this behavior is unchanged.
        # ac_mode and ac_option together control selective AC. This is only enabled when these are set AND ``enable_activation_checkpointing`` is set to False.
        # We'll clean this up as soon as testing of AC is complete
        if (not enable_activation_checkpointing) and (ac_mode is not None):
            activations.apply_selective_activation_checkpointing(_model, ac_mode, ac_option)
        # original activation checkpointing (full) - flip the condition above
        if enable_activation_checkpointing and ac_mode is None:
            training.set_activation_checkpointing(_model, auto_wrap_policy={modules.TransformerSelfAttentionLayer})
        # For FSDP sharding, we can condition on either the module or its name.
        # Shard conditions should be callables taking name (relative to model root) and the module itself and returning a bool whether to shard the module.
        # Shard transformer decoder layers (or AC-wrapped versions).
        # Alternatively we could condition on the module type (TransformerDecoder or CheckpointWrapper) but directly using the name is more concise.
        fsdp_shard_conditions = []

        def _is_layer_fqn(s: str) -> bool:
            """
            Return True for layers.i and False for all other module names
            Covers sharding for both AC-wrapped and non-AC-wrapped modules in one shot
            """
            s_list = s.split(".")
            return len(s_list) == 2 and s_list[0] == "layers" and str.isdigit(s_list[1])

        fsdp_shard_conditions = [lambda n, m: _is_layer_fqn(n)]
        # If wrapping any layers separately, we can add another shard condition. A layer will be sharded if any of the fsdp_shard_conditions are met.
        if custom_sharded_layers:
            fsdp_shard_conditions += [lambda n, m: n in custom_sharded_layers]
        training.shard_model(model=_model, shard_conditions=fsdp_shard_conditions, cpu_offload=fsdp_cpu_offload, reshard_after_forward=reshard_after_forward)
        with training.set_default_dtype(self._dtype), self._device:
            for m in _model.modules():
                # RoPE is not covered in state dict
                if hasattr(m, "rope_init"):
                    m.rope_init()
        # This method will convert the full model state dict into a sharded state dict and load into the model.
        training.load_from_full_model_state_dict(
            _model,
            model_state_dict,
            self._device,
            self._is_rank_zero,
            strict=True,
            cpu_offload=fsdp_cpu_offload,
        )
        # Ensure no params and buffers are on meta device
        training.validate_no_params_on_meta_device(_model)
        # Log memory stats after model is initialized
        _logger.info(f"Instantiating model and loading checkpoint took {time.perf_counter() - init_start:.2f} secs")
        memory_stats = training.get_memory_stats(device=self._device)
        training.log_memory_stats(memory_stats)
        # synchronize before training begins
        torch.distributed.barrier()
        return _model

    def _setup_loss(self, cfg_loss: DictConfig) -> torch.nn.Module:
        _loss_fn = config.instantiate(cfg_loss)
        if self._compile:
            training.compile_loss(_loss_fn, verbose=self._is_rank_zero)
        if hasattr(_loss_fn, "num_output_chunks"):
            self._model.set_num_output_chunks(_loss_fn.num_output_chunks)
        _logger.info("Loss is initialized.")
        return _loss_fn

    def _setup_optimizer(self, cfg_optimizer: DictConfig, opt_state_dict: dict | None = None) -> torch.optim.Optimizer:
        optimizer = torch.optim.AdamW(self._model.parameters(), **omegaconf.OmegaConf.to_object(cfg_optimizer))  # type: ignore
        if opt_state_dict:
            training.load_from_full_optimizer_state_dict(optimizer, opt_state_dict, self._device)
        _logger.info("Optimizer is initialized.")
        return optimizer

    def _setup_data(self, cfg_dataset: DictConfig) -> tuple[DistributedSampler, DataLoader, torch.Tensor]:
        """
        All data related setup happens here. Currently this recipe only supports the DistributedSamplers with Map-style Datasets which fit into memory.
        Other samplers, iterable datasets and streaming datasets are not supported.
        """
        ds = dataset.activity_config_chat_dataset(tokenizer=self._tokenizer, inference=False, **omegaconf.OmegaConf.to_object(cfg_dataset.data))  # type: ignore
        if cfg_dataset.data.packed:
            collate_fn = data.padded_collate_packed
        else:
            collate_fn = functools.partial(data.padded_collate_sft, padding_idx=self._tokenizer.pad_id, ignore_idx=self._loss_fn.ignore_index)
        world_size, rank = training.get_world_size_and_rank()
        sampler = DistributedSampler(ds, num_replicas=world_size, rank=rank, shuffle=cfg_dataset.loader.shuffle, seed=0)
        dataloader = DataLoader(
            dataset=ds,
            sampler=sampler,
            collate_fn=collate_fn,
            batch_size=cfg_dataset.loader.batch_size,
            drop_last=cfg_dataset.loader.drop_last,  # dropping last avoids shape issues with compile + flex attention
        )
        ignore_labels_cache = torch.full((cfg_dataset.loader.batch_size, 1), self._loss_fn.ignore_index, device=self._device)
        _logger.info(f"Dataset and Sampler are initialized for {cfg_dataset.data.subsets}.")
        return sampler, dataloader, ignore_labels_cache

    def _setup_profiler(self, cfg_profiler: DictConfig) -> torch.profiler.profile | training.DummyProfiler:
        """
        Parses the `profiler` section of top-level `cfg` and sets up profiler DummyProfiler is a nullcontext with no-op methods for
        `start`, `stop`, and `step` that can be used in place of `torch.profiler.profile` if profiler is not enabled
        such that the instrumented training loop does not need to be changed profiling is disabled.
        """
        profiler, cfg_profiler = training.setup_torch_profiler(**omegaconf.OmegaConf.to_object(cfg_profiler))  # type: ignore
        if self._is_rank_zero:
            self.profiler_profile_memory = cfg_profiler.get("profile_memory", False)
            if cfg_profiler.enabled:
                self.profiler_wait_steps = cfg_profiler["wait_steps"]
                self.profiler_warmup_steps = cfg_profiler["warmup_steps"]
                self.profiler_active_steps = cfg_profiler["active_steps"]
        _logger.info(f" Profiler config after instantiation: {cfg_profiler}")
        return profiler

    def _save_checkpoint(self, epoch: int) -> None:
        """
        Checkpoint the state of the recipe. The constructed checkpoint state dict
        contains the following information:
        - Model weights with key training.MODEL_KEY
        - Relevant recipe state if training is not complete

        Checkpointer will save the model weights and recipe state in
        different checkpoint files. To correctly resume training from an intermediate checkpoint,
        the model weights and recipe state must be provided.
        """
        checkpoint_dict = {}
        intermediate_checkpoint = epoch + 1 < self.total_epochs
        # To prevent GPU memory from spiking during checkpoint save, we consolidate the full model and optim state dicts on CPU for rank 0.
        cpu_state_dict = training.gather_cpu_state_dict(self._model.state_dict(), self._is_rank_zero, device=self._device)
        opt_state_dict = None
        if intermediate_checkpoint:
            opt_state_dict = training.get_full_optimizer_state_dict(self._optimizer, self._is_rank_zero, device=self._device)
        # Now that we have the model and opt state dict, create the actual checkpoint dict to be sent to the checkpointer and ultimately written to file
        if self._is_rank_zero:
            checkpoint_dict.update({training.MODEL_KEY: cpu_state_dict})
            # if training is in-progress, checkpoint the optimizer state and recipe state as well.
            if intermediate_checkpoint:
                checkpoint_dict.update(
                    {
                        training.OPT_KEY: opt_state_dict,
                        training.SEED_KEY: self.seed,
                        training.EPOCHS_KEY: self.epochs_run,
                        training.TOTAL_EPOCHS_KEY: self.total_epochs,
                    }
                )
            self._checkpointer.save_checkpoint(checkpoint_dict, epoch=epoch, intermediate_checkpoint=intermediate_checkpoint)

    def reduce_metrics(self, eval_loss: float, eval_num_tokens: int, eval_time: float) -> tuple[float, int, float]:
        # Prepare reduced tensors
        reduced_eval_num_tokens = torch.tensor(eval_num_tokens, device=self._device)
        reduced_eval_time = torch.tensor(eval_time, device=self._device)
        reduced_eval_loss = torch.tensor(eval_loss, device=self._device)
        # Reduce num_tokens
        torch.distributed.all_reduce(reduced_eval_num_tokens, op=torch.distributed.ReduceOp.SUM)
        reduced_eval_num_tokens = int(reduced_eval_num_tokens.item())
        # Reduce time as max across gpus
        torch.distributed.all_reduce(reduced_eval_time, op=torch.distributed.ReduceOp.MAX)
        reduced_eval_time = reduced_eval_time.item()
        # Reduce loss
        torch.distributed.all_reduce(reduced_eval_loss, op=torch.distributed.ReduceOp.SUM)
        reduced_eval_loss = reduced_eval_loss / reduced_eval_num_tokens
        reduced_eval_loss = reduced_eval_loss.item()

        return reduced_eval_loss, reduced_eval_num_tokens, reduced_eval_time

    def forward(self, batch: dict[str, torch.Tensor], ignore_labels_cache: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor | list[torch.Tensor]]:
        # Shape [b, s], needed for the loss not the model
        labels = batch.pop("labels")
        # Shift labels to compute loss so that tokens < n predict n.
        # Equivalent to doing labels[..., 1:] and logits[..., :-1, :], but this way we dont need to slice the logits, we just add an ignore index to labels.
        labels = torch.hstack((labels[..., 1:], ignore_labels_cache[: labels.shape[0]]))
        logits = self._model(**batch)
        if not isinstance(logits, list):
            labels = labels.reshape(-1)
            logits = logits.reshape(-1, logits.size(-1))
        # Compute loss
        loss = self._loss_fn(logits, labels)
        return loss, logits

    def train(self) -> None:
        """
        The core training loop.
        """
        # clean up before training begins
        training.cleanup_before_training()
        _, rank = training.get_world_size_and_rank()
        # zero out the gradients before starting training
        self._optimizer.zero_grad()
        # Initialize tokens count and running loss (for grad accumulation)
        step_num_tokens, step_loss, step_t0 = 0, 0, time.perf_counter()
        self._profiler.start()
        # self.epochs_run should be non-zero when we're resuming from a checkpoint
        desc = f"Training Epoch {0:03d} || Step: {0:06d} || Loss: {0:.4f}|{0:.4f}"
        pbar = tqdm.tqdm(total=self.total_epochs, dynamic_ncols=True, desc=desc, unit="Epoch", disable=not self._is_rank_zero)
        for curr_epoch in range(self.epochs_run, self.total_epochs):
            # Update the sampler to ensure data is correctly shuffled across epochs in case shuffle is True
            self._train_sampler.set_epoch(curr_epoch)
            # Make sure the model is in train mode
            self._model.train()
            train_loss, train_num_tokens, t0 = 0, 0, time.perf_counter()
            enumerated = enumerate(self._train_dataloader)
            ppbar = tqdm.tqdm(
                enumerated,
                total=len(self._train_dataloader),
                desc="Train",
                dynamic_ncols=True,
                unit="Step",
                leave=False,
                disable=not self._is_rank_zero,
            )
            for idx, batch in ppbar:
                # Start tracking CUDA memory for active steps for just the first epoch
                if self._is_rank_zero and curr_epoch == 0 and self.profiler_profile_memory and idx == self.profiler_wait_steps + self.profiler_warmup_steps:
                    torch.cuda.memory._record_memory_history()
                utils.batch_to_device(batch, self._device)
                step_num_tokens += batch["tokens"].numel()

                loss, logits = self.forward(batch, self.train_ignore_labels_cache)
                del logits
                loss = loss / self._gradient_accumulation_steps
                step_loss += loss
                loss.backward()
                # Step with optimizer
                if (idx + 1) % self._gradient_accumulation_steps == 0:
                    if self._clip_grad_norm is not None:
                        grad_norm = torch.nn.utils.clip_grad_norm_(self._model.parameters(), max_norm=float(self._clip_grad_norm))
                    self._optimizer.step()
                    self._optimizer.zero_grad(set_to_none=True)
                    # Update the number of steps when the weights are updated
                    self.global_step += 1
                    step_loss = step_loss.item()
                    # Log per-step metrics
                    if self.global_step % self._log_every_n_steps == 0 and self._is_rank_zero:
                        time_per_step = time.perf_counter() - step_t0
                        log_dict = {
                            "Training/step.loss": step_loss,
                            "Training/step.tps": step_num_tokens / time_per_step,
                            "Training/step.lr": self._optimizer.param_groups[0]["lr"],
                            "Training/step.memory": training.get_memory_stats(device=self._device),
                        }
                        if self._clip_grad_norm is not None:
                            log_dict.update({"grad_norm": grad_norm})  # type: ignore
                        self._wandb.log(log_dict)
                    # Track epoch stats
                    train_num_tokens += step_num_tokens
                    train_loss += step_loss * step_num_tokens
                    # Reset running stats for the next step
                    step_loss, step_num_tokens, step_t0 = 0, 0, time.perf_counter()
                    # Stop tracking CUDA memory now that active steps are complete
                    if (
                        self._is_rank_zero
                        and curr_epoch == 0
                        and self.profiler_profile_memory
                        and idx == self.profiler_wait_steps + self.profiler_warmup_steps + self.profiler_active_steps
                    ):
                        torch.cuda.memory._record_memory_history(enabled=None)  # type: ignore
                    # Step profiler, Note that this is called within gradient accumulation block,
                    # hence will include multiple forward / backward passes if gradient accumulation > 1
                    self._profiler.step()
            test_loss, test_num_tokens, test_time = self.eval()
            train_loss, train_num_tokens, train_time = self.reduce_metrics(train_loss, train_num_tokens, time.perf_counter() - t0)
            test_loss, test_num_tokens, test_time = self.reduce_metrics(test_loss, test_num_tokens, test_time)

            if self._is_rank_zero:
                assert pbar is not None
                log_dict = {
                    "step": self.global_step,
                    "Training/train.epoch.loss": train_loss,
                    "Training/train.epoch.tps": train_num_tokens / train_time,
                    "Training/test.epoch.loss": test_loss,
                    "Training/test.epoch.tps": test_num_tokens / test_time,
                }
                self._wandb.log(log_dict)
                pbar.set_description(f"Training: Epoch {self.epochs_run:03d} || Step: {self.global_step:06d} || Loss: {train_loss:.4f}|{test_loss:.4f}")
                pbar.update(1)
            self.epochs_run += 1
            self._save_checkpoint(epoch=curr_epoch)
        self._profiler.stop()

    @torch.inference_mode()
    def eval(self) -> tuple[float, int, float]:
        """The core evaluation loop."""
        self._model.eval()
        eval_loss, eval_num_tokens, t0 = 0, 0, time.perf_counter()
        enumerated = enumerate(self._test_dataloader)
        ppbar = tqdm.tqdm(
            enumerated,
            desc="Eval",
            total=len(self._test_dataloader),
            dynamic_ncols=True,
            unit="Step",
            leave=False,
            disable=not self._is_rank_zero,
        )
        for _idx, batch in ppbar:
            utils.batch_to_device(batch, self._device)
            step_num_tokens = batch["tokens"].numel()
            step_loss, _ = self.forward(batch, self.test_ignore_labels_cache)
            eval_num_tokens += step_num_tokens
            eval_loss += step_loss.item() * step_num_tokens
        eval_time = time.perf_counter() - t0
        return eval_loss, eval_num_tokens, eval_time

    def cleanup(self) -> None:
        torch.distributed.destroy_process_group()


def init(rank: int) -> omegaconf.DictConfig:
    typedefs.load()
    cfg = common.init_config_from_base()
    common.init_debugger(cfg.debugger, rank)
    common.init_logging(cfg.logging, rank)
    common.init_run(cfg, rank)
    common.log_config(cfg, rank)
    # set_readonly makes sure that the config is not modified after init.
    omegaconf.OmegaConf.set_readonly(cfg, True)
    return cfg


def main() -> None:
    """Usage: tune run --nnodes 1 --nproc_per_node 2 finetune_full_training --config ./configs/finetune_full.debug.yaml"""
    if not training.is_distributed():
        msg = "Distributed finetune recipe should be run via a distributed launcher. If using tune CLI, please specify --nnodes 1 and --nproc_per_node [ngpus]"
        raise RuntimeError(msg)
    torch.distributed.init_process_group(backend="nccl")
    rank = training.get_world_size_and_rank()[1]
    try:
        cfg = init(rank)
        recipe = FinetuneFullRecipe(cfg.training)
        recipe.setup(cfg.training)
        recipe.train()
        recipe.cleanup()
    except Exception as e:
        torch.distributed.destroy_process_group()
        raise e


if __name__ == "__main__":
    main()
