import argparse
import logging as lg
import os
import pathlib
import shutil

import debugpy
import omegaconf
import torchtune.config._parse
import torchtune.utils
import wandb
import wandb.util
import wandb.wandb_run

from services.studio._text_to_workflow.utils import paths

_logger = torchtune.utils.get_logger("DEBUG")


def init_config_from_base() -> omegaconf.DictConfig:
    ap = argparse.ArgumentParser(
        "Read config from yaml and override with cli using OmegaConf.",
        usage="%(prog)s --config <config-path> [dot.path.to.key.in.config=value...]",
        description="Read a config file from yaml and optionally override values from the cli.",
    )
    ap.add_argument("--config", type=str, required=True, help="Path to the config file.")
    ap.add_argument("overrides", nargs="*", help="Overrides for the config file.")
    args = ap.parse_args()
    yaml = omegaconf.OmegaConf.load(args.config)
    cli = omegaconf.OmegaConf.from_dotlist(args.overrides)
    # set_struct makes sure that cli overrides are valid.
    omegaconf.OmegaConf.set_struct(yaml, True)
    merged = omegaconf.OmegaConf.merge(yaml, cli)
    assert isinstance(merged, omegaconf.DictConfig), "Config must be a DictConfig."
    return merged


def init_config_from_run(run_config_path: pathlib.Path) -> omegaconf.DictConfig:
    cfg = omegaconf.OmegaConf.load(run_config_path)
    omegaconf.OmegaConf.set_struct(cfg, True)
    assert isinstance(cfg, omegaconf.DictConfig), "Config must be a DictConfig."
    return cfg


def init_run(cfg: omegaconf.DictConfig, rank: int) -> None:
    mode = "online" if rank == 0 else "disabled"
    wandb.init(id=cfg.run_id, dir=paths.get_logs_path(), mode=mode, resume="never", **cfg.wandb)
    assert wandb.run, "wandb.init failed."
    cfg.run_id = wandb.run.id
    cfg.name = wandb.run.name
    if rank == 0:
        if os.path.exists(cfg.finetuned_model_path) and cfg.overwrite:
            shutil.rmtree(cfg.finetuned_model_path)
        shutil.copytree(cfg.base_model_path, cfg.finetuned_model_path, ignore=shutil.ignore_patterns("*.safetensors"))


def resume_run(cfg: omegaconf.DictConfig, rank: int) -> None:
    assert cfg.run_id is not None, "run_id must be provided to resume a run."
    mode = "online" if rank == 0 else "disabled"
    wandb.init(id=cfg.run_id, dir=paths.get_logs_path(), mode=mode, resume="must", **cfg.wandb)
    assert wandb.run, "wandb.init failed."
    cfg.run_id = wandb.run.id
    cfg.name = wandb.run.name


def init_debugger(enable: bool, rank: int, debug_port: int = 5678) -> None:
    if not enable:
        return
    if rank == 0:
        # Only the process with rank 0 waits for the debugger to attach
        print(f"Rank {rank}: Waiting for debugger attach on port {debug_port}...")
        debugpy.listen(("0.0.0.0", debug_port))
        debugpy.wait_for_client()
        print(f"Rank {rank}: Debugger is attached.")
    else:
        # Other ranks also listen for debugger but do not wait
        debugpy_port = debug_port + rank
        print(f"Rank {rank}: Listening for debugger attach on port {debugpy_port}...")
        debugpy.listen(("0.0.0.0", debugpy_port))


def init_logging(enable: bool, rank: int) -> None:
    """Remove all handlers from torchtune and root loggers and prevent adding any new ones."""
    if not enable or rank != 0:
        torchtune_logger = torchtune.utils.get_logger()
        torchtune_logger.propagate = False
        for handler in torchtune_logger.handlers:
            torchtune_logger.removeHandler(handler)
        torchtune_logger.addHandler(lg.NullHandler())


def log_config(cfg: omegaconf.DictConfig, rank: int) -> None:
    if rank == 0:
        assert wandb.run is not None, "wandb.run must be initialized to log config to wandb."
        pathlib.Path(cfg.finetuned_model_path).mkdir(parents=True, exist_ok=True)
        omegaconf.OmegaConf.save(cfg, f"{cfg.finetuned_model_path}/config.yaml", resolve=False)
        omegaconf.OmegaConf.save(cfg, f"{cfg.finetuned_model_path}/config.resolved.yaml", resolve=True)
        wandb.run.config.update(omegaconf.OmegaConf.to_container(cfg, resolve=True), allow_val_change=True)
        _logger.info(f"Run config:\n{omegaconf.OmegaConf.to_yaml(cfg, resolve=True)}")
