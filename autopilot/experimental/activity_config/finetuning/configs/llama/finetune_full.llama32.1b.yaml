# This config assumes that you've run the following command before launching
# this run:
#   tune download meta-llama/Llama-3.2-1B-Instruct --output-dir /tmp/Llama-3.2-1B-Instruct --ignore-patterns "original/consolidated.00.pth"

run_id: null # The run ID is set during finetuning from wandb.
name: llama32.1b  # NOTE:  Specify a name in cli argument `name=` if you want to override.
group: finetune-full  # Modify the group from the cli argument `group=`
job: finetuning
debugger: False # Set this to True and attach with debugpy
logging: False # Set this to False to disable all torchtune logging
overwrite: False # NOTE: Set it to True to overwrite the existing run
# These are paths set based on workdir_path. Modify them if needed
workdir_path: ${oc.env:STUDIO_TEXT_TO_WORKFLOW_WORKDIR}
base_model_path: ${workdir_path}/Models/.llama/Meta-Llama-3.2-1B-Instruct
finetuned_model_path: ${workdir_path}/Runs/ActivityConfig/${group}/${job}/${name}
seed: 42
device: cuda
dtype: bf16
model:
  _component_: torchtune.models.llama3_2.llama3_2_1b
wandb:
  project: <EMAIL>
  entity: uipath
  name: ${name}
  group: ${group}
  job_type: ${job}
training:
  device: ${device}
  dtype: ${dtype}
  seed: ${seed}
  model: ${model}
  checkpointer:
    checkpoint_dir: ${base_model_path}
    checkpoint_files:
      - model.safetensors
    output_dir: ${finetuned_model_path}
    model_type: LLAMA3_2
    safe_serialization: True
    recipe_checkpoint: null
    resume_from_checkpoint: False
  tokenizer:
    path: ${base_model_path}/original/tokenizer.model
    max_seq_len: 16384
  epochs: 10
  gradient_accumulation_steps: 4
  clip_grad_norm: null
  enable_activation_checkpointing: False
  compile: True
  custom_sharded_layers: null
  fsdp_reshard_after_forward: True
  fsdp_cpu_offload: null
  ac_mode: null
  ac_option: null
  log_every_n_steps: 1
  dataset:
    train:
      data:
        subsets:
          Portable:
            - train
          Windows:
            - train
        max_seq_len: ${training.tokenizer.max_seq_len}
        train_on_input: False
        packed: True
        limit: null
        repeat: null
      loader:
        batch_size: 2
        shuffle: True
        drop_last: True
    test:
      data:
        subsets:
          Portable:
            - test
          Windows:
            - test
        max_seq_len: ${training.tokenizer.max_seq_len}
        train_on_input: False
        packed: False
        limit: null
        repeat: null
      loader:
        batch_size: 8
        shuffle: False
        drop_last: False
  loss:
    _component_: torchtune.modules.loss.CEWithChunkedOutputLoss
  optimizer:
    lr: 2e-5
    fused: True
  profiler:
    enabled: False
inference:
  device: ${device}
  dtype: ${dtype}
  seed: ${seed}
  model: ${model}
  checkpointer:
    checkpoint_dir: ${finetuned_model_path}
    checkpoint_files:
      - model-00001-of-00001_0.safetensors
    output_dir: ${finetuned_model_path}
    model_type: LLAMA3_2
  tokenizer:
    path: ${finetuned_model_path}/original/tokenizer.model
    max_seq_len: 16384
  max_new_tokens: 250
  temperature: 0.0
  top_k: 1
  compile: True
  kv_cache_batch_size: 1
evaluation:
  device: ${device}
  dtype: ${dtype}
  seed: ${seed}
  model: ${model}
  inference: ${inference}
  dataset:
    data:
      subsets:
        Portable:
          - test
        Windows:
          - test
      max_seq_len: ${inference.tokenizer.max_seq_len}
      train_on_input: False
      packed: False
      limit: null
      repeat: null
    loader:
      batch_size: 8
      shuffle: False
      drop_last: False
  persist: true # Set this to true to modify the run checkpoints and keep only best and last.
