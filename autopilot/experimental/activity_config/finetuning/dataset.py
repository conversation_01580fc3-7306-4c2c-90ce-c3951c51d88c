import numpy as np
from torchtune import data, datasets
from torchtune.models import llama3

from services.studio._text_to_workflow.activity_config import activity_config_dataset as acd
from services.studio._text_to_workflow.activity_config import activity_config_messages as acm
from services.studio._text_to_workflow.activity_config import activity_config_schema as acs
from services.studio._text_to_workflow.common import schema as cs


class ActivityConfigChatDataset(datasets.SFTDataset):
    def __init__(
        self,
        subsets: dict[cs.TargetFramework, list[cs.SubsetName]],
        tokenizer: llama3.Llama3Tokenizer,
        max_seq_len: int,
        train_on_input: bool = False,
        limit: int | None = None,
        repeat: int | None = None,
        inference: bool = False,
    ) -> None:
        self.subsets = subsets
        self.max_seq_len = max_seq_len
        self.tokenizer = tokenizer
        self.train_on_input = train_on_input
        self.limit = limit
        self.repeat = repeat
        self.inference = inference
        self.paths = []
        for framework, framework_subsets in self.subsets.items():
            for subset in framework_subsets:
                self.paths += acd.ls(framework, subset, sort=True, limit=limit)

    def __len__(self) -> int:
        length = len(self.paths)
        if self.repeat is not None:
            length *= self.repeat
        return length

    def __getitem__(self, index: int) -> dict:
        if self.repeat is not None:
            index %= len(self.paths)
        sample_path = self.paths[index]
        sample = acd.load_activity_config_example(sample_path)
        interaction = acm.create_activity_config_interaction(sample, None)
        context, configuration = interaction["context"], interaction["configuration"]
        messages = self._convert_to_messages(context, configuration)
        tokens, labels = self._prepare_messages(messages)
        return {"tokens": tokens, "labels": labels, "index": index}

    def get_item_interaction(self, index: int) -> acs.ActivityConfigInteraction:
        if self.repeat is not None:
            index %= len(self.paths)
        sample_path = self.paths[index]
        sample = acd.load_activity_config_example(sample_path)
        return acm.create_activity_config_interaction(sample, None)

    def _convert_to_messages(self, context: acs.ActivityConfigContext, configuration: acs.ActivityConfigResult) -> list[data.Message]:
        masked = not self.train_on_input  # mask system and user if not training on input. always train on assistant
        system = acm.build_system_message({context["activity_id"]: context["activity_typedef"]}, context["additional_typedefs"])
        user = acm.build_user_message(context, include_user_instructions=False)
        if self.inference:
            assistant = cs.Message(role="assistant", content="")
        else:
            assistant = acm.build_assistant_message(configuration)
        return [data.Message(**system, masked=masked), data.Message(**user, masked=masked), data.Message(**assistant, masked=False)]

    def _prepare_messages(self, messages: list[data.Message]) -> tuple[list[int], list[int]]:
        data.validate_messages(messages)
        sample = self.tokenizer({"messages": messages}, inference=self.inference)
        tokens, mask = sample["tokens"], sample["mask"]
        labels = list(np.where(mask, data.CROSS_ENTROPY_IGNORE_IDX, tokens))  # Wherever mask == True, set to CROSS_ENTROPY_IGNORE_IDX. Otherwise keep as tokens
        assert len(tokens) == len(labels)
        return tokens, labels


def activity_config_chat_dataset(
    subsets: dict[cs.TargetFramework, list[cs.SubsetName]],
    tokenizer: llama3.Llama3Tokenizer,
    max_seq_len: int,
    packed: bool,
    train_on_input: bool = False,
    limit: int | None = None,
    repeat: int | None = None,
    inference: bool = False,
):
    ds = ActivityConfigChatDataset(subsets, tokenizer, max_seq_len, train_on_input, limit, repeat, inference)
    if packed:
        if tokenizer.max_seq_len is None:
            raise ValueError("PackedDataset requires a max_seq_len to be set on the tokenizer.")
        ds = datasets.PackedDataset(ds, max_seq_len=max_seq_len, padding_idx=tokenizer.pad_id)  # type: ignore
    return ds
