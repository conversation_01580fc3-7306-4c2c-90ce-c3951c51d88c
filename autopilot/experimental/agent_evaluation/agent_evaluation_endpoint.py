from services.studio._text_to_workflow.agent_evaluation.agent_evaluation_schema import AgentEvaluationRequest
from services.studio._text_to_workflow.agent_evaluation.agent_evaluation_task import AgentEvaluationTask

_synthetic_agent_dataset_task = AgentEvaluationTask()


async def generate(request: AgentEvaluationRequest):
    result = await _synthetic_agent_dataset_task.run(request)

    return {"entries": result["entries"]}
