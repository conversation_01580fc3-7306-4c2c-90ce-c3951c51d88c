import json
import pathlib
from typing import Any, Dict, List, Optional

import fastjsonschema
import langchain_community.callbacks
from langchain.prompts.chat import (
    AIMessagePromptTemplate,
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain.schema.messages import BaseMessage

from services.studio._text_to_workflow.agent_evaluation.agent_evaluation_retriever import (
    AgentEvaluationRetriever,
)
from services.studio._text_to_workflow.agent_evaluation.agent_evaluation_schema import (
    AgentEvaluationRequest,
    AgentEvaluationResult,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = AppInsightsLogger()


def get_config_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent / "prompt.yaml"


class AgentEvaluationTask:
    def __init__(self):
        self.load_config()
        self._demos: Optional[List[Dict[str, Any]]] = None
        self.retriever = AgentEvaluationRetriever()
        self.embed_model = ModelManager().get_embeddings_model()

    def load_config(self) -> None:
        self.config = yaml_load(get_config_path())

    def load_demos(self) -> List[Dict[str, Any]]:
        """Lazily load demos when first needed"""
        if self._demos is None:
            self._demos = []
            demo_paths = paths.get_agent_evaluation_dataset_path().glob("*.json")
            for file_path in demo_paths:
                if file_path.is_file():
                    with open(file_path, "r") as f:
                        self._demos.append(json.load(f))
        return self._demos

    @property
    def demos(self) -> List[Dict[str, Any]]:
        """Property to access demos, loading them if not already loaded"""
        return self.load_demos()

    async def run(self, request: AgentEvaluationRequest) -> AgentEvaluationResult:
        llm_model = ModelManager().get_llm_model("agent_evaluation_model", ConsumingFeatureType.AGENT_EVALUATION)

        json_schema = self.generate_json_schema(request)

        structured_llm = llm_model.with_structured_output(schema=json_schema, method="json_mode")

        system_msg = SystemMessagePromptTemplate.from_template(self.config["synthetic_agent_dataset_generation"]["system_template_instructions"]).format(
            jsonSchema=json.dumps(json_schema),
            models=str(request["agent"]["availableModels"]),
        )
        messages: list[BaseMessage] = [system_msg]
        user_message_template = HumanMessagePromptTemplate.from_template(self.config["synthetic_agent_dataset_generation"]["user_template"])
        assistant_message_template = AIMessagePromptTemplate.from_template(self.config["synthetic_agent_dataset_generation"]["assistant_template"])

        for demo in self.get_demos(request):
            user_msg = user_message_template.format(
                systemPrompt=demo["agent"]["systemPrompt"],
                userPrompt=demo["agent"]["userPrompt"],
                inputSchema=demo["agent"]["inputSchema"],
                outputSchema=demo["agent"]["outputSchema"],
                availableModels=demo["agent"]["availableModels"],
                tools=demo["agent"]["tools"],
                datasets=[],
            )
            assistant_msg = assistant_message_template.format(result=demo["eval"])
            messages.extend([user_msg, assistant_msg])

        datasets = []
        for dataset in request["agent"]["datasets"][:3]:
            datasets.append(f"name: {dataset['name']}, entries: {dataset['entries'][:5]}")

        user_msg = user_message_template.format(
            systemPrompt=request["agent"]["systemPrompt"],
            userPrompt=request["agent"]["userPrompt"],
            inputSchema=request["agent"]["inputSchema"],
            outputSchema=request["agent"]["outputSchema"],
            availableModels=request["agent"]["availableModels"],
            tools=request["agent"]["tools"],
            datasets=datasets,
        )

        messages.append(user_msg)

        template = ChatPromptTemplate.from_messages(messages)

        chat_chain = template | structured_llm

        with langchain_community.callbacks.get_openai_callback() as cb:
            result_data = await chat_chain.ainvoke({})
            # Convert to dict if it's a BaseModel
            result = result_data.dict() if hasattr(result_data, "dict") else dict(result_data)

            # Get model name safely
            model_name = getattr(llm_model, "deployment_name", None)
            if model_name is None:
                model_name = getattr(llm_model, "model_name", "unknown_model")

            usage = TokenUsage(
                model=model_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
            LOGGER.info(f"Agent evaluation usage: {usage}")

        try:
            result = self.validate_and_format_result(result, json_schema, request)
        except fastjsonschema.JsonSchemaException as e:
            LOGGER.exception(f"Data failed validation: {e}")
            result = {"entries": []}

        # Ensure the result conforms to AgentEvaluationResult
        return {"entries": result.get("entries", [])}

    def validate_and_format_result(self, result: Dict[str, Any], json_schema: Dict[str, Any], request: AgentEvaluationRequest) -> Dict[str, Any]:
        try:
            # Compile the schema validator
            eval_validator = fastjsonschema.compile(json_schema)
            # Validate the result against the schema
            eval_validator(result)
        except fastjsonschema.JsonSchemaValueException as e:
            LOGGER.exception(f"Data failed validation: {e}")
            if "entries" in result:
                # Filter out invalid entries
                valid_entries = []
                for entry in result["entries"]:
                    if self.validate_and_format_result_entry(entry, request["agent"]["outputSchema"]["schema"]):
                        valid_entries.append(entry)
                result["entries"] = valid_entries
            else:
                result = {"entries": []}
        except fastjsonschema.JsonSchemaException as e:
            LOGGER.exception(f"Data failed validation: {e}")
            result = {"entries": []}
        return result

    def validate_and_format_result_entry(self, entry: Dict[str, Any], outputSchema: Dict[str, Any]) -> bool:
        if "assertion_properties" in entry:
            if "value" in entry["assertion_properties"]:
                try:
                    if entry["outputKey"] not in outputSchema["properties"].keys():
                        return False

                    # Create a validator for the output schema
                    value_validator = fastjsonschema.compile(outputSchema)

                    # Create a test object with just the output key
                    test_obj = {entry["outputKey"]: entry["assertion_properties"]["value"]}

                    # Validate the test object
                    try:
                        value_validator(test_obj)
                        # Extract just the value for the output key
                        if isinstance(entry["assertion_properties"]["value"], dict) and entry["outputKey"] in entry["assertion_properties"]["value"]:
                            entry["assertion_properties"]["value"] = entry["assertion_properties"]["value"].get(entry["outputKey"])
                        return True
                    except fastjsonschema.JsonSchemaException:
                        return False
                except Exception as e:
                    LOGGER.exception(f"Error validating entry: {e}")
                    return False
        return False

    def get_demos(self, request) -> list:
        all_prompts = [request["agent"]["systemPrompt"]] + [demo["agent"]["systemPrompt"] for demo in self.demos]
        request_embedding, *demos_embedding = self.embed_model.encode(all_prompts)
        indices = self.retriever.get_relevant_demos_indices(request_embedding, demos_embedding, k=1)

        return [self.demos[i] for i in indices]

    def generate_json_schema(self, request: AgentEvaluationRequest) -> Dict[str, Any]:
        output_property_types = set()
        output_properties = request["agent"]["outputSchema"]["schema"]["properties"]
        for property in output_properties:
            output_property_types.add(output_properties[property]["type"])

        json_schema = {
            "title": request["agent"]["description"],
            "description": request["agent"]["description"],
            "type": "object",
            "properties": {
                "entries": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "outputKey": {"type": "string"},
                                "inputs": request["agent"]["inputSchema"]["schema"],
                                "assertion_type": {"type": "string"},
                                "assertion_properties": {
                                    "type": "object",
                                    "properties": {
                                        "value": {"type": list(output_property_types)},
                                        "negate": {"type": "boolean"},
                                        "model": {"type": "string"},
                                    },
                                    "required": ["value", "negate"],
                                },
                            },
                            "required": [
                                "name",
                                "outputKey",
                                "inputs",
                                "assertion_type",
                                "assertion_properties",
                            ],
                        }
                    ],
                }
            },
            "required": ["entries"],
        }
        return json_schema
