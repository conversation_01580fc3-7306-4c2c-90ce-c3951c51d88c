import typing_extensions as t

from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse


class Tools(t.TypedDict):
    name: str
    description: str


class Entry(t.TypedDict):
    inputs: dict
    outputs: dict


class Dataset(t.TypedDict):
    name: str
    entries: list[Entry]


class Agent(t.TypedDict):
    name: str
    description: str
    systemPrompt: str
    userPrompt: str
    inputSchema: dict
    outputSchema: dict
    availableModels: list[str] | None
    tools: list[Tools] | None
    datasets: list[Dataset]


class AgentEvaluationRequest(BaseRequest):
    agent: Agent


class AgentEvaluationResult(t.TypedDict):
    entries: str


class AgentEvaluationResponse(BaseResponse):
    entries: list
