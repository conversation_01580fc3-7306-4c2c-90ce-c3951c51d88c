import math
import pathlib
import typing as t
from typing import Dict, get_args

import langchain.prompts as prompts
import openai
import torch
from langchain_core.messages import BaseMessage

from experimental.prompt_scorer.network.train import BertPromptFilter, load_model
from services.studio._text_to_workflow.utils.inference import llm_gateway_model
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

QueryFilterType = t.Literal["workflow", "expression"]
ResponseType = t.Literal["GOOD", "BAD", "JUNK", "OTHER", "CONTENTFILTERED"]


class PromptScorerResponse:
    def __init__(self, value: ResponseType, scores: Dict[str, float]):
        self.value = value
        self.scores = scores


class PromptScorerModel:
    def __init__(self, config_name: str, model_path: str | None = None):
        config_dir = (pathlib.Path(__file__).parent).absolute()
        config_path = config_dir / config_name
        self.config = yaml_load(config_path)
        self.predict_scores = True
        if self.config["model_type"] == "sentence_bert":
            network, tokenizer = load_model(model_path)
            device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
            self.model = BertPromptFilter(network, tokenizer, device)

    def _get_prompt(self, query: str, type: QueryFilterType) -> t.List[BaseMessage]:
        messages = []
        if type == "workflow":
            system_prompt_template = self.config["prompt"]["workflow_system_template"]
            system_prompt = prompts.SystemMessagePromptTemplate.from_template(system_prompt_template)
            messages.append(system_prompt)

            message_types = ["bad", "good", "junk"]

            for message_type in message_types:
                user_messages_templates = self.config["prompt"]["workflow_query_reponse_examples"][message_type]
                ai_response_template = message_type.upper()
                for user_message_template in user_messages_templates:
                    user_message = prompts.HumanMessagePromptTemplate.from_template(template=user_message_template)
                    ai_response = prompts.AIMessagePromptTemplate.from_template(ai_response_template)
                    messages.append(user_message)
                    messages.append(ai_response)
            query = query.replace("{", "{{")
            query = query.replace("}", "}}")

            messages.append(prompts.HumanMessagePromptTemplate.from_template(query))
            return messages
        else:
            raise NotImplementedError("Task not supported")

    def predict(
        self,
        query: str,
        type: QueryFilterType,
    ) -> PromptScorerResponse:
        model_config = self.config["model_config"]
        model_type = self.config["model_type"]

        response: PromptScorerResponse = PromptScorerResponse("OTHER", {})
        outcomes = set(get_args(ResponseType))

        if model_type == "openai":
            messages = self._get_prompt(query, type)
            chat_prompt = prompts.ChatPromptTemplate.from_messages(messages)
            if self.predict_scores:
                model_config["logprobs"] = True
                model_config["top_logprobs"] = 5
            value: ResponseType = "OTHER"

            model = llm_gateway_model.LLMGatewayModel(feature_type=ConsumingFeatureType.DEFAULT, **model_config)

            try:
                chat_chain = chat_prompt | model
                out = chat_chain.invoke({"query": query})
            except openai.BadRequestError:
                return PromptScorerResponse("CONTENTFILTERED", {"CONTENTFILTERED": 1})

            prediction = out.content.strip().upper()
            if prediction in outcomes:
                value = prediction

            scores = {}
            if self.predict_scores:
                for item in outcomes:
                    scores[item] = 0
                top_logprobs = out.response_metadata["logprobs"]["content"][0]["top_logprobs"]
                for item in top_logprobs:
                    token = item["token"].strip().upper()
                    if token in outcomes:
                        scores[token] = scores[token] + math.exp(item["logprob"])  # accumulate probabilities
                    else:
                        for outcome in outcomes:
                            if outcome.startswith(token):
                                scores[outcome] = scores[outcome] + math.exp(item["logprob"])  # accumulate probabilities
                                break

                total_prob = sum(scores.values())
                scores["OTHER"] = 1 - total_prob
            response = PromptScorerResponse(value, scores)
        else:
            value: ResponseType = "OTHER"
            pred_value, probs = self.model.predict(query, query_type=type)
            label = self.model.id_2_classes[pred_value]

            if label in outcomes:
                value = label

            scores = {}
            for item in outcomes:
                scores[item] = 0
                if item in self.model.classes_2_id:
                    scores[item] = float(probs[self.model.classes_2_id[item]])

            response = PromptScorerResponse(value, scores)

        return response


if __name__ == "__main__":
    model = PromptScorerModel("prompt.yaml", r"C:\work\repos\ml")
    messages = model.predict("Create a workflow to read the data from a excel file and upload it into orchestrator Queue", type="workflow")
