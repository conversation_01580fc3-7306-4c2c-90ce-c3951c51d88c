from typing import Dict, <PERSON><PERSON>

import matplotlib.pyplot as plt
import numpy as np


def compute_pr_curve(predictions: np.ndarray, targets: np.ndar<PERSON>, class_name: str, classes_dict: Dict[str, int]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    class_id = classes_dict[class_name]
    class_predictions_scores = predictions[:, class_id]
    indices = np.argsort(-class_predictions_scores)
    predictions = predictions[indices]
    thresholds = class_predictions_scores[indices]
    targets = targets[indices]

    is_class_prediction = targets == class_id

    is_tp = np.zeros_like(is_class_prediction, dtype=np.int64)
    is_fp = np.zeros_like(is_class_prediction, dtype=np.int64)

    is_tp[is_class_prediction] = 1
    is_fp[~is_class_prediction] = 1

    npos = max(1, np.sum(is_class_prediction))

    tps = np.cumsum(is_tp)
    fps = np.cumsum(is_fp)

    precision = tps / (tps + fps)
    recall = tps / npos

    return precision, recall, thresholds


def draw_pr_curve(precision, recall, thresholds, class_name, safe_file: str | None = None, interactive: bool = False):
    precision_points: list = [0.9, 0.95, 0.99]

    fig, subplot = plt.subplots()
    subplot.set_frame_on(True)
    plt.setp(tuple(subplot.spines.values()), color="0.7")
    subplot.set_xlabel("Recall", fontsize="xx-small", labelpad=-1)
    subplot.set_ylabel("Precision", fontsize="xx-small", labelpad=-1)
    subplot.tick_params(direction="out", color="0.4", labelsize="xx-small", labelcolor="0.3")
    subplot.grid(True, which="minor", linewidth=1, color="0.9", aa=True)
    subplot.set_axisbelow(True)
    subplot.set_picker(True)

    subplot.plot(recall, precision, color="0.5", linewidth=1, aa=True)
    subplot.fill_between(recall, precision, color="0.85", alpha=0.5)

    for point in precision_points:
        index = np.argmax(precision <= point)
        x = recall[index]
        y = precision[index]
        threshold = thresholds[index]
        label = "{0:.6f}".format(threshold)
        subplot.plot(x, y, color="0.4", marker="o", markersize=4, aa=True)
        subplot.annotate(label, (x, y), textcoords="offset pixels", xytext=(1, 1), fontsize="xx-small")

    # set axis ticks
    subplot.set_xticks([0, 1])
    subplot.set_yticks([0, 1])
    subplot.set_xticks(np.arange(0.1, 1, 0.1), minor=True)
    subplot.set_yticks(np.arange(0.1, 1, 0.1), minor=True)

    subplot.set_title(f"Precision recall {class_name}")
    if safe_file is not None:
        fig.savefig(safe_file)
    if interactive:
        plt.waitforbuttonpress()
