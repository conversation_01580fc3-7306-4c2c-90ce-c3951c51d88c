import csv
import os
import random
import re
import time
from typing import Dict, List, Literal, Tu<PERSON>, get_args

import numpy as np
import torch
import torch.utils.data as tud
import yaml
from torch import Tensor, nn
from torch.optim import lr_scheduler
from transformers import <PERSON><PERSON><PERSON><PERSON>, BertTokenizer

from experimental.prompt_scorer.network.utils import compute_pr_curve, draw_pr_curve
from services.studio._text_to_workflow.utils.paths import get_models_path
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import get_steps_plan

CLASSES_2_ID = {"GOOD": 0, "BAD": 1, "JUNK": 2}
QUERY_TYPE = Literal["workflow", "expression"]
BERT_MODEL_NAME = "bert-base-multilingual-uncased"
DEFAULT_MODEL_PATH = os.path.join(get_models_path(), "junk-filter")


class BertClassifierModel(nn.Module):
    def __init__(self, number_of_classes: int):
        super(BertClassifierModel, self).__init__()
        model = BertModel.from_pretrained(BERT_MODEL_NAME, output_hidden_states=True)

        self.projection = nn.Linear(768, number_of_classes)
        self.model = model

    def forward(self, encoded_input: Dict[str, Tensor]) -> Tensor:
        attention_mask = encoded_input["attention_mask"]
        token_type_ids = encoded_input["token_type_ids"]
        input_ids = encoded_input["input_ids"]

        output = self.model(input_ids, attention_mask, token_type_ids)
        hidden_states = output[2]
        out = self.projection(hidden_states[-1][:, 0, :])
        return out


def load_model(model_path: str | None = None) -> Tuple[BertClassifierModel, BertTokenizer]:
    if model_path is None:
        model_path = DEFAULT_MODEL_PATH
    model = BertClassifierModel(len(CLASSES_2_ID))
    model_file_path = os.path.join(model_path, "model.b")
    model.load_state_dict(torch.load(model_file_path, map_location=torch.device("cpu")))
    tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_NAME)
    return (model, tokenizer)


class BertPromptFilter(object):
    def __init__(self, model: BertClassifierModel, tokenizer, device: torch.device):
        self.model = model.to(device=device)
        self.tokenizer = tokenizer
        self.classes_2_id = CLASSES_2_ID
        self.id_2_classes = {value: key for key, value in self.classes_2_id.items()}
        self.device = device

    def predict(self, query: str, query_type: str | None = None) -> Tuple[int, Tensor]:
        if query_type is not None:
            query = f"{query_type}:{query}"
        encoded_input = self.tokenizer(query)
        encoded_input["attention_mask"] = torch.tensor(encoded_input["attention_mask"]).to(device=self.device).unsqueeze(0)
        encoded_input["token_type_ids"] = torch.tensor(encoded_input["token_type_ids"]).to(device=self.device).unsqueeze(0)
        encoded_input["input_ids"] = torch.tensor(encoded_input["input_ids"]).to(device=self.device).unsqueeze(0)

        out = self.model(encoded_input)
        probs = torch.nn.functional.softmax(out).squeeze().detach().cpu()
        class_id = int(torch.argmax(probs).item())
        return class_id, probs


class ClassificationDataset(tud.Dataset):
    def __init__(self, datapoints: List[Dict]):
        self.datapoints = datapoints
        self.is_train = False

    def __len__(self) -> int:
        """The length of the dataset.

        :return: integer length of the dataset.
        """
        return self.len()

    def len(self):
        return len(self.datapoints)

    def __getitem__(self, idx: int) -> Tuple[str, Tensor, str, Tensor]:
        item = self.datapoints[idx]
        query = item["query"]
        label = torch.tensor(item["label"])
        type = item["type"]
        supported_types = list(get_args(QUERY_TYPE))
        prefix = type
        if type not in supported_types:
            prefix = random.choice(supported_types)

        data_query = f"{prefix}:{query}"

        id = torch.tensor(idx)

        return (data_query, label, type, id)


class DataCollator(object):
    def __init__(self, tokenizer, **params):
        self.tokenizer = tokenizer

    def __call__(self, batch, *args, **kwargs):
        encoded_input = self.tokenizer([x[0] for x in batch])
        encoded_input["input_ids"] = torch.nn.utils.rnn.pad_sequence(
            [torch.tensor(x[:512]) for x in encoded_input["input_ids"]], batch_first=True, padding_value=self.tokenizer.pad_token_id
        )
        encoded_input["token_type_ids"] = torch.nn.utils.rnn.pad_sequence(
            [torch.tensor(x[:512]) for x in encoded_input["token_type_ids"]], batch_first=True, padding_value=self.tokenizer.pad_token_type_id
        )
        encoded_input["attention_mask"] = torch.nn.utils.rnn.pad_sequence(
            [torch.tensor(x[:512]) for x in encoded_input["attention_mask"]], batch_first=True, padding_value=0
        )
        label = torch.stack([x[1] for x in batch])
        id = torch.stack([x[3] for x in batch])
        encoded_input["id"] = id

        return encoded_input, label


def to_device(item: Dict[str, Tensor], device: torch.device) -> Dict[str, Tensor]:
    for key in item.keys():
        item[key] = item[key].to(device)
    return item


def create_dataset(chitchat_folder_path: str, workflow_paths: List[str], expressions_junk_file_paths: List[str], out_tsv_filepath: str):
    datapoints = []
    existing_queries = set([])

    def add_datapoint(query: str, label: str, query_type: str):
        if query not in existing_queries:
            datapoint = {"query": query, "label": label, "type": query_type}
            datapoints.append(datapoint)
            existing_queries.add(query)

    for root, _, filenames in os.walk(chitchat_folder_path):
        for filename in filenames:
            chitchat_file = os.path.join(root, filename)
            with open(chitchat_file, mode="r", encoding="utf-8") as file:
                csvFile = csv.DictReader(file, delimiter="\t")
                for line in csvFile:
                    add_datapoint(line["Question"], "JUNK", "chitchat")
    for workflow_path in workflow_paths:
        for root, _, filenames in os.walk(workflow_path):
            for filename in filenames:
                if re.search(r"\.(yaml)$", filename.lower()):
                    with open(os.path.join(root, filename), "r") as f:
                        wf_data = yaml.load(f, Loader=yaml.CSafeLoader)
                        query_keys = ["description", "description_sequence"]
                        plan_keys = ["plan", "original-plan"]
                        for key in query_keys:
                            if key in wf_data:
                                add_datapoint(wf_data[key], "GOOD", "workflow")
                        for key in plan_keys:
                            if key in wf_data:
                                plan = wf_data[key]
                                steps = get_steps_plan(plan)
                                for step in steps:
                                    add_datapoint(step, "GOOD", "workflow")
    for expressions_file_path in expressions_junk_file_paths:
        with open(expressions_file_path, mode="r", encoding="utf-8") as file:
            csvFile = csv.DictReader(file, delimiter=",")
            for line in csvFile:
                if line["step"] == "accept":
                    add_datapoint(line["query"], "GOOD", "expression")

    with open(out_tsv_filepath, "w", newline="", encoding="utf-8") as tsvfile:
        writer = csv.writer(tsvfile, delimiter="\t", lineterminator="\n")
        writer.writerow(["query", "label", "type"])
        for datapoint in datapoints:
            writer.writerow([datapoint["query"], datapoint["label"], datapoint["type"]])


def load_datapoints(dataset_path) -> List[Dict]:
    datapoints: List[Dict] = []
    with open(dataset_path, mode="r", encoding="utf-8") as file:
        csvFile = csv.DictReader(file, delimiter="\t")
        for line in csvFile:
            datapoint = {"query": line["query"]}
            datapoint["label"] = CLASSES_2_ID[line["label"]]
            datapoint["type"] = line["type"]
            datapoints.append(datapoint)
    return datapoints


def train_model(
    model: nn.Module,
    device: torch.device,
    dataloaders: Dict[str, tud.DataLoader],
    dataset_sizes: dict[str, int],
    model_path: str = "",
    num_epochs: int = 50,
    show_progress_interval: float = 0.1,
):
    since = time.time()

    criterion = nn.CrossEntropyLoss()
    model_file_path = os.path.join(model_path, "model.b")

    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5)
    scheduler = lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.3)

    best_acc = 0.0
    show_progress_number_steps = int(len(dataloaders["train"]) * show_progress_interval)
    print(f"Show progress each {show_progress_number_steps} number of steps")

    for epoch in range(num_epochs):
        print(f"Epoch {epoch}/{num_epochs - 1}")
        print("-" * 10)

        # Each epoch has a training and validation phase
        for phase in ["train", "val"]:
            if phase == "train":
                model.train()  # Set model to training mode
            else:
                model.eval()  # Set model to evaluate mode

            running_loss = 0.0
            running_corrects: Tensor = torch.tensor(0)
            current_index = 0
            progress_loss_list = []

            all_eval_predictions = []
            all_eval_targets = []

            # Iterate over data.
            for inputs, targets in dataloaders[phase]:
                inputs = to_device(inputs, device)
                labels = targets.to(device)

                # zero the parameter gradients
                optimizer.zero_grad()

                # forward
                # track history if only in train
                with torch.set_grad_enabled(phase == "train"):
                    outputs = model(inputs)
                    _, preds = torch.max(outputs, 1)
                    loss = criterion(outputs, labels)

                    # backward + optimize only if in training phase
                    if phase == "train":
                        loss.backward()
                        optimizer.step()
                    else:  # validation phase
                        pred_prob = torch.nn.functional.softmax(outputs)
                        all_eval_predictions.append(pred_prob.detach().cpu())
                        all_eval_targets.append(labels.detach().cpu())

                # statistics
                batch_average_loss_val = loss.item()
                batch_loss = batch_average_loss_val * labels.size(0)
                running_loss += batch_loss
                progress_loss_list.append(batch_loss)
                running_corrects += torch.sum(preds == labels).cpu().detach()
                current_index = current_index + 1
                if (model.training) and (current_index % show_progress_number_steps == 0):
                    progress_loss = sum(progress_loss_list) / len(progress_loss_list)
                    epoch_percentage = int(100 * current_index / len(dataloaders[phase]))
                    print(f"{epoch_percentage}% complete. Loss: {progress_loss:.4f}")
                    progress_loss_list = []

            if phase == "train":
                scheduler.step()

            epoch_loss = running_loss / dataset_sizes[phase]
            epoch_acc = running_corrects.double() / dataset_sizes[phase]

            print(f"{phase} Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}")

            if phase == "val" and epoch_acc > best_acc:
                best_acc = epoch_acc
                torch.save(model.state_dict(), model_file_path)
                all_prediction_array = np.vstack(all_eval_predictions)
                all_targets_array = np.hstack(all_eval_targets)
                target_class_name = "JUNK"
                precision, recall, thresholds = compute_pr_curve(all_prediction_array, all_targets_array, target_class_name, CLASSES_2_ID)
                curve_path = os.path.join(model_path, f"pr_curve_{target_class_name}.png")
                draw_pr_curve(precision, recall, thresholds, target_class_name, curve_path)

    time_elapsed = time.time() - since
    print(f"Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s")
    print(f"Best val Acc: {best_acc:4f}")

    model.load_state_dict(torch.load(model_file_path))


def test(model_path: str | None, device: torch.device, query: str, query_type: QUERY_TYPE = "workflow"):
    model, tokenizer = load_model(model_path)
    filter = BertPromptFilter(model, tokenizer, device=device)
    out = filter.predict(query, query_type)
    print(out)
    print(filter.id_2_classes)
    return out


def eval(model: BertClassifierModel, dataloader: tud.DataLoader, device: torch.device):
    predictions, targets = compute_predictions(model, dataloader, device)
    target_class = "JUNK"
    precision, recall, thresholds = compute_pr_curve(predictions, targets, target_class, CLASSES_2_ID)
    curve_path = f"pr_curve_{target_class}.png"
    draw_pr_curve(precision, recall, thresholds, target_class, curve_path, interactive=True)


def compute_predictions(model: BertClassifierModel, dataloader: tud.DataLoader, device: torch.device) -> Tuple[np.ndarray, np.ndarray]:
    model.eval()
    all_predictions = []
    all_targets = []
    for inputs, targets in dataloader:
        inputs = to_device(inputs, device)
        labels = targets.to(device)

        with torch.set_grad_enabled(False):
            outputs = model(inputs)
            outputs = torch.nn.functional.softmax(outputs)
            all_predictions.append(outputs.detach().cpu())
            all_targets.append(labels.detach().cpu())
    return np.vstack(all_predictions), np.hstack(all_targets)


def train(model_path: str | None = None) -> BertClassifierModel:
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    dataset_file_path = "workflow_junk_data_records.tsv"
    datapoints = load_datapoints(dataset_file_path)
    seed = 42
    rnd = random.Random(seed)
    rnd.shuffle(datapoints)

    train_size = int(len(datapoints) * 0.8)

    tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_NAME)
    train_dataset = ClassificationDataset(datapoints[:train_size])
    eval_dataset = ClassificationDataset(datapoints[train_size:])

    model = BertClassifierModel(3)

    datasets = {"train": train_dataset, "val": eval_dataset}

    dataloaders = {x: tud.DataLoader(datasets[x], batch_size=8, shuffle=True, num_workers=2, collate_fn=DataCollator(tokenizer)) for x in ["train", "val"]}
    dataset_sizes: dict[str, int] = {x: len(datasets[x]) for x in ["train", "val"]}

    model = model.to(device)
    if model_path is None:
        model_path = DEFAULT_MODEL_PATH

    train_model(model, device, dataloaders, num_epochs=10, dataset_sizes=dataset_sizes, model_path=model_path)
    return model


def evaluate(model_path: str | None, device: torch.device):
    dataset_file_path = "workflow_junk_data_records.tsv"
    datapoints = load_datapoints(dataset_file_path)
    seed = 42
    rnd = random.Random(seed)
    rnd.shuffle(datapoints)

    train_size = int(len(datapoints) * 0.8)

    tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_NAME)

    eval_dataset = ClassificationDataset(datapoints[train_size:])
    eval_data_loader = tud.DataLoader(eval_dataset, batch_size=8, shuffle=False, num_workers=2, collate_fn=DataCollator(tokenizer))

    if model_path is None:
        model_path = DEFAULT_MODEL_PATH
    model, _ = load_model(model_path)
    model = model.to(device)

    eval(model, eval_data_loader, device)


if __name__ == "__main__":
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    model_path = DEFAULT_MODEL_PATH
    build_dataset = False
    if build_dataset:
        chitchat_folder_path = r"C:\work\chitchat"
        workflows_path = [
            r"C:\work\repos\ProductionWorkflows\old",
            r"C:\work\repos\Autopilot.Samples\Dataset\WorkflowGeneration",
            r"C:\work\repos\Autopilot.Samples\Dataset\SequenceGeneration",
        ]

        dataset_file_path = "workflow_junk_data_records.tsv"
        expressions_junk_train_set = "expressions_junk_trainset.csv"
        force_create = False
        if not os.path.isfile(dataset_file_path) or force_create:
            create_dataset(chitchat_folder_path, workflows_path, [expressions_junk_train_set], dataset_file_path)

    # train(model_path)
    # evaluate(model_path, device)
    test(model_path, device, "How are you?")
    test(model_path, device, "Send an email to X")
    test(model_path, device, "Send a slack message with the text 'Well done'")
