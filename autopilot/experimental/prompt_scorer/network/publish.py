import argparse

from experimental.common.az_cli import check_az_req, publish_package
from experimental.prompt_scorer.network.train import DEFAULT_MODEL_PATH

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    feed = "studio-api-sentence-workflow-junk-filter-model"
    model_path = DEFAULT_MODEL_PATH
    parser.add_argument("--version", "-v", help="The version of the Universal Package to publish.", default="1.0.2")
    parser.add_argument("--model_path", "-m", help="Exported model path", default=model_path)

    args = parser.parse_args()

    check_az_req()
    publish_package(name=feed, version=args.version, path=args.model_path)
