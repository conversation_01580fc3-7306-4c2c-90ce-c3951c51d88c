#!/usr/bin/env python3

import typer

import dataset.main

app = typer.Typer(help="Autopilot CLI toolkit", add_completion=False, no_args_is_help=True)
app.add_typer(dataset.main.app, name="dataset", help="Dataset service commands.")


@app.callback()
def main():
    """
    Autopilot command line toolkit.

    This CLI provides a unified interface to interact with various autopilot services and components.
    You can use subcommands to access specific service functionality.

    Examples:
      - Run dataset retriever build: autopilot dataset build-retrievers
    """


if __name__ == "__main__":
    app()
