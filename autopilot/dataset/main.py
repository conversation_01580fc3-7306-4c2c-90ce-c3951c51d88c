#!/usr/bin/env python3
import asyncio
import time

import typer

from services.studio import service

app = typer.Typer(help="Dataset service commands for building and managing retrievers", add_completion=False, no_args_is_help=True)


@app.command(help="Build retrievers for the dataset service")
def build_retrievers(force: bool = typer.Option(False, "--force", "-f", help="Force rebuild all retrievers", show_default=True)):
    """Initialize and build all retrievers for the dataset service.

    This command will build or rebuild the vector retrievers and other resources needed to run the service.
    """
    print("\nAutopilot - Building retrievers. Force: ", force)

    start_time = time.time()
    try:
        asyncio.run(service._initialize_endpoints(force_rebuild=force))
        elapsed = time.time() - start_time
        print(f"\n✓ All retrievers built successfully! ({elapsed:.2f}s)")
    except Exception as e:
        print(f"\n✗ Error building retrievers: {str(e)}")
        raise typer.Exit(code=1) from e


# This provides better help when used as a standalone CLI
@app.callback()
def dataset_callback():
    """
    Dataset service CLI tools.

    This CLI provides commands for managing the dataset service,
    including building and maintaining vector retrievers.
    """


if __name__ == "__main__":
    app()
