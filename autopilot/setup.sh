# cd to directory of this script
cd "$(dirname "$0")"

# Check if logged into Azure CLI
if ! az account show > /dev/null 2>&1; then
    echo "Not logged into Azure CLI. Please run 'az login' first."
    exit 1
fi


if [ -d ".data/Autopilot.Samples" ]; then
    echo "Dataset repo already exists, skipping clone..."
else
    echo "Cloning dataset repo..."
    git clone https://github.com/UiPath/Autopilot.Samples.git ".data/Autopilot.Samples"
fi

echo "Downloading model resources..."
if [ ! -d ".data/Models/stella-trained" ]; then
    az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-embeddings-model" --version "0.0.5" --path ".data/Models/stella-trained"
else
    echo " Stella model already exists, skipping download..."
fi

if [ ! -d ".data/Models/cross-encoder" ]; then
    az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "studio-api-sentence-cross-encoder-model" --version "1.0.0" --path ".data/Models/cross-encoder"
else
    echo " Cross-encoder model already exists, skipping download..."
fi


echo "Downloading .net resources..."
if [ ! -d ".data/DotNet/dynamic.activities.discovery" ]; then
      az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "dynamic.activities.discovery" --version "25.0.0-alpha.19987" --path ".data/DotNet/dynamic.activities.discovery"
else
    echo " Dynamic activities discovery already exists, skipping download..."
fi

echo "Done."

# az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "workflow.converter.net461" --version "24.4.0-minor-wf-converter-fixes.15978" --path ".data/DotNet/workflow.converter.net461"
# az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "workflow.converter.net8" --version "24.4.0-minor-wf-converter-fixes.15978" --path ".data/DotNet/workflow.converter.net8"
# az artifacts universal download --organization "https://uipath.visualstudio.com/" --feed "ML-models" --name "workflow.converter.net8.windows" --version "24.4.0-minor-wf-converter-fixes.15978" --path ".data/DotNet/workflow.converter.net8.windows"
