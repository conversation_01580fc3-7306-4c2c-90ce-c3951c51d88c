#!/bin/bash
COMMAND="${1}"

# command is ruff
if [ "${COMMAND}" == "ruff" ]; then
  ruff ${@:2} services/ # only do the checks in the services folder
  exit $?
# elif command is pyright
elif [ "${COMMAND}" == "basedpyright" ]; then
  basedpyright ${@:2} services/ # only do the checks in the services folder
  exit $?
# elif command is pytest
elif [ "${COMMAND}" == "pytest" ]; then
  gcloud auth activate-service-account --key-file=/tmp/gcp.json
  gcloud config set project devtest-autopilot
  export GOOGLE_CLOUD_PROJECT=devtest-autopilot
  export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcp.json

  python main.py dataset build-retrievers --force && pytest "${@:2}"
  exit $?
else
  echo "Invalid command"
  exit 1
fi
